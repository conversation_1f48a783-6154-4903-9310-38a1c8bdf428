/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.createTable('diseases', table => {
    table.uuid('id').primary();
    table.uuid('patient_id').notNullable().references('id').inTable('patients').onDelete('CASCADE');
    table.string('name', 100).notNullable();
    table.date('diagnosis_date').notNullable();
    table.string('stage', 50).notNullable();
    table.text('description').nullable();
    table.text('treatment').nullable();
    table.boolean('is_deleted').defaultTo(false);
    table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());
    table.timestamp('updated_at', { useTz: true }).defaultTo(knex.fn.now());

    // 添加索引
    table.index('patient_id');
    table.index('name');
    table.index('stage');
    table.index('is_deleted');
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.dropTableIfExists('diseases');
}; 