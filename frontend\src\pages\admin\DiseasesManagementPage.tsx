import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Card,
  CardContent,
  Tooltip,
  CircularProgress,
  Alert,
  Link,
  Menu,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterListIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  MedicalInformation as MedicalIcon,
  CalendarMonth as DateIcon,
  Person as PersonIcon,
  Refresh as RefreshIcon,
  ArrowUpward as StageUpIcon,
  ArrowDownward as StageDownIcon,
  MoreVert as MoreVertIcon
} from '@mui/icons-material';
import axios from 'axios';
import { API_BASE_URL } from '../../config/api';
import { API_PATHS } from '../../config/apiPaths';
import { useSnackbar } from 'notistack';
import { format } from 'date-fns';
import { useTheme } from '@mui/material/styles';
import { useMediaQuery } from '@mui/material';
import { ThemeProvider, createTheme as createMuiTheme } from '@mui/material/styles';

// 病理数据接口
interface Disease {
  id: string;
  name: string;
  patientId: string;
  patientName: string;
  stage: string;
  type: string;
  diagnosisDate: string;
  userId: string;
  username?: string;
  isDeleted: boolean;
  details?: string;
  createdAt: string;
}

/**
 * 病理数据管理页面
 * 管理员可以查看所有病理数据
 */
const DiseasesManagementPage: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { enqueueSnackbar } = useSnackbar();

  const reducedFontSizeTheme = createMuiTheme(theme, {
    typography: {
      h1: { ...theme.typography.h1, fontSize: theme.typography.h1?.fontSize ? `calc(${theme.typography.h1.fontSize} - 0.4rem)`:'5.6rem' },
      h2: { ...theme.typography.h2, fontSize: theme.typography.h2?.fontSize ? `calc(${theme.typography.h2.fontSize} - 0.3rem)`:'3.45rem' },
      h3: { ...theme.typography.h3, fontSize: theme.typography.h3?.fontSize ? `calc(${theme.typography.h3.fontSize} - 0.25rem)`:'2.75rem' },
      h4: { ...theme.typography.h4, fontSize: theme.typography.h4?.fontSize ? `calc(${theme.typography.h4.fontSize} - 0.2rem)`:'1.925rem' },
      h5: { fontSize: '1.0rem' },
      h6: { fontSize: '0.85rem' },
      subtitle1: { fontSize: '0.75rem' },
      subtitle2: { fontSize: '0.65rem' },
      body1: { fontSize: '0.75rem' },
      body2: { fontSize: '0.65rem' },
      button: { fontSize: '0.65rem' },
      caption: { fontSize: '0.55rem' },
      overline: { fontSize: '0.55rem' },
    },
    components: {
      MuiInputLabel: { styleOverrides: { root: { fontSize: '0.75rem' } } },
      MuiMenuItem: { styleOverrides: { root: { fontSize: '0.75rem' } } },
      MuiTableCell: { styleOverrides: { root: { fontSize: '0.65rem' }, head: { fontSize: '0.7rem', fontWeight: 'bold' } } },
      MuiChip: { styleOverrides: { label: { fontSize: '0.55rem' }, labelSmall: { fontSize: '0.5rem' } } },
      MuiButton: { styleOverrides: { sizeSmall: { fontSize: '0.6rem' }, sizeMedium: { fontSize: '0.65rem' }, sizeLarge: { fontSize: '0.75rem' } } },
      MuiDialogTitle: { styleOverrides: { root: { fontSize: '0.85rem' } } },
      MuiDialogContentText: { styleOverrides: { root: { fontSize: '0.75rem' } } },
      MuiFormControlLabel: { styleOverrides: { label: { fontSize: '0.75rem' } } },
      MuiAlert: { styleOverrides: { message: { fontSize: '0.65rem' } } },
      MuiTablePagination: {
        styleOverrides: {
          caption: { fontSize: '0.65rem' },
          selectLabel: { fontSize: '0.65rem' },
          displayedRows: { fontSize: '0.65rem' }
        }
      }
    }
  });

  // 病理列表状态
  const [diseases, setDiseases] = useState<Disease[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 搜索和筛选状态
  const [searchKeyword, setSearchKeyword] = useState('');
  const [filterOpen, setFilterOpen] = useState(false);
  const [stageFilter, setStageFilter] = useState<string>('');
  const [typeFilter, setTypeFilter] = useState<string>('');
  const [patientIdFilter, setPatientIdFilter] = useState<string>('');

  // 分页状态
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // 删除确认对话框状态
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [diseaseToDelete, setDiseaseToDelete] = useState<Disease | null>(null);

  // 病理详情对话框状态
  const [detailDialogOpen, setDetailDialogOpen] = useState(false);
  const [selectedDisease, setSelectedDisease] = useState<Disease | null>(null);

  // 更多操作菜单状态 (for mobile cards)
  const [mobileMenuAnchorEl, setMobileMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedDiseaseForMenu, setSelectedDiseaseForMenu] = useState<Disease | null>(null);

  // 初始加载病理数据
  useEffect(() => {
    fetchDiseases();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 获取病理列表
  const fetchDiseases = useCallback(async (filterParams = {}) => {
    setLoading(true);
    setError(null);

    try {
      // 构建查询参数
      const params: any = {
        page,
        pageSize: rowsPerPage,
        ...filterParams
      };

      if (searchKeyword.trim()) {
        params.search = searchKeyword.trim();
      }

      if (stageFilter) {
        params.stage = stageFilter;
      }

      if (typeFilter) {
        params.type = typeFilter;
      }

      if (patientIdFilter) {
        params.patientId = patientIdFilter;
      }

      // 使用API_PATHS常量确保路径一致性
      const response = await axios.get(`${API_BASE_URL}${API_PATHS.ADMIN.DISEASES}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        },
        params
      });

      if (response.data) {
        setDiseases(response.data.diseases || []);
        setTotalCount(response.data.total || 0);
      } else {
        setDiseases([]);
        setTotalCount(0);
      }
    } catch (err: any) {
      console.error('获取病理列表失败:', err);
      setError(err.response?.data?.message || '获取病理列表失败');
      enqueueSnackbar('获取病理列表失败', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  }, [page, rowsPerPage, searchKeyword, stageFilter, typeFilter, patientIdFilter, enqueueSnackbar]);

  // 应用筛选条件
  const applyFilters = () => {
    setPage(0); // 重置到第一页
    fetchDiseases({
      search: searchKeyword,
      stage: stageFilter,
      type: typeFilter,
      patientId: patientIdFilter
    });
  };

  // 重置筛选条件
  const resetFilters = () => {
    setSearchKeyword('');
    setStageFilter('');
    setTypeFilter('');
    setPatientIdFilter('');
    setPage(0);
    fetchDiseases({});
  };

  // 处理搜索输入变化
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchKeyword(event.target.value);
  };

  // 处理回车键搜索
  const handleSearchKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      applyFilters();
    }
  };

  // 处理分页变化
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  // 处理每页行数变化
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // 打开删除确认对话框
  const openDeleteDialog = (disease: Disease) => {
    setDiseaseToDelete(disease);
    setDeleteDialogOpen(true);
  };

  // 关闭删除确认对话框
  const closeDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setDiseaseToDelete(null);
  };

  // 删除病理
  const deleteDisease = async () => {
    if (!diseaseToDelete) return;

    try {
      await axios.delete(`${API_BASE_URL}/api/admin/diseases/${diseaseToDelete.id}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      enqueueSnackbar('病理数据已成功删除', { variant: 'success' });

      // 关闭对话框并刷新列表
      closeDeleteDialog();
      fetchDiseases();
    } catch (err: any) {
      console.error('删除病理数据失败:', err);
      enqueueSnackbar('删除病理数据失败: ' + (err.response?.data?.message || err.message), { variant: 'error' });
    }
  };

  // 查看病理详情
  const viewDiseaseDetail = (disease: Disease) => {
    setSelectedDisease(disease);
    setDetailDialogOpen(true);
  };

  // 关闭详情对话框
  const closeDetailDialog = () => {
    setDetailDialogOpen(false);
    setSelectedDisease(null);
  };

  // 移动端菜单处理
  const handleMobileMenuOpen = (event: React.MouseEvent<HTMLElement>, disease: Disease) => {
    setMobileMenuAnchorEl(event.currentTarget);
    setSelectedDiseaseForMenu(disease);
  };

  const handleMobileMenuClose = () => {
    setMobileMenuAnchorEl(null);
    setSelectedDiseaseForMenu(null);
  };

  const handleViewDetailFromMenu = () => {
    if (selectedDiseaseForMenu) {
      viewDiseaseDetail(selectedDiseaseForMenu);
    }
    handleMobileMenuClose();
  };

  const handleDeleteFromMenu = () => {
    if (selectedDiseaseForMenu) {
      openDeleteDialog(selectedDiseaseForMenu);
    }
    handleMobileMenuClose();
  };

  // 格式化时间
  const formatDateTime = (dateString: string) => {
    if (!dateString) return '未知';
    try {
      const date = new Date(dateString);
      return format(date, 'yyyy-MM-dd');
    } catch (error) {
      return '无效日期';
    }
  };

  // 格式化病理阶段显示
  const formatStage = (stage: string) => {
    switch (stage?.toUpperCase()) {
      case 'INITIAL':
        return (
          <Chip
            icon={<StageUpIcon />}
            label="初期"
            size="small"
            color="success"
            variant="outlined"
          />
        );
      case 'INTERMEDIATE':
        return (
          <Chip
            label="中期"
            size="small"
            color="warning"
            variant="outlined"
          />
        );
      case 'ADVANCED':
        return (
          <Chip
            icon={<StageDownIcon />}
            label="晚期"
            size="small"
            color="error"
            variant="outlined"
          />
        );
      case 'REMISSION':
        return (
          <Chip
            label="缓解期"
            size="small"
            color="info"
            variant="outlined"
          />
        );
      default:
        return (
          <Chip
            label={stage || '未知'}
            size="small"
            color="default"
            variant="outlined"
          />
        );
    }
  };

  return (
    <ThemeProvider theme={reducedFontSizeTheme}>
      <Box sx={{ py: { xs: 1, sm: 2 } }}>
        <Typography variant="h6" component="h1" gutterBottom sx={{ mb: { xs: 1.5, sm: 2 } }}>
          病理数据管理
        </Typography>

        {/* 搜索和筛选区域 */}
        <Paper elevation={1} sx={{ p: { xs: '10px', sm: 2 }, mb: { xs: 1.5, sm: 3 } }}>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
            <Box sx={{ flex: '1 1 300px', minWidth: 0 }}>
              <TextField
                placeholder="搜索病理..."
                variant="outlined"
                size="small"
                fullWidth
                value={searchKeyword}
                onChange={handleSearchChange}
                onKeyPress={handleSearchKeyPress}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Box>

            <Box sx={{ flex: '1 1 300px', display: 'flex', gap: 1, justifyContent: 'flex-end', alignItems: 'center' }}>
              <Button
                variant="outlined"
                color="primary"
                startIcon={<FilterListIcon />}
                onClick={() => setFilterOpen(!filterOpen)}
              >
                高级筛选
              </Button>

              <Button
                variant="outlined"
                color="primary"
                startIcon={<RefreshIcon />}
                onClick={() => fetchDiseases()}
                disabled={loading}
              >
                刷新
              </Button>
            </Box>

            {/* 高级筛选面板 */}
            {filterOpen && (
              <Box sx={{ width: '100%' }}>
                <Card variant="outlined">
                  <CardContent>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                      <Box sx={{ flex: '1 1 200px', minWidth: 0 }}>
                        <FormControl fullWidth size="small">
                          <InputLabel id="stage-filter-label">阶段</InputLabel>
                          <Select
                            labelId="stage-filter-label"
                            value={stageFilter}
                            label="阶段"
                            onChange={(e) => setStageFilter(e.target.value)}
                          >
                            <MenuItem value="">全部</MenuItem>
                            <MenuItem value="INITIAL">初期</MenuItem>
                            <MenuItem value="INTERMEDIATE">中期</MenuItem>
                            <MenuItem value="ADVANCED">晚期</MenuItem>
                            <MenuItem value="REMISSION">缓解期</MenuItem>
                          </Select>
                        </FormControl>
                      </Box>

                      <Box sx={{ flex: '1 1 200px', minWidth: 0 }}>
                        <FormControl fullWidth size="small">
                          <InputLabel id="type-filter-label">类型</InputLabel>
                          <Select
                            labelId="type-filter-label"
                            value={typeFilter}
                            label="类型"
                            onChange={(e) => setTypeFilter(e.target.value)}
                          >
                            <MenuItem value="">全部</MenuItem>
                            <MenuItem value="CANCER">癌症</MenuItem>
                            <MenuItem value="CHRONIC">慢性病</MenuItem>
                            <MenuItem value="INFECTIOUS">传染病</MenuItem>
                            <MenuItem value="AUTOIMMUNE">自身免疫疾病</MenuItem>
                            <MenuItem value="OTHER">其他</MenuItem>
                          </Select>
                        </FormControl>
                      </Box>

                      <Box sx={{ flex: '1 1 200px', minWidth: 0 }}>
                        <TextField
                          label="患者ID"
                          size="small"
                          fullWidth
                          value={patientIdFilter}
                          onChange={(e) => setPatientIdFilter(e.target.value)}
                        />
                      </Box>

                      <Box sx={{ width: '100%', display: 'flex', justifyContent: 'flex-end', mt: 1 }}>
                        <Button
                          variant="outlined"
                          color="secondary"
                          onClick={resetFilters}
                          sx={{ mr: 1 }}
                        >
                          重置
                        </Button>
                        <Button
                          variant="contained"
                          color="primary"
                          onClick={applyFilters}
                        >
                          应用筛选
                        </Button>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Box>
            )}
          </Box>
        </Paper>

        {/* 移动端卡片视图 */}
        {isMobile && (
          <Box>
            {loading && <CircularProgress sx={{ display: 'block', margin: 'auto', my: 4 }} />}
            {error && <Typography color="error" sx={{ textAlign: 'center', my: 4 }}>{error}</Typography>}
            {!loading && !error && diseases.length === 0 && (
              <Typography sx={{ textAlign: 'center', my: 4 }}>未找到符合条件的病理数据。</Typography>
            )}
            {!loading && !error && diseases.map((disease) => (
              <Card key={disease.id} sx={{ mb: 2, boxShadow: 3 }}>
                <CardContent sx={{ px: '10px', '&:last-child': { pb: '10px' } }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Typography variant="h6" component="div" sx={{ fontWeight: 'bold', flexGrow: 1, mr: 1, wordBreak: 'break-word' }}>
                      {disease.name}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Chip
                        label={disease.isDeleted ? '已删除' : '正常'}
                        color={disease.isDeleted ? 'default' : 'success'}
                        size="small"
                        sx={{ mr: 0.5 }}
                      />
                      <IconButton size="small" onClick={(event) => handleMobileMenuOpen(event, disease)} sx={{ p: 0.5 }}>
                        <MoreVertIcon />
                      </IconButton>
                    </Box>
                  </Box>

                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    患者: <Link component="span" onClick={() => console.log('Navigate to patient ' + disease.patientId)} sx={{cursor: 'pointer'}}>{disease.patientName || disease.patientId}</Link>
                  </Typography>
                  <Typography variant="body2" color="text.secondary" component="div">
                    分期: {formatStage(disease.stage)} | 类型: {disease.type || 'N/A'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    诊断日期: {formatDateTime(disease.diagnosisDate)}
                  </Typography>
                  {disease.username && (
                    <Typography variant="caption" display="block" sx={{ mt: 1, color: 'text.disabled' }}>
                      记录者: {disease.username}
                    </Typography>
                  )}
                   <Typography variant="caption" display="block" sx={{ color: 'text.disabled' }}>
                      创建于: {formatDateTime(disease.createdAt)}
                    </Typography>
                </CardContent>
              </Card>
            ))}
             <Menu
              anchorEl={mobileMenuAnchorEl}
              open={Boolean(mobileMenuAnchorEl)}
              onClose={handleMobileMenuClose}
            >
              <MenuItem onClick={handleViewDetailFromMenu}>
                <ListItemIcon>
                  <VisibilityIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText>查看详情</ListItemText>
              </MenuItem>
              {!selectedDiseaseForMenu?.isDeleted && (
                <MenuItem onClick={handleDeleteFromMenu}>
                  <ListItemIcon>
                    <DeleteIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText>删除</ListItemText>
                </MenuItem>
              )}
            </Menu>
            {totalCount > 0 && (
              <TablePagination
                component="div"
                count={totalCount}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                rowsPerPageOptions={[5, 10, 25]}
                labelRowsPerPage="每页:"
                sx={{ mt: 2 }}
              />
            )}
          </Box>
        )}

        {/* 桌面端表格视图 */}
        {!isMobile && (
          <Paper sx={{ width: '100%', overflow: 'hidden' }}>
            <TableContainer sx={{ maxHeight: 'calc(100vh - 360px)' }}> {/* 调整MaxHeight以适应筛选区域 */}
              <Table stickyHeader size="medium">
                <TableHead>
                  <TableRow>
                    <TableCell>病理名称</TableCell>
                    <TableCell>阶段</TableCell>
                    <TableCell>患者</TableCell>
                    <TableCell>诊断日期</TableCell>
                    <TableCell>类型</TableCell>
                    <TableCell align="center">操作</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={6} align="center">
                        <CircularProgress size={24} sx={{ my: 2 }} />
                        <Typography variant="body2" color="textSecondary" sx={{ ml: 2 }}>
                          加载中...
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ) : error ? (
                    <TableRow>
                      <TableCell colSpan={6} align="center">
                        <Typography variant="body2" color="error" sx={{ my: 2 }}>
                          {error}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ) : diseases.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} align="center">
                        <Typography variant="body2" color="textSecondary" sx={{ my: 2 }}>
                          {searchKeyword || stageFilter || typeFilter || patientIdFilter ?
                            '没有匹配的病理数据' : '暂无病理数据'}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ) : (
                    // 病理列表
                    diseases.map((disease) => (
                      <TableRow
                        key={disease.id}
                        hover
                        sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                      >
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <MedicalIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                            <Typography variant="body2">{disease.name}</Typography>
                          </Box>
                        </TableCell>

                        <TableCell>
                          {formatStage(disease.stage)}
                        </TableCell>

                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <PersonIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                            <Tooltip title={`患者ID: ${disease.patientId}`}>
                              <Link
                                component="button"
                                variant="body2"
                                onClick={() => {
                                  setPatientIdFilter(disease.patientId);
                                  applyFilters();
                                }}
                              >
                                {disease.patientName}
                              </Link>
                            </Tooltip>
                          </Box>
                        </TableCell>

                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <DateIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                            <Typography variant="body2">{formatDateTime(disease.diagnosisDate)}</Typography>
                          </Box>
                        </TableCell>

                        <TableCell>
                          <Chip
                            label={
                              disease.type === 'CANCER' ? '癌症' :
                              disease.type === 'CHRONIC' ? '慢性病' :
                              disease.type === 'INFECTIOUS' ? '传染病' :
                              disease.type === 'AUTOIMMUNE' ? '自身免疫疾病' :
                              disease.type === 'OTHER' ? '其他' : disease.type || '未知'
                            }
                            size="small"
                            variant="outlined"
                          />
                        </TableCell>

                        <TableCell align="center">
                          <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                            <Tooltip title="查看详情">
                              <IconButton
                                size="small"
                                onClick={() => viewDiseaseDetail(disease)}
                                sx={{ mr: 1 }}
                              >
                                <VisibilityIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>

                            <Tooltip title="删除病理">
                              <IconButton
                                size="small"
                                color="error"
                                onClick={() => openDeleteDialog(disease)}
                              >
                                <DeleteIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>

            {/* 分页控件 */}
            <TablePagination
              rowsPerPageOptions={[5, 10, 25, 50]}
              component="div"
              count={totalCount}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
              labelRowsPerPage="每页行数:"
              labelDisplayedRows={({ from, to, count }) => `${from}-${to} / 共${count}条`}
            />
          </Paper>
        )}

        {/* 病理详情对话框 */}
        <Dialog
          open={detailDialogOpen}
          onClose={closeDetailDialog}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <MedicalIcon sx={{ mr: 1 }} />
              病理详情
            </Box>
          </DialogTitle>
          <DialogContent dividers>
            {!selectedDisease ? (
              <Alert severity="error">未能加载病理详情</Alert>
            ) : (
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>基本信息</Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                      <Box sx={{ flex: '1 1 calc(33% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">病理名称</Typography>
                        <Typography variant="body1">{selectedDisease.name}</Typography>
                      </Box>
                      <Box sx={{ flex: '1 1 calc(33% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">病理类型</Typography>
                        <Typography variant="body1">
                          {selectedDisease.type === 'CANCER' ? '癌症' :
                           selectedDisease.type === 'CHRONIC' ? '慢性病' :
                           selectedDisease.type === 'INFECTIOUS' ? '传染病' :
                           selectedDisease.type === 'AUTOIMMUNE' ? '自身免疫疾病' :
                           selectedDisease.type === 'OTHER' ? '其他' : selectedDisease.type || '未知'}
                        </Typography>
                      </Box>
                      <Box sx={{ flex: '1 1 calc(33% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">阶段</Typography>
                        <Typography variant="body1" component="div">{formatStage(selectedDisease.stage)}</Typography>
                      </Box>
                      <Box sx={{ flex: '1 1 calc(33% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">诊断日期</Typography>
                        <Typography variant="body1">{formatDateTime(selectedDisease.diagnosisDate)}</Typography>
                      </Box>
                      <Box sx={{ flex: '1 1 calc(33% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">患者姓名</Typography>
                        <Typography variant="body1">{selectedDisease.patientName}</Typography>
                      </Box>
                      <Box sx={{ flex: '1 1 calc(33% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">患者ID</Typography>
                        <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>{selectedDisease.patientId}</Typography>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>

                {selectedDisease.details && (
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>病理详情</Typography>
                      <Typography variant="body2" component="pre" sx={{
                        whiteSpace: 'pre-wrap',
                        backgroundColor: 'background.paper',
                        p: 2,
                        borderRadius: 1,
                        border: '1px solid',
                        borderColor: 'divider'
                      }}>
                        {selectedDisease.details}
                      </Typography>
                    </CardContent>
                  </Card>
                )}

                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>账户信息</Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                      <Box sx={{ flex: '1 1 calc(33% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">用户ID</Typography>
                        <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>{selectedDisease.userId}</Typography>
                      </Box>
                      <Box sx={{ flex: '1 1 calc(33% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">用户名</Typography>
                        <Typography variant="body2">{selectedDisease.username || '未知'}</Typography>
                      </Box>
                      <Box sx={{ flex: '1 1 calc(33% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">创建时间</Typography>
                        <Typography variant="body2">{formatDateTime(selectedDisease.createdAt)}</Typography>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button
              color="error"
              onClick={() => {
                closeDetailDialog();
                if (selectedDisease) {
                  openDeleteDialog(selectedDisease);
                }
              }}
              startIcon={<DeleteIcon />}
            >
              删除病理
            </Button>
            <Button onClick={closeDetailDialog} color="primary">
              关闭
            </Button>
          </DialogActions>
        </Dialog>

        {/* 删除确认对话框 */}
        <Dialog
          open={deleteDialogOpen}
          onClose={closeDeleteDialog}
        >
          <DialogTitle>
            确认删除病理数据
          </DialogTitle>
          <DialogContent>
            <DialogContentText component="div">
              确定要删除此病理数据吗？删除后将无法恢复，与此病理相关的记录也可能受到影响。
              {diseaseToDelete && (
                <Box component="div" sx={{ mt: 1 }}>
                  <Typography variant="subtitle2">病理信息：</Typography>
                  <Typography variant="body2">名称: {diseaseToDelete.name}</Typography>
                  <Typography variant="body2">阶段: {
                    diseaseToDelete.stage === 'INITIAL' ? '初期' :
                    diseaseToDelete.stage === 'INTERMEDIATE' ? '中期' :
                    diseaseToDelete.stage === 'ADVANCED' ? '晚期' :
                    diseaseToDelete.stage === 'REMISSION' ? '缓解期' : diseaseToDelete.stage || '未知'
                  }</Typography>
                  <Typography variant="body2">患者: {diseaseToDelete.patientName}</Typography>
                </Box>
              )}
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={closeDeleteDialog}>
              取消
            </Button>
            <Button
              onClick={deleteDisease}
              color="error"
              variant="contained"
              startIcon={<DeleteIcon />}
            >
              确认删除
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </ThemeProvider>
  );
};

export default DiseasesManagementPage;