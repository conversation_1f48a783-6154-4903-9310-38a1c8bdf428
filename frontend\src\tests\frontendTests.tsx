import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { MemoryRouter, Route, Routes } from 'react-router-dom';
import axios from 'axios';
import MockAdapter from 'axios-mock-adapter';

// 导入需要测试的组件
import App from '../App';
import Login from '../pages/Login';
import Register from '../pages/Register';
import Profile from '../pages/Profile';
import PatientsPage from '../pages/PatientsPage';
import PatientDetail from '../pages/PatientDetail';

// 创建一个测试用查询客户端
const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

// 模拟API请求
const setupMockAPI = () => {
  const mock = new MockAdapter(axios);
  
  // 模拟登录请求
  mock.onPost('http://localhost:3000/login').reply(200, {
    token: 'fake-jwt-token',
    user: {
      id: '1',
      username: 'testuser',
      email: '<EMAIL>',
      role: 'USER',
      level: 'BASIC'
    }
  });
  
  // 模拟注册请求
  mock.onPost('http://localhost:3000/register').reply(200, {
    message: '注册成功',
    user: {
      id: '1',
      username: 'newuser',
      email: '<EMAIL>'
    }
  });
  
  // 模拟获取用户信息
  mock.onGet('http://localhost:3000/profile').reply(200, {
    id: '1',
    username: 'testuser',
    email: '<EMAIL>',
    phoneNumber: '13800138000',
    role: 'USER',
    level: 'BASIC',
    activeDiseaseLimit: 5,
    aiUsageCount: 0,
    familyMemberLimit: 5
  });
  
  // 模拟获取患者列表
  mock.onGet('http://localhost:3000/patients').reply(200, [
    {
      id: '1',
      name: '测试患者1',
      gender: '男',
      phoneNumber: '13800138001',
      birthDate: '1990-01-01',
      bloodType: 'A',
      isPrimary: 1
    },
    {
      id: '2',
      name: '测试患者2',
      gender: '女',
      phoneNumber: '13800138002',
      birthDate: '1992-02-02',
      bloodType: 'B',
      isPrimary: 0
    }
  ]);
  
  // 模拟获取单个患者详情
  mock.onGet(/http:\/\/localhost:3000\/patients\/\d+/).reply(200, {
    id: '1',
    name: '测试患者1',
    gender: '男',
    phoneNumber: '13800138001',
    email: '<EMAIL>',
    birthDate: '1990-01-01',
    idCard: '110101199001010011',
    medicareCard: 'MC12345678',
    medicareLocation: '北京',
    address: '北京市海淀区',
    emergencyContactName: '紧急联系人',
    emergencyContactPhone: '13900139000',
    emergencyContactRelationship: '父母',
    pastMedicalHistory: '无',
    familyMedicalHistory: '无',
    allergyHistory: '无',
    bloodType: 'A',
    lastVisitDate: '2023-01-01',
    isPrimary: 1
  });
  
  // 模拟添加患者
  mock.onPost('http://localhost:3000/patients').reply(200, {
    message: '患者添加成功',
    patient: {
      id: '3',
      name: '新患者',
      gender: '男',
      phoneNumber: '13800138003',
      birthDate: '1995-05-05',
      bloodType: 'O',
      isPrimary: 0
    }
  });
  
  // 模拟更新患者
  mock.onPut(/http:\/\/localhost:3000\/patients\/\d+/).reply(200, {
    message: '患者更新成功',
    patient: {
      id: '1',
      name: '更新后的患者',
      gender: '男',
      phoneNumber: '13800138001',
      birthDate: '1990-01-01',
      bloodType: 'A'
    }
  });
  
  // 模拟删除患者
  mock.onDelete(/http:\/\/localhost:3000\/patients\/\d+/).reply(200, {
    message: '患者删除成功'
  });
  
  return mock;
};

// 测试渲染器组件
const renderWithProviders = (ui: React.ReactElement, { route = '/' } = {}) => {
  const queryClient = createTestQueryClient();
  const mock = setupMockAPI();
  
  return {
    ...render(
      <QueryClientProvider client={queryClient}>
        <MemoryRouter initialEntries={[route]}>
          {ui}
        </MemoryRouter>
      </QueryClientProvider>
    ),
    queryClient,
    mock
  };
};

// 登录页面测试
describe('登录页面测试', () => {
  test('登录表单渲染正确', () => {
    renderWithProviders(<Login />);
    
    expect(screen.getByLabelText(/用户名/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/密码/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /登录/i })).toBeInTheDocument();
  });
  
  test('登录表单提交', async () => {
    renderWithProviders(<Login />);
    
    fireEvent.change(screen.getByLabelText(/用户名/i), { target: { value: 'testuser' } });
    fireEvent.change(screen.getByLabelText(/密码/i), { target: { value: 'password' } });
    fireEvent.click(screen.getByRole('button', { name: /登录/i }));
    
    await waitFor(() => {
      expect(localStorage.getItem('token')).toBe('fake-jwt-token');
    });
  });
});

// 注册页面测试
describe('注册页面测试', () => {
  test('注册表单渲染正确', () => {
    renderWithProviders(<Register />);
    
    expect(screen.getByLabelText(/用户名/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/电子邮件/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/密码/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /注册/i })).toBeInTheDocument();
  });
});

// 个人资料页面测试
describe('个人资料页面测试', () => {
  beforeEach(() => {
    localStorage.setItem('token', 'fake-jwt-token');
  });
  
  afterEach(() => {
    localStorage.removeItem('token');
  });
  
  test('个人资料页面显示用户信息', async () => {
    renderWithProviders(<Profile />, { route: '/profile' });
    
    await waitFor(() => {
      expect(screen.getByText(/testuser/i)).toBeInTheDocument();
      expect(screen.getByText(/<EMAIL>/i)).toBeInTheDocument();
    });
  });
});

// 患者页面测试
describe('患者页面测试', () => {
  beforeEach(() => {
    localStorage.setItem('token', 'fake-jwt-token');
  });
  
  afterEach(() => {
    localStorage.removeItem('token');
  });
  
  test('患者列表显示正确', async () => {
    renderWithProviders(<PatientsPage />, { route: '/patients' });
    
    await waitFor(() => {
      expect(screen.getByText(/测试患者1/i)).toBeInTheDocument();
      expect(screen.getByText(/测试患者2/i)).toBeInTheDocument();
    });
  });
  
  test('添加患者对话框工作正常', async () => {
    renderWithProviders(<PatientsPage />, { route: '/patients' });
    
    // 打开添加对话框
    const addButton = await screen.findByText(/添加患者/i);
    fireEvent.click(addButton);
    
    // 检查对话框是否打开
    await waitFor(() => {
      expect(screen.getByText(/新增患者/i)).toBeInTheDocument();
    });
  });
});

// 患者详情页面测试
describe('患者详情页面测试', () => {
  beforeEach(() => {
    localStorage.setItem('token', 'fake-jwt-token');
  });
  
  afterEach(() => {
    localStorage.removeItem('token');
  });
  
  test('患者详情页面显示正确信息', async () => {
    renderWithProviders(
      <Routes>
        <Route path="/patients/:id" element={<PatientDetail />} />
      </Routes>,
      { route: '/patients/1' }
    );
    
    await waitFor(() => {
      expect(screen.getByText(/测试患者1/i)).toBeInTheDocument();
      expect(screen.getByText(/13800138001/i)).toBeInTheDocument();
    });
  });
});

// 路由导航测试
describe('路由导航测试', () => {
  beforeEach(() => {
    localStorage.setItem('token', 'fake-jwt-token');
  });
  
  afterEach(() => {
    localStorage.removeItem('token');
  });
  
  test('未登录用户重定向到登录页面', async () => {
    localStorage.removeItem('token');
    renderWithProviders(<App />, { route: '/patients' });
    
    await waitFor(() => {
      expect(screen.getByText(/登录/i)).toBeInTheDocument();
    });
  });
});

// 运行测试
console.log('前端测试脚本已创建，可以使用npm test运行测试'); 