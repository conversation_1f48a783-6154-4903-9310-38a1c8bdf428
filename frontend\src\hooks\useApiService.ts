import { useState, useCallback } from 'react';
import axios, { AxiosRequestConfig } from 'axios';
import { useAppContext } from '../context/AppContext';
import { useErrorContext } from '../context/ErrorContext';
import { useAuthStore } from '../store/authStore';

// 基础URL配置
let baseURL = process.env.REACT_APP_API_URL || (process.env.NODE_ENV === 'production' ? 'https://hkb.life' : 'http://localhost:3001');
// 确保URL包含/api前缀
if (!baseURL.endsWith('/api')) {
  baseURL = `${baseURL}/api`;
}

// 泛型类型定义
interface ApiResponse<T> {
  data: T;
  isLoading: boolean;
  error: any;
  fetch: (url: string, options?: AxiosRequestConfig) => Promise<T>;
  reset: () => void;
}

/**
 * 自定义钩子用于API调用，自动处理上下文参数和请求状态
 */
export function useApiService<T = any>(): ApiResponse<T> {
  const [data, setData] = useState<T | null>(null) as [T, (data: T) => void];
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<any>(null);
  const { getContextParams } = useAppContext();
  const { handleError } = useErrorContext();
  const { token } = useAuthStore();

  // 重置状态
  const reset = useCallback(() => {
    setData(null as T);
    setIsLoading(false);
    setError(null);
  }, []);

  // 发起请求
  const fetch = useCallback(
    async (url: string, options: AxiosRequestConfig = {}): Promise<T> => {
      setIsLoading(true);
      setError(null);

      try {
        // 获取当前上下文参数
        const contextParams = getContextParams();

        // 准备请求配置
        const config: AxiosRequestConfig = {
          ...options,
          baseURL,
          url,
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
            ...options.headers
          }
        };

        // 添加上下文参数到查询参数
        if (contextParams.service_context) {
          config.params = {
            ...config.params,
            service_context: true
          };
        }

        // 记录请求详情
        console.log(`[API] 请求: ${config.method || 'GET'} ${url}`, {
          参数: config.params,
          上下文: contextParams,
          体: config.data ? '存在' : '不存在'
        });

        // 发送请求
        const response = await axios(config);

        // 设置数据
        setData(response.data);
        setIsLoading(false);
        return response.data;
      } catch (err: any) {
        console.error(`[API错误] ${url}:`, err.response || err);
        setError(err);
        setIsLoading(false);

        // 使用全局错误处理
        if (err.response?.status === 401 || err.response?.status === 403 || err.response?.status === 404) {
          handleError(err);
        }

        throw err;
      }
    },
    [token, getContextParams, handleError]
  );

  return { data, isLoading, error, fetch, reset };
}

// 创建一个存储全局状态的对象 - 避免直接在非Hook中使用Hook
const globalStateStore = {
  getToken: () => localStorage.getItem('token'),
  getServiceContext: () => {
    // 从localStorage获取当前操作模式，默认为普通模式
    const mode = localStorage.getItem('operation_mode') || 'NORMAL';
    return mode === 'SERVICE' || mode === 'ADMIN';
  }
};

/**
 * 添加全局错误处理函数，可以在非Hook上下文中使用
 */
const handleGlobalError = (error: any) => {
  // 记录错误
  console.error('[全局API错误]', error);

  // 尝试获取全局错误处理函数
  // 注意：这只会在组件渲染后生效
  const errorCallback = (window as any).__handleGlobalError;
  if (typeof errorCallback === 'function') {
    errorCallback(error);
  }

  // 401错误直接跳转到登录页
  if (error?.response?.status === 401) {
    // 清除token
    localStorage.removeItem('token');
    // 重定向到登录页
    if (window.location.pathname !== '/login') {
      window.location.href = '/login';
    }
  }
};

/**
 * 简化版API调用服务，不使用Hook
 */
export const apiService = {
  /**
   * 发送GET请求
   * @param url API路径
   * @param params 查询参数
   */
  async get<T>(url: string, params: any = {}, options: AxiosRequestConfig = {}) {
    const token = globalStateStore.getToken();
    const isServiceContext = globalStateStore.getServiceContext();

    const config: AxiosRequestConfig = {
      ...options,
      baseURL,
      method: 'GET',
      url,
      params: {
        ...params,
        ...(isServiceContext ? { service_context: true } : {})
      },
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
        ...options.headers
      }
    };

    try {
      const response = await axios(config);
      return response.data as T;
    } catch (error: any) {
      console.error(`[API错误] GET ${url}:`, error);
      handleGlobalError(error);
      throw error;
    }
  },

  /**
   * 发送POST请求
   * @param url API路径
   * @param data 请求体数据
   * @param params 查询参数
   */
  async post<T>(url: string, data: any = {}, params: any = {}, options: AxiosRequestConfig = {}) {
    const token = globalStateStore.getToken();
    const isServiceContext = globalStateStore.getServiceContext();

    const config: AxiosRequestConfig = {
      ...options,
      baseURL,
      method: 'POST',
      url,
      data,
      params: {
        ...params,
        ...(isServiceContext ? { service_context: true } : {})
      },
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
        ...options.headers
      }
    };

    try {
      const response = await axios(config);
      return response.data as T;
    } catch (error: any) {
      console.error(`[API错误] POST ${url}:`, error);
      handleGlobalError(error);
      throw error;
    }
  },

  /**
   * 发送PUT请求
   * @param url API路径
   * @param data 请求体数据
   * @param params 查询参数
   */
  async put<T>(url: string, data: any = {}, params: any = {}, options: AxiosRequestConfig = {}) {
    const token = globalStateStore.getToken();
    const isServiceContext = globalStateStore.getServiceContext();

    const config: AxiosRequestConfig = {
      ...options,
      baseURL,
      method: 'PUT',
      url,
      data,
      params: {
        ...params,
        ...(isServiceContext ? { service_context: true } : {})
      },
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
        ...options.headers
      }
    };

    try {
      const response = await axios(config);
      return response.data as T;
    } catch (error: any) {
      console.error(`[API错误] PUT ${url}:`, error);
      handleGlobalError(error);
      throw error;
    }
  },

  /**
   * 发送DELETE请求
   * @param url API路径
   * @param params 查询参数
   */
  async delete<T>(url: string, params: any = {}, options: AxiosRequestConfig = {}) {
    const token = globalStateStore.getToken();
    const isServiceContext = globalStateStore.getServiceContext();

    const config: AxiosRequestConfig = {
      ...options,
      baseURL,
      method: 'DELETE',
      url,
      params: {
        ...params,
        ...(isServiceContext ? { service_context: true } : {})
      },
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
        ...options.headers
      }
    };

    try {
      const response = await axios(config);
      return response.data as T;
    } catch (error: any) {
      console.error(`[API错误] DELETE ${url}:`, error);
      handleGlobalError(error);
      throw error;
    }
  }
};