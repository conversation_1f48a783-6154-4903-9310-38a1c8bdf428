import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  Paper, 
  <PERSON>po<PERSON>, 
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardHeader,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemAvatar,
  Avatar,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  CircularProgress,
  TextField,
  Switch,
  FormControlLabel,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  DialogContentText,
  Tooltip,
  Alert,
  Tabs,
  Tab
} from '@mui/material';
import { 
  Info as InfoIcon,
  AccountCircle as AccountCircleIcon,
  Devices as DevicesIcon,
  SmartToy as SmartToyIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Warning as WarningIcon,
  Computer as ComputerIcon,
  Storage as StorageIcon,
  Memory as MemoryIcon,
  Speed as SpeedIcon,
  CloudUpload as CloudUploadIcon
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import { useMediaQuery } from '@mui/material';
import axios from 'axios';
import { API_BASE_URL } from '../../config/api';
import { useSnackbar } from 'notistack';

// 系统信息接口
interface SystemInfo {
  os: {
    platform: string;
    version: string;
    memory: {
      total: number;
      free: number;
    };
    cpus: {
      model: string;
      speed: number;
      cores: number;
    };
  };
  app: {
    version: string;
    environment: string;
    uptime: number;
    nodeVersion: string;
  };
  storage: {
    total: number;
    free: number;
    used: number;
  };
  database: {
    type: string;
    version: string;
    size: number;
    tables: number;
  };
}

// 在线会话接口
interface ActiveSession {
  id: string;
  userId: string;
  username: string;
  ip: string;
  userAgent: string;
  lastActivity: string;
  duration: number;
}

// AI设置接口
interface AISettings {
  modelName: string;
  maxTokens: number;
  temperature: number;
  isEnabled: boolean;
  dailyQuotaLimit: number;
  apiKey: string;
  endpointUrl: string;
}

/**
 * 系统维护页面
 * 用于管理员查看系统状态、会话管理和AI配置
 */
const MaintenancePage: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { enqueueSnackbar } = useSnackbar();
  
  // 当前标签页状态
  const [tabValue, setTabValue] = useState(0);
  
  // 系统信息状态
  const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null);
  const [loadingSystemInfo, setLoadingSystemInfo] = useState(true);
  
  // 活跃会话状态
  const [activeSessions, setActiveSessions] = useState<ActiveSession[]>([]);
  const [loadingSessions, setLoadingSessions] = useState(true);
  
  // AI设置状态
  const [aiSettings, setAiSettings] = useState<AISettings | null>(null);
  const [loadingAISettings, setLoadingAISettings] = useState(true);
  const [isEditingAI, setIsEditingAI] = useState(false);
  const [editedAISettings, setEditedAISettings] = useState<AISettings | null>(null);
  
  // 确认对话框状态
  const [confirmDialog, setConfirmDialog] = useState({
    open: false,
    title: '',
    message: '',
    action: () => {}
  });
  
  // 初始加载数据
  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => {
    if (tabValue === 0) {
      fetchSystemInfo();
    } else if (tabValue === 1) {
      fetchActiveSessions();
    } else if (tabValue === 2) {
      fetchAISettings();
    }
  }, [tabValue]);
  
  // 处理标签页变化
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  
  // 获取系统信息
  const fetchSystemInfo = async () => {
    setLoadingSystemInfo(true);
    
    try {
      const response = await axios.get(`${API_BASE_URL}/api/admin/system-info`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      setSystemInfo(response.data);
    } catch (err: any) {
      console.error('获取系统信息失败:', err);
      enqueueSnackbar('获取系统信息失败', { variant: 'error' });
    } finally {
      setLoadingSystemInfo(false);
    }
  };
  
  // 获取活跃会话
  const fetchActiveSessions = async () => {
    setLoadingSessions(true);
    
    try {
      const response = await axios.get(`${API_BASE_URL}/api/admin/active-sessions`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      setActiveSessions(Array.isArray(response.data) ? response.data : []);
    } catch (err: any) {
      console.error('获取活跃会话失败:', err);
      enqueueSnackbar('获取活跃会话失败', { variant: 'error' });
    } finally {
      setLoadingSessions(false);
    }
  };
  
  // 获取AI设置
  const fetchAISettings = async () => {
    setLoadingAISettings(true);
    
    try {
      const response = await axios.get(`${API_BASE_URL}/api/admin/ai-settings`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      setAiSettings(response.data);
      setEditedAISettings(response.data);
    } catch (err: any) {
      console.error('获取AI设置失败:', err);
      enqueueSnackbar('获取AI设置失败', { variant: 'error' });
    } finally {
      setLoadingAISettings(false);
    }
  };
  
  // 终止会话
  const terminateSession = async (sessionId: string) => {
    try {
      await axios.delete(`${API_BASE_URL}/api/admin/active-sessions/${sessionId}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      // 更新会话列表
      setActiveSessions(activeSessions.filter(session => session.id !== sessionId));
      enqueueSnackbar('会话已终止', { variant: 'success' });
    } catch (err: any) {
      console.error('终止会话失败:', err);
      enqueueSnackbar('终止会话失败', { variant: 'error' });
    }
  };
  
  // 保存AI设置
  const saveAISettings = async () => {
    if (!editedAISettings) return;
    
    try {
      await axios.put(`${API_BASE_URL}/api/admin/ai-settings`, editedAISettings, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      setAiSettings(editedAISettings);
      setIsEditingAI(false);
      enqueueSnackbar('AI设置已保存', { variant: 'success' });
    } catch (err: any) {
      console.error('保存AI设置失败:', err);
      enqueueSnackbar('保存AI设置失败', { variant: 'error' });
    }
  };
  
  // 重置系统缓存
  const clearSystemCache = async () => {
    try {
      await axios.post(`${API_BASE_URL}/api/admin/clear-cache`, {}, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      enqueueSnackbar('系统缓存已清除', { variant: 'success' });
    } catch (err: any) {
      console.error('清除缓存失败:', err);
      enqueueSnackbar('清除缓存失败', { variant: 'error' });
    }
  };
  
  // 确认对话框
  const openConfirmDialog = (title: string, message: string, action: () => void) => {
    setConfirmDialog({
      open: true,
      title,
      message,
      action
    });
  };
  
  const closeConfirmDialog = () => {
    setConfirmDialog({
      ...confirmDialog,
      open: false
    });
  };
  
  const handleConfirmAction = () => {
    confirmDialog.action();
    closeConfirmDialog();
  };
  
  // 格式化字节大小
  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };
  
  // 格式化持续时间（毫秒转为可读时间）
  const formatDuration = (milliseconds: number) => {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) {
      return `${days}天 ${hours % 24}小时`;
    } else if (hours > 0) {
      return `${hours}小时 ${minutes % 60}分钟`;
    } else if (minutes > 0) {
      return `${minutes}分钟 ${seconds % 60}秒`;
    } else {
      return `${seconds}秒`;
    }
  };
  
  // 格式化时间
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return isNaN(date.getTime()) 
      ? '无效日期' 
      : date.toLocaleString('zh-CN', { 
          year: 'numeric', 
          month: '2-digit', 
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        });
  };
  
  // 渲染系统信息标签页
  const renderSystemInfoTab = () => {
    if (loadingSystemInfo) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      );
    }
    
    if (!systemInfo) {
      return (
        <Alert severity="error" sx={{ mt: 2 }}>
          无法加载系统信息
        </Alert>
      );
    }
    
    return (
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3, mt: 1 }}>
        {/* 系统信息卡片 */}
        <Box sx={{ flex: '1 1 calc(50% - 12px)', minWidth: '300px' }}>
          <Card>
            <CardHeader 
              title="操作系统" 
              avatar={<ComputerIcon color="primary" />} 
              action={
                <Button
                  startIcon={<RefreshIcon />}
                  onClick={fetchSystemInfo}
                  size="small"
                >
                  刷新
                </Button>
              }
            />
            <Divider />
            <CardContent>
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <InfoIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary="平台" 
                    secondary={systemInfo.os.platform} 
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <InfoIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary="版本" 
                    secondary={systemInfo.os.version} 
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <MemoryIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary="内存" 
                    secondary={`总计: ${formatBytes(systemInfo.os.memory.total)}, 可用: ${formatBytes(systemInfo.os.memory.free)}`} 
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <SpeedIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary="处理器" 
                    secondary={`${systemInfo.os.cpus.model} (${systemInfo.os.cpus.cores}核, ${systemInfo.os.cpus.speed}MHz)`} 
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Box>
        
        {/* 应用信息卡片 */}
        <Box sx={{ flex: '1 1 calc(50% - 12px)', minWidth: '300px' }}>
          <Card>
            <CardHeader 
              title="应用信息" 
              avatar={<DevicesIcon color="primary" />}
            />
            <Divider />
            <CardContent>
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <InfoIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary="应用版本" 
                    secondary={systemInfo.app.version} 
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <InfoIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary="运行环境" 
                    secondary={systemInfo.app.environment} 
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <CloudUploadIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary="运行时间" 
                    secondary={formatDuration(systemInfo.app.uptime)} 
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <InfoIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Node.js版本" 
                    secondary={systemInfo.app.nodeVersion} 
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Box>
        
        {/* 存储信息卡片 */}
        <Box sx={{ flex: '1 1 calc(50% - 12px)', minWidth: '300px' }}>
          <Card>
            <CardHeader 
              title="存储信息" 
              avatar={<StorageIcon color="primary" />}
            />
            <Divider />
            <CardContent>
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <InfoIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary="总存储空间" 
                    secondary={formatBytes(systemInfo.storage.total)} 
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <InfoIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary="已使用空间" 
                    secondary={formatBytes(systemInfo.storage.used)} 
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <InfoIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary="可用空间" 
                    secondary={formatBytes(systemInfo.storage.free)} 
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <InfoIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary="已使用百分比" 
                    secondary={`${Math.round((systemInfo.storage.used / systemInfo.storage.total) * 100)}%`} 
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Box>
        
        {/* 数据库信息卡片 */}
        <Box sx={{ flex: '1 1 calc(50% - 12px)', minWidth: '300px' }}>
          <Card>
            <CardHeader 
              title="数据库信息" 
              avatar={<StorageIcon color="primary" />}
            />
            <Divider />
            <CardContent>
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <InfoIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary="数据库类型" 
                    secondary={systemInfo.database.type} 
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <InfoIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary="数据库版本" 
                    secondary={systemInfo.database.version} 
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <InfoIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary="数据库大小" 
                    secondary={formatBytes(systemInfo.database.size)} 
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <InfoIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary="表数量" 
                    secondary={systemInfo.database.tables} 
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Box>
        
        {/* 维护操作卡片 */}
        <Box sx={{ width: '100%' }}>
          <Card>
            <CardHeader 
              title="系统维护操作" 
              avatar={<WarningIcon color="warning" />}
            />
            <Divider />
            <CardContent>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                <Box sx={{ flex: '1 1 300px', minWidth: 0 }}>
                  <Button 
                    variant="outlined"
                    color="primary"
                    fullWidth
                    onClick={() => openConfirmDialog(
                      '清除系统缓存',
                      '确定要清除系统缓存吗？这可能会暂时影响系统性能，但不会丢失任何数据。',
                      clearSystemCache
                    )}
                  >
                    清除系统缓存
                  </Button>
                </Box>
                
                <Box sx={{ flex: '1 1 300px', minWidth: 0 }}>
                  <Button 
                    variant="outlined"
                    color="secondary"
                    fullWidth
                    onClick={fetchSystemInfo}
                  >
                    刷新系统信息
                  </Button>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Box>
      </Box>
    );
  };
  
  // 渲染活跃会话标签页
  const renderSessionsTab = () => {
    if (loadingSessions) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      );
    }
    
    if (activeSessions.length === 0) {
      return (
        <Alert severity="info" sx={{ mt: 2 }}>
          当前没有活跃的用户会话
        </Alert>
      );
    }
    
    return (
      <Box sx={{ mt: 2 }}>
        <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between' }}>
          <Typography variant="h6">
            当前活跃会话 ({activeSessions.length})
          </Typography>
          <Button 
            startIcon={<RefreshIcon />}
            onClick={fetchActiveSessions}
            size="small"
          >
            刷新
          </Button>
        </Box>
        
        <Paper elevation={2}>
          <List>
            {activeSessions.map((session) => (
              <React.Fragment key={session.id}>
                <ListItem>
                  <ListItemAvatar>
                    <Avatar>
                      <AccountCircleIcon />
                    </Avatar>
                  </ListItemAvatar>
                  
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {session.username}
                        <Chip 
                          size="small" 
                          label={`ID: ${session.id.substring(0, 8)}...`} 
                          sx={{ ml: 1 }}
                        />
                      </Box>
                    }
                    secondary={
                      <>
                        <Typography component="span" variant="body2">
                          IP: {session.ip} | 
                          最后活动: {formatDateTime(session.lastActivity)} | 
                          持续时间: {formatDuration(session.duration)}
                        </Typography>
                        <br />
                        <Typography component="span" variant="body2" color="textSecondary">
                          {session.userAgent}
                        </Typography>
                      </>
                    }
                  />
                  
                  <ListItemSecondaryAction>
                    <Tooltip title="终止会话">
                      <IconButton
                        edge="end"
                        color="error"
                        onClick={() => openConfirmDialog(
                          '终止会话',
                          `确定要终止用户 "${session.username}" 的会话吗？该用户将被迫退出系统。`,
                          () => terminateSession(session.id)
                        )}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </ListItemSecondaryAction>
                </ListItem>
                <Divider component="li" />
              </React.Fragment>
            ))}
          </List>
        </Paper>
      </Box>
    );
  };
  
  // 渲染AI设置标签页
  const renderAISettingsTab = () => {
    if (loadingAISettings) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      );
    }
    
    if (!aiSettings) {
      return (
        <Alert severity="error" sx={{ mt: 2 }}>
          无法加载AI设置
        </Alert>
      );
    }
    
    return (
      <Box sx={{ mt: 2 }}>
        <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between' }}>
          <Typography variant="h6">
            辅医AI设置
          </Typography>
          <Box>
            {isEditingAI ? (
              <>
                <Button 
                  color="primary"
                  startIcon={<SaveIcon />}
                  onClick={saveAISettings}
                  sx={{ mr: 1 }}
                >
                  保存
                </Button>
                <Button 
                  color="inherit"
                  onClick={() => {
                    setIsEditingAI(false);
                    setEditedAISettings(aiSettings);
                  }}
                >
                  取消
                </Button>
              </>
            ) : (
              <Button 
                variant="outlined"
                color="primary"
                onClick={() => setIsEditingAI(true)}
              >
                编辑
              </Button>
            )}
          </Box>
        </Box>
        
        <Paper elevation={2} sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
            <Box sx={{ width: '100%' }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={isEditingAI ? (editedAISettings?.isEnabled || false) : aiSettings.isEnabled}
                    onChange={(e) => {
                      if (isEditingAI && editedAISettings) {
                        setEditedAISettings({
                          ...editedAISettings,
                          isEnabled: e.target.checked
                        });
                      }
                    }}
                    disabled={!isEditingAI}
                  />
                }
                label="启用辅医AI功能"
              />
            </Box>
            
            <Box sx={{ flex: '1 1 calc(50% - 12px)', minWidth: '250px' }}>
              <TextField
                label="AI模型名称"
                fullWidth
                value={isEditingAI ? (editedAISettings?.modelName || '') : aiSettings.modelName}
                onChange={(e) => {
                  if (isEditingAI && editedAISettings) {
                    setEditedAISettings({
                      ...editedAISettings,
                      modelName: e.target.value
                    });
                  }
                }}
                disabled={!isEditingAI}
              />
            </Box>
            
            <Box sx={{ flex: '1 1 calc(50% - 12px)', minWidth: '250px' }}>
              <TextField
                label="API端点URL"
                fullWidth
                value={isEditingAI ? (editedAISettings?.endpointUrl || '') : aiSettings.endpointUrl}
                onChange={(e) => {
                  if (isEditingAI && editedAISettings) {
                    setEditedAISettings({
                      ...editedAISettings,
                      endpointUrl: e.target.value
                    });
                  }
                }}
                disabled={!isEditingAI}
              />
            </Box>
            
            <Box sx={{ flex: '1 1 calc(50% - 12px)', minWidth: '250px' }}>
              <TextField
                label="API密钥"
                fullWidth
                type="password"
                value={isEditingAI ? (editedAISettings?.apiKey || '') : aiSettings.apiKey}
                onChange={(e) => {
                  if (isEditingAI && editedAISettings) {
                    setEditedAISettings({
                      ...editedAISettings,
                      apiKey: e.target.value
                    });
                  }
                }}
                disabled={!isEditingAI}
              />
            </Box>
            
            <Box sx={{ flex: '1 1 calc(50% - 12px)', minWidth: '250px' }}>
              <TextField
                label="每日配额限制"
                fullWidth
                type="number"
                value={isEditingAI ? (editedAISettings?.dailyQuotaLimit || 0) : aiSettings.dailyQuotaLimit}
                onChange={(e) => {
                  if (isEditingAI && editedAISettings) {
                    setEditedAISettings({
                      ...editedAISettings,
                      dailyQuotaLimit: parseInt(e.target.value) || 0
                    });
                  }
                }}
                disabled={!isEditingAI}
              />
            </Box>
            
            <Box sx={{ flex: '1 1 calc(50% - 12px)', minWidth: '250px' }}>
              <TextField
                label="最大Token数"
                fullWidth
                type="number"
                value={isEditingAI ? (editedAISettings?.maxTokens || 0) : aiSettings.maxTokens}
                onChange={(e) => {
                  if (isEditingAI && editedAISettings) {
                    setEditedAISettings({
                      ...editedAISettings,
                      maxTokens: parseInt(e.target.value) || 0
                    });
                  }
                }}
                disabled={!isEditingAI}
              />
            </Box>
            
            <Box sx={{ flex: '1 1 calc(50% - 12px)', minWidth: '250px' }}>
              <TextField
                label="温度参数"
                fullWidth
                type="number"
                inputProps={{ min: 0, max: 1, step: 0.1 }}
                value={isEditingAI ? (editedAISettings?.temperature || 0) : aiSettings.temperature}
                onChange={(e) => {
                  if (isEditingAI && editedAISettings) {
                    setEditedAISettings({
                      ...editedAISettings,
                      temperature: parseFloat(e.target.value) || 0
                    });
                  }
                }}
                disabled={!isEditingAI}
              />
            </Box>
            
            <Box sx={{ width: '100%' }}>
              <Alert severity="info">
                <Typography variant="body2">
                  请注意，修改AI设置可能会影响系统的AI辅医功能。温度参数决定AI回答的随机性，值越高回答越多样化，值越低回答越确定性。
                </Typography>
              </Alert>
            </Box>
          </Box>
        </Paper>
      </Box>
    );
  };
  
  return (
    <Box sx={{ p: { xs: 2, md: 3 } }}>
      <Typography variant="h5" component="h1" gutterBottom>
        系统维护
      </Typography>
      
      {/* 标签页切换 */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          variant={isMobile ? "scrollable" : "fullWidth"}
          scrollButtons={isMobile ? "auto" : false}
        >
          <Tab icon={<InfoIcon />} label="系统信息" />
          <Tab icon={<AccountCircleIcon />} label="在线会话" />
          <Tab icon={<SmartToyIcon />} label="辅医设置" />
        </Tabs>
      </Paper>
      
      {/* 标签页内容 */}
      {tabValue === 0 && renderSystemInfoTab()}
      {tabValue === 1 && renderSessionsTab()}
      {tabValue === 2 && renderAISettingsTab()}
      
      {/* 确认对话框 */}
      <Dialog
        open={confirmDialog.open}
        onClose={closeConfirmDialog}
      >
        <DialogTitle>
          {confirmDialog.title}
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            {confirmDialog.message}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeConfirmDialog}>
            取消
          </Button>
          <Button onClick={handleConfirmAction} color="primary" variant="contained">
            确认
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MaintenancePage; 