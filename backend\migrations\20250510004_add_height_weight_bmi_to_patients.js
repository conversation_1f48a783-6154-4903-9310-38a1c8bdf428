/**
 * 添加身高、体重和BMI字段到患者表
 */
exports.up = function(knex) {
  return knex.schema.table('patients', function(table) {
    // 添加身高（厘米）
    table.float('height').nullable().comment('身高（厘米）');
    // 添加体重（千克）
    table.float('weight').nullable().comment('体重（千克）');
    // 添加BMI，可以根据身高体重计算，但也单独存储便于查询
    table.float('bmi').nullable().comment('体质指数（BMI）');
  });
};

exports.down = function(knex) {
  return knex.schema.table('patients', function(table) {
    table.dropColumn('height');
    table.dropColumn('weight');
    table.dropColumn('bmi');
  });
}; 