import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
  Typography,
  Box
} from '@mui/material';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import WarningAmberIcon from '@mui/icons-material/WarningAmber';
import VpnKeyIcon from '@mui/icons-material/VpnKey';
import { useNavigate } from 'react-router-dom';
import { useAppContext } from '../context/AppContext';
import { OperationMode } from '../context/AppContext';

interface ErrorHandlerProps {
  error: any;
  open: boolean;
  onClose: () => void;
}

/**
 * 统一错误处理组件
 */
const ErrorHandler: React.FC<ErrorHandlerProps> = ({ error, open, onClose }) => {
  const navigate = useNavigate();
  const { operationMode, setOperationMode } = useAppContext();
  
  // 提取错误信息
  const errorStatus = error?.response?.status;
  const errorData = error?.response?.data;
  const errorMessage = errorData?.message || errorData?.error || error?.message || '发生未知错误';
  
  // 根据错误类型确定标题和图标
  let title = '错误';
  let icon = <ErrorOutlineIcon color="error" sx={{ fontSize: 40 }} />;
  let severity = 'error';
  
  if (errorStatus === 401) {
    title = '认证失败';
    icon = <VpnKeyIcon color="error" sx={{ fontSize: 40 }} />;
  } else if (errorStatus === 403) {
    title = '权限不足';
    icon = <VpnKeyIcon color="warning" sx={{ fontSize: 40 }} />;
    severity = 'warning';
  } else if (errorStatus === 404) {
    title = '资源不存在';
    icon = <WarningAmberIcon color="warning" sx={{ fontSize: 40 }} />;
    severity = 'warning';
  }
  
  // 检查是否是上下文错误 - 上下文不匹配的权限错误
  const isContextError = errorStatus === 403 && 
    (errorMessage.includes('服务') || 
     errorMessage.includes('授权') || 
     errorMessage.includes('权限'));
  
  // 确定推荐的操作模式
  const getRecommendedMode = (): OperationMode | null => {
    // 如果错误消息包含特定关键词，推荐相应的模式
    if (errorMessage.includes('服务') && operationMode === 'NORMAL') {
      return 'SERVICE';
    }
    if (errorMessage.includes('系统') && operationMode !== 'ADMIN' && errorMessage.includes('管理')) {
      return 'ADMIN';
    }
    if (errorMessage.includes('个人') && operationMode !== 'NORMAL') {
      return 'NORMAL';
    }
    return null;
  };
  
  const recommendedMode = getRecommendedMode();
  
  // 切换到推荐的操作模式
  const handleSwitchMode = () => {
    if (recommendedMode) {
      setOperationMode(recommendedMode);
      
      // 根据新模式重定向到适当的页面
      switch (recommendedMode) {
        case 'SERVICE':
          navigate('/service-authorizations');
          break;
        case 'ADMIN':
          navigate('/admin');
          break;
        case 'NORMAL':
          navigate('/dashboard');
          break;
      }
      
      onClose();
    }
  };
  
  return (
    <Dialog
      open={open}
      onClose={onClose}
      aria-labelledby="error-dialog-title"
      aria-describedby="error-dialog-description"
      PaperProps={{
        sx: {
          borderTop: `4px solid ${severity === 'error' ? '#f44336' : '#ff9800'}`,
          minWidth: '320px'
        }
      }}
    >
      <DialogTitle id="error-dialog-title" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        {icon}
        <Typography variant="h6" component="span">
          {title}
        </Typography>
      </DialogTitle>
      <DialogContent>
        <DialogContentText id="error-dialog-description">
          {errorMessage}
        </DialogContentText>
        
        {isContextError && recommendedMode && (
          <Box sx={{ mt: 2, p: 1, bgcolor: 'action.hover', borderRadius: 1 }}>
            <Typography variant="body2" color="text.secondary">
              这可能是由于当前操作上下文不正确导致的。您可以尝试切换到
              {recommendedMode === 'SERVICE' ? '服务上下文' : 
                recommendedMode === 'ADMIN' ? '管理上下文' : '普通上下文'
              }。
            </Typography>
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        {isContextError && recommendedMode && (
          <Button onClick={handleSwitchMode} color="primary">
            切换到
            {recommendedMode === 'SERVICE' ? '服务上下文' : 
              recommendedMode === 'ADMIN' ? '管理上下文' : '普通上下文'
            }
          </Button>
        )}
        <Button onClick={onClose} color={severity === 'error' ? 'error' : 'warning'} autoFocus>
          关闭
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ErrorHandler; 