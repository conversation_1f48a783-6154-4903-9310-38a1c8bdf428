import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Card,
  CardContent,
  Tooltip,
  CircularProgress,
  Divider,
  Alert,
  Menu,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterListIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  Person as PersonIcon,
  ContactPhone as ContactIcon,
  Wc as GenderIcon,
  CalendarMonth as BirthDateIcon,
  Refresh as RefreshIcon,
  Male as MaleIcon,
  Female as FemaleIcon,
  QuestionMark as UnknownIcon,
  MoreVert as MoreVertIcon
} from '@mui/icons-material';
import axios from 'axios';
import { API_BASE_URL } from '../../config/api';
import { API_PATHS } from '../../config/apiPaths';
import { useSnackbar } from 'notistack';
import { format } from 'date-fns';
import { useTheme } from '@mui/material/styles';
import { useMediaQuery } from '@mui/material';
import { ThemeProvider, createTheme as createMuiTheme } from '@mui/material/styles';

// 患者接口
interface Patient {
  id: string;
  name: string;
  gender: string;
  birthDate: string;
  phoneNumber: string;
  email: string;
  address: string;
  userId: string;
  username?: string;
  isPrimary: boolean;
  createdAt: string;
}

/**
 * 患者数据管理页面
 * 管理员可以查看所有患者数据
 */
const PatientsManagementPage: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { enqueueSnackbar } = useSnackbar();

  const reducedFontSizeTheme = createMuiTheme(theme, {
    typography: {
      h1: { ...theme.typography.h1, fontSize: theme.typography.h1?.fontSize ? `calc(${theme.typography.h1.fontSize} - 0.4rem)`:'5.6rem' },
      h2: { ...theme.typography.h2, fontSize: theme.typography.h2?.fontSize ? `calc(${theme.typography.h2.fontSize} - 0.3rem)`:'3.45rem' },
      h3: { ...theme.typography.h3, fontSize: theme.typography.h3?.fontSize ? `calc(${theme.typography.h3.fontSize} - 0.25rem)`:'2.75rem' },
      h4: { ...theme.typography.h4, fontSize: theme.typography.h4?.fontSize ? `calc(${theme.typography.h4.fontSize} - 0.2rem)`:'1.925rem' },
      h5: { fontSize: '1.0rem' },
      h6: { fontSize: '0.85rem' },
      subtitle1: { fontSize: '0.75rem' },
      subtitle2: { fontSize: '0.65rem' },
      body1: { fontSize: '0.75rem' },
      body2: { fontSize: '0.65rem' },
      button: { fontSize: '0.65rem' },
      caption: { fontSize: '0.55rem' },
      overline: { fontSize: '0.55rem' },
    },
    components: {
      MuiInputLabel: { styleOverrides: { root: { fontSize: '0.75rem' } } },
      MuiMenuItem: { styleOverrides: { root: { fontSize: '0.75rem' } } },
      MuiTableCell: { styleOverrides: { root: { fontSize: '0.65rem' }, head: { fontSize: '0.7rem', fontWeight: 'bold' } } },
      MuiChip: { styleOverrides: { label: { fontSize: '0.55rem' }, labelSmall: { fontSize: '0.5rem' } } },
      MuiButton: { styleOverrides: { sizeSmall: { fontSize: '0.6rem' }, sizeMedium: { fontSize: '0.65rem' }, sizeLarge: { fontSize: '0.75rem' } } },
      MuiDialogTitle: { styleOverrides: { root: { fontSize: '0.85rem' } } },
      MuiDialogContentText: { styleOverrides: { root: { fontSize: '0.75rem' } } },
      MuiFormControlLabel: { styleOverrides: { label: { fontSize: '0.75rem' } } },
      MuiAlert: { styleOverrides: { message: { fontSize: '0.65rem' } } },
      MuiTablePagination: {
        styleOverrides: {
          caption: { fontSize: '0.65rem' },
          selectLabel: { fontSize: '0.65rem' },
          displayedRows: { fontSize: '0.65rem' }
        }
      }
    }
  });

  // 患者列表状态
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 搜索和筛选状态
  const [searchKeyword, setSearchKeyword] = useState('');
  const [filterOpen, setFilterOpen] = useState(false);
  const [genderFilter, setGenderFilter] = useState<string>('');
  const [userIdFilter, setUserIdFilter] = useState<string>('');

  // 分页状态
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // 删除确认对话框状态
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [patientToDelete, setPatientToDelete] = useState<Patient | null>(null);

  // 患者详情对话框状态
  const [detailDialogOpen, setDetailDialogOpen] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);

  // 更多操作菜单状态 (for mobile cards)
  const [mobileMenuAnchorEl, setMobileMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedPatientForMenu, setSelectedPatientForMenu] = useState<Patient | null>(null);

  // 初始加载患者数据
  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => {
    fetchPatients();
  }, [page, rowsPerPage]);

  // 获取患者列表
  const fetchPatients = useCallback(async (filterParams = {}) => {
    setLoading(true);
    setError(null);

    try {
      // 构建查询参数
      const params: any = {
        page,
        pageSize: rowsPerPage,
        ...filterParams
      };

      if (searchKeyword.trim()) {
        params.search = searchKeyword.trim();
      }

      if (genderFilter) {
        params.gender = genderFilter;
      }

      if (userIdFilter) {
        params.userId = userIdFilter;
      }

      // 使用API_PATHS常量确保路径一致性
      const response = await axios.get(`${API_BASE_URL}${API_PATHS.ADMIN.PATIENTS}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        },
        params
      });

      if (response.data) {
        setPatients(response.data.patients || []);
        setTotalCount(response.data.total || 0);
      } else {
        setPatients([]);
        setTotalCount(0);
      }
    } catch (err: any) {
      console.error('获取患者列表失败:', err);
      setError(err.response?.data?.message || '获取患者列表失败');
      enqueueSnackbar('获取患者列表失败', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  }, [page, rowsPerPage, searchKeyword, genderFilter, userIdFilter, enqueueSnackbar]);

  // 应用筛选条件
  const applyFilters = () => {
    setPage(0); // 重置到第一页
    fetchPatients({
      search: searchKeyword,
      gender: genderFilter,
      userId: userIdFilter
    });
  };

  // 重置筛选条件
  const resetFilters = () => {
    setSearchKeyword('');
    setGenderFilter('');
    setUserIdFilter('');
    setPage(0);
    fetchPatients({});
  };

  // 处理搜索输入变化
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchKeyword(event.target.value);
  };

  // 处理回车键搜索
  const handleSearchKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      applyFilters();
    }
  };

  // 处理分页变化
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  // 处理每页行数变化
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // 打开删除确认对话框
  const openDeleteDialog = (patient: Patient) => {
    setPatientToDelete(patient);
    setDeleteDialogOpen(true);
  };

  // 关闭删除确认对话框
  const closeDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setPatientToDelete(null);
  };

  // 删除患者
  const deletePatient = async () => {
    if (!patientToDelete) return;

    try {
      await axios.delete(`${API_BASE_URL}/api/admin/patients/${patientToDelete.id}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      enqueueSnackbar('患者数据已成功删除', { variant: 'success' });

      // 关闭对话框并刷新列表
      closeDeleteDialog();
      fetchPatients();
    } catch (err: any) {
      console.error('删除患者数据失败:', err);
      enqueueSnackbar('删除患者数据失败: ' + (err.response?.data?.message || err.message), { variant: 'error' });
    }
  };

  // 查看患者详情
  const viewPatientDetail = (patient: Patient) => {
    setSelectedPatient(patient);
    setDetailDialogOpen(true);
  };

  // 关闭详情对话框
  const closeDetailDialog = () => {
    setDetailDialogOpen(false);
    setSelectedPatient(null);
  };

  // 格式化时间
  const formatDateTime = (dateString: string) => {
    if (!dateString) return '未知';
    try {
      const date = new Date(dateString);
      return format(date, 'yyyy-MM-dd');
    } catch (error) {
      return '无效日期';
    }
  };

  // 格式化性别显示
  const formatGender = (gender: string) => {
    if (gender === 'MALE') return <Chip size="small" icon={<MaleIcon />} label="男" color="primary" variant="outlined" sx={{ mr: 1 }} />;
    if (gender === 'FEMALE') return <Chip size="small" icon={<FemaleIcon />} label="女" color="secondary" variant="outlined" sx={{ mr: 1 }} />;
    return <Chip size="small" icon={<UnknownIcon />} label="未知" variant="outlined" sx={{ mr: 1 }} />;
  };

  // 计算年龄
  const calculateAge = (birthDateString: string) => {
    if (!birthDateString) return '未知';
    try {
      const birthDate = new Date(birthDateString);
      const today = new Date();
      let age = today.getFullYear() - birthDate.getFullYear();
      const m = today.getMonth() - birthDate.getMonth();
      if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }
      return `${age} 岁`;
    } catch (error) {
      return '无效日期';
    }
  };

  // 移动端菜单处理
  const handleMobileMenuOpen = (event: React.MouseEvent<HTMLElement>, patient: Patient) => {
    setMobileMenuAnchorEl(event.currentTarget);
    setSelectedPatientForMenu(patient);
  };

  const handleMobileMenuClose = () => {
    setMobileMenuAnchorEl(null);
    setSelectedPatientForMenu(null);
  };

  const handleViewDetailFromMenu = () => {
    if (selectedPatientForMenu) {
      viewPatientDetail(selectedPatientForMenu);
    }
    handleMobileMenuClose();
  };

  const handleDeleteFromMenu = () => {
    if (selectedPatientForMenu) {
      openDeleteDialog(selectedPatientForMenu);
    }
    handleMobileMenuClose();
  };

  return (
    <ThemeProvider theme={reducedFontSizeTheme}>
      <Box sx={{ py: { xs: 1, sm: 2 } }}>
        <Typography variant="h6" component="h1" gutterBottom sx={{ mb: { xs: 1.5, sm: 2 } }}>
          患者数据管理
        </Typography>

        {/* 搜索和筛选区域 */}
        <Paper elevation={1} sx={{ p: { xs: '10px', sm: 2 }, mb: { xs: 1.5, sm: 3 } }}>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
            <Box sx={{ flex: '1 1 300px', minWidth: 0 }}>
              <TextField
                placeholder="搜索患者..."
                variant="outlined"
                size="small"
                fullWidth
                value={searchKeyword}
                onChange={handleSearchChange}
                onKeyPress={handleSearchKeyPress}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Box>

            <Box sx={{ flex: '1 1 300px', display: 'flex', gap: 1, justifyContent: 'flex-end', alignItems: 'center' }}>
              <Button
                variant="outlined"
                color="primary"
                startIcon={<FilterListIcon />}
                onClick={() => setFilterOpen(!filterOpen)}
              >
                高级筛选
              </Button>

              <Button
                variant="outlined"
                color="primary"
                startIcon={<RefreshIcon />}
                onClick={() => fetchPatients()}
                disabled={loading}
              >
                刷新
              </Button>
            </Box>

            {/* 高级筛选面板 */}
            {filterOpen && (
              <Box sx={{ width: '100%' }}>
                <Card variant="outlined">
                  <CardContent>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                      <Box sx={{ flex: '1 1 200px', minWidth: 0 }}>
                        <FormControl fullWidth size="small">
                          <InputLabel id="gender-filter-label">性别</InputLabel>
                          <Select
                            labelId="gender-filter-label"
                            value={genderFilter}
                            label="性别"
                            onChange={(e) => setGenderFilter(e.target.value)}
                          >
                            <MenuItem value="">全部</MenuItem>
                            <MenuItem value="MALE">男</MenuItem>
                            <MenuItem value="FEMALE">女</MenuItem>
                          </Select>
                        </FormControl>
                      </Box>

                      <Box sx={{ flex: '1 1 200px', minWidth: 0 }}>
                        <TextField
                          label="用户ID"
                          size="small"
                          fullWidth
                          value={userIdFilter}
                          onChange={(e) => setUserIdFilter(e.target.value)}
                        />
                      </Box>

                      <Box sx={{ width: '100%', display: 'flex', justifyContent: 'flex-end', mt: 1 }}>
                        <Button
                          variant="outlined"
                          color="secondary"
                          onClick={resetFilters}
                          sx={{ mr: 1 }}
                        >
                          重置
                        </Button>
                        <Button
                          variant="contained"
                          color="primary"
                          onClick={applyFilters}
                        >
                          应用筛选
                        </Button>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Box>
            )}
          </Box>
        </Paper>

        {/* 患者列表 */}
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 'calc(100vh - 300px)' }}>
            <CircularProgress size={40} />
          </Box>
        ) : error ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 'calc(100vh - 300px)' }}>
            <Typography variant="body2" color="error" sx={{ my: 2 }}>
              {error}
            </Typography>
          </Box>
        ) : patients.length === 0 ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 'calc(100vh - 300px)' }}>
            <Typography variant="body2" color="textSecondary" sx={{ my: 2 }}>
              {searchKeyword || genderFilter || userIdFilter ?
                '没有匹配的患者数据' : '暂无患者数据'}
            </Typography>
          </Box>
        ) : (
          <>
            {isMobile ? (
              // 移动端卡片视图
              <Box>
                {patients.length === 0 ? (
                  <Typography sx={{ textAlign: 'center', my: 4 }}>
                    {searchKeyword || genderFilter || userIdFilter ?
                      '没有匹配的患者数据' : '暂无患者数据'}
                  </Typography>
                ) : (
                  patients.map((patient) => (
                    <Card key={patient.id} sx={{ mb: 2, boxShadow: 3 }}>
                      <CardContent sx={{ px: '10px', '&:last-child': { pb: '10px' } }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                          <Typography variant="h6" component="div" sx={{ flexGrow: 1, mr: 1, wordBreak: 'break-word', fontWeight: 'bold' }}>
                            {patient.name}
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            {patient.isPrimary && <Chip label="主联系人" color="success" size="small" variant="outlined" sx={{ mr: 0.5 }} />}
                            <IconButton size="small" onClick={(event) => handleMobileMenuOpen(event, patient)} sx={{ p: 0.5 }}>
                              <MoreVertIcon />
                            </IconButton>
                          </Box>
                        </Box>

                        <Box sx={{ display: 'flex', alignItems: 'center', color: 'text.secondary', mb: 0.5 }}>
                          <GenderIcon fontSize="inherit" sx={{ mr: 0.5 }} />
                          {formatGender(patient.gender)}
                          <BirthDateIcon fontSize="inherit" sx={{ mr: 0.5, ml: 1 }} />
                          {calculateAge(patient.birthDate)} ({formatDateTime(patient.birthDate)})
                        </Box>

                        <Divider sx={{ my: 1 }} />

                        <Typography variant="body2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                          <ContactIcon fontSize="inherit" sx={{ mr: 0.5 }} /> 电话: {patient.phoneNumber || '未提供'}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
                          <PersonIcon fontSize="inherit" sx={{ mr: 0.5 }} /> 关联用户: {patient.username || '未关联'} ({patient.userId.substring(0,8)}...)
                        </Typography>
                      </CardContent>
                    </Card>
                  ))
                )}
                <Menu
                  anchorEl={mobileMenuAnchorEl}
                  open={Boolean(mobileMenuAnchorEl)}
                  onClose={handleMobileMenuClose}
                  MenuListProps={{
                    'aria-labelledby': 'patient-card-actions-button',
                  }}
                >
                  <MenuItem onClick={handleViewDetailFromMenu}>
                    <ListItemIcon><VisibilityIcon fontSize="small" /></ListItemIcon>
                    <ListItemText>查看详情</ListItemText>
                  </MenuItem>
                  {/* 可以在这里添加编辑病患的菜单项，如果后续实现了编辑功能 */}
                  {/* <MenuItem onClick={handleEditFromMenu}>
                    <ListItemIcon><EditIcon fontSize="small" /></ListItemIcon>
                    <ListItemText>编辑信息</ListItemText>
                  </MenuItem> */}
                  <MenuItem onClick={handleDeleteFromMenu} sx={{ color: 'error.main' }}>
                    <ListItemIcon><DeleteIcon fontSize="small" color="error" /></ListItemIcon>
                    <ListItemText>删除患者</ListItemText>
                  </MenuItem>
                </Menu>
              </Box>
            ) : (
              // 桌面端表格视图
              <Paper elevation={2} sx={{ width: '100%', overflow: 'hidden' }}>
                <TableContainer sx={{ maxHeight: 'calc(100vh - 300px)' }}>
                  <Table stickyHeader size={isMobile ? "small" : "medium"}>
                    <TableHead>
                      <TableRow>
                        <TableCell>姓名</TableCell>
                        <TableCell>性别</TableCell>
                        <TableCell>年龄</TableCell>
                        <TableCell>联系方式</TableCell>
                        <TableCell>创建时间</TableCell>
                        <TableCell align="center">操作</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {patients.map((patient) => (
                        <TableRow
                          key={patient.id}
                          hover
                          sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                        >
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <PersonIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                              <Typography variant="body2">{patient.name}</Typography>
                            </Box>
                          </TableCell>

                          <TableCell>
                            {formatGender(patient.gender)}
                          </TableCell>

                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <BirthDateIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                              <Tooltip title={`出生日期: ${formatDateTime(patient.birthDate)}`}>
                                <Typography variant="body2">{calculateAge(patient.birthDate)}</Typography>
                              </Tooltip>
                            </Box>
                          </TableCell>

                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <ContactIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                              <Tooltip title={`邮箱: ${patient.email || '未设置'}`}>
                                <Typography variant="body2">{patient.phoneNumber || '未设置'}</Typography>
                              </Tooltip>
                            </Box>
                          </TableCell>

                          <TableCell>
                            {formatDateTime(patient.createdAt)}
                          </TableCell>

                          <TableCell align="center">
                            <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                              <Tooltip title="查看详情">
                                <IconButton
                                  size="small"
                                  onClick={() => viewPatientDetail(patient)}
                                  sx={{ mr: 1 }}
                                >
                                  <VisibilityIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>

                              <Tooltip title="删除患者">
                                <IconButton
                                  size="small"
                                  color="error"
                                  onClick={() => openDeleteDialog(patient)}
                                >
                                  <DeleteIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>

                {/* 分页控件 */}
                <TablePagination
                  rowsPerPageOptions={[5, 10, 25, 50]}
                  component="div"
                  count={totalCount}
                  rowsPerPage={rowsPerPage}
                  page={page}
                  onPageChange={handleChangePage}
                  onRowsPerPageChange={handleChangeRowsPerPage}
                  labelRowsPerPage="每页行数:"
                  labelDisplayedRows={({ from, to, count }) => `${from}-${to} / 共${count}条`}
                />
              </Paper>
            )}
          </>
        )}

        {/* 患者详情对话框 */}
        <Dialog
          open={detailDialogOpen}
          onClose={closeDetailDialog}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <PersonIcon sx={{ mr: 1 }} />
              患者详情
            </Box>
          </DialogTitle>
          <DialogContent dividers>
            {!selectedPatient ? (
              <Alert severity="error">未能加载患者详情</Alert>
            ) : (
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>基本信息</Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                      <Box sx={{ flex: '1 1 calc(33% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">姓名</Typography>
                        <Typography variant="body1">{selectedPatient.name}</Typography>
                      </Box>
                      <Box sx={{ flex: '1 1 calc(33% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">性别</Typography>
                        <Typography variant="body1" component="div">{formatGender(selectedPatient.gender)}</Typography>
                      </Box>
                      <Box sx={{ flex: '1 1 calc(33% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">出生日期</Typography>
                        <Typography variant="body1">{formatDateTime(selectedPatient.birthDate)}</Typography>
                      </Box>
                      <Box sx={{ flex: '1 1 calc(33% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">电话</Typography>
                        <Typography variant="body1">{selectedPatient.phoneNumber || '未设置'}</Typography>
                      </Box>
                      <Box sx={{ flex: '1 1 calc(33% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">邮箱</Typography>
                        <Typography variant="body1">{selectedPatient.email || '未设置'}</Typography>
                      </Box>
                      <Box sx={{ flex: '1 1 calc(33% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">地址</Typography>
                        <Typography variant="body1">{selectedPatient.address || '未设置'}</Typography>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>账户信息</Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                      <Box sx={{ flex: '1 1 calc(33% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">用户ID</Typography>
                        <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>{selectedPatient.userId}</Typography>
                      </Box>
                      <Box sx={{ flex: '1 1 calc(33% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">用户名</Typography>
                        <Typography variant="body2">{selectedPatient.username || '未知'}</Typography>
                      </Box>
                      <Box sx={{ flex: '1 1 calc(33% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">主账户关系</Typography>
                        <Chip
                          label={selectedPatient.isPrimary ? "主账户" : "家庭成员"}
                          color={selectedPatient.isPrimary ? "primary" : "default"}
                          size="small"
                        />
                      </Box>
                      <Box sx={{ flex: '1 1 calc(33% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">创建时间</Typography>
                        <Typography variant="body2">{formatDateTime(selectedPatient.createdAt)}</Typography>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button
              color="error"
              onClick={() => {
                closeDetailDialog();
                if (selectedPatient) {
                  openDeleteDialog(selectedPatient);
                }
              }}
              startIcon={<DeleteIcon />}
            >
              删除患者
            </Button>
            <Button onClick={closeDetailDialog} color="primary">
              关闭
            </Button>
          </DialogActions>
        </Dialog>

        {/* 删除确认对话框 */}
        <Dialog
          open={deleteDialogOpen}
          onClose={closeDeleteDialog}
        >
          <DialogTitle>
            确认删除患者数据
          </DialogTitle>
          <DialogContent>
            <DialogContentText>
              确定要删除此患者数据吗？删除后将无法恢复，与此患者相关的病历、记录和报告也将被删除。
            </DialogContentText>
            {patientToDelete && (
              <Box component="div" sx={{ mt: 2 }}>
                <Typography variant="subtitle2" gutterBottom>患者信息：</Typography>
                <Typography variant="body2">姓名: {patientToDelete.name}</Typography>
                <Typography variant="body2">性别: {patientToDelete.gender === 'MALE' ? '男' : patientToDelete.gender === 'FEMALE' ? '女' : '未知'}</Typography>
                {patientToDelete.birthDate && (
                  <Typography variant="body2">出生日期: {formatDateTime(patientToDelete.birthDate)}</Typography>
                )}
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={closeDeleteDialog}>
              取消
            </Button>
            <Button
              onClick={deletePatient}
              color="error"
              variant="contained"
              startIcon={<DeleteIcon />}
            >
              确认删除
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </ThemeProvider>
  );
};

export default PatientsManagementPage;