import React from 'react';
import { Box, Chip, Tooltip } from '@mui/material';
import { useAppContext } from '../context/AppContext';
import PersonIcon from '@mui/icons-material/Person';
import SupportAgentIcon from '@mui/icons-material/SupportAgent';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';

/**
 * 上下文指示器组件，显示用户当前的操作模式
 */
const ContextIndicator: React.FC = () => {
  const { operationMode } = useAppContext();
  
  // 根据操作模式设置不同的颜色和图标
  const getChipProps = () => {
    switch (operationMode) {
      case 'SERVICE':
        return {
          icon: <SupportAgentIcon />,
          label: '服务上下文',
          color: 'primary' as const,
          tooltip: '您正在服务上下文中操作，可以访问被授权的患者数据'
        };
      case 'ADMIN':
        return {
          icon: <AdminPanelSettingsIcon />,
          label: '管理上下文',
          color: 'error' as const,
          tooltip: '您正在管理上下文中操作，可以访问全部系统数据'
        };
      case 'NORMAL':
      default:
        return {
          icon: <PersonIcon />,
          label: '个人上下文',
          color: 'success' as const,
          tooltip: '您正在个人上下文中操作，只能访问您自己的数据'
        };
    }
  };
  
  const chipProps = getChipProps();
  
  return (
    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', m: 1 }}>
      <Tooltip title={chipProps.tooltip} arrow>
        <Chip
          icon={chipProps.icon}
          label={chipProps.label}
          color={chipProps.color}
          size="small"
          variant="outlined"
          sx={{ 
            fontWeight: 'bold',
            '& .MuiChip-icon': { fontSize: 16 },
            '&.MuiChip-colorSuccess': { borderColor: 'success.main', color: 'success.main' },
            '&.MuiChip-colorPrimary': { borderColor: 'primary.main', color: 'primary.main' },
            '&.MuiChip-colorError': { borderColor: 'error.main', color: 'error.main' },
          }}
        />
      </Tooltip>
    </Box>
  );
};

export default ContextIndicator; 