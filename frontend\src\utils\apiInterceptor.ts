// API请求拦截器
// 用于监控和记录API请求，配合错误监控使用

import axios, { AxiosRequestConfig, AxiosResponse, AxiosError, isAxiosError, CanceledError } from 'axios';
import { logApiError } from './apiErrorMonitor';
import { getToken } from './auth';

// 请求计数器
let requestCount = 0;

// 请求开始时间记录
const requestStartTimes = new Map<number, number>();

/**
 * 请求拦截器
 * @param config - 请求配置
 */
export function requestInterceptor(config: AxiosRequestConfig): AxiosRequestConfig {
  // 生成请求ID
  const requestId = ++requestCount;
  
  // 记录请求开始时间
  requestStartTimes.set(requestId, Date.now());
  
  // 添加请求ID到请求头
  config.headers = {
    ...config.headers,
    'X-Request-ID': requestId.toString()
  };
  
  // 更新总请求数
  const totalRequests = parseInt(localStorage.getItem('total_api_requests') || '0');
  localStorage.setItem('total_api_requests', (totalRequests + 1).toString());
  
  // 检查认证令牌
  const token = getToken();
  const hasAuthHeader = config.headers?.['Authorization'] ? true : false;
  
  // 记录认证信息
  const authInfo = {
    hasToken: !!token,
    tokenLength: token ? token.length : 0,
    tokenPrefix: token ? token.substring(0, 6) + '...' : 'none',
    hasAuthHeader,
    authHeader: hasAuthHeader 
      ? `${(config.headers?.['Authorization'] as string).substring(0, 12)}...` 
      : 'none'
  };
  
  // 记录请求日志
  console.log(`[API请求] ${config.method?.toUpperCase()} ${config.url}`, {
    请求ID: requestId,
    参数: config.params,
    数据: config.data,
    认证信息: authInfo,
    请求头: config.headers
  });
  
  return config;
}

/**
 * 响应拦截器
 * @param response - 响应对象
 */
export function responseInterceptor(response: AxiosResponse): AxiosResponse {
  const requestId = parseInt(response.config.headers['X-Request-ID'] || '0');
  const startTime = requestStartTimes.get(requestId);
  
  if (startTime) {
    const duration = Date.now() - startTime;
    requestStartTimes.delete(requestId);
    
    // 记录响应日志
    console.log(`[API响应] ${response.config.method?.toUpperCase()} ${response.config.url}`, {
      请求ID: requestId,
      状态码: response.status,
      耗时: `${duration}ms`,
      数据: response.data
    });
  }
  
  return response;
}

/**
 * 错误拦截器
 * @param error - 错误对象
 */
export function errorInterceptor(error: unknown): Promise<never> {
  // 检查是否为请求取消错误
  if (error instanceof CanceledError) {
    const errorObj = error as AxiosError;
    const requestId = parseInt(errorObj.config?.headers?.['X-Request-ID'] as string || '0');
    const startTime = requestStartTimes.get(requestId);
    
    if (startTime) {
      const duration = Date.now() - startTime;
      requestStartTimes.delete(requestId);
      
      // 对于取消的请求，只记录调试信息，使用debug级别日志而不是error
      console.debug(`[API取消] ${errorObj.config?.method?.toUpperCase()} ${errorObj.config?.url}`, {
        请求ID: requestId,
        耗时: `${duration}ms`,
        原因: '请求已取消'
      });
    }
    
    return Promise.reject(error);
  }
  
  // 处理常规Axios错误
  if (isAxiosError(error)) {
    const errorObj = error as AxiosError;
    const requestId = parseInt(errorObj.config?.headers?.['X-Request-ID'] as string || '0');
    const startTime = requestStartTimes.get(requestId);
    
    if (startTime) {
      const duration = Date.now() - startTime;
      requestStartTimes.delete(requestId);
      
      // 检查是否为请求被取消的错误
      if (errorObj.message === 'canceled') {
        // 对于取消的请求，只记录调试信息，使用debug级别日志而不是error
        console.debug(`[API取消] ${errorObj.config?.method?.toUpperCase()} ${errorObj.config?.url}`, {
          请求ID: requestId,
          耗时: `${duration}ms`,
          原因: '请求已取消'
        });
      } else {
        // 检查认证信息
        const token = getToken();
        const hasAuthHeader = errorObj.config?.headers?.['Authorization'] ? true : false;
        
        // 记录认证信息
        const authInfo = {
          hasToken: !!token,
          tokenLength: token ? token.length : 0,
          tokenPrefix: token ? token.substring(0, 6) + '...' : 'none',
          hasAuthHeader,
          authHeader: hasAuthHeader 
            ? `${(errorObj.config?.headers?.['Authorization'] as string).substring(0, 12)}...` 
            : 'none'
        };
        
        // 记录错误日志
        console.error(`[API错误] ${errorObj.config?.method?.toUpperCase()} ${errorObj.config?.url}`, {
          请求ID: requestId,
          状态码: errorObj.response?.status,
          耗时: `${duration}ms`,
          错误: errorObj.message,
          认证信息: authInfo,
          响应数据: errorObj.response?.data,
          请求头: errorObj.config?.headers
        });
        
        // 记录API错误
        logApiError(errorObj, errorObj.config);
      }
    }
  } else {
    // 处理非Axios错误
    console.error('[API错误] 未知错误类型:', error);
  }
  
  return Promise.reject(error);
}

/**
 * 设置拦截器
 * @param instance - Axios实例
 */
export function setupInterceptors(instance: any): void {
  instance.interceptors.request.use(requestInterceptor);
  instance.interceptors.response.use(responseInterceptor, errorInterceptor);
}

/**
 * 获取请求统计
 */
export function getRequestStats(): {
  totalRequests: number;
  activeRequests: number;
  averageResponseTime: number;
} {
  const totalRequests = parseInt(localStorage.getItem('total_api_requests') || '0');
  const activeRequests = requestStartTimes.size;
  
  // 计算平均响应时间
  let totalDuration = 0;
  let completedRequests = 0;
  
  requestStartTimes.forEach((startTime, requestId) => {
    const duration = Date.now() - startTime;
    if (duration > 0) {
      totalDuration += duration;
      completedRequests++;
    }
  });
  
  const averageResponseTime = completedRequests > 0
    ? Math.round(totalDuration / completedRequests)
    : 0;
  
  return {
    totalRequests,
    activeRequests,
    averageResponseTime
  };
}

/**
 * 重置请求统计
 */
export function resetRequestStats(): void {
  localStorage.setItem('total_api_requests', '0');
  requestStartTimes.clear();
  requestCount = 0;
} 