/**
 * 授权服务
 * 处理用户之间的授权关系，包括授权创建、管理和权限控制
 */
import apiClient from './apiClient';
import { API_PATHS, getWebSocketUrl } from '../config/apiPaths';
import { logApiError } from '../utils/apiErrorMonitor';

// 授权类型接口
export interface Authorization {
  id: string;
  authorizerId: string;
  authorizedId: string;
  patientId?: string;
  diseaseId?: string;
  status: string | boolean; // 兼容字符串和布尔类型
  privacyLevel: string;
  createdAt: string;
  updatedAt: string;
  authorizer?: any;
  authorized?: any;
  patient?: any;
  // 兼容旧版字段命名
  authorizer_id?: string;
  authorized_id?: string;
  privacy_level?: string;
  authorized_switch?: boolean;
  authorizer_switch?: boolean;
  created_at?: string;
}

// API响应接口
export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data: T;
  authError?: boolean;
  networkError?: boolean;
  isServicePage?: boolean;
  error?: any;
}

/**
 * 获取用户作为授权人的授权列表
 */
export const getAuthorizationsAsAuthorizer = async (): Promise<ApiResponse<Authorization[]>> => {
  try {
    console.log('[授权服务] 开始获取用户作为授权人的授权列表');

    // 验证token
    const token = localStorage.getItem('token');
    if (!token) {
      console.error('[授权服务] 未找到token，无法获取授权列表');
      return {
        success: false,
        message: '未找到授权令牌，请重新登录',
        data: [],
        authError: true
      };
    }
    console.log('[授权服务] 当前token状态:', token ? '存在' : '不存在');

    try {
      // 使用API配置中心获取路径
      const path = API_PATHS.SERVICE.AUTHORIZATION.AS_AUTHORIZER;
      console.log(`[授权服务] 使用路径: ${path} 获取授权人列表`);

      const response = await apiClient.get(path);
      console.log('[授权服务] 获取授权人授权列表响应:', {
        状态码: response.status,
        数据长度: response.data && response.data.data ? response.data.data.length : 0
      });

      return response.data;
    } catch (requestError: any) {
      // 检查是否为认证错误
      if (requestError.isAuthError || (requestError.response && requestError.response.status === 401)) {
        console.log('[授权服务] 捕获到授权人授权列表认证错误，返回友好错误信息');
        return {
          success: false,
          message: '您的登录状态已过期，请重新登录',
          data: [],
          authError: true
        };
      }

      // 其他请求错误也返回友好信息
      console.error('[授权服务] 获取授权人授权列表请求失败:', requestError);
      logApiError(requestError);
      return {
        success: false,
        message: requestError.message || '获取授权列表失败',
        data: []
      };
    }
  } catch (error: any) {
    console.error('[授权服务] 获取授权人授权列表失败:', error);
    logApiError(error);

    // 记录更详细的错误信息
    if (error.response) {
      console.error('[授权服务] 错误响应:', {
        状态码: error.response.status,
        数据: error.response.data,
        响应头: error.response.headers
      });
    }

    // 返回友好错误信息而不是抛出异常
    return {
      success: false,
      message: error.message || '获取授权列表时发生错误',
      data: []
    };
  }
};

/**
 * 获取用户作为被授权人的授权列表
 * 优化错误处理，减少日志输出
 */
export const getAuthorizationsAsAuthorized = async (retryCount = 0): Promise<ApiResponse<Authorization[]>> => {
  try {
    // 简化日志，只在开发环境或首次请求时输出
    console.log('[授权服务] 尝试获取被授权列表', retryCount > 0 ? `(重试: ${retryCount})` : '');

    // 验证token
    const token = localStorage.getItem('token');
    if (!token) {
      console.error('[授权服务] 未找到token，无法获取授权列表');
      return {
        success: false,
        message: '未找到授权令牌，请重新登录',
        data: [],
        authError: true
      };
    }

    console.log('[授权服务] token检查通过，准备发送请求');

    // 发送请求 - 添加时间戳防止缓存
    try {
      const timestamp = new Date().getTime();
      // 使用API配置中心获取路径
      const path = `${API_PATHS.SERVICE.AUTHORIZATION.AS_AUTHORIZED}?_t=${timestamp}`;
      console.log(`[授权服务] 使用路径: ${path} 获取被授权列表`);

      const response = await apiClient.get(path);
      console.log('[授权服务] 获取被授权列表成功:', response.status);

      // 将授权列表缓存到localStorage中，供其他服务访问权限级别
      if (response.data && response.data.success && Array.isArray(response.data.data)) {
        try {
          localStorage.setItem('authorizedList', JSON.stringify(response.data.data));
          console.log('[授权服务] 授权列表已缓存到localStorage');
        } catch (cacheError) {
          console.error('[授权服务] 缓存授权列表失败:', cacheError);
        }
      }

      return response.data;
    } catch (requestError: any) {
      // 记录详细的错误信息
      console.error('[授权服务] 获取授权列表请求失败:', {
        错误类型: requestError.name,
        错误消息: requestError.message,
        响应状态: requestError.response?.status,
        响应数据: requestError.response?.data,
        是否网络错误: requestError.message === 'Network Error' || requestError.code === 'ERR_NETWORK',
        是否认证错误: requestError.isAuthError || (requestError.response && requestError.response.status === 401)
      });

      // 记录API错误
      logApiError(requestError);

      // 网络错误特殊处理
      if (requestError.message === 'Network Error' || requestError.code === 'ERR_NETWORK') {
        return {
          success: false,
          message: '网络连接失败，请检查网络连接',
          data: [],
          networkError: true
        };
      }

      // 处理认证错误
      if (requestError.isAuthError || (requestError.response && requestError.response.status === 401)) {
        // 检查是否在服务页面 - 更简化的页面路径检测
        const currentPath = window.location.pathname;
        const isServicePage = currentPath.includes('/service');

        return {
          success: false,
          message: '您的登录状态已过期，请重新登录',
          data: [],
          authError: true,
          isServicePage
        };
      }

      // 其他请求错误
      return {
        success: false,
        message: requestError.message || '获取授权列表时发生错误',
        data: [],
        error: requestError
      };
    }
  } catch (error: any) {
    // 减少错误日志的详细程度
    console.error('[授权服务] 获取授权列表失败');
    logApiError(error);

    return {
      success: false,
      message: error.message || '获取授权列表时发生错误',
      data: [],
      error
    };
  }
};

// 授权创建参数接口
export interface CreateAuthorizationParams {
  authorizerId: string;
  authorizedId: string;
  patientId?: string;
  privacyLevel?: string;
  // 兼容下划线命名格式
  authorizer_id?: string;
  authorized_id?: string;
  patient_id?: string;
  privacy_level?: string;
}

/**
 * 创建授权请求
 */
export const createAuthorization = async (data: CreateAuthorizationParams): Promise<ApiResponse<Authorization>> => {
  try {
    console.log('[授权服务] 开始创建授权');

    const path = API_PATHS.SERVICE.AUTHORIZATION.BASE;
    console.log(`[授权服务] 使用路径: ${path}`);

    const response = await apiClient.post(path, data);
    return response.data;
  } catch (error: any) {
    console.error('[授权服务] 创建授权失败:', error);
    logApiError(error);
    throw error;
  }
};

/**
 * 更新授权状态
 * @param id - 授权ID
 * @param switchValue - 开关状态（开/关）
 */
export const updateAuthorizationStatus = async (id: string, switchValue: boolean): Promise<ApiResponse<Authorization>> => {
  try {
    console.log(`[授权服务] 开始更新授权${id}状态为: ${switchValue}`);

    // 构建API路径
    const path = `${API_PATHS.SERVICE.AUTHORIZATION.BASE}/${id}/status`;
    console.log(`[授权服务] 使用路径: ${path}`);

    // 准备请求数据 - 使用switch_value参数名与后端匹配
    const payload = {
      switch_value: switchValue
    };

    const response = await apiClient.patch(path, payload);
    return response.data;
  } catch (error: any) {
    console.error(`[授权服务] 更新授权${id}状态失败:`, error);
    logApiError(error);
    throw error;
  }
};

/**
 * 更新授权隐私级别
 * @param id - 授权ID
 * @param privacyLevel - 隐私级别
 */
export const updateAuthorizationPrivacyLevel = async (id: string, privacyLevel: string): Promise<ApiResponse<Authorization>> => {
  try {
    console.log(`[授权服务] 开始更新授权${id}隐私级别为: ${privacyLevel}`);

    // 构建API路径
    const path = `${API_PATHS.SERVICE.AUTHORIZATION.BASE}/${id}/privacy`;
    console.log(`[授权服务] 使用路径: ${path}`);

    // 准备请求数据
    const payload = {
      privacyLevel
    };

    const response = await apiClient.patch(path, payload);
    return response.data;
  } catch (error: any) {
    console.error('[授权服务] 更新授权隐私级别失败:', error);
    logApiError(error);
    throw error;
  }
};

/**
 * 清除通知
 * @param id - 通知ID
 */
export const clearNotification = async (id: string): Promise<ApiResponse<{ success: boolean }>> => {
  try {
    console.log(`[授权服务] 清除通知: ${id}`);

    // 构建API路径
    const path = `/notifications/${id}/read`;
    console.log(`[授权服务] 使用路径: ${path}`);

    const response = await apiClient.post(path);
    return response.data;
  } catch (error: any) {
    console.error(`[授权服务] 清除通知失败:`, error);
    logApiError(error);
    throw error;
  }
};

/**
 * 搜索可授权的服务用户
 */
export const searchServiceUsers = async (query = ''): Promise<ApiResponse<any[]>> => {
  try {
    console.log('[授权服务] 开始搜索服务用户, 查询:', query);

    const params = query ? `?query=${encodeURIComponent(query)}` : '';
    const path = `${API_PATHS.SERVICE.AUTHORIZATION.BASE}/service-users${params}`;
    console.log(`[授权服务] 使用路径: ${path}`);

    const response = await apiClient.get(path);
    return response.data;
  } catch (error: any) {
    console.error('[授权服务] 搜索服务用户失败:', error);
    logApiError(error);
    throw error;
  }
};

/**
 * 搜索可授权的普通用户（用于反向授权）
 */
export const searchNormalUsers = async (query = ''): Promise<ApiResponse<any[]>> => {
  try {
    console.log('[授权服务] 开始搜索普通用户, 查询:', query);

    const params = query ? `?query=${encodeURIComponent(query)}` : '';
    const path = `${API_PATHS.SERVICE.AUTHORIZATION.BASE}/normal-users${params}`;
    console.log(`[授权服务] 使用路径: ${path}`);

    const response = await apiClient.get(path);
    return response.data;
  } catch (error: any) {
    console.error('[授权服务] 搜索普通用户失败:', error);
    logApiError(error);
    throw error;
  }
};

/**
 * 删除授权
 * @param id - 授权ID
 */
export const deleteAuthorization = async (id: string): Promise<ApiResponse<{ success: boolean }>> => {
  try {
    console.log(`[授权服务] 开始删除授权: ${id}`);

    // 构建API路径
    const path = `${API_PATHS.SERVICE.AUTHORIZATION.BASE}/${id}`;
    console.log(`[授权服务] 使用路径: ${path}`);

    const response = await apiClient.delete(path);
    return response.data;
  } catch (error: any) {
    console.error('[授权服务] 删除授权失败:', error);
    logApiError(error);
    throw error;
  }
};

/**
 * 删除已撤销的授权
 * @param id - 授权ID
 */
export const deleteRevokedAuthorization = async (id: string): Promise<ApiResponse<{ success: boolean }>> => {
  try {
    console.log(`[授权服务] 开始删除已撤销授权: ${id}`);

    // 构建API路径 - 修正为正确的API端点
    const path = `${API_PATHS.SERVICE.AUTHORIZATION.BASE}/${id}`;
    console.log(`[授权服务] 使用路径: ${path}`);

    const response = await apiClient.delete(path);
    return response.data;
  } catch (error: any) {
    console.error('[授权服务] 删除已撤销授权失败:', error);
    logApiError(error);
    throw error;
  }
};

/**
 * 根据ID获取授权信息
 * @param id - 授权ID
 */
export const getAuthorizationById = async (id: string): Promise<ApiResponse<Authorization>> => {
  try {
    console.log(`[授权服务] 开始获取授权详情: ${id}`);

    // 构建API路径
    const path = `${API_PATHS.SERVICE.AUTHORIZATION.BASE}/${id}`;
    console.log(`[授权服务] 使用路径: ${path}`);

    const response = await apiClient.get(path);
    return response.data;
  } catch (error: any) {
    console.error('[授权服务] 获取授权详情失败:', error);
    logApiError(error);
    throw error;
  }
};

// WebSocket客户端类
class WebSocketClient {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 3; // 增加重试次数
  private reconnectDelay = 2000; // 增加重试间隔
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private token: string | null = null;
  private connectionPromise: Promise<WebSocket> | null = null;

  // 检查后端可用性
  private async checkBackendAvailability(): Promise<boolean> {
    try {
      const response = await apiClient.get(API_PATHS.SYSTEM.HEALTH);
      return response.status === 200;
    } catch (error) {
      console.error('[WebSocketClient] 检查后端可用性失败:', error);
      return false;
    }
  }

  // 获取token
  private async getToken(): Promise<string | null> {
    return localStorage.getItem('token');
  }

  // 初始化WebSocket连接
  async init(): Promise<WebSocket> {
    // 如果已经有一个连接尝试在进行中，返回那个Promise
    if (this.connectionPromise) {
      return this.connectionPromise;
    }

    // 创建一个新的连接Promise
    this.connectionPromise = new Promise<WebSocket>(async (resolve, reject) => {
      try {
        // 检查后端是否可用
        const isAvailable = await this.checkBackendAvailability();
        if (!isAvailable) {
          // 减少日志输出
          // console.log('[WebSocketClient] 后端服务不可用，稍后重试');
          this.scheduleReconnect();
          reject(new Error('后端服务不可用'));
          return;
        }

        // 获取token
        this.token = await this.getToken();
        if (!this.token) {
          // 减少日志输出
          // console.log('[WebSocketClient] 未获取到token，无法建立WebSocket连接');
          reject(new Error('未获取到token'));
          return;
        }

        // 检查是否已经有活跃的WebSocket连接
        if (this.ws && (this.ws.readyState === WebSocket.OPEN || this.ws.readyState === WebSocket.CONNECTING)) {
          // 减少日志输出
          // console.log('[WebSocketClient] WebSocket连接已存在且活跃');
          resolve(this.ws);
          return;
        }

        // 关闭现有连接（如果存在）
        if (this.ws) {
          // 减少日志输出
          // console.log('[WebSocketClient] 关闭现有WebSocket连接');
          try {
            this.ws.close();
          } catch (closeError) {
            console.warn('[WebSocketClient] 关闭现有WebSocket连接时发生错误:', closeError);
          }
          this.ws = null;
        }

        // 建立WebSocket连接
        let wsUrl = getWebSocketUrl();

        // 添加token参数
        wsUrl = `${wsUrl}${wsUrl.includes('?') ? '&' : '?'}token=${this.token}`;

        console.log(`[WebSocketClient] 尝试连接到WebSocket服务器: ${wsUrl.replace(/token=([^&]+)/, 'token=***')}`);

        // 使用更长的超时时间创建WebSocket
        const ws = new WebSocket(wsUrl);

        // 设置连接超时
        const connectionTimeout = setTimeout(() => {
          console.error('[WebSocketClient] WebSocket连接超时');
          ws.close();
          reject(new Error('WebSocket连接超时'));
        }, 10000); // 增加超时时间到10秒

        // 设置事件处理器
        ws.onopen = () => {
          console.log('[WebSocketClient] WebSocket连接已建立成功');
          clearTimeout(connectionTimeout);
          this.ws = ws;
          this.reconnectAttempts = 0;
          this.startHeartbeat();
          resolve(ws);
        };

        ws.onerror = (error) => {
          console.error('[WebSocketClient] WebSocket错误:', error);
          clearTimeout(connectionTimeout);
          this.ws = null;
          reject(error);
        };

        ws.onclose = (event) => {
          console.log(`[WebSocketClient] WebSocket连接已关闭: 代码=${event.code}, 原因=${event.reason}`);
          clearTimeout(connectionTimeout);
          this.stopHeartbeat();
          this.ws = null;
          this.scheduleReconnect();
        };

        ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            // 减少日志输出
            // console.log('[WebSocketClient] 收到WebSocket消息:', data);
            // 处理消息...
          } catch (error) {
            console.error('[WebSocketClient] 处理WebSocket消息失败:', error);
          }
        };
      } catch (error) {
        console.error('[WebSocketClient] 初始化WebSocket连接失败:', error);
        reject(error);
      } finally {
        // 重置连接Promise
        this.connectionPromise = null;
      }
    });

    return this.connectionPromise;
  }

  // 启动心跳检测
  private startHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }
    this.heartbeatInterval = setInterval(() => {
      this.sendMessage({ type: 'ping' });
    }, 30000);
  }

  // 停止心跳检测
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  // 发送消息
  sendMessage(message: any): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket未连接，无法发送消息');
    }
  }

  // 关闭连接
  close(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.stopHeartbeat();
  }

  // 安排重连
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      // 减少日志输出
      // console.log('[WebSocketClient] 达到最大重试次数，停止重试');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(1.5, this.reconnectAttempts - 1); // 使用指数退避策略
    // 减少日志输出
    // console.log(`[WebSocketClient] 将在${delay}ms后尝试第${this.reconnectAttempts}次重新连接...`);

    setTimeout(() => {
      this.init().catch(error => {
        console.warn('[WebSocketClient] 重连尝试失败:', error);
      });
    }, delay);
  }
}

// 创建WebSocket客户端实例
const wsClient = new WebSocketClient();

// 导出WebSocket相关函数
export const initWebSocket = async (): Promise<WebSocket> => wsClient.init();
export const closeWebSocket = () => wsClient.close();
export const sendWebSocketMessage = (message: any) => wsClient.sendMessage(message);

// 创建授权服务对象
const authorizationService = {
  getAuthorizationsAsAuthorizer,
  getAuthorizationsAsAuthorized,
  createAuthorization,
  updateAuthorizationStatus,
  updateAuthorizationPrivacyLevel,
  clearNotification,
  searchServiceUsers,
  searchNormalUsers,
  deleteAuthorization,
  deleteRevokedAuthorization,
  getAuthorizationById,
  initWebSocket,
  closeWebSocket,
  sendWebSocketMessage
};

// 导出授权服务
export default authorizationService;