/**
 * 通知控制器
 * 处理通知相关API请求
 */
const notificationService = require('../services/notification/notificationService');
const { validationResult } = require('express-validator');

/**
 * 获取用户未读通知
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const getUnreadNotifications = async (req, res) => {
  try {
    const userId = req.user.id;
    const notifications = await notificationService.getUserUnreadNotifications(userId);
    
    res.status(200).json({
      success: true,
      data: notifications
    });
  } catch (error) {
    console.error('获取未读通知失败:', error);
    res.status(500).json({
      success: false,
      message: `获取未读通知失败: ${error.message}`
    });
  }
};

/**
 * 获取用户所有通知
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const getAllNotifications = async (req, res) => {
  try {
    const userId = req.user.id;
    const limit = parseInt(req.query.limit) || 20;
    const offset = parseInt(req.query.offset) || 0;
    
    const result = await notificationService.getUserNotifications(userId, { limit, offset });
    
    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('获取所有通知失败:', error);
    res.status(500).json({
      success: false,
      message: `获取所有通知失败: ${error.message}`
    });
  }
};

/**
 * 标记通知为已读
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const markAsRead = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '请求参数错误',
        errors: errors.array()
      });
    }
    
    const notificationId = req.params.id;
    const notification = await notificationService.markNotificationAsRead(notificationId);
    
    res.status(200).json({
      success: true,
      data: notification
    });
  } catch (error) {
    console.error('标记通知为已读失败:', error);
    res.status(500).json({
      success: false,
      message: `标记通知为已读失败: ${error.message}`
    });
  }
};

/**
 * 批量标记所有通知为已读
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const markAllAsRead = async (req, res) => {
  try {
    const userId = req.user.id;
    const count = await notificationService.markAllUserNotificationsAsRead(userId);
    
    res.status(200).json({
      success: true,
      data: {
        count
      }
    });
  } catch (error) {
    console.error('批量标记通知为已读失败:', error);
    res.status(500).json({
      success: false,
      message: `批量标记通知为已读失败: ${error.message}`
    });
  }
};

/**
 * 删除通知
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const deleteNotification = async (req, res) => {
  try {
    const notificationId = req.params.id;
    const result = await notificationService.deleteNotification(notificationId);
    
    res.status(200).json({
      success: true,
      data: {
        deleted: result
      }
    });
  } catch (error) {
    console.error('删除通知失败:', error);
    res.status(500).json({
      success: false,
      message: `删除通知失败: ${error.message}`
    });
  }
};

module.exports = {
  getUnreadNotifications,
  getAllNotifications,
  markAsRead,
  markAllAsRead,
  deleteNotification
}; 