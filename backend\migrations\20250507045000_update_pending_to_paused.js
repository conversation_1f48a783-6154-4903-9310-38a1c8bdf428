/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex('user_authorizations')
    .where('status', 'PENDING_AUTHORIZER')
    .whereRaw('(authorizer_switch = true AND authorized_switch = false) OR (authorizer_switch = false AND authorized_switch = true)')
    .update({
      status: 'PAUSED',
      updated_at: knex.fn.now(),
      status_changed_at: knex.fn.now()
    });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function(knex) {
  // 检查表是否存在
  const tableExists = await knex.schema.hasTable('user_authorizations');
  if (!tableExists) {
    console.log('表 user_authorizations 不存在，跳过回滚操作');
    return Promise.resolve();
  }
  
  // 表存在时才执行回滚
  return knex('user_authorizations')
    .where('status', 'PAUSED')
    .update({
      status: 'PENDING_AUTHORIZER',
      updated_at: knex.fn.now(),
      status_changed_at: knex.fn.now()
    });
}; 