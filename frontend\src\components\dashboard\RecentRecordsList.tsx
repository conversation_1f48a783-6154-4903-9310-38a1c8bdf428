import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Card, 
  CardContent, 
  Chip, 
  CircularProgress, 
  useTheme,
  alpha,
  Button,
  Stack
} from '@mui/material';
import { 
  AccessTime as TimeIcon,
  PermContactCalendar as ContactIcon,
  LocalHospital as HospitalIcon,
  PriorityHigh as ImportantIcon
} from '@mui/icons-material';
import { usePatientDiseaseContext } from '../../context/PatientDiseaseContext';
import { getRecords } from '../../services/recordService';

// 接口定义
interface RecordInfo {
  id: string;
  title: string;
  content?: string;
  record_date?: string;
  recordDate?: string;
  created_at?: string;
  updated_at?: string;
  is_important?: boolean;
  type?: string | string[];
  stageNode?: string;
  stageNodeName?: string;
  stagePhase?: string;
  stagePhaseName?: string;
  severity?: string;
  patient_name?: string;
  disease_name?: string;
}

/**
 * 格式化日期为本地化字符串（简化版）
 */
const formatDate = (date: Date | string): string => {
  try {
    const dateObj = date instanceof Date ? date : new Date(date);
    if (isNaN(dateObj.getTime())) return '无效日期';
    return dateObj.toLocaleString('zh-CN', { 
      year: 'numeric', 
      month: '2-digit', 
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    return '无效日期';
  }
};

/**
 * 最近记录列表组件
 * 显示与当前选择的患者和病理相关的最新6条记录
 */
const RecentRecordsList: React.FC = () => {
  const theme = useTheme();
  const { selectedPatientId, selectedDiseaseId } = usePatientDiseaseContext();
  const [loading, setLoading] = useState(false);
  const [records, setRecords] = useState<RecordInfo[]>([]);
  const [error, setError] = useState<string | null>(null);

  // 获取记录数据
  useEffect(() => {
    const fetchRecords = async () => {
      if (!selectedPatientId) {
        setRecords([]);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        // 构建查询参数
        const params: any = {
          patientId: selectedPatientId,
          patient_id: selectedPatientId,
          limit: 6,
          skip: 0
        };

        // 如果选择了病理，添加病理ID筛选
        if (selectedDiseaseId) {
          params.diseaseId = selectedDiseaseId;
          params.disease_id = selectedDiseaseId;
        }

        console.log('获取最新记录，参数:', params);
        const response = await getRecords(params);
        
        // 判断响应数据结构
        const recordsData = Array.isArray(response) 
          ? response 
          : (response.results || []);
        
        console.log(`获取到${recordsData.length}条最新记录:`, recordsData);
        setRecords(recordsData);
      } catch (err) {
        console.error('获取记录失败:', err);
        setError('获取记录失败，请重试');
        setRecords([]);
      } finally {
        setLoading(false);
      }
    };

    fetchRecords();
  }, [selectedPatientId, selectedDiseaseId]);

  // 获取记录日期
  const getRecordDate = (record: RecordInfo): string => {
    const dateStr = record.record_date || record.recordDate || record.created_at || '';
    return dateStr ? formatDate(new Date(dateStr)) : '未知日期';
  };

  // 获取记录类型
  const getRecordType = (record: RecordInfo): string => {
    if (!record.type) return '其他';
    
    if (Array.isArray(record.type) && record.type.length > 0) {
      return record.type[0];
    }
    
    if (typeof record.type === 'string') {
      try {
        const parsed = JSON.parse(record.type);
        return Array.isArray(parsed) && parsed.length > 0 ? parsed[0] : '其他';
      } catch (e) {
        return record.type || '其他';
      }
    }
    
    return '其他';
  };

  // 获取记录的阶段信息
  const getStageInfo = (record: RecordInfo): string => {
    return record.stageNodeName || record.stageNode || record.stagePhaseName || record.stagePhase || '';
  };

  // 获取记录严重程度对应的颜色
  const getSeverityColor = (severity?: string): string => {
    switch (severity?.toUpperCase()) {
      case 'MILD': return theme.palette.success.main;
      case 'MODERATE': return theme.palette.warning.main;
      case 'SEVERE': return theme.palette.error.main;
      case 'CRITICAL': return theme.palette.error.dark;
      default: return theme.palette.grey[500];
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 2, minHeight: 200, alignItems: 'center' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 2, textAlign: 'center', color: 'error.main' }}>
        <Typography>{error}</Typography>
      </Box>
    );
  }

  if (records.length === 0) {
    return (
      <Box sx={{ 
        p: 4, 
        textAlign: 'center', 
        minHeight: 200, 
        display: 'flex', 
        flexDirection: 'column',
        justifyContent: 'center', 
        alignItems: 'center',
        bgcolor: alpha(theme.palette.background.paper, 0.5),
        borderRadius: 2,
        border: '1px dashed',
        borderColor: 'divider'
      }}>
        <Typography color="text.secondary" sx={{ mb: 1 }}>
          {selectedDiseaseId ? '该病理下暂无记录数据' : '请选择病理查看相关记录'}
        </Typography>
        <Button 
          variant="outlined" 
          size="small"
          color="primary"
          onClick={() => window.location.href = '/records/new'}
          sx={{ mt: 1 }}
        >
          添加记录
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ display: 'flex', flexWrap: 'wrap', margin: -1 }}>
      {records.map((record) => (
        <Box 
          key={record.id} 
          sx={{ 
            width: { xs: '100%', sm: '50%', lg: '33.33%' }, 
            padding: 1,
            boxSizing: 'border-box'
          }}
        >
          <Card 
            sx={{ 
              height: '100%', 
              display: 'flex', 
              flexDirection: 'column',
              transition: 'all 0.2s ease',
              '&:hover': {
                boxShadow: 3,
                transform: 'translateY(-2px)'
              }
            }}
          >
            <CardContent sx={{ p: 2, flexGrow: 1 }}>
              {/* 记录标题 */}
              <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="subtitle1" component="div" sx={{ 
                  fontWeight: 600, 
                  fontSize: '0.9rem',
                  mb: 0.5,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  lineHeight: 1.3,
                  flexGrow: 1
                }}>
                  {record.title}
                </Typography>
                
                {record.is_important && (
                  <ImportantIcon 
                    color="error" 
                    fontSize="small" 
                    sx={{ ml: 0.5, minWidth: 20, flexShrink: 0 }}
                  />
                )}
              </Box>
              
              {/* 记录内容预览 */}
              {record.content && (
                <Typography variant="body2" color="text.secondary" sx={{ 
                  fontSize: '0.8rem',
                  mb: 1.5, 
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  display: '-webkit-box',
                  WebkitLineClamp: 3,
                  WebkitBoxOrient: 'vertical',
                  lineHeight: 1.3
                }}>
                  {record.content}
                </Typography>
              )}
              
              {/* 记录元数据 */}
              <Box sx={{ mt: 'auto' }}>
                {/* 记录日期 */}
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                  <TimeIcon sx={{ color: 'text.secondary', fontSize: '0.9rem', mr: 0.5 }} />
                  <Typography variant="caption" color="text.secondary">
                    {getRecordDate(record)}
                  </Typography>
                </Box>
                
                {/* 记录所属病理 */}
                {record.disease_name && (
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                    <HospitalIcon sx={{ color: 'text.secondary', fontSize: '0.9rem', mr: 0.5 }} />
                    <Typography variant="caption" color="text.secondary" sx={{ mr: 0.5 }}>
                      {record.disease_name}
                    </Typography>
                  </Box>
                )}
                
                {/* 阶段和类型标签 */}
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1 }}>
                  {getStageInfo(record) && (
                    <Chip
                      label={getStageInfo(record)}
                      size="small"
                      sx={{ 
                        height: 20, 
                        fontSize: '0.65rem',
                        bgcolor: alpha(theme.palette.primary.main, 0.1),
                        color: theme.palette.primary.main
                      }}
                    />
                  )}
                  
                  <Chip
                    label={getRecordType(record)}
                    size="small"
                    sx={{ 
                      height: 20, 
                      fontSize: '0.65rem',
                      bgcolor: alpha(theme.palette.secondary.main, 0.1),
                      color: theme.palette.secondary.main
                    }}
                  />
                  
                  {record.severity && (
                    <Chip
                      label={record.severity}
                      size="small"
                      sx={{ 
                        height: 20, 
                        fontSize: '0.65rem',
                        bgcolor: alpha(getSeverityColor(record.severity), 0.1),
                        color: getSeverityColor(record.severity)
                      }}
                    />
                  )}
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Box>
      ))}
    </Box>
  );
};

export default RecentRecordsList; 