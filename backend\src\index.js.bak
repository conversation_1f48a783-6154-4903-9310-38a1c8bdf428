// 加载环境变量
require('dotenv').config();

const express = require('express');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');
const knex = require('knex')(require('../knexfile').development);
const patientRoutes = require('../routes/patientRoutes');
const diseaseRoutes = require('../routes/diseaseRoutes');
const cors = require('cors');
const net = require('net');
const { setUpDb } = require('./db');
const { checkPort } = require('./utils/portUtils');
const { auth } = require('./middleware/auth');
const userRoutes = require('../routes/userRoutes');

// 定义端口，优先使用环境变量中的端口，如果没有则使用3001
const PORT = process.env.PORT || 3001;

const app = express();
app.use(express.json());
const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret'; // 使用环境变量中的JWT密钥

// 允许跨域请求
app.use(cors({
  origin: '*', // 允许所有来源访问
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Origin', 'X-Requested-With', 'Content-Type', 'Accept', 'Authorization']
}));

// 注册用户
app.post('/register', async (req, res) => {
  const {
    username,
    email,
    password,
    phoneNumber,
    role = 'USER',
    level = 'PERSONAL',
    activeDiseaseLimit = 5,
    aiUsageCount = 0,
    familyMemberLimit = 3,
  } = req.body;
  const passwordHash = await bcrypt.hash(password, 10);
  const updatedAt = new Date().toISOString();
  try {
    await knex('users').insert({
      id: uuidv4(),
      username,
      email,
      passwordHash,
      phoneNumber,
      role,
      level,
      activeDiseaseLimit,
      aiUsageCount,
      familyMemberLimit,
      updatedAt,
    });
    res.status(201).json({ message: '用户注册成功' });
  } catch (error) {
    console.error('注册错误:', error);
    res.status(400).json({ error: '用户名、邮箱或手机号已存在' });
  }
});

// 登录
app.post('/login', async (req, res) => {
  const { username, password } = req.body;
  console.log('登录请求参数:', { username, password: '******' });

  try {
    // 查看users表的结构
    const userColumns = await knex('users').columnInfo();
    console.log('用户表字段:', Object.keys(userColumns));
    
    // 首先尝试通过用户名查找用户
    let user = await knex('users').where({ username }).first();
    console.log('通过用户名查找结果:', user ? '找到用户' : '未找到用户');
    if (user) console.log('用户详情:', { id: user.id, username: user.username, role: user.role, level: user.level });
    
    // 如果未找到，尝试通过电子邮件查找
    if (!user) {
      console.log('尝试通过邮箱查找...');
      user = await knex('users').where({ email: username }).first();
      console.log('通过邮箱查找结果:', user ? '找到用户' : '未找到用户');
    }
    
    // 如果仍未找到，尝试通过手机号码查找
    if (!user) {
      console.log('尝试通过手机号查找...');
      user = await knex('users').where({ phoneNumber: username }).first();
      console.log('通过手机号查找结果:', user ? '找到用户' : '未找到用户');
    }
    
    // 如果所有尝试都失败，或密码不匹配，返回错误
    if (!user) {
      console.log('未找到匹配的用户');
      return res.status(401).json({ error: '用户名/邮箱/手机号或密码错误' });
    }
    
    console.log('检查密码哈希:', user.passwordHash ? '存在' : '不存在');
    const passwordMatch = await bcrypt.compare(password, user.passwordHash);
    console.log('密码匹配结果:', passwordMatch ? '匹配' : '不匹配');
    
    if (!passwordMatch) {
      return res.status(401).json({ error: '用户名/邮箱/手机号或密码错误' });
    }
    
    // 如果isActive字段存在，检查账号是否激活
    if (user.isActive !== undefined && !user.isActive) {
      console.log('用户账号未激活');
      return res.status(403).json({ error: '账号未激活' });
    }
    
    const token = jwt.sign({ id: user.id, username: user.username }, JWT_SECRET, {
      expiresIn: '15m', // 短生命周期
    });
    
    const lastLoginAt = new Date().toISOString();
    await knex('users').where({ id: user.id }).update({ lastLoginAt });
    console.log('登录成功, 用户ID:', user.id);
    res.json({ token });
  } catch (error) {
    console.error('登录错误详情:', error);
    res.status(500).json({ error: '服务器错误' });
  }
});

// 获取用户信息（受保护）
app.get('/user/profile', async (req, res) => {
  const authHeader = req.headers.authorization;
  if (!authHeader) return res.status(401).json({ error: '未授权' });
  const token = authHeader.split(' ')[1];
  try {
    const { id } = jwt.verify(token, JWT_SECRET);
    const user = await knex('users').where({ id }).first();
    if (!user) return res.status(404).json({ error: '用户不存在' });
    const levelLimit = await knex('user_level_limits')
      .where({ levelType: user.level })
      .first();
    res.json({
      username: user.username,
      email: user.email,
      phoneNumber: user.phoneNumber,
      avatar: user.avatar,
      role: user.role,
      level: user.level,
      lastLoginAt: user.lastLoginAt,
      activeDiseaseLimit: user.activeDiseaseLimit,
      aiUsageCount: user.aiUsageCount,
      aiUsageResetAt: user.aiUsageResetAt,
      familyMemberLimit: user.familyMemberLimit,
      maxPatients: levelLimit?.maxPatients,
      maxPathologies: levelLimit?.maxPathologies,
      maxAttachmentSize: levelLimit?.maxAttachmentSize,
      maxTotalStorage: levelLimit?.maxTotalStorage,
    });
  } catch (error) {
    console.error('获取用户信息错误:', error);
    res.status(401).json({ error: 'token 无效或已过期' });
  }
});

// 更新用户信息（受保护）
app.put('/user/profile', async (req, res) => {
  const authHeader = req.headers.authorization;
  if (!authHeader) return res.status(401).json({ error: '未授权' });
  const token = authHeader.split(' ')[1];
  try {
    const { id } = jwt.verify(token, JWT_SECRET);
    const { email, phoneNumber, avatar } = req.body;
    const updatedAt = new Date().toISOString();
    await knex('users')
      .where({ id })
      .update({ email, phoneNumber, avatar, updatedAt });
    res.json({ message: '用户信息更新成功' });
  } catch (error) {
    console.error('更新用户信息错误:', error);
    res.status(401).json({ error: 'token 无效或已过期' });
  }
});

// 修改密码（受保护）
app.put('/user/change-password', async (req, res) => {
  const authHeader = req.headers.authorization;
  if (!authHeader) return res.status(401).json({ error: '未授权' });
  const token = authHeader.split(' ')[1];
  try {
    const { id } = jwt.verify(token, JWT_SECRET);
    const { currentPassword, newPassword } = req.body;
    
    if (!currentPassword || !newPassword) {
      return res.status(400).json({ error: '当前密码和新密码都是必填项' });
    }
    
    const user = await knex('users')
      .where('id', id)
      .first();
    
    if (!user) {
      return res.status(404).json({ error: '用户不存在' });
    }
    
    // 验证当前密码
    const passwordMatch = await bcrypt.compare(currentPassword, user.passwordHash);
    if (!passwordMatch) {
      return res.status(401).json({ error: '当前密码不正确' });
    }
    
    // 生成新密码的哈希值
    const newPasswordHash = await bcrypt.hash(newPassword, 10);
    
    // 更新密码
    const now = new Date().toISOString();
    await knex('users')
      .where('id', id)
      .update({
        passwordHash: newPasswordHash,
        updatedAt: now
      });
    
    res.json({ message: '密码修改成功' });
  } catch (error) {
    console.error('修改密码失败:', error);
    res.status(500).json({ error: '修改密码失败' });
  }
});

// 使用患者路由
app.use('/patients', patientRoutes);

// 使用病理路由
app.use('/diseases', diseaseRoutes);

// 使用标签路由
app.use('/tags', require('./routes/tagRoutes'));

// 使用记录路由
app.use('/records', require('../routes/recordRoutes'));

// 注册路由
app.use('/api', userRoutes);

// 默认路由
app.get('/', (req, res) => {
  res.send('欢迎使用患者管理系统API');
});

// 全局错误处理中间件
app.use((err, req, res, next) => {
  const timestamp = new Date().toISOString();
  console.error(`[${timestamp}] 错误: ${err.message}, 路径: ${req.path}, IP: ${req.ip}`);
  
  // 根据错误类型返回适当的状态码
  let statusCode = 500;
  let errorMessage = '服务器内部错误，请稍后重试';
  
  if (err.name === 'ValidationError') {
    statusCode = 400;
    errorMessage = '请求数据验证失败: ' + err.message;
  } else if (err.name === 'UnauthorizedError' || err.name === 'JsonWebTokenError') {
    statusCode = 401;
    errorMessage = '授权失败，请重新登录';
  } else if (err.name === 'NotFoundError') {
    statusCode = 404;
    errorMessage = '请求的资源不存在';
  }
  
  res.status(statusCode).json({ 
    error: errorMessage,
    timestamp,
    path: req.path,
    method: req.method
  });
});

/**
 * 设置默认用户等级限制
 */
async function setUpDefaultUserLevelLimits() {
  try {
    const { UserLevel } = require('../models/UserLevel');
    
    const defaultLevels = [
      { 
        level: 'FREE', 
        maxPatients: 3, 
        maxRecordsPerPatient: 5,
        description: '免费账户' 
      },
      { 
        level: 'BASIC', 
        maxPatients: 10, 
        maxRecordsPerPatient: 20,
        description: '基础账户' 
      },
      { 
        level: 'PREMIUM', 
        maxPatients: 50, 
        maxRecordsPerPatient: 100,
        description: '高级账户' 
      },
      { 
        level: 'PROFESSIONAL', 
        maxPatients: 200, 
        maxRecordsPerPatient: 500,
        description: '专业账户' 
      },
      { 
        level: 'UNLIMITED', 
        maxPatients: -1, 
        maxRecordsPerPatient: -1,
        description: '无限制账户' 
      }
    ];
    
    for (const level of defaultLevels) {
      const exists = await UserLevel.query().where('level', level.level).first();
      
      if (!exists) {
        await UserLevel.query().insert(level);
        console.log(`已创建用户等级: ${level.level}`);
      }
    }
    
    console.log('用户等级设置完成');
  } catch (error) {
    console.error('设置用户等级时出错:', error);
  }
}

// 检查端口是否可用
async function startServer() {
  try {
    await setUpDb();
    
    // 检查端口是否被占用
    const isPortAvailable = await checkPort(PORT);
    
    if (!isPortAvailable) {
      console.error(`端口 ${PORT} 已经被占用，请尝试其他端口。`);
      process.exit(1);
    }
    
    // 绑定到所有网络接口，以便移动设备可以访问
    app.listen(PORT, '0.0.0.0', () => {
      console.log(`服务器运行在 http://0.0.0.0:${PORT}`);
      console.log(`本地访问: http://localhost:${PORT}`);
      console.log(`请确保防火墙已开放 ${PORT} 端口`);
    });
  } catch (error) {
    console.error('启动服务器时出错:', error);
    process.exit(1);
  }
}

// 启动服务器
startServer();
