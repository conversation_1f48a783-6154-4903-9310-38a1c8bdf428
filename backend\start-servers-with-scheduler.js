/**
 * 后端服务和调度器启动脚本
 * 
 * 同时启动HTTP API服务器和定时任务调度器
 * 用于生产环境
 */

const { fork } = require('child_process');
const path = require('path');

// 配置
const config = {
  // 服务进程
  services: [
    {
      name: 'API服务器',
      script: path.join(__dirname, 'index.js'),
      restart: true,
      maxRestarts: 5
    },
    {
      name: '定时任务调度器',
      script: path.join(__dirname, 'scripts/scheduler.js'),
      restart: true,
      maxRestarts: 5
    }
  ]
};

// 进程管理
const processes = new Map();

/**
 * 启动单个服务
 * @param {Object} service 服务配置
 */
function startService(service) {
  console.log(`[${service.name}] 启动中...`);
  
  // 如果进程已经存在，先停止
  if (processes.has(service.name)) {
    stopService(service.name);
  }
  
  // 启动新进程
  const proc = fork(service.script, [], {
    stdio: 'inherit',
    env: process.env
  });
  
  // 存储进程引用
  processes.set(service.name, {
    proc,
    config: service,
    restarts: 0,
    startTime: Date.now()
  });
  
  // 监听退出事件
  proc.on('exit', (code, signal) => {
    console.log(`[${service.name}] 进程退出，代码: ${code}, 信号: ${signal}`);
    
    // 如果需要重启且未超过最大重启次数
    if (service.restart && processes.has(service.name)) {
      const procInfo = processes.get(service.name);
      
      if (procInfo.restarts < service.maxRestarts) {
        console.log(`[${service.name}] 正在重启... (${procInfo.restarts + 1}/${service.maxRestarts})`);
        procInfo.restarts++;
        procInfo.proc = fork(service.script, [], {
          stdio: 'inherit',
          env: process.env
        });
        
        // 更新进程引用
        processes.set(service.name, procInfo);
        
        // 重新绑定退出事件
        procInfo.proc.on('exit', (code, signal) => {
          console.log(`[${service.name}] 重启的进程退出，代码: ${code}, 信号: ${signal}`);
        });
      } else {
        console.error(`[${service.name}] 已达到最大重启次数 (${service.maxRestarts})，不再重启`);
        processes.delete(service.name);
      }
    } else {
      processes.delete(service.name);
    }
  });
  
  console.log(`[${service.name}] 已启动，PID: ${proc.pid}`);
}

/**
 * 停止服务
 * @param {string} serviceName 服务名称
 */
function stopService(serviceName) {
  if (processes.has(serviceName)) {
    const procInfo = processes.get(serviceName);
    console.log(`[${serviceName}] 正在停止进程，PID: ${procInfo.proc.pid}...`);
    
    // 发送SIGTERM信号
    procInfo.proc.kill('SIGTERM');
    
    // 设置超时，如果进程没有正常退出，则强制结束
    setTimeout(() => {
      if (processes.has(serviceName) && processes.get(serviceName).proc.pid === procInfo.proc.pid) {
        console.log(`[${serviceName}] 进程未响应SIGTERM，发送SIGKILL强制结束`);
        procInfo.proc.kill('SIGKILL');
        processes.delete(serviceName);
      }
    }, 5000);
  } else {
    console.log(`[${serviceName}] 服务未运行`);
  }
}

/**
 * 停止所有服务
 */
function stopAllServices() {
  console.log('正在停止所有服务...');
  for (const serviceName of processes.keys()) {
    stopService(serviceName);
  }
}

/**
 * 启动所有服务
 */
function startAllServices() {
  console.log('正在启动所有服务...');
  config.services.forEach(service => {
    startService(service);
  });
}

// 注册退出处理函数
process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在优雅关闭...');
  stopAllServices();
  
  // 给进程一些时间来清理
  setTimeout(() => {
    console.log('退出主进程');
    process.exit(0);
  }, 3000);
});

process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在优雅关闭...');
  stopAllServices();
  
  // 给进程一些时间来清理
  setTimeout(() => {
    console.log('退出主进程');
    process.exit(0);
  }, 3000);
});

// 启动所有服务
startAllServices();

console.log('后端服务管理器已启动，按Ctrl+C停止所有服务'); 