import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { useLocation } from 'react-router-dom';

// 定义操作上下文类型
export type OperationMode = 'NORMAL' | 'SERVICE' | 'ADMIN';

// 应用上下文接口
interface AppContextType {
  // 操作模式
  operationMode: OperationMode;
  // 是否是服务上下文
  isServiceContext: boolean;
  // 设置操作模式
  setOperationMode: (mode: OperationMode) => void;
  // 获取API请求参数
  getContextParams: () => { service_context?: boolean };
}

// 创建上下文
const AppContext = createContext<AppContextType | undefined>(undefined);

// 提供者Props接口
interface AppProviderProps {
  children: ReactNode;
}

// 上下文提供者组件
export const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  // 从localStorage初始化操作模式
  const [operationMode, setOperationModeState] = useState<OperationMode>(() => {
    const savedMode = localStorage.getItem('operation_mode');
    return (savedMode === 'SERVICE' || savedMode === 'ADMIN' || savedMode === 'NORMAL') 
      ? savedMode as OperationMode 
      : 'NORMAL';
  });
  const location = useLocation();

  // 根据路径自动设置操作模式
  useEffect(() => {
    const path = location.pathname;
    let newMode: OperationMode = 'NORMAL';
    
    // 判断当前路径属于哪种操作模式
    if (path.startsWith('/admin')) {
      newMode = 'ADMIN';
    } else if (path.startsWith('/service') || path.includes('-service')) {
      newMode = 'SERVICE';
    } else {
      newMode = 'NORMAL';
    }
    
    // 更新操作模式
    setOperationModeState(newMode);
    
    // 同步到localStorage
    localStorage.setItem('operation_mode', newMode);
    
    console.log(`[AppContext] 路径变更: ${path}, 操作模式: ${newMode}`);
  }, [location.pathname]);

  // 设置操作模式
  const setOperationMode = (mode: OperationMode) => {
    console.log(`[AppContext] 手动设置操作模式: ${mode}`);
    setOperationModeState(mode);
    
    // 同步到localStorage
    localStorage.setItem('operation_mode', mode);
  };

  // 获取当前上下文是否为服务上下文
  const isServiceContext = operationMode === 'SERVICE' || operationMode === 'ADMIN';

  // 获取API请求参数
  const getContextParams = () => {
    // 只有在服务上下文（操作模式是SERVICE或ADMIN）中才添加service_context参数
    // 即使用户是服务用户或管理员，在NORMAL模式下也不添加service_context参数
    return operationMode === 'SERVICE' || operationMode === 'ADMIN' 
      ? { service_context: true } 
      : {};
  };

  // 提供上下文值
  const contextValue: AppContextType = {
    operationMode,
    isServiceContext,
    setOperationMode,
    getContextParams
  };

  return (
    <AppContext.Provider value={contextValue}>
      {children}
    </AppContext.Provider>
  );
};

// 自定义钩子，用于在组件中使用上下文
export const useAppContext = (): AppContextType => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext必须在AppProvider内部使用');
  }
  return context;
}; 