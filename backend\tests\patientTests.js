const axios = require('axios');

// 配置
const API_URL = 'http://localhost:3001';
let token = null;
let patientId = null;

// 测试数据
const testUser = {
  username: 'testuser_' + Date.now(),
  email: `testuser_${Date.now()}@example.com`,
  password: 'Test123456',
  phoneNumber: '13800138000',
};

const testPatient = {
  name: '测试患者_' + Date.now(),
  gender: '男',
  phoneNumber: '13900139000',
  email: '<EMAIL>',
  address: '北京市海淀区中关村南大街5号',
  birthDate: '1990-01-01',
  bloodType: 'A',
  emergencyContactName: '紧急联系人',
  emergencyContactPhone: '13700137000',
  isPrimary: 1
};

// 测试函数
async function runTests() {
  try {
    console.log('开始测试...');
    
    // 注册用户
    console.log('1. 注册用户...');
    await axios.post(`${API_URL}/register`, testUser);
    console.log('注册成功');
    
    // 登录
    console.log('2. 登录...');
    const loginResponse = await axios.post(`${API_URL}/login`, {
      username: testUser.username,
      password: testUser.password
    });
    token = loginResponse.data.token;
    console.log('登录成功，获取token');
    
    // 添加患者
    console.log('3. 添加患者...');
    const createResponse = await axios.post(
      `${API_URL}/patients`,
      testPatient,
      { headers: { Authorization: `Bearer ${token}` } }
    );
    patientId = createResponse.data.patient.id;
    console.log('患者添加成功，ID:', patientId);
    console.log('响应数据:', createResponse.data);
    
    // 获取患者列表
    console.log('4. 获取患者列表...');
    const listResponse = await axios.get(
      `${API_URL}/patients`,
      { headers: { Authorization: `Bearer ${token}` } }
    );
    console.log(`获取到 ${listResponse.data.length} 个患者`);
    
    // 获取患者详情
    console.log('5. 获取患者详情...');
    const detailResponse = await axios.get(
      `${API_URL}/patients/${patientId}`,
      { headers: { Authorization: `Bearer ${token}` } }
    );
    const patient = detailResponse.data;
    console.log('患者详情:');
    console.log('- 姓名:', patient.name);
    console.log('- 电子邮箱:', patient.email);
    console.log('- 地址:', patient.address);
    
    // 更新患者
    console.log('6. 更新患者...');
    const updateData = {
      ...patient,
      email: '<EMAIL>',
      address: '更新后的地址：上海市浦东新区世纪大道100号',
    };
    
    const updateResponse = await axios.put(
      `${API_URL}/patients/${patientId}`,
      updateData,
      { headers: { Authorization: `Bearer ${token}` } }
    );
    
    console.log('患者更新成功');
    console.log('- 更新后邮箱:', updateResponse.data.email);
    console.log('- 更新后地址:', updateResponse.data.address);
    
    // 测试验证完成
    console.log('所有测试通过!');
    
  } catch (error) {
    console.error('测试失败:', error.response?.data || error.message);
  }
}

// 执行测试
runTests(); 