/**
 * 通知系统路由
 */
const express = require('express');
const router = express.Router();
const { param } = require('express-validator');
const notificationController = require('../controllers/notificationController');
const { authenticate } = require('../src/middleware/auth');

// 所有通知路由都需要用户认证
router.use(authenticate);

/**
 * @route GET /api/notifications/unread
 * @desc 获取用户未读通知
 * @access 私有
 */
router.get('/unread', notificationController.getUnreadNotifications);

/**
 * @route GET /api/notifications
 * @desc 获取用户所有通知
 * @access 私有
 */
router.get('/', notificationController.getAllNotifications);

/**
 * @route POST /api/notifications/:id/read
 * @desc 标记通知为已读
 * @access 私有
 */
router.post('/:id/read', [
  param('id').isUUID().withMessage('通知ID必须是有效的UUID')
], notificationController.markAsRead);

/**
 * @route POST /api/notifications/read-all
 * @desc 批量标记所有通知为已读
 * @access 私有
 */
router.post('/read-all', notificationController.markAllAsRead);

/**
 * @route DELETE /api/notifications/:id
 * @desc 删除通知
 * @access 私有
 */
router.delete('/:id', [
  param('id').isUUID().withMessage('通知ID必须是有效的UUID')
], notificationController.deleteNotification);

module.exports = router; 