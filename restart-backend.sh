#!/bin/bash

# 重启后端服务脚本
# 用于应用 CORS 配置更改

echo "🔄 开始重启后端服务..."

# 检查是否在正确的目录
if [ ! -d "backend" ]; then
    echo "❌ 错误：请在项目根目录运行此脚本"
    exit 1
fi

# 进入后端目录
cd backend

echo "📦 检查后端依赖..."
if [ ! -d "node_modules" ]; then
    echo "📥 安装后端依赖..."
    npm install
fi

echo "🛑 停止现有的后端进程..."
# 查找并停止现有的 Node.js 进程
pkill -f "node.*src/index.js" || true
pkill -f "nodemon.*src/index.js" || true

# 等待进程完全停止
sleep 2

echo "🚀 启动后端服务..."
# 使用 PM2 启动服务（如果可用）
if command -v pm2 &> /dev/null; then
    echo "使用 PM2 启动服务..."
    pm2 delete hkb-backend || true
    pm2 start src/index.js --name hkb-backend --watch
    pm2 logs hkb-backend --lines 10
else
    echo "使用 nodemon 启动服务..."
    # 后台启动 nodemon
    nohup npm run dev > ../backend.log 2>&1 &
    echo "后端服务已在后台启动，日志文件：../backend.log"
    
    # 等待服务启动
    sleep 3
    
    # 检查服务是否启动成功
    if curl -s http://localhost:3001/health > /dev/null; then
        echo "✅ 后端服务启动成功！"
        echo "🌐 健康检查：http://localhost:3001/health"
    else
        echo "⚠️ 后端服务可能未完全启动，请检查日志"
        tail -n 20 ../backend.log
    fi
fi

echo "🎉 后端服务重启完成！"
echo ""
echo "📋 服务信息："
echo "   - API 端口：3001-3004"
echo "   - WebSocket 端口：3005-3008"
echo "   - 健康检查：http://localhost:3001/health"
echo ""
echo "🔍 查看日志："
if command -v pm2 &> /dev/null; then
    echo "   pm2 logs hkb-backend"
else
    echo "   tail -f ../backend.log"
fi
