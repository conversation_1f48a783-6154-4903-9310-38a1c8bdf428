// 记录类型枚举
export enum RecordTypeEnum {
  SELF_DESCRIPTION = 'SELF_DESCRIPTION',
  SYMPTOM = 'SYMPTOM',
  EXAMINATION = 'EXAMINATION',
  LAB_TEST = 'LAB_TEST',
  DIAGNOSIS = 'DIAGNOSIS',
  TREATMENT = 'TREATMENT',
  HOSPITALIZATION = 'HOSPITALIZATION',
  MEDICATION = 'MEDICATION',
  SURGERY = 'SURGERY',
  MONITORING = 'MONITORING',
  PHYSICAL_THERAPY = 'PHYSICAL_THERAPY',
  DISCHARGE = 'DISCHARGE',
  APPOINTMENT = 'APPOINTMENT',
  REPORT = 'REPORT',
  FOLLOW_UP = 'FOLLOW_UP',
  PROGNOSIS = 'PROGNOSIS',
  AUX_DIAGNOSIS = 'AUX_DIAGNOSIS',
  NURSING = 'NURSING',
  REVISIT = 'REVISIT',
  REFERRAL = 'REFERRAL',
  PSYCHOLOGY = 'PSYCHOLOGY',
  REHABILITATION = 'REHABILITATION',
  ASSESSMENT = 'ASSESSMENT',
  OTHER = 'OTHER'
}

// 记录类型名称映射
export const RecordTypeNames: Record<RecordTypeEnum, string> = {
  [RecordTypeEnum.SELF_DESCRIPTION]: '自述',
  [RecordTypeEnum.SYMPTOM]: '症状',
  [RecordTypeEnum.EXAMINATION]: '检查',
  [RecordTypeEnum.LAB_TEST]: '化验',
  [RecordTypeEnum.DIAGNOSIS]: '诊断',
  [RecordTypeEnum.TREATMENT]: '治疗',
  [RecordTypeEnum.HOSPITALIZATION]: '住院',
  [RecordTypeEnum.MEDICATION]: '用药',
  [RecordTypeEnum.SURGERY]: '手术',
  [RecordTypeEnum.MONITORING]: '监测',
  [RecordTypeEnum.PHYSICAL_THERAPY]: '理疗',
  [RecordTypeEnum.DISCHARGE]: '出院',
  [RecordTypeEnum.APPOINTMENT]: '预约',
  [RecordTypeEnum.REPORT]: '报告',
  [RecordTypeEnum.FOLLOW_UP]: '随访',
  [RecordTypeEnum.PROGNOSIS]: '预后',
  [RecordTypeEnum.AUX_DIAGNOSIS]: '辅诊',
  [RecordTypeEnum.NURSING]: '护理',
  [RecordTypeEnum.REVISIT]: '复诊',
  [RecordTypeEnum.REFERRAL]: '转诊',
  [RecordTypeEnum.PSYCHOLOGY]: '心理',
  [RecordTypeEnum.REHABILITATION]: '康复',
  [RecordTypeEnum.ASSESSMENT]: '评估',
  [RecordTypeEnum.OTHER]: '其他'
};

// 记录类型标签定义
export const RECORD_TYPE_LABELS: Record<RecordTypeEnum, string> = {
  [RecordTypeEnum.SELF_DESCRIPTION]: '自述',
  [RecordTypeEnum.SYMPTOM]: '症状',
  [RecordTypeEnum.EXAMINATION]: '检查',
  [RecordTypeEnum.LAB_TEST]: '化验',
  [RecordTypeEnum.DIAGNOSIS]: '诊断',
  [RecordTypeEnum.TREATMENT]: '治疗',
  [RecordTypeEnum.HOSPITALIZATION]: '住院',
  [RecordTypeEnum.MEDICATION]: '用药',
  [RecordTypeEnum.SURGERY]: '手术',
  [RecordTypeEnum.MONITORING]: '监测',
  [RecordTypeEnum.PHYSICAL_THERAPY]: '理疗',
  [RecordTypeEnum.DISCHARGE]: '出院',
  [RecordTypeEnum.APPOINTMENT]: '预约',
  [RecordTypeEnum.REPORT]: '报告',
  [RecordTypeEnum.FOLLOW_UP]: '随访',
  [RecordTypeEnum.PROGNOSIS]: '预后',
  [RecordTypeEnum.AUX_DIAGNOSIS]: '辅诊',
  [RecordTypeEnum.NURSING]: '护理',
  [RecordTypeEnum.REVISIT]: '复诊',
  [RecordTypeEnum.REFERRAL]: '转诊',
  [RecordTypeEnum.PSYCHOLOGY]: '心理',
  [RecordTypeEnum.REHABILITATION]: '康复',
  [RecordTypeEnum.ASSESSMENT]: '评估',
  [RecordTypeEnum.OTHER]: '其他'
};

// 常用记录类型
export const COMMON_RECORD_TYPES = [
  RecordTypeEnum.SYMPTOM,
  RecordTypeEnum.DIAGNOSIS,
  RecordTypeEnum.TREATMENT,
  RecordTypeEnum.MEDICATION,
  RecordTypeEnum.EXAMINATION,
  RecordTypeEnum.FOLLOW_UP,
  RecordTypeEnum.REVISIT
];

// 记录类型UI分组
export const RECORD_TYPE_GROUPS: RecordTypeEnum[][] = [
  // 第一行
  [
    RecordTypeEnum.SYMPTOM,
    RecordTypeEnum.DIAGNOSIS,
    RecordTypeEnum.TREATMENT,
    RecordTypeEnum.MEDICATION,
    RecordTypeEnum.EXAMINATION,
    RecordTypeEnum.LAB_TEST
  ],
  // 第二行
  [
    RecordTypeEnum.FOLLOW_UP,
    RecordTypeEnum.REVISIT,
    RecordTypeEnum.HOSPITALIZATION,
    RecordTypeEnum.DISCHARGE,
    RecordTypeEnum.SURGERY,
    RecordTypeEnum.APPOINTMENT
  ],
  // 第三行
  [
    RecordTypeEnum.MONITORING,
    RecordTypeEnum.PHYSICAL_THERAPY,
    RecordTypeEnum.REHABILITATION,
    RecordTypeEnum.NURSING,
    RecordTypeEnum.PSYCHOLOGY,
    RecordTypeEnum.ASSESSMENT
  ],
  // 第四行
  [
    RecordTypeEnum.SELF_DESCRIPTION,
    RecordTypeEnum.REPORT,
    RecordTypeEnum.PROGNOSIS,
    RecordTypeEnum.AUX_DIAGNOSIS,
    RecordTypeEnum.REFERRAL,
    RecordTypeEnum.OTHER
  ]
];

// 记录类型描述
export const RECORD_TYPE_DESCRIPTIONS: Record<RecordTypeEnum, string> = {
  [RecordTypeEnum.SELF_DESCRIPTION]: '患者自述的症状和感受',
  [RecordTypeEnum.SYMPTOM]: '医生记录的症状表现',
  [RecordTypeEnum.EXAMINATION]: '物理检查结果',
  [RecordTypeEnum.LAB_TEST]: '实验室检查和化验结果',
  [RecordTypeEnum.DIAGNOSIS]: '疾病诊断结果',
  [RecordTypeEnum.TREATMENT]: '治疗方案和处置记录',
  [RecordTypeEnum.HOSPITALIZATION]: '住院记录',
  [RecordTypeEnum.MEDICATION]: '用药记录和处方',
  [RecordTypeEnum.SURGERY]: '手术记录和术后情况',
  [RecordTypeEnum.MONITORING]: '生命体征监测数据',
  [RecordTypeEnum.PHYSICAL_THERAPY]: '物理治疗和康复训练',
  [RecordTypeEnum.DISCHARGE]: '出院记录和医嘱',
  [RecordTypeEnum.APPOINTMENT]: '预约和复诊安排',
  [RecordTypeEnum.REPORT]: '检查报告和诊断报告',
  [RecordTypeEnum.FOLLOW_UP]: '随访记录和病情跟进',
  [RecordTypeEnum.PROGNOSIS]: '预后评估和长期计划',
  [RecordTypeEnum.AUX_DIAGNOSIS]: '辅助诊断结果',
  [RecordTypeEnum.NURSING]: '护理记录和观察',
  [RecordTypeEnum.REVISIT]: '复诊记录',
  [RecordTypeEnum.REFERRAL]: '转诊记录',
  [RecordTypeEnum.PSYCHOLOGY]: '心理评估和治疗',
  [RecordTypeEnum.REHABILITATION]: '康复训练和进展',
  [RecordTypeEnum.ASSESSMENT]: '功能评估和量表',
  [RecordTypeEnum.OTHER]: '其他医疗记录'
};

// 阶段枚举
export enum StagePhaseEnum {
  INITIAL = 'INITIAL',
  DIAGNOSIS = 'DIAGNOSIS',
  TREATMENT = 'TREATMENT',
  RECOVERY = 'RECOVERY',
  PROGNOSIS = 'PROGNOSIS'
}

// 阶段名称映射
export const StagePhaseNames: Record<StagePhaseEnum, string> = {
  [StagePhaseEnum.INITIAL]: '初诊期',
  [StagePhaseEnum.DIAGNOSIS]: '确诊期',
  [StagePhaseEnum.TREATMENT]: '治疗期',
  [StagePhaseEnum.RECOVERY]: '恢复期',
  [StagePhaseEnum.PROGNOSIS]: '预后期'
};

// 阶段节点枚举
export enum StageNodeEnum {
  INITIAL_VISIT = 'INITIAL_VISIT',
  DIAGNOSIS = 'DIAGNOSIS',
  TREATMENT = 'TREATMENT',
  FOLLOW_UP = 'FOLLOW_UP',
  PROGNOSIS = 'PROGNOSIS',
  ARCHIVE = 'ARCHIVE'
}

// 阶段节点名称映射
export const StageNodeNames: Record<StageNodeEnum, string> = {
  [StageNodeEnum.INITIAL_VISIT]: '初诊',
  [StageNodeEnum.DIAGNOSIS]: '确诊',
  [StageNodeEnum.TREATMENT]: '治疗',
  [StageNodeEnum.FOLLOW_UP]: '随访',
  [StageNodeEnum.PROGNOSIS]: '预后',
  [StageNodeEnum.ARCHIVE]: '归档'
};

// 严重程度枚举
export enum SeverityEnum {
  MILD = 'MILD',
  MODERATE = 'MODERATE',
  SEVERE = 'SEVERE',
  CRITICAL = 'CRITICAL'
}

// 严重程度名称映射
export const SeverityNames: Record<SeverityEnum, string> = {
  [SeverityEnum.MILD]: '轻微',
  [SeverityEnum.MODERATE]: '中等',
  [SeverityEnum.SEVERE]: '严重',
  [SeverityEnum.CRITICAL]: '危重'
};

// 附件类型枚举
export enum AttachmentTypeEnum {
  PDF = 'PDF',
  JPG = 'JPG',
  PNG = 'PNG',
  TXT = 'TXT'
}

// 附件类型名称映射
export const AttachmentTypeNames: Record<AttachmentTypeEnum, string> = {
  [AttachmentTypeEnum.PDF]: 'PDF文件',
  [AttachmentTypeEnum.JPG]: 'JPG图片',
  [AttachmentTypeEnum.PNG]: 'PNG图片',
  [AttachmentTypeEnum.TXT]: '文本文件'
}; 