import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  Checkbox,
  FormControlLabel,
  Alert,
  Card,
  CardHeader,
  CardContent,
  CardActions,
  CircularProgress,
  Tabs,
  Tab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tooltip
} from '@mui/material';
import { 
  Save as SaveIcon, 
  Visibility as VisibilityIcon,
  DescriptionOutlined as ReportIcon,
  PictureAsPdf as PdfIcon 
} from '@mui/icons-material';
import { getReportConfig, saveReportConfig } from '../../services/ai-assistant/aiReportService';
import { ReportVisibilityConfig } from '../../types/ai-assistant';
import { useAuthStore } from '../../store/authStore';

/**
 * 辅医模块报告配置页面
 * 管理员可设置不同用户角色可见的报告内容
 */
const AIReportConfigPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const [config, setConfig] = useState<ReportVisibilityConfig>({
    userVisibleFields: [],
    serviceVisibleFields: []
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [currentTab, setCurrentTab] = useState(0);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewField, setPreviewField] = useState<{id: string, label: string, content: string}>({id: '', label: '', content: ''});

  // 字段选项列表
  const fieldOptions = [
    { 
      id: 'summary', 
      label: '病情综述',
      description: '概述患者当前病情状态，提供整体分析',
      content: '根据提供的信息，患者目前呈现出腰椎间盘突出症的典型症状，包括下腰部疼痛，并向一侧或双侧下肢放射。患者疼痛在站立或久坐后加重，卧床休息后缓解，常伴有腰部僵硬感。MRI检查显示L4-L5椎间盘向后突出，压迫邻近神经根。目前症状程度为中度，影响日常活动但尚未严重影响生活质量。'
    },
    { 
      id: 'differentialDiagnosis', 
      label: '疾病可能性分析',
      description: '分析可能的疾病诊断和鉴别诊断',
      content: '腰椎间盘突出症（可能性>90%）：症状与影像学结果高度一致，表现为腰痛放射至下肢，活动时加重，休息时减轻。\n\n腰椎小关节紊乱（可能性40%）：可引起类似腰痛，但通常疼痛不会沿特定神经根走行分布。\n\n梨状肌综合征（可能性30%）：可引起臀部和下肢放射痛，但疼痛通常始于臀部而非腰部。'
    },
    { 
      id: 'emergencyGuidance', 
      label: '紧急处置建议',
      description: '针对紧急情况的处理建议和指导',
      content: '当前情况不属于医疗急症，无需紧急就医。\n\n如果出现以下症状，应立即就医：\n- 双下肢严重无力或突然麻木\n- 大小便失禁或严重排尿困难\n- 鞍区（会阴部）感觉丧失\n- 剧烈疼痛伴发热超过38.5°C'
    },
    { 
      id: 'hospitalRecommendations', 
      label: '医院与科室推荐',
      description: '推荐适合患者就诊的医院和科室',
      content: '推荐医院：\n1. 北京协和医院 - 骨科/脊柱外科（三级甲等）\n2. 北京大学第三医院 - 骨科/疼痛科（三级甲等）\n3. 首都医科大学附属北京天坛医院 - 神经外科（三级甲等）\n\n科室建议：优先选择骨科或脊柱外科，其次可考虑疼痛科或康复医学科。'
    },
    { 
      id: 'treatmentPlan', 
      label: '治疗方案建议',
      description: '可能的治疗选择和方案建议',
      content: '保守治疗（推荐先尝试）：\n- 短期卧床休息（3-4天）\n- 非甾体抗炎药物（如布洛芬、塞来昔布等）\n- 理疗（如牵引、超声波、干扰电等）\n- 腰椎核心肌群训练\n\n微创介入治疗（如保守治疗2-4周无效）：\n- 椎间盘内注射术\n- 神经根阻滞术\n\n手术治疗（保守治疗无效或症状严重时考虑）：\n- 经皮椎间盘切除术\n- 显微内窥镜下椎间盘切除术'
    },
    { 
      id: 'budgetEstimation', 
      label: '预算费用评估',
      description: '各种治疗方案的预计费用范围',
      content: '保守治疗预算：\n- 门诊就诊（含挂号费、专家诊疗费）：300-800元/次\n- 药物治疗（抗炎镇痛药）：200-500元/周\n- 理疗康复：150-300元/次，建议10-15次疗程\n\n微创介入治疗预算：\n- 椎间盘内注射：3,000-6,000元/次\n- 神经根阻滞：2,000-4,000元/次\n\n手术治疗预算：\n- 微创手术：20,000-35,000元\n- 开放手术：35,000-60,000元\n\n总体预算范围：保守治疗3,000-8,000元；介入治疗8,000-15,000元；手术治疗20,000-60,000元'
    },
    { 
      id: 'crossRegionGuidance', 
      label: '异地就医指引',
      description: '为异地就医患者提供的特别指导',
      content: '跨地区就医建议：\n1. 提前通过互联网医院平台预约专家门诊\n2. 准备完整病历资料，包括影像学检查光盘和报告\n3. 携带既往治疗记录、用药清单和过敏史记录\n\n医保报销指引：\n- 异地就医需办理异地就医备案手续\n- 三级医院报销比例约为60-70%\n- 部分微创和手术治疗可能不在医保目录内，自费比例较高\n\n住宿交通参考：\n- 北京协和医院周边经济型酒店：200-400元/晚\n- 就医期间可使用打车软件或地铁，提前规划路线'
    },
    { 
      id: 'lifestyleAndMentalHealth', 
      label: '生活与心理建议',
      description: '日常生活建议和心理健康指导',
      content: '生活方式调整：\n- 保持正确坐姿和站姿，避免长时间保持同一姿势\n- 坐椅宜选用腰靠，保持腰椎自然生理弯曲\n- 避免提举重物，必须提举时应屈膝而非弯腰\n- 睡眠建议使用硬板床，侧卧时可在膝间放置枕头\n\n运动建议：\n- 急性期避免运动，缓解后可进行温和有氧运动如游泳\n- 每日进行腰背肌训练和核心肌群锻炼\n\n心理健康：\n- 了解腰椎间盘突出症通常可通过治疗得到缓解\n- 保持积极心态，避免过度焦虑\n- 必要时寻求心理咨询支持'
    },
    { 
      id: 'riskWarnings', 
      label: '风险提示',
      description: '疾病进展和治疗风险警示',
      content: '疾病风险提示：\n- 若不积极治疗，症状可能加重并导致神经功能损害\n- 长期慢性疼痛可能引发继发性抑郁和睡眠障碍\n- 约10-15%的患者可能发展为慢性疼痛综合征\n\n治疗风险提示：\n- 药物治疗可能引起胃肠道反应、肝肾功能影响等\n- 手术风险包括神经损伤、感染、麻醉并发症等\n- 约5%的患者术后可能出现复发'
    }
  ];

  // 检查权限并加载配置
  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => {
    const fetchConfig = async () => {
      // 只有管理员可以访问此页面
      if (user?.role !== 'ADMIN') {
        navigate('/dashboard');
        return;
      }
      
      setLoading(true);
      try {
        const configData = await getReportConfig();
        setConfig(configData);
      } catch (error) {
        console.error('获取报告配置失败', error);
        setError('获取报告配置失败，请稍后重试');
      } finally {
        setLoading(false);
      }
    };
    
    fetchConfig();
  }, [user, navigate]);

  // 处理Tab切换
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  // 处理用户可见字段变更
  const handleUserFieldChange = (field: string) => {
    setConfig(prevConfig => {
      const newUserFields = prevConfig.userVisibleFields.includes(field)
        ? prevConfig.userVisibleFields.filter(f => f !== field)
        : [...prevConfig.userVisibleFields, field];
      
      // 用户字段是服务用户字段的子集
      return {
        ...prevConfig,
        userVisibleFields: newUserFields
      };
    });
  };

  // 处理服务用户可见字段变更
  const handleServiceFieldChange = (field: string) => {
    setConfig(prevConfig => {
      const newServiceFields = prevConfig.serviceVisibleFields.includes(field)
        ? prevConfig.serviceVisibleFields.filter(f => f !== field)
        : [...prevConfig.serviceVisibleFields, field];
      
      // 确保用户字段是服务用户字段的子集
      const newUserFields = prevConfig.userVisibleFields.filter(f => 
        newServiceFields.includes(f)
      );
      
      return {
        serviceVisibleFields: newServiceFields,
        userVisibleFields: newUserFields
      };
    });
  };

  // 打开字段预览对话框
  const handleOpenPreview = (id: string, label: string) => {
    const fieldOption = fieldOptions.find(option => option.id === id);
    if (fieldOption) {
      setPreviewField({
        id: fieldOption.id,
        label: fieldOption.label,
        content: fieldOption.content
      });
      setPreviewOpen(true);
    }
  };

  // 关闭字段预览对话框
  const handleClosePreview = () => {
    setPreviewOpen(false);
  };

  // 保存配置
  const handleSave = async () => {
    setSaving(true);
    setError(null);
    setSuccess(null);
    
    try {
      await saveReportConfig(config);
      setSuccess('配置保存成功');
    } catch (error) {
      console.error('保存配置失败', error);
      setError('保存配置失败，请稍后重试');
    } finally {
      setSaving(false);
    }
  };

  // 返回列表
  const handleBack = () => {
    navigate('/ai-assistant');
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '70vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h5" gutterBottom>
        辅医报告配置
      </Typography>
      
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}
      
      <Card sx={{ mb: 3 }}>
        <CardHeader title="报告内容可见性设置" />
        <CardContent>
          <Typography variant="body2" color="text.secondary" paragraph>
            您可以配置不同用户角色能够查看的报告内容以及生成PDF报告时包含的内容。病情综述默认总是可见的。
          </Typography>
          
          <Tabs 
            value={currentTab} 
            onChange={handleTabChange} 
            sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}
          >
            <Tab label="普通用户" icon={<ReportIcon />} iconPosition="start" />
            <Tab label="服务用户" icon={<PdfIcon />} iconPosition="start" />
          </Tabs>
          
          {currentTab === 0 && (
            <Box>
              <Typography variant="subtitle2" color="primary" gutterBottom>
                普通用户可见内容
              </Typography>
              <Typography variant="caption" color="text.secondary" paragraph>
                普通用户为非专业人士，设置他们可见的内容会影响在用户界面和用户PDF报告中显示的内容。
              </Typography>
              
              <Box sx={{ display: 'flex', flexWrap: 'wrap', margin: -1 }}>
                {fieldOptions.map(option => (
                  <Box sx={{ width: { xs: '100%', sm: '50%' }, p: 1 }} key={option.id}>
                    <Box sx={{ 
                      p: 1,
                      display: 'flex',
                      alignItems: 'center', 
                      justifyContent: 'space-between',
                      bgcolor: config.userVisibleFields.includes(option.id) ? 'rgba(25, 118, 210, 0.08)' : 'transparent',
                      borderRadius: 1,
                      '&:hover': {
                        bgcolor: config.userVisibleFields.includes(option.id) ? 'rgba(25, 118, 210, 0.12)' : 'rgba(0, 0, 0, 0.04)',
                      }
                    }}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={config.userVisibleFields.includes(option.id)}
                        onChange={() => handleUserFieldChange(option.id)}
                            disabled={option.id === 'summary' || !config.serviceVisibleFields.includes(option.id)} // 病情综述始终可见或服务用户不可见时禁用
                      />
                    }
                        label={
                          <Typography variant="body2">
                            {option.label}
                            <Typography variant="caption" sx={{ display: 'block', color: 'text.secondary' }}>
                              {option.description}
                            </Typography>
                          </Typography>
                        }
                      />
                      <Tooltip title="预览内容示例">
                        <Button
                          size="small"
                          startIcon={<VisibilityIcon />}
                          onClick={() => handleOpenPreview(option.id, option.label)}
                        >
                          预览
                        </Button>
                      </Tooltip>
                    </Box>
                  </Box>
                ))}
              </Box>
          </Box>
          )}
          
          {currentTab === 1 && (
          <Box>
              <Typography variant="subtitle2" color="primary" gutterBottom>
                服务用户可见内容
              </Typography>
              <Typography variant="caption" color="text.secondary" paragraph>
                服务用户为医疗专业人员、导医和客服，设置他们可见的内容会影响在服务端界面和服务用户PDF报告中显示的内容。
              </Typography>
              
              <Box sx={{ display: 'flex', flexWrap: 'wrap', margin: -1 }}>
                {fieldOptions.map(option => (
                  <Box sx={{ width: { xs: '100%', sm: '50%' }, p: 1 }} key={option.id}>
                    <Box sx={{ 
                      p: 1,
                      display: 'flex',
                      alignItems: 'center', 
                      justifyContent: 'space-between',
                      bgcolor: config.serviceVisibleFields.includes(option.id) ? 'rgba(25, 118, 210, 0.08)' : 'transparent',
                      borderRadius: 1,
                      '&:hover': {
                        bgcolor: config.serviceVisibleFields.includes(option.id) ? 'rgba(25, 118, 210, 0.12)' : 'rgba(0, 0, 0, 0.04)',
                      }
                    }}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={config.serviceVisibleFields.includes(option.id)}
                        onChange={() => handleServiceFieldChange(option.id)}
                        disabled={option.id === 'summary'} // 病情综述始终可见
                      />
                    }
                        label={
                          <Typography variant="body2">
                            {option.label}
                            <Typography variant="caption" sx={{ display: 'block', color: 'text.secondary' }}>
                              {option.description}
                            </Typography>
                          </Typography>
                        }
                      />
                      <Tooltip title="预览内容示例">
                        <Button
                          size="small"
                          startIcon={<VisibilityIcon />}
                          onClick={() => handleOpenPreview(option.id, option.label)}
                        >
                          预览
                        </Button>
                      </Tooltip>
                    </Box>
                  </Box>
                ))}
              </Box>
          </Box>
          )}
          
          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 2 }}>
            注意：普通用户只能看到服务用户可见内容的子集。如果取消选择服务用户的某个字段，普通用户对应的字段也会自动取消选择。
          </Typography>
        </CardContent>
        <CardActions sx={{ justifyContent: 'flex-end', p: 2 }}>
          <Button 
            variant="outlined" 
            onClick={handleBack}
            sx={{ mr: 1 }}
          >
            返回
          </Button>
          <Button
            variant="contained"
            startIcon={<SaveIcon />}
            onClick={handleSave}
            disabled={saving}
          >
            {saving ? '保存中...' : '保存配置'}
          </Button>
        </CardActions>
      </Card>
      
      {/* 字段内容预览对话框 */}
      <Dialog 
        open={previewOpen} 
        onClose={handleClosePreview}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>{previewField.label} - 内容示例</DialogTitle>
        <DialogContent dividers>
          <Typography variant="body1" component="div" sx={{ whiteSpace: 'pre-line' }}>
            {previewField.content}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClosePreview}>关闭</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AIReportConfigPage; 