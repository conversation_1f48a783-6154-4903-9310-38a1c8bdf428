/**
 * PostgreSQL迁移前验证脚本
 * 用于检查PostgreSQL环境中可能存在的问题，并提供修复建议
 */
const knex = require('knex');
const knexfile = require('../knexfile');
const fs = require('fs');
const path = require('path');

// 获取环境配置
const env = process.env.NODE_ENV || 'development';
const config = knexfile[env];

// 创建数据库连接
const db = knex(config);

// 检查项目
const checks = [
  {
    name: '检查是否有重复的表名',
    check: async () => {
      // 检查是否有user_authorizations和authorizations表同时存在
      const hasUserAuths = await db.schema.hasTable('user_authorizations');
      const hasAuths = await db.schema.hasTable('authorizations');
      
      if (hasUserAuths && hasAuths) {
        return {
          passed: false,
          details: `发现重复的表: user_authorizations 和 authorizations 同时存在`,
          fix: `运行迁移 20250602001_drop_authorizations_if_exists.js 删除不需要的表`
        };
      }
      return { passed: true };
    }
  },
  {
    name: '检查boolean类型字段的默认值',
    check: async () => {
      // 检查可能使用数字代替布尔值的表
      const tables = ['patients', 'diseases', 'records', 'ai_reports'];
      const issues = [];
      
      for (const table of tables) {
        const hasTable = await db.schema.hasTable(table);
        if (hasTable) {
          const columns = await db.raw(`
            SELECT column_name, data_type, column_default
            FROM information_schema.columns 
            WHERE table_name = ? AND data_type = 'boolean'
          `, [table]);
          
          // 检查是否有使用0/1作为默认值的boolean字段
          if (columns.rows) {
            for (const col of columns.rows) {
              if (col.column_default === '0' || col.column_default === '1') {
                issues.push({
                  table,
                  column: col.column_name,
                  current: col.column_default,
                  expected: col.column_default === '0' ? 'false' : 'true'
                });
              }
            }
          }
        }
      }
      
      if (issues.length > 0) {
        return {
          passed: false,
          details: `发现${issues.length}个布尔字段使用了数字默认值:\n${issues.map(i => 
            `表${i.table}的${i.column}字段使用${i.current}作为默认值，应该使用${i.expected}`).join('\n')}`,
          fix: `修改迁移文件，替换 defaultTo(0)/defaultTo(1) 为 defaultTo(false)/defaultTo(true)`
        };
      }
      
      return { passed: true };
    }
  },
  {
    name: '检查外键类型匹配',
    check: async () => {
      // 检查外键引用类型是否匹配
      const issues = [];
      
      const relations = await db.raw(`
        SELECT
          tc.table_name,
          kcu.column_name,
          ccu.table_name AS foreign_table_name,
          ccu.column_name AS foreign_column_name
        FROM
          information_schema.table_constraints tc
          JOIN information_schema.key_column_usage kcu
            ON tc.constraint_name = kcu.constraint_name
          JOIN information_schema.constraint_column_usage ccu
            ON ccu.constraint_name = tc.constraint_name
        WHERE constraint_type = 'FOREIGN KEY';
      `);
      
      if (relations.rows) {
        for (const relation of relations.rows) {
          // 获取源列和目标列的数据类型
          const sourceColumn = await db.raw(`
            SELECT data_type FROM information_schema.columns
            WHERE table_name = ? AND column_name = ?
          `, [relation.table_name, relation.column_name]);
          
          const targetColumn = await db.raw(`
            SELECT data_type FROM information_schema.columns
            WHERE table_name = ? AND column_name = ?
          `, [relation.foreign_table_name, relation.foreign_column_name]);
          
          if (sourceColumn.rows && targetColumn.rows && 
              sourceColumn.rows.length > 0 && targetColumn.rows.length > 0) {
            const sourceType = sourceColumn.rows[0].data_type;
            const targetType = targetColumn.rows[0].data_type;
            
            if (sourceType !== targetType) {
              issues.push({
                table: relation.table_name,
                column: relation.column_name,
                sourceType,
                foreignTable: relation.foreign_table_name,
                foreignColumn: relation.foreign_column_name,
                foreignType: targetType
              });
            }
          }
        }
      }
      
      if (issues.length > 0) {
        return {
          passed: false,
          details: `发现${issues.length}个外键类型不匹配:\n${issues.map(i => 
            `表${i.table}的${i.column}(${i.sourceType})引用了表${i.foreignTable}的${i.foreignColumn}(${i.foreignType})`).join('\n')}`,
          fix: '修改迁移文件，确保外键类型与引用的列类型匹配'
        };
      }
      
      return { passed: true };
    }
  },
  {
    name: '检查迁移文件中的Promise返回',
    check: async () => {
      // 检查迁移文件是否正确返回Promise
      const migrationsDir = path.join(__dirname, '..', 'migrations');
      const issues = [];
      
      const files = fs.readdirSync(migrationsDir).filter(f => f.endsWith('.js'));
      
      for (const file of files) {
        const content = fs.readFileSync(path.join(migrationsDir, file), 'utf8');
        
        // 检查是否使用了async/await但没有正确返回Promise
        if (content.includes('async function') && 
            !content.includes('return knex.schema') && 
            !content.includes('return Promise')) {
          issues.push(file);
        }
      }
      
      if (issues.length > 0) {
        return {
          passed: false,
          details: `发现${issues.length}个迁移文件可能没有正确返回Promise:\n${issues.join('\n')}`,
          fix: '修改迁移文件，确保在async函数中显式返回Promise或knex.schema操作'
        };
      }
      
      return { passed: true };
    }
  }
];

// 运行检查
async function validate() {
  console.log('开始验证PostgreSQL迁移兼容性...\n');
  
  let passedAll = true;
  const issues = [];
  
  for (const check of checks) {
    try {
      console.log(`运行检查: ${check.name}...`);
      const result = await check.check();
      
      if (result.passed) {
        console.log(`✅ 通过: ${check.name}\n`);
      } else {
        passedAll = false;
        console.log(`❌ 失败: ${check.name}`);
        console.log(`  详情: ${result.details}`);
        console.log(`  建议: ${result.fix}\n`);
        
        issues.push({
          name: check.name,
          details: result.details,
          fix: result.fix
        });
      }
    } catch (error) {
      passedAll = false;
      console.error(`❌ 错误: 检查"${check.name}"执行失败`);
      console.error(`  错误信息: ${error.message}\n`);
      
      issues.push({
        name: check.name,
        details: `检查执行失败: ${error.message}`,
        fix: '检查日志以获取更多信息'
      });
    }
  }
  
  console.log('验证结果摘要:');
  if (passedAll) {
    console.log('✅ 所有检查通过，可以安全地进行PostgreSQL迁移');
  } else {
    console.log(`❌ 发现${issues.length}个问题，请在执行迁移前修复:`);
    issues.forEach((issue, index) => {
      console.log(`问题 ${index + 1}: ${issue.name}`);
      console.log(`  详情: ${issue.details}`);
      console.log(`  建议: ${issue.fix}`);
    });
  }
  
  return passedAll;
}

// 执行验证并关闭数据库连接
validate()
  .then(passed => {
    db.destroy();
    process.exit(passed ? 0 : 1);
  })
  .catch(error => {
    console.error('验证过程中发生错误:', error);
    db.destroy();
    process.exit(1);
  }); 