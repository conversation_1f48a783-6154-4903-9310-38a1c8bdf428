/**
 * 服务器启动脚本
 * 同时启动WebSocket服务器和主应用服务器
 */
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// 确保日志目录存在
const logDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true }); // 确保递归创建目录
}

// 计算端口号
const basePort = parseInt(process.env.PORT || '3001', 10);
// 根据环境设置不同的WebSocket默认端口
// 开发环境使用3003，生产环境使用3005
const baseWsPort = parseInt(process.env.WS_PORT || (process.env.NODE_ENV === 'production' ? '3005' : '3003'), 10);
const instanceId = parseInt(process.env.NODE_APP_INSTANCE || '0', 10);

const port = basePort + instanceId;
const wsPort = baseWsPort + instanceId;

// 添加时间戳函数
const timestamp = () => new Date().toISOString();

console.log(`[${timestamp()}] Starting instance ${instanceId} with ports: API=${port}, WS=${wsPort}`);

// 启动主服务器
const mainServer = spawn('node', ['src/index.js'], {
    env: {
        ...process.env,
        PORT: port.toString(),
        WS_PORT: wsPort.toString(),
        NODE_APP_INSTANCE: instanceId.toString()
    },
    // 修改 stdio 为 pipe 以捕获子进程输出
    stdio: ['inherit', 'pipe', 'pipe']
});

// 重定向输出到日志文件
const logStream = fs.createWriteStream(
    path.join(logDir, `server-${instanceId}.log`),
    { flags: 'a' }
);

// 处理标准输出
mainServer.stdout.on('data', (data) => {
    const message = data.toString().trim(); // 去除可能的换行符
    if (message) {
        console.log(`[${timestamp()}] Instance ${instanceId} stdout: ${message}`);
        logStream.write(`[${timestamp()}] ${message}
`);
    }
});

// 处理标准错误
mainServer.stderr.on('data', (data) => {
    const message = data.toString().trim(); // 去除可能的换行符
    if (message) {
        console.error(`[${timestamp()}] Instance ${instanceId} stderr: ${message}`);
        logStream.write(`[${timestamp()}] ERROR: ${message}
`);
    }
});

// 处理进程退出
mainServer.on('exit', (code, signal) => {
    const message = `Instance ${instanceId} exited with code ${code} and signal ${signal}`;
    console.log(`[${timestamp()}] ${message}`);
    logStream.write(`[${timestamp()}] ${message}
`);
    logStream.end(); // 进程退出时关闭日志流
});

// 处理进程错误 (例如，找不到node命令)
mainServer.on('error', (error) => {
    const message = `Instance ${instanceId} spawn error: ${error.message}`;
    console.error(`[${timestamp()}] ${message}`);
    logStream.write(`[${timestamp()}] ERROR: ${message}
`);
    logStream.end(); // 发生错误时关闭日志流
});

// 处理主进程信号
process.on('SIGTERM', () => {
    console.log(`[${timestamp()}] Instance ${instanceId} received SIGTERM signal`);
    mainServer.kill('SIGTERM');
});

process.on('SIGINT', () => {
    console.log(`[${timestamp()}] Instance ${instanceId} received SIGINT signal`);
    mainServer.kill('SIGINT');
});

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
    const message = `Instance ${instanceId} uncaught exception: ${error.stack}`;
    console.error(`[${timestamp()}] ${message}`);
    logStream.write(`[${timestamp()}] ERROR: ${message}
`);
    // 考虑在这里进行更彻底的清理或重启
    process.exit(1);
});

// 处理未处理的Promise拒绝
process.on('unhandledRejection', (reason, promise) => {
    const message = `Instance ${instanceId} unhandled rejection: ${reason}`;
    console.error(`[${timestamp()}] ${message}`);
    logStream.write(`[${timestamp()}] ERROR: ${message}
`);
    // 考虑在这里进行更彻底的清理或重启
});

// 启动服务器 （通常通过子进程启动，这里不需要直接调用startServer）
// startServer(); // 这行应该被移除或注释掉，因为服务是在子进程中启动的