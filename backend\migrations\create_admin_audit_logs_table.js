/**
 * 创建管理员审计日志表的迁移
 */
exports.up = function(knex) {
  return knex.schema.hasTable('admin_audit_logs').then(function(exists) {
    if (!exists) {
      return knex.schema.createTable('admin_audit_logs', function(table) {
        table.uuid('id').primary();
        table.uuid('admin_id').notNullable().references('id').inTable('users').onDelete('CASCADE');
        table.string('action_type', 100).notNullable();
        table.uuid('target_id').nullable();
        table.text('action_details').nullable();
        table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());
        
        // 添加索引
        table.index('admin_id');
        table.index('action_type');
        table.index('target_id');
        table.index('created_at');
      });
    }
  });
};

/**
 * 回滚迁移，删除管理员审计日志表
 */
exports.down = function(knex) {
  return knex.schema.dropTableIfExists('admin_audit_logs');
}; 