/**
 * API错误监控工具
 * 用于收集、分析和报告API错误
 */

// 错误类型定义
export interface ApiError {
  url: string;
  method: string;
  status: number;
  timestamp: string;
  errorMessage: string;
  hasApiPrefix: boolean;
  path: string;
  queryParams?: Record<string, string>;
  requestBody?: any;
  responseData?: any;
  stack?: string;
}

// 错误存储键
const API_ERRORS_KEY = 'api_errors';
const MAX_STORED_ERRORS = 50;

// 错误统计键
const ERROR_STATS_KEY = 'api_error_stats';

// 错误统计接口
interface ErrorStats {
  totalErrors: number;
  byStatus: Record<number, number>;
  byPath: Record<string, number>;
  byMethod: Record<string, number>;
  lastErrorTime: string;
  firstErrorTime: string;
}

/**
 * 记录API错误
 * @param error - 错误对象
 * @param requestConfig - 请求配置
 */
export function logApiError(error: any, requestConfig?: any): void {
  try {
    if (!error.response) return;
    
    const apiError: ApiError = {
      url: error.config?.url || '',
      method: error.config?.method?.toUpperCase() || 'UNKNOWN',
      status: error.response.status,
      timestamp: new Date().toISOString(),
      errorMessage: error.message,
      hasApiPrefix: error.config?.url?.includes('/api') || false,
      path: error.config?.url?.split('?')[0] || '',
      queryParams: error.config?.params,
      requestBody: error.config?.data,
      responseData: error.response.data,
      stack: error.stack
    };
    
    // 从localStorage获取现有错误
    const storedErrorsJson = localStorage.getItem(API_ERRORS_KEY) || '[]';
    const storedErrors: ApiError[] = JSON.parse(storedErrorsJson);
    
    // 添加新错误并限制数量
    storedErrors.unshift(apiError);
    if (storedErrors.length > MAX_STORED_ERRORS) {
      storedErrors.length = MAX_STORED_ERRORS;
    }
    
    // 保存回localStorage
    localStorage.setItem(API_ERRORS_KEY, JSON.stringify(storedErrors));
    
    // 更新错误统计
    updateErrorStats(apiError);
    
    console.error('[ApiMonitor] 记录API错误:', apiError);
  } catch (e) {
    console.error('记录API错误失败:', e);
  }
}

/**
 * 更新错误统计
 * @param error - API错误对象
 */
function updateErrorStats(error: ApiError): void {
  try {
    const statsJson = localStorage.getItem(ERROR_STATS_KEY) || '{}';
    const stats: ErrorStats = JSON.parse(statsJson);
    
    // 初始化统计对象
    if (!stats.byStatus) stats.byStatus = {};
    if (!stats.byPath) stats.byPath = {};
    if (!stats.byMethod) stats.byMethod = {};
    
    // 更新统计
    stats.totalErrors = (stats.totalErrors || 0) + 1;
    stats.byStatus[error.status] = (stats.byStatus[error.status] || 0) + 1;
    stats.byPath[error.path] = (stats.byPath[error.path] || 0) + 1;
    stats.byMethod[error.method] = (stats.byMethod[error.method] || 0) + 1;
    
    // 更新时间戳
    stats.lastErrorTime = error.timestamp;
    if (!stats.firstErrorTime) {
      stats.firstErrorTime = error.timestamp;
    }
    
    // 保存统计
    localStorage.setItem(ERROR_STATS_KEY, JSON.stringify(stats));
  } catch (e) {
    console.error('更新错误统计失败:', e);
  }
}

/**
 * 获取记录的API错误
 */
export function getApiErrors(): ApiError[] {
  try {
    const storedErrorsJson = localStorage.getItem(API_ERRORS_KEY) || '[]';
    return JSON.parse(storedErrorsJson);
  } catch (e) {
    console.error('获取API错误记录失败:', e);
    return [];
  }
}

/**
 * 获取错误统计
 */
export function getErrorStats(): ErrorStats {
  try {
    const statsJson = localStorage.getItem(ERROR_STATS_KEY) || '{}';
    return JSON.parse(statsJson);
  } catch (e) {
    console.error('获取错误统计失败:', e);
    return {
      totalErrors: 0,
      byStatus: {},
      byPath: {},
      byMethod: {},
      lastErrorTime: '',
      firstErrorTime: ''
    };
  }
}

/**
 * 清除API错误记录
 */
export function clearApiErrors(): void {
  localStorage.removeItem(API_ERRORS_KEY);
  localStorage.removeItem(ERROR_STATS_KEY);
}

/**
 * 分析错误模式
 * @returns 错误分析报告
 */
export function analyzeErrors(): {
  mostCommonStatus: number;
  mostCommonPath: string;
  mostCommonMethod: string;
  errorRate: number;
  timeSpan: string;
} {
  const stats = getErrorStats();
  const errors = getApiErrors();
  
  if (errors.length === 0) {
    return {
      mostCommonStatus: 0,
      mostCommonPath: '',
      mostCommonMethod: '',
      errorRate: 0,
      timeSpan: ''
    };
  }
  
  // 计算最常见的状态码
  const mostCommonStatus = Object.entries(stats.byStatus)
    .sort(([, a], [, b]) => b - a)[0]?.[0];
  
  // 计算最常见的路径
  const mostCommonPath = Object.entries(stats.byPath)
    .sort(([, a], [, b]) => b - a)[0]?.[0];
  
  // 计算最常见的方法
  const mostCommonMethod = Object.entries(stats.byMethod)
    .sort(([, a], [, b]) => b - a)[0]?.[0];
  
  // 计算错误率（如果有总请求数）
  const totalRequests = localStorage.getItem('total_api_requests') || '0';
  const errorRate = stats.totalErrors / parseInt(totalRequests);
  
  // 计算时间跨度
  const timeSpan = stats.firstErrorTime && stats.lastErrorTime
    ? new Date(stats.lastErrorTime).getTime() - new Date(stats.firstErrorTime).getTime()
    : 0;
  
  return {
    mostCommonStatus: parseInt(mostCommonStatus) || 0,
    mostCommonPath: mostCommonPath || '',
    mostCommonMethod: mostCommonMethod || '',
    errorRate: isNaN(errorRate) ? 0 : errorRate,
    timeSpan: `${Math.round(timeSpan / (1000 * 60 * 60))}小时`
  };
}

/**
 * 导出错误报告
 * @returns 格式化的错误报告
 */
export function exportErrorReport(): string {
  const errors = getApiErrors();
  const stats = getErrorStats();
  const analysis = analyzeErrors();
  
  return JSON.stringify({
    summary: {
      totalErrors: stats.totalErrors,
      timeSpan: analysis.timeSpan,
      errorRate: analysis.errorRate
    },
    mostCommon: {
      status: analysis.mostCommonStatus,
      path: analysis.mostCommonPath,
      method: analysis.mostCommonMethod
    },
    recentErrors: errors.slice(0, 10),
    statusDistribution: stats.byStatus,
    pathDistribution: stats.byPath,
    methodDistribution: stats.byMethod
  }, null, 2);
} 