const express = require('express');
const router = express.Router();
const { v4: uuidv4 } = require('uuid');
const knex = require('knex')(require('../knexfile').development);
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { auth } = require('../src/middleware/auth');

// JWT密钥，应从环境变量获取
const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret';

// 获取用户个人资料
router.get('/profile', auth, async (req, res) => {
  try {
    // 使用auth中间件设置的userId
    const { user_id } = req;
    
    console.log('获取用户资料, 用户ID:', user_id);
    
    // 获取用户数据
    const user = await knex('users')
      .where('id', user_id)
      .select(
        'id',
        'username',
        'email',
        'phone_number',
        'role',
        'level',
        'avatar',
        'active_disease_limit',
        'ai_usage_count',
        'family_member_limit',
        'created_at',
        'updated_at',
        'last_login_at'
      )
      .first();
    
    if (!user) {
      return res.status(404).json({ error: '用户不存在' });
    }
    
    console.log('查询到的用户原始数据:', {
      ...user,
      avatar: user.avatar,
      avatar类型: typeof user.avatar,
      avatar长度: user.avatar !== null ? user.avatar.length : 'null'
    });
    
    // 获取该等级的用户限制数据
    const levelLimits = await knex('user_level_limits')
      .where('level_type', user.level)
      .first();
    
    console.log('获取到的用户等级限制信息:', levelLimits);
    
    // 转换为驼峰命名
    const transformedUser = {
      id: user.id,
      username: user.username,
      email: user.email,
      phoneNumber: user.phone_number,
      role: user.role,
      level: user.level,
      avatar: user.avatar,
      activeDiseaseLimit: user.active_disease_limit,
      aiUsageCount: user.ai_usage_count,
      familyMemberLimit: user.family_member_limit,
      createdAt: user.created_at,
      updatedAt: user.updated_at,
      lastLoginAt: user.last_login_at
    };
    
    // 加入用户等级限制信息
    if (levelLimits) {
      transformedUser.maxPatients = levelLimits.max_patients;
      transformedUser.maxPathologies = levelLimits.max_pathologies;
      transformedUser.maxAttachmentSize = levelLimits.max_attachment_size;
      transformedUser.maxTotalStorage = levelLimits.max_total_storage;
      transformedUser.maxAiUsed = levelLimits.max_ai_used;
      
      // 添加调试标记
      transformedUser.__DEBUG_HAS_LIMITS = true;
      
      // 记录用户权益数据用于调试
      console.log('已添加用户权益信息:', {
        maxPatients: transformedUser.maxPatients,
        maxPathologies: transformedUser.maxPathologies,
        maxAttachmentSize: transformedUser.maxAttachmentSize,
        maxTotalStorage: transformedUser.maxTotalStorage
      });
    } else {
      // 添加调试标记
      transformedUser.__DEBUG_HAS_LIMITS = false;
      console.warn('未找到用户等级限制数据');
    }
    
    // 记录最终要返回的数据
    console.log('准备返回的用户数据:', {
      字段列表: Object.keys(transformedUser),
      avatar: transformedUser.avatar,
      avatar类型: typeof transformedUser.avatar,
      id: transformedUser.id,
      maxPatients: transformedUser.maxPatients, 
      maxPatients类型: typeof transformedUser.maxPatients
    });
    
    res.json(transformedUser);
  } catch (error) {
    console.error('获取用户资料失败:', error);
    res.status(500).json({ error: '获取用户资料失败' });
  }
});

// 更新用户资料
router.put('/profile', auth, async (req, res) => {
  try {
    const { user_id } = req;
    const { email, phone_number, avatar } = req.body;
    
    console.log('收到更新用户资料请求:', {
      用户ID: user_id,
      邮箱: email,
      手机号: phone_number,
      头像URL: avatar,
      头像URL类型: typeof avatar,
      头像URL长度: avatar !== undefined ? (avatar !== null ? avatar.length : 'null') : 'undefined',
      请求体完整内容: JSON.stringify(req.body),
    });
    
    const user = await knex('users')
      .where('id', user_id)
      .first();
    
    if (!user) {
      return res.status(404).json({ error: '用户不存在' });
    }
    
    // 检查当前用户的头像
    console.log('用户当前头像值:', {
      avatar: user.avatar,
      类型: typeof user.avatar,
      长度: user.avatar !== null ? user.avatar.length : 'null'
    });
    
    // 组装更新数据
    const updateData = {
      updated_at: new Date().toISOString()
    };
    
    // 只更新提供的字段
    if (email !== undefined) updateData.email = email;
    if (phone_number !== undefined) updateData.phone_number = phone_number;
    
    // 头像URL处理 - 重要修复
    if (avatar !== undefined) {
      // 记录更新头像的操作
      console.log(`准备更新头像URL: "${avatar}" (${typeof avatar})`);
      updateData.avatar = avatar;
    }
    
    console.log('更新用户资料的数据:', {
      ...updateData,
      avatar: updateData.avatar,
      avatar类型: typeof updateData.avatar,
      avatar长度: updateData.avatar !== undefined ? 
        (updateData.avatar !== null ? updateData.avatar.length : 'null') : 
        'undefined',
    });
    
    // 直接使用原始SQL执行更新，以便查看执行的具体SQL
    let updateSql = 'UPDATE users SET ';
    const updateParams = [];
    const updateFields = [];
    
    Object.entries(updateData).forEach(([key, value]) => {
      updateFields.push(`${key} = ?`);
      updateParams.push(value);
    });
    
    updateSql += updateFields.join(', ');
    updateSql += ' WHERE id = ?';
    updateParams.push(user_id);
    
    console.log('执行SQL:', updateSql, '参数:', updateParams);
    
    // 执行原始SQL更新
    await knex.raw(updateSql, updateParams);
    
    // 再次执行普通更新作为备份方案
    await knex('users')
      .where('id', user_id)
      .update(updateData);
    
    // 获取更新后的用户数据
    const updatedUser = await knex('users')
      .where('id', user_id)
      .select(
        'id',
        'username',
        'email',
        'phone_number',
        'role',
        'level',
        'avatar',
        'active_disease_limit',
        'ai_usage_count',
        'family_member_limit',
        'created_at',
        'updated_at',
        'last_login_at'
      )
      .first();
    
    console.log('更新后的用户数据:', {
      ...updatedUser,
      avatar: updatedUser.avatar,
      avatar类型: typeof updatedUser.avatar,
      avatar长度: updatedUser.avatar !== null ? updatedUser.avatar.length : 'null'
    });
    
    // 获取该等级的用户限制数据
    const levelLimits = await knex('user_level_limits')
      .where('level_type', updatedUser.level)
      .first();
    
    console.log('获取到的用户等级限制信息:', levelLimits);
    
    // 转换为驼峰命名
    const transformedUser = {
      id: updatedUser.id,
      username: updatedUser.username,
      email: updatedUser.email,
      phoneNumber: updatedUser.phone_number,
      role: updatedUser.role,
      level: updatedUser.level,
      avatar: updatedUser.avatar, // 不再使用 || null，保持原始值
      activeDiseaseLimit: updatedUser.active_disease_limit,
      aiUsageCount: updatedUser.ai_usage_count,
      familyMemberLimit: updatedUser.family_member_limit,
      createdAt: updatedUser.created_at,
      updatedAt: updatedUser.updated_at,
      lastLoginAt: updatedUser.last_login_at
    };
    
    // 加入用户等级限制信息
    if (levelLimits) {
      transformedUser.maxPatients = levelLimits.max_patients;
      transformedUser.maxPathologies = levelLimits.max_pathologies;
      transformedUser.maxAttachmentSize = levelLimits.max_attachment_size;
      transformedUser.maxTotalStorage = levelLimits.max_total_storage;
      transformedUser.maxAiUsed = levelLimits.max_ai_used;
      
      // 添加调试标记
      transformedUser.__DEBUG_HAS_LIMITS = true;
      
      // 记录用户权益数据用于调试
      console.log('已添加用户权益信息:', {
        maxPatients: transformedUser.maxPatients,
        maxPathologies: transformedUser.maxPathologies,
        maxAttachmentSize: transformedUser.maxAttachmentSize,
        maxTotalStorage: transformedUser.maxTotalStorage
      });
    } else {
      // 添加调试标记
      transformedUser.__DEBUG_HAS_LIMITS = false;
      console.warn('未找到用户等级限制数据');
    }
    
    console.log('响应前的最终用户数据:', {
      ...transformedUser,
      avatar: transformedUser.avatar,
      avatar类型: typeof transformedUser.avatar,
      avatar长度: transformedUser.avatar !== null ? transformedUser.avatar.length : 'null'
    });
    
    res.json({
      message: '用户资料更新成功',
      user: transformedUser
    });
  } catch (error) {
    console.error('更新用户资料失败:', error);
    res.status(500).json({ error: '更新用户资料失败' });
  }
});

// 更改密码
router.put('/change-password', auth, async (req, res) => {
  try {
    const { user_id } = req;
    const { currentPassword, newPassword } = req.body;
    
    if (!currentPassword || !newPassword) {
      return res.status(400).json({ error: '当前密码和新密码都是必填项' });
    }
    
    const user = await knex('users')
      .where('id', user_id)
      .first();
    
    if (!user) {
      return res.status(404).json({ error: '用户不存在' });
    }
    
    // 验证当前密码
    const passwordMatch = await bcrypt.compare(currentPassword, user.password_hash);
    if (!passwordMatch) {
      return res.status(401).json({ error: '当前密码不正确' });
    }
    
    // 生成新密码的哈希值
    const newPasswordHash = await bcrypt.hash(newPassword, 10);
    
    // 更新密码
    const now = new Date().toISOString();
    await knex('users')
      .where('id', user_id)
      .update({
        password_hash: newPasswordHash,
        updated_at: now
      });
    
    res.json({ message: '密码修改成功' });
  } catch (error) {
    console.error('修改密码失败:', error);
    res.status(500).json({ error: '修改密码失败' });
  }
});

// 增加POST方法的修改密码路由，保持与PUT方法相同的逻辑
router.post('/change-password', auth, async (req, res) => {
  try {
    const { user_id } = req;
    const { currentPassword, newPassword } = req.body;
    
    console.log('POST方法修改密码，请求参数:', { 
      用户ID: user_id,
      请求体: JSON.stringify(req.body)
    });
    
    if (!currentPassword || !newPassword) {
      return res.status(400).json({ error: '当前密码和新密码都是必填项' });
    }
    
    const user = await knex('users')
      .where('id', user_id)
      .first();
    
    if (!user) {
      return res.status(404).json({ error: '用户不存在' });
    }
    
    // 验证当前密码
    const passwordMatch = await bcrypt.compare(currentPassword, user.password_hash);
    if (!passwordMatch) {
      return res.status(401).json({ error: '当前密码不正确' });
    }
    
    // 生成新密码的哈希值
    const newPasswordHash = await bcrypt.hash(newPassword, 10);
    
    // 更新密码
    const now = new Date().toISOString();
    await knex('users')
      .where('id', user_id)
      .update({
        password_hash: newPasswordHash,
        updated_at: now
      });
    
    res.json({ message: '密码修改成功' });
  } catch (error) {
    console.error('修改密码失败:', error);
    res.status(500).json({ error: '修改密码失败' });
  }
});

// 获取当前用户的权限和限制
router.get('/limits', auth, async (req, res) => {
  try {
    const { user_id } = req;
    
    const user = await knex('users')
      .where('id', user_id)
      .select(
        'level',
        'active_disease_limit',
        'ai_usage_count',
        'family_member_limit'
      )
      .first();
    
    if (!user) {
      return res.status(404).json({ error: '用户不存在' });
    }
    
    // 获取该等级的用户限制
    const levelLimits = await knex('user_level_limits')
      .where('level_type', user.level)
      .first();
    
    // 获取用户当前的患者数量 - 只统计当前用户的患者并排除已删除/非活跃的
    const patientCount = await knex('patients')
      .where('user_id', user_id)
      .where(builder => {
        // 如果存在is_deleted列，过滤掉已删除的
        if (knex.schema.hasColumn('patients', 'is_deleted')) {
          builder.where('is_deleted', 0);
        }
        // 如果存在is_active列，只统计活跃的
        if (knex.schema.hasColumn('patients', 'is_active')) {
          builder.where('is_active', 1);
        }
      })
      .count('id as count')
      .first();
    
    console.log('患者数量统计查询:', user_id, patientCount);
    
    // 获取用户当前的疾病记录数量 - 只统计当前用户的病理并排除已删除/非活跃的
    const diseaseCount = await knex('diseases')
      .where('user_id', user_id)
      .where(builder => {
        // 如果存在is_deleted列，过滤掉已删除的
        if (knex.schema.hasColumn('diseases', 'is_deleted')) {
          builder.where('is_deleted', 0);
        }
        // 如果存在is_active列，只统计活跃的
        if (knex.schema.hasColumn('diseases', 'is_active')) {
          builder.where('is_active', 1);
        }
      })
      .count('id as count')
      .first();
    
    console.log('病理数量统计查询:', user_id, diseaseCount);
    
    // 获取用户当前的记录数量 - 只统计当前用户的记录并排除已删除的
    const recordCount = await knex('records')
      .where('user_id', user_id)
      .whereNull('deleted_at')
      .count('id as count')
      .first();
    
    console.log('记录数量统计查询:', user_id, recordCount);
    
    // 转换为驼峰命名返回给前端
    res.json({
      level: user.level,
      activeDiseaseLimit: user.active_disease_limit,
      aiUsageCount: user.ai_usage_count,
      familyMemberLimit: user.family_member_limit,
      currentPatientCount: patientCount.count,
      currentDiseaseCount: diseaseCount.count,
      recordCount: recordCount ? parseInt(recordCount.count) : 0,
      levelLimits: levelLimits ? {
        levelType: levelLimits.level_type,
        maxPatients: levelLimits.max_patients,
        maxPathologies: levelLimits.max_pathologies,
        maxAttachmentSize: levelLimits.max_attachment_size,
        maxTotalStorage: levelLimits.max_total_storage
      } : {}
    });
  } catch (error) {
    console.error('获取用户限制失败:', error);
    res.status(500).json({ error: '获取用户限制失败' });
  }
});

module.exports = router; 