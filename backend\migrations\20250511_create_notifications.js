/**
 * 创建通知系统数据库迁移
 */
exports.up = function(knex) {
  return knex.schema.createTable('notifications', table => {
    // 主键ID
    table.uuid('id').primary();
    
    // 外键关联
    table.uuid('user_id').notNullable().references('id').inTable('users');
    
    // 通知基本信息
    table.string('title', 100).notNullable();
    table.text('content').notNullable();
    table.enu('category', [
      'SYSTEM', 
      'MEDICAL', 
      'AUTHORIZATION', 
      'MESSAGE', 
      'REMINDER', 
      'AI_REPORT'
    ]).notNullable().defaultTo('SYSTEM');
    
    // 通知状态
    table.boolean('is_read').notNullable().defaultTo(false);
    
    // 元数据（JSON格式，存储通知相关的其他数据）
    table.json('metadata');
    
    // 时间戳
    table.timestamp('created_at', { useTz: true }).notNullable();
    table.timestamp('updated_at', { useTz: true }).notNullable();
    table.timestamp('read_at', { useTz: true }).nullable();
    
    // 索引
    table.index('user_id');
    table.index('is_read');
    table.index('category');
    table.index('created_at');
  });
};

exports.down = function(knex) {
  return knex.schema.dropTableIfExists('notifications');
}; 