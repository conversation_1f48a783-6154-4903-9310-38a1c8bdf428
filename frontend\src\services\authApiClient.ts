import axios from 'axios';
import { useAuthStore } from '../store/authStore';

// 确定API的基础URL
let baseURL = '';  // 使用相对路径

// 如果是开发环境，使用本地URL
if (process.env.NODE_ENV === 'development') {
  baseURL = 'http://localhost:3001';
} else if (process.env.NODE_ENV === 'production') {
  // 生产环境默认使用HTTPS
  baseURL = 'https://hkb.life';
}

// 如果配置了环境变量，优先使用环境变量
if (process.env.REACT_APP_API_URL) {
  // 确保环境变量URL末尾没有斜杠和/api
  baseURL = process.env.REACT_APP_API_URL.replace(/\/api\/?$/, '').replace(/\/$/, '');
} else if (process.env.REACT_APP_API_BASE_URL) {
  // 向后兼容，支持旧的环境变量名称
  console.warn('REACT_APP_API_BASE_URL被弃用，请改用REACT_APP_API_URL');
  baseURL = process.env.REACT_APP_API_BASE_URL.replace(/\/api\/?$/, '').replace(/\/$/, '');
}

// 创建axios实例 - 不添加/api前缀
const authApiClient = axios.create({
  // API的请求地址 - 使用配置的baseURL
  baseURL: baseURL,
  timeout: 90000, // 请求超时时间设置为90秒，与后端匹配
  // 添加CORS相关配置
  withCredentials: true, // 允许跨域请求携带凭证
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// 创建直接使用的axios实例，专门用于登录和注册等不需要/api前缀的请求
export const authOnlyClient = axios.create({
  baseURL: baseURL,
  timeout: 90000,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// 添加详细的日志
console.log(`======= 直接API客户端配置 =======`);
console.log(`baseURL: ${baseURL}`);
console.log(`环境: ${process.env.NODE_ENV}`);
console.log(`REACT_APP_API_URL: ${process.env.REACT_APP_API_URL || '未设置'}`);
console.log(`使用withCredentials: ${authApiClient.defaults.withCredentials}`);
console.log(`===================================`);

// 请求拦截器：在请求头中添加token
authApiClient.interceptors.request.use(
  config => {
    // 从authStore获取token
    const token = useAuthStore.getState().token;
    if (token) {
      // 设置请求头
      config.headers.Authorization = `Bearer ${token}`;
    }

    console.log(`发送直接API请求: ${config.method?.toUpperCase()} ${config.baseURL}${config.url}`, config.params);
    return config;
  },
  error => {
    console.error('请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 为authOnlyClient添加请求拦截器
authOnlyClient.interceptors.request.use(
  config => {
    // 登录和注册请求可能不需要token，但某些接口可能需要
    const token = useAuthStore.getState().token;
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    console.log(`发送直接认证请求: ${config.method?.toUpperCase()} ${config.baseURL}${config.url}`, config.params);
    return config;
  },
  error => {
    console.error('认证请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器：处理错误和token过期
authApiClient.interceptors.response.use(
  response => {
    return response;
  },
  async error => {
    const originalRequest = error.config;

    // 处理错误响应
    if (error.response) {
      // 记录API响应错误
      console.error(`直接API响应错误: ${originalRequest.method?.toUpperCase()} ${originalRequest.url}`, error);

      // 401错误 - 未授权
      if (error.response.status === 401 && !originalRequest._retry) {
        // 标记已经尝试过重试
        originalRequest._retry = true;

        // 检查当前路径
        const currentPath = window.location.pathname;
        const isServicePage = ['/service-authorizations', '/service-records', '/service-reports', '/service-diseases'].some(path =>
          currentPath.startsWith(path)
        );

        // 详细日志
        console.error(`[authApiClient] 捕获到401错误，当前路径: ${currentPath}, 是否服务页面: ${isServicePage}`, {
          请求URL: originalRequest.url,
          请求方法: originalRequest.method,
          响应数据: error.response.data,
          时间戳: new Date().toISOString()
        });

        // 服务页面特殊处理 - 添加错误标记但不重定向
        if (isServicePage) {
          console.log('[authApiClient] 在服务页面捕获到401错误，添加特殊标记');
          error.isAuthError = true;
          error.isServicePage = true;
          return Promise.reject(error);
        }

        // 其他页面处理 - 正常注销和重定向
        try {
          // 保存当前路径，以便登录后可以返回
          if (currentPath !== '/login') {
            localStorage.setItem('redirectAfterLogin', currentPath);
          }

          // 注销用户
          useAuthStore.getState().clearAuth();

          // 重定向到登录页
          if (window.location.pathname !== '/login') {
            window.location.href = '/login';
          }
        } catch (refreshError) {
          console.error('处理token过期失败:', refreshError);
          useAuthStore.getState().clearAuth();
        }
      }
    }

    return Promise.reject(error);
  }
);

// 为authOnlyClient添加响应拦截器
authOnlyClient.interceptors.response.use(
  response => {
    return response;
  },
  async error => {
    const originalRequest = error.config;

    if (error.response) {
      console.error(`认证响应错误: ${originalRequest.method?.toUpperCase()} ${originalRequest.url}`, error);
    }

    return Promise.reject(error);
  }
);

export default authApiClient;