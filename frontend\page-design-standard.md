# 页面标准化规范

我们对 `http://localhost:3002/patients` 页面进行了一系列优化和标准化，以下是可供其他页面参照的设计标准：

## 1. 全局字体调整

- **基础字体大小**：全局设置为 `0.875rem`（减小2号）
- **标题字体**：设置为 `1.1rem`（移动端）和 `1.3rem`（桌面端）
- **按钮文字**：统一使用 `0.75rem`
- **表格/卡片文字**：使用 `0.75rem`
- **说明/辅助文字**：使用 `0.7rem` 或 `0.65rem`

## 2. 页面标题与操作按钮区域

```jsx
{/* 页面标题和添加按钮 */}
<Box sx={{ display: 'flex', alignItems: 'flex-end', justifyContent: 'space-between', mb: 0 }}>
  <Typography 
    variant="h5" 
    component="h1" 
    sx={{ 
      fontWeight: 500,
      fontSize: { xs: '1.1rem', sm: '1.3rem' },
      mb: 0
    }}
  >
    页面标题
  </Typography>
  <Box sx={{ flexShrink: 0, ml: 3 }}>
    <Button
      variant="contained"
      startIcon={<适当的图标 />}
      onClick={() => 处理点击事件()}
      sx={{ minWidth: 80, px: 2, fontSize: '0.75rem', mb: '3px' }}
    >
      按钮文本
    </Button>
  </Box>
</Box>
<Divider sx={{ mb: { xs: 2, md: 3 } }} />
```

**关键点**：
- 标题与按钮底部基线对齐（`alignItems: 'flex-end'`）
- 标题无底部边距，按钮微调 `mb: '3px'` 以精确对齐
- 分隔线紧随其后，保持固定间距

## 3. 搜索和筛选区域

```jsx
{/* 搜索和筛选区 */}
<Box 
  sx={{ 
    p: 0, 
    mb: { xs: 2, md: 3 },
    width: '100%',
    '& .MuiInputBase-root': { fontSize: '0.75rem' }, 
    '& .MuiInputLabel-root': { fontSize: '0.75rem' }, 
    '& .MuiMenuItem-root': { fontSize: '0.75rem' }
  }}
>
  {/* 搜索行 */}
  <Box sx={{ 
    display: 'grid',
    gridTemplateColumns: { xs: '1fr', md: '1fr auto' },
    gap: 2,
    mb: 2,
    width: '100%'
  }}>
    <TextField
      fullWidth
      label="搜索..."
      variant="outlined"
      size="small"
      InputProps={{
        startAdornment: <SearchOutlined sx={{ color: 'action.active', mr: 1 }} />,
      }}
    />
    
    {/* 可选的下拉筛选器 */}
    <FormControl size="small" sx={{ minWidth: 120 }}>
      {/* 下拉内容 */}
    </FormControl>
  </Box>
  
  {/* 更多筛选条件 */}
  <Box sx={{ 
    display: 'grid',
    gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' },
    gap: 2,
    width: '100%',
    mb: 2
  }}>
    {/* 筛选控件 */}
  </Box>
  
  {/* 统计信息展示 */}
  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1, color: 'text.secondary', width: '100%' }}>
    <Typography variant="body2" sx={{ fontSize: '0.7rem' }}>
      左侧统计/提示
    </Typography>
    <Typography variant="body2" sx={{ fontSize: '0.7rem' }}>
      右侧统计信息
    </Typography>
  </Box>
</Box>
```

**关键点**：
- 移除外框，使用无内边距（`p: 0`）设计
- 所有内部控件使用 `width: '100%'` 确保满宽
- 明确的间距层次：组件组之间 `mb: 2`，底部统计区 `mb: 1`
- 响应式布局：设置合理的 `gridTemplateColumns` 实现多端适配

## 4. 内容展示区域

### 4.1 移动端卡片式布局

```jsx
{/* 移动端卡片式布局 */}
<Card 
  elevation={0}
  sx={{ 
    mb: 2, 
    border: '1px solid #e0e0e0',
    borderLeft: '4px solid #主题色',
    display: 'flex',
    flexDirection: 'column',
    fontSize: '0.75rem'
  }}
>
  <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 }, display: 'flex', flexDirection: 'column' }}>
    {/* 卡片标题和操作区 */}
    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
      <Typography variant="h6" component="div" sx={{ fontSize: '0.9rem' }}>
        主标题
        {/* 可选的状态标签 */}
      </Typography>
      <Box>
        {/* 操作按钮 */}
      </Box>
    </Box>
    
    {/* 卡片内容，使用较小字体 */}
    <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5, fontSize: '0.7rem' }}>
      内容文本
    </Typography>
    
    {/* 底部信息展示 */}
    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
      <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.7rem' }}>
        左侧信息
      </Typography>
      <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.6rem', fontWeight: 500 }}>
        右侧信息/统计
      </Typography>
    </Box>
  </CardContent>
</Card>
```

### 4.2 桌面端表格布局

```jsx
{/* 桌面端表格布局 */}
<TableContainer sx={{ 
  border: '1px solid #e0e0e0',
  borderRadius: 2,
  overflow: 'hidden',
  '& .MuiTableCell-root': { fontSize: '0.75rem' }
}}>
  <Table size="small">
    <TableHead>
      <TableRow>
        <TableCell 
          onClick={() => handleSortChange('字段')}
          sx={{ cursor: 'pointer', fontWeight: 'bold', fontSize: '0.75rem' }}
        >
          列标题
          {/* 排序图标 */}
        </TableCell>
        {/* 其他列标题 */}
      </TableRow>
    </TableHead>
    <TableBody>
      {/* 表格行 */}
    </TableBody>
  </Table>
</TableContainer>
```

## 5. 辅助组件样式

### 5.1 对话框样式

```jsx
<Dialog open={dialogOpen} onClose={handleClose} maxWidth="md" fullWidth>
  <DialogTitle sx={{ fontSize: '1rem' }}>
    对话框标题
  </DialogTitle>
  <DialogContent>
    <Box sx={{ 
      display: 'grid', 
      gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)' },
      gap: 2,
      mt: 2,
      '& .MuiInputBase-root': { fontSize: '0.75rem' },
      '& .MuiInputLabel-root': { fontSize: '0.75rem' },
      '& .MuiFormControlLabel-label': { fontSize: '0.75rem' }
    }}>
      {/* 表单内容 */}
    </Box>
  </DialogContent>
  <DialogActions>
    <Button onClick={handleClose} sx={{ fontSize: '0.75rem' }}>取消</Button>
    <Button 
      onClick={handleConfirm} 
      variant="contained"
      color="primary"
      sx={{ fontSize: '0.75rem' }}
    >
      确认
    </Button>
  </DialogActions>
</Dialog>
```

### 5.2 通知提示样式

```jsx
<Snackbar 
  open={notification.open} 
  autoHideDuration={5000} 
  onClose={handleCloseNotification}
  anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
>
  <Alert 
    onClose={handleCloseNotification} 
    severity={notification.type} 
    sx={{ width: '100%', '& .MuiAlert-message': { fontSize: '0.75rem' } }}
  >
    {notification.message}
  </Alert>
</Snackbar>
```

## 6. 响应式布局原则

1. **移动端适配**：
   - 使用 `useMediaQuery(theme.breakpoints.down('sm'))` 检测移动端
   - 在移动端切换为卡片式布局
   - 单列排布筛选组件和表单

2. **桌面端排版**：
   - 多列网格布局（`grid`）提高空间利用率
   - 表格展示提供更丰富的交互功能

## 7. 其他关键优化点

1. **无边框设计**：搜索和筛选组件去除外边框，内容与卡片区域保持同宽
2. **基线对齐**：标题与操作按钮底部基线与分隔线对齐
3. **统一间距**：组件间使用一致的垂直间距
4. **字体层级**：明确的字体大小层级，确保视觉信息层次清晰
5. **响应式适配**：各组件在不同屏幕尺寸下有合理的布局调整

通过遵循以上标准，可以确保所有页面具有一致的视觉风格、布局结构和用户体验，显著提高整个应用的专业度和易用性。 