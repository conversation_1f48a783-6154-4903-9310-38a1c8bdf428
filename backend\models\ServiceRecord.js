const { Model, snakeCaseMappers } = require('objection');
const { v4: uuidv4 } = require('uuid');

class ServiceRecord extends Model {
  static get tableName() {
    return 'service_records';
  }

  static get idColumn() {
    return 'id';
  }

  static get columnNameMappers() {
    return snakeCaseMappers();
  }

  $beforeInsert() {
    this.id = this.id || uuidv4();
    this.createdAt = new Date().toISOString();
    this.updatedAt = this.createdAt;
  }

  $beforeUpdate() {
    this.updatedAt = new Date().toISOString();
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['recordId', 'authorizationId', 'serviceUserId', 'ownerUserId'],
      properties: {
        id: { type: 'string' },
        recordId: { type: 'string' },
        authorizationId: { type: 'string' },
        serviceUserId: { type: 'string' },
        ownerUserId: { type: 'string' },
        createdAt: { type: 'string' },
        updatedAt: { type: 'string' }
      }
    };
  }

  static get relationMappings() {
    const Record = require('./Record');
    const User = require('./User');
    const UserAuthorization = require('./UserAuthorization');
    
    return {
      // 关联记录
      record: {
        relation: Model.BelongsToOneRelation,
        modelClass: Record,
        join: {
          from: 'service_records.recordId',
          to: 'records.id'
        }
      },
      
      // 关联授权
      authorization: {
        relation: Model.BelongsToOneRelation,
        modelClass: UserAuthorization,
        join: {
          from: 'service_records.authorizationId',
          to: 'user_authorizations.id'
        }
      },
      
      // 服务用户
      serviceUser: {
        relation: Model.BelongsToOneRelation,
        modelClass: User,
        join: {
          from: 'service_records.serviceUserId',
          to: 'users.id'
        }
      },
      
      // 记录所有者
      ownerUser: {
        relation: Model.BelongsToOneRelation,
        modelClass: User,
        join: {
          from: 'service_records.ownerUserId',
          to: 'users.id'
        }
      }
    };
  }
}

module.exports = ServiceRecord; 