import React from 'react';
import { 
  Box,
  FormControl, 
  FormHelperText,
  Typography,
  styled,
  useMediaQuery,
  useTheme
} from '@mui/material';
import { StageNodeEnum, StagePhaseEnum, StageNodeNames, StagePhaseNames } from '../../types/recordEnums';

// 定义节点样式（圆形图标）
const StageNode = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'color' && prop !== 'isSelected' && prop !== 'isMobile'
})<{ 
  color: string; 
  isSelected: boolean;
  isMobile?: boolean;
}>(({ color, isSelected, isMobile = false }) => ({
  backgroundColor: color,
  borderRadius: '50%',
  width: isMobile ? '16px' : '40px',
  height: isMobile ? '16px' : '40px',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  cursor: 'pointer',
  border: isSelected ? `${isMobile ? 1.5 : 3}px solid #555555` : 'none',
  outline: isSelected ? `${isMobile ? 0.5 : 2}px solid white` : 'none',
  boxShadow: isSelected ? `0 0 0 ${isMobile ? 1.5 : 3}px #555555` : 'none',
  transition: 'all 0.2s',
  '&:hover': {
    opacity: 0.85,
  }
}));

// 定义阶段样式（胶囊形状）
const StagePhase = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'color' && prop !== 'isSelected' && prop !== 'isMobile'
})<{ 
  color: string; 
  isSelected: boolean;
  isMobile?: boolean;
}>(({ color, isSelected, isMobile = false }) => ({
  backgroundColor: color,
  borderRadius: '6px',
  height: isMobile ? '12px' : '26px',
  width: isMobile ? '24px' : '65px',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  cursor: 'pointer',
  border: isSelected ? `${isMobile ? 1 : 2}px solid #555555` : 'none',
  outline: isSelected ? `${isMobile ? 0.5 : 1}px solid white` : 'none',
  boxShadow: isSelected ? `0 0 0 ${isMobile ? 1 : 2}px #555555` : 'none',
  transition: 'all 0.2s',
  opacity: 0.7,
  '&:hover': {
    opacity: 1,
  }
}));

// 节点颜色
const NODE_COLORS: Record<StageNodeEnum, string> = {
  [StageNodeEnum.INITIAL_VISIT]: '#E53935',  // 红色 - 初诊
  [StageNodeEnum.DIAGNOSIS]: '#8E24AA',      // 紫色 - 确诊
  [StageNodeEnum.TREATMENT]: '#FB8C00',      // 橙色 - 治疗
  [StageNodeEnum.FOLLOW_UP]: '#FDD835',      // 黄色 - 随访
  [StageNodeEnum.PROGNOSIS]: '#43A047',      // 绿色 - 预后
  [StageNodeEnum.ARCHIVE]: '#757575'         // 灰色 - 归档
};

// 阶段与节点的关系映射
const PHASE_TO_NODE_MAP: Record<StagePhaseEnum, StageNodeEnum> = {
  [StagePhaseEnum.INITIAL]: StageNodeEnum.INITIAL_VISIT,    // 初诊阶段 -> 生病节点
  [StagePhaseEnum.DIAGNOSIS]: StageNodeEnum.DIAGNOSIS,      // 确诊阶段 -> 确诊节点
  [StagePhaseEnum.TREATMENT]: StageNodeEnum.TREATMENT,      // 治疗阶段 -> 治疗节点
  [StagePhaseEnum.RECOVERY]: StageNodeEnum.FOLLOW_UP,       // 康复阶段 -> 随访节点
  [StagePhaseEnum.PROGNOSIS]: StageNodeEnum.PROGNOSIS,      // 预后阶段 -> 预后节点
};

// 节点标签 - 使用中文名称
const NODE_LABELS: Record<StageNodeEnum, string> = {
  [StageNodeEnum.INITIAL_VISIT]: StageNodeNames[StageNodeEnum.INITIAL_VISIT],
  [StageNodeEnum.DIAGNOSIS]: StageNodeNames[StageNodeEnum.DIAGNOSIS],
  [StageNodeEnum.TREATMENT]: StageNodeNames[StageNodeEnum.TREATMENT],
  [StageNodeEnum.FOLLOW_UP]: StageNodeNames[StageNodeEnum.FOLLOW_UP],
  [StageNodeEnum.PROGNOSIS]: StageNodeNames[StageNodeEnum.PROGNOSIS],
  [StageNodeEnum.ARCHIVE]: StageNodeNames[StageNodeEnum.ARCHIVE]
};

// 定义时间线顺序
const TIMELINE_ITEMS = [
  { type: 'node', value: StageNodeEnum.INITIAL_VISIT },  // 生病
  { type: 'phase', value: StagePhaseEnum.INITIAL },      // 初诊阶段
  { type: 'node', value: StageNodeEnum.DIAGNOSIS },      // 确诊
  { type: 'phase', value: StagePhaseEnum.DIAGNOSIS },    // 确诊阶段
  { type: 'node', value: StageNodeEnum.TREATMENT },      // 治疗
  { type: 'phase', value: StagePhaseEnum.TREATMENT },    // 治疗阶段
  { type: 'node', value: StageNodeEnum.FOLLOW_UP },      // 随访
  { type: 'phase', value: StagePhaseEnum.RECOVERY },     // 康复阶段
  { type: 'node', value: StageNodeEnum.PROGNOSIS },      // 预后
];

interface StageSelectorProps {
  selectedNode: StageNodeEnum | null;
  selectedPhase: StagePhaseEnum | null;
  onNodeChange: (node: StageNodeEnum | null) => void;
  onPhaseChange: (phase: StagePhaseEnum | null) => void;
  error?: string;
  helperText?: string;
  disabled?: boolean;
}

const StageSelector: React.FC<StageSelectorProps> = ({
  selectedNode,
  selectedPhase,
  onNodeChange,
  onPhaseChange,
  error,
  helperText,
  disabled = false
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // 文字尺寸
  const fontSize = isMobile ? '0.65rem' : '0.9rem';
  
  // 文字高度
  const textHeight = isMobile ? '18px' : '22px';

  // 处理单选模式
  const handleSelection = (type: 'node' | 'phase', value: StageNodeEnum | StagePhaseEnum) => {
    if (disabled) return;
    
    if (type === 'node') {
      const nodeValue = value as StageNodeEnum;
      
      // 如果当前节点被选中，则取消选择
      if (selectedNode === nodeValue) {
        onNodeChange(null);
      } else {
        // 否则选择当前节点，并清除阶段选择
        onNodeChange(nodeValue);
        onPhaseChange(null);
        
        // 添加调试信息
        console.log('选择节点:', nodeValue, '类型:', typeof nodeValue);
      }
    } else {
      const phaseValue = value as StagePhaseEnum;
      
      // 如果当前阶段被选中，则取消选择
      if (selectedPhase === phaseValue) {
        onPhaseChange(null);
      } else {
        // 否则选择当前阶段，并清除节点选择
        onPhaseChange(phaseValue);
        onNodeChange(null);
        
        // 添加调试信息
        console.log('选择阶段:', phaseValue, '类型:', typeof phaseValue);
      }
    }
  };

  // 获取阶段对应的节点颜色
  const getPhaseColor = (phase: StagePhaseEnum) => {
    const nodeType = PHASE_TO_NODE_MAP[phase];
    return NODE_COLORS[nodeType];
  };

  return (
    <FormControl fullWidth error={!!error} disabled={disabled}>
      {/* 单行水平时间线 */}
      <Box sx={{ 
        display: 'flex',
        justifyContent: 'space-between', 
        alignItems: 'center',
        mb: 2,
        px: 0.5,
        overflow: 'auto',
        '&::-webkit-scrollbar': {
          height: '4px'
        },
        '&::-webkit-scrollbar-thumb': {
          backgroundColor: '#dddddd',
          borderRadius: '4px'
        }
      }}>
        {TIMELINE_ITEMS.map((item, index) => (
          <Box key={index} sx={{ 
            display: 'flex', 
            flexDirection: 'column', 
            alignItems: 'center',
            mx: isMobile ? 0.2 : 0.5,
            // 固定高度以确保元素对齐
            height: `calc(${textHeight} * 2 + ${isMobile ? '16px' : '40px'})`,
            // 使用不同的对齐方式来保持文字位置
            justifyContent: item.type === 'node' ? 'flex-start' : 'flex-end',
            // 胶囊图标和文字整体上移4px
            ...(item.type === 'phase' ? { marginTop: '-4px' } : {})
          }}>
            {item.type === 'node' ? (
              // 节点显示为圆形+下方文字
              <>
                <Box sx={{ height: textHeight }} /> {/* 上方留白占位 */}
                <StageNode 
                  color={NODE_COLORS[item.value as StageNodeEnum]} 
                  isSelected={selectedNode === item.value}
                  onClick={() => handleSelection('node', item.value)}
                  isMobile={isMobile}
                />
                <Typography 
                  variant="caption" 
                  align="center"
                  sx={{ 
                    fontSize: fontSize,
                    fontWeight: selectedNode === item.value ? 'bold' : 'normal',
                    maxWidth: isMobile ? '35px' : '55px',
                    mt: 0.3,
                    height: textHeight,
                    wordBreak: 'keep-all',
                    whiteSpace: 'nowrap'
                  }}
                >
                  {NODE_LABELS[item.value as StageNodeEnum]}
                </Typography>
              </>
            ) : (
              // 阶段显示为胶囊+上方文字
              <>
                <Typography 
                  variant="caption" 
                  align="center"
                  sx={{ 
                    fontSize: fontSize,
                    fontWeight: selectedPhase === item.value ? 'bold' : 'normal',
                    maxWidth: isMobile ? '35px' : '55px',
                    mb: 0.3,
                    height: textHeight,
                    wordBreak: 'keep-all',
                    whiteSpace: 'nowrap'
                  }}
                >
                  {StagePhaseNames[item.value as StagePhaseEnum]}
                </Typography>
                <StagePhase
                  color={getPhaseColor(item.value as StagePhaseEnum)}
                  isSelected={selectedPhase === item.value}
                  onClick={() => handleSelection('phase', item.value)}
                  isMobile={isMobile}
                />
                <Box sx={{ height: textHeight }} /> {/* 下方留白占位 */}
              </>
            )}
          </Box>
        ))}
      </Box>
      
      {/* 帮助文本 */}
      {(helperText || error) && (
        <FormHelperText sx={{ fontSize: isMobile ? '0.7rem' : '0.75rem' }}>
          {error || helperText}
        </FormHelperText>
      )}
    </FormControl>
  );
};

export default StageSelector;