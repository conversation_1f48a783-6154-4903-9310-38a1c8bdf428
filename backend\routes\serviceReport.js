const express = require('express');
const router = express.Router();
const { authenticate } = require('../src/middleware/auth');
const serviceReportController = require('../controllers/serviceReportController');

// 获取服务用户创建的报告
router.get('/', authenticate, serviceReportController.getServiceReports);

// 获取被授权用户的报告列表
router.get('/authorized/:authorizationId/reports', authenticate, serviceReportController.getAuthorizedUserReports);

// 创建服务报告
router.post('/', authenticate, serviceReportController.createServiceReport);

// 生成扩展版PDF报告
router.post('/:serviceReportId/extended-pdf', authenticate, serviceReportController.generateExtendedPdf);

// 根据授权ID获取服务报告
router.get('/by-auth/:authorizationId', authenticate, serviceReportController.getServiceReports);

module.exports = router; 