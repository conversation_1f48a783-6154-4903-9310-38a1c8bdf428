/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return Promise.all([
    knex.schema.table('diseases', table => {
      table.uuid('created_by').nullable().references('id').inTable('users');
      table.uuid('updated_by').nullable().references('id').inTable('users');
    }),
    knex.schema.table('ai_reports', table => {
      table.uuid('created_by').nullable().references('id').inTable('users');
      table.uuid('updated_by').nullable().references('id').inTable('users');
    })
  ]);
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function(knex) {
  // 检查diseases表是否存在created_by和updated_by列
  const diseasesHasCreatedBy = await knex.schema.hasColumn('diseases', 'created_by');
  const diseasesHasUpdatedBy = await knex.schema.hasColumn('diseases', 'updated_by');
  
  // 检查ai_reports表是否存在created_by和updated_by列
  const aiReportsHasCreatedBy = await knex.schema.hasColumn('ai_reports', 'created_by');
  const aiReportsHasUpdatedBy = await knex.schema.hasColumn('ai_reports', 'updated_by');
  
  // 执行删除操作，只有在列存在时才删除
  const promises = [];
  
  if (diseasesHasCreatedBy || diseasesHasUpdatedBy) {
    promises.push(knex.schema.table('diseases', table => {
      if (diseasesHasCreatedBy) table.dropColumn('created_by');
      if (diseasesHasUpdatedBy) table.dropColumn('updated_by');
    }));
  }
  
  if (aiReportsHasCreatedBy || aiReportsHasUpdatedBy) {
    promises.push(knex.schema.table('ai_reports', table => {
      if (aiReportsHasCreatedBy) table.dropColumn('created_by');
      if (aiReportsHasUpdatedBy) table.dropColumn('updated_by');
    }));
  }
  
  return Promise.all(promises);
};
