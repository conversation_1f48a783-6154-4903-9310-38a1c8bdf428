/**
 * API适配器
 * 用于处理前后端接口格式差异
 */

/**
 * 将对象中的键从蛇形命名转换为驼峰命名
 * 例如: first_name -> firstName
 * @param {Object} obj 原始对象
 * @returns {Object} 转换后的对象
 */
export function toCamelCase(obj) {
  if (!obj || typeof obj !== 'object') return obj;
  
  if (Array.isArray(obj)) {
    return obj.map(toCamelCase);
  }
  
  const result = {};
  
  Object.keys(obj).forEach(key => {
    // 转换键名
    const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
    
    // 递归转换值
    if (obj[key] !== null && typeof obj[key] === 'object') {
      result[camelKey] = toCamelCase(obj[key]);
    } else {
      result[camelKey] = obj[key];
    }
  });
  
  return result;
}

/**
 * 将对象中的键从驼峰命名转换为蛇形命名
 * 例如: firstName -> first_name
 * @param {Object} obj 原始对象
 * @returns {Object} 转换后的对象
 */
export function toSnakeCase(obj) {
  if (!obj || typeof obj !== 'object') return obj;
  
  if (Array.isArray(obj)) {
    return obj.map(toSnakeCase);
  }
  
  const result = {};
  
  Object.keys(obj).forEach(key => {
    // 转换键名
    const snakeKey = key.replace(/([A-Z])/g, '_$1').toLowerCase();
    
    // 递归转换值
    if (obj[key] !== null && typeof obj[key] === 'object') {
      result[snakeKey] = toSnakeCase(obj[key]);
    } else {
      result[snakeKey] = obj[key];
    }
  });
  
  return result;
}

/**
 * 适配AI报告，确保前后端兼容
 * @param {Object} report AI报告对象
 * @returns {Object} 适配后的对象
 */
export function adaptAIReport(report) {
  if (!report) return null;
  
  // 转换命名规则
  const adaptedReport = toCamelCase(report);
  
  // 补充可能缺失的字段
  if (!adaptedReport.llmConfigs) adaptedReport.llmConfigs = {};
  if (!adaptedReport.visibleFields) adaptedReport.visibleFields = [];
  if (!adaptedReport.configId) adaptedReport.configId = null;
  
  return adaptedReport;
}

/**
 * 适配后端API响应，确保格式正确
 * @param {Object} response API响应对象
 * @returns {Object} 适配后的响应
 */
export function adaptApiResponse(response) {
  // 处理不同格式的后端响应
  if (response.aiReport) {
    return {
      ...response,
      aiReport: adaptAIReport(response.aiReport)
    };
  } else if (response.aiReports) {
    return {
      ...response,
      aiReports: response.aiReports.map(adaptAIReport)
    };
  } else if (Array.isArray(response)) {
    // 如果直接返回数组，假定是报告列表
    return response.map(adaptAIReport);
  } else if (response.id) {
    // 如果是单个对象且有ID，可能是单个报告
    return adaptAIReport(response);
  }
  
  // 其他情况，原样返回
  return response;
}