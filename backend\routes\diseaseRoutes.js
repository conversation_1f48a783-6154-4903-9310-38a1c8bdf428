const express = require('express');
const router = express.Router();
const Disease = require('../models/Disease');
const Patient = require('../models/Patient');
const { auth } = require('../src/middleware/auth');
const knex = require('knex')(require('../knexfile').development);

/**
 * 获取疾病列表，支持筛选和搜索
 * GET /diseases?patient_id={id}&stage={stage}&name={keyword}
 */
router.get('/', auth, async (req, res) => {
  try {
    const { patient_id, stage, name, only_current_user, service_context, only_authorized_data, authorization_id } = req.query;
    const userRole = req.user.role;
    const userId = req.userId || req.user_id || req.user.id;
    
    // 判断操作上下文 - 普通用户上下文还是服务上下文
    const isServiceContext = service_context === 'true';
    
    console.log(`[DEBUG] 获取疾病列表请求: 用户ID=${userId}, 用户角色=${userRole}, 患者ID=${patient_id || '未指定'}, 服务上下文=${isServiceContext}, 只显示授权数据=${only_authorized_data === 'true'}`);
    
    // 构建查询
    let query = Disease.query()
      .where('is_deleted', false)
      .orderBy('created_at', 'desc');
    
    // 按患者ID筛选
    if (patient_id) {
      // 验证患者是否存在
      const patient = await Patient.query()
        .where({ id: patient_id })
        .first();
      
      if (!patient) {
        console.log(`[DEBUG] 患者不存在: ID=${patient_id}`);
        return res.status(404).json({ message: '患者不存在' });
      }
      
      // 使用正确的驼峰命名属性
      const patientUserId = patient.userId;
      
      // 权限检查
      const isDirectOwner = patientUserId === userId;
      const isAdmin = userRole === 'ADMIN';
      
      // 在普通用户上下文中，任何用户都只能看到自己的患者的疾病
      if (!isServiceContext) {
        console.log(`[DEBUG] 普通用户上下文: 用户只能查看自己的患者的疾病`);
        if (!isDirectOwner && !isAdmin) {
          console.log(`[DEBUG] 普通用户上下文: 用户无权访问非自己的患者的疾病`);
          return res.status(403).json({ message: '无权访问此患者的疾病信息' });
        }
        // 是直接拥有者，可以访问
        query = query.where('patient_id', patient_id);
      }
      // 在服务上下文中，检查服务用户的授权关系
      else if (userRole === 'SERVICE' || isAdmin) {
        // 如果是直接拥有者或管理员，可以访问
        if (isDirectOwner || isAdmin) {
          console.log(`[DEBUG] 服务上下文: 用户是患者的直接拥有者或管理员，允许访问`);
          query = query.where('patient_id', patient_id);
        }
        // 服务用户 - 检查授权关系
        else {
          console.log(`[DEBUG] 服务上下文: 服务用户尝试访问患者的疾病记录，检查授权关系`);
          
          // 检查对特定患者的授权
          const authorizationExists = await knex('user_authorizations')
            .where({
              'authorized_id': userId,
              'status': 'ACTIVE'
            })
            .where(function() {
              // 1. 检查是否有对特定患者的授权
              this.where('patient_id', patient_id)
              // 2. 或者检查是否有对患者所有者的全局授权(patient_id为null)
              .orWhere(function() {
                this.where('patient_id', null)
                    .andWhere('authorizer_id', patientUserId);
              });
            })
            .first();
          
          if (authorizationExists) {
            console.log(`[DEBUG] 服务用户有授权访问患者 ${patient_id} 的疾病记录`);
      query = query.where('patient_id', patient_id);
          } else {
            console.log(`[DEBUG] 服务用户无权访问患者 ${patient_id} 的疾病记录`);
            return res.status(403).json({ message: '无权访问此患者的疾病信息' });
          }
        }
      }
      // 非服务用户在服务上下文中尝试访问
      else {
        console.log(`[DEBUG] 用户角色 ${userRole} 无权访问服务上下文`);
        return res.status(403).json({ message: '无权访问服务管理功能' });
      }
    } else {
      // 若不按患者ID筛选，则按照权限规则显示疾病数据
      // 获取患者IDs
      let patientIds = [];
      
      // 检查是否强制只显示当前用户的数据，即使是管理员也一样
      const forceCurrentUserOnly = !isServiceContext || only_current_user === 'true' || only_current_user === true;
      
      // 是否只显示授权数据（不显示自己的患者数据）
      const onlyAuthorizedData = only_authorized_data === 'true';
      
      // 根据用户角色和上下文参数决定查询条件
      if (userRole === 'ADMIN' && !forceCurrentUserOnly) {
        if (onlyAuthorizedData) {
          // 如果指定了只查看授权数据，则管理员只能看到被授权的患者
          console.log('[DEBUG] 管理员只查看被授权的患者疾病数据');
          // 需要查询管理员被授权的患者
          const authorizations = await knex('user_authorizations')
            .where({
              'authorized_id': userId,
              'status': 'ACTIVE'
            });
          
          if (authorizations.length > 0) {
            // 有授权关系
            const authorizedPatientIds = authorizations
              .filter(auth => auth.patient_id)
              .map(auth => auth.patient_id);
            
            const authorizerIds = authorizations
              .filter(auth => !auth.patient_id)
              .map(auth => auth.authorizer_id);
            
            // 查询这些授权人的所有患者
            if (authorizerIds.length > 0) {
              // 确保authorizerIds中没有undefined或null值
              const validAuthorizerIds = authorizerIds.filter(id => id !== undefined && id !== null);
              
              // 只有当有有效的授权人ID时才查询
              if (validAuthorizerIds.length > 0) {
                const globallyAuthorizedPatients = await Patient.query()
                  .whereIn('user_id', validAuthorizerIds)
                  .select('id');
                
                authorizedPatientIds.push(...globallyAuthorizedPatients.map(p => p.id));
              }
            }
            
            // 去重
            patientIds = [...new Set(authorizedPatientIds)];
          }
        } else {
          // 管理员在服务上下文中可以看到所有人的患者
          console.log('[DEBUG] 管理员查看所有患者的疾病数据');
          const allPatients = await Patient.query().select('id');
          patientIds = allPatients.map(p => p.id);
        }
      } else if (userRole === 'SERVICE' && !forceCurrentUserOnly) {
        // 服务用户在服务上下文中可以看到自己的患者和被授权的患者
        console.log('[DEBUG] 服务用户查看患者的疾病数据');
        
        let accessiblePatientIds = [];
        
        // 1. 获取用户自己的患者 - 仅当不是只查看授权数据时
        if (!onlyAuthorizedData) {
          const ownPatients = await Patient.query()
            .where('user_id', userId)
            .select('id');
          
          accessiblePatientIds = [...ownPatients.map(p => p.id)];
          console.log(`[DEBUG] 服务用户自己拥有的患者数量: ${accessiblePatientIds.length}`);
        }
        
        // 2. 获取通过授权访问的患者
        // 2.1 获取活跃的授权关系
        const authorizations = await knex('user_authorizations')
          .where('authorized_id', userId)
          .where('status', 'ACTIVE');
        
        console.log(`[DEBUG] 服务用户有 ${authorizations.length} 个授权关系`);
        
        if (authorizations.length > 0) {
          // 获取有具体患者ID的授权
          const patientAuthorizations = authorizations.filter(auth => auth.patient_id);
          const authorizedPatientIds = patientAuthorizations.map(auth => auth.patient_id);
          
          if (authorizedPatientIds.length > 0) {
            accessiblePatientIds.push(...authorizedPatientIds);
          }
          
          // 获取全局授权（没有指定患者ID）
          const globalAuthorizations = authorizations.filter(auth => !auth.patient_id);
          const authorizerIds = globalAuthorizations.map(auth => auth.authorizer_id);
          
          if (authorizerIds.length > 0) {
            // 确保authorizerIds中没有undefined或null值
            const validAuthorizerIds = authorizerIds.filter(id => id !== undefined && id !== null);
            
            // 只有当有有效的授权人ID时才查询
            if (validAuthorizerIds.length > 0) {
              // 查询这些授权人的所有患者
              const globallyAuthorizedPatients = await Patient.query()
                .whereIn('user_id', validAuthorizerIds)
                .select('id');
              
              accessiblePatientIds.push(...globallyAuthorizedPatients.map(p => p.id));
            }
          }
        }
        
        // 去除重复的患者ID
        patientIds = [...new Set(accessiblePatientIds)];
        console.log(`[DEBUG] 服务用户可访问的患者总数: ${patientIds.length}`);
      } else {
        // 普通用户或在用户上下文模式下的用户，只能看到自己的患者
        console.log('[DEBUG] 用户只查看自己的患者的疾病数据', {
          userId: userId,
          role: userRole,
          context: isServiceContext ? '服务上下文' : '普通上下文'
        });
        
        const ownPatients = await Patient.query()
          .where('user_id', userId)
          .select('id');
        
        patientIds = ownPatients.map(p => p.id);
      }
      
      if (patientIds.length === 0) {
        // 如果没有患者，返回空数组
        console.log('[DEBUG] 用户没有可访问的患者，返回空列表');
        return res.json([]);
      }
      
      // 确保patientIds中没有undefined或null值
      patientIds = patientIds.filter(id => id !== undefined && id !== null);
      if (patientIds.length === 0) {
        console.log('[DEBUG] 过滤后没有有效的患者ID，返回空列表');
        return res.json([]);
      }
      
      query = query.whereIn('patient_id', patientIds);
    }
    
    // 按疾病阶段筛选
    if (stage) {
      query = query.where('stage', stage);
    }
    
    // 按疾病名称搜索（模糊查询）
    if (name) {
      query = query.where('name', 'like', `%${name}%`);
    }
    
    // 执行查询
    const diseases = await query;
    console.log(`[DEBUG] 获取到 ${diseases.length} 条疾病记录`);
    
    // 获取相关患者数据，用于标记数据源
    let patientIds = [...new Set(diseases.map(d => d.patient_id))];
    // 过滤掉undefined和null值，以防止whereIn操作失败
    patientIds = patientIds.filter(id => id !== undefined && id !== null);
    
    // 只有在有有效患者ID的情况下才进行查询
    const patients = patientIds.length > 0 
      ? await Patient.query().whereIn('id', patientIds)
      : [];
    
    console.log(`[DEBUG] 获取了 ${patients.length} 条相关患者记录进行数据源标记`);
    
    // 在返回前，为每条疾病记录添加标记，指明是否为当前用户创建的
    const enrichedDiseases = diseases.map(disease => {
      // 检查是否是用户直接创建的疾病（而不是通过授权访问的）
      const isCreatedByCurrentUser = disease.user_id === userId;
      
      // 是否是用户直接拥有的患者
      let isOwnPatient = false;
      if (disease.patient_id) {
        // 查找对应的患者记录
        const patient = patients.find(p => p.id === disease.patient_id);
        isOwnPatient = patient?.userId === userId;
      }
      
      // 添加数据源标记
      return {
        ...disease,
        dataSource: {
          isCreatedByCurrentUser, // 是否由当前用户创建
          isOwnPatient,           // 是否是用户自己的患者
          isExternalPatient: !isOwnPatient // 是否是通过授权获取的外部患者
        }
      };
    });
    
    // 直接返回添加了标记的疾病记录列表
    res.json(enrichedDiseases);
  } catch (error) {
    console.error('获取疾病列表失败:', error);
    res.status(500).json({ message: '获取疾病列表失败', error: error.message });
  }
});

/**
 * 获取疾病详情
 * GET /diseases/:id
 */
router.get('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.userId || req.user.id;
    const userRole = req.user.role;
    const { service_context, include_deleted } = req.query; // 添加 include_deleted 参数
    
    // 判断操作上下文 - 普通用户上下文还是服务上下文
    const isServiceContext = service_context === 'true';
    // 是否包含已删除的疾病
    const shouldIncludeDeleted = include_deleted === 'true';
    
    console.log(`[DEBUG] 获取疾病详情请求: 疾病ID=${id}, 用户ID=${userId}, 用户角色=${userRole}, 服务上下文=${isServiceContext}, 包含已删除=${shouldIncludeDeleted}`);
    
    // 获取疾病详情，包括患者信息
    let query = Disease.query().where({ id });
    
    // 根据参数决定是否包含已删除的疾病
    if (!shouldIncludeDeleted) {
      query = query.where('is_deleted', false);
    }
    
    const diseaseData = await query.withGraphFetched('patient').first();
    
    if (!diseaseData) {
      console.log(`[DEBUG] 疾病 ${id} 不存在或已被删除`);
      return res.status(404).json({ message: '疾病不存在或已被删除' });
    }
    
    // 获取患者信息和权限检查
    const patient = diseaseData.patient;
    if (!patient) {
      console.log(`[DEBUG] 疾病 ${id} 没有关联的患者信息`);
      return res.status(404).json({ message: '疾病对应的患者信息不存在' });
    }
    
    const patientUserId = patient.userId;
    const isDirectOwner = patientUserId === userId;
    const isAdmin = userRole === 'ADMIN';
    
    // 在普通用户上下文中，任何用户都只能看到自己的患者的疾病
    if (!isServiceContext) {
      console.log(`[DEBUG] 普通用户上下文: 用户只能查看自己的患者的疾病`);
      
      if (!isDirectOwner && !isAdmin) {
        console.log(`[DEBUG] 普通用户上下文: 用户无权访问非自己的患者的疾病`);
        return res.status(403).json({ message: '无权访问此疾病信息' });
      }
    }
    // 在服务上下文中，检查服务用户是否有授权访问
    else if (userRole === 'SERVICE' || isAdmin) {
      // 如果不是患者所有者或管理员，检查授权关系
      if (!isDirectOwner && !isAdmin) {
        console.log(`[DEBUG] 服务上下文: 服务用户尝试访问患者的疾病记录，检查授权关系`);
        
        // 查询授权关系
        const authorization = await knex('user_authorizations')
          .where({
            'authorized_id': userId,
            'status': 'ACTIVE'
          })
          .where(function() {
            this.where('patient_id', patient.id)
              .orWhere(function() {
                this.where('patient_id', null)
                    .andWhere('authorizer_id', patientUserId);
              });
          })
          .first();
        
        if (!authorization) {
          console.log(`[DEBUG] 服务上下文: 服务用户无授权访问此患者的疾病`);
          return res.status(403).json({ message: '无权访问此疾病信息' });
    }
    
        console.log(`[DEBUG] 服务上下文: 服务用户有授权访问此患者的疾病`);
      }
    }
    // 非服务用户在服务上下文中尝试访问
    else {
      console.log(`[DEBUG] 用户角色 ${userRole} 无权访问服务上下文`);
      return res.status(403).json({ message: '无权访问服务管理功能' });
    }
    
    // 检查疾病的隐私设置 - 只有拥有者和管理员可以看到私有疾病
    if (diseaseData.isPrivate && !isDirectOwner && !isAdmin) {
      console.log(`[DEBUG] 疾病 ${id} 是私有的，只有拥有者和管理员可以访问`);
      return res.status(403).json({ message: '该疾病记录为私有，无权访问' });
    }
    
    // 返回数据
    res.json(diseaseData);
  } catch (error) {
    console.error('获取疾病详情失败:', error);
    res.status(500).json({ message: '获取疾病详情失败', error: error.message });
  }
});

/**
 * 创建新疾病记录
 * POST /diseases
 */
router.post('/', auth, async (req, res) => {
  try {
    const { patient_id, name, diagnosis_date, stage, description, treatment, service_context: bodyServiceContext, authorization_id: bodyAuthId } = req.body;
    const userId = req.userId || req.user_id || req.user.id;
    const userRole = req.user.role;
    const { service_context: queryServiceContext, authorization_id: queryAuthId } = req.query;
    
    // 同时检查请求体和URL参数中的服务上下文和授权ID
    const service_context = bodyServiceContext || queryServiceContext;
    const authorization_id = bodyAuthId || queryAuthId;
    
    console.log(`[DEBUG] 创建疾病记录请求: 患者ID=${patient_id}, 用户ID=${userId}, 用户角色=${userRole}, 服务上下文=${service_context}, 授权ID=${authorization_id}`);
    
    // 判断是否处于服务上下文
    const isServiceContext = service_context === 'true' || service_context === true;
    
    // 验证患者是否存在
    const patient = await Patient.query()
      .where({ id: patient_id })
      .first();
    
    if (!patient) {
      console.log(`[DEBUG] 患者不存在: ID=${patient_id}`);
      return res.status(404).json({ message: '患者不存在' });
    }
    
    // 获取患者所有者ID
    const patientUserId = patient.userId;
    
    // 权限检查
    let userHasPermission = false;
    let authorizationRecord = null;
    
    // 1. 管理员有权限
    if (userRole === 'ADMIN') {
      console.log(`[DEBUG] 用户是管理员，有权限创建疾病记录`);
      userHasPermission = true;
    }
    // 2. 患者所有者有权限
    else if (patientUserId === userId) {
      console.log(`[DEBUG] 用户是患者的所有者，有权限创建疾病记录`);
      userHasPermission = true;
    }
    // 3. 服务用户 - 检查授权关系
    else if (userRole === 'SERVICE' && isServiceContext) {
      console.log(`[DEBUG] 服务用户尝试创建疾病记录，检查授权关系`);
      
      // 如果提供了授权ID，直接查询这个授权
      if (authorization_id) {
        console.log(`[DEBUG] 使用指定的授权ID: ${authorization_id}`);
        
        authorizationRecord = await knex('user_authorizations')
          .where({
            'id': authorization_id,
            'authorized_id': userId,
            'status': 'ACTIVE'
          })
          .first();
        
        if (authorizationRecord) {
          // 如果授权记录指定了患者ID，检查与请求的患者ID是否匹配
          if (authorizationRecord.patient_id && authorizationRecord.patient_id !== patient_id) {
            console.log(`[DEBUG] 授权记录的患者ID(${authorizationRecord.patient_id})与请求的患者ID(${patient_id})不匹配`);
            return res.status(403).json({ 
              message: '无权为此患者添加疾病记录',
              details: '授权记录与请求的患者不匹配'
            });
          }
          
          // 如果授权记录未指定患者ID，检查授权人是否是患者所有者
          if (!authorizationRecord.patient_id && authorizationRecord.authorizer_id !== patientUserId) {
            console.log(`[DEBUG] 全局授权的授权人ID(${authorizationRecord.authorizer_id})与患者所有者ID(${patientUserId})不匹配`);
            return res.status(403).json({ 
              message: '无权为此患者添加疾病记录',
              details: '全局授权与患者所有者不匹配'
            });
          }
          
          console.log(`[DEBUG] 授权验证通过，授权ID: ${authorization_id}`);
          userHasPermission = true;
        } else {
          console.log(`[DEBUG] 未找到有效的授权记录，授权ID: ${authorization_id}`);
        }
      } 
      // 如果没有提供授权ID，查找所有合适的授权
      else {
        console.log(`[DEBUG] 没有指定授权ID，查找所有合适的授权`);
        
        // 检查授权关系
        const authorizationExists = await knex('user_authorizations')
          .where({
            'authorized_id': userId,
            'status': 'ACTIVE'
          })
          .where(function() {
            // 1. 检查是否有对特定患者的授权
            this.where('patient_id', patient_id)
            // 2. 或者检查是否有对该患者所有者的全局授权(patient_id为null)
            .orWhere(function() {
              this.where('patient_id', null)
                  .andWhere('authorizer_id', patientUserId);
            });
          })
          .first();
        
        if (authorizationExists) {
          console.log(`[DEBUG] 服务用户有授权操作患者 ${patient_id} 的疾病记录`);
          userHasPermission = true;
          authorizationRecord = authorizationExists;
        } else {
          console.log(`[DEBUG] 服务用户无权操作患者 ${patient_id} 的疾病记录`);
        }
      }
    }
    
    if (!userHasPermission) {
      console.log(`[DEBUG] 无权为患者 ${patient_id} 创建疾病记录`);
      return res.status(403).json({ message: '无权为此患者添加疾病记录' });
    }
    
    // 创建新疾病记录
    const newDisease = await Disease.query().insert({
      patient_id: patient_id,
      user_id: userId, // 记录创建疾病记录的用户ID
      name: name,
      diagnosis_date: diagnosis_date,
      stage: stage || 'INITIAL',
      description: description || '',
      treatment: treatment || '',
      status: 'ACTIVE',
      is_deleted: false,
      is_private: false,
      is_active: true,
      is_chronic: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    });
    
    // 如果是服务上下文且有授权记录，记录授权关系
    if (isServiceContext && authorizationRecord) {
      try {
        // 记录授权ID到疾病的元数据中
        await knex('disease_metadata').insert({
          disease_id: newDisease.id,
          key: 'authorization_id',
          value: authorizationRecord.id,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
        
        console.log(`[DEBUG] 已记录授权关系到疾病元数据：疾病ID=${newDisease.id}, 授权ID=${authorizationRecord.id}`);
      } catch (error) {
        console.error(`[DEBUG] 记录授权关系失败:`, error);
        // 不影响主流程，仅记录错误
      }
    }
    
    console.log(`[DEBUG] 成功创建疾病记录: ID=${newDisease.id}`);
    res.status(201).json(newDisease);
  } catch (error) {
    console.error('创建疾病记录失败:', error);
    res.status(500).json({ message: '创建疾病记录失败', error: error.message });
  }
});

/**
 * 更新疾病记录
 * PUT /diseases/:id
 */
router.put('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, stage, diagnosis_date, description, treatment, patient_id, is_private, service_context } = req.body;
    
    // 判断是否处于服务上下文
    const isServiceContext = service_context === 'true' || service_context === true;
    
    // 检查疾病记录是否存在
    const disease = await Disease.query()
      .where({ id, is_deleted: false })
      .withGraphFetched('patient')
      .first();
    
    if (!disease) {
      return res.status(404).json({ message: '疾病记录不存在' });
    }
    
    // 使用正确的驼峰命名属性
    const diseaseUserId = disease.userId;
    const patientUserId = disease.patient ? disease.patient.userId : null;
    const patientId = disease.patient ? disease.patient.id : null;
    const userId = req.userId || req.user_id;
    const userRole = req.user ? req.user.role : 'unknown';
    
    // 输出调试信息
    console.log('更新疾病记录，权限验证:', {
      diseaseId: disease.id,
      diseaseUserId: diseaseUserId,
      patientId: patientId,
      patientUserId: patientUserId,
      reqUserId: userId,
      reqUserRole: userRole,
      isServiceContext: isServiceContext,
      diseaseKeys: Object.keys(disease),
      patientKeys: disease.patient ? Object.keys(disease.patient) : []
    });
    
    // 初始化权限标志
    let userHasPermission = false;
    
    // 1. 如果用户是管理员
    if (userRole === 'ADMIN') {
      console.log(`[DEBUG] 用户是管理员，有权限更新疾病记录`);
      userHasPermission = true;
    }
    // 2. 或用户是疾病记录的创建者
    else if (diseaseUserId && (diseaseUserId === userId)) {
      console.log(`[DEBUG] 用户是疾病记录的创建者，有权限更新疾病记录`);
      userHasPermission = true;
    }
    // 3. 或用户是患者的所有者
    else if (patientUserId && (patientUserId === userId)) {
      console.log(`[DEBUG] 用户是患者的所有者，有权限更新疾病记录`);
      userHasPermission = true;
    }
    // 4. 如果是服务用户，且处于服务上下文，检查授权关系
    else if (userRole === 'SERVICE' && isServiceContext && patientId) {
      console.log(`[DEBUG] 服务用户尝试更新疾病记录，检查授权关系`);
      
      // 检查授权关系
      const authorizationExists = await knex('user_authorizations')
        .where({
          'authorized_id': userId,
          'status': 'ACTIVE'
        })
        .where(function() {
          // 1. 检查是否有对特定患者的授权
          this.where('patient_id', patientId)
          // 2. 或者检查是否有对患者所有者的全局授权(patient_id为null)
          .orWhere(function() {
            this.where('patient_id', null)
                .andWhere('authorizer_id', patientUserId);
          });
        })
        .first();
      
      if (authorizationExists) {
        console.log(`[DEBUG] 服务用户有授权操作患者 ${patientId} 的疾病记录`);
        userHasPermission = true;
      } else {
        console.log(`[DEBUG] 服务用户无权操作患者 ${patientId} 的疾病记录`);
      }
    }
    
    if (!userHasPermission) {
      console.log('更新疾病记录权限验证失败:', {
        diseaseUserId: diseaseUserId,
        patientUserId: patientUserId,
        patientId: patientId,
        reqUserId: userId,
        reqUserRole: userRole,
        isServiceContext: isServiceContext
      });
      return res.status(403).json({ message: '无权更新此疾病记录' });
    }
    
    // 更新疾病记录
    // 注意：数据库字段名使用下划线命名法
    const updateData = {
      updated_at: new Date().toISOString()
    };
    
    // 只更新提供的字段
    if (name !== undefined) updateData.name = name;
    if (diagnosis_date !== undefined) updateData.diagnosis_date = diagnosis_date;
    if (stage !== undefined) updateData.stage = stage;
    if (description !== undefined) updateData.description = description;
    if (treatment !== undefined) updateData.treatment = treatment;
    if (patient_id !== undefined) updateData.patient_id = patient_id;
    if (is_private !== undefined) updateData.is_private = is_private;
    
    // 将用户ID记录到数据库中（如果尚未设置）
    if (!diseaseUserId) {
      console.log('更新疾病记录的用户ID:', userId);
      updateData.user_id = userId;
    }
    
    console.log('准备更新数据:', updateData);
    
    const updatedDiseaseData = await Disease.query()
      .where({ id, is_deleted: false })
      .patch(updateData)
      .returning('*')
      .first();
    
    // 转换为驼峰命名格式
    const updatedDisease = {
      id: updatedDiseaseData.id,
      patientId: updatedDiseaseData.patient_id,
      userId: updatedDiseaseData.user_id,
      name: updatedDiseaseData.name,
      diagnosisDate: updatedDiseaseData.diagnosis_date,
      stage: updatedDiseaseData.stage,
      description: updatedDiseaseData.description,
      treatment: updatedDiseaseData.treatment,
      status: updatedDiseaseData.status,
      isDeleted: updatedDiseaseData.is_deleted,
      isPrivate: updatedDiseaseData.is_private,
      isActive: updatedDiseaseData.is_active,
      isChronic: updatedDiseaseData.is_chronic,
      createdAt: updatedDiseaseData.created_at,
      updatedAt: updatedDiseaseData.updated_at
    };
    
    console.log('疾病记录更新成功');
    res.json(updatedDisease);
  } catch (error) {
    console.error('更新疾病记录失败:', error);
    res.status(500).json({ message: '更新疾病记录失败', error: error.message });
  }
});

/**
 * 删除疾病记录（软删除）
 * DELETE /diseases/:id
 */
router.delete('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const { service_context } = req.query;
    
    // 判断是否处于服务上下文
    const isServiceContext = service_context === 'true' || service_context === true;
    
    // 检查疾病记录是否存在
    const disease = await Disease.query()
      .where({ id, is_deleted: false })
      .withGraphFetched('patient')
      .first();
    
    if (!disease) {
      return res.status(404).json({ message: '疾病记录不存在' });
    }
    
    // 使用正确的驼峰命名属性
    const diseaseUserId = disease.userId;
    const patientUserId = disease.patient ? disease.patient.userId : null;
    const patientId = disease.patient ? disease.patient.id : null;
    const userId = req.userId || req.user_id;
    const userRole = req.user ? req.user.role : 'unknown';
    
    // 输出调试信息
    console.log('删除疾病记录，权限验证:', {
      diseaseId: disease.id,
      diseaseUserId: diseaseUserId,
      patientId: patientId,
      patientUserId: patientUserId,
      reqUserId: userId,
      reqUserRole: userRole,
      isServiceContext: isServiceContext,
      diseaseKeys: Object.keys(disease),
      patientKeys: disease.patient ? Object.keys(disease.patient) : []
    });
    
    // 初始化权限标志
    let userHasPermission = false;
    
    // 1. 如果用户是管理员
    if (userRole === 'ADMIN') {
      console.log(`[DEBUG] 用户是管理员，有权限删除疾病记录`);
      userHasPermission = true;
    }
    // 2. 或用户是疾病记录的创建者
    else if (diseaseUserId && (diseaseUserId === userId)) {
      console.log(`[DEBUG] 用户是疾病记录的创建者，有权限删除疾病记录`);
      userHasPermission = true;
    }
    // 3. 或用户是患者的所有者
    else if (patientUserId && (patientUserId === userId)) {
      console.log(`[DEBUG] 用户是患者的所有者，有权限删除疾病记录`);
      userHasPermission = true;
    }
    // 4. 如果是服务用户，且处于服务上下文，检查授权关系
    else if (userRole === 'SERVICE' && isServiceContext && patientId) {
      console.log(`[DEBUG] 服务用户尝试删除疾病记录，检查授权关系`);
      
      // 检查授权关系
      const authorizationExists = await knex('user_authorizations')
        .where({
          'authorized_id': userId,
          'status': 'ACTIVE'
        })
        .where(function() {
          // 1. 检查是否有对特定患者的授权
          this.where('patient_id', patientId)
          // 2. 或者检查是否有对患者所有者的全局授权(patient_id为null)
          .orWhere(function() {
            this.where('patient_id', null)
                .andWhere('authorizer_id', patientUserId);
          });
        })
        .first();
      
      if (authorizationExists) {
        console.log(`[DEBUG] 服务用户有授权操作患者 ${patientId} 的疾病记录`);
        userHasPermission = true;
      } else {
        console.log(`[DEBUG] 服务用户无权操作患者 ${patientId} 的疾病记录`);
      }
    }
    
    if (!userHasPermission) {
      console.log('删除疾病记录权限验证失败:', {
        diseaseUserId: diseaseUserId,
        patientUserId: patientUserId,
        patientId: patientId,
        reqUserId: userId,
        reqUserRole: userRole,
        isServiceContext: isServiceContext
      });
      return res.status(403).json({ message: '无权删除此疾病记录' });
    }
    
    // 开始事务处理
    await knex.transaction(async (trx) => {
      // 1. 软删除关联的记录 - 把与该病理相关的所有记录标记为已删除
      const currentTime = new Date().toISOString();
      const relatedRecords = await knex('records')
        .where({ disease_id: id })
        .where('is_deleted', false)
        .transacting(trx);
      
      if (relatedRecords.length > 0) {
        console.log(`找到 ${relatedRecords.length} 条与病理ID ${id} 关联的记录，进行级联软删除...`);
        
        await knex('records')
          .where({ disease_id: id })
          .where('is_deleted', false)
          .update({ 
            is_deleted: true,
            updated_at: currentTime 
          })
          .transacting(trx);
          
        console.log(`成功软删除 ${relatedRecords.length} 条关联记录`);
      } else {
        console.log(`未找到与病理ID ${id} 关联的记录`);
      }
      
      // 2. 软删除病理记录本身
      await Disease.query()
        .where({ id })
        .patch({
          is_deleted: true,
          updated_at: currentTime
        })
        .transacting(trx);
        
      console.log(`病理记录 ${id} 已软删除`);
    });
    
    res.json({ 
      message: '疾病记录已删除',
      meta: {
        relatedRecordsDeleted: true
      }
    });
  } catch (error) {
    console.error('删除疾病记录失败:', error);
    res.status(500).json({ message: '删除疾病记录失败', error: error.message });
  }
});

/**
 * 获取疾病阶段列表（用于下拉选择）
 * GET /diseases/stages
 */
router.get('/stages', auth, async (req, res) => {
  try {
    // 获取系统中使用的所有疾病阶段（去重）
    const stages = await Disease.query()
      .where('is_deleted', false)
      .distinct('stage')
      .orderBy('stage');
    
    // 提取阶段值
    const stageValues = stages.map(s => s.stage);
    
    res.json(stageValues);
  } catch (error) {
    console.error('获取疾病阶段列表失败:', error);
    res.status(500).json({ message: '获取疾病阶段列表失败', error: error.message });
  }
});

/**
 * 获取指定患者的所有疾病
 * GET /diseases/by-patient/:patient_id
 */
router.get('/by-patient/:patient_id', auth, async (req, res) => {
  try {
    const { patient_id } = req.params;
    const userId = req.userId || req.user_id || req.user.id;
    const userRole = req.user.role;
    
    console.log(`[DEBUG] 获取患者疾病列表请求: 患者ID=${patient_id}, 用户ID=${userId}, 用户角色=${userRole}`);
    
    // 验证患者是否存在
    const patient = await Patient.query()
      .where({ id: patient_id })
      .first();
    
    if (!patient) {
      console.log(`[DEBUG] 患者不存在: ID=${patient_id}`);
      return res.status(404).json({ message: '患者不存在' });
    }
    
    // 获取患者所有者ID
    const patientUserId = patient.userId;
    
    // 权限检查
    let userHasPermission = false;
    
    // 1. 管理员有权限
    if (userRole === 'ADMIN') {
      console.log(`[DEBUG] 用户是管理员，有权限访问患者疾病列表`);
      userHasPermission = true;
    }
    // 2. 患者所有者有权限
    else if (patientUserId === userId) {
      console.log(`[DEBUG] 用户是患者的所有者，有权限访问疾病列表`);
      userHasPermission = true;
    }
    // 3. 服务用户 - 检查授权关系
    else if (userRole === 'SERVICE') {
      console.log(`[DEBUG] 服务用户尝试访问患者疾病列表，检查授权关系`);
      
      // 检查授权关系
      const authorizationExists = await knex('user_authorizations')
        .where({
          'authorized_id': userId,
          'status': 'ACTIVE'
        })
        .where(function() {
          // 1. 检查是否有对特定患者的授权
          this.where('patient_id', patient_id)
          // 2. 或者检查是否有对该患者所有者的全局授权(patient_id为null)
          .orWhere(function() {
            this.where('patient_id', null)
                .andWhere('authorizer_id', patientUserId);
          });
        })
        .first();
      
      if (authorizationExists) {
        console.log(`[DEBUG] 服务用户有授权访问患者 ${patient_id} 的疾病列表`);
        userHasPermission = true;
      } else {
        console.log(`[DEBUG] 服务用户无权访问患者 ${patient_id} 的疾病列表`);
      }
    }
    
    if (!userHasPermission) {
      console.log(`[DEBUG] 无权访问患者 ${patient_id} 的疾病列表`);
      return res.status(403).json({ message: '无权访问此患者信息' });
    }
    
    // 获取患者的所有疾病记录
    const diseases = await Disease.query()
      .where({ 
        patient_id, 
        is_deleted: false 
      })
      .orderBy('diagnosis_date', 'desc');
    
    console.log(`[DEBUG] 获取到患者 ${patient_id} 的 ${diseases.length} 条疾病记录`);
    
    // 直接返回Objection.js模型对象，它已经是驼峰命名格式了
    res.json(diseases);
  } catch (error) {
    console.error('获取患者疾病列表失败:', error);
    res.status(500).json({ message: '获取患者疾病列表失败', error: error.message });
  }
});

/**
 * 数据修复函数：确保所有疾病记录都有user_id
 * 通过患者关联，设置疾病记录的user_id
 */
async function fixDiseasesWithoutUserId() {
  try {
    const diseasesWithoutUserId = await Disease.query()
      .whereNull('user_id')
      .withGraphFetched('patient');

    if (diseasesWithoutUserId.length === 0) {
      console.log('没有需要修复的疾病记录');
      return;
    }

    console.log(`找到 ${diseasesWithoutUserId.length} 条没有user_id的疾病记录，开始修复...`);

    for (const disease of diseasesWithoutUserId) {
      // 使用正确的驼峰命名属性访问
      if (disease.patient && disease.patient.userId) {
        // 使用患者的user_id更新疾病记录
        await Disease.query()
          .where('id', disease.id)
          .patch({ user_id: disease.patient.userId });
        
        console.log(`已修复疾病记录 ${disease.id}，设置user_id为 ${disease.patient.userId}`);
      } else {
        console.log(`无法修复疾病记录 ${disease.id}，没有找到关联的患者或患者没有userId`);
      }
    }

    console.log('疾病记录修复完成');
  } catch (error) {
    console.error('修复疾病记录失败:', error);
  }
}

// 在模块导出前调用修复函数
fixDiseasesWithoutUserId();

module.exports = router; 