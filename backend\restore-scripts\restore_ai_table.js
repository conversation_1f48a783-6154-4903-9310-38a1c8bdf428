/**
 * 数据库表结构恢复脚本
 * 恢复旧版本的ai_reports表，保持功能完整
 */
const knex = require('../config/database');
const path = require('path');
const fs = require('fs');

console.log('===== 开始恢复AI报告表结构 =====');

// 获取日期时间戳，用于备份文件名
function getTimestamp() {
  const now = new Date();
  return now.getFullYear() +
    ('0' + (now.getMonth() + 1)).slice(-2) +
    ('0' + now.getDate()).slice(-2) +
    ('0' + now.getHours()).slice(-2) +
    ('0' + now.getMinutes()).slice(-2) +
    ('0' + now.getSeconds()).slice(-2);
}

// 备份数据库
async function backupDatabase() {
  console.log('正在备份当前数据库...');
  
  try {
    // 获取时间戳
    const timestamp = getTimestamp();
    const backupFileName = `db_backup_${timestamp}.sqlite`;
    const backupPath = path.join(__dirname, '..', backupFileName);
    
    // 复制当前数据库文件
    fs.copyFileSync(
      path.join(__dirname, '..', 'db.sqlite'),
      backupPath
    );
    
    console.log(`数据库已备份到: ${backupPath}`);
    return true;
  } catch (error) {
    console.error('备份数据库失败:', error);
    return false;
  }
}

// 检查并备份当前ai_reports表
async function backupCurrentTable() {
  console.log('正在检查当前AI报告表...');
  
  try {
    const hasTable = await knex.schema.hasTable('ai_reports');
    
    if (hasTable) {
      console.log('找到当前ai_reports表，准备备份...');
      
      const timestamp = getTimestamp();
      const backupTableName = `ai_reports_backup_${timestamp}`;
      
      // 检查备份表是否已存在
      const hasBackupTable = await knex.schema.hasTable(backupTableName);
      if (hasBackupTable) {
        console.log(`备份表${backupTableName}已存在，将被跳过`);
        return;
      }
      
      // 创建备份表
      await knex.schema.raw(`CREATE TABLE ${backupTableName} AS SELECT * FROM ai_reports`);
      console.log(`当前ai_reports表已备份为${backupTableName}`);
      
      // 获取当前表的记录数
      const count = await knex('ai_reports').count('* as count');
      console.log(`ai_reports表包含${count[0].count}条记录`);
      
      return count[0].count;
    } else {
      console.log('未找到当前ai_reports表，将直接创建新表');
      return 0;
    }
  } catch (error) {
    console.error('备份当前表失败:', error);
    return null;
  }
}

// 重新创建ai_reports表
async function recreateTable() {
  console.log('正在重新创建ai_reports表...');
  
  try {
    // 检查表是否存在
    const hasTable = await knex.schema.hasTable('ai_reports');
    
    if (hasTable) {
      // 删除当前表
      await knex.schema.dropTable('ai_reports');
      console.log('已删除当前ai_reports表');
    }
    
    // 创建新表，使用旧版本的表结构
    await knex.schema.createTable('ai_reports', table => {
      table.uuid('id').primary();
      table.uuid('disease_id').notNullable()
        .comment('关联的病历ID');
      table.uuid('patient_id').notNullable()
        .comment('关联的患者ID');
      table.uuid('user_id').notNullable()
        .comment('创建者ID');
      table.uuid('record_id').nullable()
        .comment('关联的记录ID（AI分析可能会生成一条记录）');
      table.string('title').notNullable()
        .comment('报告标题');
      table.enu('template_type', ['COMPREHENSIVE_ANALYSIS'], { useNative: true, enumName: 'ai_report_template_type' })
        .defaultTo('COMPREHENSIVE_ANALYSIS').notNullable()
        .comment('报告模板类型');
      table.json('anonymized_info').nullable()
        .comment('匿名化后的信息，包含原始姓名和匿名化后的姓名');
      table.text('llm_raw_response', 'longtext').nullable()
        .comment('LLM原始响应内容（JSON格式）');
      table.json('content').notNullable()
        .comment('分析报告内容（结构化JSON）');
      table.string('pdf_url').nullable()
        .comment('PDF报告的存储路径');
      table.enu('status', ['PROCESSING', 'COMPLETED', 'FAILED'], { useNative: true, enumName: 'ai_report_status' })
        .defaultTo('PROCESSING').notNullable()
        .comment('报告状态');
      table.text('error_message').nullable()
        .comment('错误信息');
      table.timestamps(true, true);
      
      // 索引
      table.index(['disease_id'], 'ai_report_disease_idx');
      table.index(['patient_id'], 'ai_report_patient_idx');
      table.index(['user_id'], 'ai_report_user_idx');
    });
    
    console.log('已成功创建ai_reports表');
    return true;
  } catch (error) {
    console.error('创建表失败:', error);
    return false;
  }
}

// 迁移数据到新表
async function migrateData(recordCount) {
  if (!recordCount || recordCount <= 0) {
    console.log('没有数据需要迁移');
    return true;
  }
  
  console.log(`开始迁移${recordCount}条记录到新表...`);
  
  try {
    // 获取最新的备份表
    // PostgreSQL兼容查询：从pg_catalog.pg_tables获取表信息
      const tables = await knex.raw(`SELECT tablename AS name FROM pg_catalog.pg_tables WHERE schemaname NOT IN ('pg_catalog', 'information_schema') AND tablename LIKE 'ai_reports_backup_%' ORDER BY tablename DESC LIMIT 1`);
    
    if (!tables || !tables.length || !tables[0]) {
      console.warn('未找到备份表，无法迁移数据');
      return false;
    }
    
    const backupTableName = tables[0].name;
    console.log(`使用备份表: ${backupTableName}`);
    
    // 分批迁移数据，减少内存使用
    const batchSize = 50;
    const totalBatches = Math.ceil(recordCount / batchSize);
    
    for (let batch = 0; batch < totalBatches; batch++) {
      const offset = batch * batchSize;
      
      console.log(`正在迁移批次 ${batch + 1}/${totalBatches}...`);
      
      // 获取当前批次的数据
      const records = await knex.raw(`SELECT * FROM ${backupTableName} LIMIT ${batchSize} OFFSET ${offset}`);
      
      if (!records || !records.length) {
        console.log(`批次 ${batch + 1} 没有数据，跳过`);
        continue;
      }
      
      // 准备迁移数据
      const dataToInsert = records.map(record => {
        // 从旧表结构转换到新表结构
        // 注意：新字段会被忽略，不需要迁移
        return {
          id: record.id,
          disease_id: record.disease_id || record.diseaseId,
          patient_id: record.patient_id || record.patientId,
          user_id: record.user_id || record.userId,
          record_id: record.record_id || record.recordId,
          title: record.title,
          template_type: record.template_type || record.templateType || 'COMPREHENSIVE_ANALYSIS',
          anonymized_info: record.anonymized_info || record.anonymizedInfo,
          llm_raw_response: typeof record.llm_raw_response === 'object' ? 
            JSON.stringify(record.llm_raw_response) : 
            (record.llm_raw_response || record.llmRawResponse),
          content: record.content,
          pdf_url: record.pdf_url || record.pdfUrl,
          status: record.status,
          error_message: record.error_message || record.errorMessage,
          created_at: record.created_at || record.createdAt || new Date(),
          updated_at: record.updated_at || record.updatedAt || new Date()
        };
      });
      
      // 插入数据到新表
      await knex('ai_reports').insert(dataToInsert);
      
      console.log(`批次 ${batch + 1} 迁移完成`);
    }
    
    console.log('所有数据已成功迁移到新表');
    return true;
  } catch (error) {
    console.error('迁移数据失败:', error);
    return false;
  }
}

// 检查迁移结果
async function verifyMigration(originalCount) {
  try {
    const newCount = await knex('ai_reports').count('* as count');
    
    console.log(`原始记录数: ${originalCount}`);
    console.log(`新表记录数: ${newCount[0].count}`);
    
    if (originalCount == newCount[0].count) {
      console.log('✅ 数据迁移验证通过！记录数匹配。');
      return true;
    } else {
      console.warn('⚠️ 数据迁移验证失败！记录数不匹配。');
      return false;
    }
  } catch (error) {
    console.error('验证迁移结果失败:', error);
    return false;
  }
}

// 主函数
async function main() {
  try {
    // 1. 备份数据库
    const dbBackupSuccess = await backupDatabase();
    if (!dbBackupSuccess) {
      console.warn('数据库备份失败，但将继续执行后续步骤');
    }
    
    // 2. 备份当前表
    const recordCount = await backupCurrentTable();
    
    // 3. 重建表结构
    const tableCreated = await recreateTable();
    if (!tableCreated) {
      throw new Error('重建表结构失败，恢复操作中止');
    }
    
    // 4. 迁移数据
    if (recordCount > 0) {
      const migrationSuccess = await migrateData(recordCount);
      if (!migrationSuccess) {
        console.warn('数据迁移过程中发生错误，部分数据可能未迁移成功');
      }
      
      // 5. 验证迁移结果
      await verifyMigration(recordCount);
    }
    
    console.log('===== AI报告表结构恢复完成 =====');
    return true;
  } catch (error) {
    console.error('恢复表结构失败:', error);
    return false;
  }
}

// 执行恢复过程
main()
  .then(success => {
    if (success) {
      console.log('AI报告表恢复成功！');
    } else {
      console.error('AI报告表恢复失败！');
    }
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('执行恢复脚本时发生错误:', error);
    process.exit(1);
  });