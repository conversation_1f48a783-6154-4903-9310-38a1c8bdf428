import apiClient from './apiClient';
import { logApiError } from '../utils/apiErrorMonitor';
import dataCache from '../utils/dataCache';

// 记录数据接口
export interface RecordData {
  title: string;
  content: string;
  recordType?: string;
  primaryType?: string;
  recordDate?: string;
  tags?: string[];
  patientId: string;
  diseaseId?: string;
  severity?: number;
  stageNode?: any;
  status?: string;
}

/**
 * 是否启用记录缓存
 * 不要缓存会频繁变更的数据
 */
const ENABLE_RECORD_CACHE = true;

/**
 * 记录缓存时间（毫秒）
 * 缓存过期时间应根据数据更新频率调整
 */
const RECORD_CACHE_EXPIRY = 2 * 60 * 1000; // 2分钟

// 创建记录
export const createRecord = async (recordData: RecordData) => {
  try {
    console.log('[recordService] 创建新记录');

    const response = await apiClient.post('/api/records', recordData);

    // 添加或更新记录后，清除相关缓存
    if (recordData.diseaseId) {
      dataCache.clearByPrefix(`records:${recordData.diseaseId}`);
    }

    return response.data;
  } catch (error) {
    console.error('[recordService] 创建记录失败:', error);
    logApiError(error);
    throw error;
  }
};

// 获取所有记录
export const getRecords = async (filters?: any, retryCount = 0): Promise<any> => {
  try {
    console.log('[recordService] 获取记录列表，过滤条件:', filters);

    // 构造缓存键
    const diseaseId = filters?.diseaseId;

    // 如果启用缓存且有diseaseId，尝试从缓存获取
    if (ENABLE_RECORD_CACHE && diseaseId) {
      const cacheKey = `records:${diseaseId}`;

      const cachedData = dataCache.get(cacheKey, filters);
      if (cachedData) {
        console.log(`[recordService] 使用缓存数据，diseaseId: ${diseaseId}`);
        return cachedData;
      }
    }

    // 简化请求参数，避免使用可能导致SQL错误的复杂参数
    const safeParams = { ...filters };

    // 移除可能导致后端SQL错误的where_clause参数
    if (safeParams.where_clause) {
      console.log('[recordService] 警告: 删除不安全的where_clause参数');
      delete safeParams.where_clause;
    }

    const response = await apiClient.get('/api/records', { params: safeParams });
    // 检查响应数据是否是数组，并安全地获取长度
    const records = Array.isArray(response.data) ? response.data :
                   (response.data && response.data.records ? response.data.records : []);
    console.log(`[recordService] 获取到${records.length}条记录`);

    // 缓存结果
    if (ENABLE_RECORD_CACHE && diseaseId) {
      dataCache.set(`records:${diseaseId}`, response.data, filters, {
        expiry: RECORD_CACHE_EXPIRY
      });
    }

    return response.data;
  } catch (error: any) {
    console.error('[recordService] 获取记录失败:', error);
    logApiError(error);

    // 实现重试机制，最多重试2次
    if (retryCount < 2) {
      console.log(`[recordService] 第${retryCount + 1}次重试获取记录...`);
      // 延迟重试，避免立即重试导致相同错误
      await new Promise(resolve => setTimeout(resolve, 500 * (retryCount + 1)));

      // 简化过滤器参数后重试
      const retryFilters = { ...filters };
      delete retryFilters.where_clause; // 删除可能导致问题的参数

      if (retryFilters.order_by && typeof retryFilters.order_by === 'string' &&
          retryFilters.order_by.includes('DESC')) {
        // 简化排序参数
        retryFilters.order_by = 'created_at DESC';
      }

      return getRecords(retryFilters, retryCount + 1);
    }

    // 所有重试失败后，返回空数据而不是抛出错误
    console.error('[recordService] 所有重试都失败，返回空数据');
    return { records: [] };
  }
};

// 获取单个记录详情
export const getRecord = async (id: string, options?: {
  include_all_users?: boolean,
  include_deleted?: boolean,
  service_context?: boolean
}) => {
  try {
    console.log(`[recordService] 获取记录详情，ID: ${id}, 选项:`, options);

    // 检查当前操作模式
    const operationMode = localStorage.getItem('operation_mode') || 'NORMAL';
    const isServiceContext = operationMode === 'SERVICE' || operationMode === 'ADMIN';

    // 检查缓存
    if (ENABLE_RECORD_CACHE) {
      const cacheKey = `record:${id}`;
      const cachedData = dataCache.get(cacheKey, options);
      if (cachedData) {
        console.log(`[recordService] 使用缓存的记录详情，ID: ${id}`);
        return cachedData;
      }
    }

    // 准备请求参数，添加服务上下文和允许已删除记录的标志
    const params = {
      include_all_users: options?.include_all_users,
      // 如果明确要求包含已删除记录，或者是服务上下文，则添加include_deleted参数
      include_deleted: options?.include_deleted || isServiceContext,
      // 添加服务上下文标记
      service_context: options?.service_context || isServiceContext
    };

    console.log(`[recordService] 发送请求参数:`, params);

    const response = await apiClient.get(`/api/records/${id}`, { params });

    // 缓存结果
    if (ENABLE_RECORD_CACHE) {
      dataCache.set(`record:${id}`, response.data, options, {
        expiry: RECORD_CACHE_EXPIRY
      });
    }

    return response.data;
  } catch (error) {
    console.error('[recordService] 获取记录详情失败:', error);
    logApiError(error);
    throw error;
  }
};

// 更新记录
export const updateRecord = async (id: string, recordData: Partial<RecordData>) => {
  try {
    console.log(`[recordService] 更新记录，ID: ${id}`);

    const response = await apiClient.put(`/api/records/${id}`, recordData);

    // 更新记录后，清除相关缓存
    dataCache.delete(`record:${id}`);
    if (recordData.diseaseId) {
      dataCache.clearByPrefix(`records:${recordData.diseaseId}`);
    }

    return response.data;
  } catch (error) {
    console.error('[recordService] 更新记录失败:', error);
    logApiError(error);
    throw error;
  }
};

// 删除记录
export const deleteRecord = async (id: string) => {
  try {
    console.log(`[recordService] 删除记录，ID: ${id}`);

    const response = await apiClient.delete(`/api/records/${id}`);

    // 删除记录后，清除相关缓存
    dataCache.delete(`record:${id}`);
    // 由于不知道diseaseId，清除所有记录相关缓存
    dataCache.clearByPrefix('records:');

    return response.data;
  } catch (error) {
    console.error('[recordService] 删除记录失败:', error);
    logApiError(error);
    throw error;
  }
};

// 上传附件
export const uploadAttachment = async (recordId: string, file: File, userLevelLimits: any) => {
  // 验证文件大小（KB转换为字节）
  if (file.size > userLevelLimits.maxAttachmentSize * 1024) {
    throw new Error(`附件大小超过限制 (${userLevelLimits.maxAttachmentSize}KB)`);
  }

  try {
    console.log(`[recordService] 上传附件，记录ID: ${recordId}, 文件名: ${file.name}`);

    // 生成带时间戳的文件名，防止同名文件
    const timestamp = new Date().getTime();
    const fileNameParts = file.name.split('.');
    const extension = fileNameParts.pop();
    const nameWithoutExtension = fileNameParts.join('.');
    const newFileName = `${nameWithoutExtension}_${timestamp}.${extension}`;

    const formData = new FormData();
    // 创建新的文件对象，保留类型但更改文件名
    const newFile = new File([file], newFileName, { type: file.type });
    formData.append('file', newFile);
    formData.append('recordId', recordId);

    const response = await apiClient.post('/api/records/attachments/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    // 清除缓存
    dataCache.delete(`record:${recordId}`);
    dataCache.delete(`attachments:${recordId}`);

    return response.data;
  } catch (error) {
    console.error('[recordService] 上传附件失败:', error);
    logApiError(error);
    throw error;
  }
};

// 获取记录的所有附件
export const getAttachments = async (recordId: string, options?: {
  include_all_users?: boolean,
  include_deleted?: boolean,
  service_context?: boolean
}) => {
  try {
    console.log(`[recordService] 获取记录附件，记录ID: ${recordId}`);

    // 检查当前操作模式
    const operationMode = localStorage.getItem('operation_mode') || 'NORMAL';
    const isServiceContext = operationMode === 'SERVICE' || operationMode === 'ADMIN';

    // 检查缓存
    if (ENABLE_RECORD_CACHE) {
      const cacheKey = `attachments:${recordId}`;
      const cachedData = dataCache.get(cacheKey, options);
      if (cachedData) {
        console.log(`[recordService] 使用缓存的附件列表，记录ID: ${recordId}`);
        return cachedData;
      }
    }

    // 准备请求参数，添加服务上下文和允许已删除记录的标志
    const params = {
      include_all_users: options?.include_all_users,
      include_deleted: options?.include_deleted || isServiceContext,
      service_context: options?.service_context || isServiceContext
    };

    console.log(`[recordService] 发送附件请求参数:`, params);

    const response = await apiClient.get(`/api/records/attachments/record/${recordId}`, { params });

    console.log(`[recordService] 获取到${response.data.length || 0}个附件`);

    // 缓存结果
    if (ENABLE_RECORD_CACHE) {
      dataCache.set(`attachments:${recordId}`, response.data, options, {
        expiry: RECORD_CACHE_EXPIRY
      });
    }

    return response.data;
  } catch (error) {
    console.error('[recordService] 获取附件失败:', error);
    logApiError(error);
    throw error;
  }
};

// 删除附件
export const deleteAttachment = async (attachmentId: string) => {
  try {
    console.log(`[recordService] 删除附件，附件ID: ${attachmentId}`);

    const response = await apiClient.delete(`/api/records/attachments/${attachmentId}`);

    // 由于不知道recordId，无法精确清除缓存
    // 清除所有附件相关缓存
    dataCache.clearByPrefix('attachments:');

    return response.data;
  } catch (error) {
    console.error('[recordService] 删除附件失败:', error);
    logApiError(error);
    throw error;
  }
};

// 导出记录服务对象
const recordService = {
  createRecord,
  getRecords,
  getRecord,
  updateRecord,
  deleteRecord,
  uploadAttachment,
  getAttachments,
  deleteAttachment
};

export default recordService;