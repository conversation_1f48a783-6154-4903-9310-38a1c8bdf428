const { v4: uuidv4 } = require('uuid');

exports.up = function (knex) {
  return knex.schema.createTable('patients', (table) => {
    table.uuid('id').primary().defaultTo(uuidv4());
    table.string('name').notNullable().index();
    table.string('gender').notNullable();
    table.string('phone_number');
    table.string('email');
    table.date('birth_date').notNullable();
    table.string('id_card').unique();
    table.string('medicare_card');
    table.string('medicare_location');
    table.string('address');
    table.string('emergency_contact_name');
    table.string('emergency_contact_phone');
    table.string('emergency_contact_relationship');
    table.string('past_medical_history');
    table.string('family_medical_history');
    table.string('allergy_history');
    table.string('blood_type').notNullable();
    table.date('last_visit_date').nullable();
    table.timestamp('deleted_at', { useTz: true }).index().nullable();
    table.uuid('user_id').notNullable().index();
    table.timestamp('created_at', { useTz: true }).notNullable();
    table.timestamp('updated_at', { useTz: true }).notNullable();
  });
};

exports.down = function (knex) {
  return knex.schema.dropTable('patients');
}; 