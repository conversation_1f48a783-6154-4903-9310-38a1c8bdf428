/**
 * 添加记录表和createdBy字段
 */
exports.up = function(knex) {
  return knex.schema
    // 1. 创建记录表（如果不存在）
    .createTable('records', function(table) {
      table.uuid('id').primary(); // 记录ID
      table.string('title').notNullable(); // 标题
      table.text('content').nullable(); // 内容
      table.json('data').nullable(); // 结构化数据（用于AI分析）
      table.timestamp('record_date', { useTz: true }).notNullable(); // 记录日期 (使用 TIMESTAMPTZ)
      table.string('description').nullable(); // 描述
      table.uuid('patient_id').notNullable(); // 患者ID
      table.uuid('disease_id').notNullable(); // 疾病ID
      table.uuid('user_id').notNullable(); // 所属用户ID
      table.uuid('created_by').notNullable(); // 创建者ID（新增字段）
      table.string('record_type').notNullable(); // 记录类型
      table.string('primary_type').notNullable(); // 主要类型
      table.string('type_tags_json').nullable(); // 类型标签（JSON格式）
      table.string('stage_tags').nullable(); // 阶段标签
      table.string('stage_node').nullable(); // 阶段节点
      table.string('stage_phase').nullable(); // 阶段
      table.string('severity').nullable(); // 严重程度
      table.boolean('is_private').notNullable().defaultTo(false); // 是否私有
      table.boolean('is_important').notNullable().defaultTo(false); // 是否重要
      table.string('custom_tags').nullable(); // 自定义标签
      table.timestamp('deleted_at', { useTz: true }).nullable(); // 软删除时间 (使用 TIMESTAMPTZ)
      table.timestamps(true, true); // 创建和更新时间
      
      // 外键约束
      table.foreign('patient_id').references('id').inTable('patients').onDelete('CASCADE');
      table.foreign('disease_id').references('id').inTable('diseases').onDelete('CASCADE');
      table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    })
    // 2. 创建索引
    .then(function() {
      return knex.schema.table('records', function(table) {
        table.index('title'); // 标题索引
        table.index('patient_id'); // 患者ID索引
        table.index('disease_id'); // 疾病ID索引
        table.index('user_id'); // 用户ID索引
        table.index('created_by'); // 创建者ID索引
        table.index('record_type'); // 记录类型索引
        table.index('primary_type'); // 主要类型索引
        table.index('stage_node'); // 阶段节点索引
        table.index('stage_phase'); // 阶段索引
        table.index('deleted_at'); // 软删除时间索引
      });
    });
};

exports.down = function(knex) {
  return knex.schema.dropTableIfExists('records');
};