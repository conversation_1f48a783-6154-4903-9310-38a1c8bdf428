const { Model, snakeCaseMappers } = require('objection');

class Tag extends Model {
  static get tableName() {
    return 'tags';
  }

  static get idColumn() {
    return 'id';
  }

  // 添加下划线命名映射配置
  static get columnNameMappers() {
    return snakeCaseMappers();
  }

  // 字段验证规则
  static get jsonSchema() {
    return {
      type: 'object',
      required: ['name'],
      properties: {
        id: { type: 'string' },
        name: { type: 'string', minLength: 1, maxLength: 50 },
        color: { type: 'string', maxLength: 20, default: '#2196F3' },
        description: { type: ['string', 'null'], maxLength: 500 },
        type: { type: 'string', enum: ['system', 'user', 'custom'], default: 'custom' },
        createdBy: { type: ['string', 'null'] },
        updatedAt: { type: ['string', 'null'] },
        createdAt: { type: ['string', 'null'] }
      }
    };
  }

  // 关系定义
  static get relationMappings() {
    const Record = require('./Record');
    const User = require('./User');

    return {
      // 记录关系（多对多）
      records: {
        relation: Model.ManyToManyRelation,
        modelClass: Record,
        join: {
          from: 'tags.id',
          through: {
            from: 'record_tags.tagId',
            to: 'record_tags.recordId'
          },
          to: 'records.id'
        }
      },

      // 创建者关系
      creator: {
        relation: Model.BelongsToOneRelation,
        modelClass: User,
        join: {
          from: 'tags.createdBy',
          to: 'users.id'
        }
      }
    };
  }

  // 查找或创建标签
  static async findOrCreate(name, data = {}) {
    const tag = await this.query().findOne({ name });
    
    if (tag) return tag;
    
    return this.query().insert({
      name,
      ...data,
      createdAt: new Date().toISOString()
    });
  }

  // 更新标签时间
  $beforeUpdate() {
    this.updatedAt = new Date().toISOString();
  }

  // 创建标签时设置时间
  $beforeInsert() {
    const now = new Date().toISOString();
    this.createdAt = now;
    this.updatedAt = now;
  }
}

module.exports = Tag; 