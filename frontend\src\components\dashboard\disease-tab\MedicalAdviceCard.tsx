import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  CircularProgress,
  Tabs,
  Tab,
  Stack,
} from '@mui/material';
import {
  DirectionsRun as ExerciseIcon,
  Spa as LifestyleIcon,
  RestaurantMenu as DietIcon,
  Psychology as PsychologyIcon,
} from '@mui/icons-material';
import BaseCard from './BaseCard';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

interface MedicalAdviceCardProps {
  aiReport: any;
  loading?: boolean;
  medicalAdvice?: string[]; // 医疗建议
  lifestyleAdvice?: string[]; // 生活建议
  exerciseAdvice?: string[]; // 运动建议
  dietAdvice?: string[]; // 饮食建议
  psychologicalAdvice?: string[]; // 心理建议
  imagingFindings?: any; // 关键影像学发现
  recommendedTests?: string[]; // 推荐检查项目
  isDemo?: boolean; // 是否为演示数据
}

// TabPanel 组件
function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`advice-tabpanel-${index}`}
      aria-labelledby={`advice-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 2 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

/**
 * 建议卡片组件
 * 显示AI生成的饮食、生活、运动和心理建议
 */
const MedicalAdviceCard: React.FC<MedicalAdviceCardProps> = ({ 
  aiReport, 
  loading = false,
  medicalAdvice = [],
  lifestyleAdvice = [],
  exerciseAdvice = [],
  dietAdvice = [],
  psychologicalAdvice = [],
  imagingFindings = null,
  recommendedTests = [],
  isDemo = false
}) => {
  // 标签页状态
  const [tabValue, setTabValue] = useState(0);
  // 添加演示数据的状态 - 默认根据isDemo决定是否启用演示数据
  const [useDemo, setUseDemo] = useState(isDemo);
  
  // 当isDemo属性变化时更新useDemo状态
  useEffect(() => {
    setUseDemo(isDemo);
    console.log('MedicalAdviceCard - isDemo变化:', isDemo);
  }, [isDemo]);
  
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  
  // 添加日志输出，便于调试
  console.log('MedicalAdviceCard接收到的数据详情:', {
    dietAdvice: Array.isArray(dietAdvice) ? `${dietAdvice.length}条数据` : '0条数据',
    lifestyleAdvice: Array.isArray(lifestyleAdvice) ? `${lifestyleAdvice.length}条数据` : '0条数据',
    exerciseAdvice: Array.isArray(exerciseAdvice) ? `${exerciseAdvice.length}条数据` : '0条数据',
    psychologicalAdvice: Array.isArray(psychologicalAdvice) ? `${psychologicalAdvice.length}条数据` : '0条数据',
    isDemo: isDemo
  });
  
  // 查看第一条数据内容，帮助调试
  if (dietAdvice && dietAdvice.length > 0) {
    console.log('饮食建议第一条:', dietAdvice[0]);
  }
  if (lifestyleAdvice && lifestyleAdvice.length > 0) {
    console.log('生活建议第一条:', lifestyleAdvice[0]);
  }
  if (exerciseAdvice && exerciseAdvice.length > 0) {
    console.log('运动建议第一条:', exerciseAdvice[0]);
  }
  if (psychologicalAdvice && psychologicalAdvice.length > 0) {
    console.log('心理建议第一条:', psychologicalAdvice[0]);
  }
  
  // 演示数据
  const demoData = {
    diet: [
      '保持均衡饮食，确保蛋白质、碳水化合物和脂肪的摄入比例合理',
      '每日摄入足够的水分（建议1.5-2升），保持良好的体液平衡',
      '增加新鲜蔬菜和水果的摄入，确保维生素和矿物质摄入充足',
      '控制盐分摄入量，避免过度使用调味品和加工食品',
      '根据个人情况控制饮食总量，维持健康体重'
    ],
    lifestyle: [
      '保持规律的作息时间，确保充足的睡眠',
      '减少长时间久坐，每小时起身活动5-10分钟',
      '避免过度疲劳，合理安排工作和休息时间',
      '保持良好的个人卫生习惯，预防常见感染'
    ],
    exercise: [
      '根据个人体能状况进行适量运动，可从低强度活动开始',
      '尝试每周进行3-5次有氧运动，每次30分钟',
      '适当进行力量训练，增强肌肉力量和耐力',
      '进行舒缓的伸展运动，提高身体灵活性'
    ],
    psychological: [
      '保持积极乐观的心态，接受疾病带来的变化',
      '寻求家人和朋友的社会支持和理解',
      '学习简单的放松技巧，如深呼吸和渐进性肌肉放松',
      '必要时寻求专业心理咨询师的帮助',
      '加入相关疾病支持小组，分享经验和获取信息'
    ]
  };
  
  // 确定显示内容
  // 使用真实数据 或 启用了演示模式时使用演示数据
  const displayAdvice = {
    diet: (dietAdvice && dietAdvice.length > 0) ? dietAdvice : (useDemo ? demoData.diet : []),
    lifestyle: (lifestyleAdvice && lifestyleAdvice.length > 0) ? lifestyleAdvice : (useDemo ? demoData.lifestyle : []),
    exercise: (exerciseAdvice && exerciseAdvice.length > 0) ? exerciseAdvice : (useDemo ? demoData.exercise : []),
    psychological: (psychologicalAdvice && psychologicalAdvice.length > 0) ? psychologicalAdvice : (useDemo ? demoData.psychological : [])
  };
  
  // 判断是否使用演示数据
  const isDemoData = isDemo || 
    ((!dietAdvice || dietAdvice.length === 0) && 
     (!lifestyleAdvice || lifestyleAdvice.length === 0) && 
     (!exerciseAdvice || exerciseAdvice.length === 0) && 
     (!psychologicalAdvice || psychologicalAdvice.length === 0));
  
  if (loading) {
    return (
      <BaseCard title="建议" loading={true}>
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress size={30} />
        </Box>
      </BaseCard>
    );
  }
  
  // 检查是否有任何建议
  const hasAnyAdvice = Object.values(displayAdvice).some(arr => arr.length > 0);
  
  if (!hasAnyAdvice) {
    return (
      <BaseCard title="建议">
        <Box sx={{ textAlign: 'center', py: 3 }}>
          <Typography variant="body2" color="text.secondary">
            正在加载建议数据...
          </Typography>
        </Box>
      </BaseCard>
    );
  }
  
  // 简洁版显示所有类型建议
  return (
    <BaseCard 
      title={`建议${isDemoData ? " (演示数据)" : ""}`}
      subTitle="本信息系智能推荐，仅供参考，谨慎核实！"
      loading={loading}
    >
      
      <Tabs
        value={tabValue}
        onChange={handleTabChange}
        variant="scrollable"
        scrollButtons="auto"
        aria-label="建议标签页"
      >
        <Tab icon={<DietIcon fontSize="small" />} label="饮食" />
        <Tab icon={<LifestyleIcon fontSize="small" />} label="生活" />
        <Tab icon={<ExerciseIcon fontSize="small" />} label="运动" />
        <Tab icon={<PsychologyIcon fontSize="small" />} label="心理" />
      </Tabs>
      
      {/* 饮食建议 */}
      <TabPanel value={tabValue} index={0}>
        <Stack spacing={1}>
          {displayAdvice.diet.length > 0 ? (
            displayAdvice.diet.map((advice, index) => (
              <Box key={`diet-${index}`} sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                <DietIcon color="primary" fontSize="small" sx={{ mt: 0.3 }} />
                <Typography variant="body2">{advice}</Typography>
              </Box>
            ))
          ) : (
            <Typography variant="body2" color="text.secondary" align="center">
              暂无饮食建议
            </Typography>
          )}
        </Stack>
      </TabPanel>
      
      {/* 生活建议 */}
      <TabPanel value={tabValue} index={1}>
        <Stack spacing={1}>
          {displayAdvice.lifestyle.length > 0 ? (
            displayAdvice.lifestyle.map((advice, index) => (
              <Box key={`lifestyle-${index}`} sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                <LifestyleIcon color="secondary" fontSize="small" sx={{ mt: 0.3 }} />
                <Typography variant="body2">{advice}</Typography>
              </Box>
            ))
          ) : (
            <Typography variant="body2" color="text.secondary" align="center">
              暂无生活建议
            </Typography>
          )}
        </Stack>
      </TabPanel>
      
      {/* 运动建议 */}
      <TabPanel value={tabValue} index={2}>
        <Stack spacing={1}>
          {displayAdvice.exercise.length > 0 ? (
            displayAdvice.exercise.map((advice, index) => (
              <Box key={`exercise-${index}`} sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                <ExerciseIcon color="success" fontSize="small" sx={{ mt: 0.3 }} />
                <Typography variant="body2">{advice}</Typography>
              </Box>
            ))
          ) : (
            <Typography variant="body2" color="text.secondary" align="center">
              暂无运动建议
            </Typography>
          )}
        </Stack>
      </TabPanel>
      
      {/* 心理建议 */}
      <TabPanel value={tabValue} index={3}>
        <Stack spacing={1}>
          {displayAdvice.psychological.length > 0 ? (
            displayAdvice.psychological.map((advice, index) => (
              <Box key={`psychological-${index}`} sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                <PsychologyIcon color="warning" fontSize="small" sx={{ mt: 0.3 }} />
                <Typography variant="body2">{advice}</Typography>
              </Box>
            ))
          ) : (
            <Typography variant="body2" color="text.secondary" align="center">
              暂无心理建议
            </Typography>
          )}
        </Stack>
      </TabPanel>
    </BaseCard>
  );
};

export default MedicalAdviceCard; 