# 病理系统权限说明文档

## 权限级别

我们的病理系统包含三个权限级别，提供了灵活的授权控制机制：

### 1. 基础授权 (BASIC)
- **权限范围**：仅具有只读权限
- **可执行操作**：只能查看个人用户的授权信息
- **操作限制**：不能进行任何编辑、创建或删除操作
- **视觉标识**：灰色系（图标和背景）

### 2. 标准授权 (STANDARD)
- **用户信息**：对授权用户的基本信息和患者信息仅有只读权限
- **病理管理**：
  - 可以编辑授权用户的病理信息
  - 不能删除授权用户的任何病理信息
  - 不能为授权用户创建新的病理信息
- **记录与报告**：
  - 可以读写记录和报告
  - 可以访问授权用户创建的记录和报告，但权限是只读，并且遵从个人用户的隐私设置和已删除标志
- **视觉标识**：蓝色系（图标和背景）

### 3. 完整授权 (FULL)
- **用户信息**：对授权用户的基本信息和患者信息仅有只读权限
- **病理管理**：
  - 可以编辑授权用户的病理信息
  - 不能删除授权用户的任何病理信息
  - 不能为授权用户创建新的病理信息
- **记录与报告**：
  - 可以读写授权用户的所有记录和报告，但需遵从个人用户的隐私设置和已删除标志
  - 可以读写自己创建的记录和报告
- **视觉标识**：绿色系（图标和背景）

## 权限逻辑

权限控制基于以下几个关键因素：

1. **授权级别**：由授权关系中的`privacyLevel`字段定义
2. **创建者关系**：对于STANDARD级别，用户只能编辑/删除自己创建的记录
3. **隐私设置**：遵循个人用户设置的隐私标志，隐私内容只对FULL级别授权可见
4. **删除标志**：已删除的内容不对任何授权用户显示

### 权限判断流程

系统使用`getPermissions`函数来精确判断用户对特定内容的操作权限：

```typescript
const permissions = {
  canView: false,    // 查看权限
  canCreate: false,  // 创建权限
  canEdit: false,    // 编辑权限
  canDelete: false   // 删除权限
};
```

权限判断逻辑：
1. 基础授权(BASIC)：只有查看用户授权信息的权限
2. 标准授权(STANDARD)：
   - 可以查看非隐私内容
   - 可以编辑授权用户的病理(但不能删除)
   - 可以读写自己创建的记录和报告
   - 对授权用户创建的内容只有只读权限
3. 完整授权(FULL)：
   - 可以查看包括隐私在内的所有内容
   - 可以编辑授权用户的病理(但不能删除)
   - 可以读写授权用户的所有记录和报告
   - 可以读写自己创建的记录和报告

## 视觉表现

我们通过一致的视觉语言来传达权限状态：

### 1. 权限提示区
- 顶部的权限摘要区域，显示当前用户的权限级别
- 包含操作指南，列出允许的操作
- 使用对应权限级别的颜色主题

### 2. 操作按钮
- 根据当前权限动态显示或隐藏操作按钮
- 禁用无权执行的操作，并提供提示信息
- 按钮颜色与权限级别一致

## 最佳实践

1. **权限检查**：在执行操作前始终检查权限
2. **友好提示**：当用户尝试无权限操作时，提供清晰提示
3. **视觉一致性**：保持权限相关的视觉元素一致
4. **自然转化**：随着权限级别提升，自然解锁更多功能

## 技术实现

权限系统的核心组件：

1. **ServiceUserContext**：存储和管理当前权限上下文
2. **authorizationService**：处理授权关系API
3. **getPermissionsForDisease**：核心权限判断函数
4. **权限样式配置**：统一的权限级别样式定义 