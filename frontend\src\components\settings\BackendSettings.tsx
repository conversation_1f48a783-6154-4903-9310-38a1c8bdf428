import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  TextField, 
  Button, 
  Alert, 
  Paper,
  Snackbar,
  IconButton
} from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';
import RefreshIcon from '@mui/icons-material/Refresh';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';

/**
 * 后端服务器设置组件
 * 允许用户配置后端服务器的IP地址，用于开发环境下的跨域测试
 */
const BackendSettings: React.FC = () => {
  const [backendIP, setBackendIP] = useState<string>(localStorage.getItem('backendServerIP') || '');
  const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    type: 'success' | 'error' | 'info' | 'warning';
  }>({
    open: false,
    message: '',
    type: 'info'
  });

  // 验证IP地址格式
  const validateIPAddress = (ip: string): boolean => {
    // 允许空值（使用默认值）或符合格式的IP地址
    if (!ip) return true;
    
    // 匹配 http://***********:3001 或 ***********:3001 格式
    const ipPattern = /^(http:\/\/)?(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})(:\d+)?$/;
    return ipPattern.test(ip);
  };

  // 保存IP地址设置
  const saveSettings = () => {
    try {
      if (!validateIPAddress(backendIP)) {
        setNotification({
          open: true,
          message: '请输入有效的IP地址格式，例如: ***********:3001',
          type: 'error'
        });
        return;
      }

      // 保存到localStorage
      if (backendIP) {
        localStorage.setItem('backendServerIP', backendIP);
        setNotification({
          open: true,
          message: '后端服务器配置已保存，请刷新页面以应用更改',
          type: 'success'
        });
      } else {
        // 如果输入为空，清除设置
        localStorage.removeItem('backendServerIP');
        setNotification({
          open: true,
          message: '已恢复使用默认后端地址 (localhost:3001)',
          type: 'success'
        });
      }
    } catch (error) {
      console.error('保存设置失败:', error);
      setNotification({
        open: true,
        message: '保存设置失败',
        type: 'error'
      });
    }
  };

  // 刷新页面应用更改
  const refreshPage = () => {
    window.location.reload();
  };

  // 关闭提示
  const handleCloseNotification = () => {
    setNotification(prev => ({
      ...prev,
      open: false
    }));
  };

  return (
    <Paper elevation={0} sx={{ p: 3, mb: 3, border: '1px solid', borderColor: 'divider' }}>
      <Typography variant="h6" gutterBottom>
        后端服务器设置
        <IconButton size="small" sx={{ ml: 1, mb: 0.5 }}>
          <HelpOutlineIcon fontSize="small" />
        </IconButton>
      </Typography>
      
      <Alert severity="info" sx={{ mb: 2 }}>
        此设置仅用于开发环境。修改后端服务器地址有助于解决跨域请求问题，特别是在文件下载时。
      </Alert>
      
      <Box sx={{ mb: 2 }}>
        <TextField
          fullWidth
          label="后端服务器IP地址和端口"
          variant="outlined"
          placeholder="例如: ***********:3001 或 http://***********:3001"
          value={backendIP}
          onChange={(e) => setBackendIP(e.target.value)}
          size="small"
          helperText="如果留空，将使用默认地址 (localhost:3001)"
        />
      </Box>
      
      <Box sx={{ display: 'flex', gap: 2 }}>
        <Button 
          variant="contained" 
          color="primary"
          startIcon={<SaveIcon />}
          onClick={saveSettings}
        >
          保存设置
        </Button>
        
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={refreshPage}
        >
          刷新应用更改
        </Button>
      </Box>
      
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseNotification} severity={notification.type} sx={{ width: '100%' }}>
          {notification.message}
        </Alert>
      </Snackbar>
    </Paper>
  );
};

export default BackendSettings; 