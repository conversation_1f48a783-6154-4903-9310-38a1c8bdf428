import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Chip,
  TextField,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  Divider,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { tagsApiClient } from '../../services/apiClient';

interface Tag {
  id: string;
  name: string;
  color?: string;
  usage_count?: number;
}

/**
 * 标签管理组件
 * 用于显示、添加和删除用户的自定义标签
 */
export const TagManager: React.FC = () => {
  const [tags, setTags] = useState<Tag[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [newTag, setNewTag] = useState("");
  const [dialogOpen, setDialogOpen] = useState(false);
  
  // 加载用户标签
  const loadTags = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await tagsApiClient.get('/user-tags');
      setTags(response.data);
    } catch (error: any) {
      console.error('获取标签失败:', error);
      setError(error.message || '获取标签失败');
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    loadTags();
  }, []);
  
  // 创建标签
  const handleCreateTag = async () => {
    if (!newTag.trim()) return;
    
    try {
      setError(null);
      // 创建新标签
      await tagsApiClient.post('/create', { name: newTag.trim() });
      
      setNewTag("");
      setDialogOpen(false);
      loadTags(); // 重新加载标签
    } catch (error: any) {
      console.error('创建标签失败:', error);
      setError(error.message || '创建标签失败');
    }
  };
  
  // 删除标签
  const handleDeleteTag = async (tagId: string) => {
    try {
      await tagsApiClient.delete(`/${tagId}`);
      loadTags(); // 重新加载标签
    } catch (error: any) {
      console.error('删除标签失败:', error);
      setError(error.message || '删除标签失败');
    }
  };
  
  return (
    <Paper sx={{ p: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">我的标签</Typography>
        <Button 
          startIcon={<AddIcon />} 
          variant="contained" 
          size="small"
          onClick={() => setDialogOpen(true)}
        >
          添加标签
        </Button>
      </Box>
      <Divider sx={{ mb: 2 }} />
      
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}
      
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress size={24} />
        </Box>
      ) : tags.length === 0 ? (
        <Typography color="text.secondary" sx={{ p: 2, textAlign: 'center' }}>
          您还没有添加任何自定义标签
        </Typography>
      ) : (
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          {tags.map(tag => (
            <Chip 
              key={tag.id} 
              label={tag.name} 
              color="primary" 
              onDelete={() => handleDeleteTag(tag.id)}
              deleteIcon={<DeleteIcon fontSize="small" />}
              size="medium"
              sx={{ m: 0.5 }}
            />
          ))}
        </Box>
      )}
      
      {/* 添加标签对话框 */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)}>
        <DialogTitle>添加新标签</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="标签名称"
            fullWidth
            variant="outlined"
            value={newTag}
            onChange={(e) => setNewTag(e.target.value)}
            helperText="标签将用于组织和筛选您的记录"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>取消</Button>
          <Button 
            onClick={handleCreateTag} 
            variant="contained"
            disabled={!newTag.trim()}
          >
            添加
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default TagManager; 