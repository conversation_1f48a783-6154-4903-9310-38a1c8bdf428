import React, { useState, ReactNode } from 'react';
import { Button, Paper, Typography, Box, Alert, CircularProgress, List, ListItem, ListItemIcon, ListItemText, Divider } from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import HelpIcon from '@mui/icons-material/Help';
import { checkApiStatus } from '../../services/debugService';
import { getToken } from '../../utils/auth';

// 自定义类型，明确处理error字段
interface ApiCheckResults {
  healthCheck: boolean;
  authCheck: boolean;
  aiReportsCheck: boolean;
  error?: Error | string | Record<string, unknown> | null;
}

/**
 * API调试器组件
 * 用于检测和显示API连接状态、认证状态和常见问题
 */
const ApiDebugger: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<ApiCheckResults | null>(null);
  const [showToken, setShowToken] = useState(false);
  const token = getToken();

  const handleCheckApi = async () => {
    setLoading(true);
    try {
      const statusResults = await checkApiStatus();
      setResults(statusResults as ApiCheckResults);
    } catch (error) {
      console.error('API检查过程中出错:', error);
      setResults({
        healthCheck: false,
        authCheck: false,
        aiReportsCheck: false,
        error: error instanceof Error ? error : String(error)
      });
    } finally {
      setLoading(false);
    }
  };

  // 安全地格式化错误
  const formatError = (error: ApiCheckResults['error']): string => {
    if (error === null || error === undefined) return '未知错误';
    if (error instanceof Error) return `${error.name}: ${error.message}`;
    if (typeof error === 'string') return error;
    return JSON.stringify(error, null, 2);
  };

  const renderErrorContent = (error: ApiCheckResults['error']): ReactNode => {
    return (
      <Typography variant="body2" component="pre" sx={{ 
        whiteSpace: 'pre-wrap',
        overflowX: 'auto',
        backgroundColor: '#f5f5f5',
        p: 1,
        borderRadius: 1
      }}>
        {formatError(error)}
      </Typography>
    );
  };

  return (
    <Paper elevation={3} sx={{ p: 3, maxWidth: 600, mx: 'auto', mt: 4 }}>
      <Typography variant="h5" gutterBottom>API连接诊断</Typography>
      
      <Divider sx={{ my: 2 }} />
      
      <Typography variant="body1" paragraph>
        此工具可以帮助您检测API连接状态、认证状态以及常见问题。
      </Typography>
      
      <Box sx={{ mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>认证状态:</Typography>
        <Box sx={{ backgroundColor: '#f5f5f5', p: 2, borderRadius: 1 }}>
          <Typography variant="body2">
            令牌状态: {token ? '已找到令牌' : '未找到令牌'}
          </Typography>
          {token && (
            <Box>
              <Typography variant="body2">
                令牌长度: {token.length} 字符
              </Typography>
              <Typography variant="body2">
                令牌前缀: {token.substring(0, 6)}...
                {showToken ? ` (${token})` : ''}
              </Typography>
              <Button 
                size="small" 
                onClick={() => setShowToken(!showToken)}
                sx={{ mt: 1 }}
              >
                {showToken ? '隐藏令牌' : '显示完整令牌'}
              </Button>
            </Box>
          )}
        </Box>
      </Box>
      
      <Button 
        variant="contained" 
        onClick={handleCheckApi} 
        disabled={loading}
      >
        {loading ? <CircularProgress size={24} /> : '开始API检查'}
      </Button>
      
      {results && (
        <Box sx={{ mt: 3 }}>
          <div>
            <Typography variant="h6" gutterBottom>检查结果:</Typography>
            
            <List>
              <ListItem>
                <ListItemIcon>
                  {results.healthCheck ? <CheckCircleIcon color="success" /> : <ErrorIcon color="error" />}
                </ListItemIcon>
                <ListItemText 
                  primary="健康检查" 
                  secondary={results.healthCheck ? '成功' : '失败 - API服务器可能未运行或无法访问'} 
                />
              </ListItem>
              
              <ListItem>
                <ListItemIcon>
                  {results.authCheck ? <CheckCircleIcon color="success" /> : 
                   (token ? <ErrorIcon color="error" /> : <HelpIcon color="disabled" />)}
                </ListItemIcon>
                <ListItemText 
                  primary="认证检查" 
                  secondary={
                    results.authCheck ? '成功 - 令牌有效' : 
                    (token ? '失败 - 令牌无效或已过期' : '跳过 - 未找到令牌')
                  } 
                />
              </ListItem>
              
              <ListItem>
                <ListItemIcon>
                  {results.aiReportsCheck ? <CheckCircleIcon color="success" /> : <ErrorIcon color="error" />}
                </ListItemIcon>
                <ListItemText 
                  primary="AI报告API检查" 
                  secondary={results.aiReportsCheck ? '成功' : '失败 - 无法访问AI报告API'} 
                />
              </ListItem>
            </List>
            
            {results.error && (
              <Alert severity="error" sx={{ mt: 2 }}>
                <Typography variant="subtitle2">错误详情:</Typography>
                {renderErrorContent(results.error)}
              </Alert>
            )}
            
            {!results.healthCheck && (
              <Alert severity="warning" sx={{ mt: 2 }}>
                健康检查失败表明API服务器可能未运行或无法访问。请确认后端服务是否已启动，并确认API URL配置正确。
              </Alert>
            )}
            
            {token && !results.authCheck && (
              <Alert severity="warning" sx={{ mt: 2 }}>
                认证检查失败表明您的令牌无效或已过期。请尝试重新登录获取新的令牌。
              </Alert>
            )}
            
            {!results.aiReportsCheck && (
              <Alert severity="warning" sx={{ mt: 2 }}>
                AI报告API检查失败表明AI报告模块可能存在问题，API路径可能配置错误，或您没有足够的权限访问AI报告功能。
              </Alert>
            )}
          </div>
        </Box>
      )}
    </Paper>
  );
};

export default ApiDebugger; 