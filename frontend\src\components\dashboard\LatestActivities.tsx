import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  List, 
  ListItem, 
  ListItemIcon, 
  ListItemText,
  Chip,
  Divider,
  IconButton,
  Tooltip,
  CircularProgress,
  useTheme
} from '@mui/material';
import {
  MedicationOutlined as MedicationIcon,
  HealthAndSafetyOutlined as HealthIcon,
  ScienceOutlined as ScienceIcon,
  VisibilityOutlined as ViewIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { usePatientDiseaseContext } from '../../context/PatientDiseaseContext';

// 活动类型
export type ActivityType = '治疗计划' | '随访检查' | '诊断报告';

// 活动项目类型
interface ActivityItem {
  id: string;
  type: ActivityType;
  title: string;
  timestamp: string;
  diseaseId: string;
  diseaseName: string;
  priority?: '重要' | '普通';
  tags?: string[];
}

/**
 * 最新动态组件
 * 显示患者的最新医疗活动记录
 */
const LatestActivities: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { selectedPatientId } = usePatientDiseaseContext();
  const [loading, setLoading] = useState(false);
  const [activities, setActivities] = useState<ActivityItem[]>([]);

  // 获取活动图标
  const getActivityIcon = (type: ActivityType) => {
    switch (type) {
      case '治疗计划':
        return <MedicationIcon sx={{ color: theme.palette.warning.main }} />;
      case '随访检查':
        return <HealthIcon sx={{ color: theme.palette.info.main }} />;
      case '诊断报告':
        return <ScienceIcon sx={{ color: theme.palette.secondary.main }} />;
      default:
        return <HealthIcon sx={{ color: theme.palette.primary.main }} />;
    }
  };

  // 从后端获取最新活动数据
  useEffect(() => {
    if (!selectedPatientId) {
      setActivities([]);
      return;
    }

    setLoading(true);

    // 实际项目中应该调用API获取数据
    // 这里使用模拟数据进行演示
    const mockData: ActivityItem[] = [
      {
        id: '1',
        type: '治疗计划',
        title: '高血压药物治疗计划',
        timestamp: '2024-04-20 08:00',
        diseaseId: '1',
        diseaseName: '高血压',
        priority: '重要',
        tags: ['用药', '重要']
      },
      {
        id: '2',
        type: '随访检查',
        title: '随访检查',
        timestamp: '2024-04-15 08:00',
        diseaseId: '1',
        diseaseName: '高血压',
        tags: ['随访', '复诊']
      },
      {
        id: '3',
        type: '诊断报告',
        title: '高血压确诊报告',
        timestamp: '2024-03-10 08:00',
        diseaseId: '1',
        diseaseName: '高血压',
        priority: '重要',
        tags: ['确诊', '重要']
      }
    ];

    // 模拟异步加载
    setTimeout(() => {
      setActivities(mockData);
      setLoading(false);
    }, 300);
  }, [selectedPatientId]);

  // 处理查看详情
  const handleViewDetail = (activityId: string, type: ActivityType) => {
    // 实际应用中跳转到对应的详情页面
    navigate(`/records/${activityId}`);
  };

  if (!selectedPatientId) {
    return null;
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
        <CircularProgress size={30} />
      </Box>
    );
  }

  if (activities.length === 0) {
    return (
      <Box sx={{ p: 2, textAlign: 'center' }}>
        <Typography color="text.secondary">暂无活动记录</Typography>
      </Box>
    );
  }

  return (
    <List sx={{ py: 0 }}>
      {activities.map((activity, index) => (
        <React.Fragment key={activity.id}>
          <ListItem
            alignItems="flex-start"
            sx={{
              py: 1.5,
              px: 0,
              '&:hover': {
                bgcolor: 'action.hover',
              },
            }}
          >
            <ListItemIcon sx={{ minWidth: 40 }}>
              {getActivityIcon(activity.type)}
            </ListItemIcon>
            
            <ListItemText
              primaryTypographyProps={{ component: 'div' }}
              secondaryTypographyProps={{ component: 'div' }}
              primary={
                <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', mb: 0.5 }}>
                  <Typography
                    variant="subtitle2"
                    component="div"
                    sx={{ 
                      fontWeight: 600, 
                      mr: 1,
                      fontSize: { xs: '0.875rem', sm: '0.9rem' }
                    }}
                  >
                    {activity.title}
                  </Typography>
                  
                  {activity.priority === '重要' && (
                    <Chip
                      label="重要"
                      size="small"
                      color="error"
                      variant="outlined"
                      sx={{ 
                        height: 20, 
                        fontSize: '0.7rem',
                        fontWeight: 'bold'
                      }}
                    />
                  )}
                </Box>
              }
              secondary={
                <Box component="div">
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                    <Typography
                      variant="caption"
                      color="text.secondary"
                      component="div"
                      sx={{ mr: 1 }}
                    >
                      {activity.timestamp}
                    </Typography>
                    
                    <Chip
                      label={activity.diseaseName}
                      size="small"
                      sx={{ 
                        height: 18, 
                        fontSize: '0.7rem',
                        bgcolor: theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.2)' : 'rgba(0,0,0,0.05)',
                        color: 'text.secondary'
                      }}
                    />
                  </Box>
                  
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                    {activity.tags?.map((tag, i) => (
                      <Chip
                        key={i}
                        label={tag}
                        size="small"
                        variant="outlined"
                        sx={{ 
                          mr: 0.5, 
                          height: 18, 
                          fontSize: '0.7rem' 
                        }}
                      />
                    ))}
                  </Box>
                </Box>
              }
            />
            
            <Tooltip title="查看详情">
              <IconButton
                edge="end"
                size="small"
                sx={{ ml: 1 }}
                onClick={() => handleViewDetail(activity.id, activity.type)}
              >
                <ViewIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </ListItem>
          
          {index < activities.length - 1 && <Divider variant="inset" component="li" />}
        </React.Fragment>
      ))}
    </List>
  );
};

export default LatestActivities; 