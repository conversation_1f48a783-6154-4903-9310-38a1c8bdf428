import React, { useState, useEffect } from 'react';
import { useNavigate, Link, useSearchParams } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  IconButton,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  FormControlLabel,
  Switch,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  Stack,
  Snackbar,
  Alert,
  CircularProgress,
  Tooltip
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import ListAltIcon from '@mui/icons-material/ListAlt';
import ManageSearchIcon from '@mui/icons-material/ManageSearch';
import InfoIcon from '@mui/icons-material/Info';
import BookmarkIcon from '@mui/icons-material/Bookmark';
import LockIcon from '@mui/icons-material/Lock';
import SettingsIcon from '@mui/icons-material/Settings';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { useAuthStore } from '../store/authStore';
import { createRecord, uploadAttachment } from '../services/recordService';
import { getPatients } from '../services/patientService';
import { getDiseases } from '../services/diseaseService';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useForm, Controller } from 'react-hook-form';
import StageSelector from '../components/records/StageSelector';
import TypeTagSelector from '../components/records/TypeTagSelector';
import SeveritySlider from '../components/records/SeveritySlider';
import { 
  RecordTypeEnum, 
  StagePhaseEnum, 
  StageNodeEnum, 
  StageNodeNames, 
  SeverityEnum, 
} from '../types/recordEnums';
import SimpleTagInput from '../components/tags/SimpleTagInput';
import { usePatientDiseaseContext } from '../context/PatientDiseaseContext';

// 添加记录类型模板定义
const RECORD_TYPE_TEMPLATES: Partial<Record<RecordTypeEnum, { title: string; content: string }>> = {
  [RecordTypeEnum.SELF_DESCRIPTION]: {
    title: '自述',
    content: '主要症状:\n\n发生时间:\n\n严重程度:\n\n持续时间:\n\n诱发因素:\n\n缓解因素:'
  },
  [RecordTypeEnum.SYMPTOM]: {
    title: '症状记录',
    content: '症状描述:\n\n开始时间:\n\n症状变化:\n\n影响日常生活程度:'
  },
  [RecordTypeEnum.EXAMINATION]: {
    title: '检查结果',
    content: '检查项目:\n\n检查日期:\n\n检查结果:\n\n医生意见:'
  },
  [RecordTypeEnum.LAB_TEST]: {
    title: '化验结果',
    content: '化验项目:\n\n采样时间:\n\n报告时间:\n\n结果指标:\n\n参考范围:\n\n结果分析:'
  },
  [RecordTypeEnum.DIAGNOSIS]: {
    title: '诊断结果',
    content: '诊断结论:\n\n诊断依据:\n\n鉴别诊断:\n\n医生建议:'
  },
  [RecordTypeEnum.TREATMENT]: {
    title: '治疗方案',
    content: '治疗类型:\n\n用药名称:\n\n用法用量:\n\n疗程:\n\n注意事项:\n\n预期效果:'
  },
  [RecordTypeEnum.MEDICATION]: {
    title: '用药记录',
    content: '药品名称:\n\n规格:\n\n用法用量:\n\n服用时间:\n\n不良反应:\n\n效果评估:'
  },
  [RecordTypeEnum.FOLLOW_UP]: {
    title: '随访记录',
    content: '随访日期:\n\n病情进展:\n\n治疗效果评估:\n\n需要调整的方案:\n\n下次随访时间:'
  },
  [RecordTypeEnum.SURGERY]: {
    title: '手术记录',
    content: '手术名称:\n\n手术日期:\n\n术前准备:\n\n手术过程:\n\n术后情况:\n\n恢复计划:'
  },
  [RecordTypeEnum.MONITORING]: {
    title: '监测记录',
    content: '监测项目:\n\n监测时间:\n\n监测结果:\n\n异常情况:\n\n处理措施:'
  },
  [RecordTypeEnum.PHYSICAL_THERAPY]: {
    title: '理疗记录',
    content: '治疗类型:\n\n治疗目标:\n\n治疗频率:\n\n治疗过程:\n\n效果评估:'
  },
  [RecordTypeEnum.PSYCHOLOGY]: {
    title: '心理状态',
    content: '心理症状:\n\n发生背景:\n\n影响程度:\n\n应对方式:\n\n改善建议:'
  },
  [RecordTypeEnum.HOSPITALIZATION]: {
    title: '住院记录',
    content: '入院日期:\n\n入院科室:\n\n主诉症状:\n\n入院诊断:\n\n入院评估:\n\n治疗计划:\n\n住院安排:'
  },
  [RecordTypeEnum.DISCHARGE]: {
    title: '出院记录',
    content: '出院日期:\n\n住院天数:\n\n出院诊断:\n\n出院情况:\n\n出院医嘱:\n\n复诊安排:'
  },
  [RecordTypeEnum.REVISIT]: {
    title: '复诊记录',
    content: '复诊日期:\n\n复诊科室:\n\n主诉:\n\n诊疗过程:\n\n医嘱:'
  },
  [RecordTypeEnum.REHABILITATION]: {
    title: '康复训练',
    content: '康复项目:\n\n训练目标:\n\n训练方法:\n\n训练频次:\n\n进展情况:'
  },
  [RecordTypeEnum.NURSING]: {
    title: '护理记录',
    content: '护理类型:\n\n护理内容:\n\n护理观察:\n\n护理评估:'
  },
  [RecordTypeEnum.APPOINTMENT]: {
    title: '预约安排',
    content: '预约日期:\n\n预约科室:\n\n预约医生:\n\n预约项目:\n\n注意事项:'
  },
  [RecordTypeEnum.REPORT]: {
    title: '报告记录',
    content: '报告类型:\n\n报告日期:\n\n报告内容:\n\n结论解释:'
  },
  [RecordTypeEnum.OTHER]: {
    title: '其他记录',
    content: '记录内容:\n\n备注:'
  }
};

// 添加节点到阶段的映射
// 节点到阶段的反向映射
const NODE_TO_PHASE_MAP: Record<StageNodeEnum, StagePhaseEnum> = {
  [StageNodeEnum.INITIAL_VISIT]: StagePhaseEnum.INITIAL,    // 生病节点 -> 初诊阶段
  [StageNodeEnum.DIAGNOSIS]: StagePhaseEnum.DIAGNOSIS,      // 确诊节点 -> 确诊阶段
  [StageNodeEnum.TREATMENT]: StagePhaseEnum.TREATMENT,      // 治疗节点 -> 治疗阶段
  [StageNodeEnum.FOLLOW_UP]: StagePhaseEnum.RECOVERY,       // 随访节点 -> 康复阶段
  [StageNodeEnum.PROGNOSIS]: StagePhaseEnum.PROGNOSIS,      // 预后节点 -> 预后阶段
  [StageNodeEnum.ARCHIVE]: StagePhaseEnum.PROGNOSIS         // 归档节点 -> 预后阶段 (默认放在预后阶段)
};

// 通知类型定义
interface Notification {
  open: boolean;
  message: string;
  type: 'success' | 'error' | 'info' | 'warning';
}

// 用户等级限制
interface UserLevelLimits {
  maxAttachmentSize: number;
  maxQuantity: number;
  maxTotalStorage: number;
}

// 附件类型
interface Attachment {
  file: File;
  preview?: string;
}

// 表单数据类型
interface FormData {
  patientId: string;
  diseaseId: string;
  title: string;
  content: string;
  recordDate: string;
  recordType: RecordTypeEnum[];
  primaryType: RecordTypeEnum;
  stagePhase: string;
  stageNode: string;
  severity: SeverityEnum;
  isImportant: boolean;
  isPrivate: boolean;
  customTags: string;
}

// 记录创建页面组件
const RecordCreatePage: React.FC = () => {
  const navigate = useNavigate();
  const { token, user } = useAuthStore((state) => state);
  const userId = user?.id || '';
  const [searchParams] = useSearchParams();
  const { 
    selectedPatientId: contextPatientId, 
    selectedDiseaseId: contextDiseaseId,
    setSelectedPatient,
    setSelectedDisease
  } = usePatientDiseaseContext();
  
  // 表单状态
  const { control, handleSubmit, setValue, watch, formState: { errors } } = useForm<FormData>({
    defaultValues: {
      patientId: '',
      diseaseId: '',
      title: '',
      content: '',
      recordDate: new Date().toISOString().slice(0, 16),
      recordType: [],
      primaryType: '' as RecordTypeEnum,
      stagePhase: '',
      stageNode: '',
      severity: SeverityEnum.MODERATE,
      isImportant: false,
      isPrivate: false,
      customTags: '',
    }
  });
  
  // 处理URL参数和Context的初始化
  useEffect(() => {
    // 首先尝试从URL参数获取patientId和diseaseId
    const urlPatientId = searchParams.get('patientId');
    const urlDiseaseId = searchParams.get('diseaseId');
    
    // 如果URL中有参数，优先使用URL参数
    if (urlPatientId) {
      setValue('patientId', urlPatientId);
    } 
    // 否则使用Context中的值
    else if (contextPatientId) {
      setValue('patientId', contextPatientId);
    }
    
    // 仅当patientId设置后才尝试设置diseaseId，确保病理选择是有效的
    if (urlPatientId && urlDiseaseId) {
      setValue('diseaseId', urlDiseaseId);
    } 
    else if (contextPatientId && contextDiseaseId) {
      setValue('diseaseId', contextDiseaseId);
    }
  }, [searchParams, contextPatientId, contextDiseaseId, setValue]);
  
  // 监听记录类型变化，应用模板
  const primaryType = watch('primaryType');
  
  // 当主要记录类型变化时，应用相应模板
  useEffect(() => {
    if (primaryType) {
      // 如果主标签有对应的模板，使用模板内容
      if (RECORD_TYPE_TEMPLATES[primaryType]) {
        // 仅当主标签改变时，更新模板内容
        setValue('title', RECORD_TYPE_TEMPLATES[primaryType]!.title);
        setValue('content', RECORD_TYPE_TEMPLATES[primaryType]!.content);
      } else {
        // 如果主标签没有对应的模板，清空标题和内容
        setValue('title', '');
        setValue('content', '');
      }
    }
  }, [primaryType, setValue]);
  
  // 监听记录类型重置事件
  useEffect(() => {
    const handleRecordTypeReset = (event: Event) => {
      console.log('记录类型重置事件触发，清空标题和内容');
      // 清空标题和内容
      setValue('title', '');
      setValue('content', '');
    };
    
    // 添加事件监听
    document.addEventListener('recordTypeReset', handleRecordTypeReset);
    
    // 组件卸载时移除监听
    return () => {
      document.removeEventListener('recordTypeReset', handleRecordTypeReset);
    };
  }, [setValue]);
  
  // 其他状态
  const [expanded, setExpanded] = useState(false);
  const [attachments, setAttachments] = useState<Attachment[]>([]);
  const [notification, setNotification] = useState<Notification>({
    open: false,
    message: '',
    type: 'success'
  });
  
  // 用户权限/等级限制
  const [userLevelLimits] = useState<UserLevelLimits>({
    maxAttachmentSize: 5120, // 默认5MB
    maxQuantity: 5, // 默认最多5个附件
    maxTotalStorage: 20480, // 默认20MB总容量
  });
  
  // 计算已用附件总大小（KB）
  const totalAttachmentSize = attachments.reduce((total, attachment) => {
    return total + Math.ceil(attachment.file.size / 1024);
  }, 0);
  
  // 获取患者列表
  const { data: patients, isLoading: isLoadingPatients } = useQuery({
    queryKey: ['patients'],
    queryFn: () => getPatients(),
    enabled: !!token
  });
  
  // 获取疾病列表
  const { data: diseases, isLoading: isLoadingDiseases } = useQuery({
    queryKey: ['diseases'],
    queryFn: () => getDiseases(),
    enabled: !!token
  });
  
  // 根据选择的患者id过滤疾病列表
  const selectedPatientId = watch('patientId');
  const selectedDiseaseId = watch('diseaseId');
  const selectedNode = watch('stageNode');
  const filteredDiseases = diseases?.filter((disease: any) => disease.patientId === selectedPatientId) || [];
  
  // 将表单的患者和病理选择同步到Context中
  useEffect(() => {
    if (selectedPatientId) {
      setSelectedPatient(selectedPatientId);
    }
  }, [selectedPatientId, setSelectedPatient]);
  
  useEffect(() => {
    if (selectedDiseaseId) {
      setSelectedDisease(selectedDiseaseId);
    }
  }, [selectedDiseaseId, setSelectedDisease]);
  
  // 检查所选节点是否已存在记录
  useEffect(() => {
    // 仅当选择了患者、病理和节点时执行检查
    if (selectedPatientId && selectedDiseaseId && selectedNode) {
      // 这里应该调用API检查该节点是否已存在记录
      // 例如：checkExistingNode(selectedDiseaseId, selectedNode)
      // 此处简化为模拟API调用
      console.log(`检查节点 ${selectedNode} 是否已存在记录`);
      
      // 模拟检查结果 - 实际项目中应替换为真实API调用
      const simulateCheckNode = async () => {
        try {
          // 假设这是API调用
          // const response = await apiClient.get(`/records/check-node?diseaseId=${selectedDiseaseId}&node=${selectedNode}`);
          // const exists = response.data.exists;
          
          // 模拟此节点已存在记录的情况
          const mockExists = Math.random() > 0.7; // 30%概率已存在，仅用于演示
          
          if (mockExists) {
            // 显示警告通知
            setNotification({
              open: true,
              message: `节点 "${StageNodeNames[selectedNode as StageNodeEnum]}" 已存在记录，请修改已有记录或选择其他节点`,
              type: 'warning'
            });
            
            // 可以选择清除当前选择
            // onChange('');
            // 或者跳转到编辑该记录的页面
            // navigate(`/records/edit?node=${selectedNode}&diseaseId=${selectedDiseaseId}`);
          }
        } catch (error) {
          console.error('检查节点记录失败', error);
        }
      };
      
      simulateCheckNode();
    }
  }, [selectedPatientId, selectedDiseaseId, selectedNode]);
  
  // 创建记录的mutation
  const createRecordMutation = useMutation({
    mutationFn: (data: any) => createRecord(data),
    onSuccess: async (data) => {
      // 如果有附件，上传附件
      if (attachments.length > 0) {
        try {
          for (const attachment of attachments) {
            await uploadAttachment(data.id, attachment.file, userLevelLimits);
          }
          
          setNotification({
            open: true,
            message: '记录创建成功，所有附件已上传',
            type: 'success'
          });
        } catch (error: any) {
          setNotification({
            open: true,
            message: `记录创建成功，但附件上传失败: ${error.message}`,
            type: 'warning'
          });
        }
      } else {
        setNotification({
          open: true,
          message: '记录创建成功',
          type: 'success'
        });
      }
      
      // 重置表单
      setAttachments([]);
      setExpanded(false);
    },
    onError: (error: any) => {
      setNotification({
        open: true,
        message: `创建记录失败: ${error.message}`,
        type: 'error'
      });
    }
  });
  
  // 提交表单
  const onSubmit = (data: FormData) => {
    // 检查primaryType是否在recordType中
    if (data.primaryType && !data.recordType.includes(data.primaryType)) {
      // 如果主要类型不在选择的类型中，则添加到记录类型中
      data.recordType.push(data.primaryType);
    }

    // 检查并确保枚举值正确
    const recordData = {
      ...data,
      userId,
      createdBy: userId,
      // 确保stageNode和stagePhase为正确的枚举值
      stageNode: data.stageNode || null,
      // 如果有节点，根据节点确定阶段（数据库写入时自动处理阶段信息）
      stagePhase: data.stageNode 
        ? NODE_TO_PHASE_MAP[data.stageNode as StageNodeEnum] // 使用节点对应的阶段
        : (data.stagePhase || null), // 如果没有节点，使用选择的阶段或null
      // 确保其他枚举值也是正确的
      recordType: data.recordType || [],
      primaryType: data.primaryType || null,
      // 直接使用枚举值，不再转换为数字
      severity: data.severity || SeverityEnum.MODERATE, // 默认为中等
      data: {} // 预留给AI分析的结构化数据
    };
    
    console.log('提交记录数据:', recordData);
    createRecordMutation.mutate(recordData);
  };
  
  // 处理附件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!event.target.files || event.target.files.length === 0) return;
    
    const newFiles = Array.from(event.target.files);
    
    // 检查附件数量限制
    if (attachments.length + newFiles.length > userLevelLimits.maxQuantity) {
      setNotification({
        open: true,
        message: `附件数量超过限制 (最多${userLevelLimits.maxQuantity}个)`,
        type: 'error'
      });
      return;
    }
    
    // 检查附件大小限制
    const oversizedFiles = newFiles.filter(file => file.size > userLevelLimits.maxAttachmentSize * 1024);
    if (oversizedFiles.length > 0) {
      setNotification({
        open: true,
        message: `文件大小超过限制 (最大${userLevelLimits.maxAttachmentSize}KB): ${oversizedFiles.map(f => f.name).join(', ')}`,
        type: 'error'
      });
      return;
    }
    
    // 检查总容量限制
    const newTotalSize = totalAttachmentSize + newFiles.reduce((size, file) => size + Math.ceil(file.size / 1024), 0);
    if (newTotalSize > userLevelLimits.maxTotalStorage) {
      setNotification({
        open: true,
        message: `总容量超过限制 (最大${userLevelLimits.maxTotalStorage}KB)`,
        type: 'error'
      });
      return;
    }
    
    // 处理上传的文件
    const newAttachments = newFiles.map(file => {
      // 对于图片类型生成预览
      const preview = file.type.startsWith('image/') 
        ? URL.createObjectURL(file)
        : undefined;
        
      return { file, preview };
    });
    
    setAttachments([...attachments, ...newAttachments]);
    
    // 重置input值，允许再次选择相同文件
    event.target.value = '';
  };
  
  // 删除附件
  const handleDeleteAttachment = (index: number) => {
    const newAttachments = [...attachments];
    // 如果有预览URL，释放资源
    if (newAttachments[index].preview) {
      URL.revokeObjectURL(newAttachments[index].preview!);
    }
    newAttachments.splice(index, 1);
    setAttachments(newAttachments);
  };
  
  // 处理高级选项展开/折叠
  const handleExpandChange = () => {
    setExpanded(!expanded);
  };
  
  // 关闭通知
  const handleCloseNotification = () => {
    setNotification({
      ...notification,
      open: false
    });
  };
  
  // 处理身体状况严重程度变化
  const handleSeverityChange = (severityEnum: SeverityEnum, numValue: number) => {
    // 直接设置枚举值
    setValue("severity", severityEnum);
  };
  
  // 清理预览URL资源
  useEffect(() => {
    return () => {
      attachments.forEach(attachment => {
        if (attachment.preview) {
          URL.revokeObjectURL(attachment.preview);
        }
      });
    };
  }, [attachments]);
  
  // 处理记录类型更改
  const handleRecordTypeChange = (types: RecordTypeEnum[]) => {
    setValue('recordType', types);
    
    // 如果只有一个类型，自动设置为主要类型
    if (types.length === 1) {
      setValue('primaryType', types[0]);
    }
    // 如果当前主要类型不在选择的类型中，更新主要类型为第一个选择的类型
    else if (types.length > 0 && !types.includes(watch('primaryType'))) {
      setValue('primaryType', types[0]);
    }
  };
  
  return (
    <Box sx={{ 
      m: 0,
      width: '100%' 
    }}>
      <Box sx={{ mt: '20px', px: '10px' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0, alignItems: 'flex-end' }}>
          <Typography 
            variant="h5" 
            component="h1" 
            sx={{ 
              fontWeight: 500,
              fontSize: { xs: '1.1rem', sm: '1.3rem' },
              mb: 0
            }}
          >
            创建新记录
          </Typography>
          <Box sx={{ flexShrink: 0, ml: 3 }}>
            <Button 
              component={Link} 
              to="/records/manage" 
              startIcon={<ManageSearchIcon />}
              color="primary"
              variant="contained"
              sx={{ 
                minWidth: 80, 
                px: 2, 
                mb: '3px', 
                fontSize: '0.75rem' 
              }}
            >
              管理
            </Button>
          </Box>
        </Box>
        <Divider sx={{ mb: { xs: 2, md: 3 } }} />
      </Box>
      
      <Paper 
        elevation={0} 
        sx={{ 
          p: { xs: 2, md: 3 }, 
          mb: { xs: 2, md: 3 },
          pl: '10px',
          pr: '10px',
          '--Paper-shadow': 'none',
          borderLeftWidth: 0,
          borderRightWidth: 0,
          borderTopWidth: 0,
          borderBottomWidth: 0
        }}
      >
        <form onSubmit={handleSubmit(onSubmit)}>
          {/* 患者和病理选择行 */}
          <Box sx={{ display: 'flex', flexDirection: 'row', gap: 2, mb: 3 }}>
            {/* 患者选择 */}
            <Box sx={{ flex: 1 }}>
              <Controller
                name="patientId"
                control={control}
                rules={{ required: '请选择患者' }}
                render={({ field }) => (
                  <FormControl fullWidth error={!!errors.patientId} size="small">
                    <InputLabel>患者</InputLabel>
                    <Select
                      {...field}
                      label="患者"
                      disabled={isLoadingPatients}
                      startAdornment={
                        isLoadingPatients ? <CircularProgress size={20} sx={{ mr: 1 }} /> : null
                      }
                    >
                      {patients?.map((patient: any) => (
                        <MenuItem key={patient.id} value={patient.id}>
                          {patient.name}
                        </MenuItem>
                      ))}
                    </Select>
                    {errors.patientId && (
                      <Typography color="error" variant="caption">
                        {errors.patientId.message}
                      </Typography>
                    )}
                  </FormControl>
                )}
              />
            </Box>
            
            {/* 病理选择 */}
            <Box sx={{ flex: 1 }}>
              <Controller
                name="diseaseId"
                control={control}
                rules={{ required: '请选择病理' }}
                render={({ field }) => (
                  <FormControl fullWidth error={!!errors.diseaseId} size="small">
                    <InputLabel>病理</InputLabel>
                    <Select
                      {...field}
                      label="病理"
                      disabled={!selectedPatientId || isLoadingDiseases}
                      startAdornment={
                        isLoadingDiseases ? <CircularProgress size={20} sx={{ mr: 1 }} /> : null
                      }
                    >
                      {filteredDiseases.length > 0 ? (
                        filteredDiseases.map((disease: any) => (
                          <MenuItem key={disease.id} value={disease.id}>
                            {disease.name}
                          </MenuItem>
                        ))
                      ) : (
                        <MenuItem disabled value="">
                          {selectedPatientId ? '该患者暂无病理记录' : '请先选择患者'}
                        </MenuItem>
                      )}
                    </Select>
                    {errors.diseaseId && (
                      <Typography color="error" variant="caption">
                        {errors.diseaseId.message}
                      </Typography>
                    )}
                  </FormControl>
                )}
              />
            </Box>
          </Box>
          
          <Divider sx={{ my: 2 }} />
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="subtitle1">
              病程阶段
            </Typography>
            <Tooltip title="请准确选择病理所处的阶段和节点，这将用于记录分类和病史跟踪">
              <Box component="span" sx={{ 
                display: 'inline-flex', 
                alignItems: 'center',
                color: 'text.secondary',
                fontSize: '0.75rem'
              }}>
                <InfoIcon fontSize="small" sx={{ mr: 0.5 }} />
                选择提示
              </Box>
            </Tooltip>
          </Box>
          
          {/* 记录阶段和节点 - 可视化选择器 */}
          <Box sx={{ mb: 3 }}>
            <Controller
              name="stageNode"
              control={control}
              render={({ field: { value, onChange, ...field } }) => (
                <Controller
                  name="stagePhase"
                  control={control}
                  render={({ field: { value: phaseValue, onChange: phaseChange, ...phaseField } }) => (
                    <StageSelector
                      selectedNode={value ? value as StageNodeEnum : null}
                      selectedPhase={phaseValue ? phaseValue as StagePhaseEnum : null}
                      onNodeChange={(node) => {
                        onChange(node || '');
                        // 当选择节点时，自动标记为重要记录
                        // 当取消选择节点时，取消重要标记
                        setValue('isImportant', !!node);
                      }}
                      onPhaseChange={(phase) => {
                        phaseChange(phase || '');
                        // 对于仅选择阶段的情况，不自动标记为重要
                        // 保持isImportant原有状态
                      }}
                      error={errors.stageNode?.message || errors.stagePhase?.message}
                      helperText=""
                      {...field}
                      {...phaseField}
                    />
                  )}
                />
              )}
            />
          </Box>
          
          <Divider sx={{ my: 2 }} />
          {/* 删除记录类型标题，防止与TypeTagSelector组件自身标题重复 */}
          {/* <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="subtitle1">
              记录类型
            </Typography>
            <Tooltip title="请选择1-4个记录类型，并指定主要记录类型用于分类和显示">
              <Box component="span" sx={{ 
                display: 'inline-flex', 
                alignItems: 'center',
                color: 'text.secondary',
                fontSize: '0.75rem'
              }}>
                <InfoIcon fontSize="small" sx={{ mr: 0.5 }} />
                类型说明
              </Box>
            </Tooltip>
          </Box> */}
          
          {/* 记录类型选择 - 使用TypeTagSelector组件 */}
          <Box sx={{ mb: 3 }}>
            <Controller
              name="recordType"
              control={control}
              rules={{ 
                required: '请至少选择一个记录类型',
                validate: value => 
                  (value && value.length > 0 && value.length <= 4) || 
                  '请选择1-4个记录类型'
              }}
              render={({ field: { value, onChange } }) => (
                <Controller
                  name="primaryType"
                  control={control}
                  rules={{ 
                    required: '请选择主要记录类型',
                    validate: value => {
                      const recordTypes = watch('recordType');
                      return (recordTypes.includes(value)) || 
                        '主要类型必须在已选记录类型中';
                    }
                  }}
                  render={({ field: { value: primaryValue, onChange: primaryChange } }) => (
                    <TypeTagSelector
                      selectedTypes={value || []}
                      onChange={(types) => {
                        handleRecordTypeChange(types);
                      }}
                      primaryType={primaryValue || null}
                      onPrimaryTypeChange={(type) => {
                        primaryChange(type);
                      }}
                      error={errors.recordType?.message || errors.primaryType?.message}
                      helperText="请选择1-4个标签，1个红色主标签，单击选择，双击取消。选择标签后会自动提供填写模板。"
                    />
                  )}
                />
              )}
            />
          </Box>
          
          <Divider sx={{ my: 2 }} />
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="subtitle1">
              记录内容
            </Typography>
          </Box>
          
          {/* 记录标题、内容和日期 - 改为垂直布局 */}
          {/* 标题 - 第一行 */}
          <Box sx={{ mb: 2 }}>
            <Controller
              name="title"
              control={control}
              rules={{ required: '请输入记录标题' }}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="标题"
                  fullWidth
                  size="small"
                  error={!!errors.title}
                  helperText={errors.title?.message || "简明扼要，字数不超10字"}
                  FormHelperTextProps={{
                    sx: { 
                      m: 0, 
                      fontSize: '0.6rem', 
                      lineHeight: 1.1, 
                      opacity: 0.85,
                      px: 0.5,
                      color: errors.title ? 'error.main' : 'text.secondary' 
                    }
                  }}
                  InputProps={{
                    startAdornment: (
                      <Box component="span" sx={{ mr: 1, color: 'text.secondary' }}>
                        T
                      </Box>
                    ),
                  }}
                />
              )}
            />
          </Box>
          
          {/* 记录内容 - 第二行 */}
          <Box sx={{ mb: 2 }}>
            <Controller
              name="content"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="内容"
                  multiline
                  rows={8}
                  fullWidth
                  size="small"
                  placeholder="请输入记录内容..."
                  helperText="详细描述记录的内容，可以包括症状、治疗方案、医嘱等信息"
                  FormHelperTextProps={{
                    sx: { 
                      m: 0, 
                      fontSize: '0.6rem', 
                      lineHeight: 1.1, 
                      opacity: 0.85,
                      px: 0.5,
                      color: 'text.secondary' 
                    }
                  }}
                  InputProps={{
                    startAdornment: (
                      <Box component="span" sx={{ mr: 1, mt: 2, color: 'text.secondary' }}>
                        ≡
                      </Box>
                    ),
                  }}
                />
              )}
            />
          </Box>
          
          {/* 日期时间 - 第三行 */}
          <Box sx={{ mb: 3 }}>
            <Controller
              name="recordDate"
              control={control}
              rules={{ required: '请选择记录日期' }}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="记录日期和时间"
                  type="datetime-local"
                  fullWidth
                  size="small"
                  InputLabelProps={{ shrink: true }}
                  error={!!errors.recordDate}
                  helperText={errors.recordDate?.message || "记录的日期和时间"}
                  FormHelperTextProps={{
                    sx: { 
                      m: 0, 
                      fontSize: '0.6rem', 
                      lineHeight: 1.1, 
                      opacity: 0.85,
                      px: 0.5,
                      color: errors.recordDate ? 'error.main' : 'text.secondary' 
                    }
                  }}
                />
              )}
            />
          </Box>
          
          {/* 高级选项折叠面板 */}
          <Accordion 
            expanded={expanded} 
            onChange={handleExpandChange}
            elevation={0}
            sx={{ 
              border: '1px solid',
              borderColor: 'divider',
              mb: 3,
              '&::before': { display: 'none' },
              borderRadius: 1,
              overflow: 'hidden'
            }}
          >
            <AccordionSummary 
              expandIcon={<ExpandMoreIcon />}
              sx={{ 
                backgroundColor: expanded ? 'rgba(0, 0, 0, 0.03)' : 'transparent',
                transition: 'background-color 0.2s ease'
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <SettingsIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="subtitle2">高级选项</Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              {/* 记录属性 */}
              <Box sx={{ 
                display: 'flex',
                flexDirection: { xs: 'column', sm: 'row' },
                justifyContent: 'space-between',
                alignItems: { xs: 'flex-start', sm: 'center' },
                mb: 2,
                p: 1.5,
                backgroundColor: 'background.paper',
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 1
              }}>
                <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                  <Typography variant="subtitle2" gutterBottom>记录属性</Typography>
                  <Box sx={{ display: 'flex', gap: 2 }}>
                    {/* 重要性开关 */}
                    <Controller
                      name="isImportant"
                      control={control}
                      render={({ field: { value, onChange, ...field } }) => (
                        <FormControlLabel
                          {...field}
                          control={
                            <Switch 
                              checked={value} 
                              onChange={onChange} 
                              color="error" 
                              size="small"
                            />
                          }
                          label={
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <BookmarkIcon fontSize="small" sx={{ mr: 0.5, color: value ? 'error.main' : 'text.secondary' }} />
                              <Typography variant="body2">重要</Typography>
                            </Box>
                          }
                        />
                      )}
                    />
                    
                    {/* 隐私开关 */}
                    <Controller
                      name="isPrivate"
                      control={control}
                      render={({ field: { value, onChange, ...field } }) => (
                        <FormControlLabel
                          {...field}
                          control={
                            <Switch 
                              checked={value} 
                              onChange={onChange} 
                              color="primary" 
                              size="small"
                            />
                          }
                          label={
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <LockIcon fontSize="small" sx={{ mr: 0.5, color: value ? 'primary.main' : 'text.secondary' }} />
                              <Typography variant="body2">私密</Typography>
                            </Box>
                          }
                        />
                      )}
                    />
                  </Box>
                </Box>
              </Box>
                
              {/* 身体情况滑块 - 独立一行 */}
              <Box sx={{ 
                width: '100%',
                mb: 3,
                p: 3,
                backgroundColor: 'background.paper',
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 1,
                boxShadow: '0 1px 3px rgba(0,0,0,0.05)'
              }}>
                <Controller
                  name="severity"
                  control={control}
                  render={({ field }) => (
                    <SeveritySlider
                      value={field.value}
                      onChange={handleSeverityChange}
                      disabled={false}
                    />
                  )}
                />
              </Box>

              {/* 自定义标签 */}
              <Controller
                name="customTags"
                control={control}
                render={({ field }) => (
                  <SimpleTagInput
                    value={field.value}
                    onChange={field.onChange}
                    helperText="输入标签，按Enter添加，可选择已有标签或创建新标签"
                  />
                )}
              />
              
              {/* 附件上传区域 */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                  <AttachFileIcon fontSize="small" sx={{ mr: 0.5 }} />
                  附件上传
                </Typography>
                <Box sx={{ 
                  display: 'flex', 
                  flexDirection: { xs: 'column', sm: 'row' }, 
                  alignItems: { xs: 'flex-start', sm: 'center' }, 
                  mb: 1,
                  gap: 2
                }}>
                  <Button
                    variant="outlined"
                    component="label"
                    startIcon={<UploadFileIcon />}
                    size="small"
                    disabled={attachments.length >= userLevelLimits.maxQuantity}
                    color="primary"
                  >
                    选择文件
                    <input
                      type="file"
                      hidden
                      multiple
                      onChange={handleFileUpload}
                    />
                  </Button>
                  <Typography variant="body2" color="text.secondary">
                    已上传: {attachments.length}/{userLevelLimits.maxQuantity} 个文件
                    ({Math.round(totalAttachmentSize/1024 * 10) / 10} MB / {Math.round(userLevelLimits.maxTotalStorage/1024 * 10) / 10} MB)
                  </Typography>
                </Box>
                
                {/* 文件限制提示 */}
                <Alert severity="info" sx={{ mb: 2 }} variant="outlined" icon={<InfoIcon fontSize="small" />}>
                  <Typography variant="caption">
                    支持的文件类型: 图片、PDF、文档等 | 
                    单个文件最大: {Math.round(userLevelLimits.maxAttachmentSize/1024 * 10) / 10} MB | 
                    总容量上限: {Math.round(userLevelLimits.maxTotalStorage/1024 * 10) / 10} MB
                  </Typography>
                </Alert>
                
                {/* 附件列表 */}
                {attachments.length > 0 && (
                  <Box sx={{ mt: 2 }}>
                    <Stack spacing={1}>
                      {attachments.map((attachment, index) => (
                        <Box
                          key={index}
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            p: 1,
                            border: '1px solid',
                            borderColor: 'divider',
                            borderRadius: 1,
                            backgroundColor: 'background.paper'
                          }}
                        >
                          {/* 预览缩略图 */}
                          {attachment.preview && (
                            <Box
                              component="img"
                              src={attachment.preview}
                              alt={attachment.file.name}
                              sx={{
                                width: 40,
                                height: 40,
                                objectFit: 'cover',
                                mr: 2,
                                borderRadius: 1,
                              }}
                            />
                          )}
                          
                          {/* 文件信息 */}
                          <Box sx={{ flexGrow: 1 }}>
                            <Typography variant="body2" noWrap>
                              {attachment.file.name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {(attachment.file.size / 1024 / 1024).toFixed(2)} MB
                            </Typography>
                          </Box>
                          
                          {/* 删除按钮 */}
                          <IconButton
                            size="small"
                            onClick={() => handleDeleteAttachment(index)}
                            aria-label="删除附件"
                            color="error"
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Box>
                      ))}
                    </Stack>
                  </Box>
                )}
              </Box>
            </AccordionDetails>
          </Accordion>
          
          {/* 表单提交按钮 */}
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'space-between',
            border: '1px solid',
            borderColor: 'divider',
            borderRadius: 1,
            p: 2,
            backgroundColor: 'background.paper',
            mb: 2
          }}>
            <Button
              variant="outlined"
              onClick={() => navigate(-1)}
              size="medium"
              startIcon={<ArrowBackIcon />}
            >
              返回
            </Button>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              size="medium"
              disabled={createRecordMutation.isPending}
              startIcon={createRecordMutation.isPending ? <CircularProgress size={20} /> : <ListAltIcon />}
            >
              {createRecordMutation.isPending ? '保存中...' : '保存记录'}
            </Button>
          </Box>
        </form>
      </Paper>
      
      {/* 操作结果通知 */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default RecordCreatePage; 