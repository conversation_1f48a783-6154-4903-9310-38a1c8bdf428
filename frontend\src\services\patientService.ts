import apiClient from './apiClient';
import { Patient } from '../types/patient';
import { logApiError } from '../utils/apiErrorMonitor';
import { useAuthStore } from '../store/authStore';

/**
 * 获取患者列表
 */
export const getPatients = async (): Promise<Patient[]> => {
  try {
    // 检查当前用户角色
    const authStore = useAuthStore.getState();
    const userRole = authStore.user?.role;
    const isServiceUser = userRole === 'SERVICE' || userRole === 'ADMIN';

    // 检查当前操作模式
    const operationMode = localStorage.getItem('operation_mode') || 'NORMAL';
    const isServiceContext = operationMode === 'SERVICE' || operationMode === 'ADMIN';

    console.log('[患者服务] 获取患者列表', isServiceContext ? '(服务上下文)' : '(普通用户上下文)');

    // 使用API配置中心获取路径
    const path = '/api/patients';

    // 仅当用户是服务用户且当前在服务上下文模式时，才添加service_context参数
    const params: any = {};
    if (isServiceUser && isServiceContext) {
      params.service_context = true;
      console.log('[患者服务] 添加服务上下文参数 service_context=true');
    } else {
      console.log('[患者服务] 普通用户上下文模式，不添加service_context参数');
    }

    console.log(`[患者服务] 使用路径: ${path}`);

    // 使用统一的API客户端发送请求
    const response = await apiClient.get(path, { params });

    // 确保返回的数据是数组
    const responseData = response.data;
    if (!Array.isArray(responseData)) {
      console.warn('[患者服务] API返回的数据不是数组，返回空数组代替:', responseData);
      return [];
    }

    return responseData;
  } catch (error) {
    console.error('[患者服务] 获取患者列表失败:', error);
    logApiError(error);
    return [];
  }
};

/**
 * 获取单个患者详情
 */
export const getPatient = async (id: string) => {
  try {
    // 检查当前用户角色
    const authStore = useAuthStore.getState();
    const userRole = authStore.user?.role;
    const isServiceUser = userRole === 'SERVICE' || userRole === 'ADMIN';

    // 检查当前操作模式
    const operationMode = localStorage.getItem('operation_mode') || 'NORMAL';
    const isServiceContext = operationMode === 'SERVICE' || operationMode === 'ADMIN';

    console.log(`[患者服务] 获取患者详情: ${id}`, isServiceContext ? '(服务上下文)' : '(普通用户上下文)');

    // 使用API配置中心获取路径
    const path = `/patients/${id}`;

    // 仅当用户是服务用户且当前在服务上下文模式时，才添加service_context参数
    const params: any = {};
    if (isServiceUser && isServiceContext) {
      params.service_context = true;
      console.log('[患者服务] 添加服务上下文参数 service_context=true');
    } else {
      console.log('[患者服务] 普通用户上下文模式，不添加service_context参数');
    }

    console.log(`[患者服务] 使用路径: ${path}`);

    // 使用统一的API客户端发送请求
    const response = await apiClient.get(path, { params });
    return response.data;
  } catch (error) {
    console.error(`[患者服务] 获取患者(${id})详情失败:`, error);
    logApiError(error);
    throw error;
  }
};

/**
 * 创建新患者
 */
export const createPatient = async (patient: Omit<Patient, 'id' | 'createdAt' | 'updatedAt'>) => {
  try {
    // 检查当前用户角色
    const authStore = useAuthStore.getState();
    const userRole = authStore.user?.role;
    const isServiceUser = userRole === 'SERVICE' || userRole === 'ADMIN';

    // 检查当前操作模式
    const operationMode = localStorage.getItem('operation_mode') || 'NORMAL';
    const isServiceContext = operationMode === 'SERVICE' || operationMode === 'ADMIN';

    console.log('[患者服务] 创建新患者', isServiceContext ? '(服务上下文)' : '(普通用户上下文)');

    // 使用API配置中心获取路径
    const path = '/api/patients';

    // 仅当用户是服务用户且当前在服务上下文模式时，才添加service_context参数
    const params: any = {};
    if (isServiceUser && isServiceContext) {
      params.service_context = true;
      console.log('[患者服务] 添加服务上下文参数 service_context=true');
    } else {
      console.log('[患者服务] 普通用户上下文模式，不添加service_context参数');
    }

    console.log(`[患者服务] 使用路径: ${path}`);

    // 使用统一的API客户端发送请求
    const response = await apiClient.post(path, patient, { params });
    return response.data;
  } catch (error) {
    console.error('[患者服务] 创建患者失败:', error);
    logApiError(error);
    throw error;
  }
};

/**
 * 删除患者（软删除）
 */
export const deletePatient = async (id: string) => {
  try {
    // 检查当前用户角色
    const authStore = useAuthStore.getState();
    const userRole = authStore.user?.role;
    const isServiceUser = userRole === 'SERVICE' || userRole === 'ADMIN';

    // 检查当前操作模式
    const operationMode = localStorage.getItem('operation_mode') || 'NORMAL';
    const isServiceContext = operationMode === 'SERVICE' || operationMode === 'ADMIN';

    console.log(`[患者服务] 删除患者: ${id}`, isServiceContext ? '(服务上下文)' : '(普通用户上下文)');

    // 使用API配置中心获取路径
    const path = `/api/patients/${id}`;

    // 仅当用户是服务用户且当前在服务上下文模式时，才添加service_context参数
    const params: any = {};
    if (isServiceUser && isServiceContext) {
      params.service_context = true;
      console.log('[患者服务] 添加服务上下文参数 service_context=true');
    } else {
      console.log('[患者服务] 普通用户上下文模式，不添加service_context参数');
    }

    console.log(`[患者服务] 使用路径: ${path}`);

    // 使用统一的API客户端发送请求
    const response = await apiClient.delete(path, { params });
    return response.data;
  } catch (error) {
    console.error(`[患者服务] 删除患者(${id})失败:`, error);
    logApiError(error);
    throw error;
  }
};

/**
 * 更新患者信息
 */
export const updatePatient = async (id: string, patient: Partial<Patient>) => {
  try {
    // 检查当前用户角色
    const authStore = useAuthStore.getState();
    const userRole = authStore.user?.role;
    const isServiceUser = userRole === 'SERVICE' || userRole === 'ADMIN';

    // 检查当前操作模式
    const operationMode = localStorage.getItem('operation_mode') || 'NORMAL';
    const isServiceContext = operationMode === 'SERVICE' || operationMode === 'ADMIN';

    console.log(`[患者服务] 更新患者信息: ${id}`, isServiceContext ? '(服务上下文)' : '(普通用户上下文)');

    // 使用API配置中心获取路径
    const path = `/api/patients/${id}`;

    // 仅当用户是服务用户且当前在服务上下文模式时，才添加service_context参数
    const params: any = {};
    if (isServiceUser && isServiceContext) {
      params.service_context = true;
      console.log('[患者服务] 添加服务上下文参数 service_context=true');
    } else {
      console.log('[患者服务] 普通用户上下文模式，不添加service_context参数');
    }

    console.log(`[患者服务] 使用路径: ${path}`);

    // 使用统一的API客户端发送请求
    const response = await apiClient.put(path, patient, { params });
    return response.data;
  } catch (error) {
    console.error(`[患者服务] 更新患者(${id})失败:`, error);
    logApiError(error);
    throw error;
  }
};

/**
 * 设置为本人
 */
export const setPrimaryPatient = async (id: string) => {
  try {
    // 检查当前用户角色
    const authStore = useAuthStore.getState();
    const userRole = authStore.user?.role;
    const isServiceUser = userRole === 'SERVICE' || userRole === 'ADMIN';

    // 检查当前操作模式
    const operationMode = localStorage.getItem('operation_mode') || 'NORMAL';
    const isServiceContext = operationMode === 'SERVICE' || operationMode === 'ADMIN';

    console.log(`[患者服务] 设置患者为主要患者: ${id}`, isServiceContext ? '(服务上下文)' : '(普通用户上下文)');

    // 构建API路径
    const path = `/api/patients/${id}/primary`;

    // 仅当用户是服务用户且当前在服务上下文模式时，才添加service_context参数
    const params: any = {};
    if (isServiceUser && isServiceContext) {
      params.service_context = true;
      console.log('[患者服务] 添加服务上下文参数 service_context=true');
    } else {
      console.log('[患者服务] 普通用户上下文模式，不添加service_context参数');
    }

    console.log(`[患者服务] 使用路径: ${path}`);

    // 使用统一的API客户端发送请求
    const response = await apiClient.post(path, {}, { params });
    return response.data;
  } catch (error) {
    console.error(`[患者服务] 设置患者(${id})为主要患者失败:`, error);
    logApiError(error);
    throw error;
  }
};