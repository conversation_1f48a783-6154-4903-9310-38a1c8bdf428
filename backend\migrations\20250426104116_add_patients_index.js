/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.table('patients', function(table) {
    // 添加联合索引以提高查询性能
    table.index(['name', 'phone_number'], 'idx_patients_name_phone');
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.table('patients', function(table) {
    // 删除之前创建的索引
    table.dropIndex(['name', 'phone_number'], 'idx_patients_name_phone');
  });
};
