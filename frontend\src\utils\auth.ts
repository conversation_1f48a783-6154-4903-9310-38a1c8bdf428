/**
 * 认证相关工具函数，处理token存储和清除
 */

// token存储的key
const TOKEN_KEY = 'auth_token';

/**
 * 保存token到本地存储
 * @param token JWT token
 */
export const saveToken = (token: string): void => {
  localStorage.setItem(TOKEN_KEY, token);
};

/**
 * 从本地存储获取token
 * @returns JWT token或null
 */
export const getToken = (): string | null => {
  return localStorage.getItem(TOKEN_KEY);
};

/**
 * 清除保存的token
 */
export const clearToken = (): void => {
  localStorage.removeItem(TOKEN_KEY);
};

/**
 * 检查是否有token存在
 * @returns 是否已认证
 */
export const isAuthenticated = (): boolean => {
  const token = getToken();
  return !!token;
}; 