import React, { useState, useEffect } from 'react';
import { Box, Typography, Paper } from '@mui/material';
import { alpha } from '@mui/material/styles';
// import { usePatientDiseaseContext } from '../../../context/PatientDiseaseContext'; // REMOVE
import { getPatientDiseases } from '../../../services/diseaseService';
import dataCache from '../../../utils/dataCache';

// 关系类型和颜色映射
const RELATIONSHIP_COLORS = {
  'SELF': '#2196f3', // 蓝色 - 本人
  'FAMILY': '#4caf50', // 绿色 - 家属
  'RELATIVE': '#ff9800', // 橙色 - 亲戚
  'FRIEND': '#9c27b0', // 紫色 - 朋友
  'DEFAULT': '#e0e0e0' // 默认灰色
};

// 关系显示文本映射
const RELATIONSHIP_LABELS = {
  'SELF': '本人',
  'FAMILY': '家属',
  'RELATIVE': '亲戚',
  'FRIEND': '朋友',
  'DEFAULT': ''
};

// 获取关系颜色
const getRelationshipColor = (relationshipValue: string): string => {
  return RELATIONSHIP_COLORS[relationshipValue as keyof typeof RELATIONSHIP_COLORS] || RELATIONSHIP_COLORS.DEFAULT;
};

// 获取关系显示文本
const getRelationshipLabel = (relationshipValue: string): string => {
  return RELATIONSHIP_LABELS[relationshipValue as keyof typeof RELATIONSHIP_LABELS] || '';
};

// 患者类型定义
interface PatientProps {
  id: string;
  name: string;
  age: number;
  gender: 'male' | 'female';
  relationship: string;  // 关系类型：SELF, FAMILY, RELATIVE, FRIEND
  diseaseCount?: number;
  maxDiseases?: number;
  birthDate?: string;
  bloodType?: string;
  phoneNumber?: string;
  isSelected?: boolean;
  onClick?: (id: string) => void; // 添加点击回调函数属性
}

/**
 * 患者卡片组件
 * 用于在选择器中显示患者信息，并处理选择事件
 */
const PatientCard: React.FC<PatientProps> = ({ 
  id, 
  name, 
  age, 
  gender, 
  relationship,
  diseaseCount: initialDiseaseCount,
  maxDiseases = 5,
  birthDate,
  bloodType,
  phoneNumber,
  isSelected = false,
  onClick, // 接收外部传入的点击回调函数
}) => {
  const [diseaseCount, setDiseaseCount] = useState(initialDiseaseCount || 0);
  const [loading, setLoading] = useState(false);

  // 获取患者疾病数据
  useEffect(() => {
    if (initialDiseaseCount === undefined) {
      const fetchDiseases = async () => {
        try {
          setLoading(true);
          console.log(`为患者ID: ${id} 获取疾病数据`);
          const diseases = await getPatientDiseases(id);
          console.log(`获取到的疾病数据:`, diseases);
          
          if (Array.isArray(diseases)) {
            // 检查数据结构
            if (diseases.length > 0) {
              console.log('第一条疾病数据结构:', diseases[0]);
            }
            
            // 检查isActive属性是否为数字类型
            const hasIsActiveProperty = diseases.length > 0 && 'isActive' in diseases[0];
            console.log('疾病数据是否有isActive属性:', hasIsActiveProperty);
            
            // 尝试两种方式统计活跃病理
            let activeCount = 0;
            
            if (hasIsActiveProperty) {
              // 方法1：使用isActive === 1条件过滤
              activeCount = diseases.filter(d => d.isActive === 1).length;
              console.log('方法1 - 活跃疾病数量 (isActive === 1):', activeCount);
              
              // 尝试其他可能的值
              const activeCount2 = diseases.filter(d => d.isActive === true || d.isActive === "1" || d.isActive === "true").length;
              console.log('方法2 - 活跃疾病数量 (其他条件):', activeCount2);
              
              // 如果方法1没有找到任何活跃疾病，但方法2找到了，使用方法2的结果
              if (activeCount === 0 && activeCount2 > 0) {
                activeCount = activeCount2;
              }
            } else {
              // 方法3：如果没有isActive属性，则统计非归档(ARCHIVED)状态的疾病
              activeCount = diseases.filter(d => d.stage !== 'ARCHIVED').length;
              console.log('方法3 - 活跃疾病数量 (非归档状态):', activeCount);
            }
            
            console.log(`设置患者 ${name} (ID:${id}) 的最终活跃疾病数量:`, activeCount);
            setDiseaseCount(activeCount);
          } else {
            console.error('获取到的疾病数据不是数组:', diseases);
          }
        } catch (error) {
          console.error('获取患者病理信息失败:', error);
          // 如果有网络错误或API错误，显示默认值
          setDiseaseCount(0);
        } finally {
          setLoading(false);
        }
      };
      
      fetchDiseases();
    }
  }, [id, initialDiseaseCount, name]);

  // 处理点击事件
  const handleClick = () => {
    console.log('选择患者:', name, id);
    
    // 强制清除病理相关缓存
    dataCache.clearDiseaseRelatedCache();
    
    // 调用传入的点击处理函数
    if (onClick) onClick(id);
  };

  // 性别文本
  const genderText = gender === 'male' ? '男' : '女';

  // 关系标签文本
  const relationshipLabel = getRelationshipLabel(relationship);
  
  // 关系颜色
  const relationshipColor = getRelationshipColor(relationship);

  return (
    <Paper
      elevation={0}
      sx={{
        borderRadius: 2,
        border: '1px solid',
        borderColor: isSelected ? 'primary.main' : '#e0e0e0',
        backgroundColor: isSelected ? (theme) => alpha(theme.palette.primary.main, 0.15) : 'background.paper',
        cursor: 'pointer',
        transition: 'all 0.2s',
        '&:hover': {
          borderColor: 'primary.main',
          boxShadow: isSelected ? 0 : 1,
        },
        position: 'relative',
        overflow: 'hidden',
        display: 'flex',
        pl: 0, // 移除左侧内边距
      }}
      onClick={handleClick}
    >
      {/* 左侧彩条 */}
      <Box
        sx={{
          width: 6,
          bgcolor: relationshipColor,
        }}
      />
      
      <Box sx={{ flex: 1, p: 0.6 }}>
        {/* 患者名称 - 增加顶部间距 */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.1, mt: 0.3 }}>
          <Typography variant="subtitle1" sx={{ fontWeight: 600, fontSize: '0.85rem', lineHeight: 1 }}>
            {name}
          </Typography>
        </Box>
        
        {/* 关系标签 - 单独一行，标签形态 */}
        {relationshipLabel && (
          <Box sx={{ mb: 0.1 }}>
            <Typography 
              component="span" 
              sx={{ 
                fontSize: '0.48rem',
                color: relationshipColor,
                fontWeight: 500,
                backgroundColor: `${relationshipColor}15`,
                px: 0.5,
                py: 0.1,
                borderRadius: 0.8,
                display: 'inline-block'
              }}
            >
              {relationshipLabel}
            </Typography>
          </Box>
        )}
        
        {/* 患者基本信息 - 按行分开显示 */}
        <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.65rem', mb: 0, lineHeight: 1 }}>
          性别: {genderText}
        </Typography>
        
        <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.65rem', mb: 0, lineHeight: 1 }}>
          年龄: {age}
        </Typography>
        
        {bloodType && (
          <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.65rem', mb: 0, lineHeight: 1 }}>
            血型: {bloodType}
          </Typography>
        )}
        
        {/* 病理计数 - 左对齐 */}
        <Box sx={{ display: 'flex', justifyContent: 'flex-start', mt: 0.2 }}>
          <Typography 
            variant="body2" 
            color="primary"
            sx={{ 
              fontSize: '0.48rem',
              fontWeight: 500,
              lineHeight: 1
            }}
          >
            {loading ? '加载中...' : `病理 ${diseaseCount} / ${maxDiseases}`}
          </Typography>
        </Box>
      </Box>
    </Paper>
  );
};

export default PatientCard; 