import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';
// import { API_PATHS } from '../utils/apiConfig';
// import apiClient from '../utils/apiClient';
import { getUserStatistics } from '../services/statisticsService';
import { getUserProfile } from '../services/authService';
import { useAuthStore } from '../store/authStore';
import { registerAiUsageUpdateListener, unregisterAiUsageUpdateListener } from '../services/aiUsageService';

// AI使用相关的上下文接口
interface AiUsageContextType {
  aiUsageCount: number;         // 当前使用次数
  maxAiUsage: number;           // 最大使用次数限制
  isLoading: boolean;           // 加载状态
  error: string | null;         // 错误信息
  refreshAiUsage: () => Promise<void>; // 刷新AI使用数据的方法
  getUsagePercentage: () => number;    // 获取使用百分比
}

// 创建上下文
const AiUsageContext = createContext<AiUsageContextType | undefined>(undefined);

// 上下文提供者组件属性
interface AiUsageProviderProps {
  children: ReactNode;
}

// 上下文提供者组件
export const AiUsageProvider: React.FC<AiUsageProviderProps> = ({ children }) => {
  const [aiUsageCount, setAiUsageCount] = useState<number>(0);
  const [maxAiUsage, setMaxAiUsage] = useState<number>(3); // 默认值为3
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuthStore();

  // 获取用户AI使用数据
  const fetchAiUsageData = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // 从统计服务获取数据
      const statsData = await getUserStatistics();
      console.log('[AiUsageContext] 从统计服务获取的AI使用数据:', statsData);
      
      // 从用户资料获取数据
      const profileData = await getUserProfile();
      console.log('[AiUsageContext] 从用户资料获取的AI使用数据:', profileData);
      
      // 更新状态 - 优先使用统计服务中的userLimits数据
      if (statsData.userLimits?.maxAiUsed !== undefined) {
        setMaxAiUsage(statsData.userLimits.maxAiUsed);
      } else if (profileData.maxAiUsed !== undefined) {
        setMaxAiUsage(profileData.maxAiUsed);
      } else {
        // 根据用户级别设置默认值
        switch (user?.level) {
          case 'PERSONAL':
            setMaxAiUsage(3);
            break;
          case 'FAMILY':
            setMaxAiUsage(20);
            break;
          case 'PROFESSIONAL':
            setMaxAiUsage(50);
            break;
          default:
            setMaxAiUsage(3);
        }
      }
      
      // 设置当前使用次数
      if (profileData.aiUsageCount !== undefined) {
        setAiUsageCount(profileData.aiUsageCount);
      } else if (statsData.aiAnalysis?.current !== undefined) {
        setAiUsageCount(statsData.aiAnalysis.current);
      }
      
    } catch (err) {
      console.error('[AiUsageContext] 获取AI使用数据失败:', err);
      setError('获取AI使用数据失败');
    } finally {
      setIsLoading(false);
    }
  }, [user?.level]);

  // 刷新AI使用数据
  const refreshAiUsage = useCallback(async () => {
    await fetchAiUsageData();
  }, [fetchAiUsageData]);

  // 计算使用百分比
  const getUsagePercentage = useCallback((): number => {
    if (maxAiUsage <= 0) return 0;
    return Math.min(100, (aiUsageCount / maxAiUsage) * 100);
  }, [aiUsageCount, maxAiUsage]);

  // 当AI使用次数更新时的回调函数
  const handleAiUsageUpdate = useCallback(() => {
    console.log('[AiUsageContext] 收到AI使用次数更新通知，正在刷新数据...');
    fetchAiUsageData();
  }, [fetchAiUsageData]);

  // 首次加载和用户变化时获取数据
  useEffect(() => {
    if (user) {
      fetchAiUsageData();
    }
  }, [user, fetchAiUsageData]);

  // 注册和清理监听器
  useEffect(() => {
    // 注册监听器
    registerAiUsageUpdateListener(handleAiUsageUpdate);
    
    // 清理函数
    return () => {
      unregisterAiUsageUpdateListener(handleAiUsageUpdate);
    };
  }, [handleAiUsageUpdate]);

  // 提供的上下文值
  const contextValue: AiUsageContextType = {
    aiUsageCount,
    maxAiUsage,
    isLoading,
    error,
    refreshAiUsage,
    getUsagePercentage
  };

  return (
    <AiUsageContext.Provider value={contextValue}>
      {children}
    </AiUsageContext.Provider>
  );
};

// 自定义Hook，用于在组件中使用AI使用上下文
export const useAiUsage = (): AiUsageContextType => {
  const context = useContext(AiUsageContext);
  if (context === undefined) {
    throw new Error('useAiUsage必须在AiUsageProvider中使用');
  }
  return context;
}; 