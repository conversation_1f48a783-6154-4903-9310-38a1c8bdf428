import React from 'react';
import { 
  Box, 
  Paper, 
  Typography, 
  Chip,
  alpha,
  useTheme,
  // Divider // Removed unused import
} from '@mui/material';
import { 
  Lock as PrivateIcon
} from '@mui/icons-material';
// import { format, differenceInDays } from 'date-fns'; // Removed unused import 'differenceInDays'
import { format } from 'date-fns'; // Keep 'format'
import { zhCN } from 'date-fns/locale';
import './DiseaseCard.css'; // 导入CSS样式
import { DiseaseCardAISection } from '../ai-assistant';
import { useAuthStore } from '../../store/authStore';

// 定义病程阶段
export enum DiseaseStage {
  INITIAL = '初诊',
  DIAGNOSIS = '确诊',
  TREATMENT = '治疗',
  FOLLOW_UP = '随访',
  PROGNOSIS = '预后',
  CLOSED = '封档'
}

// 阶段颜色映射
const stageColors = {
  [DiseaseStage.INITIAL]: '#E53935', // 红色 - 初诊
  [DiseaseStage.DIAGNOSIS]: '#8E24AA', // 紫色 - 确诊
  [DiseaseStage.TREATMENT]: '#FB8C00', // 橙色 - 治疗
  [DiseaseStage.FOLLOW_UP]: '#FDD835', // 黄色 - 随访/恢复
  [DiseaseStage.PROGNOSIS]: '#43A047', // 绿色 - 预后
  [DiseaseStage.CLOSED]: '#757575', // 灰色 - 封档
  'default': '#757575' // 默认灰色
};

// 病程阶段顺序
const stageOrder = [
  DiseaseStage.INITIAL,
  DiseaseStage.DIAGNOSIS,
  DiseaseStage.TREATMENT,
  DiseaseStage.FOLLOW_UP,
  DiseaseStage.PROGNOSIS,
  DiseaseStage.CLOSED
];

// 阶段查找函数 - 增强版
const mapStageFromString = (stageString: string | null | undefined): DiseaseStage => {
  if (!stageString) return DiseaseStage.INITIAL;
  
  // 简化阶段映射逻辑
  const stageMap: Record<string, DiseaseStage> = {
    // 中文映射
    '初诊': DiseaseStage.INITIAL,
    '确诊': DiseaseStage.DIAGNOSIS,
    '治疗': DiseaseStage.TREATMENT,
    '随访': DiseaseStage.FOLLOW_UP,
    '预后': DiseaseStage.PROGNOSIS,
    '封档': DiseaseStage.CLOSED,
    
    // 英文映射 - 小写
    'initial': DiseaseStage.INITIAL,
    'initial_visit': DiseaseStage.INITIAL,
    'diagnosis': DiseaseStage.DIAGNOSIS,
    'treatment': DiseaseStage.TREATMENT,
    'follow_up': DiseaseStage.FOLLOW_UP,
    'followup': DiseaseStage.FOLLOW_UP,
    'follow-up': DiseaseStage.FOLLOW_UP,
    'prognosis': DiseaseStage.PROGNOSIS,
    'closed': DiseaseStage.CLOSED,
    'archive': DiseaseStage.CLOSED,
    
    // 英文映射 - 大写
    'INITIAL': DiseaseStage.INITIAL,
    'INITIAL_VISIT': DiseaseStage.INITIAL,
    'DIAGNOSIS': DiseaseStage.DIAGNOSIS,
    'TREATMENT': DiseaseStage.TREATMENT,
    'FOLLOW_UP': DiseaseStage.FOLLOW_UP,
    'FOLLOWUP': DiseaseStage.FOLLOW_UP,
    'PROGNOSIS': DiseaseStage.PROGNOSIS,
    'CLOSED': DiseaseStage.CLOSED,
    'ARCHIVE': DiseaseStage.CLOSED,
    
    // 可能的缩写
    'ini': DiseaseStage.INITIAL,
    'dia': DiseaseStage.DIAGNOSIS,
    'tre': DiseaseStage.TREATMENT,
    'fol': DiseaseStage.FOLLOW_UP,
    'pro': DiseaseStage.PROGNOSIS,
    'clo': DiseaseStage.CLOSED,
    'arc': DiseaseStage.CLOSED
  };
  
  // 转为小写以进行大小写不敏感匹配
  const lowerStage = stageString.toLowerCase();
  
  // 尝试直接匹配
  if (stageMap[stageString]) {
    return stageMap[stageString];
  }
  
  if (stageMap[lowerStage]) {
    return stageMap[lowerStage];
  }
  
  // 尝试部分匹配
  for (const [key, value] of Object.entries(stageMap)) {
    if (lowerStage.includes(key.toLowerCase())) {
      return value;
    }
  }
  
  // 尝试匹配关键词
  if (lowerStage.includes('初') || lowerStage.includes('init')) {
    return DiseaseStage.INITIAL;
  } else if (lowerStage.includes('诊') || lowerStage.includes('diag')) {
    return DiseaseStage.DIAGNOSIS;
  } else if (lowerStage.includes('治') || lowerStage.includes('treat')) {
    return DiseaseStage.TREATMENT;
  } else if (lowerStage.includes('随') || lowerStage.includes('访') || lowerStage.includes('follow')) {
    return DiseaseStage.FOLLOW_UP;
  } else if (lowerStage.includes('预') || lowerStage.includes('prog')) {
    return DiseaseStage.PROGNOSIS;
  } else if (lowerStage.includes('封') || lowerStage.includes('档') || lowerStage.includes('arch') || lowerStage.includes('closed')) {
    return DiseaseStage.CLOSED;
  }
  
  // 默认返回初诊
  return DiseaseStage.INITIAL;
};

// 从阶段相位文本映射到阶段枚举
const mapPhaseToStage = (phaseString: string | null | undefined): DiseaseStage => {
  if (!phaseString) return DiseaseStage.INITIAL;
  
  const phaseMap: Record<string, DiseaseStage> = {
    'INITIAL': DiseaseStage.INITIAL,
    'DIAGNOSIS': DiseaseStage.DIAGNOSIS,
    'TREATMENT': DiseaseStage.TREATMENT,
    'FOLLOW_UP': DiseaseStage.FOLLOW_UP,
    'FOLLOWUP': DiseaseStage.FOLLOW_UP,
    'PROGNOSIS': DiseaseStage.PROGNOSIS,
    'CLOSED': DiseaseStage.CLOSED,
    
    // 阶段期间映射
    'INITIAL_PHASE': DiseaseStage.INITIAL,
    'DIAGNOSIS_PHASE': DiseaseStage.DIAGNOSIS,
    'TREATMENT_PHASE': DiseaseStage.TREATMENT,
    'FOLLOW_UP_PHASE': DiseaseStage.FOLLOW_UP,
    'PROGNOSIS_PHASE': DiseaseStage.PROGNOSIS,
    'CLOSED_PHASE': DiseaseStage.CLOSED,
    
    // 中文映射
    '初诊期': DiseaseStage.INITIAL,
    '确诊期': DiseaseStage.DIAGNOSIS,
    '治疗期': DiseaseStage.TREATMENT,
    '随访期': DiseaseStage.FOLLOW_UP,
    '恢复期': DiseaseStage.FOLLOW_UP,
    '预后期': DiseaseStage.PROGNOSIS
  };
  
  return phaseMap[phaseString] || DiseaseStage.INITIAL;
};

interface StageData {
  stage: DiseaseStage;
  date: Date | null;
  isCompleted: boolean;
  isActive: boolean;
}

interface DiseaseCardProps {
  id: string;
  name: string;
  diagnosisDate?: string; // 诊断日期
  createdAt?: string; // 创建日期
  isPrivate?: boolean; // 是否有隐私标识
  stages: Array<{
    stageNode?: string; // 阶段名称
    stage_node?: string; // 后端返回的阶段字段
    stagePhase?: string; // 阶段周期
    stage_phase?: string; // 后端返回的阶段周期字段
    recordDate?: string; // 记录日期
    record_date?: string; // 记录日期(下划线命名)
    created_at?: string; // 创建日期(下划线命名)
    createdAt?: string; // 创建日期(驼峰命名)
    recordId?: string; // 记录ID
    id?: string; // 记录ID(可能代替recordId)
    disease_id?: string; // 疾病ID(下划线命名)
    diseaseId?: string; // 疾病ID(驼峰命名)
    node?: string; // 简化阶段节点字段
    stage?: string; // 另一种阶段字段
    recordType?: string; // 记录类型(驼峰命名)
    record_type?: string; // 记录类型(下划线命名)
    title?: string; // 记录标题
    name?: string; // 记录名称(可能代替标题)
    content?: string; // 记录内容
    description?: string; // 记录描述(可能代替内容)
    // 其他可能的字段
    [key: string]: any; // 增加索引签名，允许任意其他字段
  }>;
  isSelected?: boolean;
  onClick?: () => void;
  aiAnalysisSummary?: any; // AI分析摘要数据
}

/**
 * 病理卡片组件
 * 包含彩色边条、病理名称（带建档时间）、隐私标志显示和病理时间轴
 */
const DiseaseCard: React.FC<DiseaseCardProps> = ({
  id,
  name,
  diagnosisDate,
  createdAt,
  isPrivate = false,
  stages,
  isSelected = false,
  onClick,
  aiAnalysisSummary
}) => {
  const theme = useTheme();
  const { user } = useAuthStore();
  
  // 处理阶段数据，按照阶段顺序排列并标记完成状态
  const processStages = (): StageData[] => {
    // 创建一个包含所有阶段的数组，并初始化为未完成状态
    const processedStages: StageData[] = stageOrder.map(stage => ({
      stage,
      date: null,
      isCompleted: false,
      isActive: false
    }));
    
    console.log(`[DiseaseCard] 处理 ${name} 的病程记录:`, stages);
    
    // 如果存在阶段记录
    if (stages && stages.length > 0) {
      // 创建一个映射来存储每个阶段的最早记录日期
      const stageRecords: Record<DiseaseStage, Date> = {} as Record<DiseaseStage, Date>;
      
      // 处理每个记录，提取阶段信息
      stages.forEach(record => {
        // 获取记录日期，考虑多种可能的字段名
        const recordDate = record.recordDate || record.record_date || record.created_at || record.createdAt;
        if (!recordDate) return;
        
        // 获取阶段信息，考虑多种可能的字段名
        const stageNode = record.stageNode || record.stage_node || record.node || record.stage;
        const stagePhase = record.stagePhase || record.stage_phase;
        const recordType = record.recordType || record.record_type;
        
        console.log(`[DiseaseCard] 处理记录:`, { 
          recordDate, 
          stageNode, 
          stagePhase,
          recordType,
          record 
        });
        
        // 根据节点和阶段确定病程阶段
        let stage: DiseaseStage | undefined;
        
        if (stageNode) {
          // 通过节点名称确定阶段
          stage = mapStageFromString(stageNode);
        } else if (stagePhase) {
          // 通过阶段周期确定阶段
          stage = mapPhaseToStage(stagePhase);
        } else if (recordType === 'STAGE_CHANGE') {
          // 如果是阶段变更记录，尝试从记录标题或内容中提取阶段信息
          const title = record.title || record.name || '';
          const content = record.content || record.description || '';
          
          if (title) {
            stage = mapStageFromString(title);
          }
          
          if (!stage && content) {
            stage = mapStageFromString(content);
          }
          
          // 如果仍未确定阶段，使用默认阶段
          if (!stage) {
            stage = DiseaseStage.INITIAL;
          }
        }
        
        if (stage) {
          const recordTime = new Date(recordDate);
          
          // 如果这个阶段之前没有记录，或者这个记录更早，则更新
          if (!stageRecords[stage] || recordTime < stageRecords[stage]) {
            stageRecords[stage] = recordTime;
          }
        }
      });
      
      // 标记已完成的阶段
      let latestStageIndex = -1;
      
      // 根据stageRecords更新processedStages
      Object.entries(stageRecords).forEach(([stageKey, date]) => {
        const stage = stageKey as DiseaseStage;
        const index = stageOrder.indexOf(stage);
        
        if (index !== -1) {
          processedStages[index].date = date;
          processedStages[index].isCompleted = true;
          
          if (index > latestStageIndex) {
            latestStageIndex = index;
          }
        }
      });
      
      // 设置活跃阶段
      if (latestStageIndex !== -1) {
        processedStages[latestStageIndex].isActive = true;
        
        // 确保前面的阶段都标记为已完成
        for (let i = 0; i < latestStageIndex; i++) {
          processedStages[i].isCompleted = true;
        }
      } else if (processedStages.length > 0) {
        // 如果没有找到任何阶段记录，默认第一个阶段为活跃
        processedStages[0].isActive = true;
      }

      // 输出病程节点处理结果
      console.log(`[DiseaseCard] 处理结果: 共 ${stages.length} 条记录, 识别出 ${Object.keys(stageRecords).length} 个病程阶段`);
      Object.entries(stageRecords).forEach(([stageKey, date]) => {
        console.log(`[DiseaseCard] 阶段 ${stageKey}: ${date.toISOString().split('T')[0]}`);
      });
    } else if (processedStages.length > 0) {
      // 如果没有阶段记录，默认第一个阶段为活跃
      processedStages[0].isActive = true;
    }
    
    return processedStages;
  };
  
  // 获取当前活跃阶段
  const getActiveStage = (processedStages: StageData[]): DiseaseStage => {
    const activeStage = processedStages.find(stage => stage.isActive);
    return activeStage ? activeStage.stage : DiseaseStage.INITIAL;
  };
  
  // 获取活跃阶段的颜色
  const getActiveColor = (processedStages: StageData[]): string => {
    const activeStage = getActiveStage(processedStages);
    return stageColors[activeStage] || stageColors.default;
  };
  
  // 日期格式化为短格式 (YY-MM-DD)
  const formatDate = (date: string | undefined): string => {
    if (!date) return '';
    
    try {
      return format(new Date(date), 'yy-MM-dd', { locale: zhCN });
    } catch (e) {
      console.error('日期格式化错误:', e);
      return '';
    }
  };
  
  // 处理阶段数据
  const processedStages = processStages();
  
  // 获取活跃颜色
  const activeColor = getActiveColor(processedStages);
  
  // 获取活跃阶段
  const activeStage = getActiveStage(processedStages);
  
  return (
    <Paper 
      elevation={0}
      sx={{
        mb: 2,
        borderRadius: 2,
        overflow: 'hidden',
        border: '1px solid',
        borderColor: isSelected ? theme.palette.primary.main : theme.palette.divider,
        backgroundColor: isSelected ? alpha(theme.palette.primary.main, 0.15) : 'background.paper',
        cursor: 'pointer',
        transition: 'all 0.2s ease',
        '&:hover': {
          borderColor: theme.palette.primary.light,
          boxShadow: 1
        },
        position: 'relative',
        display: 'flex',
        pl: 0, // 移除左侧内边距
        transform: 'translateZ(0)', // 添加硬件加速
        willChange: 'transform' // 提示浏览器该元素将发生变化
      }}
      onClick={onClick}
    >
      {/* 左侧彩色条 */}
      <Box 
        className={`disease-card-color-bar stage-${activeStage.toLowerCase()}`}
        sx={{ 
          width: 6,
          bgcolor: activeColor,
        }} 
      />
      
      <Box sx={{ flex: 1, p: 1.5 }}>
        {/* 病理名称、时间和隐私标识 */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 0.5 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
              {name}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.7rem' }}>
              {diagnosisDate ? `确诊于 ${formatDate(diagnosisDate)}` : (createdAt ? `建档于 ${formatDate(createdAt)}` : '')}
            </Typography>
          </Box>
          {isPrivate && (
            <Chip 
              icon={<PrivateIcon />} 
              label="隐私" 
              size="small"
              color="secondary"
              sx={{ 
                height: 18, 
                '& .MuiChip-label': { 
                  px: 0.5,
                  fontSize: '0.6rem',
                  color: 'white'
                },
                '& .MuiChip-icon': {
                  fontSize: '0.8rem',
                  color: 'white'
                }
              }}
            />
          )}
        </Box>
        
        {/* 病程时间轴 */}
        <Box sx={{ display: 'flex', alignItems: 'center', mt: 2.5, mb: 2, position: 'relative' }}>
          {processedStages.map((stageData, index) => (
            <React.Fragment key={index}>
              {/* 阶段标记节点 */}
              <Box 
                sx={{
                  width: 14,
                  height: 14,
                  borderRadius: '50%',
                  backgroundColor: stageData.isActive ? 
                    stageColors[stageData.stage] : 
                    (stageData.isCompleted ? alpha(stageColors[stageData.stage], 0.7) : alpha(stageColors[stageData.stage], 0.3)),
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  position: 'relative',
                  zIndex: 1
                }}
              >
                {/* 阶段完成或激活时显示内圈 */}
                {(stageData.isCompleted || stageData.isActive) && (
                  <Box 
                    sx={{
                      width: 5,
                      height: 5,
                      borderRadius: '50%',
                      backgroundColor: '#fff'
                    }}
                  />
                )}
                
                {/* 阶段日期提示 */}
                {stageData.date && (
                  <Box 
                    sx={{
                      position: 'absolute',
                      bottom: -20,
                      left: '50%',
                      transform: 'translateX(-50%)',
                      whiteSpace: 'nowrap',
                      fontSize: '0.55rem',
                      color: 'text.secondary'
                    }}
                  >
                    {formatDate(stageData.date?.toISOString().split('T')[0])}
                  </Box>
                )}
                
                {/* 阶段名称提示 */}
                <Box 
                  sx={{
                    position: 'absolute',
                    top: -20,
                    left: '50%',
                    transform: 'translateX(-50%)',
                    whiteSpace: 'nowrap',
                    fontSize: '0.6rem',
                    fontWeight: stageData.isActive ? 600 : 400,
                    color: stageData.isActive ? 
                      stageColors[stageData.stage] : 
                      (stageData.isCompleted ? alpha(stageColors[stageData.stage], 0.9) : 'text.secondary')
                  }}
                >
                  {stageData.stage}
                </Box>
              </Box>
              
              {/* 连接线 */}
              {index < processedStages.length - 1 && (
                <Box 
                  sx={{
                    height: 2,
                    flexGrow: 1,
                    backgroundColor: processedStages[index].isCompleted && processedStages[index + 1].isCompleted ? 
                      stageColors[processedStages[index].stage] : 
                      alpha(stageColors[processedStages[index].stage], 0.3)
                  }}
                />
              )}
            </React.Fragment>
          ))}
        </Box>
        
        {/* 底部信息区域 - 移除病程阶段标签 */}
        
        {/* 集成AI分析摘要组件 */}
        {aiAnalysisSummary && (
          <DiseaseCardAISection 
            diseaseId={id} 
            aiAnalysisSummary={aiAnalysisSummary} 
            userRole={user?.role}
          />
        )}

      </Box>
    </Paper>
  );
};

export default DiseaseCard; 