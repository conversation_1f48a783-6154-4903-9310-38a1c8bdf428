import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Divider, 
  Chip, 
  Tooltip,
  useTheme
} from '@mui/material';
import {
  CreditCard as IdCardIcon,
  Favorite as HealthIcon,
  People as FamilyIcon
} from '@mui/icons-material';
import { usePatientDiseaseContext } from '../../context/PatientDiseaseContext';

// 患者详情类型
interface PatientDetail {
  id: string;
  name: string;
  age: number;
  gender: 'male' | 'female';
  idCard: string;  // 身份证号
  existingDiseases: string[];  // 现有病史
  familyDiseases: { relation: string; disease: string }[];  // 家族病史
}

/**
 * 患者详情卡片组件
 * 显示选中患者的详细信息，包括身份信息、现有病史和家族病史
 */
const PatientDetailCard: React.FC = () => {
  const theme = useTheme();
  const { selectedPatientId } = usePatientDiseaseContext();
  const [patientDetail, setPatientDetail] = useState<PatientDetail | null>(null);
  const [loading, setLoading] = useState(true);

  // 从后端获取患者详细信息
  useEffect(() => {
    if (!selectedPatientId) {
      setPatientDetail(null);
      setLoading(false);
      return;
    }

    // 实际项目中应该调用API获取数据
    // 这里使用模拟数据进行演示
    const mockData: PatientDetail = {
      id: '1',
      name: '张三',
      age: 35,
      gender: 'male',
      idCard: 'p1身份证号',
      existingDiseases: ['高血压(伴例)', '糖尿病(伴例)'],
      familyDiseases: [
        { relation: '父亲', disease: '高血压' },
      ]
    };

    // 模拟异步加载
    setTimeout(() => {
      setPatientDetail(mockData);
      setLoading(false);
    }, 300);
  }, [selectedPatientId]);

  // 如果没有选中患者，不显示此组件
  if (!selectedPatientId || !patientDetail) {
    return null;
  }

  return (
    <Paper
      elevation={0}
      sx={{
        border: '1px solid',
        borderColor: 'divider',
        borderRadius: 2,
        p: { xs: 2, sm: 3 }
      }}
    >
      {/* 患者姓名和基本信息 */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
        <Box>
          <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
            {patientDetail.name}
            <Typography 
              component="span" 
              sx={{ 
                ml: 1, 
                color: 'text.secondary',
                fontSize: '0.9rem'
              }}
            >
              {patientDetail.age}岁 {patientDetail.gender === 'male' ? '男' : '女'}
            </Typography>
          </Typography>
        </Box>
      </Box>

      <Divider sx={{ mb: 2 }} />

      {/* 身份证信息 */}
      <Box sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
        <IdCardIcon sx={{ color: theme.palette.primary.main, mr: 1, fontSize: '1.2rem' }} />
        <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'text.primary', mr: 1 }}>
          身份证:
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {patientDetail.idCard}
        </Typography>
      </Box>

      {/* 现有病史 */}
      <Box sx={{ mb: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <HealthIcon sx={{ color: theme.palette.error.main, mr: 1, fontSize: '1.2rem' }} />
          <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'text.primary' }}>
            现往病史:
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, ml: 3 }}>
          {patientDetail.existingDiseases.map((disease, index) => (
            <Chip 
              key={index}
              label={disease}
              size="small"
              variant="outlined"
              sx={{ fontSize: '0.75rem' }}
            />
          ))}
        </Box>
      </Box>

      {/* 家族病史 */}
      <Box>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <FamilyIcon sx={{ color: theme.palette.info.main, mr: 1, fontSize: '1.2rem' }} />
          <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'text.primary' }}>
            家族病史:
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, ml: 3 }}>
          {patientDetail.familyDiseases.map((item, index) => (
            <Tooltip key={index} title={`${item.relation}的${item.disease}`}>
              <Chip 
                label={`${item.relation}: ${item.disease}`}
                size="small"
                variant="outlined"
                sx={{ fontSize: '0.75rem' }}
              />
            </Tooltip>
          ))}
        </Box>
      </Box>
    </Paper>
  );
};

export default PatientDetailCard; 