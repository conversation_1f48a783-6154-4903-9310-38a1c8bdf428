/**
 * 创建用户授权关系表
 */
exports.up = function(knex) {
  return knex.schema.createTable('user_authorizations', table => {
    // 主键
    table.uuid('id').primary();
    
    // 授权人(普通用户)
    table.uuid('authorizer_id').notNullable().references('id').inTable('users');
    
    // 被授权人(服务用户或管理员)
    table.uuid('authorized_id').notNullable().references('id').inTable('users');
    
    // 授权状态
    // PENDING_AUTHORIZER: 授权人已发起但未确认
    // PENDING_AUTHORIZED: 被授权人已发起但授权人未确认
    // ACTIVE: 双方都已确认，授权生效
    // REVOKED: 授权已撤销
    table.enum('status', ['PENDING_AUTHORIZER', 'PENDING_AUTHORIZED', 'ACTIVE', 'REVOKED']).notNullable().defaultTo('PENDING_AUTHORIZER');
    
    // 授权级别
    // BASIC: 基本授权，只读权限
    // STANDARD: 标准授权，可修改自己创建的内容
    // FULL: 完全授权，可全部修改
    table.enum('privacy_level', ['BASIC', 'STANDARD', 'FULL']).notNullable().defaultTo('STANDARD');
    
    // 患者ID，可为空表示授权所有患者(个人版)
    table.uuid('patient_id').nullable().references('id').inTable('patients');
    
    // 创建者ID
    table.uuid('created_by').notNullable().references('id').inTable('users');
    
    // 有新消息标记
    table.boolean('has_new_notification').notNullable().defaultTo(false);
    
    // 时间戳
    table.timestamp('created_at', { useTz: true }).notNullable().defaultTo(knex.fn.now());
    table.timestamp('updated_at', { useTz: true }).notNullable().defaultTo(knex.fn.now());
    table.timestamp('activated_at', { useTz: true }).nullable();
    table.timestamp('revoked_at', { useTz: true }).nullable();
    
    // 索引
    table.index(['authorizer_id']);
    table.index(['authorized_id']);
    table.index(['status']);
    table.unique(['authorizer_id', 'authorized_id', 'patient_id']);
  });
};

exports.down = function(knex) {
  return knex.schema.dropTableIfExists('user_authorizations');
}; 