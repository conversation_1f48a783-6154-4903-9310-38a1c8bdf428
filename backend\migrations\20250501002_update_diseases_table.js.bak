/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.table('diseases', table => {
    // 添加缺少的字段
    table.integer('isPrivate').defaultTo(0).notNullable();
    table.boolean('isActive').defaultTo(true).notNullable();
    table.boolean('isChronic').defaultTo(false).notNullable();
    table.string('status', 20).defaultTo('ACTIVE').notNullable();
    table.uuid('userId').references('id').inTable('users').onDelete('CASCADE');
    
    // 添加索引以提高查询性能
    table.index('isPrivate');
    table.index('isActive');
    table.index('isChronic');
    table.index('status');
    table.index('userId');
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.table('diseases', table => {
    // 回滚时删除添加的字段
    table.dropColumn('isPrivate');
    table.dropColumn('isActive');
    table.dropColumn('isChronic');
    table.dropColumn('status');
    table.dropColumn('userId');
  });
}; 