/**
 * AI报告控制器
 * 处理AI分析报告的CRUD操作和其他相关功能
 */
const { AIReport, AIReportConfig, AIReportQuota } = require('../../models/aiReport');
const Record = require('../../models/Record');
const { toCamelCase, toSnakeCase, adaptAIReport } = require('../../utils/namingAdapter');
const User = require('../../models/User');
const { errorHandler } = require('../../utils/errorHandler');
const fs = require('fs');
const path = require('path');
const uuid = require('uuid');
const axios = require('axios');
const PDFDocument = require('pdfkit');
const os = require('os'); // 操作系统模块
const { generateAIReport } = require('../../services/aiReport/aiReportService');
const { filterReportContent, determineUserRole } = require('../../services/aiReport/reportFilterService');
const Disease = require('../../models/Disease');
const Patient = require('../../models/Patient');
const knex = require('../../config/database'); // 数据库连接
// const Knex = require('knex'); // 移除或注释掉
// const knexConfig = require('../../config/knex'); // 移除或注释掉
// const knex = Knex(knexConfig.development); // 移除或注释掉

console.log('!!!!!!!!!!!! AIReportController.js WAS LOADED !!!!!!!!!!!!');

/**
 * 获取指定病理的所有AI报告
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAIReports = async (req, res) => {
  console.log('!!!!!!!!!!!! GET AIREPORTS FUNCTION ENTERED !!!!!!!!!!!!');
  try {
    const { diseaseId, contextPatientId, contextAuthorizationId } = req.query; // 新增 contextPatientId 和 contextAuthorizationId
    const currentUserId = req.user.id;
    const currentUserRole = determineUserRole(req.user); // 确定当前用户角色

    // **** 新增日志 ****
    console.log(`[AIReportController DEBUG] currentUserId: ${currentUserId}, currentUserRole: ${currentUserRole}, contextPatientId: ${contextPatientId}, contextAuthorizationId: ${contextAuthorizationId}, diseaseId (query): ${diseaseId}`);
    // **** 结束新增日志 ****

    // 1. 清理卡住的报告 (此逻辑可以保持，但清理范围可能需要调整)
    const PROCESSING_TIMEOUT_MS = 2 * 60 * 1000;
    const twoMinutesAgo = new Date(Date.now() - PROCESSING_TIMEOUT_MS);
    const stuckReportCleanerConditions = {
          status: 'PROCESSING'
    };
    // 如果是普通用户，只清理自己的卡住报告
    // 如果是服务用户，理论上可以清理其有权访问的患者的卡住报告，但为简化，此处仍按创建者清理或全局清理（如果需要）
    // 为安全起见，暂时让清理逻辑基于报告的 user_id，即谁创建谁负责清理，或管理员负责清理。
    // 此处可以根据具体业务需求决定是否让服务用户清理其患者的报告
    if (currentUserRole === 'USER') {
      stuckReportCleanerConditions.user_id = currentUserId;
    }
    if (diseaseId) { // 这个 diseaseId 是前端选的筛选器，不是服务上下文的 diseaseId
      stuckReportCleanerConditions.disease_id = diseaseId;
    }

    const stuckReportsToClean = await AIReport.query()
      .where(stuckReportCleanerConditions)
        .where('created_at', '<', twoMinutesAgo.toISOString());
      
    if (stuckReportsToClean.length > 0) {
      console.log(`用户 ${currentUserId} (角色: ${currentUserRole}) 清理 ${stuckReportsToClean.length} 个卡住的报告`);
        await AIReport.query()
        .whereIn('id', stuckReportsToClean.map(report => report.id))
          .patch({
            status: 'FAILED',
            error_message: '报告生成超时(2分钟)，已自动标记为失败状态',
            updated_at: new Date().toISOString()
          });
      }

    // 2. 构建查询条件获取报告列表
    let queryConditions = {};
    let authorizedToViewPatientReports = false;

    if (currentUserRole === 'SERVICE_USER' && contextPatientId) {
      console.log(`[AIReportController] 服务用户 ${currentUserId} 尝试查看患者 ${contextPatientId} (授权ID: ${contextAuthorizationId}) 的报告。`);
      // 服务用户：检查对 contextPatientId 的授权
      if (contextAuthorizationId) { // 如果前端传递了明确的授权ID
        console.log(`[AIReportController] 使用明确的授权ID: ${contextAuthorizationId} 进行检查。`);
        const authorization = await knex('user_authorizations')
      .where({ 
            id: contextAuthorizationId,
            authorized_id: currentUserId, // 当前服务用户是被授权者
            patient_id: contextPatientId, // 授权针对特定患者
            status: 'ACTIVE'
          })
          .first();
        if (authorization) {
          authorizedToViewPatientReports = true;
          console.log(`[AIReportController] 授权ID ${contextAuthorizationId} 验证通过。`);
        } else {
          console.log(`[AIReportController] 授权ID ${contextAuthorizationId} 验证失败。`);
          console.log(`[AIReportController] 检查参数: id=${contextAuthorizationId}, authorized_id=${currentUserId}, patient_id=${contextPatientId}, status=ACTIVE`);
        }
      } else {
        console.log(`[AIReportController] 未提供明确的授权ID，尝试查找患者 ${contextPatientId} 的任何有效授权。`);
        // 如果没有特定的 authorizationId，尝试查找任何有效的授权
        const anyPatientAuthorization = await knex('user_authorizations')
          .where({
            authorized_id: currentUserId,
            patient_id: contextPatientId,
            status: 'ACTIVE'
          })
          .first();
        if (anyPatientAuthorization) {
          authorizedToViewPatientReports = true;
          console.log(`[AIReportController] 找到患者 ${contextPatientId} 的有效授权: ${anyPatientAuthorization.id}`);
        } else {
          console.log(`[AIReportController] 未找到患者 ${contextPatientId} 的任何有效授权。`);
          console.log(`[AIReportController] 检查参数: authorized_id=${currentUserId}, patient_id=${contextPatientId}, status=ACTIVE`);
        }
      }

      if (authorizedToViewPatientReports) {
        queryConditions.patient_id = contextPatientId; // 主要按患者ID过滤
        if (diseaseId) { // 这个 diseaseId 是前端在服务用户界面上选择的病理筛选器
          queryConditions.disease_id = diseaseId;
        }
        console.log(`[AIReportController] 服务用户 ${currentUserId} 已授权查看患者 ${contextPatientId} 的报告。查询条件:`, JSON.stringify(queryConditions));
      } else {
        console.log(`[AIReportController] 服务用户 ${currentUserId} 无权查看患者 ${contextPatientId} 的AI报告。将返回空列表。`);
        return res.json([]); // 无权限则返回空列表
      }
    } else if (currentUserRole === 'USER') {
      // 普通用户：只能查看自己的报告
      queryConditions.user_id = currentUserId;
      if (diseaseId) {
        queryConditions.disease_id = diseaseId;
      }
    } else if (currentUserRole === 'SERVICE_USER' && !contextPatientId) {
      // SERVICE_USER但没有指定contextPatientId的情况：查看自己创建的报告
      console.log(`[AIReportController] 服务用户 ${currentUserId} 查看自己创建的报告`);
      queryConditions.user_id = currentUserId;
      if (diseaseId) {
        queryConditions.disease_id = diseaseId;
      }
    } else {
      // 其他情况或未指定角色的情况（例如匿名访问，如果允许的话），返回空
      console.warn(`未处理的用户角色 ${currentUserRole} 或缺少必要参数，无法获取报告列表`);
      return res.json([]);
    }
    
    const reports = await AIReport.query()
      .where(queryConditions)
      .orderBy('created_at', 'desc')
      .select('*');

    // 3. 填充患者和病理名称
    const populatedReports = await Promise.all(reports.map(async (report) => {
      const populated = { ...report }; // 创建副本以避免修改原始查询结果对象
      if (report.patient_id) {
        const patient = await Patient.query().findById(report.patient_id).select('name');
        populated.patient_name = patient ? patient.name : '未知患者';
      } else {
        populated.patient_name = '未关联患者';
      }
      if (report.disease_id) {
        const disease = await Disease.query().findById(report.disease_id).select('name');
        populated.disease_name = disease ? disease.name : '未知病理';
      } else {
        populated.disease_name = '未关联病理';
      }
      return populated;
    }));
    
    // 4. 转换蛇形命名为驼峰命名
    const formattedReports = populatedReports.map(report => adaptAIReport(report));
    
res.json(formattedReports);

  } catch (error) {
    // 确保错误处理能够捕获所有类型的错误并返回适当的响应
    console.error(`[getAIReports Controller Error] user ${req.user.id}, diseaseId ${req.query.diseaseId}:`, error);
    // 使用通用的错误处理器
    errorHandler(error, res, '获取AI报告列表时发生内部服务器错误');
  }
};

/**
 * 获取单个AI报告详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAIReport = async (req, res) => {
  // ***********************************************************************
  console.log('[AIReportController DEBUG] ENTERING getAIReport FUNCTION. Report ID:', req.params.reportId);
  // ***********************************************************************
  try {
    const { reportId } = req.params;
    console.log('[AIReportController DEBUG] MARKER 1 - Report ID destructured:', reportId);
    
    // 添加强制不缓存的响应头
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    res.setHeader('Surrogate-Control', 'no-store');
    
    const report = await AIReport.query()
      .findById(reportId)
      .withGraphFetched('record');
    
    if (!report) {
      console.log('[AIReportController DEBUG] MARKER 2 - Report not found in DB.');
      return res.status(404).json({ message: '未找到指定的AI报告' });
    }
    console.log('[AIReportController DEBUG] MARKER 3 - Report found in DB. Status:', report.status);
    
    // 检查是否为卡住的PROCESSING状态报告
    if (report.status === 'PROCESSING') {
      const twoMinutesAgo = new Date();
      twoMinutesAgo.setMinutes(twoMinutesAgo.getMinutes() - 2);
      const reportCreatedAt = new Date(report.created_at);
      
      if (reportCreatedAt < twoMinutesAgo) {
        console.log(`检测到卡住的报告: ${report.id}, 创建时间: ${report.created_at}`);
        
        // 更新报告状态为FAILED
        await AIReport.query()
          .findById(report.id)
          .patch({
            status: 'FAILED',
            error_message: '报告生成超时(2分钟)，已自动标记为失败状态',
            updated_at: new Date().toISOString()
          });
        
        console.log(`已将报告 ${report.id} 标记为FAILED状态`);
        
        // 重新获取更新后的报告
        const updatedReport = await AIReport.query()
          .findById(reportId)
          .withGraphFetched('record');
        
        if (updatedReport) {
          report.status = updatedReport.status;
          report.error_message = updatedReport.error_message;
          report.updated_at = updatedReport.updated_at;
        }
      }
    }
    
    // 获取用户角色
    const user = await User.query().findById(req.user.id);
    if (!user) {
      console.log('[AIReportController DEBUG] MARKER 5 - User not found for token.');
      return res.status(403).json({ message: '无访问权限' });
    }
    console.log('[AIReportController DEBUG] MARKER 6 - User found. Role for filtering:', determineUserRole(user));
    
    // 获取报告显示配置
    let config = await AIReportConfig.query().findOne({ id: 1 });
    if (!config) {
      // 使用默认配置
      config = {
        user_visible_fields: ['summary', 'emergencyGuidance', 'hospitalRecommendations', 'lifestyleAndMentalHealth'],
        service_visible_fields: ['summary', 'differentialDiagnosis', 'emergencyGuidance', 'hospitalRecommendations', 'treatmentPlan', 'budgetEstimation', 'crossRegionGuidance', 'lifestyleAndMentalHealth', 'riskWarnings']
      };
    }
    console.log('[AIReportController DEBUG] MARKER 7 - Report config loaded.');
    
    // 确定用户角色并过滤内容
    const userRole = determineUserRole(user);
    console.log('[AIReportController DEBUG] MARKER 8 - User role determined:', userRole);
    
    // 复制一份报告对象，避免修改原始数据
    const reportCopy = JSON.parse(JSON.stringify(report));
    console.log('[AIReportController DEBUG] MARKER 9 - Report content copied.');
    
    // 新增：在过滤和适配前，检查并修复 emergencyGuidance 的结构
    if (reportCopy.content && reportCopy.content.emergencyGuidance) {
        console.log('[AIReportController DEBUG] Initial emergencyGuidance from DB:', JSON.stringify(reportCopy.content.emergencyGuidance, null, 2));
        let eg = reportCopy.content.emergencyGuidance;
        // 检查是否为只包含 isEmergency 和 guidance 字符串的旧结构
        if (typeof eg.guidance === 'string' && eg.immediateActions === undefined && eg.nextSteps === undefined) {
            console.warn('[AIReportController] Adapting old emergencyGuidance structure with "guidance" string to new structure with "summary", "immediateActions", "nextSteps".');
            reportCopy.content.emergencyGuidance = {
                isEmergency: eg.isEmergency !== undefined ? eg.isEmergency : (eg.guidance.includes('紧急') || eg.guidance.includes('立即就医')),
                summary: eg.guidance, // 将旧的 guidance 字符串用作新的 summary
                immediateActions: [eg.guidance], // 将旧的 guidance 字符串作为一项行动建议
                nextSteps: ['请结合临床判断并遵循医嘱。'] // 提供默认的后续步骤
            };
            console.log('[AIReportController DEBUG] Adapted emergencyGuidance structure:', JSON.stringify(reportCopy.content.emergencyGuidance, null, 2));
        } else {
            // 如果不是旧的guidance字符串结构，仍然要确保基本字段存在且类型正确
            console.log('[AIReportController DEBUG] Validating existing emergencyGuidance structure (not the old "guidance" string type).');
            eg.isEmergency = typeof eg.isEmergency === 'boolean' ? eg.isEmergency : false;
            if (typeof eg.summary !== 'string' || eg.summary.trim() === '') {
                eg.summary = (Array.isArray(eg.immediateActions) && eg.immediateActions.length > 0 && typeof eg.immediateActions[0] === 'string') ? eg.immediateActions[0] : '请查看具体行动建议。';
            }
            eg.immediateActions = Array.isArray(eg.immediateActions) ? eg.immediateActions : (typeof eg.summary === 'string' && eg.summary.trim() !== '' ? [eg.summary] : ['具体行动请咨询专业医生。']);
            eg.nextSteps = Array.isArray(eg.nextSteps) ? eg.nextSteps : ['请遵循医嘱并定期复查。'];
            if (Array.isArray(eg.immediateActions)) {
                eg.immediateActions = eg.immediateActions.map(item => typeof item === 'string' ? item : String(item));
            }
            if (Array.isArray(eg.nextSteps)) {
                eg.nextSteps = eg.nextSteps.map(item => typeof item === 'string' ? item : String(item));
            }
            reportCopy.content.emergencyGuidance = eg; 
            console.log('[AIReportController DEBUG] Validated/Sanitized emergencyGuidance structure:', JSON.stringify(reportCopy.content.emergencyGuidance, null, 2));
        }
    } else if (reportCopy.content) {
        console.warn('[AIReportController] Report content exists but emergencyGuidance is missing. Initializing with default structure.');
        reportCopy.content.emergencyGuidance = {
            isEmergency: false,
            summary: 'AI未能提供紧急处置建议，请咨询医生。',
            immediateActions: ['具体行动请咨询专业医生。'],
            nextSteps: ['请遵循医嘱并定期复查。']
        };
    }
    // 结束 新增的检查和修复emergencyGuidance的逻辑
    
    // 根据用户角色过滤报告内容
    if (reportCopy.content) {
      console.log('[AIReportController DEBUG] MARKER 10 - Entering content filtering block.');
      reportCopy.content = filterReportContent(
        reportCopy.content,
        userRole,
        {
          userVisibleFields: config.user_visible_fields,
          serviceVisibleFields: config.service_visible_fields
        }
      );
      // 新增日志：检查 filterReportContent 后的 emergencyGuidance 结构
      if (reportCopy.content && reportCopy.content.emergencyGuidance) {
        console.log('[AIReportController DEBUG] Structure of emergencyGuidance AFTER filterReportContent:', JSON.stringify(reportCopy.content.emergencyGuidance, null, 2));
      } else {
        console.log('[AIReportController DEBUG] emergencyGuidance is UNDEFINED or NULL after filterReportContent.');
      }
      console.log('[AIReportController DEBUG] MARKER 11 - Content filtering finished.');
    } else {
      console.log('[AIReportController DEBUG] MARKER 10b - Report has no content to filter.');
    }
    
    // 转换为驼峰格式
    console.log('[AIReportController DEBUG] MARKER 12 - Preparing to adapt report to camel case.');
    // 使用命名适配器转换报告对象
    const formattedReport = adaptAIReport(reportCopy);
    console.log('[AIReportController DEBUG] MARKER 13 - Report adapted to camel case.');
    
    // 确保关联的记录也被正确格式化
    if (formattedReport.record) {
      formattedReport.record = toCamelCase(formattedReport.record);
    }
    
    console.log('[AIReportController DEBUG] MARKER 14 - Preparing to send JSON response.');
    res.json(formattedReport);
    console.log('[AIReportController DEBUG] FINISHED getAIReport SUCCESSFULLY.');
  } catch (error) {
    console.error('[AIReportController ERROR in getAIReport]:', error); // 确保错误也被显眼地标记
    errorHandler(error, res);
  }
};

/**
 * 创建新的AI报告 - 修改后版本
 * - 检查重复的PROCESSING报告
 * - 处理过时报告
 * - 检查用户配额
 * - 调用服务层生成报告 (阻塞型)
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - Express的next中间件
 */
exports.createAIReport = async (req, res, next) => {
  try {
    const { diseaseId, patientId, targetRegion } = req.body;
    // 假设前端发送的是驼峰命名
    const finalDiseaseId = diseaseId;
    const finalPatientId = patientId;
    const userId = req.user.id;
    
    if (!finalDiseaseId || !finalPatientId) {
      return res.status(400).json({ message: '病理ID和患者ID不能为空。' });
    }
    
    // 1. 检查是否已有用户针对此病患的正在处理的报告
    let existingProcessingReport = await AIReport.query()
        .where({ 
        disease_id: finalDiseaseId,
        patient_id: finalPatientId,
        user_id: userId, // 关键：确保是当前用户的报告
          status: 'PROCESSING'
        })
      .orderBy('created_at', 'desc') // 获取最新的一个，以防万一有多条（理论上不应发生）
      .first();

    if (existingProcessingReport) {
      // 定义报告"过时"的阈值，例如3分钟
      const PROCESSING_TIMEOUT_MS = 3 * 60 * 1000; 
      const reportCreatedAt = new Date(existingProcessingReport.created_at);
      const isOutdated = (new Date() - reportCreatedAt) > PROCESSING_TIMEOUT_MS;

      if (isOutdated) {
        console.log(`发现过时的PROCESSING报告 ${existingProcessingReport.id} (创建于 ${reportCreatedAt.toISOString()})，将允许创建新报告并标记旧的为失败。`);
        await AIReport.query().findById(existingProcessingReport.id).patch({
              status: 'FAILED',
          error_message: '报告生成超时或被新的请求覆盖而自动标记失败。',
              updated_at: new Date().toISOString()
            });
        existingProcessingReport = null; // 清除引用，以便继续创建新报告
      } else {
        console.log(`用户 ${userId} 对病患 ${finalPatientId} 的病理 ${finalDiseaseId} 已有报告 ${existingProcessingReport.id} 正在处理中 (创建于 ${reportCreatedAt.toISOString()})。`);
        return res.status(200).json({ // 使用200 OK表示请求已理解，并返回现有资源信息
          message: '您选择的病理已有报告正在生成中，请稍候。',
          status: 'ALREADY_PROCESSING', // 与前端约定一致
          aiReport: adaptAIReport(existingProcessingReport) // 确保返回的报告对象经过驼峰转换
      });
      }
    }
    
    // 2. 检查用户配额
    // 注意：这里的配额字段名可能需要根据您的数据库实际情况调整 (e.g., usedThisMonth, monthlyQuota)
    const quota = await AIReportQuota.query().findOne({ user_id: userId });
    if (!quota) {
        // 如果没有配额记录，可以考虑是拒绝还是赋予默认值，这里先拒绝
        return res.status(403).json({ 
            message: '用户配额信息未配置，无法生成报告。',
            status: 'QUOTA_NOT_CONFIGURED' 
        });
    }
    // 使用模型中定义的正确列名: usedThisMonth, monthlyQuota, additionalQuota
    if (quota.usedThisMonth >= (quota.monthlyQuota + quota.additionalQuota)) {
      return res.status(403).json({ 
        message: '您的AI报告生成次数已用完。',
        status: 'QUOTA_EXCEEDED' 
      });
    }
    
    // 3. 如果没有正在处理的报告（或已处理过时报告）且配额充足，则调用服务层生成新报告
    //    generateAIReport 服务内部会创建 AIReport 记录
    //    这里的 await 会使此API调用变为阻塞型，直到报告生成完成或失败
    console.log(`为用户 ${userId} 创建新报告 (病理: ${finalDiseaseId}, 患者: ${finalPatientId})`);
    const { aiReport: newAiReportFromService, recordId } = await generateAIReport(
      finalDiseaseId,
      finalPatientId,
      userId,
      targetRegion
    );

    // 成功调用服务层后，报告已在数据库中创建并处理完毕 (COMPLETED 或 FAILED)
    // 只有在报告成功生成 (COMPLETED) 时才扣除配额
    if (newAiReportFromService.status === 'COMPLETED') {
        // 修改这里：确保使用整数类型
        await AIReportQuota.query()
            .findById(quota.id)
            .patch({
                used_this_month: parseInt(quota.used_this_month) + 1,
                total_used: parseInt(quota.total_used) + 1,
                updated_at: new Date().toISOString()
            });
        console.log(`用户 ${userId} 配额已更新，报告 ${newAiReportFromService.id} 成功生成。`);
    } else {
        console.log(`报告 ${newAiReportFromService.id} 生成状态为 ${newAiReportFromService.status}，未扣除配额。`);
    }
    
    console.log(`新AI报告处理完成: ${newAiReportFromService.id}，状态: ${newAiReportFromService.status}`);
      
    // 根据服务返回的报告状态决定响应码和消息
    let responseHttpStatusCode = 200; // 默认为200 OK
    let responseMessage = `AI报告ID ${newAiReportFromService.id} 处理完毕，状态: ${newAiReportFromService.status}。`;
    let clientStatus = newAiReportFromService.status; // 通常直接用服务返回的状态

    if (newAiReportFromService.status === 'COMPLETED') {
      responseHttpStatusCode = 201; // 201 Created 表示资源成功创建
      responseMessage = `AI报告 ${newAiReportFromService.id} 已成功生成。`;
      clientStatus = 'COMPLETED';
    } else if (newAiReportFromService.status === 'PROCESSING') {
      // 理论上，由于上面是 await，不应返回 PROCESSING，除非服务内部逻辑特殊
      // 但为兼容，仍作判断
      responseMessage = `AI报告 ${newAiReportFromService.id} 仍在处理中，请稍后查看。`;
      clientStatus = 'PROCESSING_STARTED'; // 或保持 'PROCESSING'
    } else if (newAiReportFromService.status === 'FAILED') {
      responseMessage = `AI报告 ${newAiReportFromService.id} 生成失败。错误: ${newAiReportFromService.error_message || '未知错误'}`;
      clientStatus = 'FAILED';
    }
    
    res.status(responseHttpStatusCode).json({
      message: responseMessage,
      status: clientStatus, 
      aiReport: adaptAIReport(newAiReportFromService), // 确保返回的报告对象经过驼峰转换
      recordId // recordId 来自服务层
    });

  } catch (error) {
    console.error(`创建AI报告时发生严重错误 (controller for user ${req.user.id}):`, error);
    // 可以根据错误类型定制更具体的响应
    // 例如，如果错误是服务层已知错误类型，可以有特定处理
    // if (error instanceof CustomServiceError) { ... }
    
    // 默认将错误传递给Express的全局错误处理器
    next(error); 
  }
};

/**
 * 删除AI报告
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteAIReport = async (req, res) => {
    const { reportId } = req.params;
  const currentUserId = req.user.id; // 当前操作用户的ID
  const currentUserRole = req.user.role; // 当前操作用户的角色 (USER, SERVICE, ADMIN)
    
  console.log(`[deleteAIReport] Attempting to delete report ${reportId} by user ${currentUserId} (Role: ${currentUserRole})`);

  try {
    const report = await AIReport.query().findById(reportId);

    if (!report) {
      console.log(`[deleteAIReport] Report ${reportId} not found.`);
      return res.status(404).json({ message: 'AI报告未找到' });
    }
    
    // report.user_id 始终是患者（原始授权用户）的ID
    const patientUserIdForAuthorizationCheck = report.user_id;
    console.log(`[deleteAIReport] Report ${reportId} belongs to user (patient) ${patientUserIdForAuthorizationCheck}. Current operator: ${currentUserId} (Role: ${currentUserRole}).`);

    let canDelete = false;

    // 规则 A: 如果当前操作用户就是报告所属的患者用户，则可以删除
    if (patientUserIdForAuthorizationCheck === currentUserId) {
      canDelete = true;
      console.log(`[deleteAIReport] Operator ${currentUserId} is the owner (patient user) of report ${reportId}. Permission granted.`);
    } 
    // 规则 B: 如果当前操作用户是服务用户 (SERVICE or ADMIN)，并且不是报告的患者用户，则检查授权
    else if (currentUserRole === 'SERVICE' || currentUserRole === 'ADMIN') {
      console.log(`[deleteAIReport] Operator ${currentUserId} (Role: ${currentUserRole}) is not the patient user. Checking authorization against report's patient_id: ${report.patient_id} for patient user ${patientUserIdForAuthorizationCheck}.`);
      const authorization = await knex('user_authorizations')
        .where({
          authorizer_id: patientUserIdForAuthorizationCheck, // 患者的用户ID (来自 report.user_id)
          authorized_id: currentUserId,                   // 当前服务用户的ID
          patient_id: report.patient_id,                  // ** 新增：必须是针对此AI报告关联的患者实体的授权 **
          status: 'ACTIVE',
          privacy_level: 'FULL'
        })
        .first();

      // **** 详细日志 ****
      if (authorization) {
        canDelete = true;
        console.log(`[deleteAIReport] Found specific FULL ACTIVE authorization for patient_id ${report.patient_id}: ${JSON.stringify(authorization)}. Permission GRANTED.`);
      } else {
        console.log(`[deleteAIReport] Operator ${currentUserId} does NOT have specific FULL ACTIVE authorization for patient_id ${report.patient_id} (User: ${patientUserIdForAuthorizationCheck}).`);
        // 为了调试，检查是否存在任何针对此 patient_id (不限 privacy_level) 或任何针对此 authorizer_id (不限 patient_id 和 privacy_level) 的授权
        const anyAuthForThisPatient = await knex('user_authorizations')
            .where({ authorizer_id: patientUserIdForAuthorizationCheck, authorized_id: currentUserId, patient_id: report.patient_id, status: 'ACTIVE' })
            .first();
        if (anyAuthForThisPatient) {
            console.log(`[deleteAIReport] Found other active authorization for this specific patient_id ${report.patient_id} (but not FULL): ${JSON.stringify(anyAuthForThisPatient)}`);
        } else {
            console.log(`[deleteAIReport] No active authorization found for this specific patient_id ${report.patient_id}.`);
        }
        const anyAuthForThisAuthorizer = await knex('user_authorizations')
            .where({ authorizer_id: patientUserIdForAuthorizationCheck, authorized_id: currentUserId, status: 'ACTIVE' })
            .select('id', 'patient_id', 'privacy_level') // 只选择必要字段，避免日志过长
            .limit(5); // 最多看5条，避免日志爆炸
        if (anyAuthForThisAuthorizer && anyAuthForThisAuthorizer.length > 0) {
            console.log(`[deleteAIReport] Found other active authorizations for authorizer_id ${patientUserIdForAuthorizationCheck} (but not for specific patient or not FULL): ${JSON.stringify(anyAuthForThisAuthorizer)}`);
        } else {
            console.log(`[deleteAIReport] No other active authorizations found for authorizer_id ${patientUserIdForAuthorizationCheck}.`);
        }
        // console.log(`[deleteAIReport] Permission DENIED for deletion of report ${reportId}.`); // 这行可以移除或保留，下面的日志更具体
        // 将韩文日志修改为中文，并作为主要的权限拒绝日志
        console.log(`[deleteAIReport] 权限不足 (授权检查分支): 操作员 ${currentUserId} (角色: ${currentUserRole}) 无权删除报告 ${reportId}。报告属于患者 ${report.patient_id} (创建者: ${report.user_id})。需要 'FULL' 授权。`);
        return res.status(403).json({ message: `您没有权限删除报告 ${reportId}。需要 'FULL' 授权或您是报告的创建者。` });
      }
    } 
    // 其他情况：例如，一个普通用户尝试删除不属于自己的报告，且他也不是服务用户
    else {
        console.log(`[deleteAIReport] Operator ${currentUserId} (Role: ${currentUserRole}) is not the patient user and not a SERVICE/ADMIN. Permission denied.`);
    }

    if (!canDelete) {
      // 将第二处韩文日志修改为中文
      console.log(`[deleteAIReport] 权限不足 (canDelete总检查分支): 操作员 ${currentUserId} 无权删除报告 ${reportId}。`);
      return res.status(403).json({ message: '您无权删除此AI报告' });
    }

    // 执行删除
    // 1. 删除可能的关联记录（如果AI报告与某个主记录表项关联，但当前AIReport模型似乎没有直接记录关联）
    // 假设 Record 表中有一个 ai_report_id 字段
    // await Record.query().where('ai_report_id', reportId).delete(); // 如果有此关联

    // 2. 删除PDF文件 (如果存在)
    if (report.pdf_url) {
      // 修正路径处理，移除可能的前导斜杠，并使用 process.cwd() 作为根目录
      const pdfPath = path.join(process.cwd(), report.pdf_url.replace(/^\//, ''));
      console.log(`[deleteAIReport] Attempting to delete PDF file at: ${pdfPath}`);
      try {
        if (fs.existsSync(pdfPath)) {
          fs.unlinkSync(pdfPath);
          console.log(`[deleteAIReport] Successfully deleted PDF file: ${pdfPath}`);
        } else {
          console.log(`[deleteAIReport] PDF file not found at: ${pdfPath}`);
        }
      } catch (fileError) {
        // 文件删除失败不应阻止数据库记录的删除，但应记录错误
        console.error(`[deleteAIReport] Error deleting PDF file ${pdfPath}:`, fileError);
      }
    }
    
    // 3. 删除AIReport表中的记录
    await AIReport.query().deleteById(reportId);
    console.log(`[deleteAIReport] Successfully deleted AIReport record ${reportId} from database.`);
    
    return res.status(200).json({ message: 'AI报告已成功删除' });

  } catch (error) {
    console.error(`[deleteAIReport] Error deleting AI report ${reportId}:`, error);
    return errorHandler(res, error, '删除AI报告失败');
  }
};

/**
 * 生成并下载AI报告PDF
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.downloadPDF = async (req, res) => {
  try {
    console.log('开始处理PDF下载请求');
    const { reportId } = req.params;
    
    if (!reportId) {
      console.error('缺少报告ID参数');
      return res.status(400).json({ message: '缺少报告ID参数' });
    }
    
    // 检查URL参数token
    const urlToken = req.query.token;
    if (urlToken && !req.headers.authorization) {
      console.log('从URL参数获取到token并添加到请求头');
      req.headers.authorization = `Bearer ${urlToken}`;
    }
    
    console.log('请求下载PDF，报告ID:', reportId);
    
    // 验证用户是否有权限访问此报告
    const userId = req.user?.id;
    const userRole = req.user?.role; // 获取当前用户的角色

    if (!userId) {
      console.error('用户未认证或ID无效');
      return res.status(401).json({ message: '未授权，请重新登录' });
    }
    
    console.log('当前用户ID:', userId);
    
    try {
      // 查询报告信息
      const report = await AIReport.query().findById(reportId);
      
      if (!report) {
        console.error('未找到指定的AI报告:', reportId);
        return res.status(404).json({ message: '未找到指定的AI报告' });
      }
      
      // ---- 修改权限检查逻辑 ----
      let canAccess = false;
      const reportOwnerId = report.user_id; // 报告创建者/归属者ID

      if (reportOwnerId === userId) { // 规则1: 自己创建的报告可以访问
        canAccess = true;
        console.log(`[downloadPDF] 用户 ${userId} 是报告 ${reportId} 的创建者，允许访问。`);
      } else if (userRole === 'ADMIN') { // 规则2: ADMIN可以访问所有报告 (如果需要)
        canAccess = true;
        console.log(`[downloadPDF] 用户 ${userId} 是 ADMIN，允许访问报告 ${reportId}。`);
      } else if (userRole === 'SERVICE') { // 规则3: SERVICE用户通过授权访问
        console.log(`[downloadPDF] SERVICE用户 ${userId} 尝试访问不属于自己的报告 ${reportId} (拥有者: ${reportOwnerId})。检查授权...`);
        // 这里的授权检查逻辑需要与 getAIReport 中的类似
        // 假设 SERVICE 用户需要对 report.patient_id 有效授权
        // 或者对 report.user_id (如果patient_id在某些报告中可能为空)
        const patientIdForAuthCheck = report.patient_id || reportOwnerId; 

        const authorization = await knex('user_authorizations')
          .where({
            authorized_id: userId, // 当前SERVICE用户是被授权者
            authorizer_id: reportOwnerId, // 报告创建者是授权人
            // patient_id: patientIdForAuthCheck, // 可选：如果授权总是绑定到特定patient
            status: 'ACTIVE'
          })
          // 如果授权可能不直接绑定到 patient_id，或者允许对某个用户的所有患者的通用授权，则调整这里的查询
          // 例如，可以检查对 patient_id 的授权，或者对 authorizer_id (报告所有者) 的通用授权
          .where(function() {
            if (report.patient_id) {
              this.where('patient_id', report.patient_id).orWhereNull('patient_id');
            } else {
              // 如果报告没有patient_id，可能只检查对authorizer_id的通用授权
              this.whereNull('patient_id'); 
            }
          })
          .first();

        if (authorization) {
          canAccess = true;
          console.log(`[downloadPDF] SERVICE用户 ${userId} 通过授权 ${authorization.id} (级别: ${authorization.privacy_level}) 允许访问报告 ${reportId}。`);
        } else {
          console.log(`[downloadPDF] SERVICE用户 ${userId} 未找到对报告 ${reportId} (拥有者: ${reportOwnerId}, 患者: ${report.patient_id}) 的有效授权。`);
        }
      }

      if (!canAccess) {
        console.error('[downloadPDF] 最终权限检查失败: 用户无权访问此报告', {
          reportId: report.id,
          title: report.title,
          requester: req.user.username || userId
        });
        return res.status(403).json({ message: '您无权访问此报告' });
      }
      
      console.log('成功找到报告，开始生成PDF');
      
      // 设置CORS响应头，允许来自前端的跨域请求
      const origin = req.headers.origin || '*';
      res.header('Access-Control-Allow-Origin', origin);
      res.header('Access-Control-Allow-Methods', 'GET, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
      res.header('Access-Control-Allow-Credentials', 'true');
      res.header('Access-Control-Expose-Headers', 'Content-Disposition, Content-Type');
      
      // 生成PDF
      try {
        // 获取报告显示配置
        let reportConfig = await AIReportConfig.query().findOne({ id: 1 });
        if (!reportConfig) {
          // 使用默认配置
          reportConfig = {
            user_visible_fields: ['summary', 'emergencyGuidance', 'hospitalRecommendations', 'lifestyleAndMentalHealth'],
            service_visible_fields: ['summary', 'differentialDiagnosis', 'emergencyGuidance', 'hospitalRecommendations', 'treatmentPlan', 'budgetEstimation', 'crossRegionGuidance', 'lifestyleAndMentalHealth', 'riskWarnings']
          };
        }
        
        const pdfBuffer = await generatePDF(report, userId, reportConfig);
        console.log('PDF生成成功，大小:', pdfBuffer.length, '字节');
        
        // 设置响应头
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(report.title)}.pdf"`);
        
        // 发送PDF内容
        res.send(pdfBuffer);
        console.log('PDF响应已发送至客户端');
      } catch (pdfError) {
        console.error('生成PDF时出错:', pdfError);
        return res.status(500).json({ 
          message: '生成PDF失败', 
          error: pdfError.message || '未知PDF生成错误' 
        });
      }
    } catch (dbError) {
      console.error('查询报告数据库时出错:', dbError);
      return res.status(500).json({ 
        message: '数据库查询失败', 
        error: dbError.message 
      });
    }
  } catch (error) {
    console.error('下载PDF过程中发生未处理的错误:', error);
    
    // 避免重复发送响应
    if (!res.headersSent) {
      errorHandler(error, res);
    } else {
      console.error('错误发生在响应已发送后，无法向客户端发送错误信息');
    }
  }
};

/**
 * 获取报告显示配置
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getReportConfig = async (req, res) => {
  try {
    let config = await AIReportConfig.query().findOne({ id: 1 });
    
    if (!config) {
      // 创建默认配置
      config = await AIReportConfig.query().insert({
        user_visible_fields: ['summary', 'emergencyGuidance', 'hospitalRecommendations', 'lifestyleAndMentalHealth'],
        service_visible_fields: ['summary', 'differentialDiagnosis', 'emergencyGuidance', 'hospitalRecommendations', 'treatmentPlan', 'budgetEstimation', 'crossRegionGuidance', 'lifestyleAndMentalHealth', 'riskWarnings']
      });
    }
    
    // 转换为驼峰格式
    const formattedConfig = {
      id: config.id,
      userVisibleFields: config.user_visible_fields,
      serviceVisibleFields: config.service_visible_fields,
      anonymizationRules: config.anonymization_rules,
      llmPrompt: config.llm_prompt,
      llmResponse: config.llm_response,
      quotaConfig: config.quota_config,
      createdAt: config.created_at,
      updatedAt: config.updated_at
    };
    
    res.json(formattedConfig);
  } catch (error) {
    errorHandler(error, res);
  }
};

/**
 * 保存报告显示配置
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.saveReportConfig = async (req, res) => {
  try {
    const { userVisibleFields, serviceVisibleFields } = req.body;
    
    // 验证用户权限
    if (req.user.role !== 'ADMIN') {
      return res.status(403).json({ message: '只有管理员可以修改配置' });
    }
    
    let config = await AIReportConfig.query().findOne({ id: 1 });
    
    if (!config) {
      // 创建新配置
      config = await AIReportConfig.query().insert({
        user_visible_fields: userVisibleFields || ['summary', 'emergencyGuidance', 'hospitalRecommendations', 'lifestyleAndMentalHealth'],
        service_visible_fields: serviceVisibleFields || ['summary', 'differentialDiagnosis', 'emergencyGuidance', 'hospitalRecommendations', 'treatmentPlan', 'budgetEstimation', 'crossRegionGuidance', 'lifestyleAndMentalHealth', 'riskWarnings']
      });
    } else {
      // 更新配置
      await AIReportConfig.query()
        .findById(config.id)
        .patch({
          user_visible_fields: userVisibleFields,
          service_visible_fields: serviceVisibleFields,
          updated_at: new Date().toISOString()
        });
    }
    
    res.json({ success: true });
  } catch (error) {
    errorHandler(error, res);
  }
};

// 处理月份天数不均的问题
const isTimeToReset = (lastReset, now) => {
  // 如果年份不同，直接重置
  if (lastReset.getFullYear() !== now.getFullYear()) {
    return true;
  }
  
  // 如果月份不同，需要特殊处理
  if (lastReset.getMonth() !== now.getMonth()) {
    const lastResetDay = lastReset.getDate();
    const currentDay = now.getDate();
    
    // 如果当前日期小于等于上次重置日期，说明已经过了完整的一个月
    if (currentDay <= lastResetDay) {
      return true;
    }
    
    // 处理特殊情况：2月29日
    if (lastResetDay === 29 && lastReset.getMonth() === 1) { // 2月
      // 如果当前是3月，且日期小于等于上次重置日期，重置
      if (now.getMonth() === 2 && currentDay <= lastResetDay) {
        return true;
      }
    }
    
    // 处理30日和31日的情况
    if (lastResetDay >= 30) {
      const currentMonthDays = new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate();
      // 如果当前月份的天数小于上次重置日期，且当前日期大于等于当前月份的最后一天
      if (currentMonthDays < lastResetDay && currentDay >= currentMonthDays) {
        return true;
      }
    }
  }
  
  return false;
};

// 检查用户配额
exports.checkUserQuota = async (req, res) => {
  try {
    const userId = req.user.id;
    
    // 获取用户信息
    const user = await User.query().findById(userId);
    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }
    
    // 获取配置中的配额
    let config = await AIReportConfig.query().findOne({ id: 1 });
    const defaultQuota = {
      PERSONAL: 3,
      FAMILY: 10,
      PROFESSIONAL: 30
    };
    const quotaConfig = config?.quota_config || defaultQuota;
    
    // 获取用户配额使用情况
    let quota = await AIReportQuota.query().where({ user_id: userId }).first();
    
    // 如果没有配额记录，创建一个
    if (!quota) {
      const monthlyQuota = quotaConfig[user.level] || 3;
      
      quota = await AIReportQuota.query().insert({
        user_id: userId,
        monthly_quota: monthlyQuota,
        used_this_month: 0,
        total_used: 0,
        last_reset_date: new Date().toISOString(),
        additional_quota: 0
      });
    }
    
    // 检查是否需要重置月度配额
    const lastReset = new Date(quota.last_reset_date);
    const now = new Date();
    
    if (isTimeToReset(lastReset, now)) {
      await AIReportQuota.query()
        .findById(quota.id)
        .patch({
          used_this_month: 0,  // 只重置使用次数
          last_reset_date: now.toISOString(),
          updated_at: now.toISOString()
        });
      
      // 重新获取更新后的配额
      quota = await AIReportQuota.query().findById(quota.id);
    }
    
    // 转换为前端需要的格式
    res.json({
      used: quota.used_this_month,
      max: quota.monthly_quota + quota.additional_quota,
      canUse: quota.used_this_month < (quota.monthly_quota + quota.additional_quota)
    });
  } catch (error) {
    errorHandler(error, res);
  }
};

/**
 * 生成AI报告PDF文件
 * @param {Object} report - AI报告对象
 * @param {Object} user - 用户对象
 * @param {Object} config - 报告显示配置
 * @returns {Promise<Buffer>} PDF文件的二进制数据
 */
async function generatePDF(report, user, config) {
  return new Promise(async (resolve, reject) => {
    try {
      console.log('开始生成PDF，报告ID:', report.id);
      
      // 使用报告对象直接，不再需要get({plain:true})
      let content = report.content;
      
      if (!content) {
        console.error('错误：报告内容为空', { reportId: report.id });
        throw new Error('报告内容为空，无法生成PDF');
      }
      
      // 如果提供了用户和配置信息，则应用内容过滤
      if (user && config) {
        const userRole = determineUserRole(user);
        content = filterReportContent(
          content,
          userRole,
          {
            userVisibleFields: config.user_visible_fields,
            serviceVisibleFields: config.service_visible_fields
          }
        );
        console.log(`已根据${userRole}角色过滤报告内容`);
      }
      
      // 获取患者信息以添加身高体重BMI数据
      const Patient = require('../../models/Patient');
      let patient = null;
      
      try {
        patient = await Patient.query().findById(report.patient_id);
        console.log('成功获取患者信息:', { patientId: report.patient_id });
      } catch (patientErr) {
        console.error('获取患者信息失败:', patientErr);
        // 继续执行，不因患者信息获取失败而中断整个PDF生成
      }
      
      // 根据不同操作系统设置字体路径
      const platform = os.platform();
      console.log('当前操作系统平台:', platform);
      
      // 定义不同系统的字体路径
      let fontPath = '';
      let alternateFonts = [];
      
      if (platform === 'win32') {
        // Windows系统 - 通用路径
        fontPath = 'C:\\Windows\\Fonts\\simhei.ttf'; // 黑体
        alternateFonts = [
          'C:\\Windows\\Fonts\\msyh.ttf',           // 微软雅黑
          'C:\\Windows\\Fonts\\simsun.ttc',         // 宋体
          'C:\\Windows\\Fonts\\simkai.ttf',         // 楷体
          'C:\\Windows\\Fonts\\simfang.ttf',        // 仿宋
          // 添加可能存在的其他字体
          'C:\\Windows\\Fonts\\NotoSansSC-VF.ttf',  // Noto Sans SC
          'C:\\Windows\\Fonts\\NotoSerifSC-VF.ttf', // Noto Serif SC
          'C:\\Windows\\Fonts\\msyhl.ttc',          // 微软雅黑细体
          'C:\\Windows\\Fonts\\simhei.ttf',         // 黑体
          'C:\\Windows\\Fonts\\simkai.ttf',         // 楷体
          'C:\\Windows\\Fonts\\simli.ttf',          // 隶书
          'C:\\Windows\\Fonts\\simyou.ttf'          // 幼圆
        ];
      } else if (platform === 'linux') {
        // Linux系统 - 通用路径
        // 首选文泉驿微米黑，这是大多数Linux发行版都会安装的中文字体
        fontPath = '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc'; // 文泉驿微米黑

        // 创建一个包含多种可能路径的列表
        // 不同的Linux发行版可能将字体放在不同的位置
        alternateFonts = [
          // 文泉驿字体 - 几乎所有Linux发行版都支持
          '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc',
          '/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc',
          '/usr/share/fonts/wqy-microhei/wqy-microhei.ttc',
          '/usr/share/fonts/wqy-zenhei/wqy-zenhei.ttc',

          // Noto字体 - Google开源字体，许多发行版默认安装
          '/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc',
          '/usr/share/fonts/opentype/noto/NotoSerifCJK-Regular.ttc',
          '/usr/share/fonts/noto-cjk/NotoSansCJK-Regular.ttc',
          '/usr/share/fonts/noto/NotoSansCJK-Regular.ttc',
          '/usr/share/fonts/truetype/noto/NotoSansCJK-Regular.ttc',

          // 思源字体 - Adobe开源字体
          '/usr/share/fonts/adobe-source-han-sans-cn/SourceHanSansCN-Regular.otf',
          '/usr/share/fonts/adobe-source-han-serif-cn/SourceHanSerifCN-Regular.otf',

          // 其他常见中文字体
          '/usr/share/fonts/truetype/arphic/uming.ttc', // 文鼎明体
          '/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf',

          // 备用非中文字体 - 如果所有中文字体都不可用
          '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf'
        ];
      } else if (platform === 'darwin') {
        // macOS系统 - 通用路径
        fontPath = '/System/Library/Fonts/PingFang.ttc'; // 苹方字体，macOS默认中文字体
        alternateFonts = [
          // 系统自带中文字体
          '/System/Library/Fonts/PingFang.ttc',           // 苹方
          '/Library/Fonts/STHeiti Light.ttc',             // 华文黑体细体
          '/System/Library/Fonts/STHeiti Light.ttc',      // 系统华文黑体细体
          '/Library/Fonts/STHeiti Medium.ttc',            // 华文黑体中等
          '/System/Library/Fonts/STHeiti Medium.ttc',     // 系统华文黑体中等
          '/Library/Fonts/Hiragino Sans GB.ttc',          // 冬青黑体
          '/System/Library/Fonts/Hiragino Sans GB.ttc',   // 系统冬青黑体

          // 其他可能存在的字体
          '/Library/Fonts/Arial Unicode.ttf',             // Arial Unicode
          '/System/Library/Fonts/AppleSDGothicNeo.ttc',   // Apple SD Gothic Neo
          '/Library/Fonts/Songti.ttc',                    // 宋体
          '/System/Library/Fonts/Songti.ttc'              // 系统宋体
        ];
      }
      
      // 创建PDF文档，使用支持中文的设置
      let doc = null;
      try {
        doc = new PDFDocument({ 
          margin: 50,
          info: {
            Title: report.title || 'AI康复分析报告',
            Author: '辅医AI助手',
            Subject: '医疗分析报告',
            Keywords: '健康,AI,医疗,报告',
            CreationDate: new Date()
          }
        });
        console.log('成功创建PDF文档对象');
        
      } catch (pdfDocErr) {
        console.error('创建PDF文档对象失败:', pdfDocErr);
        throw new Error('创建PDF文档失败: ' + pdfDocErr.message);
      }
      
      // 尝试注册中文字体
      let fontLoaded = false;
      
      // 尝试首选字体
      if (fs.existsSync(fontPath)) {
        try {
          doc.registerFont('ChineseFont', fontPath);
          doc.font('ChineseFont');
          console.log('成功加载首选中文字体:', fontPath);
          fontLoaded = true;
        } catch (fontErr) {
          console.error('加载首选字体失败:', fontErr);
        }
      } else {
        console.warn('警告：首选中文字体文件不存在:', fontPath);
        
        // 尝试替代字体
        for (const altFont of alternateFonts) {
          if (fs.existsSync(altFont)) {
            try {
              doc.registerFont('ChineseFont', altFont);
              doc.font('ChineseFont');
              console.log('成功加载替代中文字体:', altFont);
              fontLoaded = true;
              break;
            } catch (altFontErr) {
              console.error('加载替代字体失败:', altFontErr);
              continue;
            }
          }
        }
      }
      
      // 如果无法加载任何字体，使用pdfkit内置字体并记录警告
      if (!fontLoaded) {
        console.error('错误：无法找到任何可用的中文字体，PDF中的中文可能显示为乱码');
        console.info('可用的系统字体路径:', fontPath, alternateFonts);
        
        // 尝试系统字体搜索
        const fontDirectories = platform === 'win32' 
          ? ['C:\\Windows\\Fonts'] 
          : platform === 'darwin'
            ? ['/Library/Fonts', '/System/Library/Fonts']
            : ['/usr/share/fonts', '/usr/local/share/fonts'];
        
        console.log('搜索以下目录中的中文字体:', fontDirectories);
        
        // 记录系统可用字体情况，帮助诊断
        try {
          for (const dir of fontDirectories) {
            if (fs.existsSync(dir)) {
              const files = fs.readdirSync(dir).filter(f => 
                f.endsWith('.ttf') || f.endsWith('.ttc') || f.endsWith('.otf')
              );
              console.log(`${dir}目录中的字体文件:`, files.slice(0, 10).join(', '), files.length > 10 ? `...等${files.length}个文件` : '');
              
              // 尝试找到任何可能包含中文的字体
              const possibleChineseFonts = files.filter(f => 
                f.toLowerCase().includes('chinese') || 
                f.toLowerCase().includes('cjk') || 
                f.toLowerCase().includes('ming') || 
                f.toLowerCase().includes('song') || 
                f.toLowerCase().includes('hei') || 
                f.toLowerCase().includes('kai') ||
                f.toLowerCase().includes('gothic') ||
                f.toLowerCase().includes('noto')
              );
              
              if (possibleChineseFonts.length > 0) {
                const chineseFont = path.join(dir, possibleChineseFonts[0]);
                console.log('找到可能的中文字体:', chineseFont);
                try {
                  doc.registerFont('FoundChineseFont', chineseFont);
                  doc.font('FoundChineseFont');
                  fontLoaded = true;
                  break;
                } catch (foundFontErr) {
                  console.error('加载找到的字体失败:', foundFontErr);
                }
              }
            }
          }
        } catch (fsErr) {
          console.error('搜索系统字体时出错:', fsErr);
        }
      }

      // 如果依然无法加载字体，尝试使用本地字体目录中的字体
      if (!fontLoaded) {
        console.warn('尝试使用本地字体目录中的字体...');

        // 创建字体目录
        const fontDir = path.join(__dirname, '..', '..', 'fonts');
        if (fs.existsSync(fontDir)) {
          try {
            // 检查是否有字体文件
            const fontFiles = fs.readdirSync(fontDir)
              .filter(file => file.endsWith('.ttf') || file.endsWith('.otf') || file.endsWith('.ttc'));

            console.log(`在本地字体目录中找到 ${fontFiles.length} 个字体文件`);

            // 优先使用OTF格式的字体，因为PDFKit对这种格式支持最好
            const otfFonts = fontFiles.filter(file => file.endsWith('.otf'));
            console.log(`其中有 ${otfFonts.length} 个OTF格式的字体`);

            // 定义要尝试的字体列表
            let fontsToTry = [];

            // 如果有OTF字体，优先使用这些字体
            if (otfFonts.length > 0) {
              // 在OTF字体中，优先使用思源黑体或Noto Sans SC
              const priorityOtfFonts = otfFonts.filter(file =>
                file.toLowerCase().includes('sourcehansans') ||
                file.toLowerCase().includes('notosanssc')
              );

              fontsToTry = priorityOtfFonts.length > 0 ? priorityOtfFonts : otfFonts;
              console.log(`将尝试加载以下OTF字体: ${fontsToTry.join(', ')}`);
            } else {
              // 如果没有OTF字体，尝试其他格式
              // 优先使用中文字体
              const priorityFonts = fontFiles.filter(file =>
                file.toLowerCase().includes('wqy') ||
                file.toLowerCase().includes('noto') ||
                file.toLowerCase().includes('source') ||
                file.toLowerCase().includes('sim') ||
                file.toLowerCase().includes('微') ||
                file.toLowerCase().includes('黑') ||
                file.toLowerCase().includes('宋') ||
                file.toLowerCase().includes('楷') ||
                file.toLowerCase().includes('圆')
              );

              fontsToTry = priorityFonts.length > 0 ? priorityFonts : fontFiles;
              console.log(`将尝试加载以下非OTF字体: ${fontsToTry.join(', ')}`);
            }

            // 尝试加载每个字体
            for (const fontFile of fontsToTry) {
              const fontPath = path.join(fontDir, fontFile);
              console.log(`尝试加载本地字体: ${fontPath}`);

              try {
                doc.registerFont('LocalChineseFont', fontPath);
                doc.font('LocalChineseFont');
                console.log('成功加载本地中文字体:', fontPath);
                fontLoaded = true;
                break;
              } catch (localFontErr) {
                console.error(`加载本地字体失败: ${fontFile}`, localFontErr);
              }
            }
          } catch (err) {
            console.error('读取本地字体目录失败:', err);
          }
        } else {
          console.log(`本地字体目录不存在: ${fontDir}`);

          // 尝试创建字体目录
          try {
            fs.mkdirSync(fontDir, { recursive: true });
            console.log(`已创建本地字体目录: ${fontDir}`);
            console.log('请运行 node scripts/install_fonts.js 安装字体');
          } catch (mkdirErr) {
            console.error('创建本地字体目录失败:', mkdirErr);
          }
        }

        // 如果仍然无法加载字体，使用默认字体
      if (!fontLoaded) {
        console.warn('将使用默认字体继续生成PDF，中文可能显示为乱码');
        }
      }
      
      // 设置缓冲区接收PDF数据
      const buffers = [];
      
      doc.on('data', buffer => buffers.push(buffer));
      doc.on('end', () => {
        console.log('PDF生成完成，大小:', buffers.reduce((acc, buf) => acc + buf.length, 0), '字节');
        resolve(Buffer.concat(buffers));
      });
      
      try {
        // 添加标题和生成日期
        doc.fontSize(24).fillColor('#1976D2').text(report.title || 'AI康复分析报告', { align: 'center' });
        doc.moveDown(0.5);
        doc.fontSize(12).fillColor('#333333').text(`报告生成时间: ${new Date(report.created_at).toLocaleString()}`, { align: 'center' });
        doc.moveDown(2);
        
        // 添加患者基本信息区块 - 使用蓝色背景区块
        doc.rect(50, doc.y, doc.page.width - 100, 30).fill('#E3F2FD');
        doc.moveUp();
        doc.fontSize(16).fillColor('#1976D2').text('患者信息', { align: 'left', indent: 10 });
        doc.moveDown(0.5);
        
        // 表格样式的患者信息
        const patientInfoStart = doc.y + 5; // 增加上边距
        doc.fontSize(12).fillColor('#333333'); // 使用深灰色而不是黑色，减少视觉压力
        
        // 第一列
        doc.text('姓名:', { continued: true, indent: 15 });
        doc.fontSize(12).fillColor('#000000').text(` ${patient?.name || '未知'}`, { align: 'left' });
        
        doc.fontSize(12).fillColor('#333333').text('性别:', { continued: true, indent: 15 });
        doc.fontSize(12).fillColor('#000000').text(` ${patient?.gender === 'MALE' ? '男' : patient?.gender === 'FEMALE' ? '女' : '未知'}`, { align: 'left' });
        
        if (patient?.birthDate) {
          const birthYear = new Date(patient.birthDate).getFullYear();
          const currentYear = new Date().getFullYear();
          const age = currentYear - birthYear;
          
          doc.fontSize(12).fillColor('#333333').text('年龄:', { continued: true, indent: 15 });
          doc.fontSize(12).fillColor('#000000').text(` ${age}岁`, { align: 'left' });
        }
        
        // 重置Y坐标，创建第二列
        doc.y = patientInfoStart;
        doc.x = 300;
        
        if (patient?.height && patient?.weight) {
          doc.fontSize(12).fillColor('#333333').text('身高:', { continued: true });
          doc.fontSize(12).fillColor('#000000').text(` ${patient.height} cm`, { align: 'left' });
          
          doc.x = 300;
          doc.fontSize(12).fillColor('#333333').text('体重:', { continued: true });
          doc.fontSize(12).fillColor('#000000').text(` ${patient.weight} kg`, { align: 'left' });
          
          // 计算BMI
          const bmi = patient.bmi || (patient.weight / Math.pow(patient.height / 100, 2)).toFixed(1);
          doc.x = 300;
          doc.fontSize(12).fillColor('#333333').text('BMI:', { continued: true });
          doc.fontSize(12).fillColor('#000000').text(` ${bmi}`, { align: 'left' });
          
          // 添加BMI状态
          let bmiStatus = '';
          let bmiColor = '';
          
          if (bmi < 18.5) {
            bmiStatus = '偏瘦';
            bmiColor = '#FFA500'; // 橙色
          } else if (bmi < 24) {
            bmiStatus = '正常';
            bmiColor = '#008000'; // 绿色
          } else if (bmi < 28) {
            bmiStatus = '超重';
            bmiColor = '#FFA500'; // 橙色
          } else {
            bmiStatus = '肥胖';
            bmiColor = '#FF0000'; // 红色
          }
          
          doc.x = 300;
          doc.fontSize(12).fillColor('#333333').text('BMI状态:', { continued: true });
          doc.fillColor(bmiColor).text(` ${bmiStatus}`, { align: 'left' });
        }
        
        // 恢复正常布局
        doc.x = 50;
        const afterPatientInfoY = Math.max(doc.y, patientInfoStart + 4 * 18); // 增加行高
        doc.y = afterPatientInfoY + 10; // 增加底部边距
        doc.fillColor('#000000'); // 重置颜色为黑色
        
        // 添加病情概述 - 使用蓝色背景区块
        doc.rect(50, doc.y, doc.page.width - 100, 30).fill('#E3F2FD');
        doc.moveUp();
        doc.fontSize(16).fillColor('#1976D2').text('病情综述', { align: 'left', indent: 10 });
        doc.moveDown(1);
        
        // 添加病情概述内容 - 使用较大的字体和行距
        doc.fontSize(12).fillColor('#000000').text(content.summary, {
          align: 'justify',
          indent: 15,
          paragraphGap: 12,  // 进一步增大段落间距
          lineGap: 7,       // 进一步增大行间距
          width: doc.page.width - 130
        });
        doc.moveDown(1.5);
        
        // 添加紧急情况警示（如果有）
        if (content.emergencyGuidance && content.emergencyGuidance.isEmergency) {
          doc.rect(50, doc.y, doc.page.width - 100, 30).fill('#FFEBEE');
          doc.moveUp();
          doc.fontSize(16).fillColor('#D32F2F').text('紧急情况警示', { align: 'left', indent: 10 });
          doc.moveDown(1);
          
          if (content.emergencyGuidance.immediateActions && content.emergencyGuidance.immediateActions.length > 0) {
            doc.fontSize(13).fillColor('#D32F2F').text('立即行动:', { indent: 15 });
            doc.moveDown(0.3);
            content.emergencyGuidance.immediateActions.forEach(action => {
              doc.fontSize(12).fillColor('#000000').text(`•   ${action}`, { 
                indent: 20,
                lineGap: 7,  // 进一步增大行间距
                width: doc.page.width - 130
              });
            });
          }
          
          if (content.emergencyGuidance.nextSteps && content.emergencyGuidance.nextSteps.length > 0) {
            doc.moveDown(0.5);
            doc.fontSize(13).fillColor('#D32F2F').text('后续步骤:', { indent: 15 });
            doc.moveDown(0.3);
            content.emergencyGuidance.nextSteps.forEach(step => {
              doc.fontSize(12).fillColor('#000000').text(`•   ${step}`, { 
                indent: 20,
                lineGap: 7,  // 进一步增大行间距
                width: doc.page.width - 130
              });
            });
          }
          
          doc.moveDown(1.5);
        }
        
        // 添加可能诊断（如果有）- 使用浅紫色背景区块
        if (content.differentialDiagnosis && content.differentialDiagnosis.possibleConditions && content.differentialDiagnosis.possibleConditions.length > 0) {
          doc.rect(50, doc.y, doc.page.width - 100, 30).fill('#EDE7F6');
          doc.moveUp();
          doc.fontSize(16).fillColor('#673AB7').text('可能诊断', { align: 'left', indent: 10 });
          doc.moveDown(1);
          
          content.differentialDiagnosis.possibleConditions.forEach((condition, index) => {
            // 显示疾病名称和可能性
            doc.fontSize(13).fillColor('#673AB7').text(`${index + 1}. ${condition.condition}`, { 
              continued: false,
              indent: 15
            });
            doc.fontSize(12).fillColor('#673AB7').text(`可能性: ${condition.probability}%`, { 
              indent: 25
            });
            
            // 显示描述
            if (condition.description) {
              doc.moveDown(0.3);
              doc.fontSize(12).fillColor('#000000').text(`${condition.description}`, {
                indent: 25,
                width: doc.page.width - 140,
                lineGap: 6  // 进一步增大行间距
              });
            }
            
            // 显示判断依据
            if (condition.evidenceFrom && condition.evidenceFrom.length > 0) {
              doc.moveDown(0.5);
              doc.fontSize(12).fillColor('#000000').text('判断依据:', { indent: 25 });
              condition.evidenceFrom.forEach(evidence => {
                doc.text(`•   ${evidence}`, { 
                  indent: 30,
                  width: doc.page.width - 150,
                  lineGap: 6  // 进一步增大行间距
                });
              });
            }
            
            // 显示确诊方法
            if (condition.confirmationMethods && condition.confirmationMethods.length > 0) {
              doc.moveDown(0.5);
              doc.fontSize(12).fillColor('#000000').text('建议的确诊方法:', { indent: 25 });
              condition.confirmationMethods.forEach(method => {
                doc.text(`•   ${method}`, { 
                  indent: 30,
                  width: doc.page.width - 150,
                  lineGap: 6  // 进一步增大行间距
                });
              });
            }
            
            doc.moveDown(1);
          });
          
          doc.moveDown(1);
        }
        
        // 添加治疗方案 - 使用浅绿色背景区块
        if (content.treatmentPlan && content.treatmentPlan.options && content.treatmentPlan.options.length > 0) {
          doc.rect(50, doc.y, doc.page.width - 100, 30).fill('#E8F5E9');
          doc.moveUp();
          doc.fontSize(16).fillColor('#2E7D32').text('治疗方案建议', { align: 'left', indent: 10 });
          doc.moveDown(1);
          
          content.treatmentPlan.options.forEach((option, index) => {
            // 处理新旧两种治疗方案结构
            if (typeof option === 'string') {
              // 旧版API返回的简单字符串格式
              doc.fontSize(12).fillColor('#000000').text(`${index + 1}. ${option}`, {
                indent: 15,
                width: doc.page.width - 130,
                lineGap: 6  // 进一步增大行间距
              });
            } else {
              // 新版API返回的详细结构
              doc.fontSize(13).fillColor('#2E7D32').text(`${index + 1}. ${option.name || '方案' + (index+1)}`, {
                indent: 15
              });
              
              // 显示适合度
              if (option.suitabilityScore !== undefined) {
                doc.fontSize(12).fillColor('#2E7D32').text(`适合度: ${option.suitabilityScore}%`, {
                  indent: 25
                });
              }
              
              // 显示方案描述
              if (option.description) {
                doc.moveDown(0.3);
                doc.fontSize(12).fillColor('#000000').text(`${option.description}`, {
                  indent: 25,
                  width: doc.page.width - 140,
                  lineGap: 6  // 进一步增大行间距
                });
              }
              
              // 显示预后数据
              if (option.prognosisData) {
                doc.moveDown(0.5);
                doc.fontSize(12).fillColor('#000000').text('预后评估:', { indent: 25 });
                
                if (option.prognosisData.survivalRate) {
                  doc.text(`•   ${option.prognosisData.survivalRate}`, { 
                    indent: 30,
                    width: doc.page.width - 150,
                    lineGap: 6  // 进一步增大行间距
                  });
                }
                
                if (option.prognosisData.remissionRate) {
                  doc.text(`•   ${option.prognosisData.remissionRate}`, { 
                    indent: 30,
                    width: doc.page.width - 150,
                    lineGap: 6  // 进一步增大行间距
                  });
                }
                
                if (option.prognosisData.recurrenceRisk) {
                  doc.text(`•   ${option.prognosisData.recurrenceRisk}`, { 
                    indent: 30,
                    width: doc.page.width - 150,
                    lineGap: 6  // 进一步增大行间距
                  });
                }
              }
              
              // 显示预算信息
              if (option.budgetEstimation) {
                doc.moveDown(0.5);
                doc.fontSize(12).fillColor('#000000').text('费用预算:', { indent: 25 });
                
                let costText = '';
                if (option.budgetEstimation.minCost !== undefined && option.budgetEstimation.maxCost !== undefined) {
                  const currency = option.budgetEstimation.currency || 'CNY';
                  costText = `•   ${option.budgetEstimation.minCost}-${option.budgetEstimation.maxCost} ${currency === 'CNY' ? '元' : currency}`;
                }
                
                if (costText) {
                  doc.text(`•   ${costText.substring(2)}`, { 
                    indent: 30,
                    width: doc.page.width - 150,
                    lineGap: 6  // 进一步增大行间距
                  });
                }
                
                // 显示医保信息
                if (option.budgetEstimation.insuranceCoverage) {
                  doc.text(`•   医保报销: ${option.budgetEstimation.insuranceCoverage}`, { 
                    indent: 30,
                    width: doc.page.width - 150,
                    lineGap: 6  // 进一步增大行间距
                  });
                }
              }
              
              // 显示后续随访计划
              if (option.followUpPlan && option.followUpPlan.length > 0) {
                doc.moveDown(0.5);
                doc.fontSize(12).fillColor('#000000').text('后续随访:', { indent: 25 });
                option.followUpPlan.forEach(plan => {
                  doc.text(`•   ${plan}`, { 
                    indent: 30,
                    width: doc.page.width - 150,
                    lineGap: 6  // 进一步增大行间距
                  });
                });
              }
            }
            
            doc.moveDown(1);
          });
          
          // 添加后续随访（旧版API格式）
          if (content.treatmentPlan.followUp && content.treatmentPlan.followUp.length > 0) {
            doc.fontSize(12).fillColor('#2E7D32').text('后续随访:', { indent: 15 });
            content.treatmentPlan.followUp.forEach(followUp => {
              doc.fontSize(12).fillColor('#000000').text(`•   ${followUp}`, { 
                indent: 20,
                width: doc.page.width - 130,
                lineGap: 6  // 进一步增大行间距
              });
            });
          }
          
          doc.moveDown(1.5);
        }
        
        // 添加医院推荐 - 使用浅蓝色背景区块
        if (content.hospitalRecommendations && content.hospitalRecommendations.hospitals && content.hospitalRecommendations.hospitals.length > 0) {
          doc.rect(50, doc.y, doc.page.width - 100, 30).fill('#E1F5FE');
          doc.moveUp();
          doc.fontSize(16).fillColor('#0288D1').text('医院科室推荐', { align: 'left', indent: 10 });
          doc.moveDown(1);
          
          // 显示地区信息
          if (content.hospitalRecommendations.targetRegion || content.hospitalRecommendations.region) {
            const region = content.hospitalRecommendations.targetRegion || content.hospitalRecommendations.region;
            doc.fontSize(12).fillColor('#0288D1').text(`地区: ${region}`, {
              indent: 15
            });
            doc.moveDown(0.5);
          }
          
          content.hospitalRecommendations.hospitals.forEach((hospital, index) => {
            doc.fontSize(13).fillColor('#0288D1').text(`${index + 1}. ${hospital.name} - ${hospital.department}`, {
              indent: 15
            });
            
            doc.fontSize(12).fillColor('#000000').text(`等级: ${hospital.level}`, {
              indent: 25
            });
            
            // 显示匹配度
            if (hospital.matchScore !== undefined) {
              doc.text(`匹配度: ${hospital.matchScore}%`, {
                indent: 25
              });
            }
            
            // 显示优势或推荐理由
            const advantages = hospital.advantages || hospital.reasons;
            if (advantages && advantages.length > 0) {
              doc.moveDown(0.3);
              doc.text('专科优势:', { indent: 25 });
              advantages.forEach(advantage => {
                doc.text(`•   ${advantage}`, { 
                  indent: 30,
                  width: doc.page.width - 150,
                  lineGap: 6  // 进一步增大行间距
                });
              });
            }
            
            // 显示联系信息
            if (hospital.contactInfo || hospital.contact) {
              doc.moveDown(0.5);
              doc.text('联系方式:', { indent: 25 });
              
              if (hospital.contactInfo) {
                if (hospital.contactInfo.website) {
                  doc.text(`•   官网: ${hospital.contactInfo.website}`, { 
                    indent: 30,
                    width: doc.page.width - 150,
                    lineGap: 6  // 进一步增大行间距
                  });
                }
                
                if (hospital.contactInfo.phone) {
                  doc.text(`•   电话: ${hospital.contactInfo.phone}`, { 
                    indent: 30,
                    width: doc.page.width - 150,
                    lineGap: 6  // 进一步增大行间距
                  });
                }
                
                if (hospital.contactInfo.wechatPublic) {
                  doc.text(`•   微信公众号: ${hospital.contactInfo.wechatPublic}`, { 
                    indent: 30,
                    width: doc.page.width - 150,
                    lineGap: 6  // 进一步增大行间距
                  });
                }
                
                if (hospital.contactInfo.appointmentPlatform) {
                  doc.text(`•   预约平台: ${hospital.contactInfo.appointmentPlatform}`, { 
                    indent: 30,
                    width: doc.page.width - 150,
                    lineGap: 6  // 进一步增大行间距
                  });
                }
              } else if (hospital.contact) {
                doc.text(`•   ${hospital.contact}`, { 
                  indent: 30,
                  width: doc.page.width - 150,
                  lineGap: 6  // 进一步增大行间距
                });
              }
            }
            
            doc.moveDown(1);
          });
          
          doc.moveDown(1);
        }
        
        // 添加生活方式和心理健康建议 - 使用浅紫色背景区块
        if (content.lifestyleAndMentalHealth) {
          doc.rect(50, doc.y, doc.page.width - 100, 30).fill('#F3E5F5');
          doc.moveUp();
          doc.fontSize(16).fillColor('#8E24AA').text('生活方式与心理健康建议', { align: 'left', indent: 10 });
          doc.moveDown(1);
          
          // 添加生活方式建议
          if (content.lifestyleAndMentalHealth.lifestyle) {
            const lifestyle = content.lifestyleAndMentalHealth.lifestyle;
            
            if (lifestyle.diet && lifestyle.diet.length > 0) {
              doc.fontSize(13).fillColor('#8E24AA').text('饮食建议:', { indent: 15 });
              doc.moveDown(0.3);
              lifestyle.diet.forEach(item => {
                doc.fontSize(12).fillColor('#000000').text(`•   ${item}`, { 
                  indent: 20,
                  width: doc.page.width - 130,
                  lineGap: 6  // 进一步增大行间距
                });
              });
              doc.moveDown(0.8);
            }
            
            if (lifestyle.exercise && lifestyle.exercise.length > 0) {
              doc.fontSize(13).fillColor('#8E24AA').text('运动建议:', { indent: 15 });
              doc.moveDown(0.3);
              lifestyle.exercise.forEach(item => {
                doc.fontSize(12).fillColor('#000000').text(`•   ${item}`, { 
                  indent: 20,
                  width: doc.page.width - 130,
                  lineGap: 6  // 进一步增大行间距
                });
              });
              doc.moveDown(0.8);
            }
            
            if (lifestyle.habits && lifestyle.habits.length > 0) {
              doc.fontSize(13).fillColor('#8E24AA').text('习惯建议:', { indent: 15 });
              doc.moveDown(0.3);
              lifestyle.habits.forEach(item => {
                doc.fontSize(12).fillColor('#000000').text(`•   ${item}`, { 
                  indent: 20,
                  width: doc.page.width - 130,
                  lineGap: 6  // 进一步增大行间距
                });
              });
              doc.moveDown(0.8);
            }
          }
          
          // 添加心理健康建议
          if (content.lifestyleAndMentalHealth.mentalHealth && 
              content.lifestyleAndMentalHealth.mentalHealth.copingStrategies && 
              content.lifestyleAndMentalHealth.mentalHealth.copingStrategies.length > 0) {
            
            doc.fontSize(13).fillColor('#8E24AA').text('心理健康建议:', { indent: 15 });
            doc.moveDown(0.3);
            content.lifestyleAndMentalHealth.mentalHealth.copingStrategies.forEach(item => {
              doc.fontSize(12).fillColor('#000000').text(`•   ${item}`, { 
                indent: 20,
                width: doc.page.width - 130,
                lineGap: 6  // 进一步增大行间距
              });
            });
            doc.moveDown(0.8);
            
            if (content.lifestyleAndMentalHealth.mentalHealth.resources && 
                content.lifestyleAndMentalHealth.mentalHealth.resources.length > 0) {
              doc.fontSize(13).fillColor('#8E24AA').text('心理健康资源:', { indent: 15 });
              doc.moveDown(0.3);
              content.lifestyleAndMentalHealth.mentalHealth.resources.forEach(item => {
                doc.fontSize(12).fillColor('#000000').text(`•   ${item}`, { 
                  indent: 20,
                  width: doc.page.width - 130,
                  lineGap: 6  // 进一步增大行间距
                });
              });
            }
          }
          
          doc.moveDown(1.5);
        }
        
        // 添加免责声明 - 使用浅橙色背景带圆角
        doc.moveDown(1);
        
        // 绘制圆角矩形，正确使用PDFKit的圆角矩形API
        const disclaimerY = doc.y;
        const disclaimerHeight = 50;
        const disclaimerWidth = doc.page.width - 100;
        const cornerRadius = 8;
        
        // 保存当前图形状态
        doc.save();
        // 设置填充颜色
        doc.fillColor('#FFF3E0');
        // 开始路径
        doc.roundedRect(50, disclaimerY, disclaimerWidth, disclaimerHeight, cornerRadius).fill();
        // 恢复图形状态
        doc.restore();
        
        // 移动到矩形内部开始写文字
        doc.y = disclaimerY + 10; // 为免责声明添加顶部内边距
        
        // 使用自定义免责声明或默认内容
        const disclaimer = content.disclaimer || '此报告由AI生成，仅供参考，不构成医疗诊断或治疗建议。请咨询专业医生获取正式医疗意见。';
        doc.fontSize(11).fillColor('#E65100').text('免责声明:', {
          align: 'center'
        });
        doc.moveDown(0.5);
        doc.fontSize(10).fillColor('#000000').text(disclaimer, { 
          align: 'center',
          width: doc.page.width - 150,
          lineGap: 6  // 进一步增大行间距
        });
        
        // 添加简单的页脚（只在当前页面添加）
        doc.moveDown(2);
        
        // 使用简单的页脚格式
        const reportDate = new Date(report.created_at).toLocaleDateString();
        doc.fontSize(9).fillColor('#666666').text(
          `生成日期: ${reportDate}`,
          { align: 'center' }
        );
        
        // 结束文档生成
        doc.end();
        console.log('PDF文档结构已完成，等待生成完成事件');
      } catch (renderErr) {
        console.error('渲染PDF内容时出错:', renderErr);
        // 如果渲染过程出错，但文档对象已创建，尝试结束文档以避免挂起
        try {
          if (doc) doc.end();
        } catch (endErr) {
          console.error('结束PDF文档时出错:', endErr);
        }
        reject(new Error('渲染PDF内容失败: ' + renderErr.message));
      }
    } catch (error) {
      console.error('生成PDF报告时出错:', error);
      reject(error);
    }
  });
}

/**
 * 重新生成AI报告
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.regenerateReport = async (req, res) => {
  try {
    const { diseaseId, patientId, targetRegion } = req.body;

    // 将驼峰命名参数转换为蛇形命名，兼容旧版API
    const snakeCaseParams = toSnakeCase({
      diseaseId,
      patientId,
      targetRegion,
      userId: req.user.id
    });
    const userId = req.user.id;
    
    // 增加调试日志
    console.log('===== 重新生成AI报告 =====');
    console.log('请求内容:', req.body);
    console.log('用户ID:', userId);
    
    // 数据验证
    if (!diseaseId) {
      console.log('验证失败: 缺少diseaseId');
      return res.status(400).json({ message: '缺少必要参数: diseaseId' });
    }
    
    if (!patientId) {
      console.log('验证失败: 缺少patientId');
      return res.status(400).json({ message: '缺少必要参数: patientId' });
    }
    
    // 验证数据是否存在
    try {
      const disease = await Disease.query().findById(diseaseId);
      if (!disease) {
        console.log('验证失败: 未找到病历记录:', diseaseId);
        return res.status(400).json({ message: `未找到ID为 ${diseaseId} 的病历记录` });
      }
      
      const patient = await Patient.query().findById(patientId);
      if (!patient) {
        console.log('验证失败: 未找到患者记录:', patientId);
        return res.status(400).json({ message: `未找到ID为 ${patientId} 的患者记录` });
      }
      
      console.log('数据验证通过: 找到病历和患者记录');
    } catch (verifyError) {
      console.error('数据验证过程中出错:', verifyError);
      return res.status(400).json({ message: '数据验证失败', details: verifyError.message });
    }
    
    // 检查用户配额
    let quota = await AIReportQuota.query().where({ user_id: userId }).first();
    if (!quota) {
      // 创建用户配额记录
      const user = await User.query().findById(userId);
      const defaultQuota = {
        PERSONAL: 3,
        FAMILY: 20,
        PROFESSIONAL: 50
      };
      
      const monthlyQuota = defaultQuota[user.level] || 3;
      
      quota = await AIReportQuota.query().insert({
        user_id: userId,
        monthly_quota: monthlyQuota,
        used_this_month: 0,
        total_used: 0,
        last_reset_date: new Date().toISOString()
      });
    }

    // 检查配额是否足够
    const remainingMonthlyQuota = Math.max(0, quota.monthly_quota - quota.used_this_month);
    const hasRemainingQuota = remainingMonthlyQuota > 0 || quota.additional_quota > 0;

    if (!hasRemainingQuota) {
      return res.status(403).json({ message: '您的AI分析次数已用完' });
    }
    
    console.log('配额检查通过, 开始重新生成AI报告');
    
    // 使用AI报告服务生成报告，标记为重新生成
    const result = await generateAIReport(
      snakeCaseParams.disease_id, 
      snakeCaseParams.patient_id, 
      snakeCaseParams.user_id, 
      snakeCaseParams.target_region,
      true // 标记为重新生成
    );
    
    // 更新用户配额
    if (remainingMonthlyQuota > 0) {
      // 先使用月度配额
    await AIReportQuota.query()
        .where({ user_id: userId })
      .patch({
        used_this_month: quota.used_this_month + 1,
        total_used: quota.total_used + 1,
        updated_at: new Date().toISOString()
        });
    } else {
      // 使用额外配额
      await AIReportQuota.query()
        .where({ user_id: userId })
        .patch({
          used_this_month: quota.used_this_month + 1,
          total_used: quota.total_used + 1,
          additional_quota: quota.additional_quota - 1,
          updated_at: new Date().toISOString()
        });
    }
    
    // 转换为驼峰格式
    const plainReport = result.aiReport;
    const formattedReport = {
      id: plainReport.id,
      diseaseId: plainReport.disease_id,
      patientId: plainReport.patient_id,
      userId: plainReport.user_id,
      recordId: plainReport.record_id,
      title: plainReport.title,
      templateType: plainReport.template_type,
      content: plainReport.content,
      status: plainReport.status,
      createdAt: plainReport.created_at,
      updatedAt: plainReport.updated_at
    };
    
    console.log('AI报告重新生成成功:', plainReport.id);
    
    res.status(201).json({
      aiReport: formattedReport,
      recordId: result.recordId || '',
      message: '报告重新生成成功'
    });
  } catch (error) {
    console.error('重新生成AI报告失败:', error);
    errorHandler(error, res);
  }
};

/**
 * 手动修复卡在PROCESSING状态的AI报告 - 管理员功能
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.fixStuckReports = async (req, res) => {
  try {
    // 验证用户权限(只允许管理员触发)
    if (req.user.role !== 'ADMIN') {
      return res.status(403).json({ message: '只有管理员可以执行此操作' });
    }
    
    console.log('手动触发修复卡住报告任务');
    
    // 导入修复脚本
    const { fixStuckReports } = require('../../scripts/fixStuckReports');
    
    // 执行修复
    const result = await fixStuckReports();
    
    return res.json({
      success: true,
      message: '已修复卡住的报告',
      details: result
    });
  } catch (error) {
    console.error('修复卡住报告时出错:', error);
    errorHandler(error, res);
  }
};

module.exports = exports; 