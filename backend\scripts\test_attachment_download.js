/**
 * 测试附件下载功能
 * 验证中文文件名和CORS问题是否已修复
 * 用法: node scripts/test_attachment_download.js
 */

const fs = require('fs');
const path = require('path');
const express = require('express');
const cors = require('cors');
const knex = require('../config/database');

// 创建一个简单的Express应用用于测试
const app = express();
const PORT = 3030;

// 启用CORS
app.use(cors({
  origin: '*',
  methods: ['GET', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  exposedHeaders: ['Content-Disposition']
}));

// 测试中文文件名和权限
app.get('/test-download', (req, res) => {
  try {
    // 测试文件路径（测试用）
    const testFilePath = path.join(__dirname, '../uploads/test-file.txt');
    
    // 如果测试文件不存在，创建一个
    if (!fs.existsSync(testFilePath)) {
      fs.writeFileSync(testFilePath, '这是一个测试文件', 'utf8');
    }
    
    // 中文文件名测试
    const chineseFileName = '测试文件名中文.txt';
    const encodedFileName = encodeURIComponent(chineseFileName)
      .replace(/'/g, '%27')
      .replace(/"/g, '%22')
      .replace(/\(/g, '%28')
      .replace(/\)/g, '%29');
    
    // 添加CORS头
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    // 设置Content-Disposition头
    res.setHeader('Content-Disposition', `attachment; filename="${encodedFileName}"; filename*=UTF-8''${encodedFileName}`);
    res.setHeader('Content-Type', 'text/plain');
    
    // 发送文件
    fs.createReadStream(testFilePath).pipe(res);
  } catch (error) {
    console.error('测试下载失败:', error);
    res.status(500).send('测试下载失败: ' + error.message);
  }
});

// 显示数据库中的所有附件记录
app.get('/list-attachments', async (req, res) => {
  try {
    // 检查attachments表是否存在
    const hasTable = await knex.schema.hasTable('attachments');
    if (!hasTable) {
      return res.status(404).json({ error: 'attachments表不存在' });
    }
    
    // 查询所有附件
    const attachments = await knex('attachments').select('*');
    res.json(attachments);
  } catch (error) {
    console.error('获取附件列表失败:', error);
    res.status(500).json({ error: error.message });
  }
});

// 提供一个HTML页面来测试附件下载
app.get('/', (req, res) => {
  const html = `
  <!DOCTYPE html>
  <html>
  <head>
    <title>附件下载测试</title>
    <meta charset="utf-8">
    <style>
      body { font-family: Arial, sans-serif; margin: 20px; }
      h1 { color: #333; }
      .btn { 
        padding: 10px 15px; 
        background: #4CAF50; 
        color: white; 
        border: none; 
        cursor: pointer; 
        margin: 5px;
      }
      .info { background: #f0f0f0; padding: 15px; margin: 15px 0; border-left: 5px solid #2196F3; }
    </style>
  </head>
  <body>
    <h1>附件下载测试</h1>
    
    <div class="info">
      <p>此页面用于测试：</p>
      <ol>
        <li>中文文件名下载</li>
        <li>CORS跨域问题</li>
        <li>Content-Disposition头设置</li>
      </ol>
    </div>
    
    <button class="btn" onclick="testDownload()">测试中文文件名下载</button>
    <button class="btn" onclick="listAttachments()">列出数据库中的附件</button>
    
    <div id="result" style="margin-top: 20px;"></div>
    
    <script>
      async function testDownload() {
        try {
          document.getElementById('result').innerHTML = '正在下载测试文件...';
          
          // 使用fetch API测试下载
          const response = await fetch('/test-download');
          
          if (!response.ok) {
            throw new Error('下载失败: ' + response.status + ' ' + response.statusText);
          }
          
          // 获取Content-Disposition头
          const contentDisposition = response.headers.get('Content-Disposition');
          
          // 获取文件内容
          const blob = await response.blob();
          
          // 创建下载链接
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = '测试文件名中文.txt'; // 尝试使用中文文件名
          document.body.appendChild(a);
          a.click();
          
          // 清理
          setTimeout(() => {
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
          }, 100);
          
          document.getElementById('result').innerHTML = 
            '下载成功! <br>Content-Disposition: ' + (contentDisposition || '未获取到');
        } catch (error) {
          document.getElementById('result').innerHTML = '错误: ' + error.message;
        }
      }
      
      async function listAttachments() {
        try {
          document.getElementById('result').innerHTML = '正在获取附件列表...';
          
          const response = await fetch('/list-attachments');
          const data = await response.json();
          
          if (response.ok) {
            let html = '<h3>附件列表 (共' + data.length + '个)</h3>';
            html += '<table border="1" style="border-collapse: collapse; width: 100%;">';
            html += '<tr><th>ID</th><th>记录ID</th><th>文件名</th><th>文件路径</th><th>文件类型</th><th>大小</th><th>上传者</th><th>上传时间</th></tr>';
            
            for (let i = 0; i < data.length; i++) {
              const att = data[i];
              html += '<tr>';
              html += '<td>' + (att.id || '') + '</td>';
              html += '<td>' + (att.record_id || '') + '</td>';
              html += '<td>' + (att.file_name || '') + '</td>';
              html += '<td>' + (att.file_path || '') + '</td>';
              html += '<td>' + (att.file_type || '') + '</td>';
              html += '<td>' + (att.file_size || 0) + ' 字节</td>';
              html += '<td>' + (att.uploaded_by || '') + '</td>';
              html += '<td>' + (att.uploaded_at || '') + '</td>';
              html += '</tr>';
            }
            
            html += '</table>';
            document.getElementById('result').innerHTML = html;
          } else {
            document.getElementById('result').innerHTML = '获取附件列表失败: ' + (data.error || '未知错误');
          }
        } catch (error) {
          document.getElementById('result').innerHTML = '错误: ' + error.message;
        }
      }
    </script>
  </body>
  </html>
  `;
  
  res.send(html);
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`附件下载测试服务器已启动: http://localhost:${PORT}`);
  console.log(`- 测试中文文件名下载: http://localhost:${PORT}/test-download`);
  console.log(`- 查看附件列表: http://localhost:${PORT}/list-attachments`);
}); 