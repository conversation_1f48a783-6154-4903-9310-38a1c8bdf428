const express = require('express');
const router = express.Router();
const Disease = require('../models/Disease');
const Patient = require('../models/Patient');
const { auth } = require('../src/middleware/auth');

/**
 * 获取疾病列表，支持筛选和搜索
 * GET /diseases?patientId={id}&stage={stage}&name={keyword}
 */
router.get('/', auth, async (req, res) => {
  try {
    const { patientId, stage, name } = req.query;
    
    // 构建查询
    let query = Disease.query()
      .where('isDeleted', false)
      .orderBy('createdAt', 'desc');
    
    // 按患者ID筛选
    if (patientId) {
      // 验证患者是否存在且属于当前用户
      const patient = await Patient.query()
        .where({ id: patientId })
        .first();
      
      if (!patient) {
        return res.status(404).json({ message: '患者不存在' });
      }
      
      if (patient.userId !== req.user.id && req.user.role !== 'ADMIN') {
        return res.status(403).json({ message: '无权访问此患者信息' });
      }
      
      query = query.where('patientId', patientId);
    } else {
      // 若不按患者ID筛选，则只显示当前用户的患者的疾病
      // 获取当前用户的所有患者ID
      const patients = await Patient.query()
        .where(function() {
          this.where('userId', req.user.id);
          if (req.user.role === 'ADMIN') {
            this.orWhereRaw('1=1');
          }
        })
        .select('id');
      
      const patientIds = patients.map(p => p.id);
      query = query.whereIn('patientId', patientIds);
    }
    
    // 按疾病阶段筛选
    if (stage) {
      query = query.where('stage', stage);
    }
    
    // 按疾病名称搜索（模糊查询）
    if (name) {
      query = query.where('name', 'like', `%${name}%`);
    }
    
    // 执行查询
    const diseases = await query;
    
    res.json(diseases);
  } catch (error) {
    console.error('获取疾病列表失败:', error);
    res.status(500).json({ message: '获取疾病列表失败', error: error.message });
  }
});

/**
 * 获取疾病详情
 * GET /diseases/:id
 */
router.get('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;
    
    // 获取疾病详情，包括患者信息
    const disease = await Disease.query()
      .where({ id, isDeleted: false })
      .withGraphFetched('patient')
      .first();
    
    if (!disease) {
      return res.status(404).json({ message: '疾病信息不存在' });
    }
    
    // 验证权限（只有患者所有者或管理员可以查看）
    if (disease.patient.userId !== req.user.id && req.user.role !== 'ADMIN') {
      return res.status(403).json({ message: '无权查看此疾病信息' });
    }
    
    res.json(disease);
  } catch (error) {
    console.error('获取疾病详情失败:', error);
    res.status(500).json({ message: '获取疾病详情失败', error: error.message });
  }
});

/**
 * 创建新疾病记录
 * POST /diseases
 */
router.post('/', auth, async (req, res) => {
  try {
    const { patientId, name, diagnosisDate, stage, description, treatment } = req.body;
    
    // 验证患者是否存在且属于当前用户
    const patient = await Patient.query()
      .where({ id: patientId })
      .first();
    
    if (!patient) {
      return res.status(404).json({ message: '患者不存在' });
    }
    
    if (patient.userId !== req.user.id && req.user.role !== 'ADMIN') {
      return res.status(403).json({ message: '无权为此患者添加疾病记录' });
    }
    
    // 创建新疾病记录
    const newDisease = await Disease.query().insert({
      patientId,
      name,
      diagnosisDate,
      stage,
      description,
      treatment
    });
    
    res.status(201).json(newDisease);
  } catch (error) {
    console.error('创建疾病记录失败:', error);
    res.status(500).json({ message: '创建疾病记录失败', error: error.message });
  }
});

/**
 * 更新疾病记录
 * PUT /diseases/:id
 */
router.put('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, diagnosisDate, stage, description, treatment, isPrivate } = req.body;
    
    // 验证疾病记录是否存在
    const disease = await Disease.query()
      .where({ id, isDeleted: false })
      .withGraphFetched('patient')
      .first();
    
    if (!disease) {
      return res.status(404).json({ message: '疾病记录不存在' });
    }
    
    // 验证权限（只有患者所有者或管理员可以修改）
    if (disease.patient.userId !== req.user.id && req.user.role !== 'ADMIN') {
      return res.status(403).json({ message: '无权修改此疾病记录' });
    }
    
    // 更新疾病记录
    const updatedDisease = await Disease.query()
      .where({ id, isDeleted: false })
      .patch({
        name: name || disease.name,
        diagnosisDate: diagnosisDate || disease.diagnosisDate,
        stage: stage || disease.stage,
        description: description !== undefined ? description : disease.description,
        treatment: treatment !== undefined ? treatment : disease.treatment,
        isPrivate: isPrivate !== undefined ? isPrivate : disease.isPrivate,
        updatedAt: new Date().toISOString()
      })
      .returning('*')
      .first();
    
    res.json(updatedDisease);
  } catch (error) {
    console.error('更新疾病记录失败:', error);
    res.status(500).json({ message: '更新疾病记录失败', error: error.message });
  }
});

/**
 * 删除疾病记录（软删除）
 * DELETE /diseases/:id
 */
router.delete('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;
    
    // 验证疾病记录是否存在
    const disease = await Disease.query()
      .where({ id, isDeleted: false })
      .withGraphFetched('patient')
      .first();
    
    if (!disease) {
      return res.status(404).json({ message: '疾病记录不存在' });
    }
    
    // 验证权限（只有患者所有者或管理员可以删除）
    if (disease.patient.userId !== req.user.id && req.user.role !== 'ADMIN') {
      return res.status(403).json({ message: '无权删除此疾病记录' });
    }
    
    // 软删除疾病记录
    await Disease.query()
      .where({ id })
      .patch({
        isDeleted: true,
        updatedAt: new Date().toISOString()
      });
    
    res.json({ message: '疾病记录已成功删除' });
  } catch (error) {
    console.error('删除疾病记录失败:', error);
    res.status(500).json({ message: '删除疾病记录失败', error: error.message });
  }
});

/**
 * 获取疾病阶段列表（用于下拉选择）
 * GET /diseases/stages
 */
router.get('/stages', auth, async (req, res) => {
  try {
    // 获取系统中使用的所有疾病阶段（去重）
    const stages = await Disease.query()
      .where('isDeleted', false)
      .distinct('stage')
      .orderBy('stage');
    
    // 提取阶段值
    const stageValues = stages.map(s => s.stage);
    
    res.json(stageValues);
  } catch (error) {
    console.error('获取疾病阶段列表失败:', error);
    res.status(500).json({ message: '获取疾病阶段列表失败', error: error.message });
  }
});

/**
 * 获取指定患者的所有疾病
 * GET /diseases/by-patient/:patientId
 */
router.get('/by-patient/:patientId', auth, async (req, res) => {
  try {
    const { patientId } = req.params;
    
    // 验证患者是否存在且属于当前用户
    const patient = await Patient.query()
      .where({ id: patientId })
      .first();
    
    if (!patient) {
      return res.status(404).json({ message: '患者不存在' });
    }
    
    if (patient.userId !== req.user.id && req.user.role !== 'ADMIN') {
      return res.status(403).json({ message: '无权访问此患者信息' });
    }
    
    // 获取患者的所有疾病记录
    const diseases = await Disease.query()
      .where({ patientId, isDeleted: false })
      .orderBy('diagnosisDate', 'desc');
    
    res.json(diseases);
  } catch (error) {
    console.error('获取患者疾病列表失败:', error);
    res.status(500).json({ message: '获取患者疾病列表失败', error: error.message });
  }
});

module.exports = router; 