import React, { useState, useEffect } from 'react';
import { Box, Typography, Paper, Chip, Badge, Tooltip } from '@mui/material';
import { Timeline, TimelineItem, TimelineConnector, TimelineContent, TimelineDot, TimelineSeparator } from '@mui/lab';
import PinDropIcon from '@mui/icons-material/PinDrop';
import LockIcon from '@mui/icons-material/Lock';
import { DiseaseInfo, RecordInfo } from './adapters';
import { styled, alpha } from '@mui/material/styles';

// 高亮的Paper组件
const HighlightedPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(1.2),
  borderLeft: `4px solid ${theme.palette.primary.main}`,
  backgroundColor: alpha(theme.palette.primary.main, 0.08),
  transition: 'all 0.3s ease',
  cursor: 'pointer',
  boxShadow: '0 2px 6px rgba(0, 0, 0, 0.1)',
  '&:hover': {
    backgroundColor: alpha(theme.palette.primary.main, 0.12),
    transform: 'translateY(-2px)',
    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.15)',
  }
}));

// 普通Paper组件
const NormalPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(1.2),
  borderLeft: `3px solid transparent`,
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  '&:hover': {
    backgroundColor: alpha(theme.palette.primary.main, 0.05),
    borderLeft: `3px solid ${alpha(theme.palette.primary.main, 0.5)}`,
    transform: 'translateY(-2px)',
    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
  }
}));

// 组件属性接口
interface DiseaseTimelineProps {
  disease: DiseaseInfo;
  isSelected: boolean;
  onClick: () => void;
}

/**
 * 带有时间轴的疾病条目组件
 * 用于显示单个疾病及其关联记录的时间线
 */
const DiseaseTimeline: React.FC<DiseaseTimelineProps> = ({ disease, isSelected, onClick }) => {
  // 跟踪组件的挂载状态
  const [isMounted, setIsMounted] = useState(false);
  
  // 挂载时添加动画效果
  useEffect(() => {
    setIsMounted(true);
    
    // 确保在卸载时重置状态
    return () => setIsMounted(false);
  }, []);
  
  // 格式化日期函数，提供多种日期格式的备选
  const formatDate = (dateString?: string | Date | null): string => {
    if (!dateString) return '-';
    
    // 如果是Date对象，转换为ISO字符串
    const dateStr = typeof dateString === 'object' && dateString instanceof Date
      ? dateString.toISOString()
      : String(dateString);
    
    // 尝试解析日期
    try {
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return dateStr.substring(0, 10);
      
      // 转换为本地日期格式
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (e) {
      // 如果日期解析出错，返回原始字符串的前10个字符
      return dateStr.substring(0, 10);
    }
  };
  
  // 首先确保记录数组存在
  const records = disease.records || [];
  
  // 找出记录中最早和最晚的日期
  let firstRecordDate: Date | null = null;
  let lastRecordDate: Date | null = null;
  
  // 尝试找出最早和最晚的记录日期
  if (records.length > 0) {
    records.forEach(record => {
      // 使用recordDate或created_at作为日期
      const dateStr = record.recordDate || record.created_at;
      if (!dateStr) return;
      
      try {
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) return;
        
        if (!firstRecordDate || date < firstRecordDate) {
          firstRecordDate = date;
        }
        
        if (!lastRecordDate || date > lastRecordDate) {
          lastRecordDate = date;
        }
      } catch (e) {
        // 忽略无法解析的日期
      }
    });
  }
  
  // 当没有任何记录或日期无效时，使用疾病的诊断日期
  if (!firstRecordDate && disease.diagnosisDate) {
    try {
      // 处理诊断日期，可能是字符串或Date对象
      const diagnosisDateStr = typeof disease.diagnosisDate === 'object' && disease.diagnosisDate instanceof Date
        ? disease.diagnosisDate.toISOString()
        : String(disease.diagnosisDate);
        
      const diagnosisDate = new Date(diagnosisDateStr);
      
      if (!isNaN(diagnosisDate.getTime())) {
        firstRecordDate = diagnosisDate;
        lastRecordDate = lastRecordDate || firstRecordDate;
      }
    } catch (e) {
      // 忽略无法解析的日期
    }
  }
  
  // 适当的Paper组件（根据选中状态）
  const PaperComponent = isSelected ? HighlightedPaper : NormalPaper;
  
  // 条目点击处理函数
  const handleClick = () => {
    // 即使已选中，也允许点击
    console.log('点击选择病理:', disease.id, disease.name, '当前选中状态:', isSelected);
    onClick();
  };
  
  // 根据私密状态确定图标
  const renderPrivacyBadge = () => {
    // 检查isPrivate属性，可以是布尔值或数字1
    const isPrivate = disease.isPrivate === true || disease.isPrivate === 1;
    
    if (isPrivate) {
      return (
        <Tooltip title="该病理为私密记录">
          <Badge
            color="error"
            variant="dot"
            sx={{
              '& .MuiBadge-badge': {
                top: 3,
                right: 3,
              }
            }}
          >
            <PinDropIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
          </Badge>
        </Tooltip>
      );
    }
    
    return null;
  };
  
  return (
    <Box sx={{ 
      mb: 1, 
      opacity: isMounted ? 1 : 0, 
      transform: isMounted ? 'translateY(0)' : 'translateY(10px)',
      transition: 'opacity 0.3s ease, transform 0.3s ease',
    }}>
      <PaperComponent 
        elevation={isSelected ? 2 : 1} 
        onClick={handleClick}
        sx={{
          position: 'relative',
          overflow: 'hidden',
          '&::after': isSelected ? {
            content: '""',
            position: 'absolute',
            top: 0,
            right: 0,
            width: '5px',
            height: '100%',
            backgroundColor: 'primary.main',
          } : {}
        }}
      >
        <Box 
          sx={{ 
            display: 'flex', 
            justifyContent: 'space-between',
            alignItems: 'flex-start'
          }}
        >
          <Box>
            <Typography 
              variant="subtitle2" 
              color={isSelected ? 'primary' : 'text.primary'}
              fontWeight={isSelected ? 'bold' : 'normal'}
              sx={{ fontSize: '0.825rem' }}
            >
              {disease.name}
            </Typography>
            
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
              {disease.diagnosisDate && (
                <Chip 
                  label={`诊断: ${formatDate(disease.diagnosisDate)}`} 
                  size="small" 
                  sx={{ height: 20, fontSize: '0.7rem' }}
                  variant={isSelected ? "filled" : "outlined"}
                  color={isSelected ? "primary" : "default"}
                />
              )}
              {records.length > 0 && (
                <Chip 
                  label={`${records.length}条记录`} 
                  size="small"
                  sx={{ height: 20, fontSize: '0.7rem' }}
                  variant={isSelected ? "filled" : "outlined"}
                  color={isSelected ? "primary" : "default"}
                />
              )}
            </Box>
          </Box>
          
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            {renderPrivacyBadge()}
          </Box>
        </Box>
        
        {records.length > 0 && (
          <Box sx={{ mt: 1, mb: -1 }}>
            <Timeline position="right" sx={{ p: 0, m: 0 }}>
              <TimelineItem sx={{ minHeight: 'auto', p: 0, m: 0 }}>
                <TimelineSeparator>
                  <TimelineDot 
                    color={isSelected ? "primary" : "grey"} 
                    variant={isSelected ? "filled" : "outlined"}
                    sx={{ width: 8, height: 8, my: 0 }}
                  />
                  <TimelineConnector sx={{ 
                    height: 20,
                    bgcolor: isSelected ? 'primary.main' : undefined,
                  }} />
                </TimelineSeparator>
                <TimelineContent sx={{ py: 0, px: 1 }}>
                  <Typography 
                    variant="caption" 
                    color={isSelected ? "primary.main" : "text.secondary"} 
                    sx={{ fontSize: '0.7rem' }}
                  >
                    {firstRecordDate ? formatDate(firstRecordDate) : '-'}
                  </Typography>
                </TimelineContent>
              </TimelineItem>
              
              <TimelineItem sx={{ minHeight: 'auto', p: 0, m: 0 }}>
                <TimelineSeparator>
                  <TimelineDot 
                    color={isSelected ? "primary" : "grey"} 
                    variant={isSelected ? "filled" : "outlined"}
                    sx={{ width: 8, height: 8, my: 0 }}
                  />
                </TimelineSeparator>
                <TimelineContent sx={{ py: 0, px: 1 }}>
                  <Typography 
                    variant="caption" 
                    color={isSelected ? "primary.main" : "text.secondary"} 
                    sx={{ fontSize: '0.7rem' }}
                  >
                    {lastRecordDate ? formatDate(lastRecordDate) : '-'}
                  </Typography>
                </TimelineContent>
              </TimelineItem>
            </Timeline>
          </Box>
        )}
      </PaperComponent>
    </Box>
  );
};

export default DiseaseTimeline;