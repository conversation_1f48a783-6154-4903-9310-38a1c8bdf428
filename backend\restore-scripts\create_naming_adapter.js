/**
 * 命名规则适配器脚本
 * 用于处理驼峰命名和蛇形命名之间的转换
 */
const fs = require('fs');
const path = require('path');

console.log('===== 创建命名规则适配器 =====');

// 创建命名规则适配器
function createNamingAdapter() {
  console.log('正在创建命名规则适配器...');
  
  const adapterContent = `/**
 * 命名规则适配器
 * 用于处理驼峰命名和蛇形命名之间的转换
 */

/**
 * 将对象中的键从驼峰命名转换为蛇形命名
 * 例如: firstName -> first_name
 * @param {Object} obj 原始对象
 * @returns {Object} 转换后的对象
 */
function toSnakeCase(obj) {
  if (!obj || typeof obj !== 'object') return obj;
  
  if (Array.isArray(obj)) {
    return obj.map(toSnakeCase);
  }
  
  const result = {};
  
  Object.keys(obj).forEach(key => {
    // 转换键名
    const snakeKey = key.replace(/([A-Z])/g, '_$1').toLowerCase();
    
    // 递归转换值
    if (obj[key] !== null && typeof obj[key] === 'object') {
      result[snakeKey] = toSnakeCase(obj[key]);
    } else {
      result[snakeKey] = obj[key];
    }
  });
  
  return result;
}

/**
 * 将对象中的键从蛇形命名转换为驼峰命名
 * 例如: first_name -> firstName
 * @param {Object} obj 原始对象
 * @returns {Object} 转换后的对象
 */
function toCamelCase(obj) {
  if (!obj || typeof obj !== 'object') return obj;
  
  if (Array.isArray(obj)) {
    return obj.map(toCamelCase);
  }
  
  const result = {};
  
  Object.keys(obj).forEach(key => {
    // 转换键名
    const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
    
    // 递归转换值
    if (obj[key] !== null && typeof obj[key] === 'object') {
      result[camelKey] = toCamelCase(obj[key]);
    } else {
      result[camelKey] = obj[key];
    }
  });
  
  return result;
}

/**
 * 适配AI报告对象，确保前后端兼容
 * @param {Object} report AI报告对象
 * @param {Boolean} toCamel 是否转换为驼峰命名
 * @returns {Object} 适配后的对象
 */
function adaptAIReport(report, toCamel = true) {
  if (!report) return null;
  
  // 首先进行深拷贝
  const reportCopy = JSON.parse(JSON.stringify(report));
  
  // 转换命名规则
  const adaptedReport = toCamel ? toCamelCase(reportCopy) : toSnakeCase(reportCopy);
  
  // 补充可能缺失的字段
  if (toCamel) {
    if (!adaptedReport.llmConfigs) adaptedReport.llmConfigs = {};
    if (!adaptedReport.visibleFields) adaptedReport.visibleFields = [];
    if (!adaptedReport.configId) adaptedReport.configId = null;
  } else {
    if (!adaptedReport.llm_configs) adaptedReport.llm_configs = {};
    if (!adaptedReport.visible_fields) adaptedReport.visible_fields = [];
    if (!adaptedReport.config_id) adaptedReport.config_id = null;
  }
  
  return adaptedReport;
}

module.exports = {
  toSnakeCase,
  toCamelCase,
  adaptAIReport
};`;

  const filePath = path.join(__dirname, '..', 'utils', 'namingAdapter.js');
  
  // 确保utils目录存在
  const utilsDir = path.join(__dirname, '..', 'utils');
  if (!fs.existsSync(utilsDir)) {
    fs.mkdirSync(utilsDir, { recursive: true });
  }
  
  fs.writeFileSync(filePath, adapterContent, 'utf8');
  console.log(`已创建命名规则适配器: ${filePath}`);
}

// 添加命名适配器到控制器
function addAdapterToController() {
  console.log('正在更新控制器以使用命名适配器...');
  
  const controllerPath = path.join(__dirname, '..', 'controllers', 'aiReport', 'aiReportController.js');
  
  if (!fs.existsSync(controllerPath)) {
    console.warn('警告: 未找到AI报告控制器，请手动添加命名适配器');
    return;
  }
  
  let controllerContent = fs.readFileSync(controllerPath, 'utf8');
  
  // 检查是否已经导入了适配器
  if (controllerContent.includes('namingAdapter')) {
    console.log('控制器已包含命名适配器引用，无需更新');
    return;
  }
  
  // 添加适配器导入
  const requirePattern = /const\s+(\w+|\{\s*\w+\s*\})\s+=\s+require\(['"]/;
  const firstRequireIndex = controllerContent.match(requirePattern).index;
  const insertPoint = controllerContent.indexOf('\n', firstRequireIndex) + 1;
  
  controllerContent = controllerContent.slice(0, insertPoint) +
    'const { toCamelCase, toSnakeCase, adaptAIReport } = require(\'../../utils/namingAdapter\');\n' +
    controllerContent.slice(insertPoint);
  
  // 修改响应转换部分 - 找到返回报告的地方
  const getAIReportPattern = /exports\.getAIReport\s*=\s*async/;
  if (getAIReportPattern.test(controllerContent)) {
    const formattedReportPattern = /const\s+formattedReport\s*=/;
    const formattedReportIndex = controllerContent.indexOf('const formattedReport =');
    
    if (formattedReportIndex !== -1) {
      // 找到格式化报告的代码块
      const startBlock = formattedReportIndex;
      const endBlock = controllerContent.indexOf('res.json(', startBlock);
      
      if (endBlock !== -1) {
        // 替换整个格式化块
        const newFormattingCode = `
    // 使用命名适配器转换报告对象
    const formattedReport = adaptAIReport(report);
    
    // 确保关联的记录也被正确格式化
    if (formattedReport.record) {
      formattedReport.record = toCamelCase(formattedReport.record);
    }
`;
        
        controllerContent = controllerContent.slice(0, startBlock) +
          newFormattingCode +
          controllerContent.slice(endBlock);
      }
    }
  }
  
  // 修改获取报告列表的部分
  const getAIReportsPattern = /exports\.getAIReports\s*=\s*async/;
  if (getAIReportsPattern.test(controllerContent)) {
    const formattedReportsIndex = controllerContent.indexOf('const formattedReports =');
    
    if (formattedReportsIndex !== -1) {
      // 找到格式化报告列表的代码块
      const startBlock = formattedReportsIndex;
      const endBlock = controllerContent.indexOf('res.json(', startBlock);
      
      if (endBlock !== -1) {
        // 替换整个格式化块
        const newFormattingCode = `
    // 使用命名适配器批量转换报告对象
    const formattedReports = reports.map(report => adaptAIReport(report));
`;
        
        controllerContent = controllerContent.slice(0, startBlock) +
          newFormattingCode +
          controllerContent.slice(endBlock);
      }
    }
  }
  
  // 修改创建报告的部分
  const createAIReportPattern = /exports\.createAIReport\s*=\s*async/;
  if (createAIReportPattern.test(controllerContent)) {
    const reqBodyIndex = controllerContent.indexOf('const { diseaseId, patientId');
    
    if (reqBodyIndex !== -1) {
      // 在参数解析后添加命名转换
      const insertPoint = controllerContent.indexOf('\n', reqBodyIndex) + 1;
      
      const snakeCaseCode = `
    // 将驼峰命名参数转换为蛇形命名，兼容旧版API
    const snakeCaseParams = toSnakeCase({
      diseaseId,
      patientId,
      targetRegion,
      userId: req.user.id
    });
`;
      
      controllerContent = controllerContent.slice(0, insertPoint) +
        snakeCaseCode +
        controllerContent.slice(insertPoint);
      
      // 修改调用generateAIReport的参数
      const generateCallIndex = controllerContent.indexOf('generateAIReport(');
      if (generateCallIndex !== -1) {
        const callEndIndex = controllerContent.indexOf(')', generateCallIndex);
        
        if (callEndIndex !== -1) {
          const originalCall = controllerContent.slice(generateCallIndex, callEndIndex + 1);
          const newCall = 'generateAIReport(snakeCaseParams.disease_id, snakeCaseParams.patient_id, snakeCaseParams.user_id, snakeCaseParams.target_region)';
          
          controllerContent = controllerContent.slice(0, generateCallIndex) +
            newCall +
            controllerContent.slice(callEndIndex + 1);
        }
      }
    }
  }
  
  fs.writeFileSync(controllerPath, controllerContent, 'utf8');
  console.log('控制器已更新以使用命名适配器');
}

// 创建前端适配层
function createFrontendAdapter() {
  console.log('正在创建前端适配层...');
  
  const frontendDir = path.join(__dirname, '..', '..', 'frontend', 'src', 'utils');
  const adapterPath = path.join(frontendDir, 'apiAdapter.js');
  
  if (!fs.existsSync(frontendDir)) {
    console.warn('警告: 未找到前端utils目录，请手动添加前端适配器');
    return;
  }
  
  const adapterContent = `/**
 * API适配器
 * 用于处理前后端接口格式差异
 */

/**
 * 将对象中的键从蛇形命名转换为驼峰命名
 * 例如: first_name -> firstName
 * @param {Object} obj 原始对象
 * @returns {Object} 转换后的对象
 */
export function toCamelCase(obj) {
  if (!obj || typeof obj !== 'object') return obj;
  
  if (Array.isArray(obj)) {
    return obj.map(toCamelCase);
  }
  
  const result = {};
  
  Object.keys(obj).forEach(key => {
    // 转换键名
    const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
    
    // 递归转换值
    if (obj[key] !== null && typeof obj[key] === 'object') {
      result[camelKey] = toCamelCase(obj[key]);
    } else {
      result[camelKey] = obj[key];
    }
  });
  
  return result;
}

/**
 * 将对象中的键从驼峰命名转换为蛇形命名
 * 例如: firstName -> first_name
 * @param {Object} obj 原始对象
 * @returns {Object} 转换后的对象
 */
export function toSnakeCase(obj) {
  if (!obj || typeof obj !== 'object') return obj;
  
  if (Array.isArray(obj)) {
    return obj.map(toSnakeCase);
  }
  
  const result = {};
  
  Object.keys(obj).forEach(key => {
    // 转换键名
    const snakeKey = key.replace(/([A-Z])/g, '_$1').toLowerCase();
    
    // 递归转换值
    if (obj[key] !== null && typeof obj[key] === 'object') {
      result[snakeKey] = toSnakeCase(obj[key]);
    } else {
      result[snakeKey] = obj[key];
    }
  });
  
  return result;
}

/**
 * 适配AI报告，确保前后端兼容
 * @param {Object} report AI报告对象
 * @returns {Object} 适配后的对象
 */
export function adaptAIReport(report) {
  if (!report) return null;
  
  // 转换命名规则
  const adaptedReport = toCamelCase(report);
  
  // 补充可能缺失的字段
  if (!adaptedReport.llmConfigs) adaptedReport.llmConfigs = {};
  if (!adaptedReport.visibleFields) adaptedReport.visibleFields = [];
  if (!adaptedReport.configId) adaptedReport.configId = null;
  
  return adaptedReport;
}

/**
 * 适配后端API响应，确保格式正确
 * @param {Object} response API响应对象
 * @returns {Object} 适配后的响应
 */
export function adaptApiResponse(response) {
  // 处理不同格式的后端响应
  if (response.aiReport) {
    return {
      ...response,
      aiReport: adaptAIReport(response.aiReport)
    };
  } else if (response.aiReports) {
    return {
      ...response,
      aiReports: response.aiReports.map(adaptAIReport)
    };
  } else if (Array.isArray(response)) {
    // 如果直接返回数组，假定是报告列表
    return response.map(adaptAIReport);
  } else if (response.id) {
    // 如果是单个对象且有ID，可能是单个报告
    return adaptAIReport(response);
  }
  
  // 其他情况，原样返回
  return response;
}`;

  fs.writeFileSync(adapterPath, adapterContent, 'utf8');
  console.log(`已创建前端适配器: ${adapterPath}`);
}

// 执行主要功能
(async function main() {
  try {
    createNamingAdapter();
    addAdapterToController();
    createFrontendAdapter();
    console.log('命名规则适配器创建完成！');
  } catch (error) {
    console.error('创建命名规则适配器失败:', error);
    process.exit(1);
  }
})(); 