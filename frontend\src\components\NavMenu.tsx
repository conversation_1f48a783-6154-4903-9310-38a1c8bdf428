import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
  Collapse
} from '@mui/material';
import PeopleIcon from '@mui/icons-material/People';
import HealthAndSafetyIcon from '@mui/icons-material/HealthAndSafety';
import SettingsIcon from '@mui/icons-material/Settings';
import HelpIcon from '@mui/icons-material/Help';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';
import ExpandLess from '@mui/icons-material/ExpandLess';
import ExpandMore from '@mui/icons-material/ExpandMore';
import DashboardIcon from '@mui/icons-material/Dashboard';
import NoteAltIcon from '@mui/icons-material/NoteAlt';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import SupportAgentIcon from '@mui/icons-material/SupportAgent';
import SecurityIcon from '@mui/icons-material/Security';
import ArticleIcon from '@mui/icons-material/Article';
import AssessmentIcon from '@mui/icons-material/Assessment';
import SupervisorAccountIcon from '@mui/icons-material/SupervisorAccount';
import SettingsSystemDaydreamIcon from '@mui/icons-material/SettingsSystemDaydream';
import VerifiedUserIcon from '@mui/icons-material/VerifiedUser';
import { useAuthStore } from '../store/authStore';
import { useTheme } from '@mui/material/styles';
import { useMediaQuery } from '@mui/material';

// 菜单项接口
interface MenuItem {
  text: string;
  icon: React.ReactNode;
  path?: string;
  roles: string[]; // 允许访问的角色列表
  subItems?: MenuItem[]; // 子菜单项
}

// 菜单项定义 (复制到此处)
const menuItems: MenuItem[] = [
  // ----- 普通用户级别 -----
  {
    text: '看板',
    icon: <DashboardIcon />,
    path: '/dashboard',
    roles: ['ADMIN', 'SERVICE', 'USER']
  },
  {
    text: '记录',
    icon: <NoteAltIcon />,
    path: '/records',
    roles: ['ADMIN', 'SERVICE', 'USER']
  },
  {
    text: '辅医',
    icon: <SmartToyIcon />,
    path: '/ai-assistant',
    roles: ['ADMIN', 'SERVICE', 'USER']
  },
  {
    text: '帮助',
    icon: <HelpIcon />,
    path: '/help',
    roles: ['ADMIN', 'SERVICE', 'USER']
  },
  {
    text: '设置',
    icon: <SettingsIcon />,
    roles: ['ADMIN', 'SERVICE', 'USER'],
    subItems: [
      {
        text: '患者管理',
        icon: <PeopleIcon />,
        path: '/patients',
        roles: ['ADMIN', 'SERVICE', 'USER']
      },
      {
        text: '疾病管理',
        icon: <HealthAndSafetyIcon />,
        path: '/diseases',
        roles: ['ADMIN', 'SERVICE', 'USER']
      },
      {
        text: '授权管理',
        icon: <SecurityIcon />,
        path: '/authorizations',
        roles: ['ADMIN', 'SERVICE', 'USER']
      }
    ]
  },
  
  // ----- 服务用户级别 -----
  {
    text: '服务管理',
    icon: <SupportAgentIcon />,
    roles: ['ADMIN', 'SERVICE'],
    subItems: [
      {
        text: '病理管理',
        icon: <HealthAndSafetyIcon />,
        path: '/service-diseases',
        roles: ['ADMIN', 'SERVICE']
      },
      {
        text: '记录管理',
        icon: <NoteAltIcon />,
        path: '/service-records',
        roles: ['ADMIN', 'SERVICE']
      },
      {
        text: '报告管理',
        icon: <AssessmentIcon />,
        path: '/service-ai-reports',
        roles: ['ADMIN', 'SERVICE']
      },
      {
        text: '授权管理',
        icon: <SecurityIcon />,
        path: '/service-authorizations',
        roles: ['ADMIN', 'SERVICE']
      }
    ]
  },
  
  // ----- 系统用户级别 -----
  {
    text: '系统管理',
    icon: <AdminPanelSettingsIcon />,
    roles: ['ADMIN'],
    subItems: [
      {
        text: '用户管理',
        icon: <SupervisorAccountIcon />,
        path: '/admin/users',
        roles: ['ADMIN']
      },
      {
        text: 'AI报告管理',
        icon: <AssessmentIcon />,
        path: '/admin/ai-reports',
        roles: ['ADMIN']
      },
      {
        text: '患者数据管理',
        icon: <PeopleIcon />,
        path: '/admin/patients',
        roles: ['ADMIN']
      },
      {
        text: '病理数据管理',
        icon: <HealthAndSafetyIcon />,
        path: '/admin/diseases',
        roles: ['ADMIN']
      },
      {
        text: '就诊记录管理',
        icon: <ArticleIcon />,
        path: '/admin/records',
        roles: ['ADMIN']
      },
      {
        text: '授权管理',
        icon: <SecurityIcon />,
        path: '/admin/authorizations',
        roles: ['ADMIN']
      },
      {
        text: '系统维护',
        icon: <SettingsSystemDaydreamIcon />,
        path: '/admin/maintenance',
        roles: ['ADMIN']
      },
      {
        text: '审计日志',
        icon: <VerifiedUserIcon />,
        path: '/admin/audit-logs',
        roles: ['ADMIN']
      }
    ]
  }
];

interface NavMenuProps {
  onNavigate?: () => void; // 可选回调，用于在移动设备上关闭抽屉
}

const NavMenu: React.FC<NavMenuProps> = ({ onNavigate }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [openSubMenu, setOpenSubMenu] = useState<string | null>(null);
  const [userRole, setUserRole] = useState<string>('USER'); // 默认为普通用户
  const { user } = useAuthStore();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // 当用户信息变化时更新角色
  useEffect(() => {
    if (user && user.role) {
      setUserRole(user.role);
    }
  }, [user]);

  // 初始化打开当前路径相关的子菜单
  useEffect(() => {
    const currentPath = location.pathname;
    // 查找当前路径对应的主菜单
    menuItems.forEach(item => {
      if (item.subItems) {
        const foundSubItem = item.subItems.find(subItem => 
          currentPath === subItem.path || currentPath.startsWith(subItem.path + '/')
        );
        if (foundSubItem) {
          setOpenSubMenu(item.text);
        }
      }
    });
  }, [location.pathname]);

  // 处理菜单项点击
  const handleMenuItemClick = (path?: string) => {
    if (path) {
      navigate(path);
      if (onNavigate) onNavigate(); // 关闭移动端的抽屉
    }
  };

  // 处理子菜单展开/收起
  const handleSubMenuToggle = (text: string) => {
    setOpenSubMenu(openSubMenu === text ? null : text);
  };

  // 过滤当前用户角色可见的菜单项
  const visibleMenuItems = menuItems.filter(item => 
    item.roles.includes(userRole)
  );

  return (
    <>
      <List sx={{ width: '100%', py: 0.5 }}>
        {visibleMenuItems.map((item) => (
          <React.Fragment key={item.text}>
            {item.subItems ? (
              // 有子菜单的项
              <>
                <ListItem disablePadding 
                  sx={{ 
                    mb: 0, 
                    display: 'block'
                  }}
                >
                  <ListItemButton 
                    onClick={() => handleSubMenuToggle(item.text)}
                    sx={{ 
                      minHeight: { xs: 40, md: 38 },
                      px: isMobile ? 2 : 1.5,
                      py: isMobile ? 0.5 : 0.25,
                      borderRadius: '4px',
                      mx: 1,
                      '&:hover': {
                        backgroundColor: 'rgba(255, 111, 97, 0.1)', // 辅色系悬停背景色
                      },
                      '&.Mui-selected': {
                        backgroundColor: 'secondary.main', // 使用辅色
                        color: 'white',
                        '&:hover': {
                          backgroundColor: 'secondary.dark', // 使用深色辅色
                        },
                        '& .MuiListItemIcon-root': {
                          color: 'white',
                        }
                      },
                    }}
                  >
                    <ListItemIcon
                      sx={{
                        minWidth: { xs: 40, md: 36 },
                        color: 'inherit'
                      }}
                    >
                      {item.icon}
                    </ListItemIcon>
                    <ListItemText 
                      primary={item.text} 
                      primaryTypographyProps={{
                        fontSize: { xs: 15, md: 14 },
                        fontWeight: openSubMenu === item.text ? 'bold' : 'regular',
                      }}
                    />
                    {openSubMenu === item.text ? <ExpandLess /> : <ExpandMore />}
                  </ListItemButton>
                </ListItem>
                <Collapse in={openSubMenu === item.text} timeout="auto" unmountOnExit>
                  <List component="div" disablePadding sx={{ pt: 0, pb: 0 }}>
                    {item.subItems
                      .filter(subItem => subItem.roles.includes(userRole))
                      .map((subItem) => (
                        <ListItemButton
                          key={subItem.text}
                          onClick={() => handleMenuItemClick(subItem.path)}
                          selected={location.pathname === subItem.path}
                          sx={{
                            pl: { xs: 4, md: 3.5 },
                            py: isMobile ? 0.75 : 0.5,
                            minHeight: { xs: 40, md: 36 },
                            borderRadius: '4px',
                            mx: 1,
                            my: 0,
                            '&:hover': {
                              backgroundColor: 'rgba(255, 111, 97, 0.1)', // 辅色系悬停背景色
                            },
                            '&.Mui-selected': {
                              backgroundColor: 'secondary.main', // 使用辅色
                              color: 'white',
                              '&:hover': {
                                backgroundColor: 'secondary.dark', // 使用深色辅色
                              },
                              '& .MuiListItemIcon-root': {
                                color: 'white',
                              }
                            }
                          }}
                        >
                          <ListItemIcon
                            sx={{
                              minWidth: { xs: 40, md: 36 },
                              color: 'inherit'
                            }}
                          >
                            {subItem.icon}
                          </ListItemIcon>
                          <ListItemText 
                            primary={subItem.text} 
                            primaryTypographyProps={{
                              fontSize: { xs: 14, md: 13 }
                            }}
                          />
                        </ListItemButton>
                      ))}
                  </List>
                </Collapse>
              </>
            ) : (
              // 没有子菜单的项
              <ListItem disablePadding 
                sx={{ 
                  mb: 0, 
                  display: 'block'
                }}
              >
                <ListItemButton 
                  onClick={() => handleMenuItemClick(item.path)}
                  selected={location.pathname === item.path}
                  sx={{ 
                    minHeight: { xs: 40, md: 38 },
                    px: isMobile ? 2 : 1.5,
                    py: isMobile ? 0.5 : 0.25,
                    borderRadius: '4px',
                    mx: 1,
                    '&:hover': {
                      backgroundColor: 'rgba(255, 111, 97, 0.1)', // 辅色系悬停背景色
                    },
                    '&.Mui-selected': {
                      backgroundColor: 'secondary.main', // 使用辅色
                      color: 'white',
                      '&:hover': {
                        backgroundColor: 'secondary.dark', // 使用深色辅色
                      },
                      '& .MuiListItemIcon-root': {
                        color: 'white',
                      }
                    },
                  }}
                >
                  <ListItemIcon
                    sx={{
                      minWidth: { xs: 40, md: 36 },
                      color: 'inherit'
                    }}
                  >
                    {item.icon}
                  </ListItemIcon>
                  <ListItemText 
                    primary={item.text} 
                    primaryTypographyProps={{
                      fontSize: { xs: 15, md: 14 }
                    }}
                  />
                </ListItemButton>
              </ListItem>
            )}
            {/* 主要类别之间添加分隔线 - 在服务管理前和系统管理前添加 */}
            {(item.text === '设置' || item.text === '服务管理') && (
              <Divider sx={{ my: 1 }} />
            )}
          </React.Fragment>
        ))}
      </List>
    </>
  );
};

export default NavMenu; 