import React from 'react';
import { 
  Box, 
  Paper, 
  Typography, 
  Chip,
  alpha,
  useTheme,
  Divider,
  styled
} from '@mui/material';
import { 
  Event as EventIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { DiseaseStage } from './DiseaseCard';
import { 
  StageNodeEnum, 
  StageNodeNames, 
  StagePhaseEnum, 
  StagePhaseNames,
  RecordTypeEnum,
  RecordTypeNames
} from '../../types/recordEnums';

// 创建一个全局样式组件用于隐藏原始标签数组
const HiddenArrayStyle = styled('style')({
  type: 'text/css',
});

// 阶段颜色映射
const stageColors = {
  [DiseaseStage.INITIAL]: '#E53935', // 红色 - 初诊
  [DiseaseStage.DIAGNOSIS]: '#8E24AA', // 紫色 - 确诊
  [DiseaseStage.TREATMENT]: '#FB8C00', // 橙色 - 治疗
  [DiseaseStage.FOLLOW_UP]: '#FDD835', // 黄色 - 随访
  [DiseaseStage.PROGNOSIS]: '#43A047', // 绿色 - 预后
  [DiseaseStage.CLOSED]: '#757575', // 灰色 - 封档
  'default': '#757575' // 灰色
};

// 记录类型颜色映射
const typeColors: Record<string, string> = {
  [RecordTypeEnum.SYMPTOM]: '#ECC94B', // 黄色
  [RecordTypeEnum.DIAGNOSIS]: '#9F7AEA', // 紫色
  [RecordTypeEnum.AUX_DIAGNOSIS]: '#9F7AEA', // 紫色
  [RecordTypeEnum.EXAMINATION]: '#4299E1', // 蓝色
  [RecordTypeEnum.LAB_TEST]: '#38B2AC', // 青色
  [RecordTypeEnum.TREATMENT]: '#F56565', // 红色
  [RecordTypeEnum.MEDICATION]: '#ED8936', // 橙色
  [RecordTypeEnum.SURGERY]: '#E53E3E', // 深红色
  [RecordTypeEnum.PHYSICAL_THERAPY]: '#DD6B20', // 橙红色
  [RecordTypeEnum.FOLLOW_UP]: '#48BB78', // 绿色
  [RecordTypeEnum.REVISIT]: '#38A169', // 深绿色
  [RecordTypeEnum.SELF_DESCRIPTION]: '#4A5568', // 深灰色
  [RecordTypeEnum.HOSPITALIZATION]: '#F687B3', // 粉色
  [RecordTypeEnum.MONITORING]: '#63B3ED', // 浅蓝色
  [RecordTypeEnum.DISCHARGE]: '#FC8181', // 浅红色
  [RecordTypeEnum.APPOINTMENT]: '#B794F4', // 浅紫色
  [RecordTypeEnum.REPORT]: '#4FD1C5', // 青绿色
  [RecordTypeEnum.PROGNOSIS]: '#68D391', // 浅绿色
  [RecordTypeEnum.NURSING]: '#F6AD55', // 橙黄色
  [RecordTypeEnum.REFERRAL]: '#90CDF4', // 天蓝色
  [RecordTypeEnum.PSYCHOLOGY]: '#D6BCFA', // 淡紫色
  [RecordTypeEnum.REHABILITATION]: '#81E6D9', // 碧绿色
  [RecordTypeEnum.ASSESSMENT]: '#9AE6B4', // 薄荷绿
  [RecordTypeEnum.OTHER]: '#A0AEC0' // 灰色
};

interface RecordCardProps {
  id: string;
  title: string;
  content?: string;
  recordDate: string;
  stageNode?: string;
  stagePhase?: string;
  stage_phase?: string;
  typeTagsJson?: string | string[]; // 添加标签数组字段
  recordType?: string; // 添加记录类型字段
  severity?: string; // 添加严重程度字段
  onClick?: () => void;
}

/**
 * 记录卡片组件
 * 显示记录标题、内容摘要、记录日期和所属病理阶段
 */
const RecordCard: React.FC<RecordCardProps> = ({
  id,
  title,
  content,
  recordDate,
  stageNode,
  stagePhase,
  stage_phase,
  typeTagsJson,
  recordType,
  severity,
  onClick
}) => {
  const theme = useTheme();
  
  // 优先使用stagePhase，如果没有则使用stage_phase
  const actualStagePhase = stagePhase || stage_phase;
  
  // 将阶段字符串映射到枚举
  const mapStageNode = (stage?: string): DiseaseStage => {
    if (!stage) return DiseaseStage.INITIAL;
    
    const lowerStage = stage.toLowerCase();
    
    if (lowerStage.includes('初诊') || lowerStage.includes('initial')) {
      return DiseaseStage.INITIAL;
    } else if (lowerStage.includes('确诊') || lowerStage.includes('diagnosis')) {
      return DiseaseStage.DIAGNOSIS;
    } else if (lowerStage.includes('治疗') || lowerStage.includes('treatment')) {
      return DiseaseStage.TREATMENT;
    } else if (lowerStage.includes('随访') || lowerStage.includes('follow')) {
      return DiseaseStage.FOLLOW_UP;
    } else if (lowerStage.includes('预后') || lowerStage.includes('prognosis')) {
      return DiseaseStage.PROGNOSIS;
    } else if (lowerStage.includes('封档') || lowerStage.includes('closed') || lowerStage.includes('close')) {
      return DiseaseStage.CLOSED;
    }
    
    return DiseaseStage.INITIAL;
  };
  
  // 获取阶段颜色
  const getStageColor = (stage?: string): string => {
    const mappedStage = mapStageNode(stage);
    return stageColors[mappedStage] || stageColors.default;
  };
  
  // 格式化日期
  const formatRecordDate = (date: string): string => {
    try {
      return format(new Date(date), 'yyyy-MM-dd HH:mm', { locale: zhCN });
    } catch (error) {
      return date;
    }
  };
  
  // 获取病程节点的中文显示名称
  const getStageNodeDisplayName = (stage?: string): string => {
    if (!stage) return '初诊';
    
    // 尝试从StageNodeEnum中匹配
    for (const key in StageNodeEnum) {
      if (stage.toUpperCase() === key || stage === StageNodeEnum[key as keyof typeof StageNodeEnum]) {
        return StageNodeNames[StageNodeEnum[key as keyof typeof StageNodeEnum]];
      }
    }
    
    // 如果找不到匹配，则使用旧的映射方式
    const mappedStage = mapStageNode(stage);
    return mappedStage;
  };
  
  // 获取病程阶段的中文显示名称
  const getStagePhaseDisplayName = (phase?: string): string => {
    if (!phase) return '';
    
    // 尝试从StagePhaseEnum中匹配
    for (const key in StagePhaseEnum) {
      if (phase.toUpperCase() === key || phase === StagePhaseEnum[key as keyof typeof StagePhaseEnum]) {
        return StagePhaseNames[StagePhaseEnum[key as keyof typeof StagePhaseEnum]];
      }
    }
    
    // 如果找不到匹配，则直接返回原值
    return phase;
  };
  
  // 截断内容，限制为100个字符
  const truncateContent = (text?: string, maxLength: number = 100): string => {
    if (!text) return '';
    
    if (text.length <= maxLength) {
      return text;
    }
    
    return text.substring(0, maxLength) + '...';
  };
  
  // 获取记录类型名称
  const getRecordTypeName = (type?: string | any): string => {
    if (!type) return '其他';
    
    // 检查是否是枚举值
    const enumValue = Object.values(RecordTypeEnum).find(val => val === type);
    if (enumValue) {
      return RecordTypeNames[enumValue as RecordTypeEnum] || '其他';
    }
    
    // 确保type是字符串类型才调用toUpperCase
    if (typeof type === 'string') {
      // 尝试转换为枚举后查询
      if (RecordTypeEnum[type.toUpperCase() as keyof typeof RecordTypeEnum]) {
        return RecordTypeNames[RecordTypeEnum[type.toUpperCase() as keyof typeof RecordTypeEnum]] || '其他';
      }
    }
    
    return '其他';
  };
  
  // 获取记录类型颜色
  const getRecordTypeColor = (type?: string | any): string => {
    if (!type) return typeColors[RecordTypeEnum.OTHER];
    
    // 直接查找颜色
    if (typeColors[type]) {
      return typeColors[type];
    }
    
    // 确保type是字符串类型才调用toUpperCase
    if (typeof type === 'string') {
      // 尝试转换为枚举后查找
      const upperType = type.toUpperCase();
      if (RecordTypeEnum[upperType as keyof typeof RecordTypeEnum]) {
        const enumValue = RecordTypeEnum[upperType as keyof typeof RecordTypeEnum];
        return typeColors[enumValue] || typeColors[RecordTypeEnum.OTHER];
      }
    }
    
    return typeColors[RecordTypeEnum.OTHER];
  };
  
  // 解析标签数组
  const parseRecordTags = (): {label: string; color: string}[] => {
    const tags: {label: string; color: string}[] = [];
    
    // 如果有记录类型，添加为第一个标签
    if (recordType) {
      tags.push({
        label: getRecordTypeName(recordType),
        color: getRecordTypeColor(recordType)
      });
    }
    
    // 处理typeTagsJson
    if (typeTagsJson) {
      try {
        let typeTags: any[] = [];
        
        // 尝试解析JSON字符串
        if (typeof typeTagsJson === 'string') {
          // 检查是否是JSON数组格式的字符串
          if (typeTagsJson.startsWith('[') && typeTagsJson.endsWith(']')) {
            try {
              typeTags = JSON.parse(typeTagsJson);
            } catch (e) {
              console.error('解析JSON标签数组失败:', e);
              typeTags = [];
            }
          } else {
            // 单个标签的情况
            typeTags = [typeTagsJson];
          }
        } else if (Array.isArray(typeTagsJson)) {
          // 如果已经是数组，直接使用
          typeTags = typeTagsJson;
        }
        
        // 将标签数组中的每个标签转换为中文
        if (Array.isArray(typeTags)) {
          typeTags.forEach(tag => {
            // 跳过null或undefined
            if (tag == null) return;
            
            // 不重复添加已有标签
            if (recordType && tag === recordType) return;
            
            const tagName = getRecordTypeName(tag);
            const tagColor = getRecordTypeColor(tag);
            
            tags.push({
              label: tagName,
              color: tagColor
            });
          });
        }
      } catch (err) {
        console.error('解析typeTagsJson出错:', err);
      }
    }
    
    // 如果没有任何标签且没有记录类型，添加默认标签
    if (tags.length === 0 && !recordType) {
      tags.push({
        label: '记录',
        color: typeColors[RecordTypeEnum.OTHER]
      });
    }
    
    return tags;
  };
  
  const stageColor = getStageColor(stageNode);
  const stageNodeDisplayName = getStageNodeDisplayName(stageNode);
  const stagePhaseDisplayName = getStagePhaseDisplayName(actualStagePhase);
  const recordTags = parseRecordTags();
  
  // 修正标题，移除可能的标签数组显示
  const cleanTitle = typeof title === 'string' 
    ? title.replace(/\[["']?[A-Z_]+["']?(,["']?[A-Z_]+["']?)*\]/g, '').trim() 
    : title || '';
  
  return (
    <Paper
      elevation={0}
      sx={{
        mb: 0.125,
        borderRadius: 2,
        overflow: 'hidden',
        border: '1px solid',
        borderColor: theme.palette.divider,
        cursor: 'pointer',
        transition: 'all 0.2s ease',
        '&:hover': {
          borderColor: theme.palette.primary.light,
          boxShadow: 1
        },
        position: 'relative',
        display: 'flex',
        pl: 0, // 移除左侧内边距
        transform: 'translateZ(0)', // 添加硬件加速
        willChange: 'transform' // 提示浏览器该元素将发生变化
      }}
      onClick={onClick}
    >
      {/* 添加隐藏原始标签数组的样式 */}
      <HiddenArrayStyle>
        {`
          .record-card-${id} p:contains(/\\[["']?[A-Z_]+["']?(?:,["']?[A-Z_]+["']?)*\\]/),
          .record-card-${id} span:contains(/\\[["']?[A-Z_]+["']?(?:,["']?[A-Z_]+["']?)*\\]/) {
            display: none !important;
          }
        `}
      </HiddenArrayStyle>
      
      {/* 左侧彩色条根据阶段 */}
      <Box 
        sx={{ 
          width: 6,
          bgcolor: stageColor,
        }} 
      />
      
      <Box sx={{ flex: 1, p: 2 }} className={`record-card-${id}`}>
        {/* 标题区域 */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
            {cleanTitle}
          </Typography>
        </Box>
        
        {/* 标签显示区 */}
        {recordTags.length > 0 && (
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 1 }}>
            {recordTags.map((tag, index) => (
              <Chip
                key={index}
                size="small"
                label={tag.label}
                sx={{ 
                  height: 18, 
                  fontSize: '0.55rem',
                  backgroundColor: `${tag.color}20`,
                  color: tag.color,
                }}
              />
            ))}
          </Box>
        )}
        
        <Divider sx={{ my: 1 }} />
        
        {/* 内容区域 */}
        {content && (
          <Typography variant="body2" sx={{ color: 'text.secondary', mb: 1.5, fontSize: '0.75rem' }}>
            {truncateContent(content)}
          </Typography>
        )}
        
        {/* 底部信息区域 */}
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <EventIcon sx={{ fontSize: '0.8rem', color: 'text.secondary', mr: 0.5 }} />
            <Typography variant="caption" sx={{ color: 'text.secondary', fontSize: '0.65rem' }}>
              {formatRecordDate(recordDate)}
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', gap: 1, mt: { xs: stageNode && actualStagePhase ? 1 : 0, sm: 0 } }}>
            {stageNode && (
              <Chip 
                size="small" 
                label={`病程节点: ${stageNodeDisplayName}`}
                sx={{ 
                  height: 18, 
                  fontSize: '0.5rem',
                  backgroundColor: alpha(stageColor, 0.1),
                  color: stageColor,
                  border: `1px solid ${alpha(stageColor, 0.3)}`
                }} 
              />
            )}
            
            {actualStagePhase && (
              <Chip 
                size="small" 
                label={`病程阶段: ${stagePhaseDisplayName}`}
                sx={{ 
                  height: 18, 
                  fontSize: '0.5rem',
                  backgroundColor: alpha(theme.palette.info.light, 0.1),
                  color: theme.palette.info.main,
                  border: `1px solid ${alpha(theme.palette.info.light, 0.3)}`
                }} 
              />
            )}
            
            {!stageNode && !actualStagePhase && !recordTags.length && (
              <Chip 
                size="small" 
                label="常规记录"
                sx={{ 
                  height: 18, 
                  fontSize: '0.5rem',
                  backgroundColor: alpha(theme.palette.grey[500], 0.1),
                  color: theme.palette.grey[600],
                  border: `1px solid ${alpha(theme.palette.grey[400], 0.3)}`
                }} 
              />
            )}
          </Box>
        </Box>
      </Box>
    </Paper>
  );
};

export default RecordCard; 