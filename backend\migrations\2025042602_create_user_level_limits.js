const { v4: uuidv4 } = require('uuid');
exports.up = function (knex) {
  return knex.schema.createTable('user_level_limits', (table) => {
    table.string('id').primary().defaultTo(uuidv4());
    table.string('level_type').unique().notNullable();
    table.integer('max_patients').notNullable();
    table.integer('max_pathologies').notNullable();
    table.integer('max_attachment_size').notNullable();
    table.integer('max_total_storage').notNullable();
  });
};
exports.down = function (knex) {
  return knex.schema.dropTable('user_level_limits');
}; 