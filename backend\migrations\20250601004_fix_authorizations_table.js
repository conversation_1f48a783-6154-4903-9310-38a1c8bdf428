/**
 * 修复用户授权表结构
 */
exports.up = function(knex) {
  return knex.schema.hasTable('user_authorizations').then(function(exists) {
    if (exists) {
      return knex.schema.table('user_authorizations', function(table) {
        // 检查并添加缺失的列
        
        // 检查has_new_notification列
        return knex.schema.hasColumn('user_authorizations', 'has_new_notification').then(function(hasColumn) {
          if (!hasColumn) {
            console.log('添加has_new_notification列');
            return knex.schema.table('user_authorizations', function(table) {
              table.boolean('has_new_notification').notNullable().defaultTo(false);
            });
          }
          return Promise.resolve();
        })
        .then(() => {
          // 检查updated_at列
          return knex.schema.hasColumn('user_authorizations', 'updated_at').then(function(hasColumn) {
            if (!hasColumn) {
              console.log('添加updated_at列');
              return knex.schema.table('user_authorizations', function(table) {
                table.timestamp('updated_at', { useTz: true }).notNullable().defaultTo(knex.fn.now());
              });
            }
            return Promise.resolve();
          });
        })
        .then(() => {
          // 检查created_at列
          return knex.schema.hasColumn('user_authorizations', 'created_at').then(function(hasColumn) {
            if (!hasColumn) {
              console.log('添加created_at列');
              return knex.schema.table('user_authorizations', function(table) {
                table.timestamp('created_at', { useTz: true }).notNullable().defaultTo(knex.fn.now());
              });
            }
            return Promise.resolve();
          });
        });
      });
    }
  });
};

exports.down = function(knex) {
  // 回滚不删除这些列，因为它们是基本功能所需的
  return Promise.resolve();
}; 