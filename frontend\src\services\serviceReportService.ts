import apiClient from './apiClient';
import { API_PATHS } from '../config/apiPaths';
import { ApiResponse, ServiceReport } from '../types/serviceTypes';
import { logApiError } from '../utils/apiErrorMonitor';

/**
 * 获取服务用户创建的AI报告
 */
export const getServiceReports = async (): Promise<ApiResponse<ServiceReport[]>> => {
  try {
    const path = API_PATHS.SERVICE.REPORTS;
    console.log(`[serviceReportService] 获取服务报告，路径: ${path}`);
    
    const response = await apiClient.get(path);
    return response.data;
  } catch (error) {
    console.error('[serviceReportService] 获取服务报告失败:', error);
    logApiError(error);
    throw error;
  }
};

/**
 * 获取被授权用户的AI报告列表
 * @param authorizationId - 授权ID
 */
export const getAuthorizedUserReports = async (authorizationId: string): Promise<ApiResponse<ServiceReport[]>> => {
  try {
    const path = `${API_PATHS.SERVICE.REPORTS}/authorized/${authorizationId}/reports`;
    console.log(`[serviceReportService] 获取授权用户报告，路径: ${path}`);
    
    const response = await apiClient.get(path);
    return response.data;
  } catch (error) {
    console.error('[serviceReportService] 获取授权用户报告失败:', error);
    logApiError(error);
    throw error;
  }
};

// 报告创建数据接口
interface ServiceReportCreateData {
  authorizationId: string;
  patientId?: string;
  diseaseId: string;
  reportType?: string;
  promptOverrides?: Record<string, string>;
}

/**
 * 获取服务报告详情
 * @param id - 报告ID
 */
export const getServiceReportById = async (id: string): Promise<ApiResponse<ServiceReport>> => {
  try {
    const path = `${API_PATHS.SERVICE.REPORTS}/${id}`;
    console.log(`[serviceReportService] 获取服务报告详情，路径: ${path}`);
    
    const response = await apiClient.get(path);
    return response.data;
  } catch (error) {
    console.error('[serviceReportService] 获取服务报告详情失败:', error);
    logApiError(error);
    throw error;
  }
};

/**
 * 创建服务报告
 * @param data - 报告数据
 */
export const createServiceReport = async (data: Partial<ServiceReport>): Promise<ApiResponse<ServiceReport>> => {
  try {
    const path = API_PATHS.SERVICE.REPORTS;
    console.log(`[serviceReportService] 创建服务报告，路径: ${path}`);
    
    const response = await apiClient.post(path, data);
    return response.data;
  } catch (error) {
    console.error('[serviceReportService] 创建服务报告失败:', error);
    logApiError(error);
    throw error;
  }
};

/**
 * 生成AI报告
 * @param reportId - 报告ID
 */
export const generateAIReport = async (reportId: string): Promise<ApiResponse<ServiceReport>> => {
  try {
    const path = `${API_PATHS.SERVICE.REPORTS}/${reportId}/generate`;
    console.log(`[serviceReportService] 生成AI报告，路径: ${path}`);
    
    const response = await apiClient.post(path);
    return response.data;
  } catch (error) {
    console.error('[serviceReportService] 生成AI报告失败:', error);
    logApiError(error);
    throw error;
  }
};

/**
 * 删除服务报告
 * @param id - 报告ID
 */
export const deleteServiceReport = async (id: string): Promise<ApiResponse<void>> => {
  try {
    const path = `${API_PATHS.SERVICE.REPORTS}/${id}`;
    console.log(`[serviceReportService] 删除服务报告，路径: ${path}`);
    
    const response = await apiClient.delete(path);
    return response.data;
  } catch (error) {
    console.error('[serviceReportService] 删除服务报告失败:', error);
    logApiError(error);
    throw error;
  }
};

/**
 * 生成并下载扩展版PDF报告
 * @param serviceReportId - 服务报告ID
 */
export const generateExtendedPdf = async (serviceReportId: string): Promise<ApiResponse<{ pdfPath: string }>> => {
  try {
    const path = `${API_PATHS.SERVICE.REPORTS}/${serviceReportId}/extended-pdf`;
    console.log(`[serviceReportService] 生成扩展版PDF，路径: ${path}`);
    
    const response = await apiClient.post(path);
    return response.data;
  } catch (error) {
    console.error('[serviceReportService] 生成扩展版PDF失败:', error);
    logApiError(error);
    throw error;
  }
};

/**
 * 下载PDF报告
 * @param pdfPath - PDF路径
 */
export const downloadPdf = async (pdfPath: string): Promise<boolean> => {
  try {
    console.log(`[serviceReportService] 下载PDF报告，路径: ${pdfPath}`);
    
    const response = await apiClient.get(pdfPath, {
      responseType: 'blob'
    });
    
    // 创建Blob链接并触发下载
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    
    // 从路径提取文件名
    const filename = pdfPath.split('/').pop() || 'report.pdf';
    link.setAttribute('download', filename);
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    return true;
  } catch (error) {
    console.error('[serviceReportService] 下载PDF报告失败:', error);
    logApiError(error);
    throw error;
  }
};

/**
 * 根据授权ID获取服务报告
 * @param authorizationId - 授权ID
 */
export const getServiceReportsByAuth = async (authorizationId: string): Promise<ApiResponse<ServiceReport[]>> => {
  try {
    const path = `${API_PATHS.SERVICE.REPORTS}/by-auth/${authorizationId}`;
    console.log(`[serviceReportService] 根据授权ID获取服务报告，路径: ${path}`);
    
    const response = await apiClient.get(path);
    return response.data;
  } catch (error) {
    console.error('[serviceReportService] 根据授权ID获取服务报告失败:', error);
    logApiError(error);
    throw error;
  }
};

/**
 * 根据上下文获取服务报告
 * @param params - 上下文参数
 */
export const getServiceReportsByContext = async (params: {
  authorizationId: string;
  patientId: string;
  diseaseId: string;
}): Promise<ApiResponse<ServiceReport[]>> => {
  try {
    const path = `${API_PATHS.SERVICE.REPORTS}/context`;
    console.log(`[serviceReportService] 根据上下文获取服务报告，路径: ${path}`);
    
    const response = await apiClient.get(path, { params });
    return response.data;
  } catch (error) {
    console.error('[serviceReportService] 根据上下文获取服务报告失败:', error);
    logApiError(error);
    throw error;
  }
};

/**
 * 更新服务报告
 * @param id - 报告ID
 * @param data - 更新后的报告数据
 */
export const updateServiceReport = async (id: string, data: Partial<ServiceReport>): Promise<ApiResponse<ServiceReport>> => {
  try {
    const path = `${API_PATHS.SERVICE.REPORTS}/${id}`;
    console.log(`[serviceReportService] 更新服务报告，路径: ${path}`);
    
    const response = await apiClient.put(path, data);
    return response.data;
  } catch (error) {
    console.error('[serviceReportService] 更新服务报告失败:', error);
    logApiError(error);
    throw error;
  }
};

// 导出所有方法作为默认对象
export default {
  getServiceReports,
  getServiceReportsByAuth,
  getAuthorizedUserReports,
  getServiceReportById,
  createServiceReport,
  generateAIReport,
  deleteServiceReport,
  generateExtendedPdf,
  downloadPdf,
  getServiceReportsByContext,
  updateServiceReport
}; 