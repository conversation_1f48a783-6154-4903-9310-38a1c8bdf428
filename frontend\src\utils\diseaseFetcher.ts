import { getDisease } from "../services/diseaseService";
import { getRecords } from "../services/recordService";
import { getPatients } from "../services/patientService";

/**
 * 病理数据获取工具
 * 集中获取病理Tab页面所需的所有数据，减少重复请求
 */

/**
 * 简单的性能监控工具
 */
class PerformanceMonitor {
  private startTime: number;
  private name: string;

  constructor(name: string) {
    this.name = name;
    this.startTime = performance.now();
  }

  end(extraInfo: string = "") {
    const endTime = performance.now();
    const duration = endTime - this.startTime;
    console.log(
      `【性能监控】${this.name} 耗时: ${duration.toFixed(2)}ms ${extraInfo}`,
    );
    return duration;
  }

  checkpoint(checkpointName: string) {
    const currentTime = performance.now();
    const duration = currentTime - this.startTime;
    console.log(
      `【性能监控】${this.name} - ${checkpointName} 耗时: ${duration.toFixed(2)}ms`,
    );
    return duration;
  }
}

/**
 * 病程节点记录
 */
export interface StageNodeRecord {
  id: string;
  title: string;
  content: string;
  recordType?: string;
  stage_node?: string;
  stageNode?: string;
  createdAt: string;
  created_at?: string;
  [key: string]: any;
}

/**
 * AI报告数据
 */
export interface AIReportData {
  id: string;
  title: string;
  content: any; // 可以是字符串或结构化对象
  recordType?: string;
  createdAt?: string;
  created_at?: string;
  data?: any; // 添加data字段
  originalContent?: any; // 用于存储原始内容
  [key: string]: any;
}

/**
 * 病理Tab所需数据结构
 */
export interface DiseaseTabData {
  disease: any | null;
  patient: any | null;
  stageNodeRecords: StageNodeRecord[];
  aiReport: AIReportData | null;
  loading: boolean;
  error: string | null;
}

/**
 * 集中获取病理Tab所需数据
 * @param diseaseId 病理ID
 * @param patientId 患者ID
 * @returns 病理Tab所需的全部数据
 */
export const fetchDiseaseTabData = async (
  diseaseId: string | null | undefined,
  patientId: string | null | undefined,
): Promise<DiseaseTabData> => {
  // 启动性能监控
  const monitor = new PerformanceMonitor("fetchDiseaseTabData");

  // 初始化返回数据结构
  const result: DiseaseTabData = {
    disease: null,
    patient: null,
    stageNodeRecords: [],
    aiReport: null,
    loading: false,
    error: null,
  };

  // 如果没有病理ID，直接返回初始数据
  if (!diseaseId) {
    return result;
  }

  try {
    // 标记加载状态
    result.loading = true;

    // 并行获取所有数据以提高性能
    const parallelStart = performance.now();
    const [diseaseData, patientsData] = await Promise.all([
      // 1. 获取病理数据
      getDisease(diseaseId, { includeDeleted: true }),

      // 2. 获取患者数据（如果有患者ID）
      patientId ? getPatients() : Promise.resolve([]),
    ]);
    console.log(
      `【性能监控】基础数据获取耗时: ${(performance.now() - parallelStart).toFixed(2)}ms`,
    );

    // 设置病理数据
    result.disease = diseaseData;
    monitor.checkpoint("获取并设置病理数据");

    // 设置患者数据
    if (patientId && Array.isArray(patientsData)) {
      result.patient = patientsData.find((p) => p.id === patientId) || null;
    }
    monitor.checkpoint("获取并设置患者数据");

    // 3. 获取病程节点记录 - 简化查询，不使用where_clause
    const stageNodesStart = performance.now();
    try {
      const recordsResponse = await getRecords({
        diseaseId: diseaseId,
        limit: 20, // 增加查询数量，确保能获取足够的记录
      });

      console.log(
        `【性能监控】获取记录耗时: ${(performance.now() - stageNodesStart).toFixed(2)}ms`,
      );

      // 处理记录数据
      let recordsData: any[] = [];
      if (Array.isArray(recordsResponse)) {
        recordsData = recordsResponse;
      } else if (
        recordsResponse &&
        typeof recordsResponse === "object" &&
        recordsResponse.records
      ) {
        recordsData = recordsResponse.records;
      }

      // 在前端筛选病程节点记录
      result.stageNodeRecords = recordsData
        .filter((record) => {
          // 筛选有病程节点的记录，仅检查stage_node和stageNode
          return (
            record && 
            !record.deleted_at && 
            (record.stage_node || record.stageNode)
          );
        })
        .map((record) => ({
          ...record,
          // 标准化创建时间字段
          createdAt: record.createdAt || record.created_at,
        }));

      monitor.checkpoint("处理病程节点记录");
    } catch (err) {
      console.error("获取病程节点记录失败:", err);
      // 出错时设置空数组，允许其他部分继续执行
      result.stageNodeRecords = [];
    }

    // 4. 获取AI报告 - 简化查询，不使用复杂条件
    const aiReportStart = performance.now();
    try {
      // 先尝试获取AI_ANALYSIS类型的记录
      const aiReportsQuery = await getRecords({
        diseaseId: diseaseId,
        limit: 1,
        recordType: "AI_ANALYSIS",
        order_by: "created_at DESC",
      });

      console.log(
        `【性能监控】获取AI报告耗时: ${(performance.now() - aiReportStart).toFixed(2)}ms`,
      );

      let aiReportsData: any[] = [];
      if (Array.isArray(aiReportsQuery)) {
        aiReportsData = aiReportsQuery;
      } else if (
        aiReportsQuery &&
        typeof aiReportsQuery === "object" &&
        aiReportsQuery.records
      ) {
        aiReportsData = aiReportsQuery.records;
      }

      // 如果找到AI_ANALYSIS类型的记录，设置为当前报告
      if (aiReportsData.length > 0) {
        result.aiReport = aiReportsData[0];
        
        // 打印报告信息用于调试
        if (result.aiReport) {
          const contentType = typeof result.aiReport.content;
          const contentSize = contentType === 'string' 
            ? result.aiReport.content.length 
            : (contentType === 'object' ? Object.keys(result.aiReport.content).length : 'unknown');
          
          console.log(`【AI报告】找到AI_ANALYSIS记录: ID=${result.aiReport.id}, 内容类型=${contentType}, 内容大小=${contentSize}`);
          
          // 检查是否有data字段并包含结构化数据
          if (result.aiReport.data) {
            try {
              let dataContent = result.aiReport.data;
              
              // 如果data是字符串，尝试解析为JSON
              if (typeof dataContent === 'string') {
                dataContent = JSON.parse(dataContent);
                console.log('【AI报告】从data字段中成功解析JSON数据');
              }
              
              // 将解析后的data内容与原始aiReport结合
              if (typeof dataContent === 'object' && dataContent !== null) {
                console.log('【AI报告】data字段包含有效的结构化数据：', Object.keys(dataContent).join(', '));
                
                // 将原始内容保存
                result.aiReport.originalContent = result.aiReport.content;
                
                // 如果原始content不是对象，而data中有结构化数据
                if (typeof result.aiReport.content !== 'object') {
                  // 使用data字段创建结构化的content
                  result.aiReport.content = {
                    summary: result.aiReport.content || '',
                    hospitalRecommendations: dataContent.hospitalRecommendations || { 
                      targetRegion: '未指定',
                      hospitals: [] 
                    },
                    lifestyleAndMentalHealth: {
                      lifestyle: dataContent.lifestyle || { 
                        diet: [], 
                        exercise: [], 
                        habits: [] 
                      },
                      mentalHealth: dataContent.mentalHealth || { 
                        copingStrategies: [], 
                        resources: [] 
                      }
                    },
                    dashboardData: dataContent.dashboardData || {
                      status: '数据不足',
                      trend: '未知',
                      riskLevel: '未知'
                    },
                    riskWarnings: dataContent.riskWarnings || []
                  };
                  
                  // 如果有BMI建议，也添加到content
                  if (dataContent.bmiRecommendations) {
                    result.aiReport.content.bmiRecommendations = dataContent.bmiRecommendations;
                  }
                  
                  console.log('【AI报告】已从data字段创建结构化content');
                }
              }
            } catch (parseError) {
              console.error('【AI报告】解析data字段失败:', parseError);
            }
          } else {
            console.log(`【AI报告】发现异常：记录中没有data字段或为空`);
          }
        } else {
          console.log(`【AI报告】发现异常：aiReportsData[0]存在但result.aiReport为空`);
        }
      } else {
        // 如果没有找到 AI_ANALYSIS 类型的记录，尝试通过标题查找
        try {
          console.log('尝试通过标题搜索AI报告');
          const titleSearchStart = performance.now();
          const titleSearch = await getRecords({
            diseaseId: diseaseId,
            limit: 1,
            q: "辅医智能分析报告", // 使用搜索关键词
            order_by: "created_at DESC",
          });

          let titleSearchData: any[] = [];
          if (Array.isArray(titleSearch)) {
            titleSearchData = titleSearch;
          } else if (
            titleSearch &&
            typeof titleSearch === "object" &&
            titleSearch.records
          ) {
            titleSearchData = titleSearch.records;
          }

          if (titleSearchData.length > 0) {
            result.aiReport = titleSearchData[0];
            console.log(
              `【AI报告】通过标题找到AI报告: ID=${result.aiReport?.id}`,
            );

            // 检查是否有data字段并包含结构化数据
            if (result.aiReport && result.aiReport.data) {
              try {
                let dataContent = result.aiReport.data;
                
                // 如果data是字符串，尝试解析为JSON
                if (typeof dataContent === 'string') {
                  dataContent = JSON.parse(dataContent);
                  console.log('【AI报告】从data字段中成功解析JSON数据');
                }
                
                // 将解析后的data内容与原始aiReport结合
                if (typeof dataContent === 'object' && dataContent !== null) {
                  console.log('【AI报告】data字段包含有效的结构化数据：', Object.keys(dataContent).join(', '));
                  
                  // 将原始内容保存
                  result.aiReport.originalContent = result.aiReport.content;
                  
                  // 如果原始content不是对象，而data中有结构化数据
                  if (typeof result.aiReport.content !== 'object') {
                    // 使用data字段创建结构化的content
                    result.aiReport.content = {
                      summary: result.aiReport.content || '',
                      hospitalRecommendations: dataContent.hospitalRecommendations || { 
                        targetRegion: '未指定',
                        hospitals: [] 
                      },
                      lifestyleAndMentalHealth: {
                        lifestyle: dataContent.lifestyle || { 
                          diet: [], 
                          exercise: [], 
                          habits: [] 
                        },
                        mentalHealth: dataContent.mentalHealth || { 
                          copingStrategies: [], 
                          resources: [] 
                        }
                      },
                      dashboardData: dataContent.dashboardData || {
                        status: '数据不足',
                        trend: '未知',
                        riskLevel: '未知'
                      },
                      riskWarnings: dataContent.riskWarnings || []
                    };
                    
                    // 如果有BMI建议，也添加到content
                    if (dataContent.bmiRecommendations) {
                      result.aiReport.content.bmiRecommendations = dataContent.bmiRecommendations;
                    }
                    
                    console.log('【AI报告】已从data字段创建结构化content');
                  }
                }
              } catch (parseError) {
                console.error('【AI报告】解析data字段失败:', parseError);
              }
            }
          } else {
            console.log('通过标题搜索未找到AI报告');
          }
          console.log(
            `【性能监控】标题搜索耗时: ${(performance.now() - titleSearchStart).toFixed(2)}ms`,
          );
        } catch (searchErr) {
          // 忽略搜索错误，只记录日志不影响流程
          console.error("通过标题查找AI报告失败:", searchErr);
          console.log('标题搜索失败，继续处理其他数据');
        }
      }
    } catch (err) {
      console.error("获取AI报告失败:", err);
      // 出错时将aiReport保持为null
    }

    monitor.checkpoint("获取并设置AI报告");

    // 清除加载状态和错误
    result.loading = false;
    result.error = null;

    // 记录总耗时
    monitor.end(
      `数据总量: 病程节点记录=${result.stageNodeRecords.length}, 有AI报告=${!!result.aiReport}`,
    );
  } catch (err: any) {
    console.error("获取病理Tab数据失败:", err);
    result.error = err.message || "获取数据失败，请稍后再试";
    result.loading = false;
    monitor.end("获取失败");
  }

  return result;
};

/**
 * 从AI报告内容中提取结构化数据
 * @param content AI报告内容
 * @returns 结构化后的AI报告数据
 */
export const extractAIReportStructuredData = (content: any): any => {
  // 启动性能监控
  const monitor = new PerformanceMonitor("extractAIReportStructuredData");

  // 初始化默认返回数据
  const result = {
    hasStructuredData: false,
    trend: "",
    status: "",
    riskLevel: "",
    summary: "",
    medicalAdvice: [] as string[],
    lifestyleAdvice: [] as string[],
    exerciseAdvice: [] as string[],
    dietAdvice: [] as string[],
    psychologicalAdvice: [] as string[],
    hospitalRecommendations: [] as any[],
    isDemo: false,
  };

  try {
    if (!content) {
      console.log('【AI数据提取】没有提供内容，使用默认值');
      monitor.end("没有内容");
      result.isDemo = true;
      return result;
    }

    console.log(`【AI数据提取】开始处理AI报告内容，类型: ${typeof content}`);
    
    // 调试：输出内容的前200个字符
    if (typeof content === "string") {
      console.log(`【AI数据提取】内容预览(字符串): ${content.substring(0, 200)}...`);
      
      // 处理长文本内容 - 从纯文本中提取各种建议
      const fullText = content;
      console.log(`【AI数据提取】处理长文本内容，总长度: ${fullText.length} 字符`);
      
      // 从文本中提取各种段落
      // 尝试找出各种建议段落
      const dietSection = extractSectionByPattern(fullText, /(饮食建议|饮食指导|营养建议|饮食推荐|饮食调整|推荐饮食)[：:]([\s\S]+?)(?=\n\n|\n[^\n]|$)/i);
      if (dietSection) {
        result.dietAdvice = extractBulletPoints(dietSection);
        console.log(`【AI数据提取】从文本中提取饮食建议: ${result.dietAdvice.length}条`);
      }
      
      const lifestyleSection = extractSectionByPattern(fullText, /(生活方式|生活指导|日常生活|生活建议|习惯建议)[：:]([\s\S]+?)(?=\n\n|\n[^\n]|$)/i);
      if (lifestyleSection) {
        result.lifestyleAdvice = extractBulletPoints(lifestyleSection);
        console.log(`【AI数据提取】从文本中提取生活建议: ${result.lifestyleAdvice.length}条`);
      }
      
      const exerciseSection = extractSectionByPattern(fullText, /(运动建议|锻炼建议|体育活动|运动指导|体育锻炼)[：:]([\s\S]+?)(?=\n\n|\n[^\n]|$)/i);
      if (exerciseSection) {
        result.exerciseAdvice = extractBulletPoints(exerciseSection);
        console.log(`【AI数据提取】从文本中提取运动建议: ${result.exerciseAdvice.length}条`);
      }
      
      const psychologicalSection = extractSectionByPattern(fullText, /(心理建议|心理指导|心理健康|心理调适|情绪管理)[：:]([\s\S]+?)(?=\n\n|\n[^\n]|$)/i);
      if (psychologicalSection) {
        result.psychologicalAdvice = extractBulletPoints(psychologicalSection);
        console.log(`【AI数据提取】从文本中提取心理建议: ${result.psychologicalAdvice.length}条`);
      }
      
      const hospitalSection = extractSectionByPattern(fullText, /(医院推荐|推荐医院|建议就诊|医疗机构推荐|医院建议)[：:]([\s\S]+?)(?=\n\n|\n[^\n]|$)/i);
      if (hospitalSection) {
        result.hospitalRecommendations = extractHospitalData(hospitalSection);
        console.log(`【AI数据提取】从文本中提取医院推荐: ${result.hospitalRecommendations.length}条`);
      }
    } else if (typeof content === "object") {
      console.log(`【AI数据提取】内容预览(对象): ${JSON.stringify(content).substring(0, 200)}...`);
    }
    
    // 设置为已处理
    result.hasStructuredData = true;

    // 处理字符串内容
    let contentText: string = "";

    if (typeof content === "string") {
      contentText = content;
      console.log(`【AI数据提取】处理字符串内容，长度: ${contentText.length}`);
      
      // 尝试解析字符串为JSON
      try {
        const parsedContent = JSON.parse(contentText);
        console.log('【AI数据提取】成功将字符串解析为JSON对象');
        return extractAIReportStructuredData(parsedContent);
      } catch (parseError) {
        console.log('【AI数据提取】内容不是有效的JSON格式，按普通文本处理');
      }
      
      monitor.checkpoint("处理字符串内容");
    } else if (typeof content === "object") {
      // 处理对象格式的内容
      contentText = JSON.stringify(content);
      console.log(`【AI数据提取】处理对象内容，键数量: ${Object.keys(content).length}`);

      // 输出所有顶级键，帮助调试
      console.log(`【AI数据提取】内容顶级键: ${Object.keys(content).join(', ')}`);
      
      // 检查是否有recommendation或recommendations字段
      if (content.recommendation || content.recommendations) {
        console.log('【AI数据提取】发现recommendation/recommendations字段');
        const recommendations = content.recommendation || content.recommendations;
        
        if (recommendations?.lifestyle) {
          console.log('【AI数据提取】从recommendations.lifestyle中提取生活建议');
          result.lifestyleAdvice = extractArrayData(recommendations.lifestyle);
        }
        
        if (recommendations?.diet) {
          console.log('【AI数据提取】从recommendations.diet中提取饮食建议');
          result.dietAdvice = extractArrayData(recommendations.diet);
        }
        
        if (recommendations?.exercise) {
          console.log('【AI数据提取】从recommendations.exercise中提取运动建议');
          result.exerciseAdvice = extractArrayData(recommendations.exercise);
        }
        
        if (recommendations?.psychological || recommendations?.psychology) {
          console.log('【AI数据提取】从recommendations.psychological中提取心理建议');
          result.psychologicalAdvice = extractArrayData(recommendations.psychological || recommendations.psychology);
        }
        
        if (recommendations?.hospitals || recommendations?.hospital) {
          console.log('【AI数据提取】从recommendations.hospitals中提取医院推荐');
          result.hospitalRecommendations = extractHospitalData(recommendations.hospitals || recommendations.hospital);
        }
      }

      // 提取对象中的数据
      if (content.summary) {
        result.summary = content.summary;
        console.log(`【AI数据提取】找到摘要，长度: ${result.summary.length}`);
      }
      monitor.checkpoint("提取摘要信息");

      // 提取各类建议 - 增加对嵌套结构的支持
      
      // 饮食建议提取
      if (content.lifestyleAndMentalHealth?.lifestyle?.diet) {
        // 新的嵌套结构
        result.dietAdvice = extractArrayData(content.lifestyleAndMentalHealth.lifestyle.diet);
        console.log('【AI数据提取】从嵌套结构提取饮食建议:', result.dietAdvice.length);
      } else if (content.diet || content.dietAdvice) {
        // 原有结构
        result.dietAdvice = extractArrayData(content.diet || content.dietAdvice);
        console.log('【AI数据提取】从原有结构提取饮食建议:', result.dietAdvice.length);
      }

      // 运动建议提取
      if (content.lifestyleAndMentalHealth?.lifestyle?.exercise) {
        // 新的嵌套结构
        result.exerciseAdvice = extractArrayData(content.lifestyleAndMentalHealth.lifestyle.exercise);
        console.log('【AI数据提取】从嵌套结构提取运动建议:', result.exerciseAdvice.length);
      } else if (content.exercise || content.exerciseAdvice) {
        // 原有结构
        result.exerciseAdvice = extractArrayData(content.exercise || content.exerciseAdvice);
        console.log('【AI数据提取】从原有结构提取运动建议:', result.exerciseAdvice.length);
      }

      // 生活习惯建议提取
      if (content.lifestyleAndMentalHealth?.lifestyle?.habits) {
        // 新的嵌套结构
        result.lifestyleAdvice = extractArrayData(content.lifestyleAndMentalHealth.lifestyle.habits);
        console.log('【AI数据提取】从嵌套结构提取生活习惯建议:', result.lifestyleAdvice.length);
      } else if (content.habits || content.lifestyle || content.lifestyleAdvice) {
        // 原有结构
        result.lifestyleAdvice = extractArrayData(content.habits || content.lifestyle || content.lifestyleAdvice);
        console.log('【AI数据提取】从原有结构提取生活习惯建议:', result.lifestyleAdvice.length);
      }

      // 心理建议提取
      if (content.lifestyleAndMentalHealth?.mentalHealth?.copingStrategies) {
        // 新的嵌套结构
        result.psychologicalAdvice = extractArrayData(content.lifestyleAndMentalHealth.mentalHealth.copingStrategies);
        console.log('【AI数据提取】从嵌套结构提取心理建议:', result.psychologicalAdvice.length);
      } else if (content.copingStrategies || content.psychological || content.psychologicalAdvice) {
        // 原有结构
        result.psychologicalAdvice = extractArrayData(
          content.copingStrategies || content.psychological || content.psychologicalAdvice
        );
        console.log('【AI数据提取】从原有结构提取心理建议:', result.psychologicalAdvice.length);
      }
      
      monitor.checkpoint("提取四类建议");

      // 提取医院推荐 - 增加对嵌套结构的支持
      if (content.hospitalRecommendations?.hospitals) {
        // 新的嵌套结构
        result.hospitalRecommendations = extractHospitalData(content.hospitalRecommendations.hospitals);
        console.log('【AI数据提取】从嵌套结构提取医院推荐:', result.hospitalRecommendations.length);
      } else if (content.hospitals) {
        // 原有结构
        result.hospitalRecommendations = extractHospitalData(content.hospitals);
        console.log('【AI数据提取】从原有结构提取医院推荐:', result.hospitalRecommendations.length);
      }
      
      monitor.checkpoint("提取医院推荐");

      // 提取风险等级和状态
      if (content.dashboardData?.riskLevel) {
        // 从dashboardData中提取风险等级
        result.riskLevel = String(content.dashboardData.riskLevel);
        
        // 同时提取状态和趋势
        if (content.dashboardData.status) {
          result.status = content.dashboardData.status;
        }
        if (content.dashboardData.trend) {
          result.trend = content.dashboardData.trend;
        }
        console.log(`【AI数据提取】从dashboardData提取风险等级: ${result.riskLevel}, 状态: ${result.status}, 趋势: ${result.trend}`);
      } else if (content.riskLevel) {
        // 直接从根级别提取
        result.riskLevel = String(content.riskLevel);
        console.log(`【AI数据提取】从根级别提取风险等级: ${result.riskLevel}`);
      } else if (
        contentText.includes("高危") ||
        contentText.includes("进展期")
      ) {
        result.riskLevel = "高";
        result.trend = "进展期";
        result.status = "高危";
        console.log(`【AI数据提取】从文本内容推断风险等级: 高危进展期`);
      } else if (contentText.includes("稳定期")) {
        result.riskLevel = "中";
        result.trend = "稳定期";
        result.status = "稳定";
        console.log(`【AI数据提取】从文本内容推断风险等级: 稳定期`);
      } else {
        // 默认值
        result.riskLevel = "中";
        result.trend = "稳定期";
        result.status = "稳定";
        console.log(`【AI数据提取】使用默认风险等级: 稳定期`);
      }
      monitor.checkpoint("提取风险等级");
    }

    // 检查是否需要使用演示数据
    const hasDietAdvice = result.dietAdvice.length > 0;
    const hasExerciseAdvice = result.exerciseAdvice.length > 0;
    const hasLifestyleAdvice = result.lifestyleAdvice.length > 0;
    const hasPsychologicalAdvice = result.psychologicalAdvice.length > 0;
    const hasHospitals = result.hospitalRecommendations.length > 0;

    if (
      !hasDietAdvice &&
      !hasExerciseAdvice &&
      !hasLifestyleAdvice &&
      !hasPsychologicalAdvice
    ) {
      console.log('【AI数据提取】未找到任何建议数据，启用演示模式');
      result.isDemo = true;
    }

    if (!hasHospitals) {
      console.log('【AI数据提取】未找到任何医院推荐数据，启用演示模式');
      result.isDemo = true;
    }

    monitor.end(
      `【AI数据提取】输出结果大小: 建议=${result.dietAdvice.length + result.exerciseAdvice.length + result.lifestyleAdvice.length + result.psychologicalAdvice.length}, 医院=${result.hospitalRecommendations.length}, 演示模式=${result.isDemo}`,
    );
    return result;
  } catch (e: any) {
    console.error("【AI数据提取】提取AI报告数据出错:", e);
    monitor.end("处理出错");
    return {
      hasStructuredData: true,
      trend: "进展期",
      status: "高危",
      riskLevel: "高",
      isDemo: true,
    };
  }
};

/**
 * 从文本中提取特定模式的段落
 */
function extractSectionByPattern(text: string, pattern: RegExp): string | null {
  const match = text.match(pattern);
  if (match && match[2]) {
    return match[2].trim();
  }
  return null;
}

/**
 * 从段落中提取项目点
 */
function extractBulletPoints(text: string): string[] {
  if (!text) return [];
  
  // 处理项目符号列表
  // 支持各种项目符号格式: 1. 2. 3. 或 • 或 - 或 * 或 数字+.
  const bulletMatches = text.match(/(?:^|\n)(?:\d+[.、]|[*\-•])\s*(.+?)(?=\n(?:\d+[.、]|[*\-•])|\n\n|$)/g);
  
  if (bulletMatches && bulletMatches.length > 0) {
    return bulletMatches
      .map(item => item.replace(/(?:^|\n)(?:\d+[.、]|[*\-•])\s*/, '').trim())
      .filter(item => item.length > 0);
  }
  
  // 如果没有找到项目符号，尝试按行分割
  const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0);
  if (lines.length > 0) {
    return lines;
  }
  
  // 如果没有换行，将整个文本作为一项
  return [text.trim()];
}

/**
 * 从数据中提取数组内容
 */
function extractArrayData(data: any): string[] {
  if (!data) return [];

  console.log(`【提取数组】数据类型: ${typeof data}, 是否数组: ${Array.isArray(data)}`);
  
  if (Array.isArray(data)) {
    // 数组处理
    const result = data
      .map((item) => {
        if (typeof item === "string") {
          return item;
        } else if (typeof item === "object") {
          // 如果对象有text或content或value属性，使用它
          if (item.text) return item.text;
          if (item.content) return item.content;
          if (item.value) return item.value;
          if (item.advice) return item.advice;
          if (item.recommendation) return item.recommendation;
          // 其他情况尝试序列化对象
          return JSON.stringify(item);
        } else if (item !== null && item !== undefined) {
          // 处理数字等其他类型
          return String(item);
        }
        return null;
      })
      .filter(Boolean);
    
    console.log(`【提取数组】成功从数组提取 ${result.length} 条项目`);
    return result;
  } else if (typeof data === "string") {
    // 字符串处理 - 检查是否包含分隔符，如果有，尝试分割成数组
    if (data.includes('\n')) {
      const lines = data.split('\n').map(line => line.trim()).filter(Boolean);
      console.log(`【提取数组】从换行分隔的字符串提取 ${lines.length} 条项目`);
      return lines;
    }
    // 单条字符串作为数组的一个元素返回
    console.log(`【提取数组】从单条字符串创建数组`);
    return [data];
  } else if (typeof data === "object" && data !== null) {
    // 对象处理 - 尝试从对象的值中提取
    const values = Object.values(data).filter(v => v !== null && v !== undefined);
    if (values.length > 0) {
      const flatValues = values.flatMap(v => {
        if (Array.isArray(v)) return extractArrayData(v);
        if (typeof v === "string") return [v];
        if (typeof v === "object") return extractArrayData(v);
        return [String(v)];
      });
      console.log(`【提取数组】从对象的值中提取 ${flatValues.length} 条项目`);
      return flatValues;
    }
  }

  console.log(`【提取数组】无法从数据中提取有效数组，返回空数组`);
  return [];
}

/**
 * 从数据中提取医院推荐
 */
function extractHospitalData(data: any): any[] {
  if (!data) return [];

  console.log(`【提取医院】数据类型: ${typeof data}, 是否数组: ${Array.isArray(data)}`);

  if (Array.isArray(data)) {
    const hospitals = data
      .map((hospital) => {
        if (typeof hospital === "string") {
          console.log(`【提取医院】处理字符串医院: ${hospital.substring(0, 30)}...`);
          return { name: hospital, details: hospital };
        } else if (hospital && typeof hospital === "object") {
          console.log(`【提取医院】处理对象医院，键: ${Object.keys(hospital).join(', ')}`);
          return {
            id: hospital.id || `hospital-${Math.random().toString(36).substring(2, 9)}`,
            name: hospital.name || hospital.hospitalName || hospital.title || "",
            address: hospital.address || hospital.location || hospital.addressDetail || "",
            // 提取更多医院详细信息
            level: hospital.level || hospital.hospitalLevel || "",
            department: hospital.department || hospital.departments || hospital.speciality || "",
            matchScore: hospital.matchScore || hospital.score || hospital.rating || 0,
            advantages: Array.isArray(hospital.advantages) ? hospital.advantages : 
                        Array.isArray(hospital.features) ? hospital.features : 
                        Array.isArray(hospital.specialFeatures) ? hospital.specialFeatures : [],
            contactInfo: hospital.contactInfo || hospital.contact || {},
            // 兼容旧字段
            details: hospital.details || hospital.description || hospital.name || "",
            recommendation_reason: hospital.recommendation_reason || hospital.reason || hospital.reasonToRecommend || "",
            isRecommended: true
          };
        }
        return null;
      })
      .filter(Boolean);
    
    console.log(`【提取医院】成功提取 ${hospitals.length} 家医院`);
    return hospitals;
  } else if (typeof data === "string") {
    // 尝试处理字符串格式
    console.log(`【提取医院】处理字符串格式医院数据`);
    try {
      // 尝试解析为JSON
      const parsedData = JSON.parse(data);
      return extractHospitalData(parsedData);
    } catch (e) {
      // 可能是一个简单的医院名列表，按行分割
      if (data.includes('\n')) {
        const hospitals = data.split('\n')
          .map(line => line.trim())
          .filter(line => line.length > 0)
          .map(name => ({ name, details: name }));
        console.log(`【提取医院】从多行文本提取 ${hospitals.length} 家医院`);
        return hospitals;
      }
      // 单行医院名
      console.log(`【提取医院】从单行文本创建医院对象`);
      return [{ name: data, details: data }];
    }
  } else if (typeof data === "object" && data !== null) {
    // 对象可能包含医院列表
    console.log(`【提取医院】处理对象格式医院容器，键: ${Object.keys(data).join(', ')}`);
    // 寻找可能是医院列表的属性
    for (const key of ['hospitals', 'hospitalList', 'recommendedHospitals', 'list', 'items']) {
      if (data[key] && (Array.isArray(data[key]) || typeof data[key] === 'object')) {
        console.log(`【提取医院】找到可能的医院列表属性: ${key}`);
        return extractHospitalData(data[key]);
      }
    }
    
    // 检查这个对象本身是否是单个医院
    if (data.name || data.hospitalName) {
      console.log(`【提取医院】对象似乎是单个医院`);
      return [extractHospitalData([data])[0]];
    }
    
    // 尝试提取所有值
    const values = Object.values(data).filter(v => v !== null && v !== undefined);
    for (const value of values) {
      if (Array.isArray(value) || (typeof value === 'object' && value !== null)) {
        const hospitals = extractHospitalData(value);
        if (hospitals.length > 0) {
          console.log(`【提取医院】从对象值递归提取到 ${hospitals.length} 家医院`);
          return hospitals;
        }
      }
    }
  }

  console.log(`【提取医院】无法提取医院数据，返回空数组`);
  return [];
}
