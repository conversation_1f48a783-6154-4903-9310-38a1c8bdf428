@echo off
chcp 65001 >nul
echo 🔄 开始重启后端服务...

REM 检查是否在正确的目录
if not exist "backend" (
    echo ❌ 错误：请在项目根目录运行此脚本
    pause
    exit /b 1
)

REM 进入后端目录
cd backend

echo 📦 检查后端依赖...
if not exist "node_modules" (
    echo 📥 安装后端依赖...
    npm install
)

echo 🛑 停止现有的后端进程...
REM 停止现有的 Node.js 进程
taskkill /f /im node.exe 2>nul
taskkill /f /im nodemon.exe 2>nul

REM 等待进程完全停止
timeout /t 2 /nobreak >nul

echo 🚀 启动后端服务...
REM 检查是否有 PM2
where pm2 >nul 2>&1
if %errorlevel% == 0 (
    echo 使用 PM2 启动服务...
    pm2 delete hkb-backend 2>nul
    pm2 start src/index.js --name hkb-backend --watch
    pm2 logs hkb-backend --lines 10
) else (
    echo 使用 nodemon 启动服务...
    start /b npm run dev
    echo 后端服务已在后台启动
    
    REM 等待服务启动
    timeout /t 3 /nobreak >nul
    
    REM 检查服务是否启动成功
    curl -s http://localhost:3001/health >nul 2>&1
    if %errorlevel% == 0 (
        echo ✅ 后端服务启动成功！
        echo 🌐 健康检查：http://localhost:3001/health
    ) else (
        echo ⚠️ 后端服务可能未完全启动，请检查控制台输出
    )
)

echo.
echo 🎉 后端服务重启完成！
echo.
echo 📋 服务信息：
echo    - API 端口：3001-3004
echo    - WebSocket 端口：3005-3008
echo    - 健康检查：http://localhost:3001/health
echo.

pause
