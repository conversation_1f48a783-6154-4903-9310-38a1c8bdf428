import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Paper,
  CircularProgress, 
  useTheme,
  Button,
  Chip,
  alpha,
  Card,
  CardContent,
  Collapse,
  IconButton,
  Tooltip
} from '@mui/material';
import { 
  AccessTime as TimeIcon,
  LocalHospital as HospitalIcon,
  PriorityHigh as ImportantIcon,
  Add as AddIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  List as ListIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { usePatientDiseaseContext } from '../../context/PatientDiseaseContext';
import { getRecords } from '../../services/recordService';
import { useNavigate } from 'react-router-dom';

// 记录接口定义
interface RecordInfo {
  id: string;
  title: string;
  content?: string;
  record_date?: string;
  recordDate?: string;
  created_at?: string;
  updated_at?: string;
  is_important?: boolean;
  type?: string | string[];
  stageNode?: string;
  stageNodeName?: string;
  stagePhase?: string;
  stagePhaseName?: string;
  severity?: string;
  patient_name?: string;
  disease_name?: string;
}

/**
 * 格式化日期为本地化字符串
 */
const formatDate = (date: Date | string): string => {
  try {
    const dateObj = date instanceof Date ? date : new Date(date);
    if (isNaN(dateObj.getTime())) return '无效日期';
    return dateObj.toLocaleString('zh-CN', { 
      year: 'numeric', 
      month: '2-digit', 
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    return '无效日期';
  }
};

/**
 * 病理记录容器组件
 * 根据选择的患者和病理显示记录列表
 */
const DiseaseRecordsContainer: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { selectedPatientId, selectedDiseaseId } = usePatientDiseaseContext();
  const [loading, setLoading] = useState(false);
  const [records, setRecords] = useState<RecordInfo[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [expanded, setExpanded] = useState(true);
  const [recordLimit, setRecordLimit] = useState(6);

  // 获取记录数据
  useEffect(() => {
    const fetchRecords = async () => {
      if (!selectedPatientId) {
        console.log('没有选择患者，不获取记录');
        setRecords([]);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        // 构建查询参数
        const params: any = {
          patientId: selectedPatientId,
          patient_id: selectedPatientId,
          limit: recordLimit,
          skip: 0,
          sort: 'recordDate:desc,created_at:desc', // 按日期降序排序
          include_all_users: true, // 包含所有用户的记录
          
          // 尝试多种可能的过滤已删除记录的参数格式
          deleted: false,
          deleted_at: null,
          include_deleted: false,
          with_deleted: false,
          _deleted: false
        };

        // 如果选择了病理，添加病理ID筛选
        if (selectedDiseaseId) {
          params.diseaseId = selectedDiseaseId;
          params.disease_id = selectedDiseaseId;
          console.log(`根据患者ID: ${selectedPatientId} 和病理ID: ${selectedDiseaseId} 筛选记录，排除已删除记录`);
        } else {
          console.log(`仅根据患者ID: ${selectedPatientId} 筛选记录，不限病理，排除已删除记录`);
        }

        console.log('获取记录列表，参数:', JSON.stringify(params, null, 2));
        const response = await getRecords(params);
        
        console.log('API原始返回数据类型:', typeof response);
        console.log('API原始返回数据是数组?', Array.isArray(response));
        console.log('API完整响应数据:', JSON.stringify(response, null, 2));
        console.log('API响应中的记录:', Array.isArray(response) ? response.length : 
                   (response?.results?.length || response?.data?.length || 
                    (response?.result && Array.isArray(response.result) ? response.result.length : 0) || 
                    '未找到记录数组'));
        
        // 判断响应数据结构
        let recordsData: any[] = [];
        
        if (Array.isArray(response)) {
          recordsData = response;
        } else if (response && typeof response === 'object') {
          if (Array.isArray(response.results)) {
            recordsData = response.results;
          } else if (Array.isArray(response.data)) {
            recordsData = response.data;
          } else if (response.result && Array.isArray(response.result)) {
            recordsData = response.result;
          } else {
            // 尝试将对象转换为数组
            console.warn('API返回的数据结构不是预期格式，尝试转换为数组');
            try {
              recordsData = Object.values(response).filter(item => 
                item && typeof item === 'object' && 'id' in item
              );
            } catch (err) {
              console.error('无法将响应转换为记录数组:', err);
            }
          }
        }
        
        console.log(`获取到${recordsData.length}条记录原始数据`);
        
        // 处理和标准化记录数据
        const processedRecords = recordsData.map((record: any) => {
          // 确保记录中的stageName和stagePhase被正确识别
          let stageNode = record.stageNode || record.stage_node;
          let stagePhase = record.stagePhase || record.stage_phase;
          
          // 中文名称的适配
          if (record.stageNodeName) {
            stageNode = record.stageNodeName;
          }
          if (record.stagePhaseName) {
            stagePhase = record.stagePhaseName;
          }
          
          // 确保记录日期是有效的
          let recordDate = record.record_date || record.recordDate || record.created_at || record.updated_at;
          
          // 确保日期格式是字符串
          if (recordDate && typeof recordDate === 'object' && recordDate.toISOString) {
            recordDate = recordDate.toISOString();
          }

          // 如果病理名称不存在，尝试使用主病理类型
          const diseaseName = record.disease_name || record.diseaseName || 
                             record.disease?.name || '未知病理';
                               
          // 标准化record_type字段
          let recordType = record.record_type || record.recordType;
          if (typeof recordType === 'string') {
            try {
              recordType = JSON.parse(recordType);
            } catch (e) {
              recordType = [recordType];
            }
          } else if (!recordType) {
            recordType = ['其他']; 
          }
          
          return {
            id: record.id,
            title: record.title || record.name || `记录 #${record.id}`,
            content: record.content || record.description,
            stageNode,
            stagePhase,
            recordDate,
            record_date: record.record_date,
            stageNodeName: record.stageNodeName || stageNode,
            stagePhaseName: record.stagePhaseName || stagePhase,
            created_at: record.created_at,
            updated_at: record.updated_at,
            is_important: record.is_important || record.isImportant || false,
            type: recordType,
            severity: record.severity,
            disease_name: diseaseName
          };
        });
        
        console.log(`处理后的记录数据 (${processedRecords.length}条):`, processedRecords);
        
        if (processedRecords.length > 0) {
          console.log('第一条记录示例:', JSON.stringify(processedRecords[0], null, 2));
        } else {
          console.log('没有符合条件的记录');
        }
        
        setRecords(processedRecords);
      } catch (err) {
        console.error('获取记录失败:', err);
        setError('获取记录失败，请重试');
        setRecords([]);
      } finally {
        setLoading(false);
      }
    };

    if (selectedPatientId) {
      console.log(`记录容器组件：检测到患者ID:${selectedPatientId}, 病理ID:${selectedDiseaseId || '未选择'}`);
      fetchRecords();
    }
  }, [selectedPatientId, selectedDiseaseId, recordLimit]);

  // 刷新记录数据
  const handleRefresh = () => {
    if (selectedPatientId) {
      setLoading(true);
      // 重新获取数据
      const fetchRecords = async () => {
        try {
          const params: any = {
            patientId: selectedPatientId,
            patient_id: selectedPatientId,
            limit: recordLimit,
            skip: 0,
            sort: 'recordDate:desc,created_at:desc',
            include_all_users: true, // 包含所有用户的记录
            
            // 尝试多种可能的过滤已删除记录的参数格式
            deleted: false,
            deleted_at: null,
            include_deleted: false,
            with_deleted: false,
            _deleted: false
          };
          
          // 如果选择了病理，添加病理ID筛选
          if (selectedDiseaseId) {
            params.diseaseId = selectedDiseaseId;
            params.disease_id = selectedDiseaseId;
            console.log(`刷新 - 根据患者ID: ${selectedPatientId} 和病理ID: ${selectedDiseaseId} 筛选记录，排除已删除记录`);
          } else {
            console.log(`刷新 - 仅根据患者ID: ${selectedPatientId} 筛选记录，不限病理，排除已删除记录`);
          }
          
          console.log('刷新 - 获取记录列表，参数:', JSON.stringify(params, null, 2));
          const response = await getRecords(params);
          
          // 处理响应数据结构
          let recordsData: any[] = [];
          
          if (Array.isArray(response)) {
            recordsData = response;
          } else if (response && typeof response === 'object') {
            if (Array.isArray(response.results)) {
              recordsData = response.results;
            } else if (Array.isArray(response.data)) {
              recordsData = response.data;
            } else if (response.result && Array.isArray(response.result)) {
              recordsData = response.result;
            } else {
              // 尝试将对象转换为数组
              try {
                recordsData = Object.values(response).filter(item => 
                  item && typeof item === 'object' && 'id' in item
                );
              } catch (err) {
                console.error('无法将响应转换为记录数组:', err);
              }
            }
          }
          
          // 处理和标准化记录数据
          const processedRecords = recordsData.map((record: any) => {
            // 确保记录中的stageName和stagePhase被正确识别
            let stageNode = record.stageNode || record.stage_node;
            let stagePhase = record.stagePhase || record.stage_phase;
            
            // 中文名称的适配
            if (record.stageNodeName) {
              stageNode = record.stageNodeName;
            }
            if (record.stagePhaseName) {
              stagePhase = record.stagePhaseName;
            }
            
            // 确保记录日期是有效的
            let recordDate = record.record_date || record.recordDate || record.created_at || record.updated_at;
            
            // 确保日期格式是字符串
            if (recordDate && typeof recordDate === 'object' && recordDate.toISOString) {
              recordDate = recordDate.toISOString();
            }

            // 如果病理名称不存在，尝试使用主病理类型
            const diseaseName = record.disease_name || record.diseaseName || 
                               record.disease?.name || '未知病理';
                               
            // 标准化record_type字段
            let recordType = record.record_type || record.recordType;
            if (typeof recordType === 'string') {
              try {
                recordType = JSON.parse(recordType);
              } catch (e) {
                recordType = [recordType];
              }
            } else if (!recordType) {
              recordType = ['其他']; 
            }
            
            return {
              id: record.id,
              title: record.title || record.name || `记录 #${record.id}`,
              content: record.content || record.description,
              stageNode,
              stagePhase,
              recordDate,
              record_date: record.record_date,
              stageNodeName: record.stageNodeName || stageNode,
              stagePhaseName: record.stagePhaseName || stagePhase,
              created_at: record.created_at,
              updated_at: record.updated_at,
              is_important: record.is_important || record.isImportant || false,
              type: recordType,
              severity: record.severity,
              disease_name: diseaseName
            };
          });
          
          console.log(`刷新 - 处理后的记录数据 (${processedRecords.length}条)`);
          setRecords(processedRecords);
          setError(null);
        } catch (err) {
          console.error('刷新记录失败:', err);
          setError('刷新记录失败，请重试');
        } finally {
          setLoading(false);
        }
      };
      
      fetchRecords();
    }
  };

  // 添加记录按钮点击事件
  const handleAddRecord = () => {
    navigate('/records/new');
  };

  // 查看全部记录按钮点击事件
  const handleViewAllRecords = () => {
    navigate('/records/manage');
  };

  // 切换展开/折叠状态
  const toggleExpanded = () => {
    setExpanded(!expanded);
  };

  // 显示更多记录
  const handleShowMore = () => {
    setRecordLimit(prev => prev + 6);
  };

  // 获取记录日期
  const getRecordDate = (record: RecordInfo): string => {
    const dateStr = record.record_date || record.recordDate || record.created_at || '';
    return dateStr ? formatDate(new Date(dateStr)) : '未知日期';
  };

  // 获取记录类型
  const getRecordType = (record: RecordInfo): string => {
    if (!record.type) return '其他';
    
    if (Array.isArray(record.type) && record.type.length > 0) {
      return record.type[0];
    }
    
    if (typeof record.type === 'string') {
      try {
        const parsed = JSON.parse(record.type);
        return Array.isArray(parsed) && parsed.length > 0 ? parsed[0] : '其他';
      } catch (e) {
        return record.type || '其他';
      }
    }
    
    return '其他';
  };

  // 获取记录的阶段信息
  const getStageInfo = (record: RecordInfo): string => {
    return record.stageNodeName || record.stageNode || record.stagePhaseName || record.stagePhase || '';
  };

  // 获取记录严重程度对应的颜色
  const getSeverityColor = (severity?: string): string => {
    switch (severity?.toUpperCase()) {
      case 'MILD': return theme.palette.success.main;
      case 'MODERATE': return theme.palette.warning.main;
      case 'SEVERE': return theme.palette.error.main;
      case 'CRITICAL': return theme.palette.error.dark;
      default: return theme.palette.grey[500];
    }
  };

  // 如果没有选择患者，不显示组件
  if (!selectedPatientId) {
    return null;
  }

  return (
    <Paper 
      elevation={0}
      sx={{
        borderRadius: 2,
        border: '1px solid',
        borderColor: 'divider',
        mt: 2,
        overflow: 'hidden'
      }}
    >
      {/* 头部和操作按钮 */}
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'space-between',
        p: { xs: 1, sm: 2 },
        backgroundColor: 'background.paper',
        borderBottom: expanded ? '1px solid' : 'none',
        borderColor: 'divider'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography
            variant="h6"
            sx={{
              fontSize: { xs: '1rem', sm: '1.1rem' },
              fontWeight: 600,
              color: 'primary.main'
            }}
          >
            记录列表
          </Typography>
          
          {selectedDiseaseId && (
            <Chip 
              label={`仅限选中病理`} 
              size="small" 
              color="primary" 
              variant="outlined"
              sx={{ ml: 1, height: 22, fontSize: '0.7rem' }}
            />
          )}
          
          {loading && <CircularProgress size={16} sx={{ ml: 1 }} />}
        </Box>
        
        <Box>
          <Tooltip title="刷新列表">
            <IconButton 
              size="small" 
              onClick={handleRefresh}
              disabled={loading}
              sx={{ mr: 0.5 }}
            >
              <RefreshIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="查看全部记录">
            <IconButton 
              size="small" 
              onClick={handleViewAllRecords}
              sx={{ mr: 0.5 }}
            >
              <ListIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="添加记录">
            <IconButton 
              size="small" 
              color="primary"
              onClick={handleAddRecord}
              sx={{ mr: 0.5 }}
            >
              <AddIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          
          <Tooltip title={expanded ? "收起" : "展开"}>
            <IconButton size="small" onClick={toggleExpanded}>
              {expanded ? <ExpandLessIcon fontSize="small" /> : <ExpandMoreIcon fontSize="small" />}
            </IconButton>
          </Tooltip>
        </Box>
      </Box>
      
      {/* 记录列表 */}
      <Collapse in={expanded}>
        <Box sx={{ p: { xs: 1, sm: 2 } }}>
          {loading && records.length === 0 ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress size={24} />
            </Box>
          ) : error ? (
            <Box sx={{ p: 2, textAlign: 'center', color: 'error.main' }}>
              <Typography variant="body2">{error}</Typography>
              <Button 
                variant="outlined" 
                size="small" 
                sx={{ mt: 1 }}
                onClick={handleRefresh}
              >
                重试
              </Button>
            </Box>
          ) : records.length === 0 ? (
            <Box sx={{ 
              p: 4, 
              textAlign: 'center', 
              display: 'flex', 
              flexDirection: 'column',
              justifyContent: 'center', 
              alignItems: 'center',
              bgcolor: alpha(theme.palette.background.paper, 0.5),
              borderRadius: 2,
              border: '1px dashed',
              borderColor: 'divider'
            }}>
              <Typography color="text.secondary" sx={{ mb: 1 }}>
                {selectedDiseaseId ? '该病理下暂无记录数据' : '没有找到相关记录'}
              </Typography>
              <Button 
                variant="outlined" 
                size="small"
                startIcon={<AddIcon />}
                color="primary"
                onClick={handleAddRecord}
                sx={{ mt: 1 }}
              >
                添加记录
              </Button>

              {process.env.NODE_ENV === 'development' && (
                <Button 
                  variant="outlined" 
                  size="small"
                  color="warning"
                  onClick={() => {
                    // 临时关闭过滤器，显示所有记录（包括已删除的）
                    setLoading(true);
                    console.log('===== 调试模式：获取所有记录（包括已删除）=====');
                    
                    const fetchAllRecords = async () => {
                      try {
                        const debugParams: any = {
                          patientId: selectedPatientId,
                          patient_id: selectedPatientId,
                          limit: 50,
                          skip: 0,
                          with_deleted: true,
                          include_deleted: true,
                          include_all_users: true
                        };
                        
                        if (selectedDiseaseId) {
                          debugParams.diseaseId = selectedDiseaseId;
                          debugParams.disease_id = selectedDiseaseId;
                        }
                        
                        console.log('调试模式参数:', JSON.stringify(debugParams, null, 2));
                        const response = await getRecords(debugParams);
                        console.log('调试模式响应:', JSON.stringify(response, null, 2));
                        
                        let recordsData: any[] = [];
                        if (Array.isArray(response)) {
                          recordsData = response;
                        } else if (response && typeof response === 'object') {
                          recordsData = response.results || response.data || 
                            (response.result && Array.isArray(response.result) ? response.result : []);
                        }
                        
                        console.log(`调试模式: 获取到${recordsData.length}条记录`);
                        
                        if(recordsData.length === 0) {
                          console.log('调试模式: 没有找到任何记录（即使包括已删除的）');
                          alert('没有找到任何记录，请检查后端API或数据库');
                        } else {
                          console.log('调试模式: 第一条记录:', recordsData[0]);
                          alert(`找到了${recordsData.length}条记录，请查看控制台日志`);
                        }
                      } catch (err) {
                        console.error('调试模式错误:', err);
                        alert('获取记录失败，请查看控制台日志');
                      } finally {
                        setLoading(false);
                      }
                    };
                    
                    fetchAllRecords();
                  }}
                  sx={{ mt: 1 }}
                >
                  调试: 查看所有记录
                </Button>
              )}
            </Box>
          ) : (
            <>
              {/* 记录卡片列表 */}
              <Box sx={{ display: 'flex', flexWrap: 'wrap', margin: -1 }}>
                {records.map((record) => (
                  <Box 
                    key={record.id} 
                    sx={{ 
                      width: { xs: '100%', sm: '50%', lg: '33.33%' }, 
                      padding: 1,
                      boxSizing: 'border-box'
                    }}
                  >
                    <Card 
                      sx={{ 
                        height: '100%', 
                        display: 'flex', 
                        flexDirection: 'column',
                        transition: 'all 0.2s ease',
                        cursor: 'pointer',
                        '&:hover': {
                          boxShadow: 3,
                          transform: 'translateY(-2px)'
                        }
                      }}
                      onClick={() => navigate(`/records/${record.id}`)}
                    >
                      <CardContent sx={{ p: 2, flexGrow: 1 }}>
                        {/* 记录标题 */}
                        <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="subtitle1" component="div" sx={{ 
                            fontWeight: 600, 
                            fontSize: '0.9rem',
                            mb: 0.5,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            lineHeight: 1.3,
                            flexGrow: 1
                          }}>
                            {record.title}
                          </Typography>
                          
                          {record.is_important && (
                            <ImportantIcon 
                              color="error" 
                              fontSize="small" 
                              sx={{ ml: 0.5, minWidth: 20, flexShrink: 0 }}
                            />
                          )}
                        </Box>
                        
                        {/* 记录内容预览 */}
                        {record.content && (
                          <Typography variant="body2" color="text.secondary" sx={{ 
                            fontSize: '0.8rem',
                            mb: 1.5, 
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            display: '-webkit-box',
                            WebkitLineClamp: 3,
                            WebkitBoxOrient: 'vertical',
                            lineHeight: 1.3
                          }}>
                            {record.content}
                          </Typography>
                        )}
                        
                        {/* 记录元数据 */}
                        <Box sx={{ mt: 'auto' }}>
                          {/* 记录日期 */}
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                            <TimeIcon sx={{ color: 'text.secondary', fontSize: '0.9rem', mr: 0.5 }} />
                            <Typography variant="caption" color="text.secondary">
                              {getRecordDate(record)}
                            </Typography>
                          </Box>
                          
                          {/* 记录所属病理 - 仅在未选择病理时显示 */}
                          {!selectedDiseaseId && record.disease_name && (
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                              <HospitalIcon sx={{ color: 'text.secondary', fontSize: '0.9rem', mr: 0.5 }} />
                              <Typography variant="caption" color="text.secondary" sx={{ mr: 0.5 }}>
                                {record.disease_name}
                              </Typography>
                            </Box>
                          )}
                          
                          {/* 阶段和类型标签 */}
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1 }}>
                            {getStageInfo(record) && (
                              <Chip
                                label={getStageInfo(record)}
                                size="small"
                                sx={{ 
                                  height: 20, 
                                  fontSize: '0.65rem',
                                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                                  color: theme.palette.primary.main
                                }}
                              />
                            )}
                            
                            <Chip
                              label={getRecordType(record)}
                              size="small"
                              sx={{ 
                                height: 20, 
                                fontSize: '0.65rem',
                                bgcolor: alpha(theme.palette.secondary.main, 0.1),
                                color: theme.palette.secondary.main
                              }}
                            />
                            
                            {record.severity && (
                              <Chip
                                label={record.severity}
                                size="small"
                                sx={{ 
                                  height: 20, 
                                  fontSize: '0.65rem',
                                  bgcolor: alpha(getSeverityColor(record.severity), 0.1),
                                  color: getSeverityColor(record.severity)
                                }}
                              />
                            )}
                          </Box>
                        </Box>
                      </CardContent>
                    </Card>
                  </Box>
                ))}
              </Box>
              
              {/* 底部操作区 */}
              {records.length >= recordLimit && (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                  <Button 
                    variant="outlined" 
                    size="small"
                    onClick={handleShowMore}
                    disabled={loading}
                  >
                    查看更多
                  </Button>
                </Box>
              )}
            </>
          )}
        </Box>
      </Collapse>
    </Paper>
  );
};

export default DiseaseRecordsContainer; 