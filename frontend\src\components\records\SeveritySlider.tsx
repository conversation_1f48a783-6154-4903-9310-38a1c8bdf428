import React from 'react';
import { Box, Typography, Slider } from '@mui/material';
import { SeverityEnum } from '../../types/recordEnums';

// 组件接口
interface SeveritySelectorProps {
  value: SeverityEnum | number;  // 支持枚举或数字值
  onChange: (value: SeverityEnum, numberValue: number) => void;
  disabled?: boolean;
}

// 严重程度值映射（枚举字符串到数字）
const SEVERITY_TO_NUMBER: Record<SeverityEnum, number> = {
  [SeverityEnum.MILD]: 1,       // 轻微 -> 1
  [SeverityEnum.MODERATE]: 2,   // 中等 -> 2
  [SeverityEnum.SEVERE]: 3,     // 严重 -> 3
  [SeverityEnum.CRITICAL]: 4    // 危重 -> 4
};

// 严重程度值映射（数字到枚举字符串）
const NUMBER_TO_SEVERITY: Record<number, SeverityEnum> = {
  1: SeverityEnum.MILD,
  2: SeverityEnum.MODERATE,
  3: SeverityEnum.SEVERE,
  4: SeverityEnum.CRITICAL
};

/**
 * 严重程度滑块组件
 * 用于选择记录或疾病的严重程度，支持枚举和数字值
 */
const SeveritySlider: React.FC<SeveritySelectorProps> = ({
  value,
  onChange,
  disabled = false
}) => {
  // 将输入值标准化为数字
  let currentNumValue: number;
  
  // 首先检查值的类型并做适当转换
  if (typeof value === 'number') {
    // 确保数字在1-4范围内
    currentNumValue = Math.max(1, Math.min(4, value)); 
  } else if (typeof value === 'string' && !isNaN(Number(value))) {
    // 如果是数字字符串（如"3"），直接转为数字
    currentNumValue = Math.max(1, Math.min(4, Number(value)));
  } else if (value && SEVERITY_TO_NUMBER[value as SeverityEnum]) {
    // 如果是枚举值，使用映射转换
    currentNumValue = SEVERITY_TO_NUMBER[value as SeverityEnum];
  } else {
    // 默认为中等(2)
    currentNumValue = 2;
  }
  
  // 记录转换日志，便于调试
  console.log('严重程度转换: 原始值=', value, '转换后=', currentNumValue);
  
  // 严重程度颜色映射
  const severityColors: Record<number, string> = {
    1: '#4caf50', // 绿色 - 轻微
    2: '#fbc02d', // 黄色 - 中等
    3: '#ff9800', // 橙色 - 严重
    4: '#f44336'  // 红色 - 危重
  };
  
  // 当前颜色
  const currentColor = severityColors[currentNumValue];
  
  // 处理滑块值变化
  const handleChange = (_event: Event, newValue: number | number[]) => {
    const numValue = newValue as number;
    const enumValue = NUMBER_TO_SEVERITY[numValue] || SeverityEnum.MODERATE;
    onChange(enumValue, numValue);
  };

  return (
    <Box sx={{ width: '100%', mb: 1 }}>
      <Typography 
        variant="subtitle2" 
        gutterBottom 
        sx={{ 
          fontWeight: 600,
          opacity: disabled ? 0.6 : 1
        }}
      >
        身体情况
        <Typography
          component="span"
          variant="body2"
          sx={{
            ml: 2,
            fontWeight: 500,
            color: disabled ? 'text.disabled' : currentColor,
            backgroundColor: disabled ? 'transparent' : `${currentColor}20`,
            px: 1.5,
            py: 0.5,
            borderRadius: 1,
            transition: 'all 0.3s ease',
          }}
        >
          {currentNumValue === 1 && '轻微'}
          {currentNumValue === 2 && '中等'}
          {currentNumValue === 3 && '严重'}
          {currentNumValue === 4 && '危重'}
        </Typography>
      </Typography>
      
      <Box sx={{ width: '100%', mt: 2 }}>
        <Slider
          min={1}
          max={4}
          value={currentNumValue}
          step={1}
          valueLabelDisplay="off"
          marks={[
            { value: 1, label: '轻微' },
            { value: 2, label: '中等' },
            { value: 3, label: '严重' },
            { value: 4, label: '危重' },
          ]}
          onChange={handleChange}
          disabled={disabled}
          sx={{
            height: 8,
            width: '100%',
            '& .MuiSlider-root': { 
              color: 'transparent'
            },
            '& .MuiSlider-thumb': {
              height: 24,
              width: 24,
              transition: '0.3s cubic-bezier(.47,1.64,.41,.8)',
              backgroundColor: '#fff',
              border: `2px solid ${currentColor}`,
              boxShadow: '0 2px 4px 0 rgba(0,0,0,0.2)',
              '&:before': {
                boxShadow: '0 2px 12px 0 rgba(0,0,0,0.2)',
              },
              '&:hover, &.Mui-focusVisible': {
                boxShadow: `0 0 0 8px ${currentColor}26`,
              },
              '&.Mui-active': {
                width: 28,
                height: 28,
                boxShadow: `0 0 0 14px ${currentColor}26`,
              },
            },
            '& .MuiSlider-rail': {
              opacity: 0.6,
              background: 'linear-gradient(to right, #4caf50, #fbc02d, #ff9800, #f44336)',
              height: 8,
              borderRadius: 4,
              boxShadow: 'inset 0 1px 3px rgba(0,0,0,0.1)',
            },
            '& .MuiSlider-track': {
              height: 8,
              borderRadius: 4,
              border: 'none',
              background: 'transparent',
            },
            '& .MuiSlider-mark': {
              width: 10,
              height: 10,
              borderRadius: '50%',
              backgroundColor: '#fff',
              border: '1px solid #bdbdbd',
              boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
              '&.MuiSlider-markActive': {
                opacity: 1,
                backgroundColor: '#fff',
                borderColor: '#757575',
                boxShadow: '0 1px 4px rgba(0,0,0,0.2)',
              }
            },
            '& .MuiSlider-markLabel': {
              fontSize: '0.85rem',
              fontWeight: 600,
              marginTop: '6px',
              textShadow: '0 1px 1px rgba(255,255,255,0.8)',
              '&[data-index="0"]': { // 轻微
                color: '#388e3c',
              },
              '&[data-index="1"]': { // 中等
                color: '#f9a825',
              },
              '&[data-index="2"]': { // 严重
                color: '#e65100',
              },
              '&[data-index="3"]': { // 危重
                color: '#d32f2f',
              },
            }
          }}
        />
      </Box>
    </Box>
  );
};

export default SeveritySlider; 