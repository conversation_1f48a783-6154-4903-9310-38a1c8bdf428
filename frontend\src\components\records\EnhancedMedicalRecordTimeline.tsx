import React, { useState, useCallback, useRef, useMemo, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Collapse,
  Chip,
  IconButton,
  Button,
  Divider,
  useTheme,
  TextField,
  InputAdornment,
  styled,
  Tooltip,
  CircularProgress,
  Fade,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Slide,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Modal
} from '@mui/material';
import { TransitionProps } from '@mui/material/transitions';
import { alpha } from '@mui/material/styles';
import { 
  Visibility as ViewIcon,
  Search as SearchIcon,
  KeyboardDoubleArrowUp as ScrollTopIcon,
  Close as ClearIcon,
  AccessTime as ClockIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  UnfoldLess as FoldAllIcon,
  Timeline as TimelineIcon,
  CalendarMonth as CalendarIcon,
  Star as StarIcon,
  Lock as LockIcon,
  History as HistoryIcon,
  UnfoldMore as UnfoldAllIcon,
  InsertDriveFile as InsertDriveFileIcon,
  Image as ImageIcon,
  PictureAsPdf as PictureAsPdfIcon,
  Description as DescriptionIcon,
  Download as DownloadIcon,
  Attachment as AttachmentIcon,
  ZoomIn as ZoomInIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { format, parseISO, isValid, getYear, getMonth } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import apiClient from '../../services/apiClient';

// 导入系统标准枚举
import { 
  RecordTypeEnum, 
  RecordTypeNames,
  SeverityEnum,
  SeverityNames
} from '../../types/recordEnums';

// 时间轴节点样式
const TimelineNode = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'color' && prop !== 'active',
})<{ color: string; active?: boolean }>(({ theme, color, active }) => ({
  width: active ? 16 : 12,
  height: active ? 16 : 12,
  borderRadius: '50%',
  backgroundColor: active ? color : theme.palette.mode === 'dark' ? theme.palette.grey[800] : theme.palette.grey[200],
  border: `2px solid ${active ? color : theme.palette.mode === 'dark' ? theme.palette.grey[700] : theme.palette.grey[300]}`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  color: active ? '#FFFFFF' : theme.palette.text.secondary,
  fontWeight: active ? 'bold' : 'normal',
  fontSize: '0.6rem',
  zIndex: 2,
  transition: 'all 0.2s ease',
  boxShadow: active ? `0 0 0 1px ${theme.palette.background.paper}, 0 0 0 3px ${color}40` : 'none',
  cursor: 'pointer',
  '&:hover': {
    transform: 'scale(1.1)',
    boxShadow: `0 0 0 1px ${theme.palette.background.paper}, 0 0 0 3px ${color}40`
  }
}));

// 时间轴线样式
const TimelineConnector = styled(Box)(({ theme }) => ({
  position: 'absolute',
  left: 14,
  top: 0,
  bottom: 0,
  width: 2,
  backgroundColor: theme.palette.mode === 'dark' ? theme.palette.grey[700] : theme.palette.grey[300],
  zIndex: 0,
  height: '100%', // 确保覆盖整个容器高度
}));

// 记录卡片样式
const RecordCard = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'borderColor',
})<{ borderColor: string }>(({ theme, borderColor }) => ({
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius,
  padding: '8px 12px',
  marginBottom: 6,
  border: `1px solid ${theme.palette.divider}`,
  borderLeft: `4px solid ${borderColor}`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  transition: 'background-color 0.2s ease',
  '&:hover': {
    backgroundColor: alpha(theme.palette.primary.main, 0.08),
    boxShadow: `0 2px 4px ${alpha(theme.palette.common.black, 0.05)}`
  }
}));

// 滚动顶部按钮
const ScrollTopButton = styled(IconButton)(({ theme }) => ({
  position: 'absolute',
  right: 16,
  bottom: 16,
  backgroundColor: theme.palette.primary.main,
  color: theme.palette.primary.contrastText,
  boxShadow: theme.shadows[4],
  '&:hover': {
    backgroundColor: theme.palette.primary.dark,
  },
  zIndex: 10,
}));

// 日期指示器样式
const DateIndicator = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: -36,
  right: 16,
  display: 'flex',
  alignItems: 'center',
  padding: '4px 8px',
  borderRadius: 16,
  backgroundColor: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(8px)',
  boxShadow: theme.shadows[1],
  zIndex: 5,
  fontSize: '0.75rem',
  color: theme.palette.text.secondary,
  '& .MuiSvgIcon-root': {
    fontSize: '0.875rem',
    marginRight: 4,
  },
}));

// 时间轴容器样式
const TimelineContainer = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'showTimeIndex',
})<{ showTimeIndex: boolean }>(({ theme, showTimeIndex }) => ({
  height: theme.breakpoints.down('sm') ? 'calc(100vh - 220px)' : 400,
  position: 'relative',
  borderRadius: theme.shape.borderRadius,
  border: `1px solid ${theme.palette.divider}`,
  overflow: 'auto',
  '&::-webkit-scrollbar': {
    display: 'none'
  },
  scrollbarWidth: 'none',
  msOverflowStyle: 'none',
  transition: 'all 0.3s ease',
  width: '100%' // 始终保持100%宽度
}));

// 时间索引容器样式
const TimeIndexContainer = styled(Box)(({ theme }) => ({
  position: 'absolute',
  right: 0,
  top: 0,
  bottom: 0,
  width: 60,
  backgroundColor: theme.palette.mode === 'dark' ? alpha(theme.palette.background.paper, 0.15) : alpha(theme.palette.background.paper, 0.15),
  borderLeft: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
  borderRadius: `0 ${theme.shape.borderRadius}px ${theme.shape.borderRadius}px 0`,
  display: 'flex',
  flexDirection: 'column',
  overflow: 'hidden',
  transition: 'all 0.3s ease',
  boxShadow: '-1px 0 4px rgba(0,0,0,0.03)',
  backdropFilter: 'blur(1px)',
  zIndex: 5, // 提高z-index使其覆盖在内容上方
  pointerEvents: 'none', // 容器本身不接收鼠标事件，使下方内容可点击
  '& > *': {
    pointerEvents: 'auto' // 子元素可以接收鼠标事件
  }
}));

// 年份按钮样式
const YearButton = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'active' && prop !== 'hasRecords',
})<{ active: boolean; hasRecords: boolean }>(({ theme, active, hasRecords }) => ({
  padding: '4px 0',
  fontSize: '0.75rem',
  fontWeight: active ? 600 : 400,
  textAlign: 'center',
  cursor: 'pointer',
  color: active 
    ? theme.palette.primary.main 
    : hasRecords 
      ? theme.palette.text.primary 
      : theme.palette.text.disabled,
  backgroundColor: active ? alpha(theme.palette.primary.main, 0.1) : 'transparent',
  borderLeft: active ? `3px solid ${theme.palette.primary.main}` : '3px solid transparent',
  '&:hover': {
    backgroundColor: alpha(theme.palette.primary.main, 0.05),
  }
}));

// 月份按钮样式
const MonthButton = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'active' && prop !== 'hasRecords',
})<{ active: boolean; hasRecords: boolean }>(({ theme, active, hasRecords }) => ({
  padding: '2px 0',
  fontSize: '0.7rem',
  fontWeight: active ? 600 : 400,
  textAlign: 'center',
  cursor: 'pointer',
  color: active 
    ? theme.palette.primary.main 
    : hasRecords 
      ? theme.palette.text.primary 
      : theme.palette.text.disabled,
  backgroundColor: active ? alpha(theme.palette.primary.main, 0.1) : 'transparent',
  borderLeft: active ? `2px solid ${theme.palette.primary.main}` : '2px solid transparent',
  '&:hover': {
    backgroundColor: alpha(theme.palette.primary.main, 0.05),
  }
}));

// 记录信息接口
export interface MedicalRecord {
  id: string;
  title: string;
  recordType?: RecordTypeEnum | string;
  severity?: SeverityEnum | string;
  recordDate?: string;
  created_at?: string;
  content?: string;
  description?: string;
  tags?: string[];
  stageNode?: string;
  stagePhase?: string;
  stage_node?: string;
  stage_phase?: string;
  typeTagsJson?: string | string[];
  stageTags?: string | string[];
  customTags?: string | string[];
  // 用于在UI隐藏源数据
  _hidesource?: boolean;
  is_important?: boolean;
  is_private?: boolean;
  attachments?: any[];
  hasAttachments?: boolean;
}

// Dialog转场动画
const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement;
  },
  ref: React.Ref<unknown>,
) {
  return <Slide direction="up" ref={ref} {...props} />;
});

// 记录详情弹窗组件
const RecordDetailDialog: React.FC<{
  open: boolean;
  record: MedicalRecord | null;
  onClose: () => void;
  getTypeColor: (type: RecordTypeEnum | string) => string;
  getTypeName: (type: RecordTypeEnum | string) => string;
  getSeverityName: (severity: SeverityEnum | string | undefined) => string;
  getSeverityColor: (severity: SeverityEnum | string | undefined) => { bg: string, text: string };
  formatChineseDate: (date: Date) => string;
}> = ({
  open,
  record,
  onClose,
  getTypeColor,
  getTypeName,
  getSeverityName,
  getSeverityColor,
  formatChineseDate
}) => {
  const theme = useTheme();
  const [attachments, setAttachments] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [recordDetail, setRecordDetail] = useState<any>(null);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewUrl, setPreviewUrl] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');
  
  // 处理记录详情与附件
  useEffect(() => {
    const fetchAttachments = async (recordId: string) => {
      setLoading(true);
      
      try {
        console.log('开始获取附件:', recordId);
        
        // 使用apiClient代替fetch，确保使用正确的baseURL
        const response = await apiClient.get(`/records/attachments/record/${recordId}`, {
          params: {
            include_all_users: true
          }
        });
        
        console.log('附件API响应:', response);
        
        const data = response.data;
        
        if (Array.isArray(data)) {
          console.log('获取到附件数组:', data.length, '个');
          setAttachments(data);
        } else if (data && data.data && Array.isArray(data.data)) {
          console.log('获取到嵌套附件数组:', data.data.length, '个');
          setAttachments(data.data);
        } else {
          console.log('附件数据结构不符合预期，设置为空:', data);
          setAttachments([]);
        }
      } catch (error) {
        console.error('获取附件失败:', error);
        setAttachments([]);
      } finally {
        setLoading(false);
      }
    };
    
    if (record && open) {
      // 设置记录详情
      setRecordDetail(record);
      
      // 获取附件信息
      if (record.id) {
        fetchAttachments(record.id);
      }
    } else {
      setRecordDetail(null);
      setAttachments([]);
    }
  }, [record, open]);
  
  if (!record) return null;
  
  // 获取记录日期
  const recordDateObj = (() => {
    try {
      const dateStr = record.recordDate || record.created_at || '';
      const date = parseISO(dateStr);
      if (isValid(date)) {
        return date;
      }
    } catch (e) {}
    return new Date();
  })();
  
  // 处理标题中可能存在的JSON数组格式
  let cleanTitle = record.title;
  if (typeof record.title === 'string') {
    // 先尝试移除JSON格式标签的正则表达式
    cleanTitle = record.title.replace(/\[["']?[A-Z_]+["']?(,["']?[A-Z_]+["']?)*\]/g, '').trim();
    
    // 如果标题本身就是一个JSON数组格式
    if (record.title.startsWith('[') && record.title.endsWith(']')) {
      try {
        // 尝试解析JSON，如果成功就认为这是标签数组而非标题
        const possibleTags = JSON.parse(record.title);
        if (Array.isArray(possibleTags)) {
          // 这是标签数组，不是真正的标题，所以清空它
          cleanTitle = '';
        }
      } catch (e) {
        // 解析失败，保持原始标题
      }
    }
  }
  
  // 处理内容中可能存在的JSON数组格式
  let cleanContent = record.content || '';
  if (typeof record.content === 'string') {
    cleanContent = record.content.replace(/\[["']?[A-Z_]+["']?(,["']?[A-Z_]+["']?)*\]/g, '').trim();
  }
  
  // 处理recordType的逻辑 - 支持数组格式
  let recordTypeArray: string[] = [];
  
  // 如果是字符串但格式像数组 ["ASSESSMENT","PROGNOSIS"]
  if (typeof record.recordType === 'string' && 
      record.recordType.startsWith('[') && 
      record.recordType.endsWith(']')) {
    try {
      // 尝试解析JSON
      const parsed = JSON.parse(record.recordType);
      if (Array.isArray(parsed)) {
        recordTypeArray = parsed;
      } else {
        recordTypeArray = [record.recordType];
      }
    } catch (e) {
      // 解析失败则当作普通字符串
      recordTypeArray = [record.recordType];
    }
  } 
  // 如果本身就是数组
  else if (Array.isArray(record.recordType)) {
    recordTypeArray = record.recordType;
  }
  // 普通字符串
  else {
    recordTypeArray = [record.recordType || RecordTypeEnum.OTHER];
  }
  
  // 获取第一个类型作为主类型
  const primaryType = recordTypeArray.length > 0 ? recordTypeArray[0] : RecordTypeEnum.OTHER;
  const typeColor = getTypeColor(primaryType);
  
  // 格式化文件大小
  const formatFileSize = (bytes?: number): string => {
    if (!bytes) return '未知大小';
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };
  
  // 获取附件图标
  const getAttachmentIcon = (fileName?: string) => {
    if (!fileName) return <InsertDriveFileIcon fontSize="small" />;
    
    const extension = fileName.split('.').pop()?.toLowerCase() || '';
    
    switch (extension) {
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
        return <ImageIcon fontSize="small" />;
      case 'pdf':
        return <PictureAsPdfIcon fontSize="small" />;
      case 'doc':
      case 'docx':
      case 'txt':
      case 'rtf':
      case 'odt':
        return <DescriptionIcon fontSize="small" />;
      default:
        return <InsertDriveFileIcon fontSize="small" />;
    }
  };
  
  // 判断附件是否可预览
  const isPreviewable = (attachment: any): boolean => {
    // 检查文件类型是否为图片
    const isImage = attachment.fileType?.startsWith('image/') || 
                   attachment.file_type?.startsWith('image/');
    
    // 检查文件大小是否小于500KB (500 * 1024 = 512000 字节)
    const fileSize = attachment.fileSize || attachment.file_size || 0;
    const isSizeAllowed = fileSize <= 512000;
    
    return isImage && isSizeAllowed;
  };
  
  // 打开预览
  const openPreview = async (attachmentId: string, fileName: string) => {
    try {
      setPreviewTitle(fileName);
      
      // 使用apiClient代替fetch
      const response = await apiClient.get(`/records/attachments/download/${attachmentId}`, {
        responseType: 'blob',
        params: {
          include_all_users: true
        }
      });
      
      // 创建Blob URL
      const url = window.URL.createObjectURL(new Blob([response.data]));
      setPreviewUrl(url);
      setPreviewOpen(true);
    } catch (error) {
      console.error('预览图片失败:', error);
    }
  };
  
  // 关闭预览
  const closePreview = () => {
    // 释放Blob URL
    if (previewUrl) {
      window.URL.revokeObjectURL(previewUrl);
    }
    setPreviewUrl('');
    setPreviewOpen(false);
  };
  
  // 下载附件处理函数
  const handleDownloadAttachment = async (attachmentId: string, fileName: string) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('未找到身份验证令牌，请重新登录');
      }

      // 获取已配置的后端地址
      const backendUrl = localStorage.getItem('backendServerIP') || apiClient.defaults.baseURL || 'http://localhost:3001';
      
      // 构建完整的URL
      const downloadUrl = `${backendUrl}/records/attachments/download/${attachmentId}?include_all_users=true`;
      
      console.log(`开始下载附件: ${downloadUrl}`);
      
      // 使用XMLHttpRequest代替fetch以更好地处理二进制数据和下载进度
      const xhr = new XMLHttpRequest();
      xhr.open('GET', downloadUrl, true);
      xhr.responseType = 'blob';
      xhr.setRequestHeader('Authorization', `Bearer ${token}`);
      
      // 添加完成事件处理
      xhr.onload = function() {
        if (xhr.status === 200) {
          // 创建一个Blob URL并触发下载
          const blob = xhr.response;
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', fileName);
          document.body.appendChild(link);
          link.click();
          
          // 清理
          setTimeout(() => {
            window.URL.revokeObjectURL(url);
            document.body.removeChild(link);
          }, 100);
        } else {
          throw new Error(`下载失败: HTTP状态 ${xhr.status}`);
        }
      };
      
      // 错误处理
      xhr.onerror = function() {
        console.error('XHR错误:', xhr.statusText);
        throw new Error(`网络错误，无法下载文件，可能是CORS或服务器问题`);
      };
      
      // 开始下载
      xhr.send();
    } catch (error) {
      console.error('下载附件失败:', error);
      // 使用 onClose 关闭弹窗，并通过错误信息传递到父组件，如果有的话
      if (onClose) {
        onClose();
      }
      // 显示通用错误提示
      alert(`下载附件失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };
  
  return (
    <Dialog
      open={open}
      onClose={onClose}
      TransitionComponent={Transition}
      fullWidth
      maxWidth="sm"
      PaperProps={{
        sx: {
          borderTop: `4px solid ${typeColor}`,
          borderRadius: '4px',
          overflow: 'hidden'
        }
      }}
    >
      <DialogTitle sx={{ 
        pb: 1, 
        display: 'flex', 
        justifyContent: 'space-between',
        alignItems: 'center',
        borderBottom: `1px solid ${theme.palette.divider}`
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="h6" sx={{ mr: 1 }}>
            {cleanTitle || '医疗记录详情'}
          </Typography>
          
          {/* 重要标记 */}
          {record.is_important && (
            <Tooltip title="重要记录">
              <StarIcon sx={{ color: '#F56565', ml: 0.5 }} />
            </Tooltip>
          )}
          
          {/* 私密标记 */}
          {record.is_private && (
            <Tooltip title="私密记录">
              <LockIcon sx={{ color: '#718096', ml: 0.5 }} />
            </Tooltip>
          )}
        </Box>
        
        <IconButton aria-label="关闭" onClick={onClose} size="small">
          <ClearIcon />
        </IconButton>
      </DialogTitle>
      
      <DialogContent sx={{ pt: 2 }}>
        {/* 记录日期和时间 */}
        <Box sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
          <CalendarIcon sx={{ mr: 1, color: 'text.secondary', fontSize: '1rem' }} />
          <Typography variant="body2" color="text.secondary">
            {formatChineseDate(recordDateObj)}
          </Typography>
        </Box>
        
        {/* 标签区域 */}
        <Box sx={{ mb: 2, display: 'flex', flexWrap: 'wrap', gap: 0.8 }}>
          {/* 记录类型标签 - 显示所有类型，每个标签使用对应的颜色 */}
          {recordTypeArray.map((type, index) => {
            // 为每个标签单独获取对应的颜色
            const tagColor = getTypeColor(type);
            return (
              <Chip 
                key={`type-${index}`}
                size="small"
                label={getTypeName(type)}
                sx={{ 
                  backgroundColor: `${tagColor}20`, // 20%透明度的背景色
                  color: tagColor,
                  fontWeight: index === 0 ? 600 : 400 // 第一个标签仍然加粗显示
                }}
              />
            );
          })}
          
          {/* 严重程度标签 */}
          {record.severity && (
            <Chip
              size="small"
              label={getSeverityName(record.severity)}
              sx={{ 
                backgroundColor: getSeverityColor(record.severity).bg,
                color: getSeverityColor(record.severity).text,
              }}
            />
          )}
          
          {/* 其他标签 */}
          {record.tags && record.tags.map((tag, index) => (
            <Chip
              key={index}
              size="small"
              label={tag}
              sx={{ 
                backgroundColor: theme.palette.action.hover,
                color: theme.palette.text.primary,
              }}
            />
          ))}
        </Box>
        
        {/* 记录内容 */}
        <Box sx={{ mt: 2 }}>
          <Divider sx={{ mb: 2 }} />
          <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
            {cleanContent || record.description || '暂无详细内容'}
          </Typography>
        </Box>
        
        {/* 记录详情区域 - 从 /records/id 获取的数据 */}
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>
            <CircularProgress size={24} />
            <Typography variant="body2" sx={{ ml: 1, color: 'text.secondary' }}>
              正在加载记录详情...
            </Typography>
          </Box>
        ) : recordDetail && recordDetail.completeInfo && (
          <Box sx={{ mt: 3 }}>
            <Divider sx={{ mb: 2 }} />
            <Typography variant="subtitle2" sx={{ mb: 1 }}>
              详细诊断信息
            </Typography>
            
            {/* 诊断项目 */}
            {recordDetail.completeInfo.diagnosisItems && recordDetail.completeInfo.diagnosisItems.length > 0 && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" sx={{ fontWeight: 'medium', mb: 0.5 }}>
                  诊断:
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.8 }}>
                  {recordDetail.completeInfo.diagnosisItems.map((item: string, index: number) => (
                    <Chip
                      key={index}
                      size="small"
                      label={item}
                      sx={{ 
                        backgroundColor: `${theme.palette.primary.main}20`,
                        color: theme.palette.primary.main
                      }}
                    />
                  ))}
                </Box>
              </Box>
            )}
            
            {/* 检验结果 */}
            {recordDetail.completeInfo.labResults && recordDetail.completeInfo.labResults.length > 0 && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" sx={{ fontWeight: 'medium', mb: 0.5 }}>
                  检验结果:
                </Typography>
                <Box sx={{ 
                  border: '1px solid', 
                  borderColor: theme.palette.divider,
                  borderRadius: 1,
                  overflow: 'hidden'
                }}>
                  {recordDetail.completeInfo.labResults.map((item: any, index: number) => (
                    <Box 
                      key={index} 
                      sx={{ 
                        p: 1,
                        display: 'flex',
                        justifyContent: 'space-between',
                        backgroundColor: item.abnormal ? alpha('#FFF5F5', 0.5) : 'transparent',
                        borderBottom: index < recordDetail.completeInfo.labResults.length-1 ? 
                          `1px solid ${theme.palette.divider}` : 'none'
                      }}
                    >
                      <Typography variant="body2">{item.name}</Typography>
                      <Typography 
                        variant="body2" 
                        sx={{ 
                          fontWeight: item.abnormal ? 'medium' : 'normal',
                          color: item.abnormal ? theme.palette.error.main : 'inherit'
                        }}
                      >
                        {item.value}
                        {item.reference && <span style={{ color: theme.palette.text.secondary, marginLeft: 8, fontSize: '0.85em' }}>
                          (参考值: {item.reference})
                        </span>}
                      </Typography>
                    </Box>
                  ))}
                </Box>
              </Box>
            )}
            
            {/* 治疗方案 */}
            {recordDetail.completeInfo.treatment && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" sx={{ fontWeight: 'medium', mb: 0.5 }}>
                  治疗方案:
                </Typography>
                <Typography variant="body2" sx={{ 
                  p: 1, 
                  backgroundColor: alpha(theme.palette.background.default, 0.5),
                  border: `1px solid ${theme.palette.divider}`,
                  borderRadius: 1
                }}>
                  {recordDetail.completeInfo.treatment}
                </Typography>
              </Box>
            )}
            
            {/* 医生备注 */}
            {recordDetail.completeInfo.doctorNotes && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" sx={{ fontWeight: 'medium', mb: 0.5 }}>
                  医生备注:
                </Typography>
                <Typography variant="body2" sx={{ 
                  p: 1, 
                  backgroundColor: alpha(theme.palette.primary.main, 0.05),
                  border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                  borderRadius: 1,
                  whiteSpace: 'pre-wrap'
                }}>
                  {recordDetail.completeInfo.doctorNotes}
                </Typography>
              </Box>
            )}
          </Box>
        )}
        
        {/* 附件区域 - 只在有附件或正在加载时显示 */}
        {(loading || (attachments && attachments.length > 0)) && (
          <Box sx={{ mt: 3 }}>
            <Divider sx={{ mb: 2 }} />
            <Typography variant="subtitle2" sx={{ mb: 1, display: 'flex', alignItems: 'center' }}>
              <AttachmentIcon sx={{ mr: 0.5, fontSize: '1rem' }} />
              附件 {attachments && attachments.length > 0 ? `(${attachments.length})` : ''}
            </Typography>
            
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>
                <CircularProgress size={24} />
                <Typography variant="body2" sx={{ ml: 1, color: 'text.secondary' }}>
                  正在加载附件...
                </Typography>
              </Box>
            ) : (
              <List dense sx={{ bgcolor: 'background.paper', border: '1px solid #e0e0e0', borderRadius: 1 }}>
                {attachments.map((attachment: any, index: number) => (
                  <ListItem 
                    key={attachment.id || index}
                    disablePadding
                    secondaryAction={
                      <Box sx={{ display: 'flex' }}>
                        {isPreviewable(attachment) && (
                          <Tooltip title="预览">
                            <IconButton 
                              edge="end" 
                              aria-label="预览"
                              onClick={() => openPreview(attachment.id, attachment.fileName || attachment.file_name || '未知文件')}
                              size="small"
                              sx={{ mr: 0.5 }}
                            >
                              <ZoomInIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        )}
                        <Tooltip title="下载">
                          <IconButton 
                            edge="end" 
                            aria-label="下载" 
                            onClick={() => handleDownloadAttachment(attachment.id, attachment.fileName || attachment.file_name || '未知文件')}
                            size="small"
                          >
                            <DownloadIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    }
                  >
                    <ListItemButton
                      onClick={() => isPreviewable(attachment) 
                        ? openPreview(attachment.id, attachment.fileName || attachment.file_name || '未知文件')
                        : handleDownloadAttachment(attachment.id, attachment.fileName || attachment.file_name || '未知文件')
                      }
                      dense
                    >
                      <ListItemIcon>
                        {getAttachmentIcon(attachment.fileName || attachment.file_name)}
                      </ListItemIcon>
                      <ListItemText 
                        primary={attachment.fileName || attachment.file_name || '未知文件'} 
                        secondary={formatFileSize(attachment.fileSize || attachment.file_size)}
                        primaryTypographyProps={{ style: { fontSize: '0.85rem' } }}
                        secondaryTypographyProps={{ style: { fontSize: '0.75rem' } }}
                      />
                    </ListItemButton>
                  </ListItem>
                ))}
              </List>
            )}
          </Box>
        )}
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose} color="primary">
          关闭
        </Button>
      </DialogActions>
      
      {/* 图片预览弹窗 */}
      <Modal
        open={previewOpen}
        onClose={closePreview}
        aria-labelledby="图片预览"
        aria-describedby="查看附件图片"
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          p: 2
        }}
      >
        <Box sx={{
          position: 'relative',
          maxWidth: '95%',
          maxHeight: '95%',
          bgcolor: 'background.paper',
          boxShadow: 24,
          borderRadius: 1,
          overflow: 'hidden',
          p: 0
        }}>
          {/* 预览标题 */}
          <Box sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            p: 1,
            pl: 2,
            bgcolor: 'primary.main',
            color: 'white'
          }}>
            <Typography variant="subtitle1" component="div" sx={{ 
              fontSize: '0.95rem',
              fontWeight: 500,
              maxWidth: '80%',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}>
              {previewTitle}
            </Typography>
            <IconButton 
              onClick={closePreview}
              size="small"
              sx={{ color: 'white' }}
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          </Box>
          
          {/* 图片容器 */}
          <Box sx={{
            overflow: 'auto',
            maxHeight: '80vh',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            p: 2
          }}>
            <img 
              src={previewUrl} 
              alt={previewTitle}
              style={{
                maxWidth: '100%',
                maxHeight: '100%',
                objectFit: 'contain'
              }}
            />
          </Box>
        </Box>
      </Modal>
    </Dialog>
  );
};

// 组件属性接口
export interface EnhancedMedicalRecordTimelineProps {
  records: MedicalRecord[];
  onViewRecord?: (recordId: string) => void;
  headerTitle?: string;
  isLoading?: boolean;
  hasMore?: boolean;
  onAddRecord?: () => void;
}

// 病程阶段颜色映射
const STAGE_PERIOD_COLORS: Record<string, string> = {
  'INITIAL': '#E53935', // 初诊期 - 红色
  'DIAGNOSIS': '#8E24AA', // 确诊期 - 紫色
  'TREATMENT': '#FB8C00', // 治疗期 - 橙色
  'RECOVERY': '#FDD835', // 恢复期 - 黄色
  'PROGNOSIS': '#43A047', // 预后期 - 绿色
  'ARCHIVE': '#757575', // 归档期 - 灰色
  'DEFAULT': '#757575', // 默认 - 灰色
};

// 中文病程阶段名称映射
const STAGE_PERIOD_NAMES: Record<string, string> = {
  'INITIAL': '初诊期',
  'DIAGNOSIS': '确诊期',
  'TREATMENT': '治疗期',
  'RECOVERY': '恢复期', 
  'PROGNOSIS': '预后期',
  'ARCHIVE': '归档期',
  'DEFAULT': '未知阶段',
};

// 节点到阶段的正确映射 (针对StageNode -> 显示的阶段文本)
const NODE_TO_PHASE_MAP: Record<string, string> = {
  'INITIAL_VISIT': 'INITIAL',    // 初诊节点 -> 初诊期 - 红色
  'DIAGNOSIS': 'DIAGNOSIS',      // 确诊节点 -> 确诊期 - 紫色
  'TREATMENT': 'TREATMENT',      // 治疗节点 -> 治疗期 - 橙色
  'FOLLOW_UP': 'RECOVERY',       // 随访节点 -> 恢复期 - 黄色
  'PROGNOSIS': 'PROGNOSIS',      // 预后节点 -> 预后期 - 绿色
  'ARCHIVE': 'ARCHIVE'           // 归档节点 -> 归档期 - 灰色
};

// 记录类型颜色映射
const TYPE_COLORS: Record<string, string> = {
  [RecordTypeEnum.SYMPTOM]: '#ECC94B', // 黄色
  [RecordTypeEnum.DIAGNOSIS]: '#9F7AEA', // 紫色
  [RecordTypeEnum.AUX_DIAGNOSIS]: '#9F7AEA', // 紫色
  [RecordTypeEnum.EXAMINATION]: '#4299E1', // 蓝色
  [RecordTypeEnum.LAB_TEST]: '#38B2AC', // 青色
  [RecordTypeEnum.TREATMENT]: '#F56565', // 红色
  [RecordTypeEnum.MEDICATION]: '#ED8936', // 橙色
  [RecordTypeEnum.SURGERY]: '#E53E3E', // 深红色
  [RecordTypeEnum.PHYSICAL_THERAPY]: '#DD6B20', // 橙红色
  [RecordTypeEnum.FOLLOW_UP]: '#48BB78', // 绿色
  [RecordTypeEnum.REVISIT]: '#38A169', // 深绿色
  [RecordTypeEnum.OTHER]: '#A0AEC0' // 灰色
};

// 严重程度颜色映射
const SEVERITY_COLORS = {
  'MILD': { bg: '#E6FFFA', text: '#38B2AC' },  // 轻微 - 绿色
  'MODERATE': { bg: '#FFFFF0', text: '#D69E2E' }, // 中等 - 黄色
  'SEVERE': { bg: '#FFFAF0', text: '#DD6B20' },  // 严重 - 橙色
  'CRITICAL': { bg: '#FFF5F5', text: '#E53E3E' }  // 危重 - 红色
};

/**
 * 增强型医疗记录时间轴组件
 * 使用虚拟滚动技术高效展示大量医疗记录
 */
const EnhancedMedicalRecordTimeline: React.FC<EnhancedMedicalRecordTimelineProps> = ({
  records,
  onViewRecord,
  headerTitle = '目前处于：恢复期',  // 默认显示"恢复期"而非"康复期"
  isLoading = false,
  hasMore = false,
  onAddRecord
}) => {
  const theme = useTheme();
  const timelineRef = useRef<HTMLDivElement>(null);
  const [expandedGroups, setExpandedGroups] = useState<{ [key: string]: boolean }>({});
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [currentVisibleDate, setCurrentVisibleDate] = useState<string>('');
  
  // 弹窗相关状态
  const [dialogOpen, setDialogOpen] = useState<boolean>(false);
  const [selectedRecord, setSelectedRecord] = useState<MedicalRecord | null>(null);
  
  // 时间轴筛选相关状态
  const [showTimeIndex, setShowTimeIndex] = useState<boolean>(true);
  const [activeYear, setActiveYear] = useState<number | null>(null);
  const [activeMonth, setActiveMonth] = useState<number | null>(null);
  const [allCollapsed, setAllCollapsed] = useState<boolean>(false);
  
  // 获取记录日期
  const getRecordDate = useCallback((record: MedicalRecord): Date => {
    const dateStr = record.recordDate || record.created_at || '';
    try {
      const date = parseISO(dateStr);
      if (isValid(date)) {
        return date;
      }
    } catch (e) {}
    return new Date();
  }, []);
  
  // 根据record属性判断当前处于的阶段
  const findCurrentStage = useCallback((recordsToSearch: MedicalRecord[]): string | null => {
    if (!recordsToSearch || recordsToSearch.length === 0) return null;
    
    // 先按日期排序（从晚到早）
    const sortedRecords = [...recordsToSearch].sort((a, b) => {
      const dateA = getRecordDate(a);
      const dateB = getRecordDate(b);
      return dateB.getTime() - dateA.getTime(); // 降序排序
    });

    console.log('排序后的记录:', sortedRecords.slice(0, 3).map(r => ({
      id: r.id,
      title: r.title,
      stageNode: r.stageNode || r.stage_node,
      stagePhase: r.stagePhase || r.stage_phase,
      date: getRecordDate(r)
    })));
    
    // 1. 首先尝试寻找最新的带有stageNode的记录
    for (const record of sortedRecords) {
      // 处理 stageNode 字段
      const nodeValue = record.stageNode || record.stage_node;
      
      if (nodeValue) {
        // 标准化为大写
        const normalizedNode = typeof nodeValue === 'string' ? nodeValue.toUpperCase() : nodeValue;
        console.log(`找到节点: ${normalizedNode}`);
        
        // 直接映射特定节点到阶段
        if (normalizedNode === 'INITIAL_VISIT') {
          console.log('映射到初诊期');
          return 'INITIAL';
        } else if (normalizedNode === 'DIAGNOSIS') {
          console.log('映射到确诊期');
          return 'DIAGNOSIS';
        } else if (normalizedNode === 'TREATMENT') {
          console.log('映射到治疗期');
          return 'TREATMENT';
        } else if (normalizedNode === 'FOLLOW_UP') {
          console.log('映射到恢复期');
          return 'RECOVERY';
        } else if (normalizedNode === 'PROGNOSIS') {
          console.log('映射到预后期');
          return 'PROGNOSIS';
        } else if (normalizedNode === 'ARCHIVE') {
          console.log('映射到归档期');
          return 'ARCHIVE';
        }
        
        // 尝试使用映射表
        if (NODE_TO_PHASE_MAP[normalizedNode]) {
          const mappedPhase = NODE_TO_PHASE_MAP[normalizedNode];
          console.log(`通过映射表映射到: ${mappedPhase}`);
          return mappedPhase;
        }
      }
    }
    
    // 2. 如果没有找到stageNode，则尝试从stagePhase字段直接获取
    for (const record of sortedRecords) {
      // 处理 stagePhase 字段
      const phaseValue = record.stagePhase || record.stage_phase;
      
      if (phaseValue) {
        // 标准化为大写
        const normalizedPhase = typeof phaseValue === 'string' ? phaseValue.toUpperCase() : phaseValue;
        console.log(`找到阶段: ${normalizedPhase}`);
        
        // 直接匹配阶段值
        if (['INITIAL', 'DIAGNOSIS', 'TREATMENT', 'RECOVERY', 'PROGNOSIS', 'ARCHIVE'].includes(normalizedPhase)) {
          console.log(`直接返回阶段: ${normalizedPhase}`);
          return normalizedPhase;
        }
      }
    }
    
    console.log('未找到任何带有节点或阶段标记的记录，返回默认阶段: INITIAL');
    return 'INITIAL'; // 如果没有任何阶段信息，默认返回初诊期而不是null
  }, [getRecordDate]);
  
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const displayHeaderTitle = useMemo(() => {
    const prefix = '目前处于：';
    
    // 优先从记录中推断阶段
    if (records && records.length > 0) {
      // 调用findCurrentStage函数获取当前阶段
      const currentStage = findCurrentStage(records);
      if (currentStage && STAGE_PERIOD_NAMES[currentStage]) {
        const stageName = STAGE_PERIOD_NAMES[currentStage];
        console.log('从记录推断显示阶段:', currentStage, stageName);
        return `${prefix}${stageName}`;
      }
    }
    
    // 如果无法从记录中推断，则检查标题
    if (headerTitle) {
      // 检查是否包含"康复期"，如果是则替换为"恢复期"以保持一致性
      if (headerTitle.includes('康复期')) {
        console.log('将"康复期"统一显示为"恢复期"');
        return `${prefix}恢复期`;
      }
      
      // 如果标题已经包含前缀，直接使用
      if (headerTitle.startsWith(prefix)) {
        return headerTitle;
      }
      
      // 否则添加前缀
      return `${prefix}${headerTitle}`;
    }
    
    // 默认情况
    return `${prefix}未知阶段`;
  }, [records, headerTitle, findCurrentStage]);
  
  // 格式化时间显示
  const formatTime = (timestamp: string) => {
    try {
      const date = parseISO(timestamp);
      if (isValid(date)) {
        return format(date, 'HH:mm', { locale: zhCN });
      }
    } catch (e) {}
    return '';
  };
  
  // 格式化日期显示为中文格式
  const formatChineseDate = (date: Date) => {
    if (!isValid(date)) return '';
    return format(date, 'yyyy年MM月dd日 EEEE', { locale: zhCN });
  };
  
  // 获取记录类型
  const getRecordType = (record: MedicalRecord): RecordTypeEnum => {
    if (!record.recordType) return RecordTypeEnum.OTHER;
    
    if (typeof record.recordType === 'string') {
      // 尝试直接匹配系统枚举
      const upperType = record.recordType.toUpperCase();
      if (RecordTypeEnum[upperType as keyof typeof RecordTypeEnum]) {
        return RecordTypeEnum[upperType as keyof typeof RecordTypeEnum];
      }
    }
    
    return record.recordType as RecordTypeEnum || RecordTypeEnum.OTHER;
  };
  
  // 获取记录类型颜色
  const getTypeColor = (type: RecordTypeEnum | string): string => {
    if (typeof type === 'string') {
      // 尝试直接查找颜色
      if (TYPE_COLORS[type]) {
        return TYPE_COLORS[type];
      }
      
      // 尝试转换为枚举后查找
      const upperType = type.toUpperCase();
      if (RecordTypeEnum[upperType as keyof typeof RecordTypeEnum]) {
        const enumValue = RecordTypeEnum[upperType as keyof typeof RecordTypeEnum];
        return TYPE_COLORS[enumValue] || '#A0AEC0';
      }
    }
    
    return TYPE_COLORS[type as string] || '#A0AEC0';
  };
  
  // 获取记录类型名称
  const getTypeName = (type: RecordTypeEnum | string): string => {
    if (typeof type === 'string') {
      // 尝试直接匹配系统枚举
      const enumValue = Object.values(RecordTypeEnum).find(val => val === type);
      if (enumValue) {
        return RecordTypeNames[enumValue] || '其他';
      }
      
      // 尝试转换为枚举后查找
      const upperType = type.toUpperCase();
      if (RecordTypeEnum[upperType as keyof typeof RecordTypeEnum]) {
        return RecordTypeNames[RecordTypeEnum[upperType as keyof typeof RecordTypeEnum]] || '其他';
      }
      
      return type;
    }
    
    return RecordTypeNames[type] || '其他';
  };
  
  // 获取记录严重程度
  const getSeverityName = (severity: SeverityEnum | string | undefined): string => {
    if (!severity) return '普通';
    
    if (typeof severity === 'string') {
      // 尝试匹配严重程度枚举
      const upperSeverity = severity.toUpperCase();
      if (SeverityEnum[upperSeverity as keyof typeof SeverityEnum]) {
        return SeverityNames[SeverityEnum[upperSeverity as keyof typeof SeverityEnum]];
      }
      
      return severity;
    }
    
    return SeverityNames[severity] || '普通';
  };
  
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const getStageColor = useMemo(() => {
    // 首先尝试从记录中推断阶段
    if (records && records.length > 0) {
      const currentStage = findCurrentStage(records);
      if (currentStage) {
        console.log('从记录推断阶段:', currentStage, '颜色:', STAGE_PERIOD_COLORS[currentStage]);
        return STAGE_PERIOD_COLORS[currentStage];
      }
    }
    
    // 如果没有记录或无法从记录推断，则使用标题中的信息
    if (headerTitle) {
      // 显示标题是"目前处于：确诊期"的情况
      if (headerTitle.includes('确诊期')) {
        console.log('标题显示确诊期，使用紫色:', STAGE_PERIOD_COLORS['DIAGNOSIS']);
        return STAGE_PERIOD_COLORS['DIAGNOSIS']; // 确诊期 - 紫色
      }
      
      // 显示标题是"目前处于：治疗期"的情况
      if (headerTitle.includes('治疗期')) {
        console.log('标题显示治疗期，使用橙色:', STAGE_PERIOD_COLORS['TREATMENT']);
        return STAGE_PERIOD_COLORS['TREATMENT']; // 治疗期 - 橙色
      }
      
      // 针对"恢复期"和"康复期"的特殊处理
      if (headerTitle.includes('恢复期') || headerTitle.includes('康复期')) {
        console.log('标题显示恢复期/康复期，使用黄色:', STAGE_PERIOD_COLORS['RECOVERY']);
        return STAGE_PERIOD_COLORS['RECOVERY']; // 恢复期 - 黄色
      }
      
      // 显示标题是"目前处于：初诊期"的情况
      if (headerTitle.includes('初诊期')) {
        console.log('标题显示初诊期，使用红色:', STAGE_PERIOD_COLORS['INITIAL']);
        return STAGE_PERIOD_COLORS['INITIAL']; // 初诊期 - 红色
      }
      
      // 显示标题是"目前处于：预后期"的情况
      if (headerTitle.includes('预后期')) {
        console.log('标题显示预后期，使用绿色:', STAGE_PERIOD_COLORS['PROGNOSIS']);
        return STAGE_PERIOD_COLORS['PROGNOSIS']; // 预后期 - 绿色
      }
      
      // 显示标题是"目前处于：归档期"的情况
      if (headerTitle.includes('归档期') || headerTitle.includes('封档期')) {
        console.log('标题显示归档期/封档期，使用灰色:', STAGE_PERIOD_COLORS['ARCHIVE']);
        return STAGE_PERIOD_COLORS['ARCHIVE']; // 归档期 - 灰色
      }
      
      // 通用情况：从headerTitle中提取阶段名称
      const matchResult = headerTitle.match(/目前处于：(.+?)(?:期|阶段|$)/);
      if (matchResult && matchResult[1]) {
        const extractedText = matchResult[1];
        
        // 检查提取的文本是否包含阶段名称
        for (const [key, value] of Object.entries(STAGE_PERIOD_NAMES)) {
          if (value.includes(extractedText) || extractedText.includes(value.replace('期', ''))) {
            console.log('匹配到阶段:', key, '颜色:', STAGE_PERIOD_COLORS[key]);
            return STAGE_PERIOD_COLORS[key];
          }
        }
      }
    }
    
    // 默认使用灰色
    console.log('无法确定阶段，使用默认灰色:', STAGE_PERIOD_COLORS['DEFAULT']);
    return STAGE_PERIOD_COLORS['DEFAULT'];
  }, [headerTitle, records, findCurrentStage]);
  
  // 获取严重程度颜色
  const getSeverityColor = (severity: SeverityEnum | string | undefined): { bg: string, text: string } => {
    if (!severity) return { bg: '#F7FAFC', text: '#718096' }; // 默认颜色
    
    if (typeof severity === 'string') {
      // 尝试匹配严重程度枚举
      const upperSeverity = severity.toUpperCase();
      if (SeverityEnum[upperSeverity as keyof typeof SeverityEnum]) {
        return SEVERITY_COLORS[upperSeverity as keyof typeof SEVERITY_COLORS] || { bg: '#F7FAFC', text: '#718096' };
      }
    }
    
    // 直接用枚举值匹配
    return SEVERITY_COLORS[severity as keyof typeof SEVERITY_COLORS] || { bg: '#F7FAFC', text: '#718096' };
  };
  
  // 按日期分组数据
  const groupedRecords = useMemo(() => {
    // 过滤搜索条件
    let filteredRecords = searchTerm.trim()
      ? records.filter(record => 
          record.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          record.content?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          record.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (record.tags && record.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())))
        )
      : records;
    
    // 应用年度/月度筛选
    if (activeYear !== null) {
      filteredRecords = filteredRecords.filter(record => {
        const date = getRecordDate(record);
        if (activeMonth !== null) {
          // 筛选特定年月的记录
          return getYear(date) === activeYear && getMonth(date) === activeMonth;
        } else {
          // 只筛选年份
          return getYear(date) === activeYear;
        }
      });
    }
    
    // 按日期分组
    const groups: { [key: string]: MedicalRecord[] } = {};
    
    filteredRecords.forEach(record => {
      const date = getRecordDate(record);
      const dateKey = format(date, 'yyyy-MM-dd');
      
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      
      groups[dateKey].push(record);
    });
    
    // 转换为数组并排序
    return Object.entries(groups)
      .map(([dateKey, groupRecords]) => {
        const date = parseISO(dateKey);
        return {
          key: dateKey,
          date,
          title: formatChineseDate(date),
          records: groupRecords.sort((a, b) => {
            return getRecordDate(b).getTime() - getRecordDate(a).getTime();
          })
        };
      })
      .sort((a, b) => b.date.getTime() - a.date.getTime());
  }, [records, searchTerm, activeYear, activeMonth, getRecordDate]);
  
  // 获取所有年份和月份数据
  const yearMonthData = useMemo(() => {
    const years: { year: number; hasRecords: boolean; months: { month: number; hasRecords: boolean }[] }[] = [];
    
    if (records.length === 0) return years;
    
    // 遍历所有记录，收集年份和月份信息
    records.forEach(record => {
      const date = getRecordDate(record);
      const year = getYear(date);
      const month = getMonth(date);
      
      // 查找年份是否已存在
      let yearItem = years.find(y => y.year === year);
      if (!yearItem) {
        // 创建新的年份项
        yearItem = {
          year,
          hasRecords: true,
          months: Array.from({ length: 12 }, (_, i) => ({ month: i, hasRecords: false }))
        };
        years.push(yearItem);
      }
      
      // 标记该月有记录
      yearItem.months[month].hasRecords = true;
    });
    
    // 按年份降序排序
    return years.sort((a, b) => b.year - a.year);
  }, [records, getRecordDate]);
  
  // 搜索处理
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    // 重置年月筛选
    setActiveYear(null);
    setActiveMonth(null);
  };
  
  // 清除搜索
  const clearSearch = () => {
    setSearchTerm('');
    // 重置年月筛选
    setActiveYear(null);
    setActiveMonth(null);
  };
  
  // 处理年份选择
  const handleYearClick = (year: number) => {
    if (activeYear === year && activeMonth === null) {
      // 如果点击已选中的年份且没有选中月份，则取消选择
      setActiveYear(null);
    } else {
      // 否则选中该年份
      setActiveYear(year);
      setActiveMonth(null);
    }
  };
  
  // 处理月份选择
  const handleMonthClick = (year: number, month: number) => {
    if (activeYear === year && activeMonth === month) {
      // 如果点击已选中的月份，则只选中年份
      setActiveMonth(null);
    } else {
      // 否则选中该年份和月份
      setActiveYear(year);
      setActiveMonth(month);
    }
  };
  
  // 切换组展开/折叠
  const toggleGroup = (groupKey: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [groupKey]: !prev[groupKey]
    }));
  };
  
  // 处理记录查看
  const handleViewRecord = (recordId: string) => {
    console.log('handleViewRecord被调用，recordId:', recordId);
    
    // 查找选中的记录
    const record = records.find(r => r.id === recordId);
    if (record) {
      console.log('找到记录:', record.title || record.id);
      
      // 检查记录中是否直接包含附件信息
      if (record.attachments && Array.isArray(record.attachments) && record.attachments.length > 0) {
        console.log('记录中包含附件信息，数量:', record.attachments.length);
      } else if (record.hasAttachments) {
        console.log('记录标记有附件，但需要从API获取');
      }
      
      try {
        // 使用原始记录创建一个深拷贝，避免共享引用可能导致的问题
        const recordCopy = JSON.parse(JSON.stringify(record));
        
        // 打开弹窗并设置选中的记录
        setSelectedRecord(recordCopy);
        setDialogOpen(true);
        console.log('Dialog状态设置为打开');
      } catch (e) {
        console.error('处理记录数据错误:', e);
        // 出错时仍然使用原始记录
        setSelectedRecord(record);
        setDialogOpen(true);
      }
    } else {
      console.error('未找到ID为', recordId, '的记录');
    }
    
    // 同时调用外部的onViewRecord回调（如果有）
    if (onViewRecord) {
      onViewRecord(recordId);
    }
  };
  
  // 关闭记录详情弹窗
  const handleCloseDialog = () => {
    setDialogOpen(false);
    console.log('Dialog状态设置为关闭');
  };
  
  // 切换时间索引显示
  const toggleTimeIndex = () => {
    setShowTimeIndex(prev => !prev);
  };
  
  // 折叠所有组
  const collapseAllGroups = () => {
    // 检查当前是否所有组都已折叠
    const allAlreadyCollapsed = Object.values(expandedGroups).every(expanded => !expanded);
    
    // 如果所有组都已折叠，则全部展开
    if (allAlreadyCollapsed) {
      const expandedState: Record<string, boolean> = {};
      groupedRecords.forEach(group => {
        expandedState[group.key] = true;
      });
      setExpandedGroups(expandedState);
      setAllCollapsed(false);
    } else {
      // 否则全部折叠
      const collapsedState: Record<string, boolean> = {};
      groupedRecords.forEach(group => {
        collapsedState[group.key] = false;
      });
      setExpandedGroups(collapsedState);
      setAllCollapsed(true);
    }
  };
  
  // 监听滚动
  const handleScroll = useCallback(() => {
    if (!timelineRef.current) return;
    
    const { scrollTop } = timelineRef.current;
    setShowScrollTop(scrollTop > 300);
    
    // 简单检测当前可见日期组（实际实现可能需要更复杂的逻辑）
    if (groupedRecords.length > 0) {
      // 这里采用简化的实现，只显示第一个组的日期
      const visibleGroupIndex = Math.min(
        Math.floor(scrollTop / 120), // 假设每组平均高度为120px
        groupedRecords.length - 1
      );
      
      if (groupedRecords[visibleGroupIndex]) {
        setCurrentVisibleDate(groupedRecords[visibleGroupIndex].title);
      }
    }
  }, [groupedRecords]);
  
  // 设置滚动事件监听
  useEffect(() => {
    const timeline = timelineRef.current;
    if (timeline) {
      timeline.addEventListener('scroll', handleScroll);
      return () => {
        timeline.removeEventListener('scroll', handleScroll);
      };
    }
  }, [handleScroll]);
  
  // 初始化所有组展开
  useEffect(() => {
    const initialExpanded: Record<string, boolean> = {};
    groupedRecords.forEach(group => {
      initialExpanded[group.key] = true;
    });
    setExpandedGroups(initialExpanded);
  }, [groupedRecords]);
  
  // 滚动到顶部
  const scrollToTop = () => {
    if (timelineRef.current) {
      timelineRef.current.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }
  };
  
  // 计算月份名称
  const getMonthName = (month: number): string => {
    // 确保按照正确顺序显示月份名称（1月到12月）
    const monthNames = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'];
    return monthNames[month] || `${month + 1}月`;
  };
  
  // 当任何组被手动展开/折叠时更新allCollapsed状态
  useEffect(() => {
    const allAreCollapsed = Object.values(expandedGroups).every(expanded => !expanded);
    setAllCollapsed(allAreCollapsed);
  }, [expandedGroups]);
  
  // 获取记录所有中文标签
  const getRecordTags = (record: MedicalRecord): {label: string; color: string}[] => {
    const tags: {label: string; color: string}[] = [];
    
    // 1. 处理主标签 (recordType)
    if (record.recordType) {
      // 如果recordType是JSON数组字符串，直接尝试解析
      if (typeof record.recordType === 'string' && record.recordType.startsWith('[') && record.recordType.endsWith(']')) {
        try {
          // 解析JSON数组
          const typeArray: string[] = JSON.parse(record.recordType);
          
          // 将每个类型转换为中文标签
          typeArray.forEach(type => {
            if (!type) return;
            
            const tagName = getTypeName(type);
            const tagColor = getTypeColor(type);
            
            tags.push({
              label: tagName,
              color: tagColor
            });
          });
        } catch (err) {
          console.warn('解析recordType JSON失败:', record.recordType);
          // 解析失败时作为单个标签处理
          const tagName = getTypeName(record.recordType);
          const tagColor = getTypeColor(record.recordType);
          
          tags.push({
            label: tagName,
            color: tagColor
          });
        }
      } 
      // 如果recordType是数组，直接处理
      else if (Array.isArray(record.recordType)) {
        record.recordType.forEach(type => {
          if (!type) return;
          
          const tagName = getTypeName(type);
          const tagColor = getTypeColor(type);
          
          tags.push({
            label: tagName,
            color: tagColor
          });
        });
      } 
      // recordType是单个字符串
      else {
        const tagName = getTypeName(record.recordType);
        const tagColor = getTypeColor(record.recordType);
        
        tags.push({
          label: tagName,
          color: tagColor
        });
      }
    } else {
      // 默认标签
      tags.push({
        label: '其他',
        color: '#A0AEC0'
      });
    }
    
    // 2. 处理严重程度标签
    if (record.severity) {
      const severityName = getSeverityName(record.severity);
      tags.push({
        label: severityName,
        color: '#F6AD55' // 警告色
      });
    }
    
    // 3. 不再添加阶段节点标签，因为它已经显示在标题后面
    // 移除之前处理阶段节点标签的代码
    
    // 4. 其他标签可以按需添加
    
    return tags;
  };
  
  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', position: 'relative' }}>
      <Box sx={{ 
        p: 1, 
        backgroundColor: alpha(getStageColor || STAGE_PERIOD_COLORS['DEFAULT'], 0.08),
        borderBottom: `1px solid ${alpha(getStageColor || STAGE_PERIOD_COLORS['DEFAULT'], 0.2)}`,
        borderRadius: '4px 4px 0 0'
      }}>
        {/* 标题和控制区 */}
        <Box sx={{ display: 'flex', flexDirection: 'column' }}>
          {/* 第一行：标题 */}
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.7 }}>
            {/* 标题 - 使用计算的displayHeaderTitle而不是原始headerTitle */}
            <Typography variant="subtitle1" 
              sx={{ 
                color: getStageColor || STAGE_PERIOD_COLORS['DEFAULT'],
                fontWeight: 'bold',
                display: 'flex',
                alignItems: 'center',
                fontSize: '0.85rem' // 减小字体大小
              }}
            >
              <HistoryIcon sx={{ mr: 0.5, fontSize: '0.9rem' }} />
              {displayHeaderTitle}
            </Typography>
          </Box>
          
          {/* 搜索框 */}
          <TextField
            fullWidth
            placeholder="搜索记录标题、内容或标签..."
            variant="outlined"
            size="small"
            value={searchTerm}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon fontSize="small" color="action" />
                </InputAdornment>
              ),
              endAdornment: searchTerm ? (
                <InputAdornment position="end">
                  <IconButton 
                    size="small" 
                    onClick={clearSearch}
                    edge="end"
                    aria-label="清除搜索"
                  >
                    <ClearIcon fontSize="small" />
                  </IconButton>
                </InputAdornment>
              ) : null
            }}
            sx={{
              mb: 1,
              '& .MuiOutlinedInput-root': {
                borderRadius: 2,
              }
            }}
          />
          
          {/* 操作按钮和筛选结果 */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.8 }}>
            {/* 筛选结果计数器 */}
            <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.6rem' }}>
              {searchTerm || activeYear !== null ? 
                `找到 ${groupedRecords.reduce((sum, group) => sum + group.records.length, 0)} 条匹配记录` : 
                `共 ${records.length} 条记录`
              }
            </Typography>
            
            {/* 操作按钮 */}
            <Box sx={{ display: 'flex' }}>
              <Button
                size="small"
                variant="text"
                onClick={collapseAllGroups}
                startIcon={
                  allCollapsed ? <FoldAllIcon /> : <UnfoldAllIcon />
                }
                sx={{ mr: 1, fontSize: '0.65rem', minWidth: 'auto', p: '3px 6px' }}
              >
                {allCollapsed ? '展开所有' : '收起所有'}
              </Button>
              
              <Button
                size="small"
                variant="text"
                onClick={toggleTimeIndex}
                startIcon={
                  showTimeIndex ? <TimelineIcon /> : <TimelineIcon />
                }
                sx={{ fontSize: '0.65rem', minWidth: 'auto', p: '3px 6px' }}
              >
                {showTimeIndex ? '隐藏索引' : '显示索引'}
              </Button>
            </Box>
          </Box>
        </Box>
      </Box>
      
      {/* 时间轴内容与时间索引 */}
      <Box sx={{ position: 'relative' }}>
        {/* 记录时间轴 */}
        <TimelineContainer ref={timelineRef} showTimeIndex={showTimeIndex}>
          {/* 垂直连接线 */}
          <TimelineConnector 
            sx={{ 
              background: (() => {
                // 提取所有具有阶段节点的记录
                const stageRecords = records
                  .filter(record => record.stageNode)
                  .sort((a, b) => {
                    const dateA = getRecordDate(a);
                    const dateB = getRecordDate(b);
                    return dateA.getTime() - dateB.getTime(); // 按时间升序排序
                  });
                
                // 如果有多个阶段节点，创建分段渐变色
                if (stageRecords.length > 1) {
                  // 获取各个阶段的颜色和时间点
                  const stagesWithColors = stageRecords.map(record => {
                    const upperNode = typeof record.stageNode === 'string' ? record.stageNode.toUpperCase() : '';
                    const phase = NODE_TO_PHASE_MAP[upperNode] || 'DEFAULT';
                    return {
                      color: STAGE_PERIOD_COLORS[phase],
                      date: getRecordDate(record).getTime()
                    };
                  });
                  
                  // 计算时间跨度
                  const startTime = stagesWithColors[0].date;
                  const endTime = stagesWithColors[stagesWithColors.length - 1].date;
                  const totalSpan = endTime - startTime;
                  
                  // 创建分段渐变
                  if (totalSpan > 0) {
                    const gradientStops = stagesWithColors.map((stage, index) => {
                      // 计算该阶段在总时间跨度中的百分比位置
                      const position = ((stage.date - startTime) / totalSpan) * 100;
                      // 为每个阶段创建两个颜色停止点，形成分明的颜色段
                      if (index === 0) {
                        // 第一个阶段
                        return `${stage.color} 0%, ${stage.color} ${Math.min(position + 5, 99)}%`;
                      } else if (index === stagesWithColors.length - 1) {
                        // 最后一个阶段
                        return `${stage.color} ${Math.max(position - 1, 1)}%, ${stage.color} 100%`;
                      } else {
                        // 中间阶段
                        return `${stage.color} ${Math.max(position - 1, 0)}%, ${stage.color} ${Math.min(position + 5, 100)}%`;
                      }
                    }).join(', ');
                    
                    return `linear-gradient(to bottom, ${gradientStops})`;
                  }
                }
                
                // 否则使用单一颜色
                return (getStageColor || STAGE_PERIOD_COLORS['DEFAULT']);
              })(),
              opacity: 0.7, // 调整透明度，使颜色更显眼
            }} 
          />
          
          {/* 记录内容 */}
          {groupedRecords.length > 0 ? (
            <Box sx={{ pl: 2, pr: 2, pb: 8, position: 'relative' }}>
              {groupedRecords.map((group) => {
                const isExpanded = expandedGroups[group.key] || false;
                
                return (
                  <Box key={group.key} sx={{ mb: 2 }}>
                    <Box 
                      sx={{ 
                        display: 'flex', 
                        alignItems: 'flex-start',
                        position: 'relative'
                      }}
                    >
                      {/* 时间节点和连接线 */}
                      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mr: 2, position: 'relative', ml: -1 }}>
                        <Box sx={{ pt: 1 }}>
                          <TimelineNode 
                            color={(() => {
                              // 获取当前组中的所有节点记录
                              const stageRecords = group.records
                                .filter(record => record.stageNode)
                                .sort((a, b) => {
                                  const dateA = getRecordDate(a);
                                  const dateB = getRecordDate(b);
                                  return dateA.getTime() - dateB.getTime(); // 按时间排序，最早的在前
                                });
                              
                              // 如果有节点记录，使用最早的节点的颜色
                              if (stageRecords.length > 0) {
                                const record = stageRecords[0]; // 使用该组中最早的节点记录
                                const upperNode = typeof record.stageNode === 'string' ? record.stageNode.toUpperCase() : '';
                                const phase = NODE_TO_PHASE_MAP[upperNode] || 'DEFAULT';
                                return STAGE_PERIOD_COLORS[phase];
                              }
                              
                              // 无节点记录时，根据记录的日期查找之前最近的节点
                              const groupDate = parseISO(group.key);
                              
                              // 查找所有比当前组日期早的节点记录
                              const earlierStageRecords = records
                                .filter(record => record.stageNode && getRecordDate(record) < groupDate)
                                .sort((a, b) => {
                                  // 按日期降序排序，找最近的
                                  return getRecordDate(b).getTime() - getRecordDate(a).getTime();
                                });
                              
                              // 使用最近的前置节点颜色
                              if (earlierStageRecords.length > 0) {
                                const record = earlierStageRecords[0];
                                const upperNode = typeof record.stageNode === 'string' ? record.stageNode.toUpperCase() : '';
                                const phase = NODE_TO_PHASE_MAP[upperNode] || 'DEFAULT';
                                return STAGE_PERIOD_COLORS[phase];
                              }
                              
                              // 如果没有前置节点，使用默认颜色
                              return getStageColor || STAGE_PERIOD_COLORS['DEFAULT'];
                            })()} 
                            active={true}
                            onClick={() => toggleGroup(group.key)}
                          />
                        </Box>
                        <Box 
                          sx={{ 
                            height: 'calc(100% - 8px)', 
                            width: 2, 
                            backgroundColor: (() => {
                              // 获取当前组中的所有节点记录
                              const stageRecords = group.records
                                .filter(record => record.stageNode)
                                .sort((a, b) => {
                                  const dateA = getRecordDate(a);
                                  const dateB = getRecordDate(b);
                                  return dateA.getTime() - dateB.getTime(); // 按时间排序，最早的在前
                                });
                              
                              // 如果有节点记录，使用最早的节点的颜色
                              if (stageRecords.length > 0) {
                                const record = stageRecords[0]; // 使用该组中最早的节点记录
                                const upperNode = typeof record.stageNode === 'string' ? record.stageNode.toUpperCase() : '';
                                const phase = NODE_TO_PHASE_MAP[upperNode] || 'DEFAULT';
                                return STAGE_PERIOD_COLORS[phase];
                              }
                              
                              // 无节点记录时，根据记录的日期查找之前最近的节点
                              const groupDate = parseISO(group.key);
                              
                              // 查找所有比当前组日期早的节点记录
                              const earlierStageRecords = records
                                .filter(record => record.stageNode && getRecordDate(record) < groupDate)
                                .sort((a, b) => {
                                  // 按日期降序排序，找最近的
                                  return getRecordDate(b).getTime() - getRecordDate(a).getTime();
                                });
                              
                              // 使用最近的前置节点颜色
                              if (earlierStageRecords.length > 0) {
                                const record = earlierStageRecords[0];
                                const upperNode = typeof record.stageNode === 'string' ? record.stageNode.toUpperCase() : '';
                                const phase = NODE_TO_PHASE_MAP[upperNode] || 'DEFAULT';
                                return STAGE_PERIOD_COLORS[phase];
                              }
                              
                              // 如果没有前置节点，使用默认颜色
                              return getStageColor || STAGE_PERIOD_COLORS['DEFAULT'];
                            })(),
                            mt: 0.5,
                            mb: 0.5,
                            opacity: 0.7,
                            zIndex: 0
                          }} 
                        />
                      </Box>
                      
                      <Box sx={{ flexGrow: 1 }}>
                        <Box 
                          onClick={() => toggleGroup(group.key)}
                          sx={{ 
                            display: 'flex', 
                            alignItems: 'center', 
                            justifyContent: 'space-between',
                            mb: 1,
                            cursor: 'pointer'
                          }}
                        >
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Typography variant="body2" sx={{ fontSize: '0.8rem', fontWeight: 'medium', mr: 1 }}>
                              {group.title}
                            </Typography>
                            
                            <Typography variant="caption" color="primary" sx={{ fontSize: '0.65rem' }}>
                              记录 {group.records.length} 条
                            </Typography>
                          </Box>
                          
                          <IconButton 
                            size="small" 
                            onClick={(e) => {
                              e.stopPropagation();
                              toggleGroup(group.key);
                            }}
                            sx={{ p: 0.5 }}
                          >
                            {isExpanded ? <ExpandLessIcon fontSize="small" /> : <ExpandMoreIcon fontSize="small" />}
                          </IconButton>
                        </Box>
                        
                        <Collapse in={isExpanded} timeout="auto">
                          <Box sx={{ mb: 1, pl: 0.5 }}>
                            {group.records.map((record) => {
                              const recordType = getRecordType(record);
                              const typeColor = getTypeColor(recordType);
                              const recordTags = getRecordTags(record);
                              
                              // 处理标题中可能存在的JSON数组格式
                              let cleanTitle = record.title;
                              if (typeof record.title === 'string') {
                                // 先尝试移除JSON格式标签的正则表达式
                                cleanTitle = record.title.replace(/\[["']?[A-Z_]+["']?(,["']?[A-Z_]+["']?)*\]/g, '').trim();
                                
                                // 如果标题本身就是一个JSON数组格式
                                if (record.title.startsWith('[') && record.title.endsWith(']')) {
                                  try {
                                    // 尝试解析JSON，如果成功就认为这是标签数组而非标题
                                    const possibleTags = JSON.parse(record.title);
                                    if (Array.isArray(possibleTags)) {
                                      // 这是标签数组，不是真正的标题，所以清空它
                                      cleanTitle = '';
                                    }
                                  } catch (e) {
                                    // 解析失败，保持原始标题
                                  }
                                }
                              }
                              
                              // 处理内容中可能存在的JSON数组格式
                              let cleanContent = record.content;
                              if (typeof record.content === 'string') {
                                cleanContent = record.content.replace(/\[["']?[A-Z_]+["']?(,["']?[A-Z_]+["']?)*\]/g, '').trim();
                              }
                              
                              return (
                                <RecordCard 
                                  key={record.id} 
                                  borderColor={recordTags.length > 0 ? recordTags[0].color : typeColor}
                                  onClick={(e) => {
                                    // 确保点击事件不会被子元素拦截
                                    e.stopPropagation();
                                    handleViewRecord(record.id);
                                    console.log('打开记录详情:', record.id);
                                  }}
                                  sx={{ 
                                    cursor: 'pointer',
                                    '&:hover': {
                                      backgroundColor: alpha(theme.palette.primary.main, 0.08),
                                      boxShadow: `0 2px 4px ${alpha(theme.palette.common.black, 0.05)}`
                                    }
                                  }}
                                >
                                  {/* 左侧内容区 */}
                                  <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                                    {/* 第一排：标题和图标 */}
                                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                                      {/* 左侧：标题和特殊图标 */}
                                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                        <Typography variant="body2" sx={{ fontWeight: 'medium', mr: 1 }}>
                                          {cleanTitle}
                                        </Typography>
                                        
                                        {/* 病程节点标签（如果有） */}
                                        {record.stageNode && (
                                          <Chip
                                            size="small"
                                            label={(() => {
                                              const stageNodeMap: Record<string, string> = {
                                                'INITIAL_VISIT': '初诊',
                                                'DIAGNOSIS': '确诊',
                                                'TREATMENT': '治疗',
                                                'FOLLOW_UP': '随访', 
                                                'PROGNOSIS': '预后',
                                                'ARCHIVE': '归档'
                                              };
                                              const upperNode = typeof record.stageNode === 'string' ? record.stageNode.toUpperCase() : '';
                                              return stageNodeMap[upperNode] || '节点';
                                            })()}
                                            sx={{ 
                                              height: 16, 
                                              fontSize: '0.6rem',
                                              backgroundColor: (() => {
                                                const upperNode = typeof record.stageNode === 'string' ? record.stageNode.toUpperCase() : '';
                                                // 使用NODE_TO_PHASE_MAP获取对应的阶段
                                                const phase = NODE_TO_PHASE_MAP[upperNode] || 'DEFAULT';
                                                // 根据阶段获取颜色
                                                const color = STAGE_PERIOD_COLORS[phase];
                                                // 返回带透明度的背景色
                                                return `${color}20`;
                                              })(),
                                              color: (() => {
                                                const upperNode = typeof record.stageNode === 'string' ? record.stageNode.toUpperCase() : '';
                                                // 使用NODE_TO_PHASE_MAP获取对应的阶段
                                                const phase = NODE_TO_PHASE_MAP[upperNode] || 'DEFAULT';
                                                // 根据阶段获取颜色
                                                return STAGE_PERIOD_COLORS[phase];
                                              })(),
                                              mr: 1
                                            }}
                                          />
                                        )}
                                        
                                        {/* 重要标记图标（如果有） */}
                                        {record.is_important && (
                                          <Tooltip title="重要记录">
                                            <Box 
                                              component="span" 
                                              sx={{ 
                                                display: 'inline-flex',
                                                color: '#F56565',
                                                mr: 0.5,
                                                fontSize: '0.8rem' 
                                              }}
                                            >
                                              <IconButton size="small" sx={{ p: 0 }}>
                                                <StarIcon fontSize="inherit" />
                                              </IconButton>
                                            </Box>
                                          </Tooltip>
                                        )}
                                        
                                        {/* 私密标记图标（如果有） */}
                                        {record.is_private && (
                                          <Tooltip title="私密记录">
                                            <Box 
                                              component="span" 
                                              sx={{ 
                                                display: 'inline-flex',
                                                color: '#718096',
                                                fontSize: '0.8rem' 
                                              }}
                                            >
                                              <IconButton size="small" sx={{ p: 0 }}>
                                                <LockIcon fontSize="inherit" />
                                              </IconButton>
                                            </Box>
                                          </Tooltip>
                                        )}
                                      </Box>
                                      
                                      {/* 右侧：严重程度标签 */}
                                      {record.severity && (
                                        <Chip
                                          size="small"
                                          label={getSeverityName(record.severity)}
                                          sx={{ 
                                            height: 16, 
                                            fontSize: '0.6rem',
                                            backgroundColor: getSeverityColor(record.severity).bg,
                                            color: getSeverityColor(record.severity).text,
                                          }}
                                        />
                                      )}
                                    </Box>
                                    
                                    {/* 第二排：标签和时间 */}
                                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                                      {/* 左侧：主要类型标签和其他标签 */}
                                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                        {recordTags.map((tag, index) => (
                                          <Chip
                                            key={index}
                                            size="small"
                                            label={tag.label}
                                            sx={{ 
                                              height: 16, 
                                              fontSize: '0.6rem',
                                              backgroundColor: `${tag.color}20`,
                                              color: tag.color,
                                            }}
                                          />
                                        ))}
                                      </Box>
                                      
                                      {/* 右侧：时间和查看按钮 */}
                                      <Box sx={{ display: 'flex', alignItems: 'center', ml: 1, flexShrink: 0 }}>
                                        <Typography 
                                          variant="caption" 
                                          color="text.secondary"
                                          sx={{ mr: 1, fontSize: '0.65rem' }}
                                        >
                                          {formatTime(record.recordDate || record.created_at || '')}
                                        </Typography>
                                        <IconButton 
                                          size="small" 
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            handleViewRecord(record.id);
                                          }}
                                          sx={{ p: 0.4 }}
                                        >
                                          <ViewIcon fontSize="small" sx={{ fontSize: '0.9rem' }} />
                                        </IconButton>
                                      </Box>
                                    </Box>

                                    {/* 第三排：内容摘要 */}
                                    {cleanContent && cleanContent.length > 0 && (
                                      <Box>
                                        <Typography 
                                          variant="body2" 
                                          sx={{ 
                                            color: 'text.secondary', 
                                            fontSize: '0.75rem',
                                            overflow: 'hidden',
                                            textOverflow: 'ellipsis',
                                            display: '-webkit-box',
                                            WebkitLineClamp: 1,
                                            WebkitBoxOrient: 'vertical',
                                          }}
                                        >
                                          {cleanContent.length > 20 ? `${cleanContent.substring(0, 20)}...` : cleanContent}
                                        </Typography>
                                      </Box>
                                    )}
                                  </Box>
                                </RecordCard>
                              );
                            })}
                          </Box>
                        </Collapse>
                      </Box>
                    </Box>
                  </Box>
                );
              })}
              
              {/* 添加新记录按钮 */}
              {onAddRecord && (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                  <Button 
                    variant="outlined"
                    size="small"
                    onClick={onAddRecord}
                  >
                    添加新记录
                  </Button>
                </Box>
              )}
            </Box>
          ) : (
            <Box sx={{ 
              display: 'flex', 
              flexDirection: 'column',
              alignItems: 'center', 
              justifyContent: 'center',
              height: '100%'
            }}>
              {isLoading ? (
                <CircularProgress size={32} />
              ) : (
                <>
                  <Typography variant="body1" color="text.secondary" sx={{ mb: 1 }}>
                    {searchTerm || activeYear !== null ? '没有找到匹配的记录' : '暂无医疗记录'}
                  </Typography>
                  {(searchTerm || activeYear !== null) && (
                    <Button 
                      variant="outlined" 
                      size="small" 
                      startIcon={<ClearIcon />}
                      onClick={clearSearch}
                    >
                      清除筛选
                    </Button>
                  )}
                </>
              )}
            </Box>
          )}
          
          {/* 当前日期指示器 */}
          <Fade in={!!currentVisibleDate}>
            <DateIndicator>
              <ClockIcon fontSize="small" />
              {currentVisibleDate}
            </DateIndicator>
          </Fade>
          
          {/* 回到顶部按钮 */}
          <Fade in={showScrollTop}>
            <ScrollTopButton 
              size="small" 
              onClick={scrollToTop}
              aria-label="回到顶部"
            >
              <ScrollTopIcon />
            </ScrollTopButton>
          </Fade>
        </TimelineContainer>
        
        {/* 时间索引 */}
        {showTimeIndex && (
          <Fade in={showTimeIndex}>
            <TimeIndexContainer>
              <Box sx={{ 
                height: '100%', 
                overflowY: 'auto', 
                overflowX: 'hidden',
                '&::-webkit-scrollbar': {
                  width: '4px',
                },
                '&::-webkit-scrollbar-track': {
                  background: 'transparent',
                },
                '&::-webkit-scrollbar-thumb': {
                  background: theme.palette.divider,
                  borderRadius: '4px',
                },
                '&:hover::-webkit-scrollbar-thumb': {
                  background: theme.palette.action.disabled,
                },
              }}>
                <Box sx={{ px: 1, py: 0.5 }}>
                  <Typography variant="caption" sx={{ fontSize: '0.6rem', opacity: 0.7, display: 'block', textAlign: 'center' }}>
                    {activeYear !== null 
                      ? activeMonth !== null 
                        ? `${activeYear}年${getMonthName(activeMonth)}` 
                        : `${activeYear}年`
                      : '全部记录'}
                  </Typography>
                </Box>
                <Divider sx={{ my: 0.5 }} />
                
                {yearMonthData.map((yearData) => (
                  <React.Fragment key={yearData.year}>
                    {/* 年份标题 */}
                    <YearButton 
                      active={activeYear === yearData.year}
                      hasRecords={yearData.hasRecords}
                      onClick={() => handleYearClick(yearData.year)}
                    >
                      {yearData.year}
                    </YearButton>
                    
                    {/* 月份列表，仅在选中当前年份时展开 */}
                    {activeYear === yearData.year && (
                      <Box sx={{ ml: 1, mt: 0.5, mb: 0.5 }}>
                        {yearData.months
                          // 按月份倒序排列（十二月到一月）
                          .sort((a, b) => b.month - a.month)
                          .map((monthData) => (
                            monthData.hasRecords && (
                              <MonthButton 
                                key={monthData.month}
                                active={activeMonth === monthData.month}
                                hasRecords={monthData.hasRecords}
                                onClick={() => handleMonthClick(yearData.year, monthData.month)}
                              >
                                {getMonthName(monthData.month)}
                              </MonthButton>
                            )
                          )
                        )}
                      </Box>
                    )}
                  </React.Fragment>
                ))}
              </Box>
            </TimeIndexContainer>
          </Fade>
        )}
      </Box>
      
      {/* 记录详情弹窗 */}
      <RecordDetailDialog
        open={dialogOpen}
        record={selectedRecord}
        onClose={handleCloseDialog}
        getTypeColor={getTypeColor}
        getTypeName={getTypeName}
        getSeverityName={getSeverityName}
        getSeverityColor={getSeverityColor}
        formatChineseDate={formatChineseDate}
      />
    </Box>
  );
};

export default EnhancedMedicalRecordTimeline; 