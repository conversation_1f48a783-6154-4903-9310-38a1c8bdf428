/**
 * LLM日志记录和WebSocket服务器
 * 记录LLM请求到文件，并提供WebSocket服务
 */

const fs = require('fs');
const path = require('path');
const http = require('http');
const WebSocket = require('ws');

// 设置日志目录
const LOG_DIR = path.join(__dirname, 'logs');

// 确保日志目录存在
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
}

// 获取WebSocket端口，根据环境设置不同的默认端口
// 开发环境使用3003，生产环境使用3005
const WS_PORT = process.env.WS_PORT || (process.env.NODE_ENV === 'production' ? 3005 : 3003);

// 创建HTTP服务器
const server = http.createServer((req, res) => {
  res.writeHead(200, { 'Content-Type': 'text/plain' });
  res.end('WebSocket服务器运行中');
});

// 创建WebSocket服务器
const wss = new WebSocket.Server({ server });

console.log('==============================');
console.log('启动LLM日志记录和WebSocket服务器');
console.log('==============================');

// 存储所有连接的客户端
const clients = new Set();

// 处理WebSocket连接
wss.on('connection', (ws) => {
  console.log('新的WebSocket客户端已连接');
  clients.add(ws);

  // 发送欢迎消息
  ws.send(JSON.stringify({
    type: 'system',
    message: '已连接到LLM日志服务器'
  }));

  // 处理客户端断开连接
  ws.on('close', () => {
    console.log('WebSocket客户端已断开连接');
    clients.delete(ws);
  });
});

// 广播消息给所有连接的客户端
function broadcast(message) {
  const messageStr = JSON.stringify(message);
  clients.forEach(client => {
    if (client.readyState === WebSocket.OPEN) {
      client.send(messageStr);
    }
  });
}

// 记录LLM请求到文件
function logLLMRequest(data) {
  const timestamp = new Date().toISOString();
  const filename = `llm-request-${timestamp}.json`;
  const filepath = path.join(LOG_DIR, filename);

  try {
    fs.writeFileSync(filepath, JSON.stringify(data, null, 2));
    console.log(`LLM请求已记录到: ${filepath}`);
  } catch (err) {
    console.error('记录LLM请求失败:', err);
  }
}

// 启动服务器
server.listen(WS_PORT, () => {
  console.log(`WebSocket服务器已启动，监听端口 ${WS_PORT}`);
});

// 导出广播函数供其他模块使用
module.exports = {
  broadcast
};