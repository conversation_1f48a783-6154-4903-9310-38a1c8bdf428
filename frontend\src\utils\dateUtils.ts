/**
 * 格式化日期为本地化字符串
 * @param date 日期对象或可以转换为日期的字符串
 * @param options 格式化选项
 * @returns 格式化后的日期字符串
 */
export const formatDate = (date: Date | string, options: Intl.DateTimeFormatOptions = {}): string => {
  try {
    const dateObj = date instanceof Date ? date : new Date(date);
    
    // 检查日期是否有效
    if (isNaN(dateObj.getTime())) {
      return '无效日期';
    }
    
    // 默认格式化选项
    const defaultOptions: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    };
    
    // 合并选项
    const mergedOptions = { ...defaultOptions, ...options };
    
    // 格式化日期
    return dateObj.toLocaleString('zh-CN', mergedOptions);
  } catch (error) {
    console.error('日期格式化错误:', error);
    return '格式化错误';
  }
};

/**
 * 计算两个日期之间的天数差异
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 天数差异
 */
export const daysBetween = (startDate: Date | string, endDate: Date | string = new Date()): number => {
  try {
    const start = startDate instanceof Date ? startDate : new Date(startDate);
    const end = endDate instanceof Date ? endDate : new Date(endDate);
    
    // 检查日期是否有效
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return 0;
    }
    
    // 将时间设为零点以便计算整天数
    const startUtc = Date.UTC(start.getFullYear(), start.getMonth(), start.getDate());
    const endUtc = Date.UTC(end.getFullYear(), end.getMonth(), end.getDate());
    
    // 计算天数差
    const millisecondsPerDay = 1000 * 60 * 60 * 24;
    return Math.floor((endUtc - startUtc) / millisecondsPerDay);
  } catch (error) {
    console.error('计算天数差异错误:', error);
    return 0;
  }
};

/**
 * 格式化相对时间（例如：3天前，5分钟前）
 * @param date 日期对象或可以转换为日期的字符串 
 * @returns 格式化后的相对时间字符串
 */
export const formatRelativeTime = (date: Date | string): string => {
  try {
    const dateObj = date instanceof Date ? date : new Date(date);
    
    // 检查日期是否有效
    if (isNaN(dateObj.getTime())) {
      return '无效日期';
    }
    
    const now = new Date();
    const diffMs = now.getTime() - dateObj.getTime();
    
    // 时间差小于0，表示是未来时间
    if (diffMs < 0) {
      return formatDate(dateObj);
    }
    
    // 时间差（秒）
    const diffSec = Math.floor(diffMs / 1000);
    
    // 一分钟内
    if (diffSec < 60) {
      return '刚刚';
    }
    
    // 一小时内
    if (diffSec < 3600) {
      return `${Math.floor(diffSec / 60)}分钟前`;
    }
    
    // 一天内
    if (diffSec < 86400) {
      return `${Math.floor(diffSec / 3600)}小时前`;
    }
    
    // 一个月内
    if (diffSec < 2592000) {
      return `${Math.floor(diffSec / 86400)}天前`;
    }
    
    // 一年内
    if (diffSec < 31536000) {
      return `${Math.floor(diffSec / 2592000)}个月前`;
    }
    
    // 超过一年
    return `${Math.floor(diffSec / 31536000)}年前`;
  } catch (error) {
    console.error('格式化相对时间错误:', error);
    return '时间未知';
  }
}; 