/* 时间轴容器 */
.disease-timeline {
  position: relative;
  margin-top: -2px; /* 从5px减小7px，改为-2px，使内容向上移动 */
  height: 30px; /* 再减小10px，改为30px */
  padding: 0 6px; /* 添加内边距，使节点不会太靠近边缘 */
}

/* 节点容器 */
.stage-nodes {
  display: flex;
  justify-content: space-between;
  position: relative;
  z-index: 2;
}

/* 连接线容器 */
.stage-connectors {
  position: absolute;
  top: 9px; /* 调整使连接线与节点中心对齐 */
  left: 12px;
  right: 12px;
  display: flex;
  z-index: 1;
}

/* 节点包装器 */
.stage-node-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 4px;
  position: relative; /* 为提示框定位准备 */
}

/* 节点样式 */
.stage-node {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: none;
  position: relative;
  transition: all 0.3s ease;
  z-index: 3; /* 确保节点在连接线上方 */
}

/* 已完成节点样式 */
.stage-node.completed {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
  opacity: 1; /* 确保完成节点不透明 */
}

/* 活跃节点样式 */
.stage-node.active {
  width: 18px;
  height: 18px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
}

/* 节点中心点 */
.node-core {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background-color: white;
}

/* 连接线样式 */
.stage-connector {
  flex: 1;
  height: 2px;
  margin: 0 1px;
  transition: background-color 0.3s ease;
}

/* 阶段标签 */
.stage-label {
  margin-top: 4px;
  font-size: 0.65rem;
  text-align: center;
  max-width: 32px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 日期标签 */
.stage-date {
  display: block;
  font-size: 0.5rem;
  margin-top: 2px;
  color: rgba(0, 0, 0, 0.54);
  text-align: center;
  max-width: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 响应式调整 */
@media (max-width: 500px) {
  .stage-node {
    width: 14px;
    height: 14px;
  }
  
  .stage-node.active {
    width: 16px;
    height: 16px;
  }
  
  .stage-label {
    font-size: 0.6rem;
    max-width: 28px;
  }

  .stage-date {
    font-size: 0.4rem;
    max-width: 32px;
  }
  
  .stage-connectors {
    top: 7px;
  }
}

/* 动画效果 */
.stage-node.active::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(66, 153, 225, 0.2) 0%, rgba(66, 153, 225, 0) 70%);
  animation: pulse 2s infinite;
  z-index: -1;
}

/* 根据阶段适应不同颜色的脉冲效果 */
.stage-node.active[style*="rgb(245, 101, 101)"]::before {
  background: radial-gradient(circle, rgba(245, 101, 101, 0.2) 0%, rgba(245, 101, 101, 0) 70%);
}

.stage-node.active[style*="rgb(159, 122, 234)"]::before {
  background: radial-gradient(circle, rgba(159, 122, 234, 0.2) 0%, rgba(159, 122, 234, 0) 70%);
}

.stage-node.active[style*="rgb(237, 137, 54)"]::before {
  background: radial-gradient(circle, rgba(237, 137, 54, 0.2) 0%, rgba(237, 137, 54, 0) 70%);
}

.stage-node.active[style*="rgb(56, 178, 172)"]::before {
  background: radial-gradient(circle, rgba(56, 178, 172, 0.2) 0%, rgba(56, 178, 172, 0) 70%);
}

.stage-node.active[style*="rgb(66, 153, 225)"]::before {
  background: radial-gradient(circle, rgba(66, 153, 225, 0.2) 0%, rgba(66, 153, 225, 0) 70%);
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.3;
  }
  100% {
    transform: scale(0.95);
    opacity: 0.7;
  }
}

/* 为活跃节点添加方向指示 */
.stage-node.active::after {
  content: '';
  position: absolute;
  height: 4px;
  width: 4px;
  border-top: 4px solid rgba(0, 0, 0, 0.1);
  border-right: 4px solid rgba(0, 0, 0, 0.1);
  transform: rotate(45deg);
  right: -8px;
  top: 50%;
  margin-top: -2px;
  opacity: 0.7;
} 