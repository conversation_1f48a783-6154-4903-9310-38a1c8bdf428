// 使用系统标准枚举
import { 
  RecordTypeEnum, 
  RecordTypeNames, 
  RECORD_TYPE_LABELS,
  SeverityEnum 
} from '../../types/recordEnums';

// 直接导出系统标准枚举
export { RecordTypeEnum, SeverityEnum };

// 记录信息接口
export interface MedicalRecord {
  id: string;
  title: string;
  type: RecordTypeEnum;
  severity?: SeverityEnum;
  timestamp: string; // ISO 格式的日期时间
  tags?: string[];
  description?: string;
}

/**
 * 获取记录类型对应的中文名称
 */
export const getRecordTypeName = (type: RecordTypeEnum | string): string => {
  if (typeof type === 'string') {
    // 检查是否是枚举值
    const enumValue = Object.values(RecordTypeEnum).find(val => val === type);
    if (enumValue) {
      return RecordTypeNames[enumValue as RecordTypeEnum] || '其他';
    }
    // 如果不是枚举值，尝试转换为枚举后查询
    if (type.toUpperCase() in RecordTypeEnum) {
      return RecordTypeNames[type.toUpperCase() as unknown as RecordTypeEnum] || '其他';
    }
    return '其他';
  }
  return RecordTypeNames[type] || '其他';
};

/**
 * 获取记录严重程度的中文名称
 */
export const getSeverityName = (severity: SeverityEnum | string | undefined): string => {
  if (!severity) return '普通';
  
  const severityMap: Record<string, string> = {
    'MILD': '轻微',
    'MODERATE': '中等',
    'SEVERE': '严重',
    'CRITICAL': '危重',
    // 兼容旧状态值
    'NORMAL': '普通',
    'FOLLOW_UP': '随访',
    'IMPORTANT': '重要',
    'URGENT': '紧急'
  };
  
  if (typeof severity === 'string') {
    return severityMap[severity.toUpperCase()] || '普通';
  }
  
  return severityMap[severity] || '普通';
};

/**
 * 这是一个空实现，实际上我们会使用系统现有组件渲染界面
 */
const EnhancedMedicalRecordTimeline: React.FC<any> = () => null;

export default EnhancedMedicalRecordTimeline; 