const express = require('express');
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');
const knex = require('knex')(require('../knexfile').development);
const router = express.Router();
const { Model } = require('objection');
const { auth } = require('../src/middleware/auth');

// JWT密钥，应从环境变量或配置文件获取
const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret';

// 设置 Objection.js
Model.knex(knex);

// 中间件：验证用户认证
const authenticateUser = async (req, res, next) => {
  const authHeader = req.headers.authorization;
  if (!authHeader) return res.status(401).json({ error: '未授权' });
  
  const token = authHeader.split(' ')[1];
  try {
    const { id } = jwt.verify(token, JWT_SECRET);
    req.userId = id; // 将用户ID附加到请求对象上
    
    // 检查用户是否存在
    const user = await knex('users').where({ id }).first();
    if (!user) {
      return res.status(404).json({ error: '用户不存在' });
    }
    
    req.user = user; // 将用户信息附加到请求对象上
    next();
  } catch (error) {
    console.error('认证错误:', error);
    res.status(401).json({ error: 'token无效或已过期' });
  }
};

// 添加日志记录函数
const addPatientLog = async (patientId, userId, action, details) => {
  try {
    await knex('patient_logs').insert({
      id: uuidv4(),
      patientId,
      userId,
      action,
      details: JSON.stringify(details),
      createdAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('添加患者日志错误:', error);
  }
};

// 获取所有患者
router.get('/', auth, async (req, res) => {
  try {
    // 获取请求参数
    const { userId } = req;
    
    // 获取所有患者
    const patients = await knex('patients')
      .where('userId', userId)
      .orderBy('createdAt', 'desc');
    
    res.json(patients);
  } catch (error) {
    console.error('获取患者列表失败:', error);
    res.status(500).json({ error: '获取患者列表失败' });
  }
});

// 获取单个患者详情
router.get('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const { userId } = req;
    
    const patient = await knex('patients')
      .where('id', id)
      .andWhere('userId', userId)
      .first();
    
    if (!patient) {
      return res.status(404).json({ error: '患者不存在' });
    }
    
    res.json(patient);
  } catch (error) {
    console.error('获取患者详情失败:', error);
    res.status(500).json({ error: '获取患者详情失败' });
  }
});

// 创建新患者
router.post('/', auth, async (req, res) => {
  try {
    const { userId } = req;
    const {
      name,
      gender,
      birthDate,
      phoneNumber,
      email,
      address,
      idCard,
      medicareCard,
      medicareLocation,
      bloodType,
      emergencyContactName,
      emergencyContactPhone,
      emergencyContactRelationship,
      pastMedicalHistory,
      familyMedicalHistory,
      allergyHistory,
      isPrimary
    } = req.body;
    
    // 检查是否已经达到患者数量限制
    const userRecord = await knex('users').where('id', userId).first();
    if (!userRecord) {
      return res.status(404).json({ error: '用户不存在' });
    }
    
    // 获取用户当前患者数量
    const patientCount = await knex('patients')
      .where('userId', userId)
      .count('id as count')
      .first();
    
    // 检查是否超过限制
    if (patientCount.count >= userRecord.familyMemberLimit) {
      return res.status(403).json({ 
        error: `已达到最大患者数量限制 (${userRecord.familyMemberLimit})。请升级账户或删除不需要的患者记录。` 
      });
    }
    
    // 如果标记为本人档案，需要先将其他本人档案重置
    if (isPrimary) {
      await knex('patients')
        .where('userId', userId)
        .where('isPrimary', 1)
        .update({ isPrimary: 0 });
    }
    
    // 创建新患者
    const now = new Date().toISOString();
    const patientId = uuidv4();
    
    await knex('patients').insert({
      id: patientId,
      userId,
      name,
      gender,
      birthDate,
      phoneNumber,
      email,
      address,
      idCard,
      medicareCard,
      medicareLocation,
      bloodType,
      emergencyContactName,
      emergencyContactPhone,
      emergencyContactRelationship,
      pastMedicalHistory,
      familyMedicalHistory,
      allergyHistory,
      isPrimary: isPrimary ? 1 : 0,
      createdAt: now,
      updatedAt: now
    });
    
    const newPatient = await knex('patients').where('id', patientId).first();
    
    res.status(201).json({ 
      message: '患者创建成功',
      patient: newPatient
    });
  } catch (error) {
    console.error('创建患者失败:', error);
    
    // 处理特定错误
    if (error.code === 'SQLITE_CONSTRAINT') {
      return res.status(400).json({ error: '患者信息已存在（身份证号或医保卡号重复）' });
    }
    
    res.status(500).json({ error: '创建患者失败' });
  }
});

// 更新患者信息
router.put('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const { userId } = req;
    const {
      name,
      gender,
      birthDate,
      phoneNumber,
      email,
      address,
      idCard,
      medicareCard,
      medicareLocation,
      bloodType,
      emergencyContactName,
      emergencyContactPhone,
      emergencyContactRelationship,
      pastMedicalHistory,
      familyMedicalHistory,
      allergyHistory,
      isPrimary
    } = req.body;
    
    // 检查患者是否存在
    const patient = await knex('patients')
      .where('id', id)
      .andWhere('userId', userId)
      .first();
    
    if (!patient) {
      return res.status(404).json({ error: '患者不存在' });
    }
    
    // 如果标记为本人档案，需要先将其他本人档案重置
    if (isPrimary) {
      await knex('patients')
        .where('userId', userId)
        .where('isPrimary', 1)
        .whereNot('id', id)
        .update({ isPrimary: 0 });
    }
    
    // 更新患者信息
    const now = new Date().toISOString();
    await knex('patients')
      .where('id', id)
      .update({
        name,
        gender,
        birthDate,
        phoneNumber,
        email,
        address,
        idCard,
        medicareCard,
        medicareLocation,
        bloodType,
        emergencyContactName,
        emergencyContactPhone,
        emergencyContactRelationship,
        pastMedicalHistory,
        familyMedicalHistory,
        allergyHistory,
        isPrimary: isPrimary ? 1 : 0,
        updatedAt: now
      });
    
    const updatedPatient = await knex('patients').where('id', id).first();
    
    res.json({ 
      message: '患者信息更新成功',
      patient: updatedPatient,
      isPrimary: !!isPrimary
    });
  } catch (error) {
    console.error('更新患者失败:', error);
    
    // 处理特定错误
    if (error.code === 'SQLITE_CONSTRAINT') {
      return res.status(400).json({ error: '患者信息更新失败（身份证号或医保卡号重复）' });
    }
    
    res.status(500).json({ error: '更新患者失败' });
  }
});

// 删除患者
router.delete('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const { userId } = req;
    
    // 检查患者是否存在
    const patient = await knex('patients')
      .where('id', id)
      .andWhere('userId', userId)
      .first();
    
    if (!patient) {
      return res.status(404).json({ error: '患者不存在' });
    }
    
    // 检查是否有关联的病理记录
    const diseaseCount = await knex('diseases')
      .where('patientId', id)
      .count('id as count')
      .first();
    
    if (diseaseCount.count > 0) {
      return res.status(409).json({ 
        error: `无法删除该患者，因为存在 ${diseaseCount.count} 条关联的病理记录。请先删除相关病理记录。` 
      });
    }
    
    // 删除患者
    await knex('patients')
      .where('id', id)
      .del();
    
    res.json({ message: '患者删除成功' });
  } catch (error) {
    console.error('删除患者失败:', error);
    res.status(500).json({ error: '删除患者失败' });
  }
});

/**
 * 获取指定患者的所有疾病
 * GET /patients/:patientId/diseases
 */
router.get('/:id/diseases', auth, async (req, res) => {
  try {
    const patientId = req.params.id;
    
    // 验证患者是否存在且属于当前用户
    const patient = await knex('patients')
      .where({ id: patientId })
      .first();
    
    if (!patient) {
      return res.status(404).json({ message: '患者不存在' });
    }
    
    if (patient.userId !== req.user.id && req.user.role !== 'ADMIN') {
      return res.status(403).json({ message: '无权访问此患者信息' });
    }
    
    // 获取患者的所有疾病记录
    const diseases = await knex('diseases')
      .where({ 
        patientId, 
        isDeleted: false 
      })
      .orderBy('diagnosisDate', 'desc');
    
    res.json(diseases);
  } catch (error) {
    console.error('获取患者疾病列表失败:', error);
    res.status(500).json({ message: '获取患者疾病列表失败', error: error.message });
  }
});

// 获取患者操作历史
router.get('/:id/timeline', authenticateUser, async (req, res) => {
  try {
    const { userId } = req;
    const { id } = req.params;
    const { page = 1, limit = 10, action } = req.query;
    
    // 检查患者是否存在并属于当前用户
    const patient = await knex('patients')
      .where({ id, userId, deletedAt: null })
      .first();
    
    if (!patient) {
      return res.status(404).json({ error: '患者不存在' });
    }
    
    // 构建查询
    let query = knex('patient_logs').where({ patientId: id });
    
    // 按操作类型筛选
    if (action && ['CREATE', 'UPDATE', 'DELETE'].includes(action)) {
      query = query.where('action', action);
    }
    
    // 获取总记录数
    const totalQuery = query.clone();
    const { count } = await totalQuery.count('id as count').first();
    
    // 分页查询
    const offset = (page - 1) * limit;
    const logs = await query
      .orderBy('createdAt', 'desc')
      .limit(limit)
      .offset(offset);
    
    // 构建响应
    res.json({
      logs,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: parseInt(count),
        totalPages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    console.error('获取患者操作历史错误:', error);
    res.status(500).json({ error: '服务器错误' });
  }
});

module.exports = router; 