const axios = require('axios');

// 配置
const API_URL = 'http://localhost:3001';
let token = null;

// 测试用户
const testUser = {
  username: 'perfuser_' + Date.now() + '_' + Math.floor(Math.random() * 10000),
  email: `perfuser_${Date.now()}_${Math.floor(Math.random() * 10000)}@example.com`,
  password: 'Test123456',
  phoneNumber: '138' + Math.floor(Math.random() * 100000000).toString().padStart(8, '0'),
};

// 性能测试函数
async function runPerformanceTests() {
  try {
    console.log('=== 开始性能测试 ===');
    
    // 1. 注册用户
    console.log('1. 注册测试用户...');
    await axios.post(`${API_URL}/register`, testUser);
    console.log('注册成功');
    
    // 2. 登录
    console.log('2. 登录获取token...');
    const loginResponse = await axios.post(`${API_URL}/login`, {
      username: testUser.username,
      password: testUser.password
    });
    token = loginResponse.data.token;
    console.log('登录成功');
    
    // 3. 创建多个测试患者
    console.log('3. 创建测试患者数据...');
    const patientCount = 8; // 创建最大数量的患者
    const patients = [];
    
    for (let i = 0; i < patientCount; i++) {
      const isPrimary = i === 0 ? 1 : 0;
      const patient = {
        name: `性能测试患者${i+1}`,
        gender: i % 2 === 0 ? '男' : '女',
        phoneNumber: `139${String(i+1).padStart(8, '0')}`,
        email: `patient${i+1}@test.com`,
        address: `测试地址${i+1}`,
        birthDate: `199${i%9+1}-01-01`,
        bloodType: ['A', 'B', 'AB', 'O'][i % 4],
        emergencyContactName: `紧急联系人${i+1}`,
        emergencyContactPhone: `137${String(i+1).padStart(8, '0')}`,
        isPrimary
      };
      
      try {
        const response = await axios.post(
          `${API_URL}/patients`,
          patient,
          { headers: { Authorization: `Bearer ${token}` } }
        );
        patients.push(response.data.patient);
        console.log(`- 创建患者${i+1}成功，ID: ${response.data.patient.id}`);
      } catch (error) {
        console.error(`- 创建患者${i+1}失败:`, error.response?.data || error.message);
      }
    }
    
    console.log(`成功创建了 ${patients.length} 个测试患者`);
    
    // 4. 测试查询性能
    console.log('\n4. 测试查询性能...');
    console.log('- 测试获取患者列表响应时间（无索引查询）');
    
    const startTime = Date.now();
    const response = await axios.get(
      `${API_URL}/patients`,
      { headers: { Authorization: `Bearer ${token}` } }
    );
    const endTime = Date.now();
    
    console.log(`- 获取到 ${response.data.length} 个患者`);
    console.log(`- 响应时间: ${endTime - startTime}ms`);
    
    if (endTime - startTime < 100) {
      console.log('✅ 性能测试通过：响应时间 < 100ms');
    } else {
      console.log('⚠️ 性能测试注意：响应时间 > 100ms，可能需要优化');
    }
    
    // 5. 清理测试数据
    console.log('\n5. 清理测试数据...');
    let deletedCount = 0;
    
    for (const patient of patients) {
      try {
        await axios.delete(
          `${API_URL}/patients/${patient.id}`,
          { headers: { Authorization: `Bearer ${token}` } }
        );
        deletedCount++;
      } catch (error) {
        console.error(`- 删除患者 ${patient.id} 失败:`, error.response?.data || error.message);
      }
    }
    
    console.log(`- 成功删除了 ${deletedCount} 个测试患者`);
    console.log('=== 性能测试完成 ===');
    
  } catch (error) {
    console.error('测试失败:', error.response?.data || error.message);
  }
}

// 执行测试
runPerformanceTests(); 