import React from 'react';
import { 
  <PERSON>, 
  Typography, 
  Chip, 
  Button, 
  List, 
  ListItem, 
  ListItemIcon, 
  ListItemText,
  Alert
} from '@mui/material';
import { 
  SmartToy as SmartToyIcon,
  ArrowForward as ArrowForwardIcon,
  ArrowRight as ArrowRightIcon,
  Add as AddIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { parseISO } from 'date-fns';

/**
 * 病理卡片中的AI分析摘要组件
 * 显示最近一次AI分析的关键信息
 */
interface DiseaseCardAISectionProps {
  diseaseId: string;
  aiAnalysisSummary?: {
    lastAnalysisDate: string;
    status: string;
    trend: 'improving' | 'stable' | 'worsening';
    riskLevel: 'low' | 'medium' | 'high';
    keyRecommendations: string[];
    reportUrl: string;
    emergencyStatus: boolean;
    topHospital: string;
    budgetRange: string;
    displayFields: string[];
  } | null;
  userRole?: string;
}

const DiseaseCardAISection: React.FC<DiseaseCardAISectionProps> = ({ 
  diseaseId, 
  aiAnalysisSummary, 
  userRole = 'USER' 
}) => {
  const navigate = useNavigate();
  
  // 获取趋势文本
  const getTrendText = (trend: string) => {
    switch(trend) {
      case 'improving': return '改善中';
      case 'stable': return '稳定';
      case 'worsening': return '恶化中';
      default: return '未知';
    }
  };
  
  // 获取趋势颜色
  const getTrendColor = (trend: string): 'success' | 'info' | 'error' | 'default' => {
    switch(trend) {
      case 'improving': return 'success';
      case 'stable': return 'info';
      case 'worsening': return 'error';
      default: return 'default';
    }
  };
  
  // 创建新分析
  const handleCreateAnalysis = () => {
    navigate(`/ai-assistant`);
  };
  
  // 查看分析报告
  const handleViewReport = () => {
    navigate(`/ai-assistant`);
  };
  
  // 如果没有分析结果，显示创建按钮
  if (!aiAnalysisSummary) {
    return (
      <Box sx={{ mt: 2, p: 1, borderTop: 1, borderColor: 'divider' }}>
        <Typography variant="subtitle2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
          <SmartToyIcon sx={{ fontSize: 16, mr: 0.5 }} />
          暂无AI辅医分析
        </Typography>
        <Button 
          size="small" 
          startIcon={<AddIcon />}
          onClick={handleCreateAnalysis}
          sx={{ mt: 1 }}
        >
          创建分析
        </Button>
      </Box>
    );
  }
  
  // 显示分析结果摘要
  return (
    <Box sx={{ mt: 2, p: 1, borderTop: 1, borderColor: 'divider' }}>
      <Typography variant="subtitle2" color="primary" sx={{ display: 'flex', alignItems: 'center' }}>
        <SmartToyIcon sx={{ fontSize: 16, mr: 0.5 }} />
        AI辅医分析
      </Typography>
      
      <Box sx={{ mt: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Typography variant="body2" sx={{ mr: 1 }}>
            状态:
          </Typography>
          <Chip 
            size="small"
            label={getTrendText(aiAnalysisSummary.trend)}
            color={getTrendColor(aiAnalysisSummary.trend)}
          />
          <Typography variant="caption" sx={{ ml: 1 }}>
            {formatDistanceToNow(parseISO(aiAnalysisSummary.lastAnalysisDate), { locale: zhCN })}前更新
          </Typography>
        </Box>
        
        {aiAnalysisSummary.emergencyStatus && (
          <Alert severity="warning" sx={{ mt: 1, py: 0, fontSize: '0.75rem' }}>
            存在紧急情况，请及时就医
          </Alert>
        )}
        
        {userRole !== 'USER' && aiAnalysisSummary.displayFields.includes('budgetEstimation') && (
          <Typography variant="body2" sx={{ mt: 1, display: 'flex', alignItems: 'center' }}>
            <InfoIcon sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
            预估费用: {aiAnalysisSummary.budgetRange}
          </Typography>
        )}
        
        {aiAnalysisSummary.keyRecommendations?.length > 0 && (
          <Box sx={{ mt: 1 }}>
            <Typography variant="body2">
              建议:
            </Typography>
            <List dense disablePadding>
              {aiAnalysisSummary.keyRecommendations.slice(0, 2).map((rec, idx) => (
                <ListItem key={idx} disablePadding>
                  <ListItemIcon sx={{ minWidth: 24 }}>
                    <ArrowRightIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText
                    primary={rec}
                    primaryTypographyProps={{ variant: 'caption' }}
                  />
                </ListItem>
              ))}
            </List>
          </Box>
        )}
      </Box>
      
      <Button
        size="small"
        endIcon={<ArrowForwardIcon />}
        onClick={handleViewReport}
        sx={{ mt: 1 }}
      >
        查看完整分析
      </Button>
    </Box>
  );
};

export default DiseaseCardAISection; 