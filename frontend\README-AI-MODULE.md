# 辅医模块实施计划

## 已完成的前端开发

1. **基础类型定义**
   - 创建了AIReport及相关接口
   - 定义了可配置的字段可见性

2. **服务层**
   - 实现了与后端API的交互服务
   - 支持获取报告、生成报告和下载PDF功能

3. **页面组件**
   - 创建了AI分析列表页面(AIAssistantPage)
   - 创建了AI报告详情页面(AIReportDetailPage)
   - 创建了管理员配置页面(AIReportConfigPage)

4. **导航菜单集成**
   - 添加了辅医主菜单
   - 为管理员添加了辅医配置子菜单

5. **Dashboard集成**
   - 创建了病理卡片中的AI分析摘要组件(DiseaseCardAISection)
   - 集成到DiseaseCard中显示

## 后端开发计划

1. **数据库模型**
   - 创建AIReport表，与Disease表关联
   - 创建AIReportConfig表，存储可见字段配置

2. **API接口**
   - POST /api/ai-reports - 创建新的AI分析报告
   - GET /api/ai-reports?diseaseId={diseaseId} - 获取病理相关的AI报告
   - GET /api/ai-reports/{reportId} - 获取特定报告详情
   - GET /api/ai-reports/{reportId}/pdf - 下载报告PDF
   - GET /api/ai-reports/config - 获取报告配置
   - POST /api/ai-reports/config - 保存报告配置
   - GET /api/ai-reports/quota - 检查用户配额

3. **AI分析服务**
   - 实现病理信息匿名化处理
   - 整合与LLM API的交互
   - 实现报告生成逻辑
   - 集成PDF生成服务

4. **用户配额和权限控制**
   - 根据用户角色和权益实现次数限制
   - 实现用户和服务用户可见字段控制

## 实施优先级和时间计划

### 第一阶段（1-2周）
- 后端数据库模型设计和创建
- 基本API接口实现
- 前端与后端API集成

### 第二阶段（2-3周）
- AI分析服务实现
- 匿名化处理和LLM集成
- 报告生成逻辑
- 配额和权限控制

### 第三阶段（1周）
- PDF生成和下载功能
- UI优化和体验改进
- 测试和Bug修复

## 测试计划

1. **单元测试**
   - 匿名化处理逻辑
   - 用户配额计算
   - 字段可见性控制

2. **集成测试**
   - API接口测试
   - 前后端交互测试
   - LLM服务集成测试

3. **用户测试**
   - 不同角色权限测试
   - 报告生成和查看流程测试
   - Dashboard集成体验测试 