/**
 * LLM服务
 * 提供与各种LLM模型API的交互
 */
const axios = require('axios');
const config = require('../../src/config');

// 模型服务商列表
const LLM_PROVIDERS = {
  DEEPSEEK_OFFICIAL: 'deepseek_official',
  VOLCENGINE: 'volcengine'
};

// 火山引擎模型列表
const VOLCENGINE_MODELS = {
  DEEPSEEK_R1: 'deepseek-r1-250120',
  DEEPSEEK_V3: 'deepseek-v3',
  DOUBAO: 'doubao-llama3'
};

/**
 * 获取LLM配置
 * @returns {Object} LLM配置
 */
const getLlmConfig = async () => {
  // 在实际应用中，这些配置可以存储在数据库或环境变量中
  // 这里为了示例简化，直接返回配置
  const llmConfig = {
    provider: process.env.LLM_PROVIDER || LLM_PROVIDERS.DEEPSEEK_OFFICIAL,
    volcengineModel: process.env.VOLCENGINE_MODEL || 'deepseek-r1-250120', // 更新默认模型名称
    deepseekApiKey: process.env.DEEPSEEK_API_KEY || '',
    volcengineApiKey: process.env.VOLCENGINE_API_KEY || '',
    maxTokens: parseInt(process.env.LLM_MAX_TOKENS || '4096'),
    temperature: parseFloat(process.env.LLM_TEMPERATURE || '0.3')
  };
  
  // 添加调试日志
  if (process.env.NODE_ENV === 'development') {
    console.log('===== LLM配置信息 =====');
    console.log('提供商:', llmConfig.provider);
    console.log('模型:', llmConfig.volcengineModel);
    console.log('DeepSeek API Key是否存在:', llmConfig.deepseekApiKey ? '是' : '否');
    console.log('火山引擎 API Key是否存在:', llmConfig.volcengineApiKey ? '是' : '否');
    console.log('最大生成Token:', llmConfig.maxTokens);
    console.log('温度:', llmConfig.temperature);
    console.log('====================');
  }
  
  return llmConfig;
};

/**
 * 调用DeepSeek官方API
 * @param {string} prompt 提示内容
 * @param {Object} options 选项
 * @returns {Promise<Object>} LLM响应
 */
const callDeepSeekApi = async (prompt, options = {}) => {
  const config = await getLlmConfig();
  
  const apiUrl = 'https://api.deepseek.com/v1/chat/completions';
  
  const response = await axios.post(apiUrl, {
    model: 'deepseek-r1',  // 或其他可用模型
    messages: [
      { role: 'system', content: options.systemPrompt || '你是一个专业的医疗AI助手，根据病历信息提供专业分析和建议。' },
      { role: 'user', content: prompt }
    ],
    max_tokens: options.maxTokens || config.maxTokens,
    temperature: options.temperature || config.temperature,
    stop: options.stop || null,
    stream: false
  }, {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${config.deepseekApiKey}`
    },
    timeout: parseInt(process.env.LLM_API_TIMEOUT || '60000') // 默认60秒超时
  });
  
  return response.data;
};

/**
 * 调用火山引擎API
 * @param {string} prompt 提示内容
 * @param {Object} options 选项
 * @returns {Promise<Object>} LLM响应
 */
const callVolcengineApi = async (prompt, options = {}) => {
  const config = await getLlmConfig();
  
  // 修正为正确的API端点
  const apiUrl = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';
  
  // 设置超时时间，优先使用选项中的超时时间，其次使用环境变量，最后使用默认值150秒
  const timeout = options.timeout || parseInt(process.env.LLM_API_TIMEOUT || '150000'); // 默认150秒超时
  console.log(`火山引擎API设置超时时间: ${timeout}ms`);
  
  // 添加重试逻辑，优先使用选项中的重试次数，其次使用环境变量，最后使用默认值
  const maxRetries = typeof options.maxRetries === 'number' ? options.maxRetries : parseInt(process.env.LLM_MAX_RETRIES || '3');
  let retries = 0;
  let lastError = null;
  
  // 如果maxRetries为0，直接执行一次调用，不进行重试
  if (maxRetries === 0) {
    console.log('火山引擎API设置为不重试');
    try {
      console.log(`调用火山引擎API (单次调用模式)`);
      
      // 按照官方格式构建请求
      const response = await axios.post(apiUrl, {
        model: options.model || config.volcengineModel, // 使用配置中的模型名称
        messages: [
          { role: 'system', content: options.systemPrompt || '你是一个专业的医疗AI助手，根据病历信息提供专业分析和建议。' },
          { role: 'user', content: prompt }
        ],
        max_tokens: options.maxTokens || config.maxTokens,
        temperature: options.temperature || config.temperature,
        top_p: options.topP || 0.9
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.volcengineApiKey}`,
          'Accept': 'application/json'
        },
        timeout: timeout
      });
      
      if (!response.data || !response.data.choices || !response.data.choices[0]) {
        throw new Error('火山引擎API返回数据格式不正确');
      }
      
      console.log('火山引擎API调用成功，响应结构:', Object.keys(response.data));
      return response.data;
    } catch (error) {
      console.error(`火山引擎API调用失败: ${error.message}`);
      throw error; // 单次调用模式下，直接抛出错误
    }
  }
  
  // 重试模式
  while (retries < maxRetries) {
    try {
      console.log(`尝试调用火山引擎API (尝试 ${retries + 1}/${maxRetries})`);
      
      // 按照官方格式构建请求
      const response = await axios.post(apiUrl, {
        model: options.model || config.volcengineModel, // 使用配置中的模型名称
        messages: [
          { role: 'system', content: options.systemPrompt || '你是一个专业的医疗AI助手，根据病历信息提供专业分析和建议。' },
          { role: 'user', content: prompt }
        ],
        max_tokens: options.maxTokens || config.maxTokens,
        temperature: options.temperature || config.temperature,
        top_p: options.topP || 0.9
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.volcengineApiKey}`,
          'Accept': 'application/json'
        },
        timeout: timeout
      });
      
      if (!response.data || !response.data.choices || !response.data.choices[0]) {
        throw new Error('火山引擎API返回数据格式不正确');
      }
      
      console.log('火山引擎API调用成功，响应结构:', Object.keys(response.data));
      return response.data;
    } catch (error) {
      lastError = error;
      retries++;
      console.error(`火山引擎API调用失败 (尝试 ${retries}/${maxRetries}): ${error.message}`);
      
      if (retries < maxRetries) {
        // 指数退避重试，每次等待时间增加
        const retryDelay = 5000 * Math.pow(2, retries - 1); // 5秒, 10秒, 20秒...
        console.log(`等待 ${retryDelay}ms 后重试...`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
    }
  }
  
  // 所有重试都失败后抛出最后一个错误
  throw lastError || new Error('火山引擎API调用失败，已达到最大重试次数');
};

/**
 * 根据配置调用相应的LLM服务
 * @param {string} prompt 提示内容
 * @param {Object} options 选项
 * @returns {Promise<string>} LLM生成的文本内容
 */
const callLlmService = async (prompt, options = {}) => {
  const config = await getLlmConfig();
  let response;
  
  try {
    // 在开发模式下，打印调试信息
    if (process.env.NODE_ENV === 'development') {
      console.log('===== 开始调用LLM服务 =====');
      console.log('使用提供商:', config.provider);
      console.log('火山引擎模型:', config.volcengineModel);
      console.log('提示内容长度:', prompt.length);
      console.log('====================');
      
      // 如果设置了仅调试模式，返回模拟响应
      if (process.env.LLM_DEBUG_ONLY === 'true') {
        console.log('LLM调试模式：返回模拟响应而不实际调用API');
        return {
          rawResponse: { debug: true },
          content: JSON.stringify({
            summary: '这是调试模式生成的AI报告摘要，未调用实际的LLM服务。',
            differentialDiagnosis: {
              possibleConditions: []
            },
            emergencyGuidance: {
              isEmergency: false,
              immediateActions: [],
              nextSteps: []
            },
            hospitalRecommendations: {
              targetRegion: options.targetRegion || '未指定',
              hospitals: []
            },
            treatmentPlan: {
              options: []
            },
            lifestyleAndMentalHealth: {
              lifestyle: {
                diet: ["保持均衡饮食，多摄入蔬果"],
                exercise: ["根据身体状况适当进行轻度运动"],
                habits: ["保持良好作息"]
              },
              mentalHealth: {
                copingStrategies: ["保持心情愉悦"],
                resources: ["可咨询专业心理医生"]
              }
            },
            dashboardData: {
              status: '调试模式',
              trend: 'stable',
              riskLevel: 'low',
              isEmergency: false,
              topHospital: '无',
              budgetRange: '无'
            },
            disclaimer: '本报告由AI生成，仅供参考，不构成医疗诊断或治疗建议。请咨询专业医生获取正式医疗意见。'
          })
        };
      }
    }
    
    if (config.provider === LLM_PROVIDERS.DEEPSEEK_OFFICIAL) {
      // 对于DeepSeek官方API，增加最大token数以支持更详细的回答
      const maxTokens = options.maxTokens || config.maxTokens || 8192;
      const enhancedOptions = { ...options, maxTokens };
      response = await callDeepSeekApi(prompt, enhancedOptions);
      
      // 添加更安全的响应处理，确保即使响应结构不符合预期也不会抛出错误
      let content = '';
      if (response && response.choices && response.choices.length > 0 && response.choices[0].message) {
        content = response.choices[0].message.content || '';
      } else if (response && response.output && response.output.text) {
        // 适配可能的另一种返回格式
        content = response.output.text;
      } else if (response && typeof response.text === 'string') {
        // 再一种可能的格式
        content = response.text;
      } else {
        console.log('无法解析LLM响应，返回空内容');
        console.log('实际响应结构:', JSON.stringify(response, null, 2));
        content = '';
      }
      
      return {
        rawResponse: response,
        content: content
      };
    } else if (config.provider === LLM_PROVIDERS.VOLCENGINE) {
      // 对于火山引擎API，同样增加最大token数
      const maxTokens = options.maxTokens || config.maxTokens || 8192;
      const enhancedOptions = { ...options, maxTokens };
      response = await callVolcengineApi(prompt, enhancedOptions);
      
      // 添加更安全的响应处理，确保即使响应结构不符合预期也不会抛出错误
      let content = '';
      console.log('处理火山引擎API响应');
      
      // 打印完整响应内容以便于调试
      if (process.env.NODE_ENV === 'development') {
        console.log('火山引擎API原始响应:', JSON.stringify(response, null, 2));
      }

      // 火山引擎API基于OpenAI格式，返回应该是choices数组
      if (response && response.choices && response.choices.length > 0) {
        if (response.choices[0].message && response.choices[0].message.content) {
          // 标准的OpenAI格式
          content = response.choices[0].message.content;
          console.log('使用标准格式解析成功');
        } else if (response.choices[0].text) {
          // 替代格式
          content = response.choices[0].text;
          console.log('使用替代格式解析成功');
        }
      } else if (response && response.output && response.output.text) {
        // 过去版本的火山引擎API格式
        content = response.output.text;
        console.log('使用旧版API格式解析成功');
      } else if (response && typeof response.text === 'string') {
        // 另一种可能的格式
        content = response.text;
        console.log('使用text字段解析成功');
      } else if (response && typeof response.content === 'string') {
        // 直接返回内容
        content = response.content;
        console.log('使用content字段解析成功');
      } else {
        console.warn('无法解析火山引擎API响应，返回空内容');
        console.warn('响应结构:', JSON.stringify(response, null, 2));
        content = '';
      }
      
      return {
        rawResponse: response,
        content: content
      };
    } else {
      throw new Error(`未支持的LLM提供商: ${config.provider}`);
    }
  } catch (error) {
    console.error('调用LLM服务失败:', error);
    // 增加错误详情
    if (error.response) {
      console.error('API响应状态码:', error.response.status);
      console.error('API响应数据:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.error('API请求未收到响应，可能是网络问题或API超时');
      console.error('请求详情:', error.request);
    } else {
      console.error('发送请求前出错:', error.message);
    }
    
    // 启用调试/开发模式下的后备机制，返回模拟响应
    if (process.env.NODE_ENV === 'development' && process.env.LLM_ALWAYS_USE_FALLBACK === 'true') {
      console.log('启用后备机制，返回模拟响应');
      return {
        rawResponse: { 
          error: true,
          errorMessage: error.message 
        },
        content: JSON.stringify({
          summary: '由于API调用错误，这是系统自动生成的后备响应。建议联系医生获取专业意见。',
          differentialDiagnosis: {
            possibleConditions: []
          },
          emergencyGuidance: {
            isEmergency: false,
            immediateActions: ["请联系您的医生获取专业建议"],
            nextSteps: ["系统遇到技术问题，请稍后重试"]
          },
          hospitalRecommendations: {
            targetRegion: options.targetRegion || '未指定',
            hospitals: []
          },
          treatmentPlan: {
            options: []
          },
          lifestyleAndMentalHealth: {
            lifestyle: {
              diet: ["保持均衡饮食，多摄入蔬果"],
              exercise: ["根据身体状况适当进行轻度运动"],
              habits: ["保持良好作息"]
            },
            mentalHealth: {
              copingStrategies: ["保持心情愉悦"],
              resources: ["可咨询专业心理医生"]
            }
          },
          dashboardData: {
            status: '系统错误',
            trend: 'stable',
            riskLevel: 'medium',
            isEmergency: false,
            topHospital: '无',
            budgetRange: '无'
          },
          disclaimer: '本报告由系统自动生成，因技术原因未能提供完整分析。这不构成医疗建议，请咨询专业医生。'
        })
      };
    }
    
    // 非调试模式或未启用后备机制时，将错误向上传递
    console.error('LLM调用失败，将向上传递错误');
    throw new Error(`LLM服务调用失败: ${error.message}`);
  }
};

/**
 * 调用LLM模型，是callLlmService的一个包装函数，提供更简单的接口
 * @param {string} prompt 提示内容
 * @param {Object} options 选项参数，可以包含timeout和maxRetries等
 * @returns {Promise<string>} LLM生成的响应内容
 */
const callLLMModel = async (prompt, options = {}) => {
  try {
    // 记录开始时间，用于计算耗时
    const startTime = Date.now();
    
    console.log('开始调用LLM模型，提示内容长度:', prompt.length);
    
    // 调用实际的LLM服务
    const result = await callLlmService(prompt, options);
    
    // 记录耗时
    const elapsedTime = Date.now() - startTime;
    console.log(`LLM模型调用完成，耗时: ${elapsedTime}ms`);
    
    // 返回生成的内容
    return result.content;
  } catch (error) {
    console.error('LLM模型调用失败:', error);
    throw new Error(`调用LLM模型失败: ${error.message}`);
  }
};

module.exports = {
  LLM_PROVIDERS,
  VOLCENGINE_MODELS,
  callLlmService,
  callVolcengineApi,
  callLLMModel,
  getLlmConfig
}; 