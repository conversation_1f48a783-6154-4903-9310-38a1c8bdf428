import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Avatar, 
  Chip, 
  useTheme
} from '@mui/material';
import { 
  PersonOutline as PersonIcon,
  AccessTime as TimeIcon 
} from '@mui/icons-material';
import { useAuthStore } from '../../store/authStore';

/**
 * 用户信息和日历组件
 * 显示用户头像、问候语、用户类型标签和当前日期
 */
const UserInfoCalendar: React.FC = () => {
  const theme = useTheme();
  const { user } = useAuthStore();
  
  // 获取当前时间和日期
  const [currentDate, setCurrentDate] = useState<Date>(new Date());
  const [greeting, setGreeting] = useState<string>('');
  
  // 更新问候语和时间
  useEffect(() => {
    // 设置问候语
    const updateGreeting = () => {
      const hours = new Date().getHours();
      if (hours >= 5 && hours < 12) {
        setGreeting('早上好');
      } else if (hours >= 12 && hours < 14) {
        setGreeting('中午好');
      } else if (hours >= 14 && hours < 18) {
        setGreeting('下午好');
      } else {
        setGreeting('晚上好');
      }
    };

    // 更新当前时间
    const updateTime = () => {
      setCurrentDate(new Date());
    };

    // 初始化
    updateGreeting();
    updateTime();

    // 每分钟更新一次时间
    const timer = setInterval(updateTime, 60000);
    
    return () => clearInterval(timer);
  }, []);

  // 计算用户使用天数
  const calculateDays = (): string => {
    if (user?.createdAt) {
      try {
        const now = new Date();
        const regDate = new Date(user.createdAt);
        
        // 计算时间差（毫秒）
        const diffTime = Math.abs(now.getTime() - regDate.getTime());
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        // 如果超过365天，显示年和天
        if (diffDays > 365) {
          const years = Math.floor(diffDays / 365);
          const remainingDays = diffDays % 365;
          return `${years} 年 ${remainingDays} 天`;
        } else {
          // 否则只显示天数
          return `${diffDays} 天`;
        }
      } catch (e) {
        console.error('计算用户使用天数出错:', e);
        return '1 天'; // 默认值
      }
    }
    return '1 天';
  };

  // 获取用户问候文本
  const getWelcomeText = (): string => {
    return `${greeting}，祝您健康愉快！`;
  };

  // 获取用户级别文本
  const getUserLevelText = (): string => {
    if (!user?.level) return '基础版';
    
    switch(user.level) {
      case 'PROFESSIONAL':
        return '专业版';
      case 'FAMILY':
        return '家庭版';
      case 'PERSONAL':
      default:
        return '个人版';
    }
  };

  return (
    <Box sx={{ 
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      bgcolor: 'background.paper',
      borderRadius: 2,
      overflow: 'hidden',
      mb: 2.5,
      ml: '-10px',
      width: 'calc(100% + 10px)'
    }}>
      {/* 左侧用户信息 */}
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        p: 1.5
      }}>
        <Avatar
          src={user?.avatar}
          sx={{
            width: 64,
            height: 64,
            mr: 1.5
          }}
        >
          {user?.username ? user.username.charAt(0).toUpperCase() : <PersonIcon />}
        </Avatar>
        
        <Box>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.3 }}>
            <Typography
              variant="subtitle1"
              sx={{
                fontWeight: 'bold',
                mr: 1,
                fontSize: '0.8rem'
              }}
            >
              {user?.username || '专业用户'}
            </Typography>
            
            <Chip
              label={getUserLevelText()}
              size="small"
              color="primary"
              sx={{ 
                height: 16,
                fontSize: '0.55rem',
                fontWeight: 'bold'
              }}
            />
          </Box>
          
          <Typography
            variant="body2"
            sx={{ 
              color: 'text.secondary',
              mb: 0.3,
              fontSize: '0.7rem'
            }}
          >
            {getWelcomeText()}
          </Typography>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center',
            color: 'text.secondary',
            fontSize: '0.6rem'
          }}>
            <TimeIcon fontSize="small" sx={{ mr: 0.5, fontSize: '0.75rem' }} />
            <Typography variant="caption" sx={{ fontSize: '0.6rem' }}>相伴 {calculateDays()}</Typography>
          </Box>
        </Box>
      </Box>
      
      {/* 右侧日期显示 */}
      <Box sx={{
        p: 1.5,
        bgcolor: theme.palette.mode === 'light' ? 'primary.light' : 'primary.dark',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        color: theme.palette.mode === 'light' ? 'primary.contrastText' : 'primary.contrastText',
        minWidth: 80,
        borderRadius: '0 8px 8px 0',
        pl: 2,
        pr: 2
      }}>
        <Typography variant="caption" sx={{ fontWeight: 'medium', mb: 0.3, fontSize: '0.6rem' }}>
          {currentDate.getFullYear()}年
        </Typography>
        <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 0.3, fontSize: '0.9rem' }}>
          {currentDate.getMonth() + 1}月{currentDate.getDate()}日
        </Typography>
        <Typography variant="body2" sx={{ fontSize: '0.7rem' }}>
          星期{['日', '一', '二', '三', '四', '五', '六'][currentDate.getDay()]}
        </Typography>
      </Box>
    </Box>
  );
};

export default UserInfoCalendar; 