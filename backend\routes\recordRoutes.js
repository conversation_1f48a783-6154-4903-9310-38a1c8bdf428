/**
 * 记录路由管理
 * 提供记录的基本增删改查功能
 */
const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');
const { JWT_SECRET } = require('../src/config');
const knex = require('knex')(require('../knexfile').development);
const { Model } = require('objection');
const Record = require('../models/Record');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// 设置 Objection.js
Model.knex(knex);

// 配置上传存储
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../uploads');
    // 确保目录存在
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // 添加时间戳和UUID到文件名以避免重复
    const uniqueSuffix = Date.now() + '-' + uuidv4();
    
    // 处理中文文件名
    let originalname = file.originalname;
    
    // 检查是否有前端传过来的原始文件名，使用它代替file.originalname
    if (req.body && req.body.originalFileName) {
      console.log(`使用前端传递的原始文件名: ${req.body.originalFileName}`);
      originalname = req.body.originalFileName;
    } else if (req.body && req.body.encodedFileName) {
      // 如果有编码的文件名，尝试解码
      try {
        console.log(`尝试解码文件名: ${req.body.encodedFileName}`);
        originalname = decodeURIComponent(req.body.encodedFileName);
        console.log(`解码后的文件名: ${originalname}`);
      } catch (err) {
        console.error('解码文件名失败:', err);
        // 出错时使用原始名称
        originalname = req.body.encodedFileName;
      }
    }
    
    // 使用Buffer来处理可能的二进制数据，避免乱码
    try {
      // 保存原始文件名作为日志
      const originalFileName = originalname;
      // 转换为Buffer并使用UTF-8编码
      originalname = Buffer.from(originalname, 'binary').toString('utf8');
      
      console.log(`文件名编码转换: 原始=${originalFileName}, 转换后=${originalname}`);
      
      // 如果转换后出现了乱码或空字符串，回退到原始名称
      if (!originalname || /\ufffd/.test(originalname)) {
        console.log(`检测到文件名转换可能出现乱码，回退到原始名称`);
        originalname = originalFileName;
      }
    } catch (err) {
      console.error('文件名转换失败:', err);
    }
    
    // 确保文件名安全，移除不安全的字符
    // 这里不会移除中文，只移除系统路径中不安全的特殊字符
    const safeFilename = originalname.replace(/[\/\\:*?"<>|]/g, '_');
    
    console.log(`文件名处理: 原始=${file.originalname}, 最终安全名=${safeFilename}`);
    cb(null, uniqueSuffix + '-' + safeFilename);
  }
});

const upload = multer({ 
  storage: storage,
  limits: { file_size: 10 * 1024 * 1024 } // 限制文件大小为10MB
});

// 身份验证中间件
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers.authorization;
  if (!authHeader) return res.status(401).json({ error: '未授权' });
  
  const token = authHeader.split(' ')[1];
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) return res.status(403).json({ error: '令牌无效' });
    
    // 设置用户信息，同时使用驼峰命名和下划线命名
    req.user = user;
    req.userId = user.id;
    req.user_id = user.id;
    
    console.log('Records认证中间件 - 设置了以下用户ID:', {
      'req.user.id': req.user.id,
      'req.userId': req.userId,
      'req.user_id': req.user_id
    });
    
    next();
  });
};

// 访问验证中间件
const auth = (req, res, next) => {
  const token = req.headers['authorization']?.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: '未提供访问令牌' });
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    req.user_id = decoded.id;
    next();
  } catch (error) {
    return res.status(403).json({ error: '访问令牌无效或已过期' });
  }
};

// 检查并创建records表
const ensureRecordsTable = async () => {
  const hasTable = await knex.schema.hasTable('records');
  if (!hasTable) {
    await knex.schema.createTable('records', table => {
      table.uuid('id').primary();
      table.uuid('user_id').notNullable();
      table.uuid('patient_id').notNullable();
      table.uuid('disease_id').notNullable();
      table.string('title').notNullable();
      table.text('content').nullable();
      table.dateTime('record_date').notNullable();
      table.json('record_type').nullable();
      table.string('primary_type').nullable();
      table.string('stage_phase').nullable();
      table.string('stage_node').nullable();
      table.string('severity').nullable();
      table.boolean('is_important').defaultTo(false);
      table.boolean('is_private').defaultTo(false);
      table.string('custom_tags').nullable();
      table.uuid('created_by').notNullable();
      table.timestamp('created_at').defaultTo(knex.fn.now());
      table.timestamp('updated_at').defaultTo(knex.fn.now());
      table.uuid('updated_by').nullable(); // 记录最后修改者
      table.timestamp('deleted_at').nullable();
      table.uuid('deleted_by').nullable(); // 记录删除操作者
      table.json('data').nullable(); // 结构化数据
    });
    console.log('已创建records表');
  } else {
    // 检查是否已存在updated_by字段，不存在则添加
    const hasUpdatedByField = await knex.schema.hasColumn('records', 'updated_by');
    if (!hasUpdatedByField) {
      await knex.schema.table('records', table => {
        table.uuid('updated_by').nullable();
        console.log('已添加updated_by字段到records表');
      });
    }
    
    // 检查是否已存在deleted_by字段，不存在则添加
    const hasDeletedByField = await knex.schema.hasColumn('records', 'deleted_by');
    if (!hasDeletedByField) {
      await knex.schema.table('records', table => {
        table.uuid('deleted_by').nullable();
        console.log('已添加deleted_by字段到records表');
      });
    }
  }
  
  // 检查附件表
  const hasAttachmentsTable = await knex.schema.hasTable('attachments');
  if (!hasAttachmentsTable) {
    await knex.schema.createTable('attachments', table => {
      table.uuid('id').primary();
      table.uuid('record_id').notNullable();
      table.string('file_name').notNullable();
      table.string('file_path').notNullable();
      table.string('file_type').nullable();
      table.integer('file_size').nullable();
      table.uuid('uploaded_by').notNullable();
      table.timestamp('uploaded_at').defaultTo(knex.fn.now());
    });
    console.log('已创建attachments表');
  }
};

// 创建新记录
router.post('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const recordData = req.body;
    
    console.log('接收到的记录数据:', JSON.stringify(recordData, null, 2));
    console.log('用户ID:', userId);
    
    // 验证必填字段 - 同时支持驼峰命名和下划线命名
    const patientId = recordData.patient_id || recordData.patientId;
    const diseaseId = recordData.disease_id || recordData.diseaseId;
    const title = recordData.title;
    
    console.log('验证字段:', {
      patientId,
      diseaseId,
      title,
      userId
    });
    
    if (!patientId || !diseaseId || !title) {
      console.log('创建记录验证失败:', {
        patientId: patientId || '缺失',
        diseaseId: diseaseId || '缺失',
        title: title || '缺失'
      });
      return res.status(400).json({ error: '患者、病理和标题为必填项' });
    }
    
    // 处理记录类型 - 确保它是JSON格式
    // 优先使用驼峰命名字段，如果不存在则使用下划线命名字段
    let recordType = recordData.recordType || recordData.record_type || [];
    if (typeof recordType === 'string') {
      try {
        recordType = JSON.parse(recordType);
      } catch (e) {
        // 如果解析失败，可能是逗号分隔的字符串
        recordType = recordType.split(',').map(type => type.trim());
      }
    }
    
    console.log('处理后的记录类型:', recordType);
    
    // 预处理data字段
    let data = recordData.data || {};
    console.log('接收到的data字段:', data, '类型:', typeof data);
    
    // 如果data是字符串，尝试解析为对象
    if (typeof data === 'string' && data.trim()) {
      try {
        data = JSON.parse(data);
        console.log('解析data字符串成功:', data);
      } catch (e) {
        console.log('无法解析data字段字符串为JSON:', e);
        data = {};
      }
    }
    
    // 确保data是对象
    if (!data || typeof data !== 'object' || Array.isArray(data)) {
      console.log('data字段非对象，重置为空对象');
      data = {};
    }
    
    // 处理severity字段
    let severity = recordData.severity || recordData.severity || 'MODERATE';
    try {
      // 确保severity是有效的枚举值
      const validSeverities = ['MILD', 'MODERATE', 'SEVERE', 'CRITICAL'];
      if (typeof severity === 'string') {
        // 如果是数字字符串，转换为对应的枚举
        if (!isNaN(parseInt(severity))) {
          const severityNum = parseInt(severity);
          switch (severityNum) {
            case 1: severity = 'MILD'; break;
            case 2: severity = 'MODERATE'; break;
            case 3: severity = 'SEVERE'; break;
            case 4: severity = 'CRITICAL'; break;
            default: severity = 'MODERATE';
          }
        } else {
          // 如果不是数字，转为大写并检查是否是有效的枚举值
          severity = severity.toUpperCase();
          if (!validSeverities.includes(severity)) {
            severity = 'MODERATE';
          }
        }
      } else if (typeof severity === 'number') {
        // 如果是数字，转换为对应的枚举
        switch (severity) {
          case 1: severity = 'MILD'; break;
          case 2: severity = 'MODERATE'; break;
          case 3: severity = 'SEVERE'; break;
          case 4: severity = 'CRITICAL'; break;
          default: severity = 'MODERATE';
        }
      } else {
        severity = 'MODERATE';
      }
    } catch (e) {
      console.log('处理severity字段失败:', e);
      severity = 'MODERATE';
    }
    console.log('处理后的severity值:', severity);
    
    // 获取字段值，优先使用驼峰命名，其次用下划线命名
    const getValue = (camelCase, snakeCase, defaultValue = null) => {
      return recordData[camelCase] !== undefined ? recordData[camelCase] : 
             (recordData[snakeCase] !== undefined ? recordData[snakeCase] : defaultValue);
    };
    
    // 构建插入对象
    const insertObj = {
      id: uuidv4(),
      userId: userId,
      user_id: userId, // 同时添加下划线版本
      patientId: patientId,
      diseaseId: diseaseId,
      title: title,
      content: getValue('content', 'content', ''),
      recordDate: getValue('recordDate', 'record_date', new Date().toISOString()),
      recordType: JSON.stringify(recordType),
      record_type: JSON.stringify(recordType), // 同时添加下划线版本
      primaryType: getValue('primaryType', 'primary_type', ''),
      stagePhase: getValue('stagePhase', 'stage_phase', ''),
      stageNode: getValue('stageNode', 'stage_node', ''),
      severity: severity,
      isImportant: getValue('isImportant', 'is_important', false) === true,
      isPrivate: getValue('isPrivate', 'is_private', false) === true,
      customTags: getValue('customTags', 'custom_tags', ''),
      createdBy: userId,
      data: data // 直接使用处理好的data对象
    };
    
    console.log('准备插入的记录数据:', JSON.stringify(insertObj, null, 2));
    
    // 创建新记录
    const newRecord = await Record.query().insert(insertObj);
    
    console.log('记录创建成功:', newRecord.id);
    
    // 返回创建的记录
    res.status(201).json(newRecord);
  } catch (error) {
    console.error('创建记录失败 - 详细错误:', error);
    console.error('错误堆栈:', error.stack);
    if (error.data) {
      console.error('验证错误数据:', error.data);
    }
    res.status(500).json({ error: error.message });
  }
});

// 获取记录总数
router.get('/count', auth, async (req, res) => {
  try {
    const userId = req.user_id;
    console.log('获取用户记录总数, 用户ID:', userId);
    
    // 查询当前用户的所有记录数量，排除已删除的记录
    const recordCount = await knex('records')
      .where('user_id', userId)
      .where(builder => {
        // 排除已删除的记录
        builder.whereNull('deleted_at');
        
        // 如果存在is_deleted列，过滤掉已删除的
        if (knex.schema.hasColumn('records', 'is_deleted')) {
          builder.where('is_deleted', 0);
        }
        
        // 如果存在is_active列，只统计活跃的
        if (knex.schema.hasColumn('records', 'is_active')) {
          builder.where('is_active', 1);
        }
      })
      .count('id as count')
      .first();
    
    console.log('用户记录总数查询结果:', recordCount);
    
    res.json({
      count: recordCount ? parseInt(recordCount.count) : 0
    });
  } catch (error) {
    console.error('获取记录总数失败:', error);
    res.status(500).json({ error: '获取记录总数失败' });
  }
});

// 获取记录列表
router.get('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { 
      patient_id, patientId, 
      disease_id, diseaseId, 
      is_important, isImportant,
      recordType, record_type,
      severity,
      q,
      include_all_users,
      skip = 0, limit = 10
    } = req.query;
    
    console.log('获取记录列表参数:', {
      userId,
      patient_id, patientId,
      disease_id, diseaseId,
      is_important, isImportant,
      recordType, record_type,
      severity,
      q,
      include_all_users,
      skip, limit
    });
    
    // 构建查询
    let query = Record.query()
      .where('is_deleted', false) // 仅获取未软删除的记录
      .where('user_id', userId); // 仅获取当前用户的记录

    console.log(`应用用户筛选: user_id=${userId}`);

    // 添加过滤条件
    const finalPatientId = patient_id || patientId; // 获取存在的患者ID
    if (finalPatientId) {
      query = query.where('patient_id', finalPatientId);
      console.log(`应用患者筛选: patient_id=${finalPatientId}`);
    }
    
    const finalDiseaseId = disease_id || diseaseId; // 获取存在的病理ID
    if (finalDiseaseId) {
      query = query.where('disease_id', finalDiseaseId);
      console.log(`应用病理筛选: disease_id=${finalDiseaseId}`);
    }
    
    const finalIsImportant = is_important !== undefined ? (is_important === 'true') : 
                             (isImportant !== undefined ? (isImportant === true) : undefined);
    if (finalIsImportant !== undefined) {
      query = query.where('is_important', finalIsImportant);
      console.log(`应用重要标记筛选: is_important=${finalIsImportant}`);
    }
    
    const finalRecordType = recordType || record_type;
    if (finalRecordType) {
      query = query.whereRaw(`
      (
        -- Case 1: record_type is a JSON array string and finalRecordType is an element
        EXISTS (
          SELECT 1
          FROM jsonb_array_elements_text(
            CASE
              WHEN record_type IS NULL OR record_type = '' THEN '[]'::jsonb -- If null or empty, treat as empty array
              ELSE
                CASE
                  -- Only attempt to use record_type as a jsonb array if it actually is one after casting
                  WHEN jsonb_typeof(record_type::jsonb) = 'array' THEN record_type::jsonb
                  ELSE '[]'::jsonb -- Otherwise (e.g., it's a json scalar or invalid json that became a scalar), treat as empty array for element extraction
                END
            END
          ) AS elem
          WHERE elem.value = ? -- Parameter 1: finalRecordType
        )
        OR 
        -- Case 2: record_type is plain text and directly equals finalRecordType
        record_type = ? -- Parameter 2: finalRecordType
      )
      `, [finalRecordType, finalRecordType]);
      console.log(`应用记录类型筛选 (PostgreSQL v4 compatible): record_type=${finalRecordType}`);
    }
    if (severity) {
      query = query.where('severity', severity);
      console.log(`应用严重程度筛选: severity=${severity}`);
    }
    if (q) {
      // 记录类型中英文映射，用于支持中文搜索
      const typeNameMap = {
        // 中文 -> 英文
        '自述': 'SELF_DESCRIPTION',
        '症状': 'SYMPTOM',
        '检查': 'EXAMINATION',
        '化验': 'LAB_TEST',
        '诊断': 'DIAGNOSIS',
        '治疗': 'TREATMENT',
        '住院': 'HOSPITALIZATION',
        '用药': 'MEDICATION',
        '手术': 'SURGERY',
        '监测': 'MONITORING',
        '理疗': 'PHYSICAL_THERAPY',
        '出院': 'DISCHARGE',
        '预约': 'APPOINTMENT',
        '报告': 'REPORT',
        '随访': 'FOLLOW_UP',
        '预后': 'PROGNOSIS',
        '辅诊': 'AUX_DIAGNOSIS',
        '护理': 'NURSING',
        '复诊': 'REVISIT',
        '转诊': 'REFERRAL',
        '心理': 'PSYCHOLOGY',
        '康复': 'REHABILITATION',
        '评估': 'ASSESSMENT',
        '其他': 'OTHER',
        // 额外的类型
        '鉴别诊断': 'DIFFERENTIAL_DIAGNOSIS',
        '确诊': 'DIAGNOSIS_CONFIRMATION',
        '治疗方案': 'TREATMENT_PLAN',
        '治疗调整': 'TREATMENT_ADJUSTMENT',
        '处方': 'PRESCRIPTION',
        '药物调整': 'MEDICATION_ADJUSTMENT',
        '影像': 'IMAGING',
        '复查': 'REVIEW',
        '就诊': 'VISIT',
        '会诊': 'CONSULTATION',
        '主诉': 'CHIEF_COMPLAINT',
        '病情变化': 'CONDITION_CHANGE',
        '备注': 'NOTE',
        '评论': 'COMMENT'
      };
      
      // 严重程度中英文映射
      const severityMap = {
        '轻微': 'MILD',
        '中等': 'MODERATE',
        '严重': 'SEVERE',
        '危重': 'CRITICAL'
      };
      
      // 阶段/节点中英文映射
      const stagePhaseMap = {
        '初诊阶段': 'INITIAL',
        '确诊阶段': 'DIAGNOSIS',
        '治疗阶段': 'TREATMENT',
        '康复阶段': 'RECOVERY',
        '预后阶段': 'PROGNOSIS'
      };
      
      const stageNodeMap = {
        '初诊': 'INITIAL_VISIT',
        '确诊': 'DIAGNOSIS',
        '治疗': 'TREATMENT',
        '随访': 'FOLLOW_UP',
        '预后': 'PROGNOSIS',
        '封档': 'ARCHIVE'
      };
      
      const keywords = q.split(/\s+/).filter(keyword => keyword.trim() !== '');
      query = query.where(builder => {
        keywords.forEach(keyword => {
          builder.orWhere(subBuilder => {
            subBuilder.orWhere('title', 'like', `%${keyword}%`)
              .orWhere('content', 'like', `%${keyword}%`)
              .orWhere('custom_tags', 'like', `%${keyword}%`)
              // Cast record_date to TEXT before applying LIKE
              .orWhereRaw(`"record_date"::text LIKE ?`, [`%${keyword}%`])
              .orWhere('primary_type', 'like', `%${keyword}%`)
              // PostgreSQL compatible search for keyword within record_type (TEXT column with JSON array string or plain text)
              .orWhereRaw(`(
                EXISTS (
                  SELECT 1
                  FROM jsonb_array_elements_text(
                    CASE
                      WHEN record_type IS NULL OR record_type = '' THEN '[]'::jsonb
                      ELSE CASE WHEN jsonb_typeof(record_type::jsonb) = 'array' THEN record_type::jsonb ELSE '[]'::jsonb END
                    END
                  ) AS elem
                  WHERE elem.value ILIKE ? 
                ) OR record_type ILIKE ?
              )`, [`%${keyword}%`, `%${keyword}%`])
              .orWhere('stage_phase', 'like', `%${keyword}%`)
              .orWhere('stage_node', 'like', `%${keyword}%`);

            const englishTypeKeyword = typeNameMap[keyword];
            if (englishTypeKeyword) {
              subBuilder
                .orWhere('primary_type', englishTypeKeyword)
                // PostgreSQL compatible search for englishTypeKeyword within record_type
                .orWhereRaw(`(
                  EXISTS (
                    SELECT 1
                    FROM jsonb_array_elements_text(
                      CASE
                        WHEN record_type IS NULL OR record_type = '' THEN '[]'::jsonb
                        ELSE CASE WHEN jsonb_typeof(record_type::jsonb) = 'array' THEN record_type::jsonb ELSE '[]'::jsonb END
                      END
                    ) AS elem
                    WHERE elem.value = ?
                  ) OR record_type = ?
                )`, [englishTypeKeyword, englishTypeKeyword]);
            }
            
            const englishSeverityKeyword = severityMap[keyword];
            if (englishSeverityKeyword) {
              subBuilder.orWhere('severity', englishSeverityKeyword);
            }
            
            const englishStagePhase = stagePhaseMap[keyword];
            if (englishStagePhase) {
              subBuilder.orWhere('stage_phase', englishStagePhase);
            }
            
            const englishStageNode = stageNodeMap[keyword];
            if (englishStageNode) {
              subBuilder.orWhere('stage_node', englishStageNode);
            }
          });
        });
      });
      console.log(`应用搜索词筛选: q=${q}`);
    }

    // 获取总记录数（用于分页）
    const totalRecordsQuery = query.clone(); // 克隆查询以避免影响原始查询
    
    // 分页
    query = query.offset(parseInt(skip) || 0).limit(parseInt(limit) || 10);
    
    // 按记录日期降序排序
    query = query.orderBy('record_date', 'desc');
    
    console.log('SQL查询:', query.toKnexQuery().toString());
    
    const records = await query;
    
    // 如果启用了调试模式，添加搜索匹配信息
    const debug = req.query.debug === 'true';
    if (debug && q && records.length > 0) {
      console.log('启用搜索调试模式');
      
      // 记录类型中英文映射，用于支持中文搜索
      const typeNameMap = {
        // 中文 -> 英文
        '自述': 'SELF_DESCRIPTION',
        '症状': 'SYMPTOM',
        '检查': 'EXAMINATION',
        '化验': 'LAB_TEST',
        '诊断': 'DIAGNOSIS',
        '治疗': 'TREATMENT',
        '住院': 'HOSPITALIZATION',
        '用药': 'MEDICATION',
        '手术': 'SURGERY',
        '监测': 'MONITORING',
        '理疗': 'PHYSICAL_THERAPY',
        '出院': 'DISCHARGE',
        '预约': 'APPOINTMENT',
        '报告': 'REPORT',
        '随访': 'FOLLOW_UP',
        '预后': 'PROGNOSIS',
        '辅诊': 'AUX_DIAGNOSIS',
        '护理': 'NURSING',
        '复诊': 'REVISIT',
        '转诊': 'REFERRAL',
        '心理': 'PSYCHOLOGY',
        '康复': 'REHABILITATION',
        '评估': 'ASSESSMENT',
        '其他': 'OTHER',
        // 额外的类型
        '鉴别诊断': 'DIFFERENTIAL_DIAGNOSIS',
        '确诊': 'DIAGNOSIS_CONFIRMATION',
        '治疗方案': 'TREATMENT_PLAN',
        '治疗调整': 'TREATMENT_ADJUSTMENT',
        '处方': 'PRESCRIPTION',
        '药物调整': 'MEDICATION_ADJUSTMENT',
        '影像': 'IMAGING',
        '复查': 'REVIEW',
        '就诊': 'VISIT',
        '会诊': 'CONSULTATION',
        '主诉': 'CHIEF_COMPLAINT',
        '病情变化': 'CONDITION_CHANGE',
        '备注': 'NOTE',
        '评论': 'COMMENT'
      };
      
      // 严重程度中英文映射
      const severityMap = {
        '轻微': 'MILD',
        '中等': 'MODERATE',
        '严重': 'SEVERE',
        '危重': 'CRITICAL'
      };
      
      // 阶段/节点中英文映射
      const stagePhaseMap = {
        '初诊阶段': 'INITIAL',
        '确诊阶段': 'DIAGNOSIS',
        '治疗阶段': 'TREATMENT',
        '康复阶段': 'RECOVERY',
        '预后阶段': 'PROGNOSIS'
      };
      
      const stageNodeMap = {
        '初诊': 'INITIAL_VISIT',
        '确诊': 'DIAGNOSIS',
        '治疗': 'TREATMENT',
        '随访': 'FOLLOW_UP',
        '预后': 'PROGNOSIS',
        '封档': 'ARCHIVE'
      };
      
      // 记录搜索匹配情况
      const recordsWithDebugInfo = records.map(record => {
        // 解析记录类型
        let recordTypes = [];
        try {
          if (record.record_type) {
            recordTypes = typeof record.record_type === 'string' 
              ? JSON.parse(record.record_type) 
              : record.record_type;
          }
        } catch (e) {
          recordTypes = [];
        }
        
        // 检查匹配点
        const matchPoints = [];
        const keywords = q.split(/\s+/).filter(kw => kw.trim() !== '');
        
        keywords.forEach(keyword => {
          const keywordLower = keyword.toLowerCase();
          
          // 检查各字段是否包含关键词
          if (record.title && record.title.toLowerCase().includes(keywordLower)) {
            matchPoints.push(`标题包含"${keyword}"`);
          }
          
          if (record.content && record.content.toLowerCase().includes(keywordLower)) {
            matchPoints.push(`内容包含"${keyword}"`);
          }
          
          if (record.custom_tags && record.custom_tags.toLowerCase().includes(keywordLower)) {
            matchPoints.push(`自定义标签包含"${keyword}"`);
          }
          
          if (record.record_date && record.record_date.toLowerCase().includes(keywordLower)) {
            matchPoints.push(`记录日期包含"${keyword}"`);
          }
          
          if (record.primary_type && record.primary_type.toLowerCase().includes(keywordLower)) {
            matchPoints.push(`主要类型包含"${keyword}"`);
          }
          
          // 检查记录类型数组
          const typeMatches = recordTypes.filter(type => 
            type && type.toLowerCase().includes(keywordLower)
          );
          
          if (typeMatches.length > 0) {
            matchPoints.push(`记录类型包含"${keyword}"`);
          }
          
          // 检查中文映射
          const englishTypeKeyword = typeNameMap[keyword];
          if (englishTypeKeyword) {
            if (record.primary_type === englishTypeKeyword) {
              matchPoints.push(`主要类型匹配"${keyword}"(${englishTypeKeyword})`);
            }
            
            if (recordTypes.includes(englishTypeKeyword)) {
              matchPoints.push(`记录类型匹配"${keyword}"(${englishTypeKeyword})`);
            }
          }
          
          // 严重程度匹配
          const englishSeverityKeyword = severityMap[keyword];
          if (englishSeverityKeyword && record.severity === englishSeverityKeyword) {
            matchPoints.push(`严重程度匹配"${keyword}"(${englishSeverityKeyword})`);
          }
          
          // 病程阶段/节点匹配
          const englishStagePhase = stagePhaseMap[keyword];
          if (englishStagePhase && record.stage_phase === englishStagePhase) {
            matchPoints.push(`病程阶段匹配"${keyword}"(${englishStagePhase})`);
          }
          
          const englishStageNode = stageNodeMap[keyword];
          if (englishStageNode && record.stage_node === englishStageNode) {
            matchPoints.push(`病程节点匹配"${keyword}"(${englishStageNode})`);
          }
        });
        
        // 添加调试信息
        return {
          ...record,
          _debug: {
            matchPoints: matchPoints.length > 0 ? matchPoints : ['未找到明确匹配点，可能是SQL模糊查询匹配']
          }
        };
      });
      
      // 返回带调试信息的记录
      return res.json({
        records: recordsWithDebugInfo,
        meta: {
          currentPage: Math.floor(parseInt(skip) || 0 / parseInt(limit) || 10) + 1,
          totalItems: totalRecordsQuery.count('id as count').first(),
          itemsPerPage: parseInt(limit) || 10,
          searchInfo: {
            keywords: q ? q.split(/\s+/).filter(kw => kw.trim() !== '') : [],
            query: q
          }
        }
      });
    }

    const total = await totalRecordsQuery.count('id as count').first();

    res.json({
      records,
      meta: {
        currentPage: Math.floor(parseInt(skip) / parseInt(limit)) + 1,
        totalItems: total ? parseInt(total.count) : 0,
        itemsPerPage: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('获取记录列表失败:', error);
    console.error('错误堆栈:', error.stack);
    res.status(500).json({ error: error.message });
  }
});

// 获取单个记录
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const { include_all_users, service_context, include_deleted } = req.query;
    
    // 增加更详细的日志输出
    console.log('[记录详情] 请求参数:', {
      记录ID: id,
      用户ID: userId,
      用户角色: req.user.role,
      包含所有用户: include_all_users,
      服务场景: service_context,
      包含已删除: include_deleted,
      完整请求参数: req.query
    });
    
    // 基础查询，包含附件
    let query = Record.query()
      .where('id', id);
    
    // 检查是否包含已删除的记录
    const shouldIncludeDeleted = include_deleted === 'true' || (
      service_context === 'true' && req.user.role === 'SERVICE'
    );
    
    if (!shouldIncludeDeleted) {
      query = query.where('is_deleted', false);
    }
    
    // 执行查询，附加关联的附件
    const record = await query.withGraphFetched('attachments').first();
      
    if (!record) {
      console.log('[记录详情] 记录不存在:', id);
      return res.status(404).json({ 
        error: '记录不存在',
        message: '请求的记录不存在或已被删除'
      });
    }
    
    console.log('[记录详情] 找到记录:', {
      记录ID: id, 
      记录所属用户ID: record.userId || record.user_id,
      记录标题: record.title,
      创建时间: record.created_at,
      删除时间: record.deleted_at,
      记录字段数: Object.keys(record).length
    });
    
    // 检查是否已软删除
    const isDeleted = record.deleted_at !== null;
    if (isDeleted && !shouldIncludeDeleted) {
      console.log('[记录详情] 记录已被删除，且请求不包含已删除记录:', id);
      return res.status(404).json({ 
        error: '记录已删除',
        message: '该记录已被删除，无法访问'
      });
    }
    
    // 检查权限 - 放宽权限控制，如果请求中指定include_all_users=true或者是自己的记录，或者是服务场景，都允许访问
    const includeAllUsers = String(include_all_users).toLowerCase() === 'true';
    const isServiceContext = String(service_context).toLowerCase() === 'true';
    const isUserRecord = record.userId === userId || record.user_id === userId || record.created_by === userId;
    const hasAccess = includeAllUsers || isUserRecord || (isServiceContext && req.user.role === 'SERVICE');
    
    if (!hasAccess) {
      console.log('[记录详情] 无权访问记录:', { 
        记录用户ID: record.userId || record.user_id, 
        请求用户ID: userId,
        包含所有用户: includeAllUsers,
        服务场景: isServiceContext,
        用户自己的记录: isUserRecord,
        用户角色: req.user.role
      });
      return res.status(403).json({ 
        error: '无权访问',
        message: '您没有权限访问此记录' 
      });
    }
    
    // 序列化记录数据，确保所有字段正常
    const serializedRecord = {
      ...record,
      // 确保字段不被压缩或修改
      id: record.id,
      title: record.title,
      content: record.content,
      createdAt: record.created_at,
      updatedAt: record.updated_at,
      // 添加记录状态信息
      recordStatus: isDeleted ? 'DELETED' : 'ACTIVE',
      isDeleted: isDeleted
    };
    
    console.log('[记录详情] 访问成功，返回记录字段:', Object.keys(serializedRecord));
    res.json(serializedRecord);
  } catch (error) {
    console.error('[记录详情] 访问失败:', error);
    res.status(500).json({ error: error.message });
  }
});

// 更新记录
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    await ensureRecordsTable();
    
    const { id } = req.params;
    const userId = req.user.id;
    const updateData = req.body;
    
    console.log('接收到的更新数据:', updateData);
    
    // 检查记录是否存在
    const record = await knex('records')
      .where('id', id)
      // .where('is_deleted', false) // Removed to allow updating soft-deleted records
      .first();
      
    if (!record) {
      return res.status(404).json({ error: '记录不存在' });
    }
    
    // 检查权限 - 只能更新自己的记录
    if (record.user_id !== userId && record.created_by !== userId) {
      return res.status(403).json({ error: '无权更新此记录' });
    }
    
    // 处理记录类型 - 支持驼峰命名
    let recordType = updateData.recordType || updateData.record_type || record.record_type;
    if (typeof recordType === 'string') {
      try {
        recordType = JSON.parse(recordType);
      } catch (e) {
        // 如果解析失败，可能是逗号分隔的字符串
        recordType = recordType.split(',').map(type => type.trim());
      }
    } else if (Array.isArray(recordType)) {
      // 已经是数组，不需要处理
    } else {
      recordType = [];
    }
    
    // 获取字段值，优先使用驼峰命名，其次用下划线命名
    const getValue = (camelCase, snakeCase, defaultValue = null) => {
      return updateData[camelCase] !== undefined ? updateData[camelCase] : 
             (updateData[snakeCase] !== undefined ? updateData[snakeCase] : defaultValue);
    };
    
    // 更新记录
    await knex('records')
      .where('id', id)
      .update({
        title: getValue('title', 'title', record.title),
        content: getValue('content', 'content', record.content),
        record_date: getValue('recordDate', 'record_date', record.record_date),
        record_type: JSON.stringify(recordType),
        primary_type: getValue('primaryType', 'primary_type', record.primary_type),
        stage_phase: getValue('stagePhase', 'stage_phase', record.stage_phase),
        stage_node: getValue('stageNode', 'stage_node', record.stage_node),
        severity: getValue('severity', 'severity', record.severity),
        is_important: getValue('isImportant', 'is_important', record.is_important),
        is_private: getValue('isPrivate', 'is_private', record.is_private),
        custom_tags: getValue('customTags', 'custom_tags', record.custom_tags),
        updated_at: knex.fn.now(),
        updated_by: userId, // 记录更新操作者
        data: getValue('data', 'data') ? 
          JSON.stringify(getValue('data', 'data')) : record.data
      });
    
    console.log('记录更新成功:', id);
    
    // 返回更新后的记录，使用Objection.js查询以获取驼峰命名的结果
    const updatedRecord = await Record.query().findById(id);
    res.json(updatedRecord);
  } catch (error) {
    console.error('更新记录失败:', error);
    res.status(500).json({ error: error.message });
  }
});

// 删除记录 (软删除)
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    await ensureRecordsTable();
    
    const { id } = req.params;
    const userId = req.user.id;
    const { service_context } = req.query;
    
    // 检查是否在服务上下文中操作
    const isServiceContext = service_context === 'true' || service_context === true;
    
    console.log('删除记录请求:', {
      记录ID: id,
      用户ID: userId,
      服务上下文: isServiceContext
    });
    
    // 检查记录是否存在
    const record = await knex('records')
      .where('id', id)
      .first();
      
    if (!record) {
      return res.status(404).json({ error: '记录不存在' });
    }
    
    // 检查权限 - 只能删除自己的记录，但如果是服务上下文则跳过此检查
    if (!isServiceContext && record.user_id !== userId && record.created_by !== userId) {
      console.log('权限检查失败:', {
        记录用户ID: record.user_id,
        记录创建者ID: record.created_by,
        当前用户ID: userId,
        服务上下文: isServiceContext
      });
      return res.status(403).json({ error: '无权删除此记录' });
    }
    
    if (isServiceContext) {
      console.log('通过服务上下文删除记录:', id);
    }
    
    // 软删除 - 设置is_deleted和deleted_by
    const updateData = {
      is_deleted: true,
      updated_at: knex.fn.now()
    };
    
    // 添加删除者信息，确保记录服务用户的操作
    updateData.deleted_by = userId;
    
    // 如果表中有updated_by字段，也更新它
    try {
      const hasUpdatedByField = await knex.schema.hasColumn('records', 'updated_by');
      if (hasUpdatedByField) {
        updateData.updated_by = userId;
      }
      
      console.log('删除记录更新数据:', {
        记录ID: id,
        更新字段: Object.keys(updateData),
        操作用户: userId,
        是否服务上下文: isServiceContext
      });
    } catch (err) {
      console.error('检查updated_by字段失败:', err);
    }
    
    await knex('records')
      .where('id', id)
      .update(updateData);
    
    console.log('记录删除成功:', id);
    res.json({ message: '记录已删除' });
  } catch (error) {
    console.error('删除记录失败:', error);
    res.status(500).json({ error: error.message });
  }
});

// 上传附件
router.post('/attachments/upload', authenticateToken, upload.single('file'), async (req, res) => {
  try {
    await ensureRecordsTable();
    
    if (!req.file) {
      return res.status(400).json({ error: '未找到上传的文件' });
    }
    
    // 支持驼峰命名和下划线命名
    const recordId = req.body.recordId || req.body.record_id;
    const userId = req.user.id;
    const serviceContext = req.body.service_context === 'true';
    
    // 处理原始文件名
    let originalFileName = req.file.originalname;
    
    // 如果前端传来了originalFileName，使用它作为真实的文件名
    if (req.body.originalFileName) {
      originalFileName = req.body.originalFileName;
      console.log(`使用前端提供的原始文件名: ${originalFileName}`);
    } else if (req.body.encodedFileName) {
      try {
        originalFileName = decodeURIComponent(req.body.encodedFileName);
        console.log(`解码后的文件名: ${originalFileName}`);
      } catch (err) {
        console.error('解码文件名失败:', err);
      }
    }
    
    console.log(`上传附件请求 - 记录ID: ${recordId}, 用户ID: ${userId}, 服务上下文: ${serviceContext}, 文件名: ${originalFileName}`);
    
    // 检查记录ID是否存在，这是必需的参数
    if (!recordId) {
      // 删除已上传的文件
      fs.unlinkSync(req.file.path);
      return res.status(400).json({ error: '记录ID为必填项' });
    }
    
    // 检查记录是否存在
    const record = await knex('records')
      .where('id', recordId)
      .first();
      
    if (!record) {
      // 删除已上传的文件
      fs.unlinkSync(req.file.path);
      return res.status(404).json({ error: '记录不存在' });
    }
    
    // 检查权限
    if (serviceContext) {
      // 服务用户上下文 - 需要检查授权关系
      console.log(`服务上下文上传附件 - 用户ID: ${userId}, 记录ID: ${recordId}`);
      
      try {
        // 获取记录所属用户和记录创建者
        const recordUserId = record.user_id;
        const recordCreatedBy = record.created_by;
        
        console.log(`检查服务记录关联 - 记录归属: ${recordUserId}, 创建者: ${recordCreatedBy}`);
        
        // 通过记录ID查找相关的服务记录关联
        const serviceRecord = await knex('service_records')
          .where('record_id', recordId)
          .first();
          
        if (!serviceRecord) {
          console.log(`未找到服务记录关联`);
          fs.unlinkSync(req.file.path);
          return res.status(403).json({ error: '无权访问此记录' });
        }
        
        console.log(`找到服务记录关联: ${JSON.stringify(serviceRecord)}`);
        
        // 获取授权关系 - 确保使用user_authorizations表
        const authorization = await knex('user_authorizations')
          .where('id', serviceRecord.authorization_id)
          .where('authorized_id', userId) // 确保当前用户是被授权者
          .where('status', 'ACTIVE')
          .first();
          
        if (!authorization) {
          console.log(`未找到有效的授权关系，授权ID: ${serviceRecord.authorization_id}, 用户ID: ${userId}`);
          fs.unlinkSync(req.file.path);
          return res.status(403).json({ error: '无效的授权关系' });
        }
        
        console.log(`找到授权关系: ${JSON.stringify(authorization)}`);
      } catch (error) {
        console.error('检查服务上下文权限时出错:', error);
        fs.unlinkSync(req.file.path);
        return res.status(500).json({ error: '检查权限时发生错误' });
      }
    } else if (record.user_id !== userId && record.created_by !== userId) {
      // 非服务上下文，执行常规权限检查
      // 删除已上传的文件
      fs.unlinkSync(req.file.path);
      return res.status(403).json({ error: '无权为此记录上传附件' });
    }
    
    // 保存附件信息
    const attachmentId = uuidv4();
    
    // 确保文件名正确编码
    let safeFileName = originalFileName;
    try {
      // 强制使用UTF-8编码确保中文文件名正确存储
      safeFileName = Buffer.from(originalFileName, 'binary').toString('utf8');
      
      // 检查编码结果
      if (!safeFileName || /\ufffd/.test(safeFileName)) {
        console.log(`数据库存储前检测到文件名可能有乱码，使用原始文件名`);
        safeFileName = originalFileName;
      }
      
      console.log(`文件名存入数据库前处理: 原始=${originalFileName}, 处理后=${safeFileName}`);
    } catch (err) {
      console.error('处理文件名编码失败:', err);
      safeFileName = originalFileName;
    }
    
    await knex('attachments').insert({
      id: attachmentId,
      record_id: recordId,
      file_name: safeFileName, // 使用处理后的文件名
      file_path: req.file.path,
      file_type: req.file.mimetype,
      file_size: req.file.size,
      uploaded_by: userId,
      uploaded_at: knex.fn.now()
    });
    
    console.log('附件上传成功:', attachmentId);
    
    // 返回附件信息，使用Objection.js查询以获取驼峰命名的结果
    const Attachment = require('../models/Attachment');
    const attachment = await Attachment.query().findById(attachmentId);
    res.status(201).json(attachment);
  } catch (error) {
    console.error('上传附件失败:', error);
    
    // 如果上传过程中出错，删除已上传的文件
    if (req.file && req.file.path) {
      try {
        fs.unlinkSync(req.file.path);
      } catch (err) {
        console.error('删除文件失败:', err);
      }
    }
    
    res.status(500).json({ error: error.message });
  }
});

// 获取记录的附件
router.get('/attachments/record/:record_id', authenticateToken, async (req, res) => {
  try {
    await ensureRecordsTable();
    
    const { record_id } = req.params;
    const user_id = req.user.id;
    const { include_all_users, service_context, include_deleted } = req.query;
    
    console.log('获取记录附件 - 记录ID:', record_id, '用户ID:', user_id, 'include_all_users:', include_all_users, 'service_context:', service_context, 'include_deleted:', include_deleted);
    
    // 检查记录是否存在
    const record = await knex('records')
      .where('id', record_id)
      .first();
      
    if (!record) {
      console.log('记录不存在:', record_id);
      return res.status(404).json({ error: '记录不存在' });
    }
    
    // 检查权限
    const includeAllUsers = String(include_all_users).toLowerCase() === 'true';
    const isServiceContext = String(service_context).toLowerCase() === 'true';
    
    if (includeAllUsers) {
      console.log('包含所有用户附件 - 跳过权限检查');
    } else if (isServiceContext) {
      // 服务上下文下的权限检查
      console.log('服务上下文获取附件 - 检查授权关系');
      
      // 查找服务记录关联
      const serviceRecord = await knex('service_records')
        .where('record_id', record_id)
        .first();
        
      if (!serviceRecord) {
        console.log('未找到服务记录关联');
        return res.status(403).json({ error: '无权访问此记录的附件' });
      }
      
      // 检查授权关系
      const authorization = await knex('user_authorizations')
        .where('id', serviceRecord.authorization_id)
        .where('authorized_id', user_id) // 确保当前用户是被授权者
        .where('status', 'ACTIVE')
        .first();
        
      if (!authorization) {
        console.log('未找到有效的授权关系');
        return res.status(403).json({ error: '无效的授权关系' });
      }
      
      console.log('找到授权关系:', authorization.id);
    } else if (record.user_id !== user_id && record.created_by !== user_id) {
      console.log('无权访问此记录的附件:', { recordUserId: record.user_id, userId: user_id });
      return res.status(403).json({ error: '无权访问此记录的附件' });
    }
    
    // 获取附件列表
    const attachments = await knex('attachments')
      .where('record_id', record_id)
      .select();
      
    console.log('获取到附件数量:', attachments.length);
    res.json(attachments);
  } catch (error) {
    console.error('获取附件列表失败:', error);
    res.status(500).json({ error: error.message });
  }
});

// 删除附件
router.delete('/attachments/:id', authenticateToken, async (req, res) => {
  try {
    await ensureRecordsTable();
    
    const { id } = req.params;
    const user_id = req.user.id;
    
    // 获取附件信息
    const attachment = await knex('attachments')
      .where('id', id)
      .first();
      
    if (!attachment) {
      return res.status(404).json({ error: '附件不存在' });
    }
    
    // 检查记录权限
    const record = await knex('records')
      .where('id', attachment.record_id)
      .first();
      
    if (!record) {
      console.log('关联记录不存在:', attachment.record_id);
      return res.status(404).json({ error: '关联的记录不存在' });
    }
    
    // 检查权限
    if (record.user_id !== user_id && record.created_by !== user_id && attachment.uploaded_by !== user_id) {
      return res.status(403).json({ error: '无权删除此附件' });
    }
    
    // 删除文件
    if (fs.existsSync(attachment.file_path)) {
      fs.unlinkSync(attachment.file_path);
    }
    
    // 从数据库中删除附件记录
    await knex('attachments').where('id', id).delete();
    
    console.log('附件删除成功:', id);
    res.json({ message: '附件已删除' });
  } catch (error) {
    console.error('删除附件失败:', error);
    res.status(500).json({ error: error.message });
  }
});

// 下载附件
router.get('/attachments/download/:id', authenticateToken, async (req, res) => {
  try {
    await ensureRecordsTable();
    
    const { id } = req.params;
    const user_id = req.user.id;
    const { include_all_users, service_context, include_deleted } = req.query;
    
    console.log('开始下载附件，ID:', id, '用户ID:', user_id, 'include_all_users:', include_all_users, 'service_context:', service_context, 'include_deleted:', include_deleted);
    console.log('请求源 (Origin):', req.headers.origin);
    
    // 设置CORS头，确保跨域访问不会被浏览器阻止
    const origin = req.headers.origin || '*';
    res.header('Access-Control-Allow-Origin', origin);
    
    // 如果Origin不是通配符，则可以设置允许凭据
    if (origin !== '*') {
      res.header('Access-Control-Allow-Credentials', 'true');
    }
    
    res.header('Access-Control-Allow-Methods', 'GET, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    res.header('Access-Control-Expose-Headers', 'Content-Disposition, Content-Type, Content-Length, Content-Range, Content-Description');
    
    // 为OPTIONS请求立即响应
    if (req.method === 'OPTIONS') {
      console.log('处理下载附件的预检请求，路径:', req.path);
      return res.status(204).send();
    }
    
    // 获取附件信息
    const attachment = await knex('attachments')
      .where('id', id)
      .first();
      
    if (!attachment) {
      console.log('附件不存在:', id);
      return res.status(404).json({ error: '附件不存在' });
    }
    
    console.log('附件信息:', attachment);
    
    // 检查记录权限
    const record = await knex('records')
      .where('id', attachment.record_id)
      .first();
      
    if (!record) {
      console.log('关联记录不存在:', attachment.record_id);
      return res.status(404).json({ error: '关联的记录不存在' });
    }
    
    // 检查权限
    const includeAllUsers = String(include_all_users).toLowerCase() === 'true';
    const isServiceContext = String(service_context).toLowerCase() === 'true';
    
    if (includeAllUsers) {
      console.log('包含所有用户附件 - 跳过权限检查');
    } else if (isServiceContext) {
      // 服务上下文下的权限检查
      console.log('服务上下文下载附件 - 检查授权关系');
      
      try {
        // 查找服务记录关联
        const serviceRecord = await knex('service_records')
          .where('record_id', record.id)
          .first();
          
        if (!serviceRecord) {
          console.log('未找到服务记录关联, 记录ID:', record.id);
          return res.status(403).json({ error: '无权下载此附件' });
        }
        
        console.log('找到服务记录关联:', serviceRecord);
        
        // 检查授权关系 - 确保使用user_authorizations表
        const authorization = await knex('user_authorizations')
          .where('id', serviceRecord.authorization_id)
          .where('authorized_id', user_id) // 确保当前用户是被授权者
          .where('status', 'ACTIVE')
          .first();
          
        if (!authorization) {
          console.log('未找到有效的授权关系, 授权ID:', serviceRecord.authorization_id);
          return res.status(403).json({ error: '无效的授权关系' });
        }
        
        console.log('找到授权关系:', authorization.id);
      } catch (error) {
        console.error('检查服务上下文权限时出错:', error);
        return res.status(500).json({ error: '检查权限时发生错误' });
      }
    } else if (record.user_id !== user_id && record.created_by !== user_id) {
      console.log('无权下载此附件, 用户ID:', user_id);
      return res.status(403).json({ error: '无权下载此附件' });
    }
    
    // 检查文件是否存在
    if (!fs.existsSync(attachment.file_path)) {
      console.log('文件路径不存在:', attachment.file_path);
      return res.status(404).json({ error: '文件不存在' });
    }
    
    // 处理文件名，确保中文文件名正确显示
    const fileName = attachment.file_name || 'download';
    
    // 尝试解码文件名，防止数据库中已经是编码的格式
    let decodedFileName = fileName;
    try {
      // 确保文件名是UTF-8编码，避免多次编码
      decodedFileName = Buffer.from(fileName, 'utf8').toString('utf8');
      console.log(`文件名解码检查: 原始=${fileName}, 解码后=${decodedFileName}`);
    } catch (err) {
      console.error('文件名解码失败:', err);
      decodedFileName = fileName;
    }
    
    // 使用encodeURIComponent对中文文件名进行编码，以确保浏览器能正确解析
    const safeFileName = encodeURIComponent(decodedFileName)
      .replace(/'/g, '%27')
      .replace(/"/g, '%22')
      .replace(/\(/g, '%28')
      .replace(/\)/g, '%29');
    
    console.log(`下载文件: 原始名=${fileName}, 解码后=${decodedFileName}, 安全编码名=${safeFileName}`);
    
    // 设置Content-Disposition头，确保文件名正确
    // 同时提供UTF-8编码版本和备用版本
    res.setHeader('Content-Disposition', `attachment; filename="${safeFileName}"; filename*=UTF-8''${safeFileName}`);
    res.setHeader('Content-Type', attachment.file_type || 'application/octet-stream');
    
    // 设置缓存控制头，防止缓存问题
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    
    // 使用流方式发送文件
    console.log('以流的方式发送文件:', attachment.file_path);
    const fileStream = fs.createReadStream(attachment.file_path);
    
    // 监听流错误
    fileStream.on('error', (err) => {
      console.error('文件流读取错误:', err);
      if (!res.headersSent) {
        res.status(500).json({ error: '文件读取失败' });
      }
    });
    
    // 监听接收方断开连接 
    req.on('close', () => {
      console.log('客户端断开连接');
      fileStream.destroy();
    });
    
    // 发送文件流
    fileStream.pipe(res);
  } catch (error) {
    console.error('下载附件失败:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router; 