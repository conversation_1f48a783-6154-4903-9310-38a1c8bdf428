import React, { useState, useEffect, useCallback } from 'react';
import { Box, Typography, CircularProgress, Tooltip, IconButton } from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import DiseaseTimeline from './DiseaseTimeline';
import { adaptDiseaseData, DiseaseInfo, RecordInfo } from './adapters';
import { usePatientDiseaseContext } from '../../../context/PatientDiseaseContext';
import { getPatientDiseases } from '../../../services/diseaseService';
import { getRecords } from '../../../services/recordService';
import dataCache from '../../../utils/dataCache';

/**
 * 病理选择器组件
 * 根据选中的患者显示相关病理列表
 */
const DiseaseSelector: React.FC = () => {
  const { selectedPatientId, selectedDiseaseId, setSelectedDisease, forceRefresh } = usePatientDiseaseContext();
  const [diseases, setDiseases] = useState<DiseaseInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null); // 跟踪选中的索引
  const [lastUpdateTime, setLastUpdateTime] = useState<number>(Date.now()); // 添加更新时间戳

  // 获取患者相关病理 - 使用useCallback优化函数引用
  const fetchPatientDiseases = useCallback(async () => {
    if (!selectedPatientId) {
      setDiseases([]);
      setSelectedIndex(null); // 清除索引
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // 获取患者的所有病理
      console.log(`开始获取患者 ${selectedPatientId} 的病理列表...`);
      const diseasesData = await getPatientDiseases(selectedPatientId);
      console.log(`获取到患者的病理数据: ${diseasesData.length}个`, diseasesData);
      
      if (!diseasesData || diseasesData.length === 0) {
        console.log(`患者 ${selectedPatientId} 没有病理数据，清空显示`);
        setDiseases([]);
        setSelectedIndex(null);
        setError(null);
        setLoading(false);
        return;
      }
      
      // 为每个病理获取相关记录
      const diseasesWithRecords = await Promise.all(
        diseasesData.map(async (disease: any) => {
          try {
            // 显示原始病理数据以调试
            console.log(`病理原始数据:`, disease);
            
            // 确保病理记录已包含患者ID信息
            if (!disease.patientId && !disease.patient_id) {
              // 如果原始数据中没有患者ID，添加当前选中的患者ID
              disease.patientId = selectedPatientId;
            }
            
            // 获取该病理的所有记录，确保只获取当前用户的记录
            console.log(`开始获取病理 ${disease.name} (ID: ${disease.id}) 的当前用户未删除记录...`);
            // 确保传递正确的diseaseId参数
            const recordParams: any = { 
              diseaseId: disease.id,
              disease_id: disease.id, // 同时传递下划线版本，以确保与后端兼容
              patientId: selectedPatientId,
              patient_id: selectedPatientId, // 同时传递下划线版本
              include_all_users: true, // 改为true，确保获取所有用户的记录
              limit: 1000,  // 获取足够多的记录以确保不遗漏
              
              // 尝试多种可能的过滤已删除记录的参数格式
              deleted_at: null, // 使用null值而不是字符串'NULL'
              include_deleted: false, // 显式排除已删除的记录
              with_deleted: false, // 另一种可能的参数名
              deleted: false
            };
            
            console.log('记录请求参数:', JSON.stringify(recordParams, null, 2));
            
            const recordsResponse = await getRecords(recordParams);
            
            // 检查API返回的结构
            console.log(`病理 ${disease.name} 的原始记录数据:`, recordsResponse);
            console.log(`原始数据类型:`, typeof recordsResponse, Array.isArray(recordsResponse));
            
            // 确定records数组的位置（可能直接是数组或包含在results字段中）
            const records = Array.isArray(recordsResponse) 
              ? recordsResponse 
              : (recordsResponse.results || []);
            
            console.log(`解析后的病理 ${disease.name} 记录数量: ${records.length}`);
            console.log(`记录数组:`, records);
            
            // 将记录格式化为RecordInfo格式
            const formattedRecords: RecordInfo[] = records.map((record: any) => {
              // 确保记录中的stageName和stagePhase被正确识别
              let stageNode = record.stageNode || record.stage_node;
              let stagePhase = record.stagePhase || record.stage_phase;
              
              // 中文名称的适配
              if (record.stageNodeName) {
                stageNode = record.stageNodeName;
              }
              if (record.stagePhaseName) {
                stagePhase = record.stagePhaseName;
              }
              
              // 保留原始的record_date值（如果存在）
              const original_record_date = record.record_date;
              
              // 确保记录日期是有效的
              // 创建recordDate作为UI显示用，不会影响原始record_date
              let recordDate = record.record_date || record.recordDate || record.created_at || record.updated_at;
              
              // 确保日期格式是字符串
              if (recordDate && typeof recordDate === 'object' && recordDate.toISOString) {
                recordDate = recordDate.toISOString();
              }

              // 只有在没有任何日期字段时才使用默认日期
              if (!recordDate) {
                const defaultDate = new Date();
                defaultDate.setDate(defaultDate.getDate() - 30);
                recordDate = defaultDate.toISOString();
                console.warn(`记录 ${record.id} 没有有效日期，使用默认值: ${recordDate}`);
              }
              
              console.log(`格式化记录: ID=${record.id}, 阶段=${stageNode || '无'}, 日期=${recordDate}, 原始record_date=${original_record_date || '无'}`);
              
              // 创建格式化的记录对象，保留原始字段
              return {
                id: record.id,
                title: record.title || record.name || `记录 #${record.id}`,
                stageNode,
                stagePhase,
                recordDate, // 用于UI显示的日期
                record_date: original_record_date, // 保留原始record_date，不覆盖
                stageNodeName: record.stageNodeName || stageNode,
                stagePhaseName: record.stagePhaseName || stagePhase,
                created_at: record.created_at,
                updated_at: record.updated_at,
                content: record.content || record.description,
                type: record.type || record.record_type
              };
            });
            
            // 尝试将记录按日期排序（从早到晚）
            try {
              formattedRecords.sort((a, b) => {
                const dateA = new Date(a.recordDate || a.record_date || a.created_at || '').getTime();
                const dateB = new Date(b.recordDate || b.record_date || b.created_at || '').getTime();
                
                // 如果日期无效，将其放在后面
                if (isNaN(dateA)) return 1;
                if (isNaN(dateB)) return -1;
                
                return dateA - dateB; // 从早到晚排序
              });
            } catch (err) {
              console.error('排序记录时出错:', err);
            }
            
            console.log(`病理 ${disease.name} 处理后记录 (${formattedRecords.length}个):`, formattedRecords);
            
            // 使用适配器处理数据
            const adaptedDisease = adaptDiseaseData(disease, formattedRecords);
            console.log(`病理 ${disease.name} 适配后结果:`, adaptedDisease);
            console.log(`适配后的记录数量: ${adaptedDisease.records?.length || 0}`);
            
            // 添加记录数量检查
            if(!adaptedDisease.records || adaptedDisease.records.length === 0) {
              console.warn(`警告: 病理 ${disease.name} 适配后没有记录数据`);
            }
            
            return adaptedDisease;
          } catch (err) {
            console.error(`获取病理记录失败: ${disease.id}`, err);
            // 如果获取记录失败，仍然返回基本病理信息
            return adaptDiseaseData(disease, []);
          }
        })
      );
      
      console.log('所有病理处理完成:', diseasesWithRecords);
      setDiseases(diseasesWithRecords);
      
      // 如果有选中的病理ID，查找它在列表中的索引
      if (selectedDiseaseId) {
        const index = diseasesWithRecords.findIndex(d => d.id === selectedDiseaseId);
        console.log(`当前选中的病理索引: ${index}, ID: ${selectedDiseaseId}`);
        
        if (index >= 0) {
          // 找到匹配的病理，更新选中索引
          setSelectedIndex(index);
        } else {
          // 没有找到匹配的病理，清除选择，不自动选择新的病理
          console.log(`警告: 当前选中的病理ID ${selectedDiseaseId} 不在获取的病理列表中，清除选择`);
          setSelectedIndex(null);
          setSelectedDisease(null);
        }
      } else {
        // 没有选中的病理ID，不自动选择第一个，让用户自行选择
        console.log(`没有选中的病理ID，不自动选择，保持null状态`);
        setSelectedIndex(null);
      }
      
      // 更新最后一次加载时间
      setLastUpdateTime(Date.now());
    } catch (err) {
      console.error('获取患者病理失败:', err);
      setError('获取患者病理失败，请重试');
      setDiseases([]);
      setSelectedIndex(null);
    } finally {
      setLoading(false);
    }
  }, [selectedPatientId, selectedDiseaseId, setSelectedDisease]);

  // 获取病理数据
  useEffect(() => {
    fetchPatientDiseases();
  }, [fetchPatientDiseases]);

  // 处理病理选择
  const handleDiseaseSelect = (disease: DiseaseInfo, index: number) => {
    console.log('选择病理:', disease.id, disease.name, '患者ID:', selectedPatientId, '病理归属患者ID:', disease.patientId);
    console.log('选择的病理索引:', index);
    
    // 安全检查1 - 确保有当前患者
    if (!selectedPatientId) {
      console.error(`[DiseaseSelector] 错误：无法选择病理，当前没有选中的患者`);
      return;
    }
    
    // 安全检查2 - 如果选择的是当前已选中的病理，不做任何操作
    if (disease.id === selectedDiseaseId && index === selectedIndex) {
      console.log(`[DiseaseSelector] 注意：选择的病理已经是当前选中的，跳过操作`);
      return;
    }
    
    // 安全检查3 - 确保病理属于当前选中的患者
    if (disease.patientId && disease.patientId !== selectedPatientId) {
      console.error(`[DiseaseSelector] 错误：病理(${disease.id})属于患者(${disease.patientId})，而非当前选中的患者(${selectedPatientId})`);
      return;
    }
    
    // 设置选中索引
    setSelectedIndex(index);
    
    // 确保setSelectedDisease函数存在且被调用
    if (setSelectedDisease) {
      // 清除特定病理的缓存，避免过时数据
      dataCache.clearDiseaseRelatedCache(disease.id);
      
      // 更新Context中的选中病理ID
      setSelectedDisease(disease.id);
      console.log(`[DiseaseSelector] 已设置选中的病理: ${disease.id} (${disease.name})`);
    } else {
      console.error('无法设置选中的病理：setSelectedDisease函数不存在');
    }
  };

  // 处理强制刷新
  const handleForceRefresh = () => {
    console.log('[DiseaseSelector] 强制刷新病理选择和缓存');
    setRefreshing(true);
    
    // 保存当前患者ID以便稍后恢复
    const currentPatientId = selectedPatientId;
    // 暂存当前选中的病理ID
    const currentDiseaseId = selectedDiseaseId;
    
    // 清除所有缓存
    dataCache.clear();
    
    // 强制刷新上下文
    if (forceRefresh) {
      forceRefresh();
    }
    
    // 短暂延时后重新加载数据
    setTimeout(() => {
      setDiseases([]);
      setError(null);
      setSelectedIndex(null); // 重置索引
      
      // 重新获取患者的疾病数据
      if (currentPatientId) {
        console.log(`[DiseaseSelector] 正在重新获取患者(${currentPatientId})的病理数据...`);
        getPatientDiseases(currentPatientId)
          .then(diseaseData => {
            console.log(`[DiseaseSelector] 强制刷新后获取到${diseaseData.length}条病理数据`);
            if (diseaseData && diseaseData.length > 0) {
              // 尝试恢复之前选中的病理
              if (currentDiseaseId) {
                const exists = diseaseData.some((d: any) => d.id === currentDiseaseId);
                if (exists) {
                  setSelectedDisease(currentDiseaseId);
                  console.log(`[DiseaseSelector] 恢复之前选中的病理: ${currentDiseaseId}`);
                } else {
                  // 之前的病理不存在，不自动选择新病理
                  console.log(`[DiseaseSelector] 之前选中的病理不存在，保持未选中状态`);
                  setSelectedDisease(null);
                }
              } else {
                // 没有之前选中的病理，维持未选中状态
                console.log(`[DiseaseSelector] 之前无选中病理，保持未选中状态`);
                setSelectedDisease(null);
              }
            }
            
            // 重新加载完整数据
            fetchPatientDiseases();
          })
          .catch(err => {
            console.error('[DiseaseSelector] 强制刷新后获取病理数据失败:', err);
            fetchPatientDiseases(); // 仍然尝试正常加载
          })
          .finally(() => {
            setRefreshing(false);
          });
      } else {
        setRefreshing(false);
      }
    }, 300);
  };

  // 渲染加载状态
  if (loading) {
    return (
      <Box sx={{ p: 2, display: 'flex', justifyContent: 'center' }}>
        <CircularProgress size={24} />
      </Box>
    );
  }

  // 渲染错误状态
  if (error) {
    return (
      <Box sx={{ p: 2 }}>
        <Typography color="error" variant="body2">
          {error}
          <IconButton size="small" onClick={handleForceRefresh} sx={{ ml: 1 }}>
            <RefreshIcon fontSize="small" />
          </IconButton>
        </Typography>
      </Box>
    );
  }

  // 渲染病理列表
  return (
    <Box sx={{ p: 1 }}>
      {/* 标题和刷新按钮 */}
      <Box 
        sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center', 
          mb: 1,
          px: 1 
        }}
      >
        <Typography variant="subtitle2" sx={{ fontSize: '0.75rem', color: 'text.secondary' }}>
          患者病理 ({diseases.length})
          {selectedDiseaseId && (
            <Typography component="span" variant="caption" sx={{ ml: 1, fontSize: '0.65rem' }}>
              当前选中: {selectedDiseaseId.substring(0, 8)}...
            </Typography>
          )}
        </Typography>
        
        <Tooltip title="强制刷新病理选择和缓存">
          <IconButton 
            size="small" 
            onClick={handleForceRefresh}
            disabled={refreshing || loading}
            sx={{ p: 0.5 }}
          >
            <RefreshIcon 
              fontSize="small" 
              sx={{ 
                animation: (refreshing || loading) ? 'spin 1s linear infinite' : 'none',
                '@keyframes spin': {
                  '0%': { transform: 'rotate(0deg)' },
                  '100%': { transform: 'rotate(360deg)' }
                }
              }} 
            />
          </IconButton>
        </Tooltip>
      </Box>
      
      {/* 检查是否有疾病列表，如果无则显示提示信息 */}
      {diseases.length === 0 ? (
        <Box sx={{ p: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            {loading ? '正在加载病理...' : '该患者暂无病理记录'}
          </Typography>
        </Box>
      ) : (
        /* 确保列表中只渲染属于当前患者的病理 */
        diseases
          .filter(disease => !disease.patientId || disease.patientId === selectedPatientId)
          .map((disease, index) => (
            <DiseaseTimeline
              key={disease.id}
              disease={disease}
              isSelected={disease.id === selectedDiseaseId}
              onClick={() => handleDiseaseSelect(disease, index)}
            />
          ))
      )}
    </Box>
  );
};

export default DiseaseSelector; 