exports.up = function(knex) {
  return knex.schema.raw(`
    INSERT INTO knex_migrations (name, batch, migration_time)
    VALUES 
    ('20240510001_create_diseases.js', (SELECT MAX(batch) FROM knex_migrations), NOW()),
    ('20240510005_create_ai_report_tables.js', (SELECT MAX(batch) FROM knex_migrations), NOW()),
    ('20250426104646_add_created_at_to_users.js', (SELECT MAX(batch) FROM knex_migrations), NOW()),
    ('2025042701_init_database.js', (SELECT MAX(batch) FROM knex_migrations), NOW()),
    ('20250428001_rename_emergency_contact_relation.js', (SELECT MAX(batch) FROM knex_migrations), NOW()),
    ('20250602001_drop_authorizations_if_exists.js', (SELECT MAX(batch) FROM knex_migrations), NOW())
  `);
};

exports.down = function(knex) {
  return knex.schema.raw(`
    DELETE FROM knex_migrations 
    WHERE name IN (
      '20240510001_create_diseases.js',
      '20240510005_create_ai_report_tables.js',
      '20250426104646_add_created_at_to_users.js',
      '2025042701_init_database.js',
      '20250428001_rename_emergency_contact_relation.js',
      '20250602001_drop_authorizations_if_exists.js'
    )
  `);
};