import React, { useState, useEffect, useMemo } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  Snackbar,
  Card,
  CardContent,
  useTheme,
  useMediaQuery,
  Collapse,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import SearchOutlined from '@mui/icons-material/SearchOutlined';
import VisibilityOutlined from '@mui/icons-material/VisibilityOutlined';
import EditOutlined from '@mui/icons-material/EditOutlined';
import DeleteOutlined from '@mui/icons-material/DeleteOutlined';
import BookmarkIcon from '@mui/icons-material/Bookmark';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import LockIcon from '@mui/icons-material/Lock';
import AttachmentIcon from '@mui/icons-material/Attachment';
import LabelIcon from '@mui/icons-material/Label';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getRecords, deleteRecord } from '../services/recordService';
import { getPatients } from '../services/patientService';
import { getDiseases } from '../services/diseaseService';
import { useAuthStore } from '../store/authStore';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { 
  RecordTypeEnum, 
  RecordTypeNames, 
  SeverityEnum, 
  SeverityNames 
} from '../types/recordEnums';

// 个人标签主色
const PERSONAL_TAG_COLOR = '#3498db';
const PERSONAL_TAG_LIGHT_COLOR = `${PERSONAL_TAG_COLOR}20`; // 20%透明度的背景色，增加可见度

// 记录类型颜色映射 - 使用枚举值
const recordTypeColors: Record<string, {main: string, light: string, dark: string}> = {
  // 基本类型
  [RecordTypeEnum.SELF_DESCRIPTION]: {
    main: '#607d8b',  // 蓝灰色
    light: '#eceff1',
    dark: '#37474f'
  },
  [RecordTypeEnum.SYMPTOM]: {
    main: '#d32f2f',  // 红色
    light: '#ffebee',
    dark: '#b71c1c'
  },
  [RecordTypeEnum.EXAMINATION]: {
    main: '#7b1fa2',  // 紫色
    light: '#f3e5f5',
    dark: '#4a148c'
  },
  [RecordTypeEnum.LAB_TEST]: {
    main: '#9c27b0',  // 浅紫色
    light: '#f3e5f5',
    dark: '#6a1b9a'
  },
  [RecordTypeEnum.DIAGNOSIS]: {
    main: '#1976d2',  // 蓝色
    light: '#e3f2fd',
    dark: '#0d47a1'
  },
  [RecordTypeEnum.TREATMENT]: {
    main: '#388e3c',  // 绿色
    light: '#e8f5e9',
    dark: '#1b5e20'
  },
  [RecordTypeEnum.HOSPITALIZATION]: {
    main: '#303f9f',  // 靛蓝色
    light: '#e8eaf6',
    dark: '#1a237e'
  },
  [RecordTypeEnum.MEDICATION]: {
    main: '#f57c00',  // 橙色
    light: '#fff3e0',
    dark: '#e65100'
  },
  [RecordTypeEnum.SURGERY]: {
    main: '#c2185b',  // 粉色
    light: '#fce4ec',
    dark: '#880e4f'
  },
  [RecordTypeEnum.MONITORING]: {
    main: '#0097a7',  // 青色
    light: '#e0f7fa',
    dark: '#006064'
  },
  [RecordTypeEnum.PHYSICAL_THERAPY]: {
    main: '#00796b',  // 青绿色
    light: '#e0f2f1',
    dark: '#004d40'
  },
  [RecordTypeEnum.DISCHARGE]: {
    main: '#283593',  // 深蓝色
    light: '#e8eaf6',
    dark: '#1a237e'
  },
  [RecordTypeEnum.APPOINTMENT]: {
    main: '#3f51b5',  // 蓝紫色
    light: '#e8eaf6',
    dark: '#283593'
  },
  [RecordTypeEnum.REPORT]: {
    main: '#455a64',  // 深灰蓝色
    light: '#cfd8dc',
    dark: '#263238'
  },
  [RecordTypeEnum.FOLLOW_UP]: {
    main: '#0097a7',  // 青色
    light: '#e0f7fa',
    dark: '#006064'
  },
  [RecordTypeEnum.PROGNOSIS]: {
    main: '#00bcd4',  // 浅青色
    light: '#e0f7fa',
    dark: '#00838f'
  },
  [RecordTypeEnum.AUX_DIAGNOSIS]: {
    main: '#2196f3',  // 浅蓝色
    light: '#e3f2fd',
    dark: '#1565c0'
  },
  [RecordTypeEnum.NURSING]: {
    main: '#4caf50',  // 浅绿色
    light: '#e8f5e9',
    dark: '#2e7d32'
  },
  [RecordTypeEnum.REVISIT]: {
    main: '#3f51b5',  // 浅靛蓝色
    light: '#e8eaf6',
    dark: '#283593'
  },
  [RecordTypeEnum.REFERRAL]: {
    main: '#1a237e',  // 深靛蓝色
    light: '#c5cae9',
    dark: '#000051'
  },
  [RecordTypeEnum.PSYCHOLOGY]: {
    main: '#8e24aa',  // 中紫色
    light: '#f3e5f5',
    dark: '#5e35b1'
  },
  [RecordTypeEnum.REHABILITATION]: {
    main: '#00897b',  // 蓝绿色
    light: '#e0f2f1',
    dark: '#00695c'
  },
  [RecordTypeEnum.ASSESSMENT]: {
    main: '#00bcd4',  // 浅青色
    light: '#e0f7fa',
    dark: '#00838f'
  },
  [RecordTypeEnum.OTHER]: {
    main: '#546e7a',  // 灰蓝色
    light: '#eceff1',
    dark: '#263238'
  },
  
  // 额外类型
  'DIFFERENTIAL_DIAGNOSIS': {
    main: '#2196f3',  // 浅蓝色
    light: '#e3f2fd',
    dark: '#1565c0'
  },
  'DIAGNOSIS_CONFIRMATION': {
    main: '#0d47a1',  // 深蓝色
    light: '#d0d9ff',
    dark: '#002171'
  },
  'TREATMENT_PLAN': {
    main: '#4caf50',  // 浅绿色
    light: '#e8f5e9', 
    dark: '#2e7d32'
  },
  'TREATMENT_ADJUSTMENT': {
    main: '#1b5e20',  // 深绿色
    light: '#c8e6c9',
    dark: '#003300'
  },
  'PRESCRIPTION': {
    main: '#ff9800',  // 浅橙色
    light: '#fff3e0',
    dark: '#e65100'
  },
  'MEDICATION_ADJUSTMENT': {
    main: '#e65100',  // 深橙色
    light: '#ffe0b2',
    dark: '#bf360c'
  },
  'IMAGING': {
    main: '#4a148c',  // 深紫色
    light: '#e1bee7',
    dark: '#38006b'
  },
  'REVIEW': {
    main: '#006064',  // 深青色
    light: '#b2ebf2',
    dark: '#00363a'
  },
  'VISIT': {
    main: '#303f9f',  // 靛蓝色
    light: '#e8eaf6',
    dark: '#1a237e'
  },
  'CONSULTATION': {
    main: '#3f51b5',  // 浅靛蓝色
    light: '#e8eaf6',
    dark: '#283593'
  },
  'CHIEF_COMPLAINT': {
    main: '#f44336',  // 浅红色
    light: '#ffebee',
    dark: '#c62828'
  },
  'CONDITION_CHANGE': {
    main: '#b71c1c',  // 深红色
    light: '#ffcdd2',
    dark: '#7f0000'
  },
  'NOTE': {
    main: '#546e7a',  // 灰蓝色
    light: '#eceff1',
    dark: '#263238'
  },
  'COMMENT': {
    main: '#607d8b',  // 浅灰蓝色
    light: '#eceff1',
    dark: '#37474f'
  }
};

// 病程节点/阶段中文映射 - 移到文件顶层
const stagePhaseMap: Record<string, string> = {
  'INITIAL': '初诊阶段',
  'DIAGNOSIS': '确诊阶段',
  'TREATMENT': '治疗阶段',
  'RECOVERY': '康复阶段',
  'PROGNOSIS': '预后阶段'
};

const stageNodeMap: Record<string, string> = {
  'INITIAL_VISIT': '初诊',
  'DIAGNOSIS': '确诊',
  'TREATMENT': '治疗',
  'FOLLOW_UP': '随访',
  'PROGNOSIS': '预后',
  'ARCHIVE': '封档'
};

// 病程阶段/节点颜色映射 - 移到文件顶层
const stageColors: Record<string, {main: string, light: string, dark: string}> = {
  '初诊': {
    main: '#E53935',
    light: '#FFEBEE',
    dark: '#C62828'
  },
  '确诊': {
    main: '#8E24AA',
    light: '#F3E5F5',
    dark: '#6A1B9A'
  },
  '治疗': {
    main: '#FB8C00',
    light: '#FFF3E0',
    dark: '#EF6C00'
  },
  '随访': {
    main: '#FDD835',
    light: '#FFFDE7',
    dark: '#F9A825'
  },
  '预后': {
    main: '#43A047',
    light: '#E8F5E9',
    dark: '#2E7D32'
  },
  '封档': {
    main: '#757575',
    light: '#EEEEEE',
    dark: '#424242'
  }
};

// 节点到阶段的映射 - 移到文件顶层
const nodeToPhase: Record<string, string | null> = {
  '初诊': '初诊阶段',
  '确诊': '确诊阶段',
  '治疗': '治疗阶段',
  '随访': '康复阶段',
  '预后': '预后阶段',
  '封档': null
};

// 获取病程节点/阶段中文名称 - 移到文件顶层
const getStageText = (stageNode?: string, stagePhase?: string): string => {
  if (stageNode && stageNode.trim() !== '') {
    return stageNodeMap[stageNode] || stageNode;
  }
  if (stagePhase && stagePhase.trim() !== '') {
    return stagePhaseMap[stagePhase] || stagePhase;
  }
  return '未知阶段';
};

// 获取病程阶段/节点颜色 - 移到文件顶层
const getStageColor = (stageNode?: string, stagePhase?: string): {main: string, light: string, dark: string} => {
  if (stageNode && stageNode.trim() !== '') {
    const nodeName = stageNodeMap[stageNode] || '';
    return stageColors[nodeName] || stageColors['封档']; // 默认返回封档色
  }
  
  if (stagePhase && stagePhase.trim() !== '') {
    // 从阶段名称推断对应的节点名称
    const phaseName = stagePhaseMap[stagePhase] || '';
    for (const [node, phase] of Object.entries(nodeToPhase)) {
      if (phase === phaseName) {
        return stageColors[node] || stageColors['封档'];
      }
    }
  }
  
  return stageColors['封档']; // 默认返回封档色
};

// 移动端下卡片组件
interface MobileRecordCardProps {
  record: any;
  onView: (id: string) => void;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
  getRecordTypeFullColors: (type: string) => {main: string, light: string, dark: string};
  getRecordTypeName: (type: string | string[]) => string;
  getSeverityMainColor: (severity: string) => string;
  formatDateTime: (dateTime: string) => string;
  truncateText: (text: string, maxLength: number) => string;
  parseRecordType: (recordType: any) => string[];
}

const MobileRecordCard: React.FC<MobileRecordCardProps> = ({
  record,
  onView,
  onEdit,
  onDelete,
  getRecordTypeFullColors,
  getRecordTypeName,
  getSeverityMainColor,
  formatDateTime,
  truncateText,
  parseRecordType
}) => {
  const theme = useTheme();
  const [expanded, setExpanded] = useState(false);

  const handleExpandClick = () => {
    setExpanded(!expanded);
  };

  const getSeverityTextFromEnum = (severity: string): string => {
    return SeverityNames[severity as SeverityEnum] || severity;
  };

  // 使用字符串字面量作为键来修复Linter错误
  const severityColorMap: Record<string, string> = {
    'LOW': theme.palette.info.main,
    'MODERATE': theme.palette.warning.main,
    'HIGH': theme.palette.error.main,
    'CRITICAL': theme.palette.error.dark,
    'UNKNOWN': theme.palette.grey[500],
  };

  const getSeverityChipColor = (severity: string): string => {
    return severityColorMap[severity] || theme.palette.grey[500];
  };

  const recordTypeArray = parseRecordType(record.recordType || record.primaryType || 'NOTE');
  const hasCustomTags = record.customTags && record.customTags.trim() !== '';
  const customTagsArray: string[] = hasCustomTags 
    ? (record.customTags as string).split(',')
        .map((tag: string) => tag.trim())
        .filter((tag: string) => tag !== '')
    : [];
  
  const allTags: { text: string; isPrimary: boolean; isType: boolean }[] = [
    ...recordTypeArray.map((type, index) => ({
      text: getRecordTypeName(type),
      isPrimary: index === 0, 
      isType: true
    })),
    ...customTagsArray.map(tag => ({
      text: tag,
      isPrimary: false,
      isType: false
    }))
  ];
  
  const hasContent = record.content && record.content.trim() !== '';
  const hasAttachments = record.attachments && record.attachments.length > 0;

  return (
    <Card
      elevation={0}
      sx={{
        mb: 2,
        border: '1px solid #e0e0e0',
        pl: 0,
        position: 'relative',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <Box sx={{
        position: 'absolute',
        left: 0,
        top: 0,
        bottom: 0,
        width: '4px',
        borderRadius: '4px 0 0 4px',
        bgcolor: getSeverityChipColor(record.severity),
      }} />
      
      <CardContent sx={{ pb: '8px !important', pl: 1.5 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', maxWidth: '70%' }}>
            {record.isImportant === true && (
              <Tooltip title="重要记录">
                <BookmarkIcon color="error" sx={{ mr: 1, fontSize: '0.75rem' }} />
              </Tooltip>
            )}
            <Typography 
              variant="subtitle1" 
              component="div" 
              sx={{ 
                fontWeight: 600,
                fontSize: '0.75rem',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}
            >
              {truncateText(record.title, 12)}
            </Typography>
          </Box>
          
          <Box>
            <Tooltip title="查看详情">
              <IconButton
                size="small"
                onClick={() => onView(record.id)}
                color="primary"
                sx={{ ml: 0.5 }}
              >
                <VisibilityOutlined fontSize="small" />
              </IconButton>
            </Tooltip>
            <Tooltip title="编辑">
              <IconButton
                size="small"
                onClick={() => onEdit(record.id)}
                color="primary"
                sx={{ ml: 0.5 }}
              >
                <EditOutlined fontSize="small" />
              </IconButton>
            </Tooltip>
            <Tooltip title="删除">
              <IconButton
                size="small"
                onClick={() => onDelete(record.id)}
                color="error"
                sx={{ ml: 0.5 }}
              >
                <DeleteOutlined fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
        
        <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', mb: 1, gap: 0.5 }}>
          {record.isPrivate === true && (
            <Tooltip title="隐私记录">
              <LockIcon color="primary" sx={{ fontSize: '0.875rem', mr: 0.5 }} />
            </Tooltip>
          )}
          
          {(record.stageNode || record.stagePhase) && (
            <Chip
              label={getStageText(record.stageNode, record.stagePhase)}
              size="small"
              sx={{
                height: 20,
                fontSize: '0.7rem',
                bgcolor: getStageColor(record.stageNode, record.stagePhase).light,
                color: getStageColor(record.stageNode, record.stagePhase).dark,
                border: `1px solid ${getStageColor(record.stageNode, record.stagePhase).main}`,
                fontWeight: 600
              }}
            />
          )}
          
          <Chip
            label={getSeverityTextFromEnum(record.severity)}
            size="small"
            sx={{
              height: 20,
              fontSize: '0.7rem',
              ml: 0.5,
              bgcolor: getSeverityChipColor(record.severity),
              border: `1px solid ${getSeverityChipColor(record.severity)}`,
              color: 'white',
              fontWeight: 600
            }}
          />
          
          <Typography 
            variant="caption" 
            color="text.secondary" 
            sx={{ ml: 'auto' }}
          >
            {formatDateTime(record.recordDate)}
          </Typography>
        </Box>
        
        <Box sx={{ display: 'flex', alignItems: 'center', mb: expanded ? 1 : 0, flexWrap: 'wrap' }}>
          <IconButton 
            size="small" 
            onClick={handleExpandClick}
            sx={{ p: 0.5, mr: 0.5 }}
          >
            {expanded ? <ExpandLessIcon fontSize="small" /> : <ExpandMoreIcon fontSize="small" />}
          </IconButton>
          
          {allTags && allTags.length > 0 && (
            <Box sx={{ display: 'flex', flexWrap: 'wrap', flex: 1, gap: 0.5, mt: 0.5 }}>
              {allTags.map((tag, index) => {
                let tagStyle = {};
                if (tag.isType) {
                  const typeColors = getRecordTypeFullColors(recordTypeArray[0] || 'NOTE');
                  tagStyle = {
                    bgcolor: tag.isPrimary ? typeColors.light : 'transparent',
                    color: typeColors.dark,
                    border: `1px solid ${typeColors.main}`,
                    fontWeight: tag.isPrimary ? 600 : 400
                  };
                } else {
                  tagStyle = {
                    bgcolor: PERSONAL_TAG_LIGHT_COLOR,
                    color: PERSONAL_TAG_COLOR,
                    border: `1px solid ${PERSONAL_TAG_COLOR}`,
                    fontWeight: 500,
                    '& .MuiChip-icon': {
                      color: PERSONAL_TAG_COLOR,
                      marginRight: '2px',
                      fontSize: '0.8rem'
                    }
                  };
                }
                return (
                  <Chip
                    key={index}
                    label={tag.text}
                    size="small"
                    variant={tag.isPrimary ? "filled" : "outlined"}
                    icon={!tag.isType ? <LabelIcon fontSize="small" /> : undefined}
                    sx={{
                      ml: index === 0 ? 0 : 0.5,
                      height: 20,
                      fontSize: '0.7rem',
                      ...tagStyle
                    }}
                  />
                );
              })}
            </Box>
          )}
        </Box>
        
        <Collapse in={expanded} timeout="auto" unmountOnExit>
          <Divider sx={{ my: 1 }} />
          
          {hasContent && (
            <Box sx={{ mb: 1 }}>
              <Typography 
                variant="body2" 
                color="text.secondary"
                sx={{ 
                  fontSize: '0.8rem',
                  whiteSpace: 'pre-line'
                }}
              >
                {record.content.length > 50 
                  ? truncateText(record.content, 50) + ' ' 
                  : record.content}
                {record.content.length > 50 && (
                  <Button 
                    size="small" 
                    onClick={() => onView(record.id)} 
                    sx={{ p: 0, ml: 0.5, minWidth: 'auto', fontSize: '0.7rem', verticalAlign: 'baseline' }}
                  >
                    查看全文
                  </Button>
                )}
              </Typography>
            </Box>
          )}
          
          {hasAttachments && (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <AttachmentIcon fontSize="small" sx={{ mr: 0.5, fontSize: '0.9rem' }} />
              <Typography variant="caption">
                {`${record.attachments.length}个附件`}
              </Typography>
            </Box>
          )}
        </Collapse>
      </CardContent>
    </Card>
  );
};

// 记录管理页面组件
const RecordManagePage: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { token } = useAuthStore((state) => state);
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm);
  const [filter, setFilter] = useState({ 
    recordType: '', 
    severity: '',
    patientId: '',
    diseaseId: '' 
  });
  const [searchDebugMode, setSearchDebugMode] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [notification, setNotification] = useState({ open: false, message: '', type: 'success' as 'success' | 'error' });
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [recordToDelete, setRecordToDelete] = useState<string | null>(null);
  
  // 获取患者列表
  const { data: patients = [] } = useQuery({
    queryKey: ['patients'],
    queryFn: () => getPatients(),
    enabled: !!token
  });
  
  // 获取疾病列表
  const { data: diseases = [] } = useQuery({
    queryKey: ['diseases', filter.patientId],
    queryFn: () => getDiseases({ patientId: filter.patientId || undefined }),
    enabled: !!token
  });
  
  // 当患者选择变化时，重置疾病选择
  useEffect(() => {
    if (filter.patientId) {
      // 只有当patientId变化且diseaseId存在时才重置diseaseId
      if (filter.diseaseId) {
        // 检查当前选择的diseaseId是否属于当前选择的patientId
        const belongsToCurrentPatient = diseases.some((disease: any) => 
          disease.id === filter.diseaseId && disease.patientId === filter.patientId
        );
        
        if (!belongsToCurrentPatient) {
          setFilter(prev => ({ ...prev, diseaseId: '' }));
        }
      }
    } else {
      // 如果没有选择患者，清空疾病选择
      setFilter(prev => ({ ...prev, diseaseId: '' }));
    }
  }, [filter.patientId, diseases, filter.diseaseId]);
  
  // 筛选可用的疾病列表
  const filteredDiseases = useMemo(() => {
    if (!filter.patientId) return diseases;
    return diseases.filter((disease: any) => disease.patientId === filter.patientId);
  }, [diseases, filter.patientId]);

  // 搜索关键词防抖
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);
    
    return () => {
      clearTimeout(timer);
    };
  }, [searchTerm]);
  
  // 获取记录列表
  const { 
    data, 
    isLoading, 
  } = useQuery({
    queryKey: ['records', filter, page, rowsPerPage, debouncedSearchTerm, searchDebugMode],
    queryFn: async () => {
      console.log('发送记录查询请求，筛选条件:', filter, '调试模式:', searchDebugMode);
      return getRecords({
        ...filter,
        skip: page * rowsPerPage,
        limit: rowsPerPage,
        q: debouncedSearchTerm,
        // 无论何种情况都启用所有用户记录查询，后端会根据patientId和diseaseId进行筛选
        include_all_users: 'true',
        // 添加调试模式参数
        debug: searchDebugMode ? 'true' : 'false'
      });
    },
    enabled: !!token
  });

  // 删除记录的mutation
  const deleteMutation = useMutation({
    mutationFn: (id: string) => deleteRecord(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['records'] });
      setNotification({
        open: true,
        message: '记录删除成功',
        type: 'success'
      });
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.error || '删除记录失败';
      setNotification({
        open: true,
        message: errorMessage,
        type: 'error'
      });
    }
  });

  // 处理页面变更
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  // 处理每页行数变更
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // 处理记录查看
  const handleViewRecord = (id: string) => {
    navigate(`/records/${id}`);
  };

  // 处理记录编辑
  const handleEditRecord = (id: string) => {
    navigate(`/records/${id}/edit`);
  };

  // 处理记录删除
  const handleDeleteRecord = (id: string) => {
    setRecordToDelete(id);
    setDeleteDialogOpen(true);
  };

  // 确认删除
  const confirmDelete = () => {
    if (recordToDelete) {
      deleteMutation.mutate(recordToDelete);
      setDeleteDialogOpen(false);
      setRecordToDelete(null);
    }
  };

  // 取消删除
  const cancelDelete = () => {
    setDeleteDialogOpen(false);
    setRecordToDelete(null);
  };

  // 关闭通知
  const handleCloseNotification = () => {
    setNotification({
      ...notification,
      open: false
    });
  };

  // 获取记录类型名称
  const getRecordTypeName = (type: string | string[]): string => {
    if (Array.isArray(type)) {
      return type.map(t => RecordTypeNames[t as RecordTypeEnum] || t).join(', ');
    }
    return RecordTypeNames[type as RecordTypeEnum] || type;
  };

  // 获取记录类型的完整颜色
  const getRecordTypeFullColors = (type: string): {main: string, light: string, dark: string} => {
    return recordTypeColors[type as RecordTypeEnum] || {
      main: '#757575', // 灰色
      light: '#eeeeee',
      dark: '#424242'
    };
  };

  // 获取严重程度中文显示
  const getSeverityText = (severity: string): string => {
    // 如果是有效的枚举值，直接返回对应的中文
    if (severity && SeverityNames[severity as SeverityEnum]) {
      return SeverityNames[severity as SeverityEnum];
    }
    
    // 如果是数字字符串，转换为对应的中文
    if (!isNaN(Number(severity))) {
      const numSeverity = Number(severity);
      switch (numSeverity) {
        case 1: return SeverityNames[SeverityEnum.MILD];
        case 2: return SeverityNames[SeverityEnum.MODERATE];
        case 3: return SeverityNames[SeverityEnum.SEVERE];
        case 4: return SeverityNames[SeverityEnum.CRITICAL];
        default: return '未知';
      }
    }
    
    // 默认值
    return severity || '未知';
  };

  // 获取严重程度主色
  const getSeverityMainColor = (severity: string): string => {
    // 检查是否是枚举值
    switch (severity) {
      case SeverityEnum.MILD: return '#4caf50'; // 绿色
      case SeverityEnum.MODERATE: return '#ff9800'; // 橙色
      case SeverityEnum.SEVERE: return '#f44336'; // 红色
      case SeverityEnum.CRITICAL: return '#9c27b0'; // 紫色
      default: break;
    }
    
    // 如果是数字字符串，转换为对应的颜色
    if (!isNaN(Number(severity))) {
      const numSeverity = Number(severity);
      switch (numSeverity) {
        case 1: return '#4caf50'; // 绿色
        case 2: return '#ff9800'; // 橙色
        case 3: return '#f44336'; // 红色
        case 4: return '#9c27b0'; // 紫色
        default: return '#9e9e9e'; // 灰色
      }
    }
    
    // 默认灰色
    return '#9e9e9e';
  };

  // 截断文本，超出部分显示...
  const truncateText = (text: string, maxLength: number): string => {
    if (!text) return '';
    return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
  };

  // 格式化日期时间
  const formatDateTime = (dateTime: string): string => {
    try {
      return format(new Date(dateTime), 'yyyy-MM-dd HH:mm', { locale: zhCN });
    } catch (error) {
      return '无效日期';
    }
  };

  // 解析记录类型，确保返回一个标准格式(字符串数组)
  const parseRecordType = (recordType: any): string[] => {
    if (Array.isArray(recordType) && recordType.every(item => typeof item === 'string')) {
      return recordType;
    }
    if (typeof recordType === 'string') {
      if (recordType.startsWith('[') && recordType.endsWith(']')) {
        try {
          const parsed = JSON.parse(recordType);
          if (Array.isArray(parsed) && parsed.every(item => typeof item === 'string')) {
            return parsed;
          }
        } catch (e) {
          const inner = recordType.slice(1, -1);
          return inner.split(',').map(s => s.trim().replace(/^"|"$/g, ''));
        }
      } else if (recordType.includes(',')) {
        return recordType.split(',').map(s => s.trim());
      }
      return [recordType.trim()];
    }
    return [RecordTypeEnum.OTHER];
  };

  // 切换搜索调试模式
  const toggleSearchDebugMode = () => {
    setSearchDebugMode(prev => !prev);
  };

  // 渲染匹配信息
  const renderMatchInfo = (record: any) => {
    if (!searchDebugMode || !record._debug || !debouncedSearchTerm) return null;
    
    return (
      <Box sx={{ mt: 1, pt: 1, borderTop: '1px dashed #ccc' }}>
        <Typography variant="caption" color="text.secondary" sx={{ display: 'block', fontWeight: 600 }}>
          匹配信息:
        </Typography>
        <Box component="ul" sx={{ m: 0, pl: 2 }}>
          {record._debug.matchPoints.map((point: string, index: number) => (
            <Typography component="li" variant="caption" color="text.secondary" key={index}>
              {point}
            </Typography>
          ))}
        </Box>
      </Box>
    );
  };

  return (
    <Box sx={{ 
      m: 0,
      width: '100%' 
    }}>
      {/* 页面标题和添加按钮 */}
      <Box sx={{ mb: 1, px: '10px' }}>
        <Box sx={{ 
          display: 'flex', 
          alignItems: 'flex-end', 
          justifyContent: 'space-between',
          mt: '20px',
          mb: 0
        }}>
          <Typography 
            variant="h5" 
            component="h1" 
            sx={{ 
              fontWeight: 500,
              fontSize: { xs: '1.1rem', sm: '1.3rem' },
              mb: 0
            }}
          >
            记录管理
          </Typography>
          <Box sx={{ flexShrink: 0, ml: 3 }}>
            <Button 
              component={Link} 
              to="/records" 
              startIcon={<AddIcon />}
              variant="contained"
              color="primary"
              sx={{ minWidth: 80, px: 2, fontSize: '0.75rem', mb: '3px' }}
            >
              新建
            </Button>
          </Box>
        </Box>
        <Divider sx={{ mb: { xs: 2, md: 3 } }} />
      </Box>
      
      {/* 搜索和筛选区 */}
      <Paper 
        elevation={0} 
        sx={{ 
          p: { xs: 2, md: 3 }, 
          mb: { xs: 2, md: 3 },
          pl: '20px',
          pr: '20px',
          '--Paper-shadow': 'none',
          borderLeftWidth: 0,
          borderRightWidth: 0,
          borderTop: 0,
          borderBottom: 0
        }}
      >
        <Box sx={{ 
          display: 'grid',
          gridTemplateColumns: { xs: '1fr', md: '1fr auto' },
          gap: 2,
          mb: 2
        }}>
          {/* 搜索框 */}
          <TextField
            fullWidth
            label="搜索记录"
            variant="outlined"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="搜索标题、内容、标签、日期、记录类型等（支持多关键词）"
            size="small"
            InputProps={{
              startAdornment: (
                <Tooltip title="支持多关键词搜索，用空格分隔。可搜索：标题、内容、记录类型、记录日期、病程阶段、自定义标签等">
                  <SearchOutlined sx={{ color: 'action.active', mr: 1 }} />
                </Tooltip>
              ),
              endAdornment: debouncedSearchTerm && (
                <Tooltip title={searchDebugMode ? "关闭搜索调试模式" : "开启搜索调试模式，查看匹配原因"}>
                  <IconButton 
                    size="small" 
                    onClick={toggleSearchDebugMode}
                    color={searchDebugMode ? "primary" : "default"}
                  >
                    <LabelIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              ),
              style: { fontSize: '0.75rem' }
            }}
            InputLabelProps={{
              style: { fontSize: '0.75rem' }
            }}
          />
        </Box>
        
        {/* 筛选条件 */}
        <Box sx={{ 
          display: 'grid',
          gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)' },
          gap: 2
        }}>
          {/* 患者筛选 */}
          <FormControl size="small" fullWidth>
            <InputLabel sx={{ fontSize: '0.75rem' }}>患者</InputLabel>
            <Select
              value={filter.patientId}
              label="患者"
              onChange={(e) => setFilter({...filter, patientId: e.target.value})}
              sx={{ fontSize: '0.75rem' }}
            >
              <MenuItem value="" sx={{ fontSize: '0.75rem' }}>所有患者</MenuItem>
              {patients.map((patient: any) => (
                <MenuItem key={patient.id} value={patient.id} sx={{ fontSize: '0.75rem' }}>
                  {patient.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          
          {/* 病理筛选 */}
          <FormControl size="small" fullWidth>
            <InputLabel sx={{ fontSize: '0.75rem' }}>病理</InputLabel>
            <Select
              value={filter.diseaseId}
              label="病理"
              onChange={(e) => setFilter({...filter, diseaseId: e.target.value})}
              disabled={!filter.patientId}
              sx={{ fontSize: '0.75rem' }}
            >
              <MenuItem value="" sx={{ fontSize: '0.75rem' }}>所有病理</MenuItem>
              {filteredDiseases.map((disease: any) => (
                <MenuItem key={disease.id} value={disease.id} sx={{ fontSize: '0.75rem' }}>
                  {disease.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          
          <FormControl size="small" fullWidth>
            <InputLabel sx={{ fontSize: '0.75rem' }}>记录类型</InputLabel>
            <Select
              value={filter.recordType}
              label="记录类型"
              onChange={(e) => setFilter({...filter, recordType: e.target.value})}
              sx={{ fontSize: '0.75rem' }}
            >
              <MenuItem value="" sx={{ fontSize: '0.75rem' }}>所有类型</MenuItem>
              
              {/* 诊断相关 */}
              <MenuItem value={RecordTypeEnum.DIAGNOSIS} sx={{ fontSize: '0.75rem' }}>诊断</MenuItem>
              <MenuItem value={RecordTypeEnum.AUX_DIAGNOSIS} sx={{ fontSize: '0.75rem' }}>辅诊</MenuItem>
              
              {/* 症状相关 */}
              <MenuItem value={RecordTypeEnum.SYMPTOM} sx={{ fontSize: '0.75rem' }}>症状</MenuItem>
              <MenuItem value={RecordTypeEnum.SELF_DESCRIPTION} sx={{ fontSize: '0.75rem' }}>自述</MenuItem>
              
              {/* 治疗相关 */}
              <MenuItem value={RecordTypeEnum.TREATMENT} sx={{ fontSize: '0.75rem' }}>治疗</MenuItem>
              <MenuItem value={RecordTypeEnum.SURGERY} sx={{ fontSize: '0.75rem' }}>手术</MenuItem>
              <MenuItem value={RecordTypeEnum.PHYSICAL_THERAPY} sx={{ fontSize: '0.75rem' }}>理疗</MenuItem>
              <MenuItem value={RecordTypeEnum.REHABILITATION} sx={{ fontSize: '0.75rem' }}>康复</MenuItem>
              
              {/* 药物相关 */}
              <MenuItem value={RecordTypeEnum.MEDICATION} sx={{ fontSize: '0.75rem' }}>用药</MenuItem>
              
              {/* 检查与检验 */}
              <MenuItem value={RecordTypeEnum.EXAMINATION} sx={{ fontSize: '0.75rem' }}>检查</MenuItem>
              <MenuItem value={RecordTypeEnum.LAB_TEST} sx={{ fontSize: '0.75rem' }}>化验</MenuItem>
              <MenuItem value={RecordTypeEnum.MONITORING} sx={{ fontSize: '0.75rem' }}>监测</MenuItem>
              <MenuItem value={RecordTypeEnum.REPORT} sx={{ fontSize: '0.75rem' }}>报告</MenuItem>
              
              {/* 就诊相关 */}
              <MenuItem value={RecordTypeEnum.HOSPITALIZATION} sx={{ fontSize: '0.75rem' }}>住院</MenuItem>
              <MenuItem value={RecordTypeEnum.DISCHARGE} sx={{ fontSize: '0.75rem' }}>出院</MenuItem>
              <MenuItem value={RecordTypeEnum.REVISIT} sx={{ fontSize: '0.75rem' }}>复诊</MenuItem>
              <MenuItem value={RecordTypeEnum.FOLLOW_UP} sx={{ fontSize: '0.75rem' }}>随访</MenuItem>
              <MenuItem value={RecordTypeEnum.APPOINTMENT} sx={{ fontSize: '0.75rem' }}>预约</MenuItem>
              <MenuItem value={RecordTypeEnum.REFERRAL} sx={{ fontSize: '0.75rem' }}>转诊</MenuItem>
              
              {/* 评估相关 */}
              <MenuItem value={RecordTypeEnum.ASSESSMENT} sx={{ fontSize: '0.75rem' }}>评估</MenuItem>
              <MenuItem value={RecordTypeEnum.PROGNOSIS} sx={{ fontSize: '0.75rem' }}>预后</MenuItem>
              <MenuItem value={RecordTypeEnum.PSYCHOLOGY} sx={{ fontSize: '0.75rem' }}>心理</MenuItem>
              <MenuItem value={RecordTypeEnum.NURSING} sx={{ fontSize: '0.75rem' }}>护理</MenuItem>
              
              {/* 其他类型 */}
              <MenuItem value="AI_ANALYSIS" sx={{ fontSize: '0.75rem' }}>辅医智能分析报告</MenuItem>
              <MenuItem value={RecordTypeEnum.OTHER} sx={{ fontSize: '0.75rem' }}>其他</MenuItem>
            </Select>
          </FormControl>
          
          <FormControl size="small" fullWidth>
            <InputLabel sx={{ fontSize: '0.75rem' }}>严重程度</InputLabel>
            <Select
              value={filter.severity}
              label="严重程度"
              onChange={(e) => setFilter({...filter, severity: e.target.value})}
              sx={{ fontSize: '0.75rem' }}
            >
              <MenuItem value="" sx={{ fontSize: '0.75rem' }}>所有程度</MenuItem>
              <MenuItem value="MILD" sx={{ fontSize: '0.75rem' }}>轻微</MenuItem>
              <MenuItem value="MODERATE" sx={{ fontSize: '0.75rem' }}>中等</MenuItem>
              <MenuItem value="SEVERE" sx={{ fontSize: '0.75rem' }}>严重</MenuItem>
              <MenuItem value="CRITICAL" sx={{ fontSize: '0.75rem' }}>危重</MenuItem>
            </Select>
          </FormControl>
        </Box>
      </Paper>
      
      {/* 加载中 */}
      {isLoading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : (
        data?.records?.length > 0 ? (
          isMobile ? (
            // 移动端卡片式布局 - 使用新的MobileRecordCard组件
            <>
              {data.records.map((record: any) => (
                <Box key={record.id} sx={{ px: '10px' }}>
                  <MobileRecordCard
                    record={record}
                    onView={handleViewRecord}
                    onEdit={handleEditRecord}
                    onDelete={handleDeleteRecord}
                    getRecordTypeFullColors={getRecordTypeFullColors}
                    getRecordTypeName={getRecordTypeName}
                    getSeverityMainColor={getSeverityMainColor}
                    formatDateTime={formatDateTime}
                    truncateText={truncateText}
                    parseRecordType={parseRecordType}
                  />
                  {renderMatchInfo(record)}
                </Box>
              ))}
              
              {/* 分页控件 */}
              <Box sx={{ mt: 2 }}>
                <TablePagination
                  rowsPerPageOptions={[5, 10, 25]}
                  component="div"
                  count={data.meta?.totalItems || 0}
                  rowsPerPage={rowsPerPage}
                  page={page}
                  onPageChange={handleChangePage}
                  onRowsPerPageChange={handleChangeRowsPerPage}
                  labelRowsPerPage="每页行数:"
                  labelDisplayedRows={({ from, to, count }) => `${from}-${to} / ${count}`}
                />
              </Box>
            </>
          ) : (
            // 桌面端表格布局
            <Paper 
              elevation={0} 
              sx={{ 
                mb: 3,
                px: '20px',
                '--Paper-shadow': 'none',
                borderLeftWidth: 0,
                borderRightWidth: 0,
                borderTop: 0,
                borderBottom: 0
              }}
            >
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell sx={{ fontSize: '0.75rem' }}>标题</TableCell>
                      <TableCell sx={{ fontSize: '0.75rem' }}>类型</TableCell>
                      <TableCell sx={{ fontSize: '0.75rem' }}>严重程度</TableCell>
                      <TableCell sx={{ fontSize: '0.75rem' }}>日期</TableCell>
                      <TableCell align="right" sx={{ fontSize: '0.75rem' }}>操作</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {data.records.map((record: any) => (
                      <React.Fragment key={record.id}>
                        <TableRow hover>
                          <TableCell sx={{ fontSize: '0.75rem' }}>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              {record.isImportant === true && (
                                <Tooltip title="重要记录">
                                  <BookmarkIcon color="error" sx={{ mr: 1, fontSize: '0.75rem' }} />
                                </Tooltip>
                              )}
                              <Typography 
                                variant="subtitle1" 
                                component="div" 
                                sx={{ 
                                  fontWeight: 600,
                                  fontSize: '0.75rem',
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  whiteSpace: 'nowrap'
                                }}
                              >
                                {truncateText(record.title, 12)}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell sx={{ fontSize: '0.75rem' }}>
                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                              {/* 记录类型标签 */}
                              <Chip
                                label={(() => {
                                  const recordTypesArray = parseRecordType(record.recordType || record.primaryType || 'NOTE');
                                  let stageText = '';
                                  if (record.stageNode || record.stagePhase) {
                                    stageText = getStageText(record.stageNode, record.stagePhase);
                                  }
                                  return stageText ? `${getRecordTypeName(recordTypesArray)} - ${stageText}` : getRecordTypeName(recordTypesArray);
                                })()}
                                size="small"
                                sx={{ 
                                  fontSize: '0.75rem',
                                  ...((() => {
                                    const recordTypesArray = parseRecordType(record.recordType || record.primaryType || 'NOTE');
                                    const primaryType = recordTypesArray[0] || 'NOTE';
                                    const typeColors = getRecordTypeFullColors(primaryType);
                                    
                                    if (record.stageNode || record.stagePhase) {
                                      const colors = getStageColor(record.stageNode, record.stagePhase);
                                      return {
                                        bgcolor: colors.light,
                                        color: colors.dark,
                                        border: `1px solid ${colors.main}`,
                                      };
                                    } else {
                                      return {
                                        bgcolor: typeColors.light,
                                        color: typeColors.dark,
                                        border: `1px solid ${typeColors.main}`,
                                      };
                                    }
                                  })()),
                                  fontWeight: 600
                                }}
                              />
                              
                              {/* 自定义标签 */}
                              {record.customTags && record.customTags.trim() !== '' && 
                                record.customTags.split(',').map((tag: string, index: number) => {
                                  const tagText = tag.trim();
                                  if (!tagText) return null;
                                  
                                  // 使用个人标签颜色
                                  return (
                                    <Chip
                                      key={index}
                                      label={tagText}
                                      size="small"
                                      icon={<LabelIcon fontSize="small" />}
                                      variant="outlined"
                                      sx={{
                                        fontSize: '0.75rem',
                                        height: 20,
                                        // 使用主记录类型的主色，更加明显
                                        bgcolor: PERSONAL_TAG_LIGHT_COLOR,
                                        color: PERSONAL_TAG_COLOR,
                                        border: `1px solid ${PERSONAL_TAG_COLOR}`,
                                        fontWeight: 500,
                                        '& .MuiChip-icon': {
                                          color: PERSONAL_TAG_COLOR,
                                          marginRight: '2px',
                                          fontSize: '0.8rem'
                                        }
                                      }}
                                    />
                                  );
                                })
                              }
                            </Box>
                          </TableCell>
                          <TableCell sx={{ fontSize: '0.75rem' }}>
                            <Chip
                              label={getSeverityText(record.severity)}
                              size="small"
                              sx={{
                                fontSize: '0.75rem',
                                // 自定义颜色，使颜色更明显
                                bgcolor: getSeverityMainColor(record.severity),
                                border: `1px solid ${getSeverityMainColor(record.severity)}`,
                                color: 'white',
                                fontWeight: 600
                              }}
                            />
                          </TableCell>
                          <TableCell sx={{ fontSize: '0.75rem' }}>{formatDateTime(record.recordDate)}</TableCell>
                          <TableCell align="right" sx={{ fontSize: '0.75rem' }}>
                            <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                              <Tooltip title="查看">
                                <IconButton
                                  size="small"
                                  onClick={() => handleViewRecord(record.id)}
                                  color="primary"
                                >
                                  <VisibilityOutlined fontSize="small" />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="编辑">
                                <IconButton
                                  size="small"
                                  onClick={() => handleEditRecord(record.id)}
                                  color="primary"
                                >
                                  <EditOutlined fontSize="small" />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="删除">
                                <IconButton
                                  size="small"
                                  onClick={() => handleDeleteRecord(record.id)}
                                  color="error"
                                >
                                  <DeleteOutlined fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            </Box>
                          </TableCell>
                        </TableRow>
                        {/* 添加匹配信息行 */}
                        {searchDebugMode && record._debug && debouncedSearchTerm && (
                          <TableRow>
                            <TableCell colSpan={5} sx={{ py: 0.5, bgcolor: '#f5f5f5' }}>
                              {renderMatchInfo(record)}
                            </TableCell>
                          </TableRow>
                        )}
                      </React.Fragment>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
              
              <TablePagination
                rowsPerPageOptions={[5, 10, 25]}
                component="div"
                count={data.meta?.totalItems || 0}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                labelRowsPerPage="每页行数:"
                labelDisplayedRows={({ from, to, count }) => `${from}-${to} / ${count}`}
                sx={{ fontSize: '0.75rem' }}
              />
            </Paper>
          )
        ) : (
          <Paper 
            elevation={0} 
            sx={{ 
              p: 3, 
              textAlign: 'center', 
              px: '20px',
              '--Paper-shadow': 'none',
              borderLeftWidth: 0,
              borderRightWidth: 0,
              borderTop: 0,
              borderBottom: 0
            }}
          >
            <Typography color="text.secondary" sx={{ fontSize: '0.75rem' }}>
              暂无记录数据
            </Typography>
            <Button 
              component={Link} 
              to="/records" 
              startIcon={<AddIcon />}
              variant="contained"
              color="primary"
              size="small"
              sx={{ mt: 2, fontSize: '0.75rem' }}
            >
              立即创建记录
            </Button>
          </Paper>
        )
      )}

      {/* 删除确认对话框 */}
      <Dialog
        open={deleteDialogOpen}
        onClose={cancelDelete}
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <DialogTitle id="delete-dialog-title">
          确认删除
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" id="delete-dialog-description">
            您确定要删除此记录吗？此操作无法撤销。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={cancelDelete} color="primary">
            取消
          </Button>
          <Button onClick={confirmDelete} color="error" variant="contained" autoFocus>
            删除
          </Button>
        </DialogActions>
      </Dialog>

      {/* 通知提示 */}
      <Snackbar
        open={notification.open}
        autoHideDuration={5000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default RecordManagePage;