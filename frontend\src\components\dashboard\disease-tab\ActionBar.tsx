import React from 'react';
import { 
  Box, 
  Button, 
  Paper,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  AddCircleOutline as AddIcon,
  SmartToy as AIIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

interface ActionBarProps {
  disease: any;
  hasAIReport: boolean;
  selectedDiseaseId: string;
}

/**
 * 操作栏组件
 * 提供病理相关的操作按钮
 */
const ActionBar: React.FC<ActionBarProps> = ({ 
  disease, 
  hasAIReport, 
  selectedDiseaseId 
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  // 添加记录处理
  const handleAddRecord = () => {
    navigate(`/records/new?diseaseId=${selectedDiseaseId}`);
  };
  
  // 生成AI报告处理
  const handleGenerateAIReport = () => {
    navigate(`/ai-assistant?diseaseId=${selectedDiseaseId}`);
  };
  
  // 移动端布局
  if (isMobile) {
    return (
      <Paper 
        elevation={0} 
        sx={{ 
          p: 1.5,
          borderRadius: 2,
          position: 'sticky',
          bottom: 16,
          zIndex: 10,
          border: `1px solid ${theme.palette.divider}`
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', gap: 1 }}>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAddRecord}
            fullWidth
            sx={{ textTransform: 'none' }}
          >
            添加记录
          </Button>
          
          <Button
            variant="outlined"
            color="secondary"
            startIcon={<AIIcon />}
            onClick={handleGenerateAIReport}
            fullWidth
            sx={{ textTransform: 'none' }}
          >
            {hasAIReport ? '更新AI报告' : '生成AI报告'}
          </Button>
        </Box>
      </Paper>
    );
  }
  
  // 桌面端布局
  return (
    <Paper 
      elevation={0} 
      sx={{ 
        p: 2,
        borderRadius: 2,
        border: `1px solid ${theme.palette.divider}`
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAddRecord}
            sx={{ textTransform: 'none' }}
          >
            添加记录
          </Button>
          
          <Button
            variant="outlined"
            color="secondary"
            startIcon={<AIIcon />}
            onClick={handleGenerateAIReport}
            sx={{ textTransform: 'none' }}
          >
            {hasAIReport ? '更新AI报告' : '生成AI报告'}
          </Button>
        </Box>
      </Box>
    </Paper>
  );
};

export default ActionBar; 