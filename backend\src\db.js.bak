const knex = require('knex')(require('../knexfile').development);
const { Model } = require('objection');

// 设置 Objection.js
Model.knex(knex);

/**
 * 设置数据库表结构和初始数据
 */
async function setUpDb() {
  console.log('正在初始化数据库...');
  
  try {
    // 检查是否需要创建users表
    const hasUsersTable = await knex.schema.hasTable('users');
    if (!hasUsersTable) {
      console.log('创建users表...');
      await knex.schema.createTable('users', table => {
        table.string('id').primary();
        table.string('username').unique().notNullable();
        table.string('email').unique().nullable();
        table.string('phoneNumber').unique().nullable();
        table.string('passwordHash').notNullable();
        table.string('avatar').nullable();
        table.string('role').defaultTo('USER');
        table.string('level').defaultTo('PERSONAL');
        table.integer('activeDiseaseLimit').defaultTo(5);
        table.integer('aiUsageCount').defaultTo(0);
        table.dateTime('aiUsageResetAt').nullable();
        table.integer('familyMemberLimit').defaultTo(3);
        table.boolean('isActive').defaultTo(true);
        table.dateTime('createdAt').notNullable();
        table.dateTime('updatedAt').notNullable();
        table.dateTime('lastLoginAt').nullable();
        table.dateTime('deletedAt').nullable();
      });
    }
    
    // 检查是否需要创建患者表
    const hasPatientsTable = await knex.schema.hasTable('patients');
    if (!hasPatientsTable) {
      console.log('创建patients表...');
      await knex.schema.createTable('patients', table => {
        table.string('id').primary();
        table.string('userId').notNullable().references('id').inTable('users');
        table.string('name').notNullable();
        table.string('gender').notNullable();
        table.string('birthDate').nullable();
        table.string('phoneNumber').nullable();
        table.string('email').nullable();
        table.string('address').nullable();
        table.string('idCard').nullable().unique();
        table.string('medicareCard').nullable().unique();
        table.string('medicareLocation').nullable();
        table.string('bloodType').nullable();
        table.string('emergencyContactName').nullable();
        table.string('emergencyContactPhone').nullable();
        table.string('emergencyContactRelationship').nullable();
        table.text('pastMedicalHistory').nullable();
        table.text('familyMedicalHistory').nullable();
        table.text('allergyHistory').nullable();
        table.string('lastVisitDate').nullable();
        table.integer('isPrimary').defaultTo(0); // 0: 不是本人, 1: 是本人
        table.dateTime('createdAt').notNullable();
        table.dateTime('updatedAt').notNullable();
        table.dateTime('deletedAt').nullable();
        
        // 添加索引
        table.index('userId');
        table.index('name');
      });
    }
    
    // 检查是否需要创建疾病表
    const hasDiseasesTable = await knex.schema.hasTable('diseases');
    if (!hasDiseasesTable) {
      console.log('创建diseases表...');
      await knex.schema.createTable('diseases', table => {
        table.string('id').primary();
        table.string('userId').notNullable().references('id').inTable('users');
        table.string('patientId').notNullable().references('id').inTable('patients');
        table.string('name').notNullable();
        table.string('diagnosisDate').nullable();
        table.string('stage').nullable();
        table.text('description').nullable();
        table.text('treatment').nullable();
        table.string('status').defaultTo('ACTIVE');
        table.dateTime('createdAt').notNullable();
        table.dateTime('updatedAt').notNullable();
        table.dateTime('deletedAt').nullable();
        
        // 添加索引
        table.index('userId');
        table.index('patientId');
        table.index(['userId', 'patientId']);
      });
    }
    
    // 检查是否需要创建用户等级限制表
    const hasUserLevelLimitsTable = await knex.schema.hasTable('user_level_limits');
    if (!hasUserLevelLimitsTable) {
      console.log('创建user_level_limits表...');
      await knex.schema.createTable('user_level_limits', table => {
        table.increments('id').primary();
        table.string('levelType').notNullable().unique();
        table.string('levelName').notNullable();
        table.text('description').nullable();
        table.integer('maxPatients').notNullable();
        table.integer('maxPathologies').notNullable();
        table.integer('maxAttachmentSize').notNullable(); // 单位: MB
        table.integer('maxTotalStorage').notNullable(); // 单位: MB
        table.integer('aiUsageLimit').notNullable(); // AI使用次数每月限制
        table.dateTime('createdAt').notNullable();
        table.dateTime('updatedAt').notNullable();
      });
      
      // 插入默认用户等级限制
      await knex('user_level_limits').insert([
        {
          levelType: 'FREE',
          levelName: '免费版',
          description: '基础功能，有限制',
          maxPatients: 5,
          maxPathologies: 10,
          maxAttachmentSize: 5,
          maxTotalStorage: 50,
          aiUsageLimit: 10,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          levelType: 'BASIC',
          levelName: '基础版',
          description: '更多功能，较少限制',
          maxPatients: 15,
          maxPathologies: 30,
          maxAttachmentSize: 10,
          maxTotalStorage: 200,
          aiUsageLimit: 30,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          levelType: 'PREMIUM',
          levelName: '高级版',
          description: '全部功能，几乎无限制',
          maxPatients: 50,
          maxPathologies: 100,
          maxAttachmentSize: 50,
          maxTotalStorage: 1000,
          aiUsageLimit: 100,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          levelType: 'UNLIMITED',
          levelName: '无限版',
          description: '无任何限制',
          maxPatients: -1, // -1表示无限制
          maxPathologies: -1,
          maxAttachmentSize: -1,
          maxTotalStorage: -1,
          aiUsageLimit: -1,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ]);
    }
    
    console.log('数据库初始化完成');
  } catch (error) {
    console.error('数据库初始化失败:', error);
    throw error;
  }
}

module.exports = {
  knex,
  setUpDb
}; 