/**
 * 数据缓存工具
 * 用于缓存API请求结果，减少重复请求，优化性能
 */

interface CacheItem<T> {
  data: T;
  timestamp: number;
  expiry: number; // 缓存过期时间（毫秒）
}

interface CacheOptions {
  expiry?: number; // 缓存过期时间（毫秒）
  disableCache?: boolean; // 是否禁用缓存
}

/**
 * 数据缓存
 */
class DataCache {
  private cache: Map<string, CacheItem<any>> = new Map();
  private defaultExpiry: number = 5 * 60 * 1000; // 默认缓存5分钟
  
  // 设置默认过期时间
  setDefaultExpiry(expiry: number): void {
    this.defaultExpiry = expiry;
  }
  
  // 生成缓存键
  private generateKey(key: string, params?: any): string {
    if (!params) return key;
    
    // 对params进行排序，确保相同参数生成相同的键
    if (typeof params === 'object') {
      const sortedParams = Object.keys(params)
        .sort()
        .reduce((result: any, key) => {
          if (params[key] !== undefined && params[key] !== null) {
            result[key] = params[key];
          }
          return result;
        }, {});
      
      return `${key}:${JSON.stringify(sortedParams)}`;
    }
    
    return `${key}:${String(params)}`;
  }
  
  // 获取缓存数据
  get<T>(key: string, params?: any): T | null {
    const cacheKey = this.generateKey(key, params);
    const item = this.cache.get(cacheKey);
    
    // 如果缓存不存在或已过期，返回null
    if (!item || Date.now() > item.timestamp + item.expiry) {
      if (item) {
        // 缓存已过期，删除
        this.cache.delete(cacheKey);
        console.log(`[数据缓存] 缓存过期，已删除: ${cacheKey}`);
      }
      return null;
    }
    
    console.log(`[数据缓存] 命中缓存: ${cacheKey}`);
    return item.data as T;
  }
  
  // 设置缓存数据
  set<T>(key: string, data: T, params?: any, options?: CacheOptions): void {
    // 如果禁用缓存，直接返回
    if (options?.disableCache) {
      return;
    }
    
    const cacheKey = this.generateKey(key, params);
    const expiry = options?.expiry || this.defaultExpiry;
    
    this.cache.set(cacheKey, {
      data,
      timestamp: Date.now(),
      expiry
    });
    
    console.log(`[数据缓存] 已缓存数据: ${cacheKey}, 过期时间: ${expiry}ms`);
  }
  
  // 删除缓存
  delete(key: string, params?: any): void {
    const cacheKey = this.generateKey(key, params);
    this.cache.delete(cacheKey);
    console.log(`[数据缓存] 已删除缓存: ${cacheKey}`);
  }
  
  // 清除所有缓存
  clear(): void {
    const size = this.cache.size;
    console.log(`[数据缓存] 准备清除所有缓存，当前缓存数量: ${size}`);
    
    if (size > 0) {
      console.log('[数据缓存] 缓存键列表:');
      Array.from(this.cache.keys()).forEach(key => {
        console.log(`- ${key}`);
      });
    }
    
    this.cache.clear();
    console.log('[数据缓存] 已清除所有缓存');
  }
  
  // 清除指定前缀的缓存
  clearByPrefix(prefix: string): void {
    const keysToDelete = Array.from(this.cache.keys()).filter(key => key.startsWith(prefix));
    
    keysToDelete.forEach(key => {
      this.cache.delete(key);
    });
    
    console.log(`[数据缓存] 已清除前缀为 "${prefix}" 的 ${keysToDelete.length} 项缓存`);
  }
  
  // 清除病理相关的所有缓存
  clearDiseaseRelatedCache(diseaseId?: string): void {
    console.log(`[数据缓存] 开始清除病理相关缓存${diseaseId ? ': ' + diseaseId : '所有'}`);
    
    // 病理记录相关缓存
    if (diseaseId) {
      this.clearByPrefix(`records:${diseaseId}`);
    } else {
      this.clearByPrefix('records:');
    }
    
    // 清除病理数据相关缓存
    this.clearByPrefix('disease:');
    this.clearByPrefix('diseases:');
    
    // 清除AI报告相关缓存
    this.clearByPrefix('aiReport:');
    
    // 清除患者相关缓存
    this.clearByPrefix('patient:');
    this.clearByPrefix('patients:');
    
    // 清除可能的嵌套缓存键
    const keysToDelete: string[] = [];
    this.cache.forEach((_, key) => {
      if (key.includes('disease') || key.includes('Disease') || 
          key.includes('patient') || key.includes('Patient') || 
          key.includes('record') || key.includes('Record')) {
        keysToDelete.push(key);
      }
    });
    
    // 批量删除匹配的键
    if (keysToDelete.length > 0) {
      keysToDelete.forEach(key => {
        this.cache.delete(key);
        console.log(`[数据缓存] 已删除匹配键: ${key}`);
      });
    }
    
    console.log(`[数据缓存] 病理相关缓存清除完成, 剩余缓存数量: ${this.cache.size}`);
    
    // 确保缓存大小不超过阈值，防止内存泄漏
    this.enforceMaxSize();
  }
  
  // 确保缓存大小不超过最大阈值
  private enforceMaxSize(maxSize: number = 100): void {
    if (this.cache.size <= maxSize) return;
    
    console.log(`[数据缓存] 缓存大小(${this.cache.size})超过阈值(${maxSize})，清理最旧条目`);
    
    // 获取所有键并按时间戳排序
    const keys = Array.from(this.cache.keys());
    const entries = keys.map(key => {
      const item = this.cache.get(key);
      return { key, timestamp: item ? item.timestamp : 0 };
    });
    
    // 按时间戳升序排序（最旧的在前）
    entries.sort((a, b) => a.timestamp - b.timestamp);
    
    // 删除最旧的条目，直到缓存大小在阈值以下
    const deleteCount = this.cache.size - maxSize;
    for (let i = 0; i < deleteCount; i++) {
      if (i < entries.length) {
        this.cache.delete(entries[i].key);
        console.log(`[数据缓存] 删除最旧缓存条目: ${entries[i].key}`);
      }
    }
    
    console.log(`[数据缓存] 完成缓存清理，当前大小: ${this.cache.size}`);
  }
  
  // 获取缓存大小
  size(): number {
    return this.cache.size;
  }
  
  // 获取缓存状态
  getStatus(): { size: number, items: string[] } {
    return {
      size: this.cache.size,
      items: Array.from(this.cache.keys())
    };
  }
  
  // 带缓存的异步函数包装器 - 修复ES5中异步函数引用arguments的问题
  async withCache<T, P extends any[]>(
    fn: (...args: P) => Promise<T>,
    key: string,
    paramsFn: (...args: P) => any,
    options?: CacheOptions,
    ...args: P
  ): Promise<T> {
    // 如果禁用缓存，直接执行原函数
    if (options?.disableCache) {
      return fn(...args);
    }
    
    const params = paramsFn(...args);
    const cachedData = this.get<T>(key, params);
    
    if (cachedData !== null) {
      return cachedData;
    }
    
    // 缓存未命中，执行原函数
    const result = await fn(...args);
    
    // 缓存结果
    this.set(key, result, params, options);
    
    return result;
  }
}

// 导出单例
export const dataCache = new DataCache();

export default dataCache; 