import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  DialogContentText,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Card,
  CardContent,
  CircularProgress,
  Tooltip,
  Divider,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Menu,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { zhCN } from 'date-fns/locale';
import {
  Search as SearchIcon,
  FilterList as FilterListIcon,
  Refresh as RefreshIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  Person as PersonIcon,
  MedicalServices as MedicalServicesIcon,
  Accessible as AccessibleIcon,
  SyncProblem as SyncProblemIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  ExpandMore as ExpandMoreIcon,
  AccessTime as AccessTimeIcon,
  MoreVert as MoreVertIcon
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import { useMediaQuery } from '@mui/material';
import axios from 'axios';
import { API_BASE_URL } from '../../config/api';
import { API_PATHS } from '../../config/apiPaths';
import { useSnackbar } from 'notistack';
import { format } from 'date-fns';
import { ThemeProvider, createTheme as createMuiTheme } from '@mui/material/styles';

// AI报告状态类型
type AIReportStatus = 'PROCESSING' | 'COMPLETED' | 'FAILED';

// AI报告接口
interface AIReport {
  id: string;
  userId: string;
  username: string;
  patientId: string;
  patientName: string;
  diseaseId: string;
  title: string;
  templateType: string;
  status: AIReportStatus;
  errorMessage?: string;
  createdAt: string;
  updatedAt: string;
}

// 报告详情接口
interface AIReportDetail extends AIReport {
  user: {
    id: string;
    username: string;
    email: string;
  };
  patient: {
    id: string;
    name: string;
    gender: string;
    birthDate: string;
  };
  disease: {
    id: string;
    name: string;
    description: string;
  };
  content: any;
  anonymizedInfo: any;
}

// 分页接口
interface Pagination {
  total: number;
  page: number;
  pageSize: number;
  pages: number;
}

/**
 * AI报告管理页面
 * 用于管理员查看和管理系统中的AI生成报告
 */
const AIReportManagementPage: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { enqueueSnackbar } = useSnackbar();

  // Create a theme with reduced font size based on the original theme
  const reducedFontSizeTheme = createMuiTheme(theme, {
    typography: {
      h1: { ...theme.typography.h1, fontSize: theme.typography.h1?.fontSize ? `calc(${theme.typography.h1.fontSize} - 0.4rem)`:'5.6rem' },
      h2: { ...theme.typography.h2, fontSize: theme.typography.h2?.fontSize ? `calc(${theme.typography.h2.fontSize} - 0.3rem)`:'3.45rem' },
      h3: { ...theme.typography.h3, fontSize: theme.typography.h3?.fontSize ? `calc(${theme.typography.h3.fontSize} - 0.25rem)`:'2.75rem' },
      h4: { ...theme.typography.h4, fontSize: theme.typography.h4?.fontSize ? `calc(${theme.typography.h4.fontSize} - 0.2rem)`:'1.925rem' },
      h5: { fontSize: '1.0rem' },
      h6: { fontSize: '0.85rem' },
      subtitle1: { fontSize: '0.75rem' },
      subtitle2: { fontSize: '0.65rem' },
      body1: { fontSize: '0.75rem' },
      body2: { fontSize: '0.65rem' },
      button: { fontSize: '0.65rem' },
      caption: { fontSize: '0.55rem' },
      overline: { fontSize: '0.55rem' },
    },
    components: {
      MuiInputLabel: { styleOverrides: { root: { fontSize: '0.75rem' } } },
      MuiMenuItem: { styleOverrides: { root: { fontSize: '0.75rem' } } },
      MuiTableCell: { styleOverrides: { root: { fontSize: '0.65rem' }, head: { fontSize: '0.7rem', fontWeight: 'bold' } } },
      MuiChip: { styleOverrides: { label: { fontSize: '0.55rem' }, labelSmall: { fontSize: '0.5rem' } } },
      MuiButton: { styleOverrides: { sizeSmall: { fontSize: '0.6rem' }, sizeMedium: { fontSize: '0.65rem' }, sizeLarge: { fontSize: '0.75rem' } } },
      MuiDialogTitle: { styleOverrides: { root: { fontSize: '0.85rem' } } },
      MuiDialogContentText: { styleOverrides: { root: { fontSize: '0.75rem' } } },
      MuiFormControlLabel: { styleOverrides: { label: { fontSize: '0.75rem' } } },
      MuiAlert: { styleOverrides: { message: { fontSize: '0.65rem' } } },
      MuiTablePagination: {
        styleOverrides: {
          caption: { fontSize: '0.65rem' },
          selectLabel: { fontSize: '0.65rem' },
          displayedRows: { fontSize: '0.65rem' }
        }
      }
    }
  });

  // 报告列表状态
  const [reports, setReports] = useState<AIReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 搜索和分页状态
  const [searchKeyword, setSearchKeyword] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [pagination, setPagination] = useState<Pagination>({
    total: 0,
    page: 0,
    pageSize: 10,
    pages: 0
  });

  // 筛选条件状态
  const [filterOpen, setFilterOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [userIdFilter, setUserIdFilter] = useState<string>('');
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);

  // 报告详情和删除确认状态
  const [detailDialogOpen, setDetailDialogOpen] = useState(false);
  const [selectedReport, setSelectedReport] = useState<AIReportDetail | null>(null);
  const [detailLoading, setDetailLoading] = useState(false);

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [reportToDelete, setReportToDelete] = useState<AIReport | null>(null);
  const [deleteReason, setDeleteReason] = useState('');
  const [deleteLoading, setDeleteLoading] = useState(false);

  // 更多操作菜单状态 (for mobile cards)
  const [mobileMenuAnchorEl, setMobileMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedReportForMenu, setSelectedReportForMenu] = useState<AIReport | null>(null);

  // 获取AI报告列表 - 使用useCallback包装
  const fetchReports = useCallback(async (filterParams = {}) => {
    setLoading(true);
    setError(null);

    try {
      // 构建查询参数
      const params: any = {
        page,
        pageSize: rowsPerPage,
        ...filterParams
      };

      if (searchKeyword.trim()) {
        params.search = searchKeyword.trim();
      }

      if (statusFilter) {
        params.status = statusFilter;
      }

      if (userIdFilter) {
        params.userId = userIdFilter;
      }

      if (startDate) {
        params.startDate = startDate.toISOString().split('T')[0];
      }

      if (endDate) {
        params.endDate = endDate.toISOString().split('T')[0];
      }

      // 发送请求获取报告列表
      // 使用API_PATHS常量确保路径一致性
      const response = await axios.get(`${API_BASE_URL}${API_PATHS.ADMIN.AI_REPORTS}`, {
        params,
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      // 修改判断条件：检查 response.data.reports 是否存在且为数组
      if (response && response.data && Array.isArray(response.data.reports)) {
        setReports(response.data.reports); // 使用 response.data.reports
        setPagination(response.data.pagination || {
          total: response.data.reports.length, // Fallback pagination, 使用 reports.length
          page: page, // 使用当前的 page state
          pageSize: rowsPerPage, // 使用当前的 rowsPerPage state
          pages: Math.ceil(response.data.reports.length / rowsPerPage) // 使用 reports.length
        });
        // setError(null); // 已经在函数开头设置
      } else {
        // 如果数据格式不符合预期，也视为错误
        console.error('获取AI报告列表响应成功，但数据格式不符合预期 (期望 response.data.reports 为数组):', response.data);
        setError(response.data?.message || '获取报告成功但数据格式错误');
      }
    } catch (err: any) {
      console.error('获取AI报告列表失败:', err);
      setError(err.response?.data?.message || err.message || '获取报告列表失败');
    } finally {
      setLoading(false);
    }
  }, [page, rowsPerPage, searchKeyword, statusFilter, userIdFilter, startDate, endDate]);

  // 初始加载报告数据
  useEffect(() => {
    fetchReports();
  }, [fetchReports, page, rowsPerPage, searchKeyword, statusFilter, userIdFilter, startDate, endDate]);

  // 应用筛选条件
  const applyFilters = () => {
    setPage(0); // 重置到第一页
    fetchReports({
      search: searchKeyword,
      status: statusFilter,
      userId: userIdFilter,
      startDate: startDate ? format(startDate, 'yyyy-MM-dd') : undefined,
      endDate: endDate ? format(endDate, 'yyyy-MM-dd') : undefined
    });
  };

  // 重置筛选条件
  const resetFilters = () => {
    setSearchKeyword('');
    setStatusFilter('');
    setUserIdFilter('');
    setStartDate(null);
    setEndDate(null);
    setPage(0);
    fetchReports({});
  };

  // 处理搜索输入变化
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchKeyword(event.target.value);
  };

  // 处理回车键搜索
  const handleSearchKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      applyFilters();
    }
  };

  // 处理分页变化
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  // 处理每页行数变化
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // 查看报告详情
  const viewReportDetail = async (report: AIReport) => {
    setSelectedReport(null);
    setDetailLoading(true);
    setDetailDialogOpen(true);

    try {
      // 使用API_PATHS常量确保路径一致性
      const response = await axios.get(`${API_BASE_URL}${API_PATHS.ADMIN.AI_REPORTS}/${report.id}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      setSelectedReport(response.data);
    } catch (err: any) {
      console.error('获取报告详情失败:', err);
      enqueueSnackbar('获取报告详情失败', { variant: 'error' });
      setDetailDialogOpen(false);
    } finally {
      setDetailLoading(false);
    }
  };

  // 关闭详情对话框
  const closeDetailDialog = () => {
    setDetailDialogOpen(false);
    setSelectedReport(null);
  };

  // 打开删除确认对话框
  const openDeleteDialog = (report: AIReport) => {
    setReportToDelete(report);
    setDeleteReason('');
    setDeleteDialogOpen(true);
  };

  // 关闭删除确认对话框
  const closeDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setReportToDelete(null);
    setDeleteReason('');
  };

  // 删除报告
  const deleteReport = async () => {
    if (!reportToDelete) return;

    setDeleteLoading(true);

    try {
      // 使用API_PATHS常量确保路径一致性
      await axios.delete(`${API_BASE_URL}${API_PATHS.ADMIN.AI_REPORTS}/${reportToDelete.id}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        },
        data: {
          reason: deleteReason || '管理员删除'
        }
      });

      enqueueSnackbar('AI报告已成功删除', { variant: 'success' });

      // 关闭对话框并刷新列表
      closeDeleteDialog();
      fetchReports();
    } catch (err: any) {
      console.error('删除AI报告失败:', err);
      enqueueSnackbar('删除AI报告失败: ' + (err.response?.data?.message || err.message), { variant: 'error' });
    } finally {
      setDeleteLoading(false);
    }
  };

  // 格式化时间
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return isNaN(date.getTime())
      ? '无效日期'
      : format(date, 'yyyy-MM-dd HH:mm:ss');
  };

  // 获取报告状态显示
  const getStatusChip = (status: AIReportStatus) => {
    switch (status) {
      case 'PROCESSING':
        return <Chip
          size="small"
          label="处理中"
          color="warning"
          variant="outlined"
          icon={<SyncProblemIcon />}
        />;
      case 'COMPLETED':
        return <Chip
          size="small"
          label="已完成"
          color="success"
          variant="outlined"
          icon={<CheckCircleIcon />}
        />;
      case 'FAILED':
        return <Chip
          size="small"
          label="失败"
          color="error"
          variant="outlined"
          icon={<ErrorIcon />}
        />;
      default:
        return <Chip
          size="small"
          label={status}
          color="default"
          variant="outlined"
        />;
    }
  };

  // 获取模板类型中文名称
  const getTemplateTypeLabel = (type: string): string => {
    switch (type) {
      case 'COMPREHENSIVE_ANALYSIS': return '综合分析';
      case 'DIFFERENTIAL_DIAGNOSIS': return '鉴别诊断';
      case 'TREATMENT_PLAN_OPTIONS': return '治疗方案';
      case 'SECOND_OPINION': return '第二诊疗意见';
      case 'PATIENT_SUMMARY': return '患者摘要';
      case 'CUSTOM_REPORT': return '自定义报告';
      default: return type;
    }
  };

  // 格式化JSON为可读形式
  const formatJsonForDisplay = (json: any): string => {
    if (!json) return '无数据';
    if (typeof json !== 'object') return String(json);
    return JSON.stringify(json, null, 2);
  };

  // 移动端菜单处理
  const handleMobileMenuOpen = (event: React.MouseEvent<HTMLElement>, report: AIReport) => {
    setMobileMenuAnchorEl(event.currentTarget);
    setSelectedReportForMenu(report);
  };

  const handleMobileMenuClose = () => {
    setMobileMenuAnchorEl(null);
    setSelectedReportForMenu(null);
  };

  const handleViewDetailFromMenu = () => {
    if (selectedReportForMenu) {
      viewReportDetail(selectedReportForMenu);
    }
    handleMobileMenuClose();
  };

  const handleDeleteFromMenu = () => {
    if (selectedReportForMenu) {
      openDeleteDialog(selectedReportForMenu);
    }
    handleMobileMenuClose();
  };

  return (
    <ThemeProvider theme={reducedFontSizeTheme}>
      <Box sx={{py: { xs: 1, sm: 2 }}}>
        <Typography variant="h6" component="h1" gutterBottom sx={{ mb: { xs: 1.5, sm: 2 } }}>
          AI 报告管理
        </Typography>

        {/* Search and Filter Bar */}
        <Paper elevation={1} sx={{ p: { xs: '10px', sm: 2 }, mb: { xs: 1.5, sm: 3 } }}>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
            <Box sx={{ flex: '1 1 300px', minWidth: 0 }}>
              <TextField
                placeholder="搜索报告..."
                variant="outlined"
                size="small"
                fullWidth
                value={searchKeyword}
                onChange={handleSearchChange}
                onKeyPress={handleSearchKeyPress}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Box>

            <Box sx={{ flex: '1 1 300px', display: 'flex', gap: 1, justifyContent: 'flex-end', alignItems: 'center' }}>
              <Button
                variant="outlined"
                color="primary"
                startIcon={<FilterListIcon />}
                onClick={() => setFilterOpen(!filterOpen)}
              >
                高级筛选
              </Button>

              <Button
                variant="outlined"
                color="primary"
                startIcon={<RefreshIcon />}
                onClick={() => fetchReports()}
                disabled={loading}
              >
                刷新
              </Button>
            </Box>

            {/* 高级筛选面板 */}
            {filterOpen && (
              <Box sx={{ width: '100%' }}>
                <Card variant="outlined">
                  <CardContent>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                      <Box sx={{ flex: '1 1 200px', minWidth: 0 }}>
                        <FormControl fullWidth size="small">
                          <InputLabel id="status-filter-label">状态</InputLabel>
                          <Select
                            labelId="status-filter-label"
                            value={statusFilter}
                            label="状态"
                            onChange={(e) => setStatusFilter(e.target.value)}
                          >
                            <MenuItem value="">全部</MenuItem>
                            <MenuItem value="PROCESSING">处理中</MenuItem>
                            <MenuItem value="COMPLETED">已完成</MenuItem>
                            <MenuItem value="FAILED">失败</MenuItem>
                          </Select>
                        </FormControl>
                      </Box>

                      <Box sx={{ flex: '1 1 200px', minWidth: 0 }}>
                        <TextField
                          label="用户ID"
                          size="small"
                          fullWidth
                          value={userIdFilter}
                          onChange={(e) => setUserIdFilter(e.target.value)}
                        />
                      </Box>

                      <Box sx={{ flex: '1 1 200px', minWidth: 0 }}>
                        <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={zhCN}>
                          <DatePicker
                            label="开始日期"
                            value={startDate}
                            onChange={setStartDate}
                            slotProps={{
                              textField: {
                                size: 'small',
                                fullWidth: true
                              }
                            }}
                          />
                        </LocalizationProvider>
                      </Box>

                      <Box sx={{ flex: '1 1 200px', minWidth: 0 }}>
                        <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={zhCN}>
                          <DatePicker
                            label="结束日期"
                            value={endDate}
                            onChange={setEndDate}
                            slotProps={{
                              textField: {
                                size: 'small',
                                fullWidth: true
                              }
                            }}
                          />
                        </LocalizationProvider>
                      </Box>

                      <Box sx={{ width: '100%', display: 'flex', justifyContent: 'flex-end', mt: 1 }}>
                        <Button
                          variant="outlined"
                          color="secondary"
                          onClick={resetFilters}
                          sx={{ mr: 1 }}
                        >
                          重置
                        </Button>
                        <Button
                          variant="contained"
                          color="primary"
                          onClick={applyFilters}
                        >
                          应用筛选
                        </Button>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Box>
            )}
          </Box>
        </Paper>

        {/* AI报告列表 */}
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', flexDirection: 'column', my: 4 }}>
            <CircularProgress sx={{ mb: 2 }} />
            <Typography>加载中...</Typography>
          </Box>
        ) : error ? (
          <Alert severity="error" sx={{ my: 4 }}>{error}</Alert>
        ) : (
          <>
            {isMobile ? (
              // 移动端卡片视图
              <Box>
                {reports.length === 0 ? (
                   <Typography sx={{ textAlign: 'center', my: 4 }}>
                     {searchKeyword || statusFilter || userIdFilter || startDate || endDate ?
                       '没有匹配的AI报告' : '暂无AI报告数据'}
                   </Typography>
                ) : (
                  reports.map((report) => (
                    <Card key={report.id} sx={{ mb: 2, boxShadow: 3 }}>
                      <CardContent sx={{ px: '10px', '&:last-child': { pb: '10px' } }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                          <Typography variant="h6" component="div" sx={{ flexGrow: 1, mr: 1, wordBreak: 'break-word', fontWeight: 'bold' }}>
                            {report.title}
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            {getStatusChip(report.status)}
                            <IconButton size="small" onClick={(event) => handleMobileMenuOpen(event, report)} sx={{ p: 0.5, ml: 0.5 }}>
                              <MoreVertIcon />
                            </IconButton>
                          </Box>
                        </Box>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          类型: {getTemplateTypeLabel(report.templateType)}
                        </Typography>
                        <Divider sx={{ my: 1 }} />
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: theme.spacing(1), fontSize: '0.875rem' }}>
                          <Box sx={{ width: { xs: '100%', sm: `calc(50% - ${theme.spacing(0.5)})` } }}>
                            <Typography variant="caption" color="text.secondary" display="block">
                              <PersonIcon fontSize="inherit" sx={{ verticalAlign: 'bottom', mr: 0.5 }} />
                              用户:
                            </Typography>
                            <Typography variant="body2">{report.username || 'N/A'}</Typography>
                          </Box>
                          <Box sx={{ width: { xs: '100%', sm: `calc(50% - ${theme.spacing(0.5)})` } }}>
                            <Typography variant="caption" color="text.secondary" display="block">
                              <AccessibleIcon fontSize="inherit" sx={{ verticalAlign: 'bottom', mr: 0.5 }} />
                              患者:
                            </Typography>
                            <Typography variant="body2">{report.patientName || 'N/A'}</Typography>
                          </Box>
                          <Box sx={{ width: '100%' }}>
                            <Typography variant="caption" color="text.secondary" display="block">
                              <AccessTimeIcon fontSize="inherit" sx={{ verticalAlign: 'bottom', mr: 0.5 }} />
                              生成时间:
                            </Typography>
                            <Typography variant="body2">{formatDateTime(report.createdAt)}</Typography>
                          </Box>
                        </Box>
                      </CardContent>
                    </Card>
                  ))
                )}
                <Menu
                  anchorEl={mobileMenuAnchorEl}
                  open={Boolean(mobileMenuAnchorEl)}
                  onClose={handleMobileMenuClose}
                >
                  <MenuItem onClick={handleViewDetailFromMenu}>
                    <ListItemIcon>
                      <VisibilityIcon fontSize="small" />
                    </ListItemIcon>
                    <ListItemText>查看详情</ListItemText>
                  </MenuItem>
                  <MenuItem onClick={handleDeleteFromMenu} sx={{ color: 'error.main' }}>
                    <ListItemIcon>
                      <DeleteIcon fontSize="small" color="error" />
                    </ListItemIcon>
                    <ListItemText>删除报告</ListItemText>
                  </MenuItem>
                </Menu>
              </Box>
            ) : (
              // 桌面端表格视图
              <Paper elevation={2} sx={{ width: '100%', overflow: 'hidden' }}>
                <TableContainer sx={{ maxHeight: 'calc(100vh - 300px)' }}>
                  <Table stickyHeader size={isMobile ? "small" : "medium"}>
                    <TableHead>
                      <TableRow>
                        <TableCell>生成时间</TableCell>
                        <TableCell>用户</TableCell>
                        <TableCell>患者</TableCell>
                        <TableCell>报告标题</TableCell>
                        <TableCell>模板类型</TableCell>
                        <TableCell>状态</TableCell>
                        <TableCell align="center">操作</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {reports.map((report) => (
                        <TableRow
                          key={report.id}
                          hover
                          sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                        >
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <AccessTimeIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                              {formatDateTime(report.createdAt)}
                            </Box>
                          </TableCell>

                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <PersonIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                              {report.username || '未知用户'}
                            </Box>
                          </TableCell>

                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <AccessibleIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                              {report.patientName || '未知患者'}
                            </Box>
                          </TableCell>

                          <TableCell>
                            <Box sx={{ maxWidth: 200, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                              <Tooltip title={report.title}>
                                <Typography variant="body2">
                                  {report.title}
                                </Typography>
                              </Tooltip>
                            </Box>
                          </TableCell>

                          <TableCell>
                            <Chip
                              size="small"
                              label={getTemplateTypeLabel(report.templateType)}
                              color="primary"
                              variant="outlined"
                            />
                          </TableCell>

                          <TableCell>
                            {getStatusChip(report.status)}
                          </TableCell>

                          <TableCell align="center">
                            <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                              <Tooltip title="查看详情">
                                <IconButton
                                  size="small"
                                  onClick={() => viewReportDetail(report)}
                                  sx={{ mr: 1 }}
                                >
                                  <VisibilityIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>

                              <Tooltip title="删除报告">
                                <IconButton
                                  size="small"
                                  color="error"
                                  onClick={() => openDeleteDialog(report)}
                                >
                                  <DeleteIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>

                {/* 分页控件 */}
                <TablePagination
                  rowsPerPageOptions={[5, 10, 25, 50]}
                  component="div"
                  count={pagination.total}
                  rowsPerPage={rowsPerPage}
                  page={page}
                  onPageChange={handleChangePage}
                  onRowsPerPageChange={handleChangeRowsPerPage}
                  labelRowsPerPage="每页行数:"
                  labelDisplayedRows={({ from, to, count }) => `${from}-${to} / 共${count}条`}
                />
              </Paper>
            )}
          </>
        )}

        {/* 报告详情对话框 */}
        <Dialog
          open={detailDialogOpen}
          onClose={closeDetailDialog}
          maxWidth="xl"
          fullWidth
        >
          <DialogTitle>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <MedicalServicesIcon sx={{ mr: 1 }} />
              AI报告详情
            </Box>
          </DialogTitle>
          <DialogContent dividers>
            {detailLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : !selectedReport ? (
              <Alert severity="error">未能加载报告详情</Alert>
            ) : (
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {/* 报告基本信息 */}
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>基本信息</Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                      <Box sx={{ flex: '1 1 calc(33% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">报告标题</Typography>
                        <Typography variant="body1">{selectedReport.title}</Typography>
                      </Box>
                      <Box sx={{ flex: '1 1 calc(33% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">报告ID</Typography>
                        <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>{selectedReport.id}</Typography>
                      </Box>
                      <Box sx={{ flex: '1 1 calc(33% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">创建时间</Typography>
                        <Typography variant="body2">{formatDateTime(selectedReport.createdAt)}</Typography>
                      </Box>
                      <Box sx={{ flex: '1 1 calc(33% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">模板类型</Typography>
                        <Typography variant="body2">{getTemplateTypeLabel(selectedReport.templateType)}</Typography>
                      </Box>
                      <Box sx={{ flex: '1 1 calc(33% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">状态</Typography>
                        {getStatusChip(selectedReport.status)}
                      </Box>
                      {selectedReport.errorMessage && (
                        <Box sx={{ flex: '1 1 calc(33% - 8px)', minWidth: '250px' }}>
                          <Typography variant="subtitle2" color="error">错误信息</Typography>
                          <Typography variant="body2" color="error">{selectedReport.errorMessage}</Typography>
                        </Box>
                      )}
                    </Box>
                  </CardContent>
                </Card>

                {/* 关联信息 */}
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>关联信息</Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                      <Box sx={{ flex: '1 1 calc(33% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">用户</Typography>
                        <Typography variant="body2">
                          {selectedReport.user?.username || '未知'} ({selectedReport.user?.email || 'N/A'})
                        </Typography>
                      </Box>
                      <Box sx={{ flex: '1 1 calc(33% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">患者</Typography>
                        <Typography variant="body2">
                          {selectedReport.patient?.name || '未知'}
                          {selectedReport.patient?.gender && ` (${selectedReport.patient.gender})`}
                        </Typography>
                      </Box>
                      <Box sx={{ flex: '1 1 calc(33% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">病理</Typography>
                        <Typography variant="body2">
                          {selectedReport.disease?.name || '未知病理'}
                        </Typography>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>

                {/* 报告内容 - 设为手风琴可折叠模式 */}
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="h6">报告摘要</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                      {selectedReport.content?.summary || '无摘要内容'}
                    </Typography>
                  </AccordionDetails>
                </Accordion>

                {/* 紧急指南 */}
                {selectedReport.content?.emergencyGuidance && (
                  <Accordion>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Typography variant="h6">
                        紧急指南
                        {selectedReport.content.emergencyGuidance.isEmergency && (
                          <Chip
                            size="small"
                            label="紧急情况"
                            color="error"
                            sx={{ ml: 1 }}
                          />
                        )}
                      </Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      {selectedReport.content.emergencyGuidance.isEmergency ? (
                        <Box>
                          <Typography variant="subtitle2" gutterBottom>立即行动：</Typography>
                          <ul>
                            {selectedReport.content.emergencyGuidance.immediateActions.map((action: string, idx: number) => (
                              <li key={idx}><Typography variant="body2">{action}</Typography></li>
                            ))}
                          </ul>

                          <Typography variant="subtitle2" gutterBottom>下一步：</Typography>
                          <ul>
                            {selectedReport.content.emergencyGuidance.nextSteps.map((step: string, idx: number) => (
                              <li key={idx}><Typography variant="body2">{step}</Typography></li>
                            ))}
                          </ul>
                        </Box>
                      ) : (
                        <Alert severity="info">该情况不需要紧急处理</Alert>
                      )}
                    </AccordionDetails>
                  </Accordion>
                )}

                {/* 完整内容(JSON格式) */}
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="h6">完整报告内容 (JSON)</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <TextField
                      fullWidth
                      multiline
                      rows={15}
                      variant="outlined"
                      value={formatJsonForDisplay(selectedReport.content)}
                      InputProps={{
                        readOnly: true,
                        sx: { fontFamily: 'monospace' }
                      }}
                    />
                  </AccordionDetails>
                </Accordion>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button
              color="error"
              onClick={() => {
                closeDetailDialog();
                if (selectedReport) {
                  openDeleteDialog(selectedReport);
                }
              }}
              startIcon={<DeleteIcon />}
            >
              删除报告
            </Button>
            <Button onClick={closeDetailDialog} color="primary">
              关闭
            </Button>
          </DialogActions>
        </Dialog>

        {/* 删除确认对话框 */}
        <Dialog
          open={deleteDialogOpen}
          onClose={closeDeleteDialog}
        >
          <DialogTitle>
            确认删除AI报告
          </DialogTitle>
          <DialogContent>
            <DialogContentText component="div">
              确定要删除此AI报告吗？删除后将无法恢复。
              {reportToDelete && (
                <Box component="div" sx={{ mt: 1 }}>
                  <Typography variant="subtitle2">报告信息：</Typography>
                  <Typography variant="body2">标题: {reportToDelete.title}</Typography>
                  <Typography variant="body2">用户: {reportToDelete.username}</Typography>
                  <Typography variant="body2">患者: {reportToDelete.patientName}</Typography>
                  <Typography variant="body2">创建时间: {formatDateTime(reportToDelete.createdAt)}</Typography>
                </Box>
              )}
            </DialogContentText>
            <TextField
              autoFocus
              margin="dense"
              label="删除原因（可选）"
              fullWidth
              value={deleteReason}
              onChange={(e) => setDeleteReason(e.target.value)}
              sx={{ mt: 2 }}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={closeDeleteDialog} disabled={deleteLoading}>
              取消
            </Button>
            <Button
              onClick={deleteReport}
              color="error"
              variant="contained"
              disabled={deleteLoading}
              startIcon={deleteLoading ? <CircularProgress size={18} /> : <DeleteIcon />}
            >
              {deleteLoading ? '删除中...' : '确认删除'}
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </ThemeProvider>
  );
};

export default AIReportManagementPage;