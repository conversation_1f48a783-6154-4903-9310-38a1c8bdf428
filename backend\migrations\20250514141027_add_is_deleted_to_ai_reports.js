/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.table('ai_reports', function(table) {
    // 添加is_deleted字段，默认值为false
    table.boolean('is_deleted').defaultTo(false);
    
    // 添加索引以提高查询性能
    table.index('is_deleted');
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.table('ai_reports', function(table) {
    // 回滚时删除字段
    table.dropColumn('is_deleted');
  });
};
