/**
 * 通知系统模型
 * 用于系统消息通知功能
 */
const { Model } = require('objection');
const { knex } = require('../src/db');

// 不需要再次设置 Model.knex，因为已经在 src/db.js 中设置了

class Notification extends Model {
  static get tableName() {
    return 'notifications';
  }

  static get idColumn() {
    return 'id';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['user_id', 'title', 'content', 'category'],
      properties: {
        id: { type: 'string', format: 'uuid' },
        user_id: { type: 'string', format: 'uuid' },
        title: { type: 'string', minLength: 1, maxLength: 100 },
        content: { type: 'string', minLength: 1, maxLength: 1000 },
        category: { 
          type: 'string', 
          enum: [
            'SYSTEM', 
            'MEDICAL', 
            'AUTHORIZATION', 
            'MESSAGE', 
            'REMINDER', 
            'AI_REPORT'
          ]
        },
        is_read: { type: 'boolean', default: false },
        metadata: { type: 'object' },
        created_at: { type: 'string', format: 'date-time' },
        updated_at: { type: 'string', format: 'date-time' },
        read_at: { type: ['string', 'null'], format: 'date-time' }
      }
    };
  }

  static get relationMappings() {
    const User = require('./User');

    return {
      user: {
        relation: Model.BelongsToOneRelation,
        modelClass: User,
        join: {
          from: 'notifications.user_id',
          to: 'users.id'
        }
      }
    };
  }

  /**
   * 将数据库格式转换为API响应格式
   * @returns {Object} API格式的通知对象
   */
  toApiFormat() {
    return {
      id: this.id,
      userId: this.user_id,
      title: this.title,
      content: this.content,
      category: this.category,
      isRead: this.is_read,
      metadata: this.metadata || {},
      createdAt: this.created_at,
      updatedAt: this.updated_at,
      readAt: this.read_at
    };
  }
}

module.exports = Notification; 