import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Chip,
  Tooltip,
  Button,
  Divider
} from '@mui/material';
import PersonIcon from '@mui/icons-material/Person';
import MedicalServicesIcon from '@mui/icons-material/MedicalServices';
import VerifiedUserIcon from '@mui/icons-material/VerifiedUser';
import ErrorIcon from '@mui/icons-material/Error';
import RestartAltIcon from '@mui/icons-material/RestartAlt';
import { useServiceUserContext } from '../../context/ServiceUserContext';

// 组件接口
interface ServiceContextBarProps {
  // 是否显示重置按钮
  showReset?: boolean;
  // 是否紧凑模式显示
  compact?: boolean;
  // 选择上下文按钮点击回调
  onSelectContext?: () => void;
}

/**
 * 服务上下文信息显示组件
 * 用于在服务用户相关页面显示当前操作的授权关系、患者和病理上下文
 */
const ServiceContextBar: React.FC<ServiceContextBarProps> = ({
  showReset = false,
  compact = false,
  onSelectContext
}) => {
  // 获取服务上下文
  const serviceContext = useServiceUserContext();
  
  // 检查是否有完整的上下文（授权ID、患者ID和病理ID）
  const hasPatient = !!serviceContext.patientId;
  const hasDisease = !!serviceContext.diseaseId;
  const hasAuthorization = !!serviceContext.authorizationId;
  const validContext = hasAuthorization && hasPatient;
  
  // 授权级别颜色映射
  const privacyLevelColors = {
    BASIC: {
      bg: '#ECEFF1', // 灰色系
      color: '#546E7A', 
      border: '#78909C'
    },
    STANDARD: {
      bg: '#E3F2FD', // 浅蓝色
      color: '#1565c0',
      border: '#90caf9'
    },
    FULL: {
      bg: '#E8F5E9', // 浅绿色
      color: '#2e7d32',
      border: '#a5d6a7'
    }
  };
  
  // 授权级别显示名称
  const privacyLevelNames = {
    BASIC: '基础授权',
    STANDARD: '标准授权',
    FULL: '完整授权'
  };
  
  // 处理重置上下文
  const handleResetContext = () => {
    if (window.confirm('确定要重置服务上下文吗？这将清除当前选择的授权、患者和病理。')) {
    serviceContext.clearContext();
    }
  };
  
  // 如果没有有效上下文，显示提示
  if (!validContext) {
    return (
      <Paper 
        elevation={0}
        variant="outlined"
        sx={{ 
          p: compact ? 1 : 2, 
          mb: 2, 
          bgcolor: 'warning.light',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <ErrorIcon sx={{ mr: 1, color: 'warning.dark' }} />
          <Typography variant={compact ? 'body2' : 'body1'}>
            未设置服务上下文，请先选择授权和患者
          </Typography>
        </Box>
        
        <Button 
          variant="contained" 
          size={compact ? 'small' : 'medium'}
          onClick={onSelectContext}
        >
          选择上下文
        </Button>
      </Paper>
    );
  }
  
  return (
    <Paper 
      elevation={0}
      variant="outlined"
      sx={{ 
        p: compact ? 1 : 2, 
        mb: 2, 
        display: 'flex',
        flexDirection: { xs: 'column', md: 'row' },
        justifyContent: 'space-between',
        alignItems: { xs: 'flex-start', md: 'center' },
        gap: 2
      }}
    >
      <Box sx={{ 
        display: 'flex', 
        flexDirection: { xs: 'column', sm: 'row' },
        alignItems: { xs: 'flex-start', sm: 'center' },
        gap: { xs: 1, sm: 2 }
      }}>
        {/* 授权关系 */}
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <VerifiedUserIcon sx={{ mr: 0.5, color: 'primary.main' }} />
          <Typography variant={compact ? 'body2' : 'body1'} component="span" fontWeight="medium">
            授权人:
          </Typography>
          <Typography 
            variant={compact ? 'body2' : 'body1'} 
            component="span" 
            sx={{ ml: 1 }}
          >
            {serviceContext.ownerUserId ? `ID: ${serviceContext.ownerUserId}` : '未知'}
          </Typography>
          {serviceContext.privacyLevel && (
            <Tooltip title={`授权级别: ${privacyLevelNames[serviceContext.privacyLevel] || serviceContext.privacyLevel}`}>
              <Chip 
                label={privacyLevelNames[serviceContext.privacyLevel] || serviceContext.privacyLevel} 
                size="small" 
                sx={{ 
                  ml: 1,
                  bgcolor: privacyLevelColors[serviceContext.privacyLevel]?.bg || '#f5f5f5',
                  color: privacyLevelColors[serviceContext.privacyLevel]?.color || '#424242',
                  border: `1px solid ${privacyLevelColors[serviceContext.privacyLevel]?.border || '#e0e0e0'}`,
                  fontWeight: 500,
                  fontSize: '0.7rem'
                }}
              />
            </Tooltip>
          )}
        </Box>
        
        {hasPatient && (
          <>
            <Divider orientation="vertical" flexItem sx={{ display: { xs: 'none', sm: 'block' } }} />
            <Divider sx={{ display: { xs: 'block', sm: 'none' }, width: '100%' }} />
            
            {/* 患者信息 */}
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <PersonIcon sx={{ mr: 0.5, color: 'info.main' }} />
              <Typography variant={compact ? 'body2' : 'body1'} component="span" fontWeight="medium">
                患者:
              </Typography>
              <Typography 
                variant={compact ? 'body2' : 'body1'} 
                component="span" 
                sx={{ ml: 1 }}
              >
                {serviceContext.patientName || '未知'}
              </Typography>
            </Box>
          </>
        )}
        
        {hasDisease && (
          <>
            <Divider orientation="vertical" flexItem sx={{ display: { xs: 'none', sm: 'block' } }} />
            <Divider sx={{ display: { xs: 'block', sm: 'none' }, width: '100%' }} />
            
            {/* 病理信息 */}
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <MedicalServicesIcon sx={{ mr: 0.5, color: 'error.main' }} />
              <Typography variant={compact ? 'body2' : 'body1'} component="span" fontWeight="medium">
                病理:
              </Typography>
              <Typography 
                variant={compact ? 'body2' : 'body1'} 
                component="span" 
                sx={{ ml: 1 }}
              >
                {serviceContext.diseaseName || '未知'}
              </Typography>
            </Box>
          </>
        )}
      </Box>
      
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        {/* 删除"更改选择"按钮 */}
        
        {showReset && (
          <Button 
            variant="contained" 
            color="error"
            size={compact ? 'small' : 'medium'}
            onClick={handleResetContext}
            startIcon={<RestartAltIcon />}
            sx={{ fontWeight: 'bold', boxShadow: 2, borderRadius: 2 }}
          >
            重置上下文
          </Button>
        )}
      </Box>
    </Paper>
  );
};

export default ServiceContextBar; 