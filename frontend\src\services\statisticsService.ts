import apiClient from './apiClient';
import { API_PATHS } from '../config/apiPaths';
import { logApiError } from '../utils/apiErrorMonitor';

/**
 * 用户统计数据接口
 */
export interface UserStatistics {
  patientHistory: {
    count: number;
    total: number;
  };
  diseaseCount: {
    count: number;
  };
  recordCount: {
    count: number;
  };
  aiAnalysis: {
    current: number;
    total: number;
  };
  userLimits?: {
    maxPatients?: number;
    maxPathologies?: number;
    maxAttachmentSize?: number;
    maxTotalStorage?: number;
    maxAiUsed?: number;
  };
}

/**
 * 获取用户统计数据
 * 包括病历数、病理数、记录数和AI分析次数
 */
export const getUserStatistics = async (): Promise<UserStatistics> => {
  try {
    // 获取用户限制和统计数据
    console.log('[statisticsService] 正在请求用户限制数据...');
    
    // 使用API_PATHS配置
    const userLimitsPath = API_PATHS.USER.LIMITS;
    console.log(`[statisticsService] 使用用户限制路径: ${userLimitsPath}`);
    
    // 添加时间戳避免缓存问题
    const timestamp = new Date().getTime();
    const queryParams = `?_t=${timestamp}`;
    
    // 使用apiClient发送请求
    const response = await apiClient.get(`${userLimitsPath}${queryParams}`);
    
    // 打印详细的API响应信息，帮助调试
    console.log('[statisticsService] API响应状态码:', response.status);
    console.log('[statisticsService] API原始响应数据:', JSON.stringify(response.data, null, 2));
    
    // 提取levelLimits数据
    const levelLimits = response.data.levelLimits || {};
    console.log('[statisticsService] 提取的levelLimits数据:', JSON.stringify(levelLimits, null, 2));
    
    // 提取用户限制数据
    let maxPatients = undefined;
    let maxPathologies = undefined;
    let maxAttachmentSize = undefined;
    let maxTotalStorage = undefined;
    let maxAiUsed = undefined;
    
    // 检查levelLimits是否存在且是否为对象
    if (levelLimits && typeof levelLimits === 'object') {
      // 直接从levelLimits对象中提取字段
      maxPatients = 'maxPatients' in levelLimits ? Number(levelLimits.maxPatients) : undefined;
      maxPathologies = 'maxPathologies' in levelLimits ? Number(levelLimits.maxPathologies) : undefined;
      maxAttachmentSize = 'maxAttachmentSize' in levelLimits ? Number(levelLimits.maxAttachmentSize) : undefined;
      maxTotalStorage = 'maxTotalStorage' in levelLimits ? Number(levelLimits.maxTotalStorage) : undefined;
      maxAiUsed = 'maxAiUsed' in levelLimits ? Number(levelLimits.maxAiUsed) : undefined;
      
      console.log('[statisticsService] 成功从levelLimits提取数据:', {
        maxPatients,
        maxPathologies,
        maxAttachmentSize,
        maxTotalStorage,
        maxAiUsed
      });
    } else {
      console.warn('[statisticsService] 从API响应中未找到有效的levelLimits数据');
    }
    
    // 组装并返回结果
    return {
      patientHistory: {
        count: response.data.currentPatientCount || 0,
        total: response.data.familyMemberLimit || 0
      },
      diseaseCount: {
        count: response.data.currentDiseaseCount || 0
      },
      recordCount: {
        count: response.data.recordCount || 0
      },
      aiAnalysis: {
        current: response.data.aiUsageCount || 0,
        total: response.data.activeDiseaseLimit || 30
      },
      userLimits: {
        maxPatients,
        maxPathologies,
        maxAttachmentSize,
        maxTotalStorage,
        maxAiUsed
      }
    };
  } catch (error) {
    console.error('[statisticsService] 获取用户统计数据失败:', error);
    // 记录API错误
    logApiError(error);
    // 返回0值以表示API调用失败
    return {
      patientHistory: { count: 0, total: 0 },
      diseaseCount: { count: 0 },
      recordCount: { count: 0 },
      aiAnalysis: { current: 0, total: 0 },
      userLimits: {}
    };
  }
};

/**
 * 获取医疗记录总数
 */
const getMedicalRecordsCount = async (): Promise<number> => {
  try {
    // 直接使用路径而不添加前缀（apiClient已配置前缀）
    const recordsCountPath = '/records/count';
    console.log(`[statisticsService] 请求记录数量，路径: ${recordsCountPath}`);
    
    const response = await apiClient.get(recordsCountPath);
    return response.data.count || 0;
  } catch (error) {
    console.error('[statisticsService] 获取记录数量失败:', error);
    // 记录API错误
    logApiError(error);
    return 0;
  }
};

// 定义导出的服务对象
const statisticsService = {
  getUserStatistics,
  getMedicalRecordsCount
};

// 导出服务对象
export default statisticsService; 