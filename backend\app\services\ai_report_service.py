from typing import Dict

class AIReporterService:
    def prepare_patient_data(self, patient_id: int) -> Dict:
        """准备患者数据，包括基本信息和所有记录"""
        try:
            # 获取患者信息
            patient = self.patient_service.get_patient(patient_id)
            if not patient:
                raise ValueError(f"未找到ID为{patient_id}的患者")

            # 获取患者记录
            records = self.patient_service.get_patient_records(patient_id)
            
            # 准备返回数据
            patient_data = {
                "id": patient.id,
                "name": patient.name,
                "age": patient.age,  # 保留年龄信息
                "gender": patient.gender,
                "records": records
            }
            
            # 记录数据准备情况
            logger.info(f"患者数据准备完成 - ID: {patient_id}")
            logger.debug(f"患者基本信息: {patient_data['name']}, {patient_data['age']}岁, {patient_data['gender']}")
            logger.debug(f"记录数量: {len(records)}条")
            
            # 记录每条记录的类型分布
            record_types = {}
            for record in records:
                record_type = record['record_type']
                record_types[record_type] = record_types.get(record_type, 0) + 1
            
            logger.debug(f"记录类型分布: {record_types}")
            
            return patient_data
            
        except Exception as e:
            logger.error(f"准备患者数据失败: {str(e)}")
            raise

    def create_report(self, user_id: int, disease_id: int, patient_id: int) -> Dict:
        """创建AI分析报告"""
        try:
            # 验证用户权限
            if not self.user_service.has_permission(user_id, "create_report"):
                raise PermissionError("用户没有创建报告的权限")

            # 准备患者数据
            patient_data = self.prepare_patient_data(patient_id)
            
            # 创建报告记录
            report = AIReport(
                user_id=user_id,
                disease_id=disease_id,
                patient_id=patient_id,
                status="PENDING"
            )
            self.db.add(report)
            self.db.commit()
            self.db.refresh(report)
            
            logger.info(f"创建报告成功 - ID: {report.id}")
            logger.debug(f"报告初始状态: {report.status}")
            
            return {
                "id": report.id,
                "status": report.status,
                "created_at": report.created_at.isoformat()
            }
            
        except Exception as e:
            logger.error(f"创建报告失败: {str(e)}")
            raise 