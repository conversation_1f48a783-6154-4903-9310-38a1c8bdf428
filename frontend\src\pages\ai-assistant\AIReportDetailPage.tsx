import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  CircularProgress,
  Divider,
  Chip,
  Alert,
  Card,
  CardHeader,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Rating,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  ThemeProvider,
  createTheme,
  useTheme,
  Paper
} from '@mui/material';
import {
  SmartToy as SmartToyIcon,
  FileDownload as DownloadIcon,
  Description as DescriptionIcon,
  ArrowForward as ArrowForwardIcon,
  ExpandMore as ExpandMoreIcon,
  Circle as CircleIcon,
  LocalHospital as HospitalIcon,
  MonetizationOn as MoneyIcon,
  Warning as WarningIcon,
  HealthAndSafety as HealthIcon,
  DirectionsRun as LifestyleIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  ArrowBack as ArrowBackIcon,
  Refresh as RefreshIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { format, parseISO } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { getAIReport, downloadAIReportPDF, getReportConfig, deleteAIReport, regenerateAIReport } from '../../services/ai-assistant/aiReportService';
import { AIReport, ReportVisibilityConfig } from '../../types/ai-assistant';
import { useAuthStore } from '../../store/authStore';
import apiClient from '../../services/apiClient';
import { usePatientDiseaseContext } from '../../context/PatientDiseaseContext';
import { getPatientDiseases } from '../../services/diseaseService';
import { SelectChangeEvent } from '@mui/material/Select';

/**
 * 辅医分析报告详情页面
 * 根据用户角色显示不同级别的报告内容
 */
const AIReportDetailPage: React.FC = () => {
  const { reportId } = useParams<{ reportId: string }>();
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const [report, setReport] = useState<AIReport | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [visibilityConfig, setVisibilityConfig] = useState<ReportVisibilityConfig | null>(null);
  const [patientData, setPatientData] = useState<any>(null);
  const [hasRecord, setHasRecord] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [regenerating, setRegenerating] = useState(false);
  const { selectedPatientId, selectedDiseaseId, setSelectedDisease } = usePatientDiseaseContext();
  const [diseases, setDiseases] = useState<any[]>([]);
  const [diseasesLoading, setDiseasesLoading] = useState(false);
  
  // 创建一个字体大小减小2号的主题
  const baseTheme = useTheme();
  const smallerFontTheme = createTheme({
    ...baseTheme,
    typography: {
      ...baseTheme.typography,
      h5: {
        ...baseTheme.typography.h5,
        fontSize: '1.1rem',  // 减小h5字体
      },
      h6: {
        ...baseTheme.typography.h6,
        fontSize: '0.95rem', // 减小h6字体
      },
      body1: {
        ...baseTheme.typography.body1,
        fontSize: '0.85rem', // 减小body1字体
      },
      body2: {
        ...baseTheme.typography.body2,
        fontSize: '0.75rem', // 减小body2字体
      },
      caption: {
        ...baseTheme.typography.caption,
        fontSize: '0.65rem', // 减小caption字体
      },
      button: {
        ...baseTheme.typography.button,
        fontSize: '0.75rem', // 减小按钮字体
      },
    },
    components: {
      ...baseTheme.components,
      MuiChip: {
        styleOverrides: {
          label: {
            fontSize: '0.65rem', // 减小Chip标签字体
          }
        }
      },
      MuiListItemText: {
        styleOverrides: {
          primary: {
            fontSize: '0.85rem', // 减小列表项主要文本字体
          },
          secondary: {
            fontSize: '0.75rem', // 减小列表项次要文本字体
          }
        }
      },
      MuiInputLabel: {
        styleOverrides: {
          root: {
            fontSize: '0.75rem', // 减小输入标签字体
          }
        }
      },
      MuiMenuItem: {
        styleOverrides: {
          root: {
            fontSize: '0.75rem', // 减小菜单项字体
          }
        }
      },
      MuiDialogTitle: {
        styleOverrides: {
          root: {
            fontSize: '1.0rem', // 减小对话框标题字体
          }
        }
      },
      MuiDialogContentText: {
        styleOverrides: {
          root: {
            fontSize: '0.75rem', // 减小对话框内容文本字体
          }
        }
      }
    }
  });

  // isFieldVisible 函数定义
  const isFieldVisible = (fieldName: string): boolean => {
    if (!visibilityConfig || !report) return false;

    // 即使报告未完成，也应显示如标题、状态、错误信息等基本字段
    if (report.status !== 'COMPLETED') {
      const alwaysVisibleFields = ['title', 'status', 'errorMessage', 'summary', 'dashboardData'];
      if (alwaysVisibleFields.includes(fieldName)) {
        return true;
      }
      // 对于非COMPLETED状态，可以根据需要决定其他字段是否可见，此处默认隐藏其他大部分字段
      // return false; 
    }

    const role = user?.role || 'USER';
    const fields = role === 'USER' 
      ? visibilityConfig.userVisibleFields 
      : visibilityConfig.serviceVisibleFields;
    return fields.includes(fieldName);
  };

  // 加载报告详情和配置
  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => {
    const fetchReportAndConfig = async () => {
      if (!reportId) return;
      
      setLoading(true);
      setError(null); // 重置错误状态
      try {
        const [reportData, configData] = await Promise.all([
          getAIReport(reportId),
          getReportConfig()
        ]);
        
        setVisibilityConfig(configData); // 设置配置

        if (reportData && reportData.content) {
        setReport(reportData);
          
        if (reportData.patientId) {
          try {
            const patientResponse = await apiClient.get(`/patients/${reportData.patientId}`);
            if (patientResponse.status === 200) {
                const fetchedPatientData = patientResponse.data;
                setPatientData(fetchedPatientData);
              
                if (reportData.content && !reportData.content.bmiRecommendations && fetchedPatientData.height && fetchedPatientData.weight) {
                  const enhancedReport = generateBMIRecommendations(reportData, fetchedPatientData);
                setReport(enhancedReport);
              }
            }
            } catch (patientError) {
              console.error('获取患者信息失败', patientError);
            }
          }
        } else {
          console.error('获取到的报告数据不完整:', reportData);
          setError('报告数据不完整，无法显示详情。');
          setReport(null); 
          }

      } catch (fetchError) {
        console.error('获取报告详情或配置失败', fetchError);
        setError('获取报告详情失败，请稍后重试');
        setReport(null); // 确保出错时报告为空
      } finally {
        setLoading(false);
      }
    };
    
    fetchReportAndConfig();
  }, [reportId, user]); // user 变动也可能影响可见字段，加入依赖

  // 获取当前患者的病理列表
  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => {
    const fetchDiseases = async () => {
      if (!selectedPatientId) {
        setDiseases([]);
        return;
      }
      setDiseasesLoading(true);
      try {
        const data = await getPatientDiseases(selectedPatientId);
        setDiseases(data);
      } catch (err) {
        setDiseases([]);
      } finally {
        setDiseasesLoading(false);
      }
    };
    fetchDiseases();
  }, [selectedPatientId]);

  // 根据BMI生成健康建议
  const generateBMIRecommendations = (report: AIReport, patient: any): AIReport => {
    // 复制报告对象
    const enhancedReport = { ...report };
    const content = { ...enhancedReport.content };
    
    // 计算BMI
    const height = parseFloat(patient.height);
    const weight = parseFloat(patient.weight);
    
    if (isNaN(height) || isNaN(weight) || height <= 0 || weight <= 0) {
      return report; // 数据无效，返回原始报告
    }
    
    const bmi = weight / Math.pow(height / 100, 2);
    
    // 根据BMI值确定状态和颜色
    let bmiStatus, bmiColor;
    let recommendations = [];
    
    if (bmi < 18.5) {
      bmiStatus = '偏瘦';
      bmiColor = '#FFA500'; // 橙色
      recommendations = [
        '增加优质蛋白质和健康脂肪的摄入，如鱼类、坚果和橄榄油',
        '适量增加热量摄入，可增加进餐频次',
        '进行力量训练以增加肌肉质量',
        '咨询医生了解是否需要营养补充剂'
      ];
    } else if (bmi < 24) {
      bmiStatus = '正常';
      bmiColor = '#008000'; // 绿色
      recommendations = [
        '保持目前的饮食习惯和体重',
        '坚持规律运动，每周至少150分钟中等强度活动',
        '定期监测体重，保持健康生活方式',
        '均衡摄入各类营养素，保持良好饮食结构'
      ];
    } else if (bmi < 28) {
      bmiStatus = '超重';
      bmiColor = '#FFA500'; // 橙色
      recommendations = [
        '控制每日热量摄入，增加蔬菜水果比例',
        '减少精制碳水化合物和饱和脂肪的摄入',
        '增加有氧运动，每周至少150-300分钟',
        '考虑制定减重计划，每周减重0.5-1公斤为宜'
      ];
    } else {
      bmiStatus = '肥胖';
      bmiColor = '#FF0000'; // 红色
      recommendations = [
        '在医生指导下制定减重计划',
        '严格控制热量摄入，增加高纤维食物',
        '每天进行至少30分钟中等强度有氧运动',
        '考虑咨询营养师获取个性化饮食建议',
        '定期监测血压、血糖和血脂等指标'
      ];
    }
    
    // 添加BMI推荐到报告内容
    content.bmiRecommendations = {
      bmiValue: parseFloat(bmi.toFixed(1)),
      bmiStatus: {
        status: bmiStatus,
        color: bmiColor
      },
      recommendations: recommendations
    };
    
    enhancedReport.content = content;
    return enhancedReport;
  };

  // 下载PDF报告
  const handleDownloadPDF = async () => {
    if (!reportId) return;
    
    try {
      setError(''); // 清除之前的错误信息
      await downloadAIReportPDF(reportId);
      // 不需要额外处理，downloadAIReportPDF已经直接触发了下载
    } catch (error) {
      console.error('下载PDF失败', error);
      setError('下载PDF失败，请稍后重试');
    }
  };

  // 查看关联记录
  const handleViewRecord = () => {
    if (!report) return;
    navigate(`/records/${report.recordId}`);
  };

  // 返回列表
  const handleBackToList = () => {
    navigate('/ai-assistant');
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    try {
      return format(parseISO(dateString), 'yyyy-MM-dd HH:mm', { locale: zhCN });
    } catch (error) {
      return dateString;
    }
  };

  // 渲染紧急处置指南
  const renderEmergencyGuidance = () => {
    if (!report || !report.content || !report.content.emergencyGuidance || !isFieldVisible('emergencyGuidance')) {
      return isFieldVisible('emergencyGuidance') ? <Typography sx={{ p: 2 }}>暂无紧急情况指导信息。</Typography> : null;
    }
    const { isEmergency, immediateActions, nextSteps } = report.content.emergencyGuidance;
    
    return (
      <Card sx={{ mb: 3, border: isEmergency ? '1px solid #f44336' : 'none' }}>
        <CardHeader 
          title="紧急处置指南" 
          titleTypographyProps={{ variant: 'h6' }}
          avatar={<WarningIcon color={isEmergency ? 'error' : 'disabled'} />}
        />
        <CardContent>
          {isEmergency ? (
            <>
              <Alert severity="error" sx={{ mb: 2 }}>
                系统检测到可能的紧急情况，请尽快就医
              </Alert>
              
              <Typography variant="subtitle1" gutterBottom>
                立即行动
              </Typography>
              <List dense>
                {immediateActions.map((action, index) => (
                  <ListItem key={index}>
                    <ListItemIcon sx={{ minWidth: 28 }}>
                      <CircleIcon sx={{ fontSize: 8 }} />
                    </ListItemIcon>
                    <ListItemText primary={action} />
                  </ListItem>
                ))}
              </List>
              
              <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
                后续步骤
              </Typography>
              <List dense>
                {nextSteps.map((step, index) => (
                  <ListItem key={index}>
                    <ListItemIcon sx={{ minWidth: 28 }}>
                      <CircleIcon sx={{ fontSize: 8 }} />
                    </ListItemIcon>
                    <ListItemText primary={step} />
                  </ListItem>
                ))}
              </List>
            </>
          ) : (
            <Typography>
              当前情况暂未检测到需要紧急处置的问题
            </Typography>
          )}
        </CardContent>
      </Card>
    );
  };

  // 渲染医院推荐
  const renderHospitalRecommendations = () => {
    if (!report || !report.content || !report.content.hospitalRecommendations || !isFieldVisible('hospitalRecommendations')) {
      return isFieldVisible('hospitalRecommendations') ? <Typography sx={{ p: 2 }}>暂无医院推荐信息。</Typography> : null;
    }
    const { targetRegion, hospitals } = report.content.hospitalRecommendations;
    
    const hospitalList = Array.isArray(hospitals) ? hospitals : [];
    
    return (
      <Card sx={{ mb: 3 }}>
        <CardHeader 
          title="医院与科室推荐" 
          titleTypographyProps={{ variant: 'h6' }}
          avatar={<HospitalIcon color="primary" />}
        />
        <CardContent>
          <Typography variant="body2" gutterBottom>
            位于{targetRegion || '全国范围'}，系统根据您的情况推荐以下医院
          </Typography>
          
          {hospitalList.map((hospital, index) => (
            <Box key={index} sx={{ mb: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid #eee' }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                <Typography variant="subtitle1">
                  {hospital.name} ({hospital.level})
                </Typography>
                <Chip 
                  label={`匹配度: ${hospital.matchScore}%`} 
                  color={hospital.matchScore > 70 ? 'success' : 'default'} 
                  size="small"
                />
              </Box>
              
              <Typography variant="body2" gutterBottom>
                推荐科室: {hospital.department}
              </Typography>
              
              <Typography variant="subtitle2" sx={{ mt: 1 }}>
                推荐理由:
              </Typography>
              <List dense>
                {(hospital.reasons || hospital.advantages || []).map((reason, idx) => (
                  <ListItem key={idx}>
                    <ListItemIcon sx={{ minWidth: 28 }}>
                      <CircleIcon sx={{ fontSize: 8 }} />
                    </ListItemIcon>
                    <ListItemText primary={reason} />
                  </ListItem>
                ))}
              </List>
              
              {hospital.contact && (
                <Typography variant="body2" sx={{ mt: 1 }}>
                  联系方式: {hospital.contact}
                </Typography>
              )}
              
              {hospital.contactInfo && (
                <Box sx={{ mt: 1 }}>
                  <Typography variant="subtitle2">
                    联系方式:
                  </Typography>
                  <List dense>
                    {hospital.contactInfo.website && (
                      <ListItem>
                        <ListItemIcon sx={{ minWidth: 28 }}>
                          <CircleIcon sx={{ fontSize: 8 }} />
                        </ListItemIcon>
                        <ListItemText primary={`官网: ${hospital.contactInfo.website}`} />
                      </ListItem>
                    )}
                    {hospital.contactInfo.phone && (
                      <ListItem>
                        <ListItemIcon sx={{ minWidth: 28 }}>
                          <CircleIcon sx={{ fontSize: 8 }} />
                        </ListItemIcon>
                        <ListItemText primary={`电话: ${hospital.contactInfo.phone}`} />
                      </ListItem>
                    )}
                    {hospital.contactInfo.wechatPublic && (
                      <ListItem>
                        <ListItemIcon sx={{ minWidth: 28 }}>
                          <CircleIcon sx={{ fontSize: 8 }} />
                        </ListItemIcon>
                        <ListItemText primary={`微信公众号: ${hospital.contactInfo.wechatPublic}`} />
                      </ListItem>
                    )}
                    {hospital.contactInfo.appointmentPlatform && (
                      <ListItem>
                        <ListItemIcon sx={{ minWidth: 28 }}>
                          <CircleIcon sx={{ fontSize: 8 }} />
                        </ListItemIcon>
                        <ListItemText primary={`挂号平台: ${hospital.contactInfo.appointmentPlatform}`} />
                      </ListItem>
                    )}
                  </List>
                </Box>
              )}
            </Box>
          ))}
        </CardContent>
      </Card>
    );
  };

  // 渲染预算评估
  const renderBudgetEstimation = (budgetEstimation: AIReport['content']['budgetEstimation']) => {
    if (!budgetEstimation || !isFieldVisible('budgetEstimation')) {
      return isFieldVisible('budgetEstimation') ? <Typography sx={{ p: 2 }}>暂无费用预估信息。</Typography> : null;
    }
    const columnsData = [
      { title: '经济型', data: budgetEstimation.low },
      { title: '标准型', data: budgetEstimation.medium },
      { title: '高端型', data: budgetEstimation.high }
    ];

    // 过滤掉可能不存在的层级（例如，如果后端没有提供 'medium'）
    const validColumns = columnsData.filter(col => col.data && typeof col.data.range === 'string');
    
    if (validColumns.length === 0) {
        return isFieldVisible('budgetEstimation') ? <Typography sx={{ p: 2 }}>费用预估数据不完整。</Typography> : null;
    }

    return (
      <Box sx={{ mt: 2 }}>
        <Typography variant="h6" gutterBottom>费用预估</Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
          {validColumns.map((col, idx) => (
            <Box key={idx} sx={{ flex: '1 1 300px', minWidth: 260, maxWidth: 400, bgcolor: 'background.paper', borderRadius: 2, boxShadow: 1, p: 2 }}>
              <Typography variant="subtitle1" gutterBottom>{col.title}</Typography>
              <Typography variant="body2" paragraph>
                {col.data.range}
              </Typography>
              <Typography variant="subtitle2" gutterBottom>优缺点:</Typography>
              <List dense>
                {(Array.isArray(col.data.tradeOffs) ? col.data.tradeOffs : []).map((tradeOff: string, index: number) => (
                  <ListItem key={index}>
                    <ListItemIcon sx={{ minWidth: 28 }}>
                      <CircleIcon sx={{ fontSize: 8 }} />
                    </ListItemIcon>
                    <ListItemText primary={tradeOff} />
                  </ListItem>
                ))}
              </List>
            </Box>
          ))}
        </Box>
      </Box>
    );
  };

  // 渲染治疗方案
  const renderTreatmentPlan = () => {
    if (!report || !report.content || !report.content.treatmentPlan || !isFieldVisible('treatmentPlan')) {
      return isFieldVisible('treatmentPlan') ? <Typography sx={{ p: 2 }}>暂无治疗方案信息。</Typography> : null;
    }
    const { options, followUp } = report.content.treatmentPlan;
    
    if (!Array.isArray(options) || options.length === 0) {
      return isFieldVisible('treatmentPlan') ? <Typography sx={{ p: 2 }}>暂无具体的治疗方案选项。</Typography> : null;
    }
    
    return (
      <Card sx={{ mb: 3 }}>
        <CardHeader 
          title="治疗方案建议" 
          titleTypographyProps={{ variant: 'h6' }}
          avatar={<HealthIcon color="primary" />}
        />
        <CardContent>
          {options.map((option, index) => (
            <Box key={index}>
              {renderTreatmentOption(option, index)}
            </Box>
          ))}
          
          {followUp && followUp.length > 0 && (
            <>
              <Typography variant="subtitle1" sx={{ mt: 2 }}>
                随访建议
              </Typography>
              <List dense>
                {followUp.map((item, idx) => (
                  <ListItem key={idx}>
                    <ListItemIcon sx={{ minWidth: 28 }}>
                      <CircleIcon sx={{ fontSize: 8 }} />
                    </ListItemIcon>
                    <ListItemText primary={item} />
                  </ListItem>
                ))}
              </List>
            </>
          )}
        </CardContent>
      </Card>
    );
  };

  // 渲染生活与心理建议
  const renderLifestyleAndMentalHealth = () => {
    if (!report || !report.content || !report.content.lifestyleAndMentalHealth || !isFieldVisible('lifestyleAndMentalHealth')) {
      return isFieldVisible('lifestyleAndMentalHealth') ? <Typography sx={{ p: 2 }}>暂无生活方式与心理健康建议。</Typography> : null;
    }
    const { lifestyle, mentalHealth } = report.content.lifestyleAndMentalHealth;
    
    const safeLifestyle = lifestyle || { diet: [], exercise: [], habits: [] }; // 提供默认空数组
    const safeMentalHealth = mentalHealth || { copingStrategies: [], resources: [] }; // 提供默认空数组
    
    return (
      <Card sx={{ mb: 3 }}>
        <CardHeader 
          title="生活与心理建议" 
          titleTypographyProps={{ variant: 'h6' }}
          avatar={<LifestyleIcon color="primary" />}
        />
        <CardContent>
          {/* BMI健康建议板块 */}
          {report.content.bmiRecommendations && (
            <Accordion defaultExpanded>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography sx={{ fontWeight: 'bold' }}>基于BMI的健康建议</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Box sx={{ mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                      当前BMI:
                    </Typography>
                    <Typography 
                      variant="body1" 
                      sx={{ 
                        color: report.content.bmiRecommendations.bmiStatus?.color || 'inherit',
                        fontWeight: 'bold'
                      }}
                    >
                      {report.content.bmiRecommendations.bmiValue?.toFixed(1) || '未知'} 
                      {report.content.bmiRecommendations.bmiStatus && 
                        ` (${report.content.bmiRecommendations.bmiStatus.status})`}
                    </Typography>
                  </Box>
                  
                  <Divider sx={{ my: 1 }} />
                  
                  <List dense>
                    {report.content.bmiRecommendations.recommendations?.map((item, idx) => (
                      <ListItem key={idx}>
                        <ListItemIcon sx={{ minWidth: 28 }}>
                          <CircleIcon sx={{ fontSize: 8 }} />
                        </ListItemIcon>
                        <ListItemText primary={item} />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              </AccordionDetails>
            </Accordion>
          )}
          
          <Accordion defaultExpanded>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography>饮食建议</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List dense>
                {safeLifestyle.diet?.length > 0 ? safeLifestyle.diet.map((item, idx) => (
                  <ListItem key={idx}>
                    <ListItemIcon sx={{ minWidth: 28 }}>
                      <CircleIcon sx={{ fontSize: 8 }} />
                    </ListItemIcon>
                    <ListItemText primary={item} />
                  </ListItem>
                )) : (
                  <ListItem>
                    <ListItemText primary="无饮食建议数据" />
                  </ListItem>
                )}
              </List>
            </AccordionDetails>
          </Accordion>
          
          <Accordion defaultExpanded>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography>运动建议</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List dense>
                {safeLifestyle.exercise?.length > 0 ? safeLifestyle.exercise.map((item, idx) => (
                  <ListItem key={idx}>
                    <ListItemIcon sx={{ minWidth: 28 }}>
                      <CircleIcon sx={{ fontSize: 8 }} />
                    </ListItemIcon>
                    <ListItemText primary={item} />
                  </ListItem>
                )) : (
                  <ListItem>
                    <ListItemText primary="无运动建议数据" />
                  </ListItem>
                )}
              </List>
            </AccordionDetails>
          </Accordion>
          
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography>习惯调整</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List dense>
                {safeLifestyle.habits?.length > 0 ? safeLifestyle.habits.map((item, idx) => (
                  <ListItem key={idx}>
                    <ListItemIcon sx={{ minWidth: 28 }}>
                      <CircleIcon sx={{ fontSize: 8 }} />
                    </ListItemIcon>
                    <ListItemText primary={item} />
                  </ListItem>
                )) : (
                  <ListItem>
                    <ListItemText primary="无习惯调整建议" />
                  </ListItem>
                )}
              </List>
            </AccordionDetails>
          </Accordion>
          
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography>心理健康</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="subtitle2" gutterBottom>
                应对策略
              </Typography>
              <List dense>
                {safeMentalHealth.copingStrategies?.length > 0 ? safeMentalHealth.copingStrategies.map((item, idx) => (
                  <ListItem key={idx}>
                    <ListItemIcon sx={{ minWidth: 28 }}>
                      <CircleIcon sx={{ fontSize: 8 }} />
                    </ListItemIcon>
                    <ListItemText primary={item} />
                  </ListItem>
                )) : (
                  <ListItem>
                    <ListItemText primary="无应对策略建议" />
                  </ListItem>
                )}
              </List>
              
              <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                资源推荐
              </Typography>
              <List dense>
                {safeMentalHealth.resources?.length > 0 ? safeMentalHealth.resources.map((item, idx) => (
                  <ListItem key={idx}>
                    <ListItemIcon sx={{ minWidth: 28 }}>
                      <CircleIcon sx={{ fontSize: 8 }} />
                    </ListItemIcon>
                    <ListItemText primary={item} />
                  </ListItem>
                )) : (
                  <ListItem>
                    <ListItemText primary="无资源推荐" />
                  </ListItem>
                )}
              </List>
            </AccordionDetails>
          </Accordion>
        </CardContent>
      </Card>
    );
  };

  // 渲染风险提示
  const renderRiskWarnings = () => {
    if (!report || !report.content || !isFieldVisible('riskWarnings')) {
      return isFieldVisible('riskWarnings') ? <Typography sx={{ p: 2 }}>暂无风险警告信息。</Typography> : null;
    }
    const riskWarnings = report.content.riskWarnings;

    if (!Array.isArray(riskWarnings) || riskWarnings.length === 0) {
        return isFieldVisible('riskWarnings') ? <Typography sx={{ p: 2 }}>暂无风险警告信息或数据格式错误。</Typography> : null;
    }
    
    return (
      <Alert severity="warning" sx={{ mb: 3 }}>
        <Typography variant="subtitle2" gutterBottom>
          风险提示
        </Typography>
        <List dense disablePadding>
          {riskWarnings.map((warning, idx) => (
            <ListItem key={idx} disablePadding>
              <ListItemIcon sx={{ minWidth: 28 }}>
                <CircleIcon sx={{ fontSize: 8 }} />
              </ListItemIcon>
              <ListItemText primary={warning} />
            </ListItem>
          ))}
        </List>
      </Alert>
    );
  };

  // 渲染治疗方案选项
  const renderTreatmentOption = (option: any, index: number) => (
    <Box key={index} sx={{ mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        {option.name || `方案${index + 1}`}
        {option.suitabilityScore !== undefined && (
          <Chip
            label={`适合度: ${option.suitabilityScore}%`}
            color={option.suitabilityScore > 70 ? 'success' : 'default'}
            size="small"
            sx={{ ml: 1 }}
          />
        )}
      </Typography>
      <Typography variant="body1" paragraph>
        {option.description}
      </Typography>

      {option.prognosisData && (
        <Box sx={{ mt: 1, mb: 2 }}>
          <Typography variant="subtitle2">预后评估:</Typography>
          <List dense>
            {option.prognosisData.survivalRate && (
              <ListItem>
                <ListItemIcon sx={{ minWidth: 28 }}>
                  <CircleIcon sx={{ fontSize: 8 }} />
                </ListItemIcon>
                <ListItemText primary={option.prognosisData.survivalRate} />
              </ListItem>
            )}
            {option.prognosisData.remissionRate && (
              <ListItem>
                <ListItemIcon sx={{ minWidth: 28 }}>
                  <CircleIcon sx={{ fontSize: 8 }} />
                </ListItemIcon>
                <ListItemText primary={option.prognosisData.remissionRate} />
              </ListItem>
            )}
            {option.prognosisData.recurrenceRisk && (
              <ListItem>
                <ListItemIcon sx={{ minWidth: 28 }}>
                  <CircleIcon sx={{ fontSize: 8 }} />
                </ListItemIcon>
                <ListItemText primary={option.prognosisData.recurrenceRisk} />
              </ListItem>
            )}
          </List>
        </Box>
      )}

      {option.budgetEstimation && (
        <Box sx={{ mt: 1, mb: 2 }}>
          <Typography variant="subtitle2">费用预估:</Typography>
          <Typography variant="body2">
            {option.budgetEstimation.minCost && option.budgetEstimation.maxCost ?
              `${option.budgetEstimation.minCost} - ${option.budgetEstimation.maxCost} ${option.budgetEstimation.currency || '元'}` :
              '费用信息暂无'
          }
        </Typography>
        {option.budgetEstimation.insuranceCoverage && (
          <Typography variant="body2" color="text.secondary">
            {option.budgetEstimation.insuranceCoverage}
          </Typography>
        )}
      </Box>
    )}
  </Box>
);

const handleDelete = async () => {
  if (!reportId) return;
  try {
    await deleteAIReport(reportId);
    navigate('/ai-reports');
  } catch (err) {
    setError('删除报告失败，请稍后重试');
    console.error('删除报告失败:', err);
  }
};

const handleRegenerate = async () => {
  if (!reportId || !report) return;
  try {
    setRegenerating(true);
    const response = await regenerateAIReport({
      diseaseId: report.diseaseId,
      patientId: report.patientId
    });
    
    if (response.status === 'SUCCESS' && response.aiReport) {
      setReport(response.aiReport);
      setError(null);
    } else {
      setError('重新生成报告失败，请稍后重试');
    }
  } catch (err) {
    setError('重新生成报告失败，请稍后重试');
    console.error('重新生成报告失败:', err);
  } finally {
    setRegenerating(false);
  }
};

// 处理病理选择
const handlePathologySelect = (event: SelectChangeEvent<string>) => {
  const newDiseaseId = event.target.value as string;
  if (newDiseaseId && newDiseaseId !== selectedDiseaseId) {
    setSelectedDisease(newDiseaseId);
    // 跳转到AI分析列表页
    navigate('/ai-assistant');
  }
};

if (loading) {
  return (
    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '70vh' }}>
      <CircularProgress />
    </Box>
  );
}

if (error || !report) {
  return (
    <Box sx={{ p: 3 }}>
      <Alert severity="error" sx={{ mb: 3 }}>
        {error || '加载报告失败'}
      </Alert>
      <Button variant="outlined" onClick={handleBackToList}>
        返回列表
      </Button>
    </Box>
  );
}

return (
  <ThemeProvider theme={smallerFontTheme}>
    <Box sx={{ p: 3 }}>
      {/* 病理选择下拉框 */}
      {selectedPatientId && (
        <Box sx={{ mb: 3, maxWidth: 360 }}>
          <FormControl fullWidth size="small" disabled={diseasesLoading}>
            <InputLabel id="disease-select-label">切换病理</InputLabel>
            <Select
              labelId="disease-select-label"
              value={selectedDiseaseId || ''}
              label="切换病理"
              onChange={handlePathologySelect}
            >
              {diseases.map((disease) => (
                <MenuItem key={disease.id} value={disease.id}>
                  {disease.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      )}
      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box>
          <Typography variant="h5" gutterBottom>
            {report.title}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            生成时间: {formatDate(report.createdAt)}
          </Typography>
        </Box>
        <Box>
          <Button
            variant="outlined"
            startIcon={<DescriptionIcon />}
            onClick={handleViewRecord}
            sx={{ mr: 1 }}
          >
            查看关联记录
          </Button>
          <Button
            variant="contained"
            startIcon={<DownloadIcon />}
            onClick={handleDownloadPDF}
          >
            下载PDF
          </Button>
        </Box>
      </Box>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          病情综述
        </Typography>
        <Typography variant="body1" paragraph>
          {report.content.summary}
        </Typography>
        
        {report.content.dashboardData && (
          <Box sx={{ mt: 2 }}>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
              <Box sx={{ flexBasis: { xs: '100%', sm: 'calc(33.3% - 16px)' } }}>
                <Typography variant="body2" color="text.secondary">
                  当前状态
                </Typography>
                <Typography variant="body1">
                  {report.content.dashboardData.status}
                </Typography>
              </Box>
              <Box sx={{ flexBasis: { xs: '100%', sm: 'calc(33.3% - 16px)' } }}>
                <Typography variant="body2" color="text.secondary">
                  发展趋势
                </Typography>
                <Chip 
                  label={
                    report.content.dashboardData.trend === 'improving' ? '改善中' :
                    report.content.dashboardData.trend === 'stable' ? '稳定' : '恶化中'
                  }
                  color={
                    report.content.dashboardData.trend === 'improving' ? 'success' :
                    report.content.dashboardData.trend === 'stable' ? 'info' : 'error'
                  }
                  size="small"
                />
              </Box>
              <Box sx={{ flexBasis: { xs: '100%', sm: 'calc(33.3% - 16px)' } }}>
                <Typography variant="body2" color="text.secondary">
                  风险等级
                </Typography>
                <Chip 
                  label={
                    report.content.dashboardData.riskLevel === 'low' ? '低风险' :
                    report.content.dashboardData.riskLevel === 'medium' ? '中风险' : '高风险'
                  }
                  color={
                    report.content.dashboardData.riskLevel === 'low' ? 'success' :
                    report.content.dashboardData.riskLevel === 'medium' ? 'warning' : 'error'
                  }
                  size="small"
                />
              </Box>
            </Box>
          </Box>
        )}
      </Paper>

      {/* 紧急处置指南 */}
      {renderEmergencyGuidance()}
      
      {/* 医院与科室推荐 */}
      {renderHospitalRecommendations()}
      
      {/* 治疗方案建议 */}
      {renderTreatmentPlan()}
      
      {/* 预算费用评估 */}
      {renderBudgetEstimation(report.content.budgetEstimation)}
      
      {/* 生活与心理建议 */}
      {renderLifestyleAndMentalHealth()}
      
      {/* 风险提示 */}
      {renderRiskWarnings()}
      
      {/* 免责声明 */}
      <Box sx={{ mt: 4, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
        <Typography variant="caption" color="text.secondary">
          免责声明: {report.content.disclaimer || '本报告由AI生成，仅供参考，不构成医疗诊断或治疗建议。请咨询专业医生获取正式医疗意见。'}
        </Typography>
      </Box>
      
      <Box sx={{ mt: 3 }}>
        <Button 
          variant="outlined" 
          startIcon={<ArrowForwardIcon sx={{ transform: 'rotate(180deg)' }} />}
          onClick={handleBackToList}
        >
          返回列表
        </Button>
      </Box>

      <Box sx={{ mt: 3 }}>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={handleRegenerate}
          disabled={regenerating}
          sx={{ mr: 1 }}
        >
          {regenerating ? '重新生成中...' : '重新生成'}
        </Button>
        <Button
          variant="outlined"
          color="error"
          startIcon={<DeleteIcon />}
          onClick={() => setDeleteDialogOpen(true)}
        >
          删除
        </Button>
      </Box>

      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>确认删除</DialogTitle>
        <DialogContent>
          <DialogContentText>
            确定要删除这份报告吗？此操作不可撤销。
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>取消</Button>
          <Button onClick={handleDelete} color="error" autoFocus>
            删除
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  </ThemeProvider>
);
};

export default AIReportDetailPage; 