/**
 * 授权关系表创建
 * 用于存储用户之间的授权关系
 * 注意：此表已被user_authorizations替代，保留此迁移文件仅为了兼容性
 */
exports.up = function(knex) {
  return knex.schema.hasTable('user_authorizations').then(userAuthExists => {
    if (userAuthExists) {
      // 如果user_authorizations表已存在，跳过创建authorizations表
      console.log('user_authorizations表已存在，跳过创建authorizations表');
      return Promise.resolve();
    }
    
    return knex.schema.hasTable('authorizations').then(authExists => {
      if (authExists) {
        console.log('authorizations表已存在，跳过创建');
        return Promise.resolve();
      }
      
      console.log('创建历史遗留的authorizations表');
      return knex.schema.createTable('authorizations', function(table) {
        table.uuid('id').primary();
        table.uuid('grantor_id').notNullable().references('id').inTable('users');
        table.uuid('grantee_id').notNullable().references('id').inTable('users');
        table.string('access_type').notNullable(); // READ, WRITE, FULL
        table.string('scope').notNullable(); // ALL, PATIENT, DISEASE, RECORD
        table.uuid('target_id').nullable(); // 授权目标ID，取决于scope
        table.string('status').notNullable().defaultTo('PENDING'); // PENDING, ACTIVE, EXPIRED, REVOKED
        table.timestamp('created_at', { useTz: true }).notNullable();
        table.timestamp('updated_at', { useTz: true }).nullable();
        table.timestamp('expires_at', { useTz: true }).nullable();
        
        // 复合索引
        table.index(['grantor_id', 'grantee_id']);
        table.index(['grantee_id', 'status']);
        table.index(['scope', 'target_id']);
      });
    });
  });
};

/**
 * 删除授权关系表
 */
exports.down = function(knex) {
  return knex.schema.hasTable('authorizations').then(exists => {
    if (exists) {
      return knex.schema.dropTable('authorizations');
    }
    return Promise.resolve();
  });
}; 