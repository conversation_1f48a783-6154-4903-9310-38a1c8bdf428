<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>HKB 登录修复测试</h1>
    
    <div class="test-container">
        <h2>环境检测</h2>
        <div id="env-results"></div>
        <button onclick="checkEnvironment()">检测环境</button>
    </div>

    <div class="test-container">
        <h2>API 连接测试</h2>
        <div id="api-results"></div>
        <button onclick="testApiConnection()">测试 API 连接</button>
    </div>

    <div class="test-container">
        <h2>HTTPS 配置测试</h2>
        <div id="https-results"></div>
        <button onclick="testHttpsConfig()">测试 HTTPS 配置</button>
    </div>

    <div class="test-container">
        <h2>登录功能测试</h2>
        <div id="login-results"></div>
        <input type="text" id="username" placeholder="用户名" style="margin: 5px; padding: 8px;">
        <input type="password" id="password" placeholder="密码" style="margin: 5px; padding: 8px;">
        <button onclick="testLogin()">测试登录</button>
    </div>

    <div class="test-container">
        <h2>测试日志</h2>
        <div id="test-log" class="log"></div>
        <button onclick="clearLog()">清除日志</button>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type}] ${message}`);
        }

        function showResult(containerId, message, type) {
            const container = document.getElementById(containerId);
            container.innerHTML = `<div class="test-result ${type}">${message}</div>`;
        }

        function clearLog() {
            document.getElementById('test-log').textContent = '';
        }

        function checkEnvironment() {
            log('开始环境检测...');
            
            const protocol = window.location.protocol;
            const hostname = window.location.hostname;
            const port = window.location.port;
            
            let results = [];
            
            // 检查协议
            if (protocol === 'https:') {
                results.push('✅ 使用 HTTPS 协议');
                log('协议检查通过: HTTPS');
            } else {
                results.push('❌ 未使用 HTTPS 协议');
                log('协议检查失败: 使用的是 ' + protocol, 'warning');
            }
            
            // 检查域名
            if (hostname === 'hkb.life') {
                results.push('✅ 正确的生产域名');
                log('域名检查通过: ' + hostname);
            } else {
                results.push('⚠️ 非生产域名: ' + hostname);
                log('域名检查: ' + hostname, 'warning');
            }
            
            // 检查端口
            if (!port || port === '443') {
                results.push('✅ 正确的 HTTPS 端口');
                log('端口检查通过');
            } else {
                results.push('⚠️ 非标准端口: ' + port);
                log('端口检查: ' + port, 'warning');
            }
            
            showResult('env-results', results.join('<br>'), 'info');
        }

        async function testApiConnection() {
            log('开始 API 连接测试...');
            
            try {
                // 测试健康检查端点
                const healthResponse = await fetch('/health');
                if (healthResponse.ok) {
                    showResult('api-results', '✅ API 健康检查通过', 'success');
                    log('API 健康检查成功');
                } else {
                    throw new Error(`健康检查失败: ${healthResponse.status}`);
                }
            } catch (error) {
                showResult('api-results', `❌ API 连接失败: ${error.message}`, 'error');
                log('API 连接测试失败: ' + error.message, 'error');
            }
        }

        async function testHttpsConfig() {
            log('开始 HTTPS 配置测试...');
            
            try {
                // 测试 manifest.json
                const manifestResponse = await fetch('/manifest.json');
                if (manifestResponse.ok) {
                    const manifest = await manifestResponse.json();
                    log('Manifest 文件加载成功: ' + manifest.name);
                    showResult('https-results', '✅ Manifest 文件加载成功<br>✅ HTTPS 配置正常', 'success');
                } else {
                    throw new Error(`Manifest 加载失败: ${manifestResponse.status}`);
                }
            } catch (error) {
                showResult('https-results', `❌ HTTPS 配置测试失败: ${error.message}`, 'error');
                log('HTTPS 配置测试失败: ' + error.message, 'error');
            }
        }

        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                showResult('login-results', '❌ 请输入用户名和密码', 'warning');
                return;
            }
            
            log('开始登录测试...');
            
            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                log(`登录请求状态: ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('login-results', '✅ 登录请求成功发送', 'success');
                    log('登录响应: ' + JSON.stringify(data, null, 2));
                } else {
                    const errorData = await response.text();
                    showResult('login-results', `❌ 登录失败: ${response.status} ${response.statusText}`, 'error');
                    log('登录失败响应: ' + errorData, 'error');
                }
            } catch (error) {
                showResult('login-results', `❌ 登录请求失败: ${error.message}`, 'error');
                log('登录请求异常: ' + error.message, 'error');
            }
        }

        // 页面加载时自动检测环境
        window.onload = function() {
            log('页面加载完成，开始自动检测...');
            checkEnvironment();
        };
    </script>
</body>
</html>
