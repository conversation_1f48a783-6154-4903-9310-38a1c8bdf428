import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Box, 
  Typography, 
  <PERSON>ton, 
  Card,
  CardContent,
  CardActions,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Alert,
  CircularProgress,
} from '@mui/material';
import CheckIcon from '@mui/icons-material/Check';
import StarIcon from '@mui/icons-material/Star';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';

// 订阅信息页面组件
const SubscriptionPage: React.FC = () => {
  const navigate = useNavigate();
  
  // 当前订阅方案（模拟数据）
  const currentPlan = 'PERSONAL';
  
  // 状态管理
  const [upgradeSuccess, setUpgradeSuccess] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  
  // 处理升级订阅
  const handleUpgrade = (plan: string) => {
    if (plan === currentPlan) {
      return;
    }
    
    setSelectedPlan(plan);
    setIsSubmitting(true);
    
    // 模拟API请求
    setTimeout(() => {
      setUpgradeSuccess(`成功升级到${getPlanName(plan)}方案`);
      setIsSubmitting(false);
      
      // 3秒后自动返回个人资料页
      setTimeout(() => {
        navigate('/profile');
      }, 3000);
    }, 1500);
  };
  
  // 获取方案名称
  const getPlanName = (plan: string): string => {
    switch (plan) {
      case 'PERSONAL': return '个人版';
      case 'FAMILY': return '家庭版';
      case 'PROFESSIONAL': return '专业版';
      default: return '未知';
    }
  };
  
  // 方案详情
  const plans = [
    {
      id: 'PERSONAL',
      name: '个人版',
      price: '免费',
      features: [
        '最多1个患者记录',
        '最多3个病理学报告',
        '每个附件最大2MB',
        '每月3次AI使用'
      ],
      recommended: false
    },
    {
      id: 'FAMILY',
      name: '家庭版',
      price: '¥9.9/月',
      features: [
        '最多4个患者记录',
        '最多5个病理学报告/患者',
        '每个附件最大5MB',
        '最多3个家庭成员',
        '每月20次AI使用',
        '优先客户支持'
      ],
      recommended: false
    },
    {
      id: 'PROFESSIONAL',
      name: '专业版',
      price: '¥19.9/月',
      features: [
        '最多8个患者记录',
        '最多5个病理学报告/患者',
        '每个附件最大10MB',
        '最多7个家庭成员',
        '每月50次AI使用',
        '专属客户支持'
      ],
      recommended: false
    }
  ];
  
  return (
    <Box sx={{ 
      m: 0, // 移除外边距，使用Layout提供的统一10px间距
      width: '100%',
      px: '10px' // 添加左右内边距各10px
    }}>
      {/* 页面标题与操作按钮区域 */}
      <Box sx={{ display: 'flex', alignItems: 'flex-end', justifyContent: 'space-between', mb: 0 }}>
        <Typography 
          variant="h5" 
          component="h1" 
          sx={{ 
            fontWeight: 500,
            fontSize: { xs: '1.1rem', sm: '1.3rem' },
            mb: 0
          }}
        >
          订阅信息
        </Typography>
      </Box>
      <Divider sx={{ mb: { xs: 2, md: 3 } }} />
      
      {/* 提示消息 */}
      {upgradeSuccess && (
        <Alert severity="success" sx={{ mb: { xs: 1.5, md: 3 }, '& .MuiAlert-message': { fontSize: '0.75rem' } }}>
          {upgradeSuccess}
        </Alert>
      )}
      
      {/* 当前订阅信息 */}
      <Box 
        sx={{ 
          mb: { xs: 2, md: 3 },
          p: 0
        }}
      >
        <Card 
          elevation={0} 
          sx={{ 
            border: '1px solid #e0e0e0',
            borderRadius: 2,
            mb: 3
          }}
        >
          <CardContent sx={{ p: 2 }}>
            <Typography variant="h6" sx={{ fontSize: '0.9rem', fontWeight: 500, mb: 1 }}>
              当前方案: <Chip 
                label={`${getPlanName(currentPlan)}方案`} 
                color="primary" 
                size="small" 
                sx={{ ml: 1, fontSize: '0.7rem' }}
              />
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
              选择适合您需求的订阅方案，随时可以升级或降级。所有的方案都包括基础的健康记录管理功能。
            </Typography>
          </CardContent>
        </Card>
      </Box>
      
      {/* 订阅方案列表 */}
      <Box 
        sx={{ 
          display: 'grid',
          gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)', md: 'repeat(3, 1fr)' },
          gap: { xs: 2, md: 3 },
          mb: 3
        }}
      >
        {plans.map((plan) => (
          <Box key={plan.id}>
            <Card 
              elevation={0}
              sx={{ 
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                position: 'relative',
                border: plan.id === currentPlan ? '2px solid #2196f3' : '1px solid #e0e0e0',
                borderRadius: 2
              }}
            >
              {plan.recommended && (
                <Box 
                  sx={{ 
                    position: 'absolute', 
                    top: -12, 
                    left: 0, 
                    right: 0, 
                    textAlign: 'center'
                  }}
                >
                  <Chip 
                    label="推荐" 
                    color="secondary" 
                    size="small" 
                    icon={<StarIcon fontSize="small" />}
                    sx={{ fontSize: '0.7rem' }}
                  />
                </Box>
              )}
              
              <CardContent sx={{ flexGrow: 1, p: 2 }}>
                <Typography variant="h5" component="h2" sx={{ fontSize: '0.9rem', fontWeight: 500, mb: 1 }}>
                  {plan.name}
                </Typography>
                
                <Typography variant="h4" color="primary" sx={{ fontSize: '1.1rem', fontWeight: 500, mb: 1 }}>
                  {plan.price}
                </Typography>
                
                <Divider sx={{ my: 1.5 }} />
                
                <List dense disablePadding>
                  {plan.features.map((feature, index) => (
                    <ListItem key={index} disablePadding sx={{ py: 0.5 }}>
                      <ListItemIcon sx={{ minWidth: 35 }}>
                        <CheckIcon color="primary" fontSize="small" />
                      </ListItemIcon>
                      <ListItemText 
                        primary={feature} 
                        primaryTypographyProps={{ 
                          fontSize: '0.75rem'
                        }}
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
              
              <CardActions sx={{ p: 2, pt: 0 }}>
                <Button 
                  fullWidth 
                  variant={plan.id === currentPlan ? "outlined" : "contained"}
                  color="primary"
                  disabled={plan.id === currentPlan || isSubmitting}
                  onClick={() => handleUpgrade(plan.id)}
                  size="small"
                  sx={{ fontSize: '0.75rem' }}
                >
                  {isSubmitting && selectedPlan === plan.id ? (
                    <>
                      <CircularProgress size={20} color="inherit" sx={{ mr: 1 }} />
                      处理中...
                    </>
                  ) : plan.id === currentPlan ? '当前方案' : '升级'}
                </Button>
              </CardActions>
            </Card>
          </Box>
        ))}
      </Box>
      
      {/* 返回按钮 */}
      <Box sx={{ 
        mt: { xs: 2, md: 3 }, 
        display: 'flex', 
        justifyContent: 'flex-start' 
      }}>
        <Button 
          variant="outlined" 
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/profile')}
          disabled={isSubmitting}
          sx={{ fontSize: '0.75rem', width: { xs: '100%', sm: 'auto' } }}
        >
          返回个人资料
        </Button>
      </Box>
    </Box>
  );
};

export default SubscriptionPage; 