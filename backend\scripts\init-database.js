/**
 * 数据库初始化脚本
 * 用于在生产环境中创建和初始化数据库
 */
const knex = require('knex');
const knexfile = require('../knexfile');
const { Model } = require('objection');

// 获取环境配置
const env = process.env.NODE_ENV || 'development';
const config = knexfile[env];

// 创建数据库连接
const db = knex(config);

// 设置 Objection.js
Model.knex(db);

// 定义迁移顺序
const MIGRATION_ORDER = [
  // 用户和权限相关
  '2025042601_create_users.js',
  '2025042602_create_user_level_limits.js',
  '20250504041757_add_created_at_to_users.js',
  'update_existing_users_created_at.js',
  '20250501061759_add_max_ai_used_to_user_level_limits.js',
  
  // 订阅相关
  '2025042603_create_subscriptions.js',
  
  // 患者相关
  '2025042604_create_patients.js',
  '20250426120714_add_is_primary_to_patients.js',
  '20250426104116_add_patients_index.js',
  '2025042707_add_patients_index.js',
  '20250426151848_add_email_address_to_patients.js',
  '20250510004_add_height_weight_bmi_to_patients.js',
  
  // 疾病相关
  '20250501001_create_diseases.js',
  '20250501002_update_diseases_table.js',
  
  // 记录相关
  '20250430001_add_records_table.js',
  '20250430002_create_attachments_table.js',
  '20250514140611_add_is_deleted_to_records.js',
  '20250514141857_add_reference_id_to_records.js',
  
  // 标签相关
  '20240601_create_tags.js',
  
  // 患者日志相关
  '20250426161035_create_patient_logs.js',
  '20250501003_drop_patient_logs.js',
  
  // 管理员审计日志相关
  'create_admin_audit_logs_table.js',
  
  // AI报告相关
  '20250510001_create_ai_reports.js',
  '20250510002_create_ai_report_configs.js',
  '20250510003_create_ai_report_quotas.js',
  '20250510004_update_ai_reports.js',
  '20250512092101_add_created_by_updated_by_to_diseases_and_ai_reports.js',
  '20250514141027_add_is_deleted_to_ai_reports.js',
  
  // 通知相关
  '20250511_create_notifications.js',
  
  // 授权相关 - 新顺序
  '20250601001_create_user_authorizations.js',  // 先创建user_authorizations表
  '20250507043021_add_switch_states_to_authorizations.js',  // 添加开关状态列
  '20250507044236_update_status_check_constraint.js',
  '20250507044637_add_paused_state_to_check.js',
  '20250507045000_update_pending_to_paused.js',
  '20250507062409_remove_paused_state.js',
  '20250601004_fix_authorizations_table.js',
  'create_authorizations_table.js', // 已修改为检查user_authorizations存在则跳过创建
  '20250602001_drop_authorizations_if_exists.js', // 新增：删除不需要的authorizations表
  
  // 服务相关
  '20250601002_create_service_records.js',
  '20250601003_create_service_reports.js',
  
  // 列名规范化
  '20240720_normalize_column_names.js',
  
  // 患者记录删除
  '2025042706_drop_patient_records.js',
  
  // 初始化数据
  '2025042701_init_database.js'
];

async function initDatabase() {
  try {
    console.log('开始初始化数据库...');
    
    // 检查迁移表是否存在
    const hasMigrationTable = await db.schema.hasTable('knex_migrations');
    if (!hasMigrationTable) {
      console.log('创建迁移表...');
      await db.schema.createTable('knex_migrations', table => {
        table.increments('id').primary();
        table.string('name');
        table.integer('batch');
        table.timestamp('migration_time');
      });
    }

    // 获取已执行的迁移
    const executedMigrations = await db('knex_migrations').select('name');
    const executedNames = executedMigrations.map(m => m.name);

    // 按顺序执行迁移
    console.log('执行数据库迁移...');
    for (const migrationName of MIGRATION_ORDER) {
      if (!executedNames.includes(migrationName)) {
        console.log(`执行迁移: ${migrationName}`);
        try {
          await db.migrate.up({
            name: migrationName,
            directory: config.migrations.directory
          });
          console.log(`迁移 ${migrationName} 执行成功`);
        } catch (error) {
          console.error(`迁移 ${migrationName} 执行失败:`, error);
          throw error;
        }
      } else {
        console.log(`迁移 ${migrationName} 已执行，跳过`);
      }
    }
    console.log('数据库迁移完成！');

    // 验证表结构
    console.log('\n验证数据库表结构...');
    const tables = await db.raw(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    `);
    
    console.log('\n已创建的表:');
    tables.rows.forEach(table => {
      console.log(`- ${table.table_name}`);
    });

    // 验证用户等级限制数据
    console.log('\n验证用户等级限制数据...');
    const levelLimits = await db('user_level_limits').select('*');
    console.log('用户等级限制:');
    levelLimits.forEach(limit => {
      console.log(`- ${limit.level_type}:`);
      console.log(`  最大患者数: ${limit.max_patients}`);
      console.log(`  最大病理数: ${limit.max_pathologies}`);
      console.log(`  最大附件大小: ${limit.max_attachment_size}MB`);
      console.log(`  最大存储空间: ${limit.max_total_storage}MB`);
    });

    // 验证管理员账号
    console.log('\n验证管理员账号...');
    const admin = await db('users')
      .where('username', 'Adr')
      .where('role', 'ADMIN')
      .first();
    
    if (admin) {
      console.log('管理员账号创建成功:');
      console.log(`- 用户名: ${admin.username}`);
      console.log(`- 邮箱: ${admin.email}`);
      console.log(`- 角色: ${admin.role}`);
      console.log(`- 等级: ${admin.level}`);
      console.log(`- 状态: ${admin.is_active ? '激活' : '禁用'}`);
    } else {
      console.error('警告: 未找到管理员账号！');
    }

    console.log('\n数据库初始化完成！');
  } catch (error) {
    console.error('数据库初始化失败:', error);
    process.exit(1);
  } finally {
    // 关闭数据库连接
    await db.destroy();
  }
}

// 执行初始化
initDatabase(); 