/**
 * 配置文件更新脚本
 * 确保应用配置与旧版本AI模块兼容
 */
const fs = require('fs');
const path = require('path');

console.log('===== 更新应用配置文件 =====');

// 检查并更新环境变量配置
function updateEnvConfig() {
  console.log('正在检查环境变量配置...');
  
  const envPath = path.join(__dirname, '..', '.env');
  
  // 检查.env文件是否存在
  if (!fs.existsSync(envPath)) {
    console.log('未找到.env文件，将创建新文件');
    
    // 创建基本的.env文件
    const defaultEnv = `# AI模块配置
LLM_PROVIDER=volcengine
VOLCENGINE_MODEL=deepseek-r1-250120
VOLCENGINE_API_KEY=your_api_key_here
DEEPSEEK_API_KEY=your_api_key_here
LLM_MAX_TOKENS=8192
LLM_TEMPERATURE=0.3
LLM_API_TIMEOUT=120000
`;
    
    fs.writeFileSync(envPath, defaultEnv, 'utf8');
    console.log('已创建基本的.env文件，请手动更新API密钥');
    return;
  }
  
  // 读取现有.env文件
  let envContent = fs.readFileSync(envPath, 'utf8');
  let updated = false;
  
  // 检查各项配置，如果缺少则添加
  const requiredConfigs = {
    LLM_PROVIDER: 'volcengine',
    VOLCENGINE_MODEL: 'deepseek-r1-250120',
    LLM_MAX_TOKENS: '8192',
    LLM_TEMPERATURE: '0.3',
    LLM_API_TIMEOUT: '120000'
  };
  
  // 添加缺失的配置
  for (const [key, defaultValue] of Object.entries(requiredConfigs)) {
    if (!envContent.includes(`${key}=`)) {
      envContent += `\n${key}=${defaultValue}`;
      updated = true;
      console.log(`添加缺失配置: ${key}=${defaultValue}`);
    }
  }
  
  // 检查API密钥是否存在，如果不存在则添加占位符
  if (!envContent.includes('VOLCENGINE_API_KEY=')) {
    envContent += '\nVOLCENGINE_API_KEY=your_api_key_here';
    updated = true;
    console.log('添加火山引擎API密钥占位符');
  }
  
  if (!envContent.includes('DEEPSEEK_API_KEY=')) {
    envContent += '\nDEEPSEEK_API_KEY=your_api_key_here';
    updated = true;
    console.log('添加DeepSeek API密钥占位符');
  }
  
  // 更新文件
  if (updated) {
    fs.writeFileSync(envPath, envContent, 'utf8');
    console.log('环境变量配置已更新');
  } else {
    console.log('环境变量配置检查完成，无需更新');
  }
}

// 更新数据库配置
function updateDbConfig() {
  console.log('正在检查数据库配置...');
  
  const configPath = path.join(__dirname, '..', 'config', 'database.js');
  
  // 检查配置文件是否存在
  if (!fs.existsSync(configPath)) {
    console.warn('未找到数据库配置文件，请确保数据库配置正确');
    return;
  }
  
  // 读取数据库配置
  let configContent = fs.readFileSync(configPath, 'utf8');
  
  // 检查是否已经支持长文本类型
  if (!configContent.includes('longtext')) {
    console.log('添加对长文本类型的支持...');
    
    // 找到适当的位置添加自定义类型定义
    const insertPoint = configContent.indexOf('module.exports');
    
    if (insertPoint !== -1) {
      // 添加自定义类型定义
      const customTypeCode = `
// 添加对长文本类型的支持
const { TEXT } = require('sequelize');
function LONGTEXT() {
  return TEXT('long');
}

`;
      
      configContent = configContent.slice(0, insertPoint) + 
        customTypeCode + 
        configContent.slice(insertPoint);
      
      fs.writeFileSync(configPath, configContent, 'utf8');
      console.log('已添加对长文本类型的支持');
    }
  }
}

// 检查服务启动文件
function checkStartupFile() {
  console.log('正在检查服务启动文件...');
  
  const serverPath = path.join(__dirname, '..', 'llm-logging-server.js');
  
  // 检查文件是否存在
  if (!fs.existsSync(serverPath)) {
    console.log('未找到LLM日志服务器文件，从备份恢复...');
    
    // 从备份中复制
    const backupPath = path.join(__dirname, '..', '..', 'backups', 'LLM_BACKUP', 'backend', 'llm-logging-server.js');
    
    if (fs.existsSync(backupPath)) {
      fs.copyFileSync(backupPath, serverPath);
      console.log('已从备份恢复LLM日志服务器文件');
    } else {
      console.warn('未找到备份中的LLM日志服务器文件');
    }
  }
}

// 检查上传目录
function checkUploadDir() {
  console.log('正在检查上传目录...');
  
  const uploadDir = path.join(__dirname, '..', 'uploads', 'ai-reports');
  
  // 确保目录存在
  if (!fs.existsSync(uploadDir)) {
    console.log('创建AI报告上传目录...');
    fs.mkdirSync(uploadDir, { recursive: true });
    console.log(`已创建目录: ${uploadDir}`);
  }
}

// 主函数
async function main() {
  try {
    updateEnvConfig();
    updateDbConfig();
    checkStartupFile();
    checkUploadDir();
    
    console.log('===== 应用配置文件更新完成 =====');
    return true;
  } catch (error) {
    console.error('更新配置文件失败:', error);
    return false;
  }
}

// 执行配置更新
main()
  .then(success => {
    if (success) {
      console.log('配置文件更新成功！');
    } else {
      console.error('配置文件更新失败！');
    }
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('执行配置更新脚本时发生错误:', error);
    process.exit(1);
  }); 