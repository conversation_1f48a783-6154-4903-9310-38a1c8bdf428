import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Button,
  Divider,
  Chip,
  CircularProgress,
  Alert,
  IconButton,
  AlertColor,
  Snackbar,
  Tooltip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Modal,
  useMediaQuery
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import EditIcon from '@mui/icons-material/Edit';
import BookmarkIcon from '@mui/icons-material/Bookmark';
import LockIcon from '@mui/icons-material/Lock';
import DownloadIcon from '@mui/icons-material/Download';
import AttachmentIcon from '@mui/icons-material/Attachment';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import ImageIcon from '@mui/icons-material/Image';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import DescriptionIcon from '@mui/icons-material/Description';
import LabelIcon from '@mui/icons-material/Label';
import PersonIcon from '@mui/icons-material/Person';
import MedicalServicesIcon from '@mui/icons-material/MedicalServices';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import CloseIcon from '@mui/icons-material/Close';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';

// 导入主题相关的 Hook 和函数
import { createTheme, ThemeProvider, useTheme as useMuiTheme } from '@mui/material/styles';

// 引入服务上下文
import { useServiceUserContext } from '../context/ServiceUserContext';
import { useAuthStore } from '../store/authStore';
import * as serviceRecordService from '../services/serviceRecordService';
import apiClient from '../services/apiClient';
import ServiceContextBar from '../components/service/ServiceContextBar';

// 引入复用的类型和样式常量
import { RecordTypeEnum, RecordTypeNames, SeverityEnum, SeverityNames } from '../types/recordEnums';

// 个人标签主色
const PERSONAL_TAG_COLOR = '#3498db';
const PERSONAL_TAG_LIGHT_COLOR = `${PERSONAL_TAG_COLOR}20`; // 20%透明度的背景色，增加可见度

// 记录类型颜色映射 - 使用枚举值
const recordTypeColors: Record<string, {main: string, light: string, dark: string}> = {
  // 基本类型
  [RecordTypeEnum.SELF_DESCRIPTION]: {
    main: '#607d8b',  // 蓝灰色
    light: '#eceff1',
    dark: '#37474f'
  },
  [RecordTypeEnum.SYMPTOM]: {
    main: '#d32f2f',  // 红色
    light: '#ffebee',
    dark: '#b71c1c'
  },
  [RecordTypeEnum.EXAMINATION]: {
    main: '#7b1fa2',  // 紫色
    light: '#f3e5f5',
    dark: '#4a148c'
  },
  [RecordTypeEnum.LAB_TEST]: {
    main: '#9c27b0',  // 浅紫色
    light: '#f3e5f5',
    dark: '#6a1b9a'
  },
  [RecordTypeEnum.DIAGNOSIS]: {
    main: '#1976d2',  // 蓝色
    light: '#e3f2fd',
    dark: '#0d47a1'
  },
  [RecordTypeEnum.TREATMENT]: {
    main: '#388e3c',  // 绿色
    light: '#e8f5e9',
    dark: '#1b5e20'
  },
  // ... 添加其他记录类型的颜色映射
};

// 严重程度颜色映射
const severityColors: Record<string, {main: string, light: string, dark: string}> = {
  [SeverityEnum.CRITICAL]: {
    main: '#d32f2f', // 红色
    light: '#ffebee',
    dark: '#b71c1c'
  },
  [SeverityEnum.SEVERE]: {
    main: '#f57c00', // 橙色
    light: '#fff3e0',
    dark: '#e65100'
  },
  [SeverityEnum.MODERATE]: {
    main: '#fbc02d', // 黄色
    light: '#fffde7',
    dark: '#f9a825'
  },
  [SeverityEnum.MILD]: {
    main: '#4caf50', // 绿色
    light: '#e8f5e9',
    dark: '#2e7d32'
  },
  'NORMAL': {
    main: '#2196f3', // 蓝色
    light: '#e3f2fd',
    dark: '#1565c0'
  }
};

// 通知类型接口
interface Notification {
  open: boolean;
  message: string;
  type: AlertColor;
}

// 性别映射
const genderMap: Record<string, string> = {
  'MALE': '男',
  'FEMALE': '女',
  'OTHER': '其他',
  'UNKNOWN': '未知',
  // 兼容英文值
  'male': '男',
  'female': '女',
  'other': '其他',
  'unknown': '未知',
  // 兼容中文值
  '男': '男',
  '女': '女'
};

// 病理阶段映射
const diseaseStageMap: Record<string, string> = {
  'INITIAL': '初期',
  'EARLY': '早期',
  'MIDDLE': '中期',
  'ADVANCED': '晚期',
  'TERMINAL': '终末期',
  'REMISSION': '缓解期',
  'RECOVERY': '康复期',
  'OBSERVATION': '观察期',
  // 数字映射
  '1': '一期',
  '2': '二期',
  '3': '三期',
  '4': '四期',
  // 兼容罗马数字
  'I': '一期',
  'II': '二期',
  'III': '三期',
  'IV': '四期',
  // 兼容中文值，直接返回
  '初期': '初期',
  '早期': '早期',
  '中期': '中期',
  '晚期': '晚期'
};

// 服务记录详情页面组件
const ServiceRecordDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const serviceContext = useServiceUserContext();
  const user = useAuthStore((state) => state.user);
  const currentTheme = useMuiTheme(); // 使用mui的useTheme，避免与本组件内的theme变量冲突
  const isMobile = useMediaQuery(currentTheme.breakpoints.down('sm'));
  
  // 为 ServiceContextBar 创建一个字体更小的主题
  const contextBarTheme = createTheme({
    ...currentTheme,
    typography: {
      ...currentTheme.typography,
      body1: {
        ...currentTheme.typography.body1,
        fontSize: '0.75rem', // 假设比默认 body1 (1rem) 小约2号
      },
      body2: {
        ...currentTheme.typography.body2,
        fontSize: '0.65rem', // 假设比默认 body2 (0.875rem) 小约2号
      },
    },
    components: {
      ...currentTheme.components,
      MuiChip: { // 确保ServiceContextBar中的Chip字体也相应调整
        styleOverrides: {
          ...currentTheme.components?.MuiChip?.styleOverrides,
          labelSmall: {
            ...(currentTheme.components?.MuiChip?.styleOverrides as any)?.labelSmall,
            fontSize: '0.6rem', // ServiceContextBar 内部 Chip size="small"
          },
        }
      },
      MuiButton: { // 如果 ServiceContextBar 内部有 Button (例如 "选择上下文" 按钮)
        styleOverrides: {
          ...currentTheme.components?.MuiButton?.styleOverrides,
          root: {
            ...(currentTheme.components?.MuiButton?.styleOverrides as any)?.root,
            fontSize: '0.7rem',
          }
        }
      }
    }
  });
  
  // 为页面标题行创建一个字体更小的主题
  const pageTitleRowTheme = createTheme({
    ...currentTheme,
    typography: {
      ...currentTheme.typography,
      // 当前标题 variant="h5", sx={{ fontSize: { xs: '1.2rem', sm: '1.5rem' } }}
      // 将其减小2号，例如 xs: '1.0rem', sm: '1.2rem' 
      // 或者直接将其设置为类似 h6 的大小
      h5: {
        ...currentTheme.typography.h5,
        fontSize: currentTheme.typography.h6.fontSize || '1.0rem', // 使用h6大小或默认1rem
      },
    },
    components: {
      ...currentTheme.components,
      MuiButton: {
        ...currentTheme.components?.MuiButton,
        styleOverrides: {
          ...currentTheme.components?.MuiButton?.styleOverrides,
          // 按钮 size="small" or "medium"
          // 减小字体，例如 0.7rem
          root: {
             ...(currentTheme.components?.MuiButton?.styleOverrides as any)?.root,
            fontSize: '0.7rem',
          },
          // 如果需要更细致的控制，可以按size调整
          // sizeSmall: { fontSize: '0.65rem' },
          // sizeMedium: { fontSize: '0.7rem' },
        }
      }
    }
  });
  
  // 状态
  const [record, setRecord] = useState<any>(null);
  const [patient, setPatient] = useState<any>(null);
  const [disease, setDisease] = useState<any>(null);
  const [attachments, setAttachments] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [notification, setNotification] = useState<Notification>({
    open: false,
    message: '',
    type: 'info'
  });
  
  // 预览相关状态
  const [previewOpen, setPreviewOpen] = useState<boolean>(false);
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [previewTitle, setPreviewTitle] = useState<string>('');
  
  // 获取记录数据
  useEffect(() => {
    const fetchRecord = async () => {
      if (!id) return;
      
      setLoading(true);
      try {
        console.log(`[记录详情] 开始获取记录，ID: ${id}`);
        const response = await serviceRecordService.getServiceRecordById(id);
        console.log(`[记录详情] 获取到API响应:`, response);
        
        // 检查响应是否包含错误消息
        if (!response.success && response.message) {
          console.error(`[记录详情] API返回错误:`, response.message);
          setError(response.message);
          setRecord(null);
        } else if (response && response.data) {
          console.log(`[记录详情] 成功获取记录数据:`, response.data);
          setRecord(response.data);
          
          // 如果记录有患者ID和病理ID，尝试加载关联信息
          if (response.data.patientId) {
            fetchPatientData(response.data.patientId);
          }
          
          if (response.data.diseaseId) {
            fetchDiseaseData(response.data.diseaseId);
          }
          
          // 获取附件
          fetchAttachments(id);
        } else {
          console.error(`[记录详情] 响应中没有数据:`, response);
          setError('记录数据加载失败');
        }
      } catch (err: any) {
        console.error('[记录详情] 获取记录详情失败:', err);
        setError(err.message || '获取记录详情失败');
      } finally {
        setLoading(false);
      }
    };
    
    fetchRecord();
  }, [id]);
  
  // 获取患者数据
  const fetchPatientData = async (patientId: string) => {
    try {
      console.log(`[记录详情] 开始获取患者数据，ID: ${patientId}`);
      // 使用普通记录API，但添加service_context参数
      const response = await apiClient.get(`/patients/${patientId}`, {
        params: {
          service_context: true,
          include_all_users: true
        }
      });
      
      if (response.data) {
        console.log(`[记录详情] 成功获取患者数据:`, response.data);
        setPatient(response.data);
      }
    } catch (err) {
      console.error('[记录详情] 获取患者数据失败:', err);
      // 不设置全局错误，仅记录日志
    }
  };
  
  // 获取病理数据
  const fetchDiseaseData = async (diseaseId: string) => {
    try {
      console.log(`[记录详情] 开始获取病理数据，ID: ${diseaseId}`);
      // 使用普通记录API，但添加service_context参数
      const response = await apiClient.get(`/diseases/${diseaseId}`, {
        params: {
          service_context: true,
          include_all_users: true
        }
      });
      
      if (response.data) {
        console.log(`[记录详情] 成功获取病理数据:`, response.data);
        setDisease(response.data);
      }
    } catch (err) {
      console.error('[记录详情] 获取病理数据失败:', err);
      // 不设置全局错误，仅记录日志
    }
  };
  
  // 获取附件数据
  const fetchAttachments = async (recordId: string) => {
    setLoading(true);
    try {
      console.log(`[记录详情] 开始获取附件数据，记录ID: ${recordId}`);
      const response = await apiClient.get(`/records/attachments/record/${recordId}`, {
        params: {
          service_context: true,
          include_all_users: true,
          include_deleted: true
        }
      });
      
      if (Array.isArray(response.data)) {
        console.log(`[记录详情] 成功获取附件数据，数量: ${response.data.length}`);
        setAttachments(response.data);
      } else {
        console.warn(`[记录详情] 附件数据格式不正确:`, response.data);
        setAttachments([]);
      }
    } catch (err) {
      console.error('[记录详情] 获取附件数据失败:', err);
      setAttachments([]);
    } finally {
      setLoading(false);
    }
  };
  
  // 判断是否有编辑权限
  const hasEditPermission = (): boolean => {
    // 获取当前权限级别
    const privacyLevel = serviceContext.privacyLevel;
    
    if (user?.role === 'ADMIN') return true; // 管理员始终有权限
    
    // 根据授权级别判断权限
    switch(privacyLevel) {
      case 'FULL':
        return true; // 完全授权可以编辑
      case 'STANDARD':
        return record && record.createdBy === user?.id; // 标准授权只能编辑自己创建的记录
      case 'BASIC':
      default:
        return false; // 基础授权只读
    }
  };
  
  // 处理编辑记录
  const handleEditRecord = () => {
    if (!hasEditPermission()) {
      setNotification({
        open: true,
        message: '您没有编辑此记录的权限',
        type: 'error'
      });
      return;
    }
    
    navigate(`/service-records/${id}/edit`);
  };
  
  // 工具函数 - 格式化日期时间
  const formatDateTime = (dateTime: string): string => {
    if (!dateTime || dateTime === '-' || dateTime === 'null') return '-';
    try {
      const date = new Date(dateTime);
      // 检查日期是否有效
      if (isNaN(date.getTime())) return '-';
      return format(date, 'yyyy-MM-dd HH:mm', { locale: zhCN });
    } catch (e) {
      console.warn('[记录详情] 日期格式化失败:', e);
      return dateTime || '-';
    }
  };
  
  // 获取记录类型全名
  const getRecordTypeName = (type: string | string[]): string => {
    if (Array.isArray(type)) {
      return type.map(t => RecordTypeNames[t as RecordTypeEnum] || t).join(', ');
    }
    return RecordTypeNames[type as RecordTypeEnum] || type;
  };
  
  // 获取记录类型完整颜色
  const getRecordTypeFullColors = (type: string): {main: string, light: string, dark: string} => {
    return recordTypeColors[type] || { main: '#9e9e9e', light: '#f5f5f5', dark: '#616161' };
  };
  
  // 获取严重程度文本
  const getSeverityText = (severity: string): string => {
    return SeverityNames[severity as SeverityEnum] || severity;
  };
  
  // 获取严重程度颜色
  const getSeverityMainColor = (severity: string): string => {
    return severityColors[severity as SeverityEnum]?.main || '#757575';
  };
  
  // 解析记录类型（可能是数组或单个字符串）
  const parseRecordType = (recordType: any): string[] => {
    if (!recordType) return [];
    
    if (typeof recordType === 'string') {
      try {
        // 尝试解析JSON
        const parsed = JSON.parse(recordType);
        return Array.isArray(parsed) ? parsed : [recordType];
      } catch (e) {
        // 如果解析失败，则认为是单个字符串
        return [recordType];
      }
    }
    
    if (Array.isArray(recordType)) {
      return recordType;
    }
    
    return [String(recordType)];
  };
  
  // 附件相关工具函数
  // 获取附件图标
  const getAttachmentIcon = (fileName: string | undefined) => {
    // 确保fileName存在
    if (!fileName) {
      return <InsertDriveFileIcon />;
    }
    
    const extension = fileName.split('.').pop()?.toLowerCase() || '';
    
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension)) {
      return <ImageIcon />;
    } else if (extension === 'pdf') {
      return <PictureAsPdfIcon />;
    } else if (['doc', 'docx', 'txt', 'rtf', 'odt'].includes(extension)) {
      return <DescriptionIcon />;
    } else {
      return <InsertDriveFileIcon />;
    }
  };
  
  // 格式化文件大小显示
  const formatFileSize = (bytes: number | undefined): string => {
    if (bytes === undefined || bytes === null) return '未知大小';
    
    if (bytes < 1024) {
      return `${bytes} B`;
    } else if (bytes < 1024 * 1024) {
      return `${(bytes / 1024).toFixed(1)} KB`;
    } else {
      return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
    }
  };
  
  // 判断附件是否可预览
  const isPreviewable = (attachment: any): boolean => {
    // 检查文件类型是否为图片
    const isImage = attachment.file_type?.startsWith('image/') || 
                   attachment.fileType?.startsWith('image/');
    
    // 检查文件大小是否小于500KB (500 * 1024 = 512000 字节)
    const fileSize = attachment.file_size || attachment.fileSize || 0;
    const isSizeAllowed = fileSize <= 512000;
    
    return isImage && isSizeAllowed;
  };
  
  // 下载附件
  const downloadAttachment = async (attachmentId: string, fileName: string) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('未找到身份验证令牌，请重新登录');
      }

      // 获取已配置的后端地址
      const backendUrl = localStorage.getItem('backendServerIP') || apiClient.defaults.baseURL || 'http://localhost:3001';
      
      // 构建完整的URL
      const downloadUrl = `${backendUrl}/records/attachments/download/${attachmentId}?service_context=true`;
      
      console.log(`开始下载附件: ${downloadUrl}`);
      
      // 使用XMLHttpRequest代替fetch以更好地处理二进制数据和下载进度
      const xhr = new XMLHttpRequest();
      xhr.open('GET', downloadUrl, true);
      xhr.responseType = 'blob';
      xhr.setRequestHeader('Authorization', `Bearer ${token}`);
      
      // 确保发送凭证 (cookies, authorization headers 等)
      xhr.withCredentials = true;
      
      // 添加超时设置
      xhr.timeout = 30000; // 30秒超时
      
      // 超时处理
      xhr.ontimeout = function() {
        console.error('下载附件请求超时');
        throw new Error('下载超时，请稍后重试');
      };
      
      // 添加进度事件处理
      xhr.onprogress = function(event) {
        if (event.lengthComputable) {
          const percentComplete = Math.round((event.loaded / event.total) * 100);
          console.log(`下载进度: ${percentComplete}%`);
        }
      };
      
      // 添加完成事件处理
      xhr.onload = function() {
        if (xhr.status === 200) {
          // 检查响应类型
          console.log(`响应类型: ${xhr.response?.type || '未知'}`);
          
          // 创建一个Blob URL并触发下载
          const blob = xhr.response;
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', fileName);
          document.body.appendChild(link);
          link.click();
          
          // 清理
          setTimeout(() => {
            window.URL.revokeObjectURL(url);
            document.body.removeChild(link);
          }, 100);
        } else {
          // 尝试读取错误信息
          const reader = new FileReader();
          reader.onload = function() {
            let errorMsg = '未知错误';
            try {
              const response = JSON.parse(reader.result as string);
              errorMsg = response.error || errorMsg;
            } catch (e) {
              errorMsg = `下载失败: HTTP状态 ${xhr.status}`;
            }
            console.error('下载失败:', errorMsg);
            throw new Error(errorMsg);
          };
          reader.onerror = function() {
            throw new Error(`下载失败: HTTP状态 ${xhr.status}`);
          };
          reader.readAsText(xhr.response);
        }
      };
      
      // 错误处理
      xhr.onerror = function() {
        console.error('XHR错误:', xhr.statusText);
        // 检查是否是由广告拦截器导致的错误
        const errorMsg = (xhr.status === 0) 
          ? `网络错误，无法下载文件，可能是由广告拦截器或浏览器扩展导致`
          : `网络错误，无法下载文件，可能是CORS或服务器问题`;
        throw new Error(errorMsg);
      };
      
      // 开始下载
      xhr.send();
    } catch (error) {
      console.error('下载附件失败:', error);
      setNotification({
        open: true,
        message: `下载附件失败: ${error instanceof Error ? error.message : '未知错误'}`,
        type: 'error'
      });
    }
  };
  
  // 打开预览
  const openPreview = async (attachmentId: string, fileName: string) => {
    try {
      setPreviewTitle(fileName);
      
      // 获取已配置的后端地址
      const backendUrl = localStorage.getItem('backendServerIP') || apiClient.defaults.baseURL || 'http://localhost:3001';
      const downloadUrl = `${backendUrl}/records/attachments/download/${attachmentId}?service_context=true`;
      
      console.log(`开始预览图片: ${downloadUrl}`);
      
      // 获取token
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('未找到身份验证令牌，请重新登录');
      }
      
      // 使用XMLHttpRequest代替axios，保持与下载函数相同的处理方式
      const xhr = new XMLHttpRequest();
      xhr.open('GET', downloadUrl, true);
      xhr.responseType = 'blob';
      xhr.setRequestHeader('Authorization', `Bearer ${token}`);
      xhr.withCredentials = true;
      
      // 添加超时设置
      xhr.timeout = 30000; // 30秒超时
      
      // 错误处理
      xhr.onerror = function() {
        console.error('预览图片加载失败:', xhr.statusText);
        const errorMsg = (xhr.status === 0) 
          ? `网络错误，无法加载图片，可能是由广告拦截器或浏览器扩展导致`
          : `网络错误，无法加载图片，可能是CORS或服务器问题`;
        throw new Error(errorMsg);
      };
      
      // 超时处理
      xhr.ontimeout = function() {
        console.error('预览图片请求超时');
        throw new Error('加载超时，请稍后重试');
      };
      
      // 完成处理
      xhr.onload = function() {
        if (xhr.status === 200) {
          // 创建Blob URL
          const url = window.URL.createObjectURL(xhr.response);
          setPreviewUrl(url);
          setPreviewOpen(true);
        } else {
          throw new Error(`加载图片失败: HTTP状态 ${xhr.status}`);
        }
      };
      
      // 开始请求
      xhr.send();
    } catch (error) {
      console.error('预览图片失败:', error);
      setNotification({
        open: true,
        message: `预览图片失败: ${error instanceof Error ? error.message : '未知错误'}`,
        type: 'error'
      });
    }
  };
  
  // 关闭预览
  const closePreview = () => {
    // 释放Blob URL
    if (previewUrl) {
      window.URL.revokeObjectURL(previewUrl);
    }
    setPreviewUrl('');
    setPreviewOpen(false);
  };
  
  // 获取性别中文名称
  const getGenderName = (gender: string): string => {
    return genderMap[gender] || gender || '未知';
  };
  
  // 获取病理阶段中文名称
  const getDiseaseStage = (stage: string): string => {
    return diseaseStageMap[stage] || stage || '未指定';
  };
  
  // 关闭通知
  const handleCloseNotification = () => {
    setNotification({
      ...notification,
      open: false
    });
  };
  
  // 返回上一页
  const handleGoBack = () => {
    navigate(-1);
  };
  
  // 显示加载中
  if (loading) {
    return (
      <Box sx={{ 
        display: 'flex', 
        flexDirection: 'column',
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '50vh',
        p: 2
      }}>
        <CircularProgress size={isMobile ? 40 : 60} thickness={4} />
        <Typography 
          variant="body1" 
          sx={{ 
            mt: 2, 
            color: 'text.secondary',
            fontSize: { xs: '0.9rem', sm: '1rem' }
          }}
        >
          正在加载记录详情...
        </Typography>
      </Box>
    );
  }
  
  // 显示错误
  if (error) {
    return (
      <Box sx={{ 
        m: { xs: 1.5, sm: 2 },
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-start'
      }}>
        <Alert 
          severity="warning" 
          variant={isMobile ? "standard" : "outlined"}
          sx={{ 
            mb: 2,
            width: '100%',
            fontSize: { xs: '0.85rem', sm: '0.9rem' }
          }}
        >
          {error}
        </Alert>
        <Button 
          startIcon={<ArrowBackIcon />}
          onClick={handleGoBack}
          variant="outlined"
          size={isMobile ? "medium" : "large"}
          fullWidth={isMobile}
        >
          返回服务记录列表
        </Button>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          记录ID: {id}
        </Typography>
      </Box>
    );
  }
  
  // 显示记录不存在
  if (!record) {
    return (
      <Box sx={{ 
        m: { xs: 1.5, sm: 2 },
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-start'
      }}>
        <Alert 
          severity="warning" 
          variant={isMobile ? "standard" : "outlined"}
          sx={{ 
            mb: 2,
            width: '100%',
            fontSize: { xs: '0.85rem', sm: '0.9rem' }
          }}
        >
          找不到该记录或记录可能已被删除
        </Alert>
        <Button 
          startIcon={<ArrowBackIcon />}
          onClick={handleGoBack}
          variant="outlined"
          size={isMobile ? "medium" : "large"}
          fullWidth={isMobile}
        >
          返回服务记录列表
        </Button>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          记录ID: {id}
        </Typography>
      </Box>
    );
  }
  
  // 解析记录类型（可能是数组或单个字符串）
  const recordTypes = parseRecordType(record.type || record.recordType || record.primaryType);
  // 获取主类型
  const mainType = recordTypes.length > 0 ? recordTypes[0] : '';
  const typeColors = getRecordTypeFullColors(mainType);
  
  return (
    <Box sx={{ m: 0, width: '100%', px: '10px' }}>
      {/* 面包屑导航 - Removed as per request */}
      {/* 
      <Breadcrumbs sx={{ mb: 2 }}>
        <MuiLink 
          component={Link} 
          to="/dashboard" 
          color="inherit" 
          underline="hover"
        >
          看板
        </MuiLink>
        <MuiLink 
          component={Link} 
          to="/service-records" 
          color="inherit" 
          underline="hover"
        >
          服务记录管理
        </MuiLink>
        <Typography color="text.primary">{record.title}</Typography>
      </Breadcrumbs>
      */}
      
      {/* 服务上下文信息栏 */}
      <Box sx={{ paddingLeft: '0px', paddingRight: '0px', paddingTop: '10px' }}>
        <ThemeProvider theme={contextBarTheme}>
          <ServiceContextBar />
        </ThemeProvider>
      </Box>
      
      {/* 页面标题和操作按钮 */}
      <ThemeProvider theme={pageTitleRowTheme}>
        <Box sx={{ 
          display: 'flex', 
          flexDirection: { xs: 'column', sm: 'row' },
          justifyContent: 'space-between', 
          alignItems: { xs: 'flex-start', sm: 'center' }, 
          mb: 2,
          mt: 2,
          paddingLeft: '10px',
          paddingRight: '10px'
        }}>
          <Typography 
            variant="h5"
            component="h1" 
            gutterBottom 
            sx={{ 
              fontWeight: 500,
              mb: { xs: 1, sm: 0 }
            }}
          >
            服务记录详情
          </Typography>
          
          <Box sx={{ 
            display: 'flex', 
            gap: 1,
            width: { xs: '100%', sm: 'auto' },
            justifyContent: { xs: 'space-between', sm: 'flex-end' }
          }}>
            <Button 
              startIcon={<ArrowBackIcon />}
              onClick={handleGoBack}
              variant="outlined"
              size={isMobile ? "small" : "medium"}
              sx={{ flex: { xs: 1, sm: 'none' } }}
            >
              返回
            </Button>
            
            {hasEditPermission() && (
              <Button 
                startIcon={<EditIcon />}
                onClick={handleEditRecord}
                variant="contained"
                color="primary"
                size={isMobile ? "small" : "medium"}
                sx={{ flex: { xs: 1, sm: 'none' } }}
              >
                编辑
              </Button>
            )}
          </Box>
          <Divider sx={{ mt: 2, mb: { xs: 2, md: 3 }, width: '100%' }} />
        </Box>
      </ThemeProvider>
      
      {/* 记录详情卡片 */}
      <Paper
        elevation={0}
        sx={{ 
          mb: 3,
          border: '1px solid #e0e0e0',
          borderRadius: { xs: 1, sm: 2 },
          position: 'relative',
          overflow: 'hidden',
          marginLeft: '10px',
          marginRight: '10px',
          width: 'auto',
        }}
      >
        {/* 左侧色条 */}
        <Box sx={{
          position: 'absolute',
          left: 0,
          top: 0,
          bottom: 0,
          width: { xs: '4px', sm: '6px' },
          backgroundColor: typeColors.main
        }} />
        
        {/* 已删除记录警告 */}
        {record.isDeleted && (
          <Alert 
            severity="warning" 
            sx={{ mb: 0, borderRadius: 0 }}
          >
            此记录已被删除 {(record.deletedAt || record.deleted_at) && 
              `(删除时间: ${formatDateTime(record.deletedAt || record.deleted_at)})`}
          </Alert>
        )}
        
        {/* 记录内容区域 */}
        <Box sx={{ 
          p: { xs: 1.5, sm: 2, md: 3 }, 
          pl: { xs: 2.5, sm: 3, md: 4 }
        }}>
          {/* 记录标题及属性行 */}
          <Box sx={{ 
            display: 'flex', 
            flexDirection: { xs: 'column', sm: 'row' },
            justifyContent: 'space-between',
            alignItems: { xs: 'flex-start', sm: 'center' },
            mb: { xs: 1.5, sm: 2 } 
          }}>
            <Box sx={{ 
              display: 'flex', 
              alignItems: 'flex-start',
              flexWrap: 'wrap', 
              mb: { xs: 1, sm: 0 } 
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mr: 1, mb: 0.5 }}>
                {record.isImportant === true && (
                  <Tooltip title="重要记录">
                    <BookmarkIcon color="error" sx={{ mr: 0.5, fontSize: { xs: '1.2rem', sm: '1.5rem' } }} />
                  </Tooltip>
                )}
                {record.isPrivate === true && (
                  <Tooltip title="隐私记录">
                    <LockIcon color="primary" sx={{ mr: 0.5, fontSize: { xs: '1.2rem', sm: '1.5rem' } }} />
                  </Tooltip>
                )}
              </Box>
              {/* Record Title - Removed as per request */}
              {/* 
              <Typography variant="h6" component="h2" sx={{ 
                fontWeight: 600,
                fontSize: { xs: '1.1rem', sm: '1.25rem' },
                wordBreak: 'break-word'
              }}>
                {record.title}
              </Typography>
              */}
            </Box>
            
            <Typography 
              variant="body2" 
              color="text.secondary"
              sx={{ 
                display: 'flex',
                alignItems: 'center',
                flexShrink: 0,
                fontSize: { xs: '0.75rem', sm: '0.875rem' }
              }}
            >
              {formatDateTime(record.recordDate || record.createdAt)}
            </Typography>
          </Box>
          
          {/* 标签行 */}
          <Box sx={{ 
            display: 'flex', 
            flexWrap: 'wrap', 
            gap: { xs: 0.5, sm: 1 }, 
            mb: { xs: 2, sm: 3 } 
          }}>
            {/* 记录类型标签 */}
            {recordTypes.map((type: string, index: number) => (
              <Chip
                key={`type-${index}`}
                label={getRecordTypeName(type)}
                size="small"
                sx={{
                  bgcolor: type === mainType ? typeColors.light : 'transparent',
                  color: typeColors.dark,
                  border: `1px solid ${typeColors.main}`,
                  fontWeight: type === mainType ? 600 : 400,
                  fontSize: { xs: '0.7rem', sm: '0.75rem' },
                  height: { xs: '24px', sm: '32px' },
                  mb: 0.5
                }}
              />
            ))}
            
            {/* 严重程度标签 */}
            {record.severity && (
              <Chip
                label={getSeverityText(record.severity)}
                size="small"
                sx={{
                  bgcolor: `${getSeverityMainColor(record.severity)}20`,
                  color: getSeverityMainColor(record.severity),
                  border: `1px solid ${getSeverityMainColor(record.severity)}`,
                  fontWeight: 500,
                  fontSize: { xs: '0.7rem', sm: '0.75rem' },
                  height: { xs: '24px', sm: '32px' },
                  mb: 0.5
                }}
              />
            )}
            
            {/* 自定义标签 */}
            {record.tags && (Array.isArray(record.tags) ? record.tags : record.tags.split(','))
              .filter((tag: string) => tag && tag.trim())
              .map((tag: string, index: number) => (
                <Chip
                  key={`custom-${index}`}
                  label={tag.trim()}
                  size="small"
                  icon={<LabelIcon fontSize="small" />}
                  variant="outlined"
                  sx={{
                    bgcolor: PERSONAL_TAG_LIGHT_COLOR,
                    color: PERSONAL_TAG_COLOR,
                    border: `1px solid ${PERSONAL_TAG_COLOR}`,
                    fontWeight: 500,
                    fontSize: { xs: '0.7rem', sm: '0.75rem' },
                    height: { xs: '24px', sm: '32px' },
                    mb: 0.5,
                    '& .MuiChip-icon': {
                      color: PERSONAL_TAG_COLOR,
                      marginRight: '2px',
                      fontSize: { xs: '0.7rem', sm: '0.8rem' }
                    }
                  }}
                />
              ))
            }
          </Box>
          
          {/* 关联信息 */}
          <Box sx={{ 
            display: 'flex', 
            flexDirection: 'column',
            mb: { xs: 2, sm: 3 }
          }}>
            <Typography 
              variant="subtitle1" 
              sx={{ 
                fontSize: { xs: '0.95rem', sm: '1.05rem' },
                fontWeight: 500,
                mb: 1,
                display: 'flex',
                alignItems: 'center'
              }}
            >
              <Box 
                component="span" 
                sx={{ 
                  width: 4, 
                  height: 16, 
                  backgroundColor: 'primary.main', 
                  mr: 1,
                  display: 'inline-block',
                  borderRadius: 4
                }} 
              />
              关联信息
            </Typography>
            
            <Box sx={{ 
              display: 'flex', 
              flexDirection: { xs: 'column', sm: 'row' }, 
              gap: { xs: 1.5, sm: 2 },
              backgroundColor: 'background.paper',
              borderRadius: { xs: 1, sm: 2 },
              p: { xs: 1, sm: 1.5 },
              border: '1px solid #e0e0e0'
            }}>
              <Box sx={{ 
                width: { xs: '100%', sm: '50%' } 
              }}>
                <Box sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  mb: 0.5 
                }}>
                  <PersonIcon sx={{ 
                    color: 'primary.main', 
                    mr: 1, 
                    fontSize: { xs: '1.1rem', sm: '1.2rem' } 
                  }} />
                  <Typography variant="subtitle2" sx={{ 
                    fontSize: { xs: '0.85rem', sm: '0.9rem' },
                    fontWeight: 600,
                    color: 'primary.main'
                  }}>
                    患者信息
                  </Typography>
                </Box>
                
                {patient ? (
                  <Box sx={{ ml: 2 }}>
                    <Typography 
                      variant="body1" 
                      component="div"
                      sx={{ 
                        fontSize: { xs: '0.9rem', sm: '1rem' },
                        fontWeight: 600,
                        mb: 0.5,
                        display: 'flex',
                        alignItems: 'center'
                      }}
                    >
                      {patient.name}
                      {patient.isPrimary && (
                        <Chip 
                          label="本人" 
                          size="small" 
                          color="primary"
                          variant="outlined"
                          sx={{ 
                            ml: 1, 
                            height: 20, 
                            fontSize: '0.7rem' 
                          }} 
                        />
                      )}
                    </Typography>
                    
                    <Typography variant="body2" sx={{ 
                      fontSize: { xs: '0.8rem', sm: '0.85rem' },
                      color: 'text.secondary',
                      display: 'flex',
                      flexDirection: 'column',
                      gap: 0.5
                    }}>
                      <span>性别: {getGenderName(patient.gender)}</span>
                      <span>联系方式: {patient.phoneNumber || '未填写'}</span>
                      {patient.birthDate && (
                        <span>出生日期: {format(new Date(patient.birthDate), 'yyyy-MM-dd')}</span>
                      )}
                    </Typography>
                  </Box>
                ) : (
                  <Typography 
                    variant="body1" 
                    sx={{ 
                      fontSize: { xs: '0.9rem', sm: '1rem' },
                      fontWeight: 500,
                      color: 'text.secondary',
                      ml: 2
                    }}
                  >
                    {record.patientName || '未关联患者'}
                    {record.patientId && (
                      <Typography variant="caption" sx={{ 
                        display: 'block', 
                        color: 'text.secondary' 
                      }}>
                        ID: {record.patientId}
                      </Typography>
                    )}
                  </Typography>
                )}
              </Box>
              
              <Box sx={{ 
                width: { xs: '100%', sm: '50%' } 
              }}>
                <Box sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  mb: 0.5 
                }}>
                  <MedicalServicesIcon sx={{ 
                    color: 'secondary.main', 
                    mr: 1, 
                    fontSize: { xs: '1.1rem', sm: '1.2rem' } 
                  }} />
                  <Typography variant="subtitle2" sx={{ 
                    fontSize: { xs: '0.85rem', sm: '0.9rem' },
                    fontWeight: 600,
                    color: 'secondary.main'
                  }}>
                    病理信息
                  </Typography>
                </Box>
                
                {disease ? (
                  <Box sx={{ ml: 2 }}>
                    <Typography 
                      variant="body1" 
                      component="div"
                      sx={{ 
                        fontSize: { xs: '0.9rem', sm: '1rem' },
                        fontWeight: 600,
                        mb: 0.5
                      }}
                    >
                      {disease.name}
                    </Typography>
                    
                    <Typography variant="body2" sx={{ 
                      fontSize: { xs: '0.8rem', sm: '0.85rem' },
                      color: 'text.secondary',
                      display: 'flex',
                      flexDirection: 'column',
                      gap: 0.5
                    }}>
                      <span>阶段: {getDiseaseStage(disease.stage)}</span>
                      {disease.severity && (
                        <span>严重程度: 
                          <Box component="span" sx={{ 
                            color: getSeverityMainColor(disease.severity),
                            fontWeight: 500,
                            ml: 0.5
                          }}>
                            {getSeverityText(disease.severity)}
                          </Box>
                        </span>
                      )}
                      {disease.diagnosisDate && (
                        <span>确诊日期: {format(new Date(disease.diagnosisDate), 'yyyy-MM-dd')}</span>
                      )}
                      {disease.isPrivate === 1 ? (
                        <Box 
                          component="span" 
                          sx={{ 
                            display: 'flex', 
                            alignItems: 'center' 
                          }}
                        >
                          <LockIcon sx={{ fontSize: '0.8rem', mr: 0.5 }} />
                          <span>隐私病理</span>
                        </Box>
                      ) : null}
                    </Typography>
                  </Box>
                ) : (
                  <Typography 
                    variant="body1" 
                    sx={{ 
                      fontSize: { xs: '0.9rem', sm: '1rem' },
                      fontWeight: 500,
                      color: 'text.secondary',
                      ml: 2
                    }}
                  >
                    {record.diseaseName || '未关联病理'}
                    {record.diseaseId && (
                      <Typography variant="caption" sx={{ 
                        display: 'block', 
                        color: 'text.secondary' 
                      }}>
                        ID: {record.diseaseId}
                      </Typography>
                    )}
                  </Typography>
                )}
              </Box>
            </Box>
          </Box>
          
          {/* 记录内容 */}
          <Box sx={{ mb: { xs: 2, sm: 3 } }}>
            <Typography
              variant="subtitle1"
              sx={{
                fontSize: { xs: '0.95rem', sm: '1.05rem' },
                fontWeight: 500,
                mb: 1,
                display: 'flex',
                alignItems: 'center',
                paddingLeft: '10px',
                paddingRight: '10px',
              }}
            >
              <Box
                component="span"
                sx={{
                  width: 4,
                  height: 16,
                  backgroundColor: typeColors.main,
                  mr: 1,
                  display: 'inline-block',
                  borderRadius: 4
                }}
              />
              记录内容
            </Typography>

            <Box
              sx={{
                paddingLeft: '10px',
                paddingRight: '10px',
              }}
            >
              <Typography
                variant="body1"
                component="div"
                sx={{
                  whiteSpace: 'pre-line',
                  overflowWrap: 'break-word',
                  fontSize: { xs: '0.7rem', sm: '0.8rem' },
                  lineHeight: { xs: 1.5, sm: 1.6 },
                  backgroundColor: 'background.paper',
                  borderRadius: { xs: 1, sm: 2 },
                  p: { xs: 1.5, sm: 2 },
                  border: '1px solid #e0e0e0',
                }}
              >
                {record.content || '无内容'}
              </Typography>
            </Box>
          </Box>
          
          {/* 附件区域 */}
          {attachments && attachments.length > 0 && (
            <Box sx={{ mb: { xs: 2, sm: 3 } }}>
              <Typography 
                variant="subtitle1" 
                sx={{ 
                  fontSize: { xs: '0.95rem', sm: '1.05rem' },
                  fontWeight: 500,
                  mb: 1,
                  display: 'flex',
                  alignItems: 'center'
                }}
              >
                <Box 
                  component="span" 
                  sx={{ 
                    width: 4, 
                    height: 16, 
                    backgroundColor: 'info.main', 
                    mr: 1,
                    display: 'inline-block',
                    borderRadius: 4
                  }} 
                />
                <AttachmentIcon sx={{ mr: 1, fontSize: { xs: '1rem', sm: '1.1rem' }, color: 'text.secondary' }} />
                附件 ({attachments.length})
              </Typography>
              
              <List sx={{ 
                bgcolor: 'background.paper',
                border: '1px solid #e0e0e0',
                borderRadius: { xs: 1, sm: 2 },
                overflow: 'hidden',
                '& .MuiListItem-root': {
                  pl: { xs: 1, sm: 2 },
                  pr: { xs: 2, sm: 3 }
                },
                '& .MuiListItemButton-root': {
                  py: { xs: 0.75, sm: 1 }
                },
                '& .MuiListItemIcon-root': {
                  minWidth: { xs: 36, sm: 48 }
                },
                '& .MuiListItemText-primary': {
                  fontSize: { xs: '0.85rem', sm: '0.95rem' },
                  fontWeight: 500
                },
                '& .MuiListItemText-secondary': {
                  fontSize: { xs: '0.75rem', sm: '0.8rem' }
                }
              }}>
                {attachments.map((attachment: any) => (
                  <ListItem
                    key={attachment.id}
                    disablePadding
                    secondaryAction={
                      <Box sx={{ display: 'flex' }}>
                        {isPreviewable(attachment) && (
                          <Tooltip title="预览">
                            <IconButton 
                              edge="end" 
                              aria-label="预览"
                              onClick={() => openPreview(attachment.id, attachment.fileName || attachment.file_name || '未知文件')}
                              sx={{ p: { xs: 0.75, sm: 1 } }}
                              size={isMobile ? "small" : "medium"}
                            >
                              <ZoomInIcon fontSize={isMobile ? "small" : "medium"} />
                            </IconButton>
                          </Tooltip>
                        )}
                        <Tooltip title="下载">
                          <IconButton 
                            edge="end" 
                            aria-label="下载"
                            onClick={() => downloadAttachment(attachment.id, attachment.fileName || attachment.file_name || '未知文件')}
                            sx={{ p: { xs: 0.75, sm: 1 } }}
                            size={isMobile ? "small" : "medium"}
                          >
                            <DownloadIcon fontSize={isMobile ? "small" : "medium"} />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    }
                  >
                    <ListItemButton
                      onClick={() => isPreviewable(attachment) 
                        ? openPreview(attachment.id, attachment.fileName || attachment.file_name || '未知文件')
                        : downloadAttachment(attachment.id, attachment.fileName || attachment.file_name || '未知文件')
                      }
                      dense={isMobile}
                    >
                      <ListItemIcon>
                        {getAttachmentIcon(attachment.fileName || attachment.file_name)}
                      </ListItemIcon>
                      <ListItemText 
                        primary={attachment.fileName || attachment.file_name || '未知文件'}
                        secondary={`${formatFileSize(attachment.fileSize || attachment.file_size || 0)} • ${formatDateTime(attachment.createdAt || attachment.created_at || new Date().toISOString())}`}
                      />
                    </ListItemButton>
                  </ListItem>
                ))}
              </List>
            </Box>
          )}
          
          {/* 元数据信息 */}
          <Box sx={{ 
            display: 'flex',
            flexDirection: 'column',
            gap: 1,
            color: 'text.secondary',
            fontSize: '0.75rem',
            mt: 3,
            pt: 2,
            borderTop: '1px dashed #e0e0e0'
          }}>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: { xs: 2, sm: 3 } }}>
              <Typography component="span" fontSize="inherit">
                <strong>创建时间：</strong>{formatDateTime(record.createdAt)}
              </Typography>
              
              {record.updatedAt !== record.createdAt && (
                <Typography component="span" fontSize="inherit">
                  <strong>更新时间：</strong>{formatDateTime(record.updatedAt)}
                </Typography>
              )}
              
              <Typography component="span" fontSize="inherit">
                <strong>创建者：</strong>{record.createdByName || record.createdBy || '未知'}
              </Typography>
            </Box>
            
            <Typography component="span" fontSize="inherit">
              <strong>记录ID：</strong>{record.id}
            </Typography>
          </Box>
        </Box>
      </Paper>
      
      {/* 操作按钮（底部重复，便于长内容操作） */}
      <Box sx={{ 
        display: 'flex', 
        flexDirection: { xs: 'column', sm: 'row' },
        justifyContent: { xs: 'stretch', sm: 'flex-end' }, 
        gap: { xs: 1, sm: 1.5 },
        mb: 3,
        paddingLeft: '10px',
        paddingRight: '10px'
      }}>
        <Button 
          startIcon={<ArrowBackIcon />}
          onClick={handleGoBack}
          variant="outlined"
          fullWidth={isMobile}
          size={isMobile ? "medium" : "large"}
        >
          返回列表
        </Button>
      </Box>
      
      {/* 通知消息 */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ 
          vertical: 'bottom', 
          horizontal: isMobile ? 'center' : 'right' 
        }}
        sx={{
          bottom: { xs: 16, sm: 24 }
        }}
      >
        <Alert 
          onClose={handleCloseNotification} 
          severity={notification.type}
          variant="filled"
          sx={{ 
            width: '100%',
            boxShadow: 2
          }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
      
      {/* 图片预览Modal */}
      <Modal
        open={previewOpen}
        onClose={closePreview}
        aria-labelledby="图片预览"
        aria-describedby="查看附件图片"
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          p: 2
        }}
      >
        <Box sx={{
          position: 'relative',
          maxWidth: '95%',
          maxHeight: '95%',
          bgcolor: 'background.paper',
          boxShadow: 24,
          borderRadius: 1,
          overflow: 'hidden',
          p: 0
        }}>
          {/* 预览标题 */}
          <Box sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            p: 1,
            pl: 2,
            bgcolor: 'primary.main',
            color: 'white'
          }}>
            <Typography variant="subtitle1" component="div" sx={{ 
              fontSize: { xs: '0.85rem', sm: '0.95rem' },
              fontWeight: 500,
              maxWidth: '80%',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}>
              {previewTitle}
            </Typography>
            <IconButton 
              onClick={closePreview}
              size="small"
              sx={{ color: 'white' }}
            >
              <CloseIcon />
            </IconButton>
          </Box>
          
          {/* 图片容器 */}
          <Box sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            p: 2,
            maxHeight: 'calc(95vh - 48px)', // 减去标题栏高度
            overflow: 'auto'
          }}>
            {previewUrl ? (
              <img 
                src={previewUrl}
                alt={previewTitle}
                style={{
                  maxWidth: '100%',
                  maxHeight: 'calc(95vh - 80px)',
                  objectFit: 'contain'
                }}
              />
            ) : (
              <CircularProgress />
            )}
          </Box>
        </Box>
      </Modal>
    </Box>
  );
};

export default ServiceRecordDetailPage; 