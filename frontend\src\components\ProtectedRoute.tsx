import React, { useEffect, useState, useRef } from 'react';
import { Navigate, useNavigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
// import { fetchUserProfile } from '../services/authService';
import { CircularProgress, Box } from '@mui/material';

// 受保护路由组件，用于验证用户是否已登录
// 如果用户已登录，则显示子组件
// 如果用户未登录，则重定向到登录页
interface ProtectedRouteProps {
  children: React.ReactNode;
}

// 添加自定义错误接口
interface ApiError extends Error {
  response?: {
    status: number;
    data: any;
    headers: any;
  };
  isAuthError?: boolean;
  code?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const [isLoading, setIsLoading] = useState(true);
  const { token, user, setUser } = useAuthStore();
  const navigate = useNavigate();
  const location = useLocation();
  
  // 判断当前是否为服务相关页面 - 使用精确的路径匹配
  const currentPath = location.pathname;
  const servicePagePaths = ['/service-authorizations', '/service-records', '/service-reports', '/service-diseases', '/service/authorizations', '/service/records', '/service/reports', '/service/diseases'];
  const isServicePage = servicePagePaths.some(path => 
    currentPath === path || currentPath.startsWith(path + '/')
  );
  
  // 将路径分类为系统路径，帮助调试路由问题
  const pathCategory = servicePagePaths.find(path => currentPath === path || currentPath.startsWith(path + '/')) || 
                      (currentPath === '/login' ? 'login' : 
                       currentPath === '/dashboard' ? 'dashboard' : 'other');
  
  // 跟踪上一个路径，避免在同一路径上重复初始化
  const prevPathRef = useRef(currentPath);
  // 跟踪组件是否已经挂载和初始化
  const initializedRef = useRef(false);
  // 跟踪是否已经验证过认证状态
  const authCheckedRef = useRef(false);
  // 用于存储上次认证时间
  const lastAuthTimeRef = useRef<number>(0);
  
  // 只在首次渲染和路径实际变化时记录日志
  if (!initializedRef.current || prevPathRef.current !== currentPath) {
    console.log(`[ProtectedRoute] 页面加载: 路径=${currentPath}, 类别=${pathCategory}, 服务页面=${isServicePage}, 时间=${new Date().toISOString()}`);
    prevPathRef.current = currentPath;
  }

  // 为了避免在开发环境中可能的循环渲染导致的页面重载，添加一个参考标记
  const renderId = useRef(Math.random().toString(36).substring(7));
  const mountedRef = useRef(true);
  
  // 只在首次渲染时记录渲染ID
  if (!initializedRef.current) {
    console.log(`[ProtectedRoute] 初始化, 渲染ID: ${renderId.current}`);
    initializedRef.current = true;
  }

  useEffect(() => {
    // 如果路径没有变化且已经验证过认证状态，不重新执行认证检查
    const isSamePath = prevPathRef.current === currentPath;
    // 如果上次认证时间在30秒内，跳过认证检查
    const currentTime = Date.now();
    const shouldSkipCheck = isSamePath && 
                          authCheckedRef.current && 
                          (currentTime - lastAuthTimeRef.current < 30000);
                          
    if (shouldSkipCheck) {
      // 路径没有变化且已经验证过，直接返回
      if (process.env.NODE_ENV === 'development') {
        console.log(`[ProtectedRoute] 路径未变化且最近已验证过，跳过认证检查: ${currentPath}`);
      }
      setIsLoading(false);
      return;
    }
    
    const currentPathRef = currentPath; // 捕获当前路径的引用
    mountedRef.current = true;
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`[ProtectedRoute] 执行认证检查，路径=${currentPathRef}, 服务页面=${isServicePage}`);
    }
    
    // 捕获所有未处理的异常
    const errorHandler = (event: ErrorEvent) => {
      console.error('[ProtectedRoute] 全局错误:', event.error || event.message);
    };
    window.addEventListener('error', errorHandler);
    
    // 添加错误处理函数
    const handleApiError = (error: unknown) => {
      const apiError = error as ApiError;
      
      // 网络错误处理
      if (apiError.message === 'Network Error' || apiError.code === 'ERR_NETWORK') {
        return { isNetworkError: true };
      }
      
      // 401错误处理
      if (apiError.response?.status === 401) {
        return { isAuthError: true };
      }
      
      // 其他错误
      return { isOtherError: true, message: apiError.message };
    };
    
    const checkAuth = async () => {
      if (process.env.NODE_ENV === 'development') {
        console.time('[ProtectedRoute] 认证检查');
      }
      
      // 从localStorage获取token，而不是从store
      const token = localStorage.getItem('token');
      
      // 记录token状态和格式
      if (token) {
        // 仅在开发环境下记录详细token信息
        if (process.env.NODE_ENV === 'development') {
          const tokenParts = token.split('.');
          const isValidJWT = tokenParts.length === 3;
          
          // 如果是有效JWT，解析并检查过期时间
          if (isValidJWT) {
            try {
              const payload = JSON.parse(atob(tokenParts[1]));
              const currentTime = Math.floor(Date.now() / 1000);
              const isExpired = payload.exp && payload.exp < currentTime;
              
              // 如果token已过期，直接处理为无token
              if (isExpired) {
                localStorage.removeItem('token');
                // 继续执行，但当作没有token处理
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      }
      
      // 如果没有token或token已过期
      if (!token) {
        // 服务页面特殊处理 - 不立即重定向，允许页面自己处理
        if (isServicePage) {
          if (process.env.NODE_ENV === 'development') {
            console.log('[ProtectedRoute] 服务页面未找到token，但允许页面加载');
          }
          setIsLoading(false);
          authCheckedRef.current = true;
          lastAuthTimeRef.current = Date.now();
          if (process.env.NODE_ENV === 'development') {
            console.timeEnd('[ProtectedRoute] 认证检查');
          }
          return;
        }
        
        // 保存当前路径用于登录后重定向
        localStorage.setItem('redirectAfterLogin', currentPathRef);
        
        navigate('/login', { replace: true });
        authCheckedRef.current = true;
        lastAuthTimeRef.current = Date.now();
        if (process.env.NODE_ENV === 'development') {
          console.timeEnd('[ProtectedRoute] 认证检查');
        }
        return;
      }
      
      // 如果有token但没有用户信息，尝试获取用户资料
      if (!user || !user.id) {
        try {
          // 使用动态导入方式导入fetchUserProfile
          const authService = await import('../services/authService');
          // 使用类型断言
          const userInfo = await (authService as any).fetchUserProfile();
           
          if (userInfo) {
            setUser(userInfo);
            localStorage.setItem('userId', userInfo.id);
          }
          
          setIsLoading(false);
          lastAuthTimeRef.current = Date.now();
          authCheckedRef.current = true;
        } catch (error) {
          const errorInfo = handleApiError(error);
          
          if (errorInfo.isNetworkError) {
            if (isServicePage) {
              setIsLoading(false);
              return;
            }
            
            // 非服务页面的网络错误需要提示
            console.error('网络连接错误，请检查网络后重试');
            setIsLoading(false);
            return;
          } else if (isServicePage) {
            // 服务页面特殊处理 - 错误时依然允许加载页面
            setIsLoading(false);
          } else if (errorInfo.isAuthError) {
            // 401错误，清除token并重定向到登录页
            localStorage.removeItem('token');
            localStorage.setItem('redirectAfterLogin', currentPathRef);
            navigate('/login', { replace: true });
            setIsLoading(false);
          } else {
            // 非服务页面，非401错误，清除token并重定向到登录
            localStorage.removeItem('token');
            localStorage.setItem('redirectAfterLogin', currentPathRef);
            navigate('/login', { replace: true });
            setIsLoading(false);
          }
        }
      }
      
      setIsLoading(false);
      authCheckedRef.current = true;
      lastAuthTimeRef.current = Date.now();
      if (process.env.NODE_ENV === 'development') {
        console.timeEnd('[ProtectedRoute] 认证检查');
      }
    };
    
    checkAuth();
    
    return () => {
      mountedRef.current = false;
      window.removeEventListener('error', errorHandler);
    };
  }, [navigate, currentPath, user, setUser, isServicePage, pathCategory]);

  // 如果正在加载，显示加载器
  if (isLoading) {
    console.log(`[ProtectedRoute] 显示加载状态，路径=${currentPath}, 服务页面=${isServicePage}`);
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  // 服务页面始终允许访问，不立即重定向
  // 这允许页面组件自己处理身份验证错误
  if (isServicePage) {
    console.log(`[ProtectedRoute] 服务页面允许进入，类别=${pathCategory}, 路径=${currentPath}, token存在=${!!token}`);
    return <>{children}</>;
  }
  
  // 非服务页面，如果没有token，重定向到登录页
  if (!token) {
    console.log(`[ProtectedRoute] 非服务页面，没有token，重定向到登录页，路径=${currentPath}`);
    return <Navigate to="/login" replace />;
  }

  // 渲染子组件
  console.log(`[ProtectedRoute] 渲染受保护内容，路径=${currentPath}, 类别=${pathCategory}`);
  return <>{children}</>;
};

export default ProtectedRoute; 