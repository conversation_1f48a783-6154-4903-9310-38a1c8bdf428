/**
 * 标签路由管理
 * 提供标签的基本增删改查功能
 */
const express = require('express');
const knex = require('knex')(require('../knexfile').development);
const { auth } = require('../src/middleware/auth');

const router = express.Router();

/**
 * 获取用户标签 - 提供默认标签数据
 * @route GET /user-tags
 * @desc 获取用户创建的标签，返回系统默认标签和用户自定义标签
 * @access 公开
 */
router.get('/user-tags', async (req, res) => {
  try {
    // 记录请求信息
    console.log('处理/tags/user-tags请求', {
      路径: req.path,
      方法: req.method,
      认证头: req.headers.authorization ? '存在' : '不存在'
    });
    
    // 系统默认标签列表 - 始终返回这些标签
    const defaultTags = [
      { id: 'default1', name: '常见症状', category: 'system', createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() },
      { id: 'default2', name: '日常用药', category: 'system', createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() },
      { id: 'default3', name: '随访复查', category: 'system', createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() },
      { id: 'default4', name: '体检', category: 'system', createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() },
      { id: 'default5', name: '专科检查', category: 'system', createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() },
      { id: 'default6', name: '心血管', category: 'system', createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() },
      { id: 'default7', name: '呼吸系统', category: 'system', createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() },
      { id: 'default8', name: '消化系统', category: 'system', createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() },
      { id: 'default9', name: '神经系统', category: 'system', createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() },
      { id: 'default10', name: '骨科', category: 'system', createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() }
    ];
    
    // 尝试从认证信息获取用户ID
    const authHeader = req.headers.authorization;
    let userId = null;
    
    // 如果有认证信息，尝试解析用户ID - 但不强制要求认证
    if (authHeader && authHeader.startsWith('Bearer ')) {
      try {
        // 这里可以添加逻辑来解析token并获取用户ID
        // 但即使失败也不阻止请求继续处理
        console.log('检测到认证请求，但不进行严格验证');
      } catch (authError) {
        console.warn('解析token失败，将只使用默认标签:', authError.message);
      }
    }
    
    // 用户特定标签 - 如果能获取到用户ID
    let userTags = [];
    if (userId) {
      try {
        // 获取用户创建的标签
        const dbUserTags = await knex('tags')
          .select('id', 'name', 'category', 'created_at', 'updated_at')
          .where('created_by', userId)
          .orderBy('name');
        
        console.log(`找到${dbUserTags.length}个用户标签`);
        
        // 转换为驼峰命名
        userTags = dbUserTags.map(tag => ({
          id: tag.id,
          name: tag.name,
          category: tag.category || 'user',
          createdAt: tag.created_at,
          updatedAt: tag.updated_at
        }));
      } catch (dbError) {
        console.error('数据库查询失败:', dbError);
        // 发生错误时，userTags保持为空数组
      }
    }
    
    // 合并默认标签和用户标签并返回
    const allTags = [...defaultTags, ...userTags];
    res.json(allTags);
  } catch (error) {
    console.error('获取标签时发生错误:', error);
    // 即使发生错误，也返回默认标签，确保前端不会崩溃
    const fallbackTags = [
      { id: 'err1', name: '错误恢复标签', category: 'system', createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() }
    ];
    res.json(fallbackTags);
  }
});

/**
 * 获取所有标签
 * @route GET /
 * @desc 获取系统中所有可用的标签
 * @access 公开
 */
router.get('/', async (req, res) => {
  try {
    // 获取所有标签
    const tags = await knex('tags')
      .select('id', 'name', 'category', 'created_at', 'updated_at')
      .orderBy('category')
      .orderBy('name');
    
    // 转换为驼峰命名
    const formattedTags = tags.map(tag => ({
      id: tag.id,
      name: tag.name,
      category: tag.category,
      createdAt: tag.created_at,
      updatedAt: tag.updated_at
    }));
    
    res.json(formattedTags);
  } catch (error) {
    console.error('获取标签失败:', error);
    res.status(500).json({ error: '获取标签失败' });
  }
});

/**
 * 创建用户标签
 * @route POST /create
 * @desc 用户创建自己的标签
 * @access 私有
 */
router.post('/create', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const { name } = req.body;
    
    // 验证必要字段
    if (!name || !name.trim()) {
      return res.status(400).json({ error: '标签名称是必填项' });
    }
    
    // 检查用户是否已有同名标签
    const existingTag = await knex('tags')
      .where({ 
        name: name.trim(),
        created_by: userId
      })
      .first();
    
    if (existingTag) {
      return res.status(409).json({ error: '已存在同名标签' });
    }
    
    // 创建时间
    const now = new Date().toISOString();
    
    // 插入新标签
    const [tagId] = await knex('tags').insert({
      name: name.trim(),
      category: 'user',
      created_by: userId,
      created_at: now,
      updated_at: now
    });
    
    // 获取新创建的标签
    const newTag = await knex('tags')
      .where('id', tagId)
      .first();
    
    res.status(201).json({
      id: newTag.id,
      name: newTag.name,
      category: newTag.category,
      createdAt: newTag.created_at,
      updatedAt: newTag.updated_at
    });
  } catch (error) {
    console.error('创建标签失败:', error);
    res.status(500).json({ error: '创建标签失败' });
  }
});

/**
 * 添加新标签
 * @route POST /
 * @desc 添加新的标签到系统
 * @access 私有（需要管理员权限）
 */
router.post('/', auth, async (req, res) => {
  try {
    // 验证用户是否为管理员
    if (req.user.role !== 'ADMIN') {
      return res.status(403).json({ error: '只有管理员可以添加标签' });
    }
    
    const { name, category } = req.body;
    
    // 验证必要字段
    if (!name || !category) {
      return res.status(400).json({ error: '标签名称和类别是必填项' });
    }
    
    // 检查标签是否已存在
    const existingTag = await knex('tags')
      .where({ name, category })
      .first();
    
    if (existingTag) {
      return res.status(409).json({ error: '相同类别下已存在同名标签' });
    }
    
    // 创建时间
    const now = new Date().toISOString();
    
    // 插入新标签
    const [tagId] = await knex('tags').insert({
      name,
      category,
      created_at: now,
      updated_at: now
    });
    
    // 获取新创建的标签
    const newTag = await knex('tags')
      .where('id', tagId)
      .first();
    
    res.status(201).json({
      id: newTag.id,
      name: newTag.name,
      category: newTag.category,
      createdAt: newTag.created_at,
      updatedAt: newTag.updated_at
    });
  } catch (error) {
    console.error('添加标签失败:', error);
    res.status(500).json({ error: '添加标签失败' });
  }
});

/**
 * 删除标签
 * @route DELETE /:id
 * @desc 删除系统中的标签
 * @access 私有（需要管理员权限）
 */
router.delete('/:id', auth, async (req, res) => {
  try {
    // 验证用户是否为管理员
    if (req.user.role !== 'ADMIN') {
      return res.status(403).json({ error: '只有管理员可以删除标签' });
    }
    
    const { id } = req.params;
    
    // 检查标签是否存在
    const tag = await knex('tags').where('id', id).first();
    
    if (!tag) {
      return res.status(404).json({ error: '标签不存在' });
    }
    
    // 检查标签是否已被使用
    const recordTagCount = await knex('record_tags')
      .where('tag_id', id)
      .count('* as count')
      .first();
    
    if (recordTagCount && recordTagCount.count > 0) {
      return res.status(409).json({ 
        error: '该标签已被使用，不能删除',
        count: recordTagCount.count
      });
    }
    
    // 删除标签
    await knex('tags').where('id', id).delete();
    
    res.json({ message: '标签删除成功' });
  } catch (error) {
    console.error('删除标签失败:', error);
    res.status(500).json({ error: '删除标签失败' });
  }
});

module.exports = router; 