import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate, Link } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Button,
  Divider,
  CircularProgress,
  Alert,
  Chip,
  IconButton,
  Tooltip,
  Snackbar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  useTheme,
  useMediaQuery,
  Modal
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import BookmarkIcon from '@mui/icons-material/Bookmark';
import LockIcon from '@mui/icons-material/Lock';
import DownloadIcon from '@mui/icons-material/Download';
import AttachmentIcon from '@mui/icons-material/Attachment';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import ImageIcon from '@mui/icons-material/Image';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import DescriptionIcon from '@mui/icons-material/Description';
import LabelIcon from '@mui/icons-material/Label';
import PersonIcon from '@mui/icons-material/Person';
import MedicalServicesIcon from '@mui/icons-material/MedicalServices';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import CloseIcon from '@mui/icons-material/Close';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { useQuery } from '@tanstack/react-query';
import { getRecord, getAttachments } from '../services/recordService';
import { getPatient } from '../services/patientService';
import { getDisease } from '../services/diseaseService';
import { useAuthStore } from '../store/authStore';
import apiClient from '../services/apiClient';

// 记录类型颜色映射
const PERSONAL_TAG_COLOR = '#3498db';
const PERSONAL_TAG_LIGHT_COLOR = `${PERSONAL_TAG_COLOR}20`; // 20%透明度的背景色，增加可见度

// 记录类型颜色映射 - 这里使用简化版本，实际应从共享文件导入
const recordTypeColors: Record<string, {main: string, light: string, dark: string}> = {
  // 诊断相关 - 蓝色系
  'DIAGNOSIS': {
    main: '#1976d2',  // 蓝色
    light: '#e3f2fd', 
    dark: '#0d47a1'
  },
  // 治疗相关 - 绿色系
  'TREATMENT': {
    main: '#388e3c',  // 绿色
    light: '#e8f5e9',
    dark: '#1b5e20'
  },
  // 药物相关 - 橙色系
  'MEDICATION': {
    main: '#f57c00',  // 橙色
    light: '#fff3e0',
    dark: '#e65100'
  },
  // 检查与检验 - 紫色系
  'EXAMINATION': {
    main: '#7b1fa2',  // 紫色
    light: '#f3e5f5',
    dark: '#4a148c'
  },
  'LAB_TEST': {
    main: '#9c27b0',  // 浅紫色
    light: '#f3e5f5',
    dark: '#6a1b9a'
  },
  // 随访与复查 - 青色系
  'FOLLOW_UP': {
    main: '#0097a7',  // 青色
    light: '#e0f7fa',
    dark: '#006064'
  },
  // 症状相关 - 红色系
  'SYMPTOM': {
    main: '#d32f2f',  // 红色
    light: '#ffebee',
    dark: '#b71c1c'
  },
  // 其他记录类型 - 灰色系
  'NOTE': {
    main: '#546e7a',  // 灰蓝色
    light: '#eceff1',
    dark: '#263238'
  },
  'OTHER': {
    main: '#455a64',  // 深灰蓝色
    light: '#cfd8dc',
    dark: '#263238'
  }
};

// 严重程度颜色映射
const severityMainColors: Record<string, string> = {
  'MILD': '#4caf50',       // 绿色
  'MODERATE': '#fdd835',   // 黄色
  'SEVERE': '#ff9800',     // 橙色
  'CRITICAL': '#f44336',   // 红色
  // 数字映射
  '1': '#4caf50',          // MILD - 绿色
  '2': '#fdd835',          // MODERATE - 黄色
  '3': '#ff9800',          // SEVERE - 橙色
  '4': '#f44336'           // CRITICAL - 红色
};

// 记录详情页面组件
const RecordDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { token /*, user*/ } = useAuthStore((state) => state);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  // 如果ID是'new'，重定向到创建记录页面
  useEffect(() => {
    if (id === 'new') {
      navigate('/records/new', { replace: true });
    }
  }, [id, navigate]);
  
  // 通知状态
  const [notification, setNotification] = useState({ 
    open: false, 
    message: '', 
    type: 'success' as 'success' | 'error' | 'info' | 'warning' 
  });
  
  // 图片预览状态
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewUrl, setPreviewUrl] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');
  
  // 获取记录详情
  const { 
    data: record, 
    isLoading: isLoadingRecord, 
    error: recordError 
  } = useQuery({
    queryKey: ['record', id],
    queryFn: () => getRecord(id || '', { 
      include_all_users: true,
      include_deleted: true,
      service_context: true
    }),
    enabled: !!id && !!token && id !== 'new',
  });
  
  // 获取记录附件
  const { 
    data: attachments, 
  } = useQuery({
    queryKey: ['recordAttachments', id],
    queryFn: () => getAttachments(id || '', { 
      include_all_users: true,
      include_deleted: true,
      service_context: true
    }),
    enabled: !!id && !!token && id !== 'new',
  });
  
  // 获取患者详情
  const {
    data: patientData,
    isLoading: isLoadingPatient
  } = useQuery({
    queryKey: ['patient', record?.patientId],
    queryFn: () => getPatient(record?.patientId || ''),
    enabled: !!record?.patientId && !!token,
  });
  
  // 获取病理详情
  const {
    data: diseaseData,
    isLoading: isLoadingDisease
  } = useQuery({
    queryKey: ['disease', record?.diseaseId],
    queryFn: () => getDisease(record?.diseaseId || '', { includeDeleted: true }),
    enabled: !!record?.diseaseId && !!token,
  });
  
  // 记录类型名称映射
  const recordTypeNames: Record<string, string> = {
    // 诊断相关
    'DIAGNOSIS': '诊断',
    'DIFFERENTIAL_DIAGNOSIS': '鉴别诊断',
    'DIAGNOSIS_CONFIRMATION': '确诊',
    
    // 治疗相关
    'TREATMENT': '治疗',
    'TREATMENT_PLAN': '治疗方案',
    'TREATMENT_ADJUSTMENT': '治疗调整',
    
    // 药物相关
    'MEDICATION': '用药',
    'PRESCRIPTION': '处方',
    'MEDICATION_ADJUSTMENT': '药物调整',
    
    // 检查与检验
    'EXAMINATION': '检查',
    'LAB_TEST': '化验',
    'IMAGING': '影像',
    
    // 随访与复查
    'FOLLOW_UP': '随访',
    'ASSESSMENT': '评估',
    'REVIEW': '复查',
    
    // 就诊相关
    'VISIT': '就诊',
    'CONSULTATION': '会诊',
    'REFERRAL': '转诊',
    
    // 症状相关
    'SYMPTOM': '症状',
    'CHIEF_COMPLAINT': '主诉',
    'CONDITION_CHANGE': '病情变化',
    
    // 其他类型
    'NOTE': '备注',
    'COMMENT': '评论',
    'OTHER': '其他'
  };
  
  // 阶段/节点名称映射
  const stagePhaseMap: Record<string, string> = {
    'INITIAL': '初诊阶段',
    'DIAGNOSIS': '确诊阶段',
    'TREATMENT': '治疗阶段',
    'RECOVERY': '康复阶段',
    'PROGNOSIS': '预后阶段'
  };
  
  const stageNodeMap: Record<string, string> = {
    'INITIAL_VISIT': '初诊',
    'DIAGNOSIS': '确诊',
    'TREATMENT': '治疗',
    'FOLLOW_UP': '随访',
    'PROGNOSIS': '预后',
    'ARCHIVE': '封档'
  };
  
  // 严重程度名称映射
  const severityNames: Record<string, string> = {
    'MILD': '轻微',
    'MODERATE': '中等',
    'SEVERE': '严重',
    'CRITICAL': '危重',
    '1': '轻微',
    '2': '中等',
    '3': '严重',
    '4': '危重',
    // 添加更多可能的映射
    'mild': '轻微',
    'moderate': '中等',
    'severe': '严重',
    'critical': '危重',
    '轻': '轻微',
    '中': '中等',
    '重': '严重',
    '危重': '危重',
    'LOW': '轻微',
    'MEDIUM': '中等',
    'HIGH': '严重',
    'URGENT': '危重'
  };
  
  // 性别映射
  const genderMap: Record<string, string> = {
    'MALE': '男',
    'FEMALE': '女',
    'OTHER': '其他',
    'UNKNOWN': '未知',
    // 兼容英文值
    'male': '男',
    'female': '女',
    'other': '其他',
    'unknown': '未知',
    // 兼容中文值
    '男': '男',
    '女': '女'
  };
  
  // 病理阶段映射
  const diseaseStageMap: Record<string, string> = {
    'INITIAL': '初期',
    'EARLY': '早期',
    'MIDDLE': '中期',
    'ADVANCED': '晚期',
    'TERMINAL': '终末期',
    'REMISSION': '缓解期',
    'RECOVERY': '康复期',
    'OBSERVATION': '观察期',
    // 数字映射
    '1': '一期',
    '2': '二期',
    '3': '三期',
    '4': '四期',
    // 兼容罗马数字
    'I': '一期',
    'II': '二期',
    'III': '三期',
    'IV': '四期',
    // 兼容中文值，直接返回
    '初期': '初期',
    '早期': '早期',
    '中期': '中期',
    '晚期': '晚期'
  };
  
  // 解析记录类型数组
  const parseRecordType = (type: any): string[] => {
    if (Array.isArray(type)) {
      return type;
    }
    
    if (typeof type === 'string') {
      try {
        if (type.startsWith('[') && type.endsWith(']')) {
          return JSON.parse(type);
        }
        return [type];
      } catch (e) {
        return [type];
      }
    }
    
    return [];
  };
  
  // 获取记录类型名称
  const getRecordTypeName = (type: string): string => {
    return recordTypeNames[type] || type;
  };
  
  // 获取阶段/节点名称
  const getStageName = (stageNode?: string, stagePhase?: string): string => {
    if (stageNode && stageNode.trim() !== '') {
      return stageNodeMap[stageNode] || stageNode;
    }
    if (stagePhase && stagePhase.trim() !== '') {
      return stagePhaseMap[stagePhase] || stagePhase;
    }
    return '未指定';
  };
  
  // 获取严重程度名称
  const getSeverityName = (severity: string | number): string => {
    // 确保有值
    if (severity === null || severity === undefined) {
      return '未知';
    }
    
    // 如果是数字，先转为字符串
    const severityStr = severity.toString();
    
    // 先尝试直接从映射中获取
    if (severityNames[severityStr]) {
      return severityNames[severityStr];
    }
    
    // 尝试转换数字
    if (!isNaN(Number(severityStr))) {
      const num = parseInt(severityStr, 10);
      switch (num) {
        case 1: return '轻微';
        case 2: return '中等';
        case 3: return '严重';
        case 4: return '危重';
        default: return `未知(${severityStr})`;
      }
    }
    
    // 返回原值或默认值
    return severityStr || '未知';
  };
  
  // 获取记录类型颜色
  const getRecordTypeColor = (type: string): {main: string, light: string, dark: string} => {
    return recordTypeColors[type] || recordTypeColors['OTHER'];
  };
  
  // 获取严重程度颜色
  const getSeverityColor = (severity: string | number): string => {
    // 如果是空值，返回默认颜色
    if (severity === null || severity === undefined) {
      return '#607d8b';
    }
    
    // 如果是数字，先转为字符串
    const severityStr = severity.toString();
    
    // 先尝试直接从映射中获取
    if (severityMainColors[severityStr]) {
      return severityMainColors[severityStr];
    }
    
    // 尝试转换数字
    if (!isNaN(Number(severityStr))) {
      const num = parseInt(severityStr, 10);
      switch (num) {
        case 1: return '#4caf50'; // 绿色 - 轻微
        case 2: return '#fdd835'; // 黄色 - 中等 
        case 3: return '#ff9800'; // 橙色 - 严重
        case 4: return '#f44336'; // 红色 - 危重
        default: return '#607d8b'; // 默认灰色
      }
    }
    
    // 返回默认颜色
    return '#607d8b';
  };
  
  // 格式化日期时间
  const formatDateTime = (dateTime: string): string => {
    try {
      return format(new Date(dateTime), 'yyyy年MM月dd日 HH:mm', { locale: zhCN });
    } catch (error) {
      return '无效日期';
    }
  };
  
  // 格式化文件大小显示
  const formatFileSize = (bytes: number | undefined): string => {
    if (bytes === undefined || bytes === null) return '未知大小';
    
    if (bytes < 1024) {
      return `${bytes} B`;
    } else if (bytes < 1024 * 1024) {
      return `${(bytes / 1024).toFixed(1)} KB`;
    } else {
      return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
    }
  };
  
  // 获取附件图标
  const getAttachmentIcon = (fileName: string | undefined) => {
    // 确保fileName存在
    if (!fileName) {
      return <InsertDriveFileIcon />;
    }
    
    const extension = fileName.split('.').pop()?.toLowerCase() || '';
    
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension)) {
      return <ImageIcon />;
    } else if (extension === 'pdf') {
      return <PictureAsPdfIcon />;
    } else if (['doc', 'docx', 'txt', 'rtf', 'odt'].includes(extension)) {
      return <DescriptionIcon />;
    } else {
      return <InsertDriveFileIcon />;
    }
  };

  // 下载附件
  const downloadAttachment = async (attachmentId: string, fileName: string) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('未找到身份验证令牌，请重新登录');
      }

      // 获取已配置的后端地址
      const backendUrl = localStorage.getItem('backendServerIP') || apiClient.defaults.baseURL || 'http://localhost:3001';
      
      // 构建完整的URL
      const downloadUrl = `${backendUrl}/records/attachments/download/${attachmentId}?include_all_users=true&include_deleted=true`;
      
      console.log(`开始下载附件: ${downloadUrl}`);
      
      // 使用XMLHttpRequest代替fetch以更好地处理二进制数据和下载进度
      const xhr = new XMLHttpRequest();
      xhr.open('GET', downloadUrl, true);
      xhr.responseType = 'blob';
      xhr.setRequestHeader('Authorization', `Bearer ${token}`);
      
      // 确保发送凭证 (cookies, authorization headers 等)
      xhr.withCredentials = true;
      
      // 添加超时设置
      xhr.timeout = 30000; // 30秒超时
      
      // 超时处理
      xhr.ontimeout = function() {
        console.error('下载附件请求超时');
        throw new Error('下载超时，请稍后重试');
      };
      
      // 添加进度事件处理
      xhr.onprogress = function(event) {
        if (event.lengthComputable) {
          const percentComplete = Math.round((event.loaded / event.total) * 100);
          console.log(`下载进度: ${percentComplete}%`);
        }
      };
      
      // 添加完成事件处理
      xhr.onload = function() {
        if (xhr.status === 200) {
          // 检查响应类型
          console.log(`响应类型: ${xhr.response?.type || '未知'}`);
          
          // 创建一个Blob URL并触发下载
          const blob = xhr.response;
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', fileName);
          document.body.appendChild(link);
          link.click();
          
          // 清理
          setTimeout(() => {
            window.URL.revokeObjectURL(url);
            document.body.removeChild(link);
          }, 100);
        } else {
          // 尝试读取错误信息
          const reader = new FileReader();
          reader.onload = function() {
            let errorMsg = '未知错误';
            try {
              const response = JSON.parse(reader.result as string);
              errorMsg = response.error || errorMsg;
            } catch (e) {
              errorMsg = `下载失败: HTTP状态 ${xhr.status}`;
            }
            console.error('下载失败:', errorMsg);
            throw new Error(errorMsg);
          };
          reader.onerror = function() {
            throw new Error(`下载失败: HTTP状态 ${xhr.status}`);
          };
          reader.readAsText(xhr.response);
        }
      };
      
      // 错误处理
      xhr.onerror = function() {
        console.error('XHR错误:', xhr.statusText);
        // 检查是否是由广告拦截器导致的错误
        const errorMsg = (xhr.status === 0) 
          ? `网络错误，无法下载文件，可能是由广告拦截器或浏览器扩展导致`
          : `网络错误，无法下载文件，可能是CORS或服务器问题`;
        throw new Error(errorMsg);
      };
      
      // 开始下载
      xhr.send();
    } catch (error) {
      console.error('下载附件失败:', error);
      setNotification({
        open: true,
        message: `下载附件失败: ${error instanceof Error ? error.message : '未知错误'}`,
        type: 'error'
      });
    }
  };
  
  // 关闭通知
  const handleCloseNotification = () => {
    setNotification({
      ...notification,
      open: false
    });
  };
  
  // 获取性别中文名称
  const getGenderName = (gender: string): string => {
    return genderMap[gender] || gender || '未知';
  };
  
  // 获取病理阶段中文名称
  const getDiseaseStage = (stage: string): string => {
    return diseaseStageMap[stage] || stage || '未指定';
  };
  
  // 判断附件是否可预览
  const isPreviewable = (attachment: any): boolean => {
    // 检查文件类型是否为图片
    const isImage = attachment.file_type?.startsWith('image/') || 
                   attachment.fileType?.startsWith('image/');
    
    // 检查文件大小是否小于500KB (500 * 1024 = 512000 字节)
    const fileSize = attachment.file_size || attachment.fileSize || 0;
    const isSizeAllowed = fileSize <= 512000;
    
    return isImage && isSizeAllowed;
  };
  
  // 打开预览
  const openPreview = async (attachmentId: string, fileName: string) => {
    try {
      setPreviewTitle(fileName);
      
      // 获取已配置的后端地址
      const backendUrl = localStorage.getItem('backendServerIP') || apiClient.defaults.baseURL || 'http://localhost:3001';
      const downloadUrl = `${backendUrl}/records/attachments/download/${attachmentId}?include_all_users=true&include_deleted=true`;
      
      console.log(`开始预览图片: ${downloadUrl}`);
      
      // 获取token
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('未找到身份验证令牌，请重新登录');
      }
      
      // 使用XMLHttpRequest代替axios，保持与下载函数相同的处理方式
      const xhr = new XMLHttpRequest();
      xhr.open('GET', downloadUrl, true);
      xhr.responseType = 'blob';
      xhr.setRequestHeader('Authorization', `Bearer ${token}`);
      xhr.withCredentials = true;
      
      // 添加超时设置
      xhr.timeout = 30000; // 30秒超时
      
      // 错误处理
      xhr.onerror = function() {
        console.error('预览图片加载失败:', xhr.statusText);
        const errorMsg = (xhr.status === 0) 
          ? `网络错误，无法加载图片，可能是由广告拦截器或浏览器扩展导致`
          : `网络错误，无法加载图片，可能是CORS或服务器问题`;
        throw new Error(errorMsg);
      };
      
      // 超时处理
      xhr.ontimeout = function() {
        console.error('预览图片请求超时');
        throw new Error('加载超时，请稍后重试');
      };
      
      // 完成处理
      xhr.onload = function() {
        if (xhr.status === 200) {
          // 创建Blob URL
          const url = window.URL.createObjectURL(xhr.response);
          setPreviewUrl(url);
          setPreviewOpen(true);
        } else {
          throw new Error(`加载图片失败: HTTP状态 ${xhr.status}`);
        }
      };
      
      // 开始请求
      xhr.send();
    } catch (error) {
      console.error('预览图片失败:', error);
      setNotification({
        open: true,
        message: `预览图片失败: ${error instanceof Error ? error.message : '未知错误'}`,
        type: 'error'
      });
    }
  };
  
  // 关闭预览
  const closePreview = () => {
    // 释放Blob URL
    if (previewUrl) {
      window.URL.revokeObjectURL(previewUrl);
    }
    setPreviewUrl('');
    setPreviewOpen(false);
  };
  
  // 加载中显示
  if (isLoadingRecord) {
    return (
      <Box sx={{ 
        display: 'flex', 
        flexDirection: 'column',
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '50vh',
        p: 2
      }}>
        <CircularProgress size={isMobile ? 40 : 60} thickness={4} />
        <Typography 
          variant="body1" 
          sx={{ 
            mt: 2, 
            color: 'text.secondary',
            fontSize: { xs: '0.8rem', sm: '0.9rem' }
          }}
        >
          正在加载记录详情...
        </Typography>
      </Box>
    );
  }
  
  // 错误显示
  if (recordError) {
    const errorMessage = ((recordError as any)?.message || '未知错误').toString();
    const isDbColumnError = errorMessage.includes('no such column: deletedAt');
    
    return (
      <Box sx={{ 
        m: { xs: 1.5, sm: 2 },
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-start'
      }}>
        <Alert 
          severity="error" 
          variant={isMobile ? "standard" : "outlined"}
          sx={{ 
            mb: 2,
            width: '100%',
            fontSize: { xs: '0.75rem', sm: '0.8rem' }
          }}
        >
          {isDbColumnError ? 
            '数据库列名错误: 系统查询使用了驼峰命名(deletedAt)，但数据库使用下划线命名(deleted_at)。请联系管理员修复后端代码。' : 
            `加载记录失败: ${errorMessage}`
          }
        </Alert>
        <Button 
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/records/manage')}
          variant="outlined"
          size={isMobile ? "medium" : "large"}
          fullWidth={isMobile}
        >
          返回记录列表
        </Button>
      </Box>
    );
  }
  
  // 记录不存在
  if (!record) {
    return (
      <Box sx={{ 
        m: { xs: 1.5, sm: 2 },
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-start'
      }}>
        <Alert 
          severity="warning" 
          variant={isMobile ? "standard" : "outlined"}
          sx={{ 
            mb: 2,
            width: '100%',
            fontSize: { xs: '0.75rem', sm: '0.8rem' }
          }}
        >
          记录不存在或已被删除
        </Alert>
        <Button 
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/records/manage')}
          variant="outlined"
          size={isMobile ? "medium" : "large"}
          fullWidth={isMobile}
        >
          返回记录列表
        </Button>
      </Box>
    );
  }
  
  // 解析记录类型
  const recordTypeArray = parseRecordType(record.recordType || record.primaryType || 'NOTE');
  const primaryType = record.primaryType || (recordTypeArray.length > 0 ? recordTypeArray[0] : 'NOTE');
  
  // 获取主要记录类型的颜色
  const primaryTypeColor = getRecordTypeColor(primaryType);
  
  return (
    <Box sx={{ m: 0, width: '100%' }}>
      {/* 页面标题和操作按钮 */}
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'flex-end', 
        justifyContent: 'space-between', 
        mb: 0,
        mt: '20px',
        px: '10px'
      }}>
        <Typography 
          variant="h5" 
          component="h1" 
          sx={{ 
            fontWeight: 500,
            fontSize: { xs: '1.1rem', sm: '1.3rem' },
            mb: 0
          }}
        >
          记录详情
        </Typography>
        
        <Box sx={{ 
          display: 'flex', 
          gap: 1
        }}>
          <Button 
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/records/manage')}
            variant="outlined"
            size={isMobile ? "small" : "medium"}
            sx={{ 
              fontSize: { xs: '0.75rem', sm: '0.75rem' },
              mb: '3px'
            }}
          >
            返回
          </Button>
          
          <Button 
            startIcon={<EditIcon />}
            component={Link}
            to={`/records/${id}/edit`}
            variant="contained"
            color="primary"
            size={isMobile ? "small" : "medium"}
            sx={{ 
              fontSize: { xs: '0.75rem', sm: '0.75rem' },
              mb: '3px'
            }}
          >
            编辑
          </Button>
        </Box>
      </Box>
      <Divider sx={{ mb: { xs: 2, md: 3 }, mx: '10px' }} />
      
      {/* 记录详情卡片 */}
      <Paper
        elevation={0}
        sx={{ 
          mb: 3,
          mx: '10px',
          border: '1px solid #e0e0e0',
          borderRadius: { xs: 1, sm: 2 },
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        {/* 左侧色条 */}
        <Box sx={{
          position: 'absolute',
          left: 0,
          top: 0,
          bottom: 0,
          width: { xs: '4px', sm: '6px' },
          backgroundColor: primaryTypeColor.main
        }} />
        
        {/* 记录内容区域 */}
        <Box sx={{ 
          p: { xs: 1.5, sm: 2, md: 3 }, 
          pl: { xs: 2.5, sm: 3, md: 4 },
          '& .MuiTypography-root': { fontSize: { xs: '0.875rem', sm: '0.875rem' } }
        }}>
          {/* 记录标题及属性行 */}
          <Box sx={{ 
            display: 'flex', 
            flexDirection: { xs: 'column', sm: 'row' },
            justifyContent: 'space-between',
            alignItems: { xs: 'flex-start', sm: 'center' },
            mb: { xs: 1.5, sm: 2 } 
          }}>
            <Box sx={{ 
              display: 'flex', 
              alignItems: 'flex-start',
              flexWrap: 'wrap', 
              mb: { xs: 1, sm: 0 } 
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mr: 1, mb: 0.5 }}>
                {record.isImportant === true && (
                  <Tooltip title="重要记录">
                    <BookmarkIcon color="error" sx={{ mr: 0.5, fontSize: { xs: '1.2rem', sm: '1.5rem' } }} />
                  </Tooltip>
                )}
                {record.isPrivate === true && (
                  <Tooltip title="隐私记录">
                    <LockIcon color="primary" sx={{ mr: 0.5, fontSize: { xs: '1.2rem', sm: '1.5rem' } }} />
                  </Tooltip>
                )}
              </Box>
              <Typography variant="h6" component="h2" sx={{ 
                fontWeight: 600,
                fontSize: { xs: '1.0rem', sm: '1.15rem' },
                wordBreak: 'break-word'
              }}>
                {record.title}
              </Typography>
            </Box>
            
            <Typography 
              variant="body2" 
              color="text.secondary"
              sx={{ 
                display: 'flex',
                alignItems: 'center',
                flexShrink: 0,
                fontSize: { xs: '0.65rem', sm: '0.775rem' }
              }}
            >
              {formatDateTime(record.recordDate)}
            </Typography>
          </Box>
          
          {/* 标签行 */}
          <Box sx={{ 
            display: 'flex', 
            flexWrap: 'wrap', 
            gap: { xs: 0.5, sm: 1 }, 
            mb: { xs: 2, sm: 3 } 
          }}>
            {/* 记录类型标签 */}
            {recordTypeArray.map((type, index) => (
              <Chip
                key={`type-${index}`}
                label={getRecordTypeName(type)}
                size="small"
                sx={{
                  bgcolor: type === primaryType ? primaryTypeColor.light : 'transparent',
                  color: primaryTypeColor.dark,
                  border: `1px solid ${primaryTypeColor.main}`,
                  fontWeight: type === primaryType ? 600 : 400,
                  fontSize: { xs: '0.6rem', sm: '0.65rem' },
                  height: { xs: '22px', sm: '30px' },
                  mb: 0.5
                }}
              />
            ))}
            
            {/* 阶段/节点标签 */}
            {(record.stageNode || record.stagePhase) && (
              <Chip
                label={getStageName(record.stageNode, record.stagePhase)}
                size="small"
                color="primary"
                variant="outlined"
                sx={{
                  fontSize: { xs: '0.6rem', sm: '0.65rem' },
                  height: { xs: '22px', sm: '30px' },
                  mb: 0.5
                }}
              />
            )}
            
            {/* 严重程度标签 */}
            {record.severity && (
              <Chip
                label={getSeverityName(record.severity)}
                size="small"
                sx={{
                  bgcolor: `${getSeverityColor(record.severity)}20`,
                  color: getSeverityColor(record.severity),
                  border: `1px solid ${getSeverityColor(record.severity)}`,
                  fontWeight: 500,
                  fontSize: { xs: '0.6rem', sm: '0.65rem' },
                  height: { xs: '22px', sm: '30px' },
                  mb: 0.5
                }}
              />
            )}
            
            {/* 自定义标签 */}
            {record.customTags && record.customTags.split(',').map((tag: string, index: number) => {
              const tagText = tag.trim();
              if (!tagText) return null;
              
              return (
                <Chip
                  key={`custom-${index}`}
                  label={tagText}
                  size="small"
                  icon={<LabelIcon fontSize="small" />}
                  variant="outlined"
                  sx={{
                    bgcolor: PERSONAL_TAG_LIGHT_COLOR,
                    color: PERSONAL_TAG_COLOR,
                    border: `1px solid ${PERSONAL_TAG_COLOR}`,
                    fontWeight: 500,
                    fontSize: { xs: '0.6rem', sm: '0.65rem' },
                    height: { xs: '22px', sm: '30px' },
                    mb: 0.5,
                    '& .MuiChip-icon': {
                      color: PERSONAL_TAG_COLOR,
                      marginRight: '2px',
                      fontSize: { xs: '0.7rem', sm: '0.75rem' }
                    }
                  }}
                />
              );
            })}
          </Box>
          
          {/* 关联信息 */}
          <Box sx={{ 
            display: 'flex', 
            flexDirection: 'column',
            mb: { xs: 2, sm: 3 }
          }}>
            <Typography 
              variant="subtitle1" 
              sx={{ 
                fontSize: { xs: '0.85rem', sm: '0.95rem' },
                fontWeight: 500,
                mb: 1,
                display: 'flex',
                alignItems: 'center'
              }}
            >
              <Box 
                component="span" 
                sx={{ 
                  width: 4, 
                  height: 16, 
                  backgroundColor: 'primary.main', 
                  mr: 1,
                  display: 'inline-block',
                  borderRadius: 4
                }} 
              />
              关联信息
            </Typography>
            
            <Box sx={{ 
              display: 'flex', 
              flexDirection: { xs: 'column', sm: 'row' }, 
              gap: { xs: 1.5, sm: 2 },
              backgroundColor: 'background.paper',
              borderRadius: { xs: 1, sm: 2 },
              p: { xs: 1, sm: 1.5 },
              border: '1px solid #e0e0e0'
            }}>
              <Box sx={{ 
                width: { xs: '100%', sm: '50%' } 
              }}>
                <Box sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  mb: 0.5 
                }}>
                  <PersonIcon sx={{ 
                    color: 'primary.main', 
                    mr: 1, 
                    fontSize: { xs: '1.0rem', sm: '1.1rem' } 
                  }} />
                  <Typography variant="subtitle2" sx={{ 
                    fontSize: { xs: '0.75rem', sm: '0.8rem' },
                    fontWeight: 600,
                    color: 'primary.main'
                  }}>
                    患者信息
                  </Typography>
                  
                  {isLoadingPatient && (
                    <CircularProgress size={16} sx={{ ml: 1, color: 'primary.light' }} />
                  )}
                </Box>
                
                {patientData ? (
                  <Box sx={{ ml: 2 }}>
                    <Typography 
                      variant="body1" 
                      component="div"
                      sx={{ 
                        fontSize: { xs: '0.8rem', sm: '0.9rem' },
                        fontWeight: 600,
                        mb: 0.5,
                        display: 'flex',
                        alignItems: 'center'
                      }}
                    >
                      {patientData.name}
                      {patientData.isPrimary && (
                        <Chip 
                          label="本人" 
                          size="small" 
                          color="primary"
                          variant="outlined"
                          sx={{ 
                            ml: 1, 
                            height: 20, 
                            fontSize: '0.6rem' 
                          }} 
                        />
                      )}
                    </Typography>
                    
                    <Typography variant="body2" sx={{ 
                      fontSize: { xs: '0.7rem', sm: '0.75rem' },
                      color: 'text.secondary',
                      display: 'flex',
                      flexDirection: 'column',
                      gap: 0.5
                    }}>
                      <span>性别: {getGenderName(patientData.gender)}</span>
                      <span>联系方式: {patientData.phoneNumber || '未填写'}</span>
                      {patientData.birthDate && (
                        <span>出生日期: {format(new Date(patientData.birthDate), 'yyyy-MM-dd')}</span>
                      )}
                    </Typography>
                  </Box>
                ) : (
                  <Typography 
                    variant="body1" 
                    sx={{ 
                      fontSize: { xs: '0.8rem', sm: '0.9rem' },
                      fontWeight: 500,
                      color: 'text.secondary',
                      ml: 2
                    }}
                  >
                    {record.patientName || '未关联患者'}
                    {record.patientId && !isLoadingPatient && (
                      <Typography variant="caption" sx={{ 
                        display: 'block', 
                        color: 'text.secondary' 
                      }}>
                        ID: {record.patientId}
                      </Typography>
                    )}
                  </Typography>
                )}
              </Box>
              
              <Box sx={{ 
                width: { xs: '100%', sm: '50%' } 
              }}>
                <Box sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  mb: 0.5 
                }}>
                  <MedicalServicesIcon sx={{ 
                    color: 'secondary.main', 
                    mr: 1, 
                    fontSize: { xs: '1.0rem', sm: '1.1rem' } 
                  }} />
                  <Typography variant="subtitle2" sx={{ 
                    fontSize: { xs: '0.75rem', sm: '0.8rem' },
                    fontWeight: 600,
                    color: 'secondary.main'
                  }}>
                    病理信息
                  </Typography>
                  
                  {isLoadingDisease && (
                    <CircularProgress size={16} sx={{ ml: 1, color: 'secondary.light' }} />
                  )}
                </Box>
                
                {diseaseData ? (
                  <Box sx={{ ml: 2 }}>
                    <Typography 
                      variant="body1" 
                      component="div"
                      sx={{ 
                        fontSize: { xs: '0.8rem', sm: '0.9rem' },
                        fontWeight: 600,
                        mb: 0.5
                      }}
                    >
                      {diseaseData.name}
                    </Typography>
                    
                    <Typography variant="body2" sx={{ 
                      fontSize: { xs: '0.7rem', sm: '0.75rem' },
                      color: 'text.secondary',
                      display: 'flex',
                      flexDirection: 'column',
                      gap: 0.5
                    }}>
                      <span>阶段: {getDiseaseStage(diseaseData.stage)}</span>
                      {diseaseData.severity && (
                        <span>严重程度: 
                          <Box component="span" sx={{ 
                            color: getSeverityColor(diseaseData.severity),
                            fontWeight: 500,
                            ml: 0.5
                          }}>
                            {getSeverityName(diseaseData.severity)}
                          </Box>
                        </span>
                      )}
                      {diseaseData.diagnosisDate && (
                        <span>确诊日期: {format(new Date(diseaseData.diagnosisDate), 'yyyy-MM-dd')}</span>
                      )}
                      {diseaseData.isPrivate === 1 ? (
                        <Box 
                          component="span" 
                          sx={{ 
                            display: 'flex', 
                            alignItems: 'center' 
                          }}
                        >
                          <LockIcon sx={{ fontSize: '0.7rem', mr: 0.5 }} />
                          <span>隐私病理</span>
                        </Box>
                      ) : null}
                    </Typography>
                  </Box>
                ) : (
                  <Typography 
                    variant="body1" 
                    sx={{ 
                      fontSize: { xs: '0.8rem', sm: '0.9rem' },
                      fontWeight: 500,
                      color: 'text.secondary',
                      ml: 2
                    }}
                  >
                    {record.diseaseName || '未关联病理'}
                    {record.diseaseId && !isLoadingDisease && (
                      <Typography variant="caption" sx={{ 
                        display: 'block', 
                        color: 'text.secondary' 
                      }}>
                        ID: {record.diseaseId}
                      </Typography>
                    )}
                  </Typography>
                )}
              </Box>
            </Box>
          </Box>
          
          {/* 记录内容 */}
          <Box sx={{ mb: { xs: 2, sm: 3 } }}>
            <Typography 
              variant="subtitle1" 
              sx={{ 
                fontSize: { xs: '0.85rem', sm: '0.95rem' },
                fontWeight: 500,
                mb: 1,
                display: 'flex',
                alignItems: 'center'
              }}
            >
              <Box 
                component="span" 
                sx={{ 
                  width: 4, 
                  height: 16, 
                  backgroundColor: primaryTypeColor.main, 
                  mr: 1,
                  display: 'inline-block',
                  borderRadius: 4
                }} 
              />
              记录内容
            </Typography>
            
            <Typography 
              variant="body1" 
              component="div" 
              sx={{ 
                whiteSpace: 'pre-line',
                overflowWrap: 'break-word',
                fontSize: { xs: '0.8rem', sm: '0.9rem' },
                lineHeight: { xs: 1.5, sm: 1.6 },
                backgroundColor: 'background.paper',
                borderRadius: { xs: 1, sm: 2 },
                p: { xs: 1.5, sm: 2 },
                border: '1px solid #e0e0e0'
              }}
            >
              {record.content || '无内容'}
            </Typography>
          </Box>
          
          {/* 附件区域 */}
          {attachments && attachments.length > 0 && (
            <Box sx={{ mb: { xs: 2, sm: 3 } }}>
              <Typography 
                variant="subtitle1" 
                sx={{ 
                  fontSize: { xs: '0.85rem', sm: '0.95rem' },
                  fontWeight: 500,
                  mb: 1,
                  display: 'flex',
                  alignItems: 'center'
                }}
              >
                <Box 
                  component="span" 
                  sx={{ 
                    width: 4, 
                    height: 16, 
                    backgroundColor: 'info.main', 
                    mr: 1,
                    display: 'inline-block',
                    borderRadius: 4
                  }} 
                />
                <AttachmentIcon sx={{ mr: 1, fontSize: { xs: '0.9rem', sm: '1.0rem' }, color: 'text.secondary' }} />
                附件 ({attachments.length})
              </Typography>
              
              <List sx={{ 
                bgcolor: 'background.paper',
                border: '1px solid #e0e0e0',
                borderRadius: { xs: 1, sm: 2 },
                overflow: 'hidden',
                '& .MuiListItem-root': {
                  pl: { xs: 1, sm: 2 },
                  pr: { xs: 2, sm: 3 }
                },
                '& .MuiListItemButton-root': {
                  py: { xs: 0.75, sm: 1 }
                },
                '& .MuiListItemIcon-root': {
                  minWidth: { xs: 36, sm: 48 }
                },
                '& .MuiListItemText-primary': {
                  fontSize: { xs: '0.75rem', sm: '0.85rem' },
                  fontWeight: 500
                },
                '& .MuiListItemText-secondary': {
                  fontSize: { xs: '0.65rem', sm: '0.7rem' }
                }
              }}>
                {attachments.map((attachment: any) => (
                  <ListItem
                    key={attachment.id}
                    disablePadding
                    secondaryAction={
                      <Box sx={{ display: 'flex' }}>
                        {isPreviewable(attachment) && (
                          <Tooltip title="预览">
                            <IconButton 
                              edge="end" 
                              aria-label="预览"
                              onClick={() => openPreview(attachment.id, attachment.fileName || attachment.file_name || '未知文件')}
                              sx={{ p: { xs: 0.75, sm: 1 } }}
                              size={isMobile ? "small" : "medium"}
                            >
                              <ZoomInIcon fontSize={isMobile ? "small" : "medium"} />
                            </IconButton>
                          </Tooltip>
                        )}
                        <Tooltip title="下载">
                          <IconButton 
                            edge="end" 
                            aria-label="下载"
                            onClick={() => downloadAttachment(attachment.id, attachment.fileName || attachment.file_name || '未知文件')}
                            sx={{ p: { xs: 0.75, sm: 1 } }}
                            size={isMobile ? "small" : "medium"}
                          >
                            <DownloadIcon fontSize={isMobile ? "small" : "medium"} />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    }
                  >
                    <ListItemButton
                      onClick={() => isPreviewable(attachment) 
                        ? openPreview(attachment.id, attachment.fileName || attachment.file_name || '未知文件')
                        : downloadAttachment(attachment.id, attachment.fileName || attachment.file_name || '未知文件')
                      }
                      dense={isMobile}
                    >
                      <ListItemIcon>
                        {getAttachmentIcon(attachment.fileName || attachment.file_name)}
                      </ListItemIcon>
                      <ListItemText 
                        primary={attachment.fileName || attachment.file_name || '未知文件'}
                        secondary={`${formatFileSize(attachment.fileSize || attachment.file_size || 0)} • ${formatDateTime(attachment.createdAt || attachment.created_at || new Date().toISOString())}`}
                      />
                    </ListItemButton>
                  </ListItem>
                ))}
              </List>
            </Box>
          )}
        </Box>
      </Paper>
      
      {/* 底部返回按钮 */}
      <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3, mt: 2, px: '10px' }}>
        <Button
          startIcon={<ArrowBackIcon />}
          variant="outlined"
          onClick={() => navigate('/records/manage')}
          sx={{ fontSize: '0.75rem' }}
        >
          返回记录列表
        </Button>
      </Box>
      
      {/* 通知消息 */}
      <Snackbar
        open={notification.open}
        autoHideDuration={5000}
        onClose={handleCloseNotification}
      >
        <Alert 
          onClose={handleCloseNotification} 
          severity={notification.type}
          variant="filled"
          sx={{ 
            width: '100%',
            '& .MuiAlert-message': { fontSize: '0.75rem' }
          }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
      
      {/* 图片预览弹窗 */}
      <Modal
        open={previewOpen}
        onClose={closePreview}
        aria-labelledby="image-preview-title"
        aria-describedby="image-preview-description"
      >
        <Box sx={{ 
          position: 'absolute', 
          top: '50%', 
          left: '50%', 
          transform: 'translate(-50%, -50%)', 
          bgcolor: 'background.paper', 
          boxShadow: 24, 
          p: 1,
          maxWidth: '90vw',
          maxHeight: '90vh',
          display: 'flex',
          flexDirection: 'column'
        }}>
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            mb: 1
          }}>
            <Typography variant="subtitle1" sx={{ fontSize: '0.875rem' }}>
              {previewTitle}
            </Typography>
            <IconButton onClick={closePreview} size="small">
              <CloseIcon fontSize="small" />
            </IconButton>
          </Box>
          
          <Box sx={{ 
            overflow: 'auto', 
            maxWidth: '100%', 
            maxHeight: 'calc(90vh - 50px)',
            textAlign: 'center'
          }}>
            {previewUrl && (
              <img 
                src={previewUrl} 
                alt={previewTitle}
                style={{ maxWidth: '100%', maxHeight: 'calc(90vh - 60px)' }}
              />
            )}
          </Box>
        </Box>
      </Modal>
    </Box>
  );
};

export default RecordDetailPage; 