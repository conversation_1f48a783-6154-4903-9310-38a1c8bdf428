/**
 * 字体安装脚本
 * 用于下载和安装中文字体到本地字体目录
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const { execSync } = require('child_process');
const os = require('os');

// 字体目录
const fontDir = path.join(__dirname, '..', 'fonts');

// 确保字体目录存在
if (!fs.existsSync(fontDir)) {
  fs.mkdirSync(fontDir, { recursive: true });
  console.log(`创建字体目录: ${fontDir}`);
}

// 字体下载链接 - 使用开源字体，确保许可证允许嵌入
const fontUrls = {
  // 文泉驿微米黑 - 开源中文字体
  'wqy-microhei.ttc': 'https://raw.githubusercontent.com/anthonyfok/fonts-wqy-microhei/master/wqy-microhei.ttc',
  
  // Noto Sans SC - Google开源的中文字体
  'NotoSansSC-Regular.otf': 'https://github.com/googlefonts/noto-cjk/raw/main/Sans/OTF/SimplifiedChinese/NotoSansSC-Regular.otf',
  
  // 思源黑体 - Adobe开源的中文字体
  'SourceHanSansSC-Regular.otf': 'https://github.com/adobe-fonts/source-han-sans/raw/release/OTF/SimplifiedChinese/SourceHanSansSC-Regular.otf'
};

/**
 * 下载文件
 * @param {string} url - 下载URL
 * @param {string} dest - 目标文件路径
 * @returns {Promise<void>}
 */
function downloadFile(url, dest) {
  return new Promise((resolve, reject) => {
    console.log(`开始下载: ${url} 到 ${dest}`);
    
    const file = fs.createWriteStream(dest);
    
    https.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`下载失败，状态码: ${response.statusCode}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`下载完成: ${dest}`);
        resolve();
      });
    }).on('error', (err) => {
      fs.unlink(dest, () => {}); // 删除部分下载的文件
      reject(err);
    });
  });
}

/**
 * 复制系统字体到本地字体目录
 */
function copySystemFonts() {
  const platform = os.platform();
  let fontPaths = [];
  
  if (platform === 'win32') {
    // Windows系统
    fontPaths = [
      'C:\\Windows\\Fonts\\simhei.ttf',     // 黑体
      'C:\\Windows\\Fonts\\simsun.ttc',     // 宋体
      'C:\\Windows\\Fonts\\msyh.ttf'        // 微软雅黑
    ];
  } else if (platform === 'linux') {
    // Linux系统
    fontPaths = [
      '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc',
      '/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc',
      '/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc'
    ];
  } else if (platform === 'darwin') {
    // macOS系统
    fontPaths = [
      '/System/Library/Fonts/PingFang.ttc',
      '/Library/Fonts/STHeiti Light.ttc'
    ];
  }
  
  // 复制系统字体到本地字体目录
  let copiedCount = 0;
  for (const fontPath of fontPaths) {
    if (fs.existsSync(fontPath)) {
      const fontName = path.basename(fontPath);
      const destPath = path.join(fontDir, fontName);
      
      if (!fs.existsSync(destPath)) {
        try {
          fs.copyFileSync(fontPath, destPath);
          console.log(`成功复制系统字体: ${fontPath} -> ${destPath}`);
          copiedCount++;
        } catch (err) {
          console.error(`复制字体失败: ${fontPath}`, err);
        }
      } else {
        console.log(`字体已存在: ${destPath}`);
        copiedCount++;
      }
    }
  }
  
  return copiedCount;
}

/**
 * 主函数
 */
async function main() {
  console.log('=== 字体安装脚本 ===');
  
  // 1. 尝试复制系统字体
  console.log('\n尝试复制系统字体...');
  const copiedCount = copySystemFonts();
  console.log(`复制了 ${copiedCount} 个系统字体`);
  
  // 2. 如果没有复制到足够的字体，尝试下载
  if (copiedCount < 1) {
    console.log('\n尝试下载字体...');
    
    for (const [fontName, url] of Object.entries(fontUrls)) {
      const fontPath = path.join(fontDir, fontName);
      
      if (fs.existsSync(fontPath)) {
        console.log(`字体已存在: ${fontPath}`);
        continue;
      }
      
      try {
        await downloadFile(url, fontPath);
        console.log(`成功下载字体: ${fontName}`);
      } catch (err) {
        console.error(`下载字体失败: ${fontName}`, err);
      }
    }
  }
  
  // 3. 列出已安装的字体
  console.log('\n已安装的字体:');
  const installedFonts = fs.readdirSync(fontDir);
  
  if (installedFonts.length === 0) {
    console.log('未找到任何字体！');
  } else {
    installedFonts.forEach(font => console.log(`- ${font}`));
  }
  
  console.log('\n字体安装完成！');
  console.log('PDF生成时将优先使用这些字体，确保在任何环境下都能正确显示中文。');
}

// 执行主函数
main().catch(err => {
  console.error('脚本执行失败:', err);
  process.exit(1);
});
