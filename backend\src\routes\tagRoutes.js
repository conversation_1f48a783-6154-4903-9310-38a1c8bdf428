/**
 * 标签路由管理
 * 提供标签的基本增删改查功能
 */
const express = require('express');
const router = express.Router();
const Tag = require('../../models/Tag');
const jwt = require('jsonwebtoken');
const { JWT_SECRET } = require('../config');

// 身份验证中间件
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers.authorization;
  if (!authHeader) return res.status(401).json({ error: '未授权' });
  
  const token = authHeader.split(' ')[1];
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) return res.status(403).json({ error: '令牌无效' });
    req.user = user;
    next();
  });
};

// 获取用户的所有自定义标签
router.get('/user-tags', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const tags = await Tag.query()
      .where('created_by', userId)
      .andWhere('type', 'user')
      .orderBy('name');
    res.json(tags);
  } catch (error) {
    console.error('获取用户标签失败:', error);
    res.status(500).json({ error: error.message });
  }
});

// 创建新标签
router.post('/create', authenticateToken, async (req, res) => {
  try {
    const { name } = req.body;
    const userId = req.user.id;
    
    if (!name || !name.trim()) {
      return res.status(400).json({ error: '标签名称不能为空' });
    }
    
    // 查找或创建标签
    const tag = await Tag.findOrCreate(name.trim(), { 
      created_by: userId,
      type: 'user'
    });
    
    res.status(201).json(tag);
  } catch (error) {
    console.error('创建标签失败:', error);
    res.status(500).json({ error: error.message });
  }
});

// 删除标签
router.delete('/:tagId', authenticateToken, async (req, res) => {
  try {
    const { tagId } = req.params;
    const userId = req.user.id;
    
    // 确保只能删除自己的标签
    const tag = await Tag.query()
      .findById(tagId)
      .where('created_by', userId);
      
    if (!tag) {
      return res.status(404).json({ error: '标签不存在或无权删除' });
    }
    
    await Tag.query().deleteById(tagId);
    
    res.json({ message: '标签已删除' });
  } catch (error) {
    console.error('删除标签失败:', error);
    res.status(500).json({ error: error.message });
  }
});

// 根据标签筛选记录
router.get('/filter-records', authenticateToken, async (req, res) => {
  try {
    const { tagName } = req.query;
    const userId = req.user.id;
    
    if (!tagName) {
      return res.status(400).json({ error: '请提供标签名称' });
    }
    
    // 先找到标签
    const tag = await Tag.query()
      .where('name', tagName)
      .first();
      
    if (!tag) {
      return res.json([]);
    }
    
    // 找到包含此标签的记录
    const records = await tag.$relatedQuery('records')
      .where('user_id', userId)
      .whereNull('deletedAt');
      
    res.json(records);
  } catch (error) {
    console.error('按标签筛选记录失败:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router; 