{"name": "backend", "version": "1.0.0", "description": "慧看病系统后端", "main": "src/index.js", "scripts": {"start": "node src/index.js", "start-all": "node start-servers.js", "migrate": "knex migrate:latest", "dev": "nodemon src/index.js", "test": "echo \"Error: no test specified\" && exit 1", "init-db": "node scripts/init-database.js", "rollback-db": "node scripts/rollback-database.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"axios": "^1.9.0", "bcrypt": "^5.1.1", "chalk": "^4.1.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-validator": "^7.2.1", "jsonwebtoken": "^9.0.2", "knex": "^3.0.1", "multer": "^1.4.5-lts.1", "objection": "^3.1.3", "pdfkit": "^0.17.1", "pg": "^8.16.0", "sequelize": "^6.37.7", "uuid": "^9.0.1", "ws": "^8.18.2"}, "devDependencies": {"nodemon": "^3.1.10"}}