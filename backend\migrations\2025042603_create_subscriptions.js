const { v4: uuidv4 } = require('uuid');
exports.up = function (knex) {
  return knex.schema.createTable('subscriptions', (table) => {
    table.string('id').primary().defaultTo(uuidv4());
    table.string('user_id').notNullable();
    table.string('plan').notNullable();
    table.string('status').notNullable();
    table.date('start_date').notNullable();
    table.date('end_date').notNullable();
    table.timestamp('created_at', { useTz: true }).notNullable();
  });
};
exports.down = function (knex) {
  return knex.schema.dropTable('subscriptions');
}; 