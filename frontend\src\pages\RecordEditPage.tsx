import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  IconButton,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  FormControlLabel,
  Switch,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Divider,
  CircularProgress,
  Alert,
  Snackbar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Tooltip,
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import SaveIcon from '@mui/icons-material/Save';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import BookmarkIcon from '@mui/icons-material/Bookmark';
import LockIcon from '@mui/icons-material/Lock';
import AttachmentIcon from '@mui/icons-material/Attachment';
import SettingsIcon from '@mui/icons-material/Settings';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import ImageIcon from '@mui/icons-material/Image';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import DescriptionIcon from '@mui/icons-material/Description';
import InfoIcon from '@mui/icons-material/Info';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useForm, Controller } from 'react-hook-form';
import { 
  getRecord, 
  updateRecord, 
  getAttachments, 
  uploadAttachment, 
  deleteAttachment 
} from '../services/recordService';
import { getPatients } from '../services/patientService';
import { getDiseases } from '../services/diseaseService';
import { useAuthStore } from '../store/authStore';
import StageSelector from '../components/records/StageSelector';
import TypeTagSelector from '../components/records/TypeTagSelector';
import SeveritySlider from '../components/records/SeveritySlider';
import SimpleTagInput from '../components/tags/SimpleTagInput';
import { RecordTypeEnum, StagePhaseEnum } from '../types/recordEnums';

// 严重程度枚举
enum SeverityEnum {
  MILD = 'MILD',
  MODERATE = 'MODERATE',
  SEVERE = 'SEVERE',
  CRITICAL = 'CRITICAL'
}

// 严重程度值映射（数字到字符串）
const NUMBER_TO_SEVERITY: Record<number, SeverityEnum> = {
  1: SeverityEnum.MILD,
  2: SeverityEnum.MODERATE,
  3: SeverityEnum.SEVERE,
  4: SeverityEnum.CRITICAL
};

// 节点枚举
enum StageNodeEnum {
  INITIAL_VISIT = 'INITIAL_VISIT',
  DIAGNOSIS = 'DIAGNOSIS',
  TREATMENT = 'TREATMENT',
  FOLLOW_UP = 'FOLLOW_UP',
  PROGNOSIS = 'PROGNOSIS',
  ARCHIVE = 'ARCHIVE'
}

// 添加节点到阶段的映射
const NODE_TO_PHASE_MAP: Record<StageNodeEnum, StagePhaseEnum> = {
  [StageNodeEnum.INITIAL_VISIT]: StagePhaseEnum.INITIAL,    // 生病节点 -> 初诊阶段
  [StageNodeEnum.DIAGNOSIS]: StagePhaseEnum.DIAGNOSIS,      // 确诊节点 -> 确诊阶段
  [StageNodeEnum.TREATMENT]: StagePhaseEnum.TREATMENT,      // 治疗节点 -> 治疗阶段
  [StageNodeEnum.FOLLOW_UP]: StagePhaseEnum.RECOVERY,       // 随访节点 -> 康复阶段
  [StageNodeEnum.PROGNOSIS]: StagePhaseEnum.PROGNOSIS,      // 预后节点 -> 预后阶段
  [StageNodeEnum.ARCHIVE]: StagePhaseEnum.PROGNOSIS         // 归档节点 -> 预后阶段 (默认放在预后阶段)
};

// 附件类型
interface Attachment {
  id?: string;
  file?: File;
  fileName?: string;
  fileSize?: number;
  preview?: string;
  url?: string;
  createdAt?: string;
}

// 通知类型
interface Notification {
  open: boolean;
  message: string;
  type: 'success' | 'error' | 'info' | 'warning';
}

// 用户等级限制
interface UserLevelLimits {
  maxAttachmentSize: number;
  maxQuantity: number;
  maxTotalStorage: number;
}

// 表单数据类型
interface FormData {
  patientId: string;
  diseaseId: string;
  title: string;
  content: string;
  recordDate: string;
  recordType: any[]; // 修改类型为any[]避免类型冲突
  primaryType: any; // 修改类型为any避免类型冲突
  stagePhase: string;
  stageNode: string;
  severity: SeverityEnum;
  isImportant: boolean;
  isPrivate: boolean;
  customTags: string;
}

// 记录编辑页面组件
const RecordEditPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { token, user } = useAuthStore((state) => state);
  const userId = user?.id || '';

  // 通知状态
  const [notification, setNotification] = useState<Notification>({
    open: false,
    message: '',
    type: 'success'
  });
  
  // 高级选项面板状态
  const [expanded, setExpanded] = useState(false);
  
  // 附件状态
  const [attachments, setAttachments] = useState<Attachment[]>([]);
  const [existingAttachments, setExistingAttachments] = useState<Attachment[]>([]);
  const [deletedAttachmentIds, setDeletedAttachmentIds] = useState<string[]>([]);
  
  // 用户权限/等级限制
  const [userLevelLimits] = useState<UserLevelLimits>({
    maxAttachmentSize: 5120, // 默认5MB
    maxQuantity: 5, // 默认最多5个附件
    maxTotalStorage: 20480, // 默认20MB总容量
  });
  
  // 计算已用附件总大小（KB）
  const totalAttachmentSize = attachments.reduce((total, attachment) => {
    return total + Math.ceil((attachment.file?.size || 0) / 1024);
  }, 0);
  
  // 表单状态
  const { control, handleSubmit, setValue, watch, reset, formState: { errors } } = useForm<FormData>({
    defaultValues: {
      patientId: '',
      diseaseId: '',
      title: '',
      content: '',
      recordDate: new Date().toISOString().slice(0, 16),
      recordType: [],
      primaryType: '' as RecordTypeEnum,
      stagePhase: '',
      stageNode: '',
      severity: SeverityEnum.MODERATE,
      isImportant: false,
      isPrivate: false,
      customTags: '',
    }
  });
  
  // 获取记录数据
  const { 
    data: recordData, 
    isLoading: isLoadingRecord, 
    error: recordError 
  } = useQuery({
    queryKey: ['record', id],
    queryFn: () => getRecord(id || '', { 
      include_all_users: true,
      include_deleted: true,
      service_context: true
    }),
    enabled: !!id && !!token,
  });

  // 当记录数据加载成功后处理
  useEffect(() => {
    if (recordData) {
      // 记录原始数据，便于调试
      console.log('原始记录数据:', recordData);
      console.log('原始记录日期:', recordData.recordDate);
      
      // 确保记录日期格式正确
      let formattedRecordDate = new Date().toISOString().slice(0, 16); // 默认为当前时间
      
      if (recordData.recordDate) {
        try {
          // 尝试解析日期
          const recordDateObj = new Date(recordData.recordDate);
          if (!isNaN(recordDateObj.getTime())) {
            // 日期有效，将其格式化为 yyyy-MM-ddThh:mm 格式
            formattedRecordDate = recordDateObj.toISOString().slice(0, 16);
            console.log('解析后的记录日期:', formattedRecordDate);
          } else {
            console.warn('记录日期无效:', recordData.recordDate);
          }
        } catch (error) {
          console.error('解析记录日期失败:', error);
        }
      }
      
      // 解析记录类型
      let recordTypeArray: any[] = [];
      try {
        if (Array.isArray(recordData.recordType)) {
          recordTypeArray = recordData.recordType as any[];
        } else if (typeof recordData.recordType === 'string') {
          if (recordData.recordType.startsWith('[') && recordData.recordType.endsWith(']')) {
            recordTypeArray = JSON.parse(recordData.recordType) as any[];
          } else {
            recordTypeArray = [recordData.recordType as any];
          }
        }
      } catch (error) {
        console.error('解析记录类型失败:', error);
        recordTypeArray = [];
      }
      
      // 将数字严重程度转换为枚举字符串
      let severityValue = SeverityEnum.MODERATE;
      const severity = recordData.severity;
      
      console.log('原始严重程度值:', severity, typeof severity);
      
      if (typeof severity === 'number' || (typeof severity === 'string' && !isNaN(Number(severity)))) {
        // 数字或数字字符串
        const numValue = typeof severity === 'number' ? severity : Number(severity);
        severityValue = NUMBER_TO_SEVERITY[numValue] || SeverityEnum.MODERATE;
        console.log('转换后的严重程度枚举:', severityValue);
      } else if (typeof severity === 'string' && Object.values(SeverityEnum).includes(severity as SeverityEnum)) {
        // 已经是枚举字符串
        severityValue = severity as SeverityEnum;
      }
      
      // 设置表单默认值
      reset({
        patientId: recordData.patientId || '',
        diseaseId: recordData.diseaseId || '',
        title: recordData.title || '',
        content: recordData.content || '',
        recordDate: formattedRecordDate,
        recordType: recordTypeArray,
        primaryType: (recordData.primaryType as any) || (recordTypeArray.length > 0 ? recordTypeArray[0] : ''),
        stagePhase: recordData.stagePhase || '',
        stageNode: recordData.stageNode || '',
        severity: severityValue,
        isImportant: !!recordData.isImportant,
        isPrivate: !!recordData.isPrivate,
        customTags: recordData.customTags || '',
      });
      
      // 如果记录有相关的病程阶段或节点，展开高级选项
      if (recordData.stagePhase || recordData.stageNode) {
        setExpanded(true);
      }
    }
  }, [recordData, reset]);
  
  // 获取记录附件
  const { 
    data: attachmentsData, 
    // isLoading: isLoadingAttachments // Unused
  } = useQuery({
    queryKey: ['recordAttachments', id],
    queryFn: () => getAttachments(id || '', {
      include_all_users: true,
      include_deleted: true,
      service_context: true
    }),
    enabled: !!id && !!token,
  });

  // 当附件数据加载成功后处理
  useEffect(() => {
    if (attachmentsData) {
      // 处理已有的附件
      const existingAttachmentsData = attachmentsData.map((attachment: any) => ({
        id: attachment.id,
        fileName: attachment.fileName || attachment.file_name,
        fileSize: attachment.fileSize || attachment.file_size,
        createdAt: attachment.createdAt || attachment.created_at,
        url: attachment.url
      }));
      console.log('已加载的现有附件:', existingAttachmentsData.length, '个');
      setExistingAttachments(existingAttachmentsData);
    } else {
      // 确保附件数据为空时设置为空数组
      setExistingAttachments([]);
    }
  }, [attachmentsData]);
  
  // 获取患者列表
  const { data: patients, /*isLoading: isLoadingPatients*/ } = useQuery({ // Unused isLoadingPatients
    queryKey: ['patients', userId],
    queryFn: () => getPatients(),
    enabled: !!token
  });
  
  // 获取疾病列表
  const { data: diseases, /*isLoading: isLoadingDiseases*/ } = useQuery({ // Unused isLoadingDiseases
    queryKey: ['diseasesForUser', userId],
    queryFn: () => getDiseases(),
    enabled: !!token
  });
  
  // 更新记录的mutation
  const updateRecordMutation = useMutation({
    mutationFn: (data: any) => updateRecord(id || '', data),
    onSuccess: async (data) => {
      // 记录当前的附件状态以便调试
      console.log('记录更新成功, ID:', data.id);
      console.log('现有附件数量:', existingAttachments.length, '个');
      console.log('待删除附件数量:', deletedAttachmentIds.length, '个');
      console.log('新上传附件数量:', attachments.length, '个');
      
      let hasErrors = false;
      let errorMessages = [];
      
      // 处理附件上传 - 新附件
      if (attachments.length > 0) {
        try {
          for (const attachment of attachments) {
            if (attachment.file) {
              console.log('开始上传附件:', attachment.fileName);
              await uploadAttachment(data.id, attachment.file, userLevelLimits);
            }
          }
          console.log('所有新附件上传成功');
        } catch (error: any) {
          console.error('附件上传失败:', error);
          hasErrors = true;
          errorMessages.push(`上传附件失败: ${error.message}`);
        }
      }
      
      // 处理附件删除 - 已删除的附件
      if (deletedAttachmentIds.length > 0) {
        try {
          for (const attachmentId of deletedAttachmentIds) {
            console.log('开始删除附件:', attachmentId);
            await deleteAttachment(attachmentId);
          }
          console.log('所有待删除附件处理完成');
        } catch (error: any) {
          console.error('删除附件失败:', error);
          hasErrors = true;
          errorMessages.push(`删除附件失败: ${error.message}`);
        }
      }
      
      // 判断是否有错误，并显示相应的通知
      if (hasErrors) {
        setNotification({
          open: true,
          message: `记录更新成功，但附件处理出现问题: ${errorMessages.join('; ')}`,
          type: 'warning'
        });
      } else {
        // 全部成功
        setNotification({
          open: true,
          message: '记录及附件更新成功',
          type: 'success'
        });
      }
      
      // 重置状态
      setAttachments([]);
      setDeletedAttachmentIds([]);
      
      // 刷新记录和附件数据
      queryClient.invalidateQueries({ queryKey: ['record', id] });
      queryClient.invalidateQueries({ queryKey: ['recordAttachments', id] });
      
      // 导航到记录详情页，稍微延迟以确保用户能看到通知
      setTimeout(() => {
        navigate(`/records/${id}`);
      }, 1500);
    },
    onError: (error: any) => {
      console.error('更新记录失败:', error);
      setNotification({
        open: true,
        message: `更新记录失败: ${error.message}`,
        type: 'error'
      });
    }
  });
  
  // 提交表单
  const onSubmit = (data: FormData) => {
    // 检查primaryType是否在recordType中
    if (data.primaryType && !data.recordType.includes(data.primaryType)) {
      // 如果主要类型不在选择的类型中，则添加到记录类型中
      data.recordType.push(data.primaryType);
    }

    // 确保记录日期有效
    console.log('提交前的记录日期:', data.recordDate);
    console.log('原始记录日期:', recordData?.recordDate);
    
    // 如果提交的日期无效，使用原始记录日期
    let finalRecordDate = data.recordDate;
    if (!finalRecordDate && recordData?.recordDate) {
      console.log('使用原始记录日期作为备选');
      try {
        const recordDateObj = new Date(recordData.recordDate);
        if (!isNaN(recordDateObj.getTime())) {
          finalRecordDate = recordDateObj.toISOString().slice(0, 16);
        }
      } catch (error) {
        console.error('解析原始记录日期失败:', error);
      }
    }
    
    // 确保枚举值正确
    const updatedRecord = {
      ...data,
      // 保持原有ID相关字段不变
      userId,
      createdBy: recordData?.createdBy || userId,
      // 确保stageNode和stagePhase为正确的枚举值
      stageNode: data.stageNode || null,
      // 如果有节点，根据节点确定阶段
      stagePhase: data.stageNode 
        ? NODE_TO_PHASE_MAP[data.stageNode as StageNodeEnum] // 使用节点对应的阶段
        : (data.stagePhase || null), // 如果没有节点，使用选择的阶段或null
      // 确保其他枚举值也是正确的
      recordType: data.recordType || [],
      primaryType: data.primaryType || null,
      // 直接使用枚举值，不再转换为数字
      severity: data.severity || SeverityEnum.MODERATE,
      // 确保记录日期不变
      recordDate: finalRecordDate
    };
    
    // 保存更新前的附件信息，用于记录日志和错误恢复
    const existingAttachmentInfo = existingAttachments
      .filter(att => !deletedAttachmentIds.includes(att.id || ''))
      .map(att => ({
        id: att.id,
        fileName: att.fileName,
        fileSize: att.fileSize
      }));
    console.log('保留附件列表:', existingAttachmentInfo.length, '个');
    console.log('需要删除附件列表:', deletedAttachmentIds.length, '个');
    console.log('新增附件列表:', attachments.length, '个');
    
    console.log('提交更新的记录数据:', updatedRecord);
    updateRecordMutation.mutate(updatedRecord);
  };

  // 处理高级选项面板展开/折叠
  const handleExpandChange = () => {
    setExpanded(!expanded);
  };

  // 处理附件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!event.target.files || event.target.files.length === 0) return;
    
    const newFiles = Array.from(event.target.files);
    
    // 检查附件数量限制（新附件 + 已有附件）
    if (attachments.length + existingAttachments.length + newFiles.length > userLevelLimits.maxQuantity) {
      setNotification({
        open: true,
        message: `附件数量超过限制 (最多${userLevelLimits.maxQuantity}个)`,
        type: 'error'
      });
      return;
    }
    
    // 检查附件大小限制
    const oversizedFiles = newFiles.filter(file => file.size > userLevelLimits.maxAttachmentSize * 1024);
    if (oversizedFiles.length > 0) {
      setNotification({
        open: true,
        message: `文件大小超过限制 (最大${userLevelLimits.maxAttachmentSize}KB): ${oversizedFiles.map(f => f.name).join(', ')}`,
        type: 'error'
      });
      return;
    }
    
    // 检查总容量限制
    const newTotalSize = totalAttachmentSize + newFiles.reduce((size, file) => size + Math.ceil(file.size / 1024), 0);
    if (newTotalSize > userLevelLimits.maxTotalStorage) {
      setNotification({
        open: true,
        message: `总容量超过限制 (最大${userLevelLimits.maxTotalStorage}KB)`,
        type: 'error'
      });
      return;
    }
    
    // 处理上传的文件
    const newAttachments = newFiles.map(file => {
      // 对于图片类型生成预览
      const preview = file.type.startsWith('image/') 
        ? URL.createObjectURL(file)
        : undefined;
        
      return { file, fileName: file.name, fileSize: file.size, preview };
    });
    
    setAttachments([...attachments, ...newAttachments]);
    
    // 重置input值，允许再次选择相同文件
    event.target.value = '';
  };
  
  // 删除新上传的附件
  const handleDeleteNewAttachment = (index: number) => {
    const newAttachments = [...attachments];
    // 如果有预览URL，释放资源
    if (newAttachments[index].preview) {
      URL.revokeObjectURL(newAttachments[index].preview!);
    }
    newAttachments.splice(index, 1);
    setAttachments(newAttachments);
  };
  
  // 删除已有的附件
  const handleDeleteExistingAttachment = (attachmentId: string) => {
    if (!attachmentId) {
      console.error('尝试删除无效的附件ID');
      return;
    }
    // 将ID添加到已删除附件ID列表
    setDeletedAttachmentIds([...deletedAttachmentIds, attachmentId]);
    // 从显示列表中移除
    setExistingAttachments(existingAttachments.filter(att => att.id !== attachmentId));
  };
  
  // 关闭通知
  const handleCloseNotification = () => {
    setNotification({
      ...notification,
      open: false
    });
  };
  
  // 处理记录类型更改
  const handleRecordTypeChange = (types: any[]) => {
    setValue('recordType', types);
    
    // 如果只有一个类型，自动设置为主要类型
    if (types.length === 1) {
      setValue('primaryType', types[0]);
    }
    // 如果当前主要类型不在选择的类型中，更新主要类型为第一个选择的类型
    else if (types.length > 0 && !types.includes(watch('primaryType'))) {
      setValue('primaryType', types[0]);
    }
  };
  
  // 获取附件图标
  const getAttachmentIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase() || '';
    
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension)) {
      return <ImageIcon />;
    } else if (extension === 'pdf') {
      return <PictureAsPdfIcon />;
    } else if (['doc', 'docx', 'txt', 'rtf', 'odt'].includes(extension)) {
      return <DescriptionIcon />;
    } else {
      return <InsertDriveFileIcon />;
    }
  };
  
  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  };
  
  // 处理严重程度变化
  const handleSeverityChange = (enumValue: SeverityEnum, numValue: number) => {
    // 直接使用枚举字符串值，不再转换为数字
    setValue('severity', enumValue);
  };
  
  // 加载中显示
  if (isLoadingRecord) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh', px: '10px' }}>
        <CircularProgress />
      </Box>
    );
  }
  
  // 错误显示
  if (recordError) {
    return (
      <Box sx={{ m: 2, px: '10px' }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          加载记录失败: {(recordError as Error).message}
        </Alert>
        <Button 
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/records/manage')}
          variant="outlined"
        >
          返回记录列表
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ m: 0, width: '100%' }}>
      {/* 页面标题和操作按钮 */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-end', mb: 0, px: '10px', mt: '20px' }}>
        <Typography 
          variant="h5" 
          component="h1" 
          sx={{ 
            fontWeight: 500,
            fontSize: { xs: '1.1rem', sm: '1.3rem' },
            mb: 0
          }}
        >
          编辑记录
        </Typography>
      </Box>
      <Divider sx={{ mb: { xs: 2, md: 3 }, mx: '10px' }} />
      
      <Paper 
        elevation={0} 
        sx={{ 
          p: { xs: 2, md: 3 }, 
          mb: { xs: 2, md: 3 },
          mx: '10px',
          borderWidth: 0,
          boxShadow: 'none'
        }}
      >
        <form onSubmit={handleSubmit(onSubmit)}>
          {/* 患者和病理选择行 */}
          <Box sx={{ display: 'flex', flexDirection: 'row', gap: 2, mb: 3 }}>
            {/* 患者选择 - 锁定状态 */}
            <Box sx={{ flex: 1 }}>
              <Controller
                name="patientId"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth size="small">
                    <InputLabel sx={{ fontSize: '0.75rem' }}>患者</InputLabel>
                    <Select
                      {...field}
                      label="患者"
                      disabled={true}
                      sx={{ fontSize: '0.75rem' }}
                      IconComponent={() => (
                        <Tooltip title="编辑记录时无法更改关联患者">
                          <LockIcon fontSize="small" sx={{ mr: 2, color: 'text.disabled' }} />
                        </Tooltip>
                      )}
                    >
                      {patients?.map((patient: any) => (
                        <MenuItem key={patient.id} value={patient.id} sx={{ fontSize: '0.75rem' }}>
                          {patient.name}
                        </MenuItem>
                      ))}
                    </Select>
                    <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, fontSize: '0.65rem' }}>
                      记录编辑时无法更改关联患者
                    </Typography>
                  </FormControl>
                )}
              />
            </Box>
            
            {/* 病理选择 - 锁定状态 */}
            <Box sx={{ flex: 1 }}>
              <Controller
                name="diseaseId"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth size="small">
                    <InputLabel sx={{ fontSize: '0.75rem' }}>病理</InputLabel>
                    <Select
                      {...field}
                      label="病理"
                      disabled={true}
                      sx={{ fontSize: '0.75rem' }}
                      IconComponent={() => (
                        <Tooltip title="编辑记录时无法更改关联病理">
                          <LockIcon fontSize="small" sx={{ mr: 2, color: 'text.disabled' }} />
                        </Tooltip>
                      )}
                    >
                      {diseases?.filter((disease: any) => disease.id === field.value).map((disease: any) => (
                        <MenuItem key={disease.id} value={disease.id} sx={{ fontSize: '0.75rem' }}>
                          {disease.name}
                        </MenuItem>
                      ))}
                    </Select>
                    <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, fontSize: '0.65rem' }}>
                      记录编辑时无法更改关联病理
                    </Typography>
                  </FormControl>
                )}
              />
            </Box>
          </Box>
          
          <Divider sx={{ my: 2 }} />
          
          {/* 病程阶段组件 - 从高级选项中移出到这里 */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle1" gutterBottom sx={{ fontSize: '0.75rem' }}>病程阶段</Typography>
            <Controller
              name="stageNode"
              control={control}
              render={({ field: { value, onChange, ...field } }) => (
                <Controller
                  name="stagePhase"
                  control={control}
                  render={({ field: { value: phaseValue, onChange: phaseChange, ...phaseField } }) => (
                    <StageSelector
                      selectedNode={value ? value as StageNodeEnum : null}
                      selectedPhase={phaseValue ? phaseValue as StagePhaseEnum : null}
                      onNodeChange={(node) => {
                        onChange(node || '');
                        // 当选择节点时，自动标记为重要记录
                        // 当取消选择节点时，取消重要标记
                        setValue('isImportant', !!node);
                      }}
                      onPhaseChange={(phase) => {
                        phaseChange(phase || '');
                        // 对于仅选择阶段的情况，不自动标记为重要
                        // 保持isImportant原有状态
                      }}
                      error={errors.stageNode?.message || errors.stagePhase?.message}
                      helperText="选择病程节点或阶段，记录发生在病程的哪个阶段"
                      {...field}
                      {...phaseField}
                    />
                  )}
                />
              )}
            />
          </Box>
          
          {/* 记录类型选择 */}
          <Box sx={{ mb: 3 }}>
            <Controller
              name="recordType"
              control={control}
              rules={{ 
                required: '请至少选择一个记录类型',
                validate: value => 
                  (value && value.length > 0 && value.length <= 4) || 
                  '请选择1-4个记录类型'
              }}
              render={({ field: { value, onChange } }) => (
                <Controller
                  name="primaryType"
                  control={control}
                  rules={{ 
                    required: '请选择主要记录类型',
                    validate: value => {
                      const recordTypes = watch('recordType');
                      return (recordTypes.includes(value)) || 
                        '主要类型必须在已选记录类型中';
                    }
                  }}
                  render={({ field: { value: primaryValue, onChange: primaryChange } }) => (
                    <TypeTagSelector
                      selectedTypes={value as any || []}
                      onChange={(types) => {
                        handleRecordTypeChange(types);
                      }}
                      primaryType={primaryValue as any || null}
                      onPrimaryTypeChange={(type) => {
                        primaryChange(type);
                      }}
                      error={
                        (errors.recordType?.message ? String(errors.recordType.message) : 
                         errors.primaryType?.message ? String(errors.primaryType.message) : 
                         undefined)
                      }
                      helperText="请选择1-4个标签，1个红色主标签，单击选择，双击取消。"
                    />
                  )}
                />
              )}
            />
          </Box>
          
          {/* 记录内容区域 */}
          <Divider sx={{ my: 2 }} />
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="subtitle1" sx={{ fontSize: { xs: '0.75rem', sm: '0.85rem' } }}>
              记录内容
            </Typography>
          </Box>
          
          {/* 标题 */}
          <Box sx={{ mb: 2 }}>
            <Controller
              name="title"
              control={control}
              rules={{ required: '请输入记录标题' }}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="标题"
                  fullWidth
                  size="small"
                  error={!!errors.title}
                  helperText={errors.title?.message || "简明扼要，字数不超10字"}
                  sx={{ 
                    '& .MuiInputBase-input': { 
                      fontSize: { xs: '0.75rem', sm: '0.85rem' } 
                    },
                    '& .MuiFormHelperText-root': {
                      fontSize: { xs: '0.65rem', sm: '0.7rem' }
                    }
                  }}
                />
              )}
            />
          </Box>
          
          {/* 记录内容 */}
          <Box sx={{ mb: 2 }}>
            <Controller
              name="content"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="内容"
                  multiline
                  rows={8}
                  fullWidth
                  size="small"
                  placeholder="请输入记录内容..."
                  helperText="详细描述记录的内容，可以包括症状、治疗方案、医嘱等信息"
                  sx={{ 
                    '& .MuiInputBase-input': { 
                      fontSize: { xs: '0.75rem', sm: '0.85rem' } 
                    },
                    '& .MuiFormHelperText-root': {
                      fontSize: { xs: '0.65rem', sm: '0.7rem' }
                    }
                  }}
                />
              )}
            />
          </Box>
          
          {/* 日期时间 */}
          <Box sx={{ mb: 3 }}>
            <Controller
              name="recordDate"
              control={control}
              rules={{ required: '请选择记录日期' }}
              render={({ field }) => (
                <Box sx={{ position: 'relative' }}>
                  <TextField
                    {...field}
                    label="记录日期和时间"
                    type="datetime-local"
                    fullWidth
                    size="small"
                    InputLabelProps={{ shrink: true }}
                    error={!!errors.recordDate}
                    helperText={errors.recordDate?.message || "保留原始记录时间，一般不需要修改"}
                    sx={{ 
                      '& .MuiInputBase-input': { 
                        fontSize: { xs: '0.75rem', sm: '0.85rem' } 
                      },
                      '& .MuiFormHelperText-root': {
                        fontSize: { xs: '0.65rem', sm: '0.7rem' }
                      }
                    }}
                    onChange={(e) => {
                      const oldValue = field.value;
                      field.onChange(e);
                      const newValue = e.target.value;
                      console.log('记录日期变更:', oldValue, '->', newValue);
                      
                      // 如果日期发生变化，显示温馨提示
                      if (oldValue !== newValue) {
                        setNotification({
                          open: true,
                          message: '提示：修改记录日期会影响记录在时间轴上的位置，请谨慎修改',
                          type: 'info'
                        });
                      }
                    }}
                    InputProps={{
                      endAdornment: (
                        <Tooltip title="这是原始记录的创建时间，除非特殊情况，建议保持不变">
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Chip 
                              size="small" 
                              label="原始时间" 
                              color="primary" 
                              variant="outlined"
                              sx={{ mr: 1, height: 24, fontSize: '0.65rem' }}
                            />
                            <InfoIcon color="action" fontSize="small" />
                          </Box>
                        </Tooltip>
                      )
                    }}
                  />
                </Box>
              )}
            />
          </Box>
          
          {/* 高级选项折叠面板 */}
          <Accordion 
            expanded={expanded} 
            onChange={handleExpandChange}
            elevation={0}
            sx={{ 
              border: '1px solid',
              borderColor: 'divider',
              mb: 3,
              '&::before': { display: 'none' },
              borderRadius: 1,
              overflow: 'hidden'
            }}
          >
            <AccordionSummary 
              expandIcon={<ExpandMoreIcon />}
              sx={{ 
                backgroundColor: expanded ? 'rgba(0, 0, 0, 0.03)' : 'transparent',
                transition: 'background-color 0.2s ease'
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <SettingsIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="subtitle2" sx={{ fontSize: { xs: '0.7rem', sm: '0.8rem' } }}>高级选项</Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              {/* 记录属性 */}
              <Box sx={{ 
                display: 'flex',
                flexDirection: { xs: 'column', sm: 'row' },
                justifyContent: 'space-between',
                alignItems: { xs: 'flex-start', sm: 'center' },
                mb: 2,
                p: 1.5,
                backgroundColor: 'background.paper',
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 1
              }}>
                <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                  <Typography variant="subtitle2" gutterBottom sx={{ fontSize: { xs: '0.7rem', sm: '0.8rem' } }}>记录属性</Typography>
                  <Box sx={{ display: 'flex', gap: 2 }}>
                    {/* 重要性开关 */}
                    <Controller
                      name="isImportant"
                      control={control}
                      render={({ field: { value, onChange, ...field } }) => (
                        <FormControlLabel
                          {...field}
                          control={
                            <Switch 
                              checked={value} 
                              onChange={onChange} 
                              color="error" 
                              size="small"
                            />
                          }
                          label={
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <BookmarkIcon fontSize="small" sx={{ mr: 0.5, color: value ? 'error.main' : 'text.secondary' }} />
                              <Typography variant="body2" sx={{ fontSize: { xs: '0.7rem', sm: '0.8rem' } }}>重要</Typography>
                            </Box>
                          }
                        />
                      )}
                    />
                    
                    {/* 隐私开关 */}
                    <Controller
                      name="isPrivate"
                      control={control}
                      render={({ field: { value, onChange, ...field } }) => (
                        <FormControlLabel
                          {...field}
                          control={
                            <Switch 
                              checked={value} 
                              onChange={onChange} 
                              color="primary" 
                              size="small"
                            />
                          }
                          label={
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <LockIcon fontSize="small" sx={{ mr: 0.5, color: value ? 'primary.main' : 'text.secondary' }} />
                              <Typography variant="body2" sx={{ fontSize: { xs: '0.7rem', sm: '0.8rem' } }}>私密</Typography>
                            </Box>
                          }
                        />
                      )}
                    />
                  </Box>
                </Box>
              </Box>
              
              {/* 严重程度滑块 */}
              <Box sx={{ 
                width: '100%',
                mb: 3,
                p: 3,
                backgroundColor: 'background.paper',
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 1
              }}>
                <Controller
                  name="severity"
                  control={control}
                  render={({ field }) => (
                    <SeveritySlider
                      value={field.value}
                      onChange={handleSeverityChange}
                      disabled={false}
                    />
                  )}
                />
              </Box>
              
              {/* 自定义标签 */}
              <Controller
                name="customTags"
                control={control}
                render={({ field }) => (
                  <SimpleTagInput
                    value={field.value}
                    onChange={field.onChange}
                    helperText="输入标签，按Enter添加，可选择已有标签或创建新标签"
                  />
                )}
              />
              
              {/* 附件区域 */}
              <Box sx={{ mt: 3, mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', fontSize: { xs: '0.7rem', sm: '0.8rem' } }}>
                  <AttachmentIcon fontSize="small" sx={{ mr: 0.5 }} />
                  附件管理
                </Typography>
                
                {/* 新附件上传 */}
                <Box sx={{ 
                  display: 'flex', 
                  flexDirection: { xs: 'column', sm: 'row' }, 
                  alignItems: { xs: 'flex-start', sm: 'center' }, 
                  mb: 1,
                  gap: 2
                }}>
                  <Button
                    variant="outlined"
                    component="label"
                    startIcon={<UploadFileIcon />}
                    size="small"
                    disabled={attachments.length + existingAttachments.length - deletedAttachmentIds.length >= userLevelLimits.maxQuantity}
                    color="primary"
                    sx={{ fontSize: { xs: '0.75rem', sm: '0.85rem' } }}
                  >
                    添加附件
                    <input
                      type="file"
                      hidden
                      multiple
                      onChange={handleFileUpload}
                    />
                  </Button>
                  <Typography variant="body2" color="text.secondary" sx={{ fontSize: { xs: '0.65rem', sm: '0.75rem' } }}>
                    已上传: {attachments.length + existingAttachments.length - deletedAttachmentIds.length}/{userLevelLimits.maxQuantity} 个文件
                  </Typography>
                </Box>
                
                {/* 已有附件列表 */}
                {existingAttachments.length > 0 && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" sx={{ mb: 1, fontSize: { xs: '0.7rem', sm: '0.8rem' } }}>现有附件</Typography>
                    <List sx={{ bgcolor: 'background.paper', borderRadius: 1, border: '1px solid', borderColor: 'divider' }}>
                      {existingAttachments.map((attachment) => (
                        <ListItem key={attachment.id} divider>
                          <ListItemIcon>{getAttachmentIcon(attachment.fileName || '')}</ListItemIcon>
                          <ListItemText
                            primary={attachment.fileName}
                            secondary={attachment.fileSize ? formatFileSize(attachment.fileSize) : ''}
                            primaryTypographyProps={{ fontSize: { xs: '0.75rem', sm: '0.85rem' } }}
                            secondaryTypographyProps={{ fontSize: { xs: '0.65rem', sm: '0.7rem' } }}
                          />
                          <IconButton
                            edge="end"
                            aria-label="delete"
                            onClick={() => attachment.id && handleDeleteExistingAttachment(attachment.id)}
                            color="error"
                            size="small"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </ListItem>
                      ))}
                    </List>
                  </Box>
                )}
                
                {/* 新上传附件列表 */}
                {attachments.length > 0 && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" sx={{ mb: 1, fontSize: { xs: '0.7rem', sm: '0.8rem' } }}>新上传附件</Typography>
                    <List sx={{ bgcolor: 'background.paper', borderRadius: 1, border: '1px solid', borderColor: 'divider' }}>
                      {attachments.map((attachment, index) => (
                        <ListItem key={index} divider>
                          <ListItemIcon>{getAttachmentIcon(attachment.fileName || '')}</ListItemIcon>
                          <ListItemText
                            primary={attachment.fileName}
                            secondary={attachment.fileSize ? formatFileSize(attachment.fileSize) : ''}
                            primaryTypographyProps={{ fontSize: { xs: '0.75rem', sm: '0.85rem' } }}
                            secondaryTypographyProps={{ fontSize: { xs: '0.65rem', sm: '0.7rem' } }}
                          />
                          <IconButton
                            edge="end"
                            aria-label="delete"
                            onClick={() => handleDeleteNewAttachment(index)}
                            color="error"
                            size="small"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </ListItem>
                      ))}
                    </List>
                  </Box>
                )}
              </Box>
            </AccordionDetails>
          </Accordion>
          
          {/* 表单提交按钮 */}
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'space-between',
            border: '1px solid',
            borderColor: 'divider',
            borderRadius: 1,
            p: 2,
            backgroundColor: 'background.paper',
            mb: 2
          }}>
            <Button
              variant="outlined"
              onClick={() => navigate(`/records/${id}`)}
              size="medium"
              startIcon={<ArrowBackIcon />}
              sx={{ fontSize: { xs: '0.75rem', sm: '0.85rem' } }}
            >
              取消
            </Button>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              size="medium"
              disabled={updateRecordMutation.isPending}
              startIcon={updateRecordMutation.isPending ? <CircularProgress size={20} /> : <SaveIcon />}
              sx={{ fontSize: { xs: '0.75rem', sm: '0.85rem' } }}
            >
              {updateRecordMutation.isPending ? '保存中...' : '保存更改'}
            </Button>
          </Box>
        </form>
      </Paper>
      
      {/* 操作结果通知 */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        sx={{ mx: '10px' }}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification.type}
          sx={{ width: '100%', fontSize: { xs: '0.7rem', sm: '0.8rem' } }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default RecordEditPage; 