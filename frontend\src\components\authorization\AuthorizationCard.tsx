import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Chip,
  FormControlLabel,
  Switch,
  IconButton,
  Tooltip,
  useTheme,
  Stack,
  Select,
  MenuItem,
  SelectChangeEvent,
  Button,
  Collapse
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import PersonIcon from '@mui/icons-material/Person';
import SecurityIcon from '@mui/icons-material/Security';
import EventIcon from '@mui/icons-material/Event';
import LockIcon from '@mui/icons-material/Lock';
import FaceIcon from '@mui/icons-material/Face';
import DescriptionIcon from '@mui/icons-material/Description';
import AssessmentIcon from '@mui/icons-material/Assessment';
import MedicalServicesIcon from '@mui/icons-material/MedicalServices';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import { useServiceUserContext } from '../../context/ServiceUserContext';

// 授权状态映射
const statusMap: Record<string, { label: string, color: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' }> = {
  PENDING_AUTHORIZER: { label: '待确认', color: 'warning' },
  PENDING_AUTHORIZED: { label: '待确认', color: 'warning' },
  ACTIVE: { label: '已激活', color: 'success' },
  REVOKED: { label: '已撤销', color: 'error' }
};

// 角色映射
const roleMap: Record<string, { label: string, color: 'default' | 'primary' | 'secondary' | 'info' }> = {
  ADMIN: { label: '管理员', color: 'secondary' },
  SERVICE: { label: '服务用户', color: 'primary' },
  USER: { label: '普通用户', color: 'info' }
};

// 权限级别映射
const privacyLevelMap: Record<string, { label: string, description: string }> = {
  BASIC: { label: '基础授权', description: '只能查看个人用户的授权信息' },
  STANDARD: { label: '标准授权', description: '可编辑病理，读写自己创建的记录和报告，只读授权用户的记录和报告' },
  FULL: { label: '完整授权', description: '可编辑病理，读写授权用户和自己创建的记录和报告' }
};

interface AuthorizationCardProps {
  auth: any;
  isAuthorizer: boolean; // 是否为授权人视图
  onSwitchChange: (id: string, value: boolean) => void;
  onDelete?: (auth: any) => void;
  onPrivacyLevelChange?: (id: string, privacyLevel: string) => void;
}

/**
 * 授权卡片组件
 * 用于在移动端显示授权信息
 */
const AuthorizationCard: React.FC<AuthorizationCardProps> = ({
  auth,
  isAuthorizer,
  onSwitchChange,
  onDelete,
  onPrivacyLevelChange
}) => {
  const [servicesExpanded, setServicesExpanded] = useState(false);
  const theme = useTheme();
  const serviceUserContext = useServiceUserContext();

  // 获取正确的用户信息
  const user = isAuthorizer ? auth.authorized : auth.authorizer;
  const username = user?.username || '未知用户';
  const role = user?.role;

  // 获取状态信息
  const { status } = auth;
  const isRevoked = status === 'REVOKED';

  // 获取Switch状态 - 使用当前用户对应的开关状态
  const switchChecked = isAuthorizer ?
    Boolean(auth.authorizer_switch) :
    Boolean(auth.authorized_switch);

  // 添加状态自动处理逻辑
  const [localSwitchState, setLocalSwitchState] = useState(switchChecked);

  // 处理开关状态变更
  const handleSwitchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newState = e.target.checked;

    // 立即更新本地开关状态，提供即时反馈
    setLocalSwitchState(newState);

    // 调用父组件传入的处理函数
    onSwitchChange(auth.id, newState);
  };

  // 获取权限级别
  const privacyLevel = auth.privacy_level || auth.privacyLevel || 'STANDARD';

  // 获取患者信息
  const patientInfo = auth.patient || null;
  const isAllPatients = !patientInfo;

  // 获取创建时间
  const createdAt = auth.created_at || auth.createdAt;
  const formattedDate = createdAt ?
    new Date(createdAt).toString() !== 'Invalid Date' ?
      new Date(createdAt).toLocaleString() :
      '日期格式错误'
    : '--';

  // 处理切换服务选项展开状态
  const toggleServicesExpanded = () => {
    setServicesExpanded(!servicesExpanded);
  };

  // 处理权限级别变更
  const handlePrivacyLevelChange = (e: SelectChangeEvent<string>) => {
    if (onPrivacyLevelChange) {
      onPrivacyLevelChange(auth.id, e.target.value);
    }
  };

  return (
    <Paper
      elevation={0}
      variant="outlined"
      sx={{
        mb: 2,
        p: 2,
        borderRadius: 2,
        borderColor: theme.palette.divider,
        '&:hover': {
          borderColor: theme.palette.primary.main,
          boxShadow: '0 0 0 1px ' + theme.palette.primary.main
        }
      }}
    >
      {/* 头部: 用户和角色 */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <PersonIcon sx={{ mr: 1, color: 'text.secondary' }} />
          <Box>
            <Typography variant="subtitle1">{username}</Typography>
            {role && roleMap[role] && (
              <Chip
                label={roleMap[role].label}
                size="small"
                color={roleMap[role].color}
                sx={{ mt: 0.5 }}
              />
            )}
          </Box>
        </Box>

        <Chip
          label={statusMap[status]?.label || '未知状态'}
          color={statusMap[status]?.color || 'default'}
          size="small"
        />
      </Box>

      {/* 内容: 权限和日期 */}
      <Stack spacing={1.5} sx={{ mb: 2 }}>
        {/* 患者信息 */}
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <FaceIcon sx={{ mr: 1, fontSize: 20, color: 'text.secondary' }} />
          <Typography variant="body2" sx={{ mr: 1 }}>授权患者:</Typography>
          <Chip
            label={isAllPatients ? '所有患者' : (patientInfo?.name || '未知患者')}
            size="small"
            variant="outlined"
            color={isAllPatients ? 'info' : 'default'}
          />
        </Box>

        {/* 权限级别信息 */}
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <LockIcon sx={{ mr: 1, fontSize: 20, color: 'text.secondary' }} />
          <Typography variant="body2" sx={{ mr: 1 }}>权限级别:</Typography>
          {onPrivacyLevelChange && !isRevoked ? (
            <Select
              value={privacyLevel}
              onChange={handlePrivacyLevelChange}
              size="small"
              sx={{ minWidth: 120, height: 32 }}
            >
              {Object.entries(privacyLevelMap).map(([key, { label }]) => (
                <MenuItem key={key} value={key}>
                  {label}
                </MenuItem>
              ))}
            </Select>
          ) : (
            <Chip
              label={privacyLevelMap[privacyLevel]?.label || privacyLevel}
              size="small"
              variant="outlined"
            />
          )}
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <EventIcon sx={{ mr: 1, fontSize: 20, color: 'text.secondary' }} />
          <Typography variant="body2" sx={{ mr: 1 }}>创建时间:</Typography>
          <Typography variant="body2" color="text.secondary">
            {formattedDate}
          </Typography>
        </Box>
      </Stack>

      {/* 服务用户授权视图: 服务记录入口 */}
      {!isAuthorizer && status === 'ACTIVE' && (
        <Box sx={{ mb: 2 }}>
          <Button
            variant="text"
            color="primary"
            endIcon={servicesExpanded ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
            onClick={toggleServicesExpanded}
            sx={{ textTransform: 'none', pl: 0 }}
          >
            相关服务和记录
          </Button>

          <Collapse in={servicesExpanded}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, mt: 1, ml: 2 }}>
              {/* 病理服务 */}
              <Button
                variant="outlined"
                size="small"
                startIcon={<MedicalServicesIcon />}
                sx={{ justifyContent: 'flex-start' }}
                onClick={() => {
                  // 先设置服务上下文
                  if (auth.privacy_level || auth.privacyLevel) {
                    const privacyLevel = (auth.privacy_level || auth.privacyLevel) as any;

                    // 明确设置授权和患者信息，但清除病理信息
                    serviceUserContext.setAuthorization(
                      auth.id,
                      auth.authorizer_id,
                      privacyLevel,
                      auth.status as any
                    );
                    serviceUserContext.setPatient(
                      patientInfo?.id || '',
                      patientInfo?.name || ''
                    );

                    // 在下一个事件循环中导航，确保上下文设置已完成
                    setTimeout(() => {
                      window.location.href = `/service-diseases?authId=${auth.id}&patientId=${patientInfo?.id || ''}&t=${Date.now()}`;
                    }, 100);
                  } else {
                    // 如果没有权限级别，直接导航
                    window.location.href = `/service-diseases?authId=${auth.id}&patientId=${patientInfo?.id || ''}&t=${Date.now()}`;
                  }
                }}
              >
                病理管理
              </Button>

              {/* 服务记录 */}
              <Button
                variant="outlined"
                size="small"
                startIcon={<DescriptionIcon />}
                sx={{ justifyContent: 'flex-start' }}
                onClick={() => {
                  // 先设置服务上下文
                  if (auth.privacy_level || auth.privacyLevel) {
                    const privacyLevel = (auth.privacy_level || auth.privacyLevel) as any;

                    // 明确设置授权和患者信息，但清除病理信息
                    serviceUserContext.setAuthorization(
                      auth.id,
                      auth.authorizer_id,
                      privacyLevel,
                      auth.status as any
                    );
                    serviceUserContext.setPatient(
                      patientInfo?.id || '',
                      patientInfo?.name || ''
                    );

                    // 在下一个事件循环中导航，确保上下文设置已完成
                    setTimeout(() => {
                      window.location.href = `/service-records?authId=${auth.id}&t=${Date.now()}`;
                    }, 100);
                  } else {
                    // 如果没有权限级别，直接导航
                    window.location.href = `/service-records?authId=${auth.id}&t=${Date.now()}`;
                  }
                }}
              >
                记录管理
              </Button>

              {/* 服务报告 */}
              <Button
                variant="outlined"
                size="small"
                startIcon={<AssessmentIcon />}
                sx={{ justifyContent: 'flex-start' }}
                onClick={() => {
                  // 先设置服务上下文
                  if (auth.privacy_level || auth.privacyLevel) {
                    const privacyLevel = (auth.privacy_level || auth.privacyLevel) as any;

                    // 明确设置授权和患者信息，但清除病理信息
                    serviceUserContext.setAuthorization(
                      auth.id,
                      auth.authorizer_id,
                      privacyLevel,
                      auth.status as any
                    );
                    serviceUserContext.setPatient(
                      patientInfo?.id || '',
                      patientInfo?.name || ''
                    );

                    // 在下一个事件循环中导航，确保上下文设置已完成
                    setTimeout(() => {
                      window.location.href = `/service-reports?authId=${auth.id}&t=${Date.now()}`;
                    }, 100);
                  } else {
                    // 如果没有权限级别，直接导航
                    window.location.href = `/service-reports?authId=${auth.id}&t=${Date.now()}`;
                  }
                }}
              >
                报告管理
              </Button>
            </Box>
          </Collapse>
        </Box>
      )}

      {/* 底部: 操作按钮 */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <FormControlLabel
          control={
            <Switch
              checked={localSwitchState}
              onChange={handleSwitchChange}
              disabled={isRevoked}
              color="success"
              sx={{
                '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                  backgroundColor: '#4caf50',
                },
                '& .MuiSwitch-switchBase.Mui-checked': {
                  color: '#4caf50',
                  '&:hover': {
                    backgroundColor: 'rgba(76, 175, 80, 0.08)',
                  },
                },
                '& .MuiSwitch-track': {
                  backgroundColor: '#f44336',
                },
              }}
            />
          }
          label={
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <SecurityIcon sx={{ fontSize: 16, mr: 0.5 }} />
              <Typography variant="body2">
                {isRevoked ? '已撤销' : (localSwitchState ? '已同意' : '未同意')}
              </Typography>
            </Box>
          }
        />

        {/* 只有在真正撤销状态时才显示删除按钮 */}
        {status === 'REVOKED' && onDelete && (
          <Tooltip title="删除已撤销的授权记录">
            <IconButton
              color="error"
              size="small"
              onClick={() => onDelete(auth)}
            >
              <DeleteIcon />
            </IconButton>
          </Tooltip>
        )}
      </Box>
    </Paper>
  );
};

export default AuthorizationCard;