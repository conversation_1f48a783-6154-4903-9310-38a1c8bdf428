/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('patient_logs', (table) => {
    table.uuid('id').primary();
    table.uuid('patient_id').notNullable().references('id').inTable('patients');
    table.uuid('user_id').notNullable().references('id').inTable('users');
    table.enum('action', ['CREATE', 'UPDATE', 'DELETE']).notNullable();
    table.text('details').notNullable(); // JSON格式存储详细操作信息
    table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now()).notNullable();
    
    // 添加索引以提高查询性能
    table.index(['patient_id']);
    table.index(['user_id']);
    table.index(['action']);
    table.index(['created_at']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('patient_logs');
};
