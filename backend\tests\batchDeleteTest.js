const axios = require('axios');

// 配置
const API_URL = 'http://localhost:3001';
let token = null;

// 测试管理员用户
const adminUser = {
  username: 'adminuser_' + Date.now() + '_' + Math.floor(Math.random() * 10000),
  email: `adminuser_${Date.now()}_${Math.floor(Math.random() * 10000)}@example.com`,
  password: 'Test123456',
  phoneNumber: '138' + Math.floor(Math.random() * 100000000).toString().padStart(8, '0'),
  role: 'ADMIN',
  level: 'PROFESSIONAL'
};

// 批量删除测试函数
async function runBatchDeleteTest() {
  try {
    console.log('=== 开始批量删除测试 ===');
    
    // 1. 注册测试管理员
    console.log('1. 注册测试管理员...');
    await axios.post(`${API_URL}/register`, adminUser);
    console.log('注册成功');
    
    // 2. 登录
    console.log('2. 登录获取token...');
    const loginResponse = await axios.post(`${API_URL}/login`, {
      username: adminUser.username,
      password: adminUser.password
    });
    token = loginResponse.data.token;
    console.log('登录成功');
    
    // 3. 创建测试患者
    console.log('3. 创建测试患者数据...');
    const patientCount = 5;
    const patients = [];
    
    for (let i = 0; i < patientCount; i++) {
      const isPrimary = i === 0 ? 1 : 0;
      const patient = {
        name: `批量删除测试患者${i+1}`,
        gender: i % 2 === 0 ? '男' : '女',
        phoneNumber: `139${String(i+1).padStart(8, '0')}`,
        email: `batchdeletepatient${i+1}@test.com`,
        address: `测试地址${i+1}`,
        birthDate: `199${i%9+1}-01-01`,
        bloodType: ['A', 'B', 'AB', 'O'][i % 4],
        emergencyContactName: `紧急联系人${i+1}`,
        emergencyContactPhone: `137${String(i+1).padStart(8, '0')}`,
        isPrimary
      };
      
      try {
        const response = await axios.post(
          `${API_URL}/patients`,
          patient,
          { headers: { Authorization: `Bearer ${token}` } }
        );
        patients.push(response.data.patient);
        console.log(`- 创建患者${i+1}成功，ID: ${response.data.patient.id}`);
      } catch (error) {
        console.error(`- 创建患者${i+1}失败:`, error.response?.data || error.message);
        break;
      }
    }
    
    console.log(`成功创建了 ${patients.length} 个测试患者`);
    
    // 4. 获取患者列表
    console.log('\n4. 获取患者列表...');
    const listResponse = await axios.get(
      `${API_URL}/patients`,
      { headers: { Authorization: `Bearer ${token}` } }
    );
    console.log(`获取到 ${listResponse.data.length} 个患者`);
    
    // 5. 批量删除患者
    console.log('\n5. 批量删除患者...');
    const patientIds = patients.map(p => p.id);
    console.log(`待删除患者IDs: ${patientIds.join(', ')}`);
    
    let deletedCount = 0;
    for (const id of patientIds) {
      try {
        await axios.delete(
          `${API_URL}/patients/${id}`,
          { headers: { Authorization: `Bearer ${token}` } }
        );
        deletedCount++;
        console.log(`- 成功删除患者: ${id}`);
      } catch (error) {
        console.error(`- 删除患者 ${id} 失败:`, error.response?.data || error.message);
      }
    }
    
    // 6. 验证删除结果
    console.log('\n6. 验证删除结果...');
    const verifyResponse = await axios.get(
      `${API_URL}/patients`,
      { headers: { Authorization: `Bearer ${token}` } }
    );
    
    // 检查是否所有患者都被标记为删除（在API结果中不再返回）
    const remainingPatients = verifyResponse.data.filter(p => patientIds.includes(p.id));
    
    console.log(`- 成功删除了 ${deletedCount}/${patientIds.length} 个患者`);
    console.log(`- 列表中剩余测试患者: ${remainingPatients.length}个`);
    
    if (remainingPatients.length === 0) {
      console.log('✅ 批量删除测试通过：所有患者都已成功删除');
    } else {
      console.log('❌ 批量删除测试失败：仍有患者未被删除');
    }
    
    console.log('=== 批量删除测试完成 ===');
    
  } catch (error) {
    console.error('测试失败:', error.response?.data || error.message);
  }
}

// 执行测试
runBatchDeleteTest(); 