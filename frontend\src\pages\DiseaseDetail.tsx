import React, { useState, useEffect, forwardRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  TextField,
  Button,
  Snackbar,
  Alert as MuiAlert,
  AlertProps,
  AlertColor,
  CircularProgress,
  Paper,
  MenuItem,
  Divider,
  FormControl,
  InputLabel,
  Select,
  Switch,
  FormControlLabel
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import { useAuthStore } from '../store/authStore';
import { Disease, DiseaseStages, DiseaseStageLabels } from '../types/disease';
import * as diseaseService from '../services/diseaseService';
import * as patientService from '../services/patientService';
import { Patient } from '../types/patient';

// 通知接口定义
interface Notification {
  open: boolean;
  message: string;
  type: AlertColor;
}

// 创建Alert组件的转发引用版本，解决类型问题
const Alert = forwardRef<HTMLDivElement, AlertProps>(
  function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
  }
);

// 格式化日期函数
const formatDate = (dateString?: string) => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleDateString();
};

// 格式化日期时间函数，显示完整的日期和时间
const formatDateTime = (dateString?: string) => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;
};

// 疾病详情组件
const DiseaseDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const token = useAuthStore((state) => state.token);
  const [disease, setDisease] = useState<Disease | null>(null);
  const [patient, setPatient] = useState<Patient | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [editing, setEditing] = useState<boolean>(false);
  const [editedDisease, setEditedDisease] = useState<Partial<Disease>>({});
  const [notification, setNotification] = useState<Notification>({
    open: false,
    message: '',
    type: 'success'
  });

  // 获取疾病详情
  useEffect(() => {
    const fetchDiseaseAndPatient = async () => {
      try {
        setLoading(true);
        if (!token) {
          console.log('未找到token，重定向到登录页');
          navigate('/login');
          return;
        }

        // 获取疾病详情
        const diseaseData = await diseaseService.getDisease(id as string, { includeDeleted: true });
        setDisease(diseaseData);
        setEditedDisease(diseaseData);
        
        // 获取关联患者信息
        const patientData = await patientService.getPatient(diseaseData.patientId);
        setPatient(patientData);
        
        // 检查URL参数是否包含edit=true，如果是，则自动进入编辑模式
        const searchParams = new URLSearchParams(window.location.search);
        if (searchParams.get('edit') === 'true') {
          setEditing(true);
        }
        
        setLoading(false);
      } catch (err: any) {
        console.error('获取疾病详情失败', err);
        console.error('详细错误信息:', err.response?.data, '状态码:', err.response?.status);
        setError('获取疾病详情失败');
        setLoading(false);
      }
    };

    if (id) {
      fetchDiseaseAndPatient();
    }
  }, [id, navigate, token]);

  // 处理输入变化
  const handleInputChange = (field: keyof Disease, value: any) => {
    setEditedDisease(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 开始编辑
  const handleEdit = () => {
    if (disease) {
      setEditedDisease(disease);
      setEditing(true);
    }
  };

  // 取消编辑
  const handleCancel = () => {
    setEditing(false);
    if (disease) {
      setEditedDisease(disease);
    }
  };

  // 保存更改
  const handleSave = async () => {
    if (!disease || !editedDisease) return;
    
    try {
      // 更新疾病记录
      await diseaseService.updateDisease(disease.id, editedDisease);
      
      // 获取更新后的疾病详情
      const updatedDisease = await diseaseService.getDisease(disease.id, { includeDeleted: true });
      setDisease(updatedDisease);
      setEditedDisease(updatedDisease);
      setEditing(false);
      
      setNotification({
        open: true,
        message: '疾病信息已更新',
        type: 'success'
      });
    } catch (err: any) {
      console.error('更新疾病信息失败', err);
      setNotification({
        open: true,
        message: '更新疾病信息失败: ' + (err.response?.data?.message || err.message),
        type: 'error'
      });
    }
  };

  // 返回上一页
  const handleBack = () => {
    navigate('/diseases');
  };

  // 处理通知关闭
  const handleCloseNotification = () => {
    setNotification(prev => ({ ...prev, open: false }));
  };

  return (
    <Box sx={{ 
      m: 0, // 移除外边距，使用Layout提供的统一10px间距
      width: '100%' 
    }}>
      <Box sx={{ 
        display: 'flex', 
        flexDirection: { xs: 'column', sm: 'row' }, 
        justifyContent: 'space-between', 
        alignItems: { xs: 'flex-start', sm: 'center' }, 
        mb: 3,
        gap: { xs: 2, sm: 0 } // 在移动端添加间距
      }}>
        <Typography 
          variant="h5" 
          component="h1"
          sx={{ fontWeight: 500, fontSize: { xs: '1.3rem', sm: '1.5rem' } }}
        >
          {disease ? `${disease.name} 详情` : '疾病详情'}
        </Typography>
        <Box sx={{ display: 'flex', width: { xs: '100%', sm: 'auto' } }}>
          <Button 
            variant="outlined"
            sx={{ mr: 1, flex: { xs: 1, sm: 'none' } }}
            onClick={handleBack}
          >
            返回
          </Button>
          {!editing ? (
            <Button 
              variant="contained"
              startIcon={<EditIcon />}
              onClick={handleEdit}
              sx={{ flex: { xs: 1, sm: 'none' } }}
            >
              编辑
            </Button>
          ) : (
            <>
              <Button
                variant="outlined"
                sx={{ mr: 1, flex: { xs: 1, sm: 'none' } }}
                onClick={handleCancel}
              >
                取消
              </Button>
              <Button
                variant="contained"
                color="primary"
                onClick={handleSave}
                sx={{ flex: { xs: 1, sm: 'none' } }}
              >
                保存
              </Button>
            </>
          )}
        </Box>
      </Box>

      <Divider sx={{ mb: { xs: 2, md: 3 } }} />

      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {!loading && !error && disease && (
        <Paper 
          elevation={0}
          sx={{ 
            p: { xs: 2, md: 3 }, 
            border: '1px solid #e0e0e0', 
            borderRadius: 2,
            width: '100%'
          }}
        >
          {/* 基本信息 */}
          <Typography variant="h6" sx={{ mb: 2, borderBottom: '1px solid #eee', pb: 1 }}>
            基本信息
          </Typography>
          <Box sx={{ mb: 4 }}>
            <Box sx={{ 
              display: 'grid', 
              gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)' },
              gap: 2
            }}>
              {editing ? (
                <TextField
                  fullWidth
                  required
                  label="疾病名称"
                  value={editedDisease.name || ''}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  variant="outlined"
                />
              ) : (
                <Box>
                  <Typography variant="body2" color="text.secondary">疾病名称</Typography>
                  <Typography variant="body1">{disease.name}</Typography>
                </Box>
              )}
              
              {editing ? (
                <FormControl fullWidth>
                  <InputLabel id="stage-label">疾病阶段</InputLabel>
                  <Select
                    labelId="stage-label"
                    value={editedDisease.stage || DiseaseStages.INITIAL}
                    onChange={(e) => handleInputChange('stage', e.target.value)}
                    label="疾病阶段"
                  >
                    {Object.values(DiseaseStages).map((stage) => (
                      <MenuItem key={stage} value={stage}>
                        {DiseaseStageLabels[stage as DiseaseStages]}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              ) : (
                <Box>
                  <Typography variant="body2" color="text.secondary">疾病阶段</Typography>
                  <Typography variant="body1">
                    {DiseaseStageLabels[disease.stage as DiseaseStages] || disease.stage}
                  </Typography>
                </Box>
              )}
            </Box>
            
            <Box sx={{ 
              display: 'grid', 
              gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)' },
              gap: 2,
              mt: 2
            }}>
              {editing ? (
                <TextField
                  fullWidth
                  label="诊断日期"
                  type="date"
                  value={editedDisease.diagnosisDate ? editedDisease.diagnosisDate.split('T')[0] : ''}
                  onChange={(e) => handleInputChange('diagnosisDate', e.target.value)}
                  InputLabelProps={{ shrink: true }}
                  variant="outlined"
                />
              ) : (
                <Box>
                  <Typography variant="body2" color="text.secondary">诊断日期</Typography>
                  <Typography variant="body1">{formatDate(disease.diagnosisDate)}</Typography>
                </Box>
              )}
              
              <Box>
                <Typography variant="body2" color="text.secondary">患者姓名</Typography>
                <Typography variant="body1">{patient?.name || '-'}</Typography>
              </Box>
            </Box>
            
            {editing && (
              <Box sx={{ mt: 2 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={Boolean(editedDisease.isPrivate)}
                      onChange={(e) => handleInputChange('isPrivate', e.target.checked ? 1 : 0)}
                      color="primary"
                    />
                  }
                  label="设为隐私记录"
                />
              </Box>
            )}
          </Box>

          {/* 描述和治疗 */}
          <Typography variant="h6" sx={{ mb: 2, borderBottom: '1px solid #eee', pb: 1 }}>
            详细信息
          </Typography>
          <Box sx={{ mb: 4 }}>
            {editing ? (
              <TextField
                fullWidth
                label="疾病描述"
                value={editedDisease.description || ''}
                onChange={(e) => handleInputChange('description', e.target.value)}
                variant="outlined"
                multiline
                rows={4}
                sx={{ mb: 2 }}
              />
            ) : (
              <Box sx={{ mb: 3 }}>
                <Typography variant="body2" color="text.secondary">疾病描述</Typography>
                <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                  {disease.description || '-'}
                </Typography>
              </Box>
            )}
            
            {editing ? (
              <TextField
                fullWidth
                label="遗嘱及用药或备注"
                value={editedDisease.treatment || ''}
                onChange={(e) => handleInputChange('treatment', e.target.value)}
                variant="outlined"
                multiline
                rows={4}
              />
            ) : (
              <Box>
                <Typography variant="body2" color="text.secondary">遗嘱及用药或备注</Typography>
                <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                  {disease.treatment || '-'}
                </Typography>
              </Box>
            )}
          </Box>

          {/* 系统信息 */}
          <Typography variant="h6" sx={{ mb: 2, borderBottom: '1px solid #eee', pb: 1 }}>
            系统信息
          </Typography>
          <Box sx={{ 
            display: 'grid', 
            gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)' },
            gap: 2
          }}>
            <Box>
              <Typography variant="body2" color="text.secondary">创建时间</Typography>
              <Typography variant="body1">{formatDateTime(disease.createdAt)}</Typography>
            </Box>
            <Box>
              <Typography variant="body2" color="text.secondary">更新时间</Typography>
              <Typography variant="body1">{formatDateTime(disease.updatedAt)}</Typography>
            </Box>
          </Box>

          {/* 底部编辑操作按钮 - 仅在编辑模式下显示（放在表单内部） */}
          {editing && (
            <Box sx={{ 
              display: 'flex', 
              justifyContent: 'flex-end', 
              mt: 4,
              pt: 2,
              borderTop: '1px solid #eee'
            }}>
              <Button
                variant="outlined"
                sx={{ mr: 1 }}
                onClick={handleCancel}
              >
                取消
              </Button>
              <Button
                variant="contained"
                color="primary"
                onClick={handleSave}
              >
                保存
              </Button>
            </Box>
          )}
        </Paper>
      )}
      
      <Snackbar 
        open={notification.open} 
        autoHideDuration={6000} 
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseNotification} severity={notification.type}>
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default DiseaseDetail; 