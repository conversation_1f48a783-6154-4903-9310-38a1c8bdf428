const { Model, snakeCaseMappers } = require('objection');
const path = require('path');

class Record extends Model {
  // 添加下划线命名映射配置
  static get columnNameMappers() {
    return snakeCaseMappers();
  }

  static get tableName() {
    return 'records';
  }

  static get idColumn() {
    return 'id';
  }

  // 字段验证规则
  static get jsonSchema() {
    return {
      type: 'object',
      required: ['title', 'recordType', 'userId'],
      properties: {
        id: { type: 'string' },
        title: { type: 'string', minLength: 1, maxLength: 255 },
        content: { type: ['string', 'null'] },
        data: { type: ['object', 'null'] },
        recordDate: { type: ['string', 'null'] },
        description: { type: ['string', 'null'], maxLength: 1000 },
        patientId: { type: ['string', 'null'] },
        diseaseId: { type: ['string', 'null'] },
        userId: { type: 'string' },
        createdBy: { type: 'string' },
        recordType: { type: 'string' },
        primaryType: { type: ['string', 'null'] },
        typeTagsJson: { type: ['string', 'null'] },
        stageTags: { type: ['string', 'null'] },
        stageNode: { type: ['string', 'null'] },
        stagePhase: { type: ['string', 'null'] },
        severity: { type: ['integer', 'null'] },
        isPrivate: { type: 'boolean', default: false },
        isImportant: { type: 'boolean', default: false },
        customTags: { type: ['string', 'null'] },
        deletedAt: { type: ['string', 'null'] }
      }
    };
  }

  // 关系定义
  static get relationMappings() {
    const User = require('./User');
    const Patient = require('./Patient');
    const Disease = require('./Disease');
    const Attachment = require('./Attachment');

    return {
      // 用户关系
      user: {
        relation: Model.BelongsToOneRelation,
        modelClass: User,
        join: {
          from: 'records.userId',
          to: 'users.id'
        }
      },
      // 创建者关系
      creator: {
        relation: Model.BelongsToOneRelation,
        modelClass: User,
        join: {
          from: 'records.createdBy',
          to: 'users.id'
        }
      },
      // 患者关系
      patient: {
        relation: Model.BelongsToOneRelation,
        modelClass: Patient,
        join: {
          from: 'records.patientId',
          to: 'patients.id'
        }
      },
      // 疾病关系
      disease: {
        relation: Model.BelongsToOneRelation,
        modelClass: Disease,
        join: {
          from: 'records.diseaseId',
          to: 'diseases.id'
        }
      },
      // 附件关系
      attachments: {
        relation: Model.HasManyRelation,
        modelClass: Attachment,
        join: {
          from: 'records.id',
          to: 'attachments.recordId'
        }
      }
    };
  }

  // 软删除方法
  softDelete() {
    return this.$query().patch({
      deletedAt: new Date().toISOString()
    });
  }

  // 获取类型标签
  get typeTags() {
    if (!this.typeTagsJson) return [];
    try {
      return JSON.parse(this.typeTagsJson);
    } catch (e) {
      return [];
    }
  }

  // 设置类型标签
  set typeTags(value) {
    this.typeTagsJson = JSON.stringify(value);
  }

  // 获取自定义标签
  get tags() {
    if (!this.customTags) return [];
    try {
      return this.customTags.split(',').filter(tag => tag.trim());
    } catch (e) {
      return [];
    }
  }

  // 设置自定义标签
  set tags(value) {
    if (Array.isArray(value)) {
      this.customTags = value.join(',');
    }
  }
}

module.exports = Record; 