const net = require('net');

/**
 * 检查端口是否可用
 * @param {number} port - 要检查的端口号
 * @returns {Promise<boolean>} - 如果端口可用，则返回true；否则返回false
 */
function checkPort(port) {
  return new Promise((resolve) => {
    const server = net.createServer();
    
    server.once('error', (err) => {
      if (err.code === 'EADDRINUSE') {
        // 端口已被占用
        resolve(false);
      } else {
        // 其他错误
        console.error(`检查端口 ${port} 时出错:`, err);
        resolve(false);
      }
    });
    
    server.once('listening', () => {
      // 端口可用，关闭服务器
      server.close(() => {
        resolve(true);
      });
    });
    
    server.listen(port);
  });
}

/**
 * 查找第一个可用的端口
 * @param {number} startPort - 起始端口号
 * @param {number} endPort - 结束端口号
 * @returns {Promise<number>} - 返回第一个可用的端口号，如果没有可用端口则返回null
 */
async function findAvailablePort(startPort, endPort) {
  for (let port = startPort; port <= endPort; port++) {
    const isAvailable = await checkPort(port);
    if (isAvailable) {
      return port;
    }
  }
  
  return null;
}

module.exports = {
  checkPort,
  findAvailablePort
}; 