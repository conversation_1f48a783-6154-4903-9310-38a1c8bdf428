const jwt = require('jsonwebtoken');
const knex = require('knex')(require('../../knexfile').development);

// JWT密钥，应从环境变量获取
const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret';

/**
 * 身份验证中间件
 * 验证请求头中的JWT令牌，并将用户ID添加到请求对象中
 */
const auth = async (req, res, next) => {
  try {
    // 首先从请求头获取授权信息
    let authHeader = req.headers.authorization;
    
    // 如果请求头中没有token，尝试从URL查询参数获取
    if (!authHeader && req.query.token) {
      authHeader = `Bearer ${req.query.token}`;
      req.headers.authorization = authHeader;
    }
    
    // 如果没有找到任何形式的token
    if (!authHeader) {
      return res.status(401).json({ error: '未提供授权令牌' });
    }

    // 检查授权格式是否正确（Bearer token）
    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return res.status(401).json({ error: '授权格式无效' });
    }

    const token = parts[1];

    try {
      // 验证令牌
      const decoded = jwt.verify(token, JWT_SECRET);
      
      // 检查用户是否存在
      const user = await knex('users')
        .where('id', decoded.id)
        .first();
      
      if (!user) {
        return res.status(401).json({ error: '用户不存在' });
      }

      // 将用户ID添加到请求对象中（同时使用驼峰命名和下划线命名）
      req.userId = decoded.id;  // 驼峰命名
      req.user_id = decoded.id; // 下划线命名
      req.user = user;
      
      // 确保req.user中也有id属性，并且和userId保持一致
      if (!req.user.id) {
        req.user.id = decoded.id;
      }
      
      next();
    } catch (jwtError) {
      if (jwtError.name === 'TokenExpiredError') {
        return res.status(401).json({ error: '令牌已过期' });
      }
      
      if (jwtError.name === 'JsonWebTokenError') {
        return res.status(401).json({ error: '无效的令牌' });
      }
      
      return res.status(401).json({ error: '授权验证失败' });
    }
  } catch (error) {
    console.error('认证中间件异常:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
};

/**
 * 管理员权限验证中间件
 * 验证用户是否具有ADMIN角色
 */
const adminOnly = (req, res, next) => {
  try {
    // 确保用户已经通过了auth中间件的验证
    if (!req.user) {
      return res.status(401).json({ error: '未授权访问' });
    }
    
    // 检查用户角色是否为ADMIN
    if (req.user.role !== 'ADMIN') {
      return res.status(403).json({ 
        error: '权限不足',
        message: '该操作需要管理员权限'
      });
    }
    
    // 用户是管理员，继续执行
    next();
  } catch (error) {
    console.error('管理员权限验证中间件异常:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
};

// 添加isAuthenticated作为auth的别名，保持向后兼容性
const isAuthenticated = auth;
// 添加authenticate作为auth的别名，供路由文件使用
const authenticate = auth;

module.exports = { auth, isAuthenticated, authenticate, adminOnly }; 