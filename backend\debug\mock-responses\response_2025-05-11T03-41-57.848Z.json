{"summary": "这是一个模拟的AI健康分析报告摘要。由于LLM API认证失败，系统返回了此模拟数据。请配置正确的API密钥以获取真实分析。", "differentialDiagnosis": {"possibleConditions": [{"condition": "模拟诊断A", "probability": 0.85, "description": "这是模拟诊断A的描述", "evidenceFrom": ["模拟症状1", "模拟症状2"], "confirmationMethods": ["模拟检查方法1", "模拟检查方法2"], "icd10Code": "Z00.0"}, {"condition": "模拟诊断B", "probability": 0.65, "description": "这是模拟诊断B的描述", "evidenceFrom": ["模拟症状2", "模拟症状3"], "confirmationMethods": ["模拟检查方法2", "模拟检查方法3"], "icd10Code": "Z00.1"}]}, "emergencyGuidance": {"isEmergency": false, "emergency": false, "immediateActions": ["保持镇静", "按医嘱用药"], "nextSteps": ["预约随访", "记录症状变化"], "guidance": "这是模拟的紧急指导信息。无紧急情况，请定期随访。"}, "hospitalRecommendations": {"targetRegion": "模拟地区", "hospitals": [{"name": "模拟医院A", "level": "三级甲等", "department": "内科", "matchScore": 0.9, "advantages": ["模拟优势1", "模拟优势2"], "contactInfo": {"website": "http://hospital-a.example.com", "phone": "010-12345678", "wechatPublic": "hospital_a_wechat", "appointmentPlatform": "医院官网预约系统"}}, {"name": "模拟医院B", "level": "三级甲等", "department": "内科", "matchScore": 0.85, "advantages": ["模拟优势3", "模拟优势4"], "contactInfo": {"website": "http://hospital-b.example.com", "phone": "010-87654321", "wechatPublic": "hospital_b_wechat", "appointmentPlatform": "医院微信小程序"}}]}, "treatmentPlan": {"options": [{"name": "模拟治疗方案A", "description": "这是模拟治疗方案A的详细描述", "suitabilityScore": 0.9, "prognosisData": {"survivalRate": "95%", "remissionRate": "85%", "recurrenceRisk": "低"}, "budgetEstimation": {"minCost": 5000, "maxCost": 10000, "currency": "CNY", "insuranceCoverage": "基本医保可报销70%"}, "followUpPlan": ["一周后复查血常规", "一个月后随访评估治疗效果"]}, {"name": "模拟治疗方案B", "description": "这是模拟治疗方案B的详细描述", "suitabilityScore": 0.8, "prognosisData": {"survivalRate": "92%", "remissionRate": "80%", "recurrenceRisk": "中低"}, "budgetEstimation": {"minCost": 3000, "maxCost": 8000, "currency": "CNY", "insuranceCoverage": "基本医保可报销80%"}, "followUpPlan": ["两周后复查", "两个月后随访评估治疗效果"]}]}, "lifestyleAndMentalHealth": {"lifestyle": {"diet": ["均衡饮食，增加蔬果摄入", "控制盐分摄入", "增加全谷物食品"], "exercise": ["每周中等强度运动3-5次，每次30分钟", "避免久坐，每小时起身活动5分钟", "考虑加入瑜伽或太极等放松性运动"], "habits": ["保证每晚7-8小时充足睡眠", "建议戒烟", "限制饮酒，每周不超过2次，每次不超过2单位"]}, "mentalHealth": {"copingStrategies": ["尝试冥想或深呼吸放松技巧", "记日记表达情感", "学习时间管理技巧减轻压力"], "resources": ["保持社交活动", "必要时寻求心理咨询", "加入相关支持小组"]}}, "dashboardData": {"status": "稳定", "riskLevel": "moderate", "keyMetrics": {"bloodPressure": "正常范围", "bloodSugar": "略高", "cholesterol": "正常范围"}, "trend": "保持稳定", "nextCheckup": "2025-07-10", "isEmergency": false, "topHospital": "模拟医院A", "budgetRange": "5000-10000 CNY"}, "riskWarnings": [{"type": "血压", "level": "低风险", "description": "目前血压处于正常范围，继续保持健康生活方式"}, {"type": "血糖", "level": "中风险", "description": "血糖略高，建议减少精制糖摄入，定期监测"}], "is_chronic_disease": false, "disclaimer": "这是模拟的AI健康分析结果，仅用于测试系统集成。实际医疗决策请咨询专业医生。"}