const { Model, snakeCaseMappers } = require('objection');
const { v4: uuidv4 } = require('uuid');

class Patient extends Model {
  // 定义表名
  static get tableName() {
    return 'patients';
  }

  static get idColumn() {
    return 'id';
  }

  // 添加下划线命名映射配置
  static get columnNameMappers() {
    return snakeCaseMappers();
  }

  // 定义JSON模式验证
  static get jsonSchema() {
    return {
      type: 'object',
      required: ['userId', 'name', 'gender'],
      properties: {
        id: { type: 'string' },
        userId: { type: 'string' },
        name: { type: 'string', minLength: 1, maxLength: 100 },
        gender: { type: 'string', enum: ['男', '女', '其他'] },
        birthDate: { type: ['string', 'null'] },
        phoneNumber: { type: ['string', 'null'] },
        email: { type: ['string', 'null'] },
        address: { type: ['string', 'null'] },
        idCard: { type: ['string', 'null'] },
        medicareCard: { type: ['string', 'null'] },
        medicareLocation: { type: ['string', 'null'] },
        bloodType: { type: ['string', 'null'] },
        emergencyContactName: { type: ['string', 'null'] },
        emergencyContactPhone: { type: ['string', 'null'] },
        emergencyContactRelationship: { type: ['string', 'null'] },
        pastMedicalHistory: { type: ['string', 'null'] },
        familyMedicalHistory: { type: ['string', 'null'] },
        allergyHistory: { type: ['string', 'null'] },
        lastVisitDate: { type: ['string', 'null'] },
        isPrimary: { type: 'integer', default: 0 },
        createdAt: { type: 'string' },
        updatedAt: { type: 'string' },
        deletedAt: { type: ['string', 'null'] }
      }
    };
  }

  // 在查询中自动过滤已删除的记录
  static get modifiers() {
    return {
      notDeleted(builder) {
        builder.whereNull('deletedAt');
      }
    };
  }

  $beforeInsert() {
    this.id = this.id || uuidv4();
    this.createdAt = new Date().toISOString();
    this.updatedAt = this.createdAt;
  }

  $beforeUpdate() {
    this.updatedAt = new Date().toISOString();
  }

  static get relationMappings() {
    const Disease = require('./Disease');
    const User = require('./User');
    
    return {
      diseases: {
        relation: Model.HasManyRelation,
        modelClass: Disease,
        join: {
          from: 'patients.id',
          to: 'diseases.patientId'
        }
      },
      user: {
        relation: Model.BelongsToOneRelation,
        modelClass: User,
        join: {
          from: 'patients.userId',
          to: 'users.id'
        }
      }
    };
  }
}

module.exports = Patient; 