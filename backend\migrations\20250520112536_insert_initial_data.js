/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.transaction(async (trx) => {
    // 1. 管理员用户
    await trx('users').insert({
      id: knex.raw('gen_random_uuid()'),
      username: 'adr',
      email: '<EMAIL>',
      password_hash: 'Password@1',
      phone_number: '13391060505',
      avatar: 'https://i.postimg.cc/Fz2cpXtP/528.png',
      role: 'ADMIN',
      level: 'PROFESSIONAL',
      is_active: true,
      active_disease_limit: 5,
      ai_usage_count: 0,
      family_member_limit: 8,
      updated_at: new Date()
    });

    // 2. 用户等级限制
    await trx('user_level_limits').insert([
      {
        id: knex.raw('gen_random_uuid()'),
        level_type: 'PERSONAL',
        max_patients: 1,
        max_pathologies: 3,
        max_attachment_size: 2048,
        max_total_storage: 10240,
        max_ai_used: 3
      },
      {
        id: knex.raw('gen_random_uuid()'),
        level_type: 'FAMILY',
        max_patients: 4,
        max_pathologies: 5,
        max_attachment_size: 5120,
        max_total_storage: 25600,
        max_ai_used: 20
      },
      {
        id: knex.raw('gen_random_uuid()'),
        level_type: 'PROFESSIONAL',
        max_patients: 8,
        max_pathologies: 5,
        max_attachment_size: 10240,
        max_total_storage: 51200,
        max_ai_used: 50
      }
    ]);

    // 3. AI报告配置
    await trx('ai_report_configs').insert({
      id: 1,
      user_visible_fields: JSON.stringify([
        "summary",
        "emergencyGuidance",
        "hospitalRecommendations",
        "lifestyleAndMentalHealth",
        "differentialDiagnosis",
        "riskWarnings"
      ]),
      service_visible_fields: JSON.stringify([
        "summary",
        "differentialDiagnosis",
        "emergencyGuidance",
        "hospitalRecommendations",
        "treatmentPlan",
        "budgetEstimation",
        "crossRegionGuidance",
        "lifestyleAndMentalHealth",
        "riskWarnings"
      ])
    });
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.transaction(async (trx) => {
    await trx('users').where('username', 'adr').del();
    await trx('user_level_limits').whereIn('level_type', ['PERSONAL', 'FAMILY', 'PROFESSIONAL']).del();
    await trx('ai_report_configs').where('id', 1).del();
  });
};
