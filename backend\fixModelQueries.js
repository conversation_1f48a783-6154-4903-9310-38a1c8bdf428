/**
 * 修复模型文件中的驼峰命名
 */
const fs = require('fs');
const path = require('path');

// 驼峰命名到下划线命名的映射
const camelToSnakeMap = {
  // users表
  'passwordHash': 'password_hash',
  'phoneNumber': 'phone_number',
  'isActive': 'is_active',
  'lastLoginAt': 'last_login_at',
  'activeDiseaseLimit': 'active_disease_limit', 
  'aiUsageCount': 'ai_usage_count',
  'aiUsageResetAt': 'ai_usage_reset_at',
  'familyMemberLimit': 'family_member_limit',
  'updatedAt': 'updated_at',
  
  // user_level_limits表
  'levelType': 'level_type',
  'maxPatients': 'max_patients',
  'maxPathologies': 'max_pathologies',
  'maxAttachmentSize': 'max_attachment_size',
  'maxTotalStorage': 'max_total_storage',
  
  // subscriptions表
  'userId': 'user_id',
  'startDate': 'start_date',
  'endDate': 'end_date',
  'createdAt': 'created_at',
  
  // patients表
  'birthDate': 'birth_date',
  'idCard': 'id_card',
  'medicareCard': 'medicare_card',
  'medicareLocation': 'medicare_location',
  'emergencyContactName': 'emergency_contact_name',
  'emergencyContactPhone': 'emergency_contact_phone',
  'emergencyContactRelationship': 'emergency_contact_relationship',
  'pastMedicalHistory': 'past_medical_history',
  'familyMedicalHistory': 'family_medical_history',
  'allergyHistory': 'allergy_history',
  'bloodType': 'blood_type',
  'lastVisitDate': 'last_visit_date',
  'deletedAt': 'deleted_at',
  'isPrimary': 'is_primary',
  
  // diseases表
  'patientId': 'patient_id',
  'diagnosisDate': 'diagnosis_date',
  'isDeleted': 'is_deleted',
  'isPrivate': 'is_private',
  'isActive': 'is_active',
  'isChronic': 'is_chronic',
  
  // tag_categories表
  'isSystem': 'is_system',
  'createdBy': 'created_by',
  
  // tags表
  'categoryId': 'category_id',
  
  // record_tags表
  'recordId': 'record_id',
  'tagId': 'tag_id',
  
  // records表
  'recordDate': 'record_date',
  'diseaseId': 'disease_id',
  'recordType': 'record_type',
  'primaryType': 'primary_type',
  'typeTagsJson': 'type_tags_json',
  'stageTags': 'stage_tags',
  'stageNode': 'stage_node',
  'stagePhase': 'stage_phase',
  'isImportant': 'is_important',
  'customTags': 'custom_tags',
  
  // attachments表
  'fileName': 'file_name',
  'filePath': 'file_path',
  'fileType': 'file_type',
  'fileSize': 'file_size',
  'uploadedBy': 'uploaded_by',
  'uploadedAt': 'uploaded_at'
};

/**
 * 修复模型文件中的驼峰命名
 */
function fixModels() {
  console.log('开始修复模型文件中的驼峰命名...\n');
  
  const modelsDir = path.join(__dirname, 'models');
  
  // 确保目录存在
  if (!fs.existsSync(modelsDir)) {
    console.log('models目录不存在');
    return;
  }
  
  // 读取所有模型文件
  const modelFiles = fs.readdirSync(modelsDir).filter(file => file.endsWith('.js'));
  
  for (const file of modelFiles) {
    const filePath = path.join(modelsDir, file);
    
    try {
      // 读取文件内容
      const content = fs.readFileSync(filePath, 'utf8');
      let modifiedContent = content;
      let replacementCount = 0;
      
      // 修复 required: ['column1', 'column2'] 模式中的驼峰命名
      const requiredPattern = /required:\s*\[\s*(['"][^'"]*['"](?:\s*,\s*['"][^'"]*['"])*)\s*\]/g;
      
      modifiedContent = modifiedContent.replace(requiredPattern, (match, columnsStr) => {
        let columns = columnsStr.split(',').map(col => col.trim());
        let modified = false;
        
        // 替换数组中的每个列名
        const newColumns = columns.map(col => {
          // 去掉引号
          const colName = col.replace(/['"]/g, '');
          
          // 检查是否在映射表中
          if (camelToSnakeMap[colName]) {
            modified = true;
            replacementCount++;
            return col.replace(colName, camelToSnakeMap[colName]);
          }
          
          return col;
        });
        
        return modified ? `required: [${newColumns.join(', ')}]` : match;
      });
      
      // 修复 builder.whereNull('deletedAt') 模式中的驼峰命名
      for (const [camelCase, snakeCase] of Object.entries(camelToSnakeMap)) {
        // where条件模式
        const wherePattern = new RegExp(`(where[^\\(]*\\(['"])${camelCase}(['"][^\\)]*\\))`, 'g');
        modifiedContent = modifiedContent.replace(wherePattern, (match, prefix, suffix) => {
          replacementCount++;
          return `${prefix}${snakeCase}${suffix}`;
        });
        
        // whereNull模式
        const whereNullPattern = new RegExp(`(whereNull\\(['"])${camelCase}(['"]\\))`, 'g');
        modifiedContent = modifiedContent.replace(whereNullPattern, (match, prefix, suffix) => {
          replacementCount++;
          return `${prefix}${snakeCase}${suffix}`;
        });
        
        // orderBy模式
        const orderByPattern = new RegExp(`(orderBy\\(['"])${camelCase}(['"][^\\)]*\\))`, 'g');
        modifiedContent = modifiedContent.replace(orderByPattern, (match, prefix, suffix) => {
          replacementCount++;
          return `${prefix}${snakeCase}${suffix}`;
        });
      }
      
      // 只有在有修改时才写入文件
      if (replacementCount > 0) {
        // 创建备份
        fs.writeFileSync(`${filePath}.bak`, content);
        
        // 写入修改后的内容
        fs.writeFileSync(filePath, modifiedContent);
        
        console.log(`✅ 修复文件 ${file}: ${replacementCount} 处驼峰命名已替换`);
      } else {
        console.log(`✓ 文件 ${file} 未发现需要修复的问题`);
      }
    } catch (error) {
      console.error(`修复文件 ${file} 时出错:`, error);
    }
  }
  
  console.log('\n模型文件修复完成！');
}

// 执行修复
fixModels(); 