/**
 * AI报告服务
 * 整合匿名化、构建提示和调用LLM的过程，实现完整的AI报告生成流程
 */
const { v4: uuidv4 } = require('uuid');
const { callLlmService } = require('./llmService');
const { AIReport } = require('../../models/aiReport');
const Patient = require('../../models/Patient');
const Disease = require('../../models/Disease');
const Record = require('../../models/Record');
const fs = require('fs');
const path = require('path');
const { callVolcengineApi, callLLMModel } = require('./llmService');

/**
 * 获取医疗数据
 * @param {string} diseaseId 病理ID
 * @param {string} patientId 患者ID
 * @returns {Promise<Object>} 患者、病理和记录数据
 */
const getMedicalData = async (diseaseId, patientId) => {
  // 获取患者信息
  const patient = await Patient.query().findById(patientId);
  if (!patient) {
    throw new Error('未找到患者信息');
  }

  // 获取病理信息
  const disease = await Disease.query().findById(diseaseId);
  if (!disease) {
    throw new Error('未找到病理信息');
  }

  // 获取相关记录
  const records = await Record.query()
    .where({ disease_id: diseaseId })
    .orderBy('created_at', 'asc'); // 按时间顺序排序

  return {
    patient,
    disease,
    records
  };
};

/**
 * 处理LLM返回的内容
 * @param {string} content LLM返回的内容
 * @returns {Object} 解析后的JSON内容
 */
const processLlmResponse = (content) => {
  try {
    console.log('===== 开始处理LLM响应 =====');
    console.log('原始响应内容:', content);
    
    // 尝试解析JSON
    let jsonContent;
    
    // 有时LLM会在JSON前后添加文本，我们需要提取JSON部分
    const jsonMatch = content.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      console.log('找到JSON内容:', jsonMatch[0]);
      jsonContent = JSON.parse(jsonMatch[0]);
    } else {
      console.error('未找到有效的JSON内容');
      throw new Error('未找到有效的JSON内容');
    }
    
    // 验证必要字段是否存在
    const requiredFields = [
      'summary', 
      'emergencyGuidance', 
      'hospitalRecommendations', 
      'treatmentPlan',
      'lifestyleAndMentalHealth',
      'dashboardData',
      'disclaimer'
    ];
    
    const missingFields = requiredFields.filter(field => !jsonContent[field]);
    if (missingFields.length > 0) {
      console.warn(`LLM响应缺少以下字段: ${missingFields.join(', ')}`);
      
      // 为缺失字段提供默认值
      missingFields.forEach(field => {
        console.log(`为缺失字段 ${field} 提供默认值`);
        switch (field) {
          case 'summary':
            jsonContent.summary = '无法生成病情摘要，信息不足';
            break;
          case 'emergencyGuidance':
            jsonContent.emergencyGuidance = {
              isEmergency: false,
              immediateActions: [],
              nextSteps: []
            };
            break;
          case 'hospitalRecommendations':
            jsonContent.hospitalRecommendations = {
              targetRegion: '',
              hospitals: []
            };
            break;
          case 'treatmentPlan':
            jsonContent.treatmentPlan = {
              options: [],
              followUp: []
            };
            break;
          case 'lifestyleAndMentalHealth':
            jsonContent.lifestyleAndMentalHealth = {
              lifestyle: {
                diet: [
                  '保持均衡饮食，增加蔬菜水果摄入',
                  '控制盐分和糖分摄入',
                  '保持充分的水分摄入，每天至少8杯水',
                  '减少加工食品和高脂肪食物的摄入'
                ],
                exercise: [
                  '根据自身情况进行适量运动，每周至少150分钟中等强度活动',
                  '避免长时间久坐，每小时起身活动5分钟',
                  '可以选择步行、游泳或太极等低强度运动开始'
                ],
                habits: [
                  '保持规律的作息时间，确保充足睡眠',
                  '避免或减少烟酒摄入',
                  '养成定期体检的习惯'
                ]
              },
              mentalHealth: {
                copingStrategies: [
                  '学习简单的呼吸放松技巧，帮助缓解焦虑',
                  '保持社交联系，与亲友分享感受',
                  '设定合理的期望，接受治疗过程中的起伏'
                ],
                resources: [
                  '考虑寻求专业心理咨询师的帮助',
                  '加入病友支持小组，分享经验和情感支持',
                  '使用正念冥想等应用程序辅助放松'
                ]
              }
            };
            break;
          case 'dashboardData':
            jsonContent.dashboardData = {
              status: '未知',
              trend: 'stable',
              riskLevel: 'medium',
              isEmergency: false,
              topHospital: '',
              budgetRange: ''
            };
            break;
          case 'disclaimer':
            jsonContent.disclaimer = '本报告由AI生成，仅供参考，不构成医疗诊断或治疗建议。请咨询专业医生获取正式医疗意见。';
            break;
          default:
            jsonContent[field] = null;
        }
      });
    }
    
    // 修复紧急处置指南可能被包含在summary中的问题
    if (jsonContent.summary && jsonContent.summary.includes('紧急') && jsonContent.emergencyGuidance) {
      console.log('检测到紧急处置指南可能被包含在summary中，进行分离处理');
      
      // 如果emergencyGuidance的immediateActions为空，但summary中包含紧急处置信息
      if (jsonContent.emergencyGuidance.immediateActions.length === 0 && 
          (jsonContent.summary.includes('紧急处置') || jsonContent.summary.includes('立即就医'))) {
        
        // 尝试提取紧急处置信息
        const emergencyRegex = /紧急处置[：:]([\s\S]*?)(?=\n\n|\n##|$)/i;
        const match = jsonContent.summary.match(emergencyRegex);
        
        if (match && match[1]) {
          // 从summary中移除这部分内容
          jsonContent.summary = jsonContent.summary.replace(match[0], '').trim();
          
          // 提取的紧急处置信息
          const emergencyInfo = match[1].trim();
          
          // 更新emergencyGuidance
          jsonContent.emergencyGuidance.isEmergency = true;
          jsonContent.emergencyGuidance.immediateActions = [
            '请立即前往最近的医疗机构就诊',
            '携带所有相关的检查资料',
            emergencyInfo
          ];
          jsonContent.emergencyGuidance.nextSteps = [
            '遵循医生建议进行后续检查和治疗',
            '定期随访监测病情变化'
          ];
        }
      }
    }
    
    // 检查lifestyleAndMentalHealth是否有实质内容
    if (jsonContent.lifestyleAndMentalHealth) {
      const lifestyle = jsonContent.lifestyleAndMentalHealth.lifestyle || {};
      const mentalHealth = jsonContent.lifestyleAndMentalHealth.mentalHealth || {};
      
      // 检查diet数组是否为空或不存在
      if (!lifestyle.diet || lifestyle.diet.length === 0) {
        console.log('添加默认饮食建议');
        jsonContent.lifestyleAndMentalHealth.lifestyle = jsonContent.lifestyleAndMentalHealth.lifestyle || {};
        jsonContent.lifestyleAndMentalHealth.lifestyle.diet = [
          '保持均衡饮食，增加蔬菜水果摄入',
          '控制盐分和糖分摄入',
          '保持充分的水分摄入，每天至少8杯水',
          '减少加工食品和高脂肪食物的摄入'
        ];
      }
      
      // 检查exercise数组是否为空或不存在
      if (!lifestyle.exercise || lifestyle.exercise.length === 0) {
        console.log('添加默认运动建议');
        jsonContent.lifestyleAndMentalHealth.lifestyle = jsonContent.lifestyleAndMentalHealth.lifestyle || {};
        jsonContent.lifestyleAndMentalHealth.lifestyle.exercise = [
          '根据自身情况进行适量运动，每周至少150分钟中等强度活动',
          '避免长时间久坐，每小时起身活动5分钟',
          '可以选择步行、游泳或太极等低强度运动开始'
        ];
      }
      
      // 检查habits数组是否为空或不存在
      if (!lifestyle.habits || lifestyle.habits.length === 0) {
        console.log('添加默认习惯调整建议');
        jsonContent.lifestyleAndMentalHealth.lifestyle = jsonContent.lifestyleAndMentalHealth.lifestyle || {};
        jsonContent.lifestyleAndMentalHealth.lifestyle.habits = [
          '保持规律的作息时间，确保充足睡眠',
          '避免或减少烟酒摄入',
          '养成定期体检的习惯'
        ];
      }
      
      // 检查copingStrategies数组是否为空或不存在
      if (!mentalHealth.copingStrategies || mentalHealth.copingStrategies.length === 0) {
        console.log('添加默认心理应对策略');
        jsonContent.lifestyleAndMentalHealth.mentalHealth = jsonContent.lifestyleAndMentalHealth.mentalHealth || {};
        jsonContent.lifestyleAndMentalHealth.mentalHealth.copingStrategies = [
          '学习简单的呼吸放松技巧，帮助缓解焦虑',
          '保持社交联系，与亲友分享感受',
          '设定合理的期望，接受治疗过程中的起伏'
        ];
      }
      
      // 检查resources数组是否为空或不存在
      if (!mentalHealth.resources || mentalHealth.resources.length === 0) {
        console.log('添加默认心理资源建议');
        jsonContent.lifestyleAndMentalHealth.mentalHealth = jsonContent.lifestyleAndMentalHealth.mentalHealth || {};
        jsonContent.lifestyleAndMentalHealth.mentalHealth.resources = [
          '考虑寻求专业心理咨询师的帮助',
          '加入病友支持小组，分享经验和情感支持',
          '使用正念冥想等应用程序辅助放松'
        ];
      }
    }
    
    console.log('处理后的JSON内容:', JSON.stringify(jsonContent, null, 2));
    console.log('===== LLM响应处理完成 =====');
    
    return jsonContent;
  } catch (error) {
    console.error('解析LLM响应失败:', error);
    console.error('错误详情:', error.stack);
    // 返回基本结构，避免系统崩溃
    return {
      summary: '无法解析AI模型响应，请重试。',
      differentialDiagnosis: { possibleConditions: [], followUpQuestions: [] },
      emergencyGuidance: { isEmergency: false, immediateActions: [], nextSteps: [] },
      hospitalRecommendations: { targetRegion: '', hospitals: [] },
      treatmentPlan: { options: [], followUp: [] },
      lifestyleAndMentalHealth: {
        lifestyle: { 
          diet: [
            '保持均衡饮食，增加蔬菜水果摄入',
            '控制盐分和糖分摄入',
            '保持充分的水分摄入，每天至少8杯水',
            '减少加工食品和高脂肪食物的摄入'
          ],
          exercise: [
            '根据自身情况进行适量运动，每周至少150分钟中等强度活动',
            '避免长时间久坐，每小时起身活动5分钟',
            '可以选择步行、游泳或太极等低强度运动开始'
          ],
          habits: [
            '保持规律的作息时间，确保充足睡眠',
            '避免或减少烟酒摄入',
            '养成定期体检的习惯'
          ]
        },
        mentalHealth: { 
          copingStrategies: [
            '学习简单的呼吸放松技巧，帮助缓解焦虑',
            '保持社交联系，与亲友分享感受',
            '设定合理的期望，接受治疗过程中的起伏'
          ],
          resources: [
            '考虑寻求专业心理咨询师的帮助',
            '加入病友支持小组，分享经验和情感支持',
            '使用正念冥想等应用程序辅助放松'
          ]
        }
      },
      dashboardData: {
        status: '解析错误',
        trend: 'stable',
        riskLevel: 'medium',
        isEmergency: false,
        topHospital: '',
        budgetRange: ''
      },
      riskWarnings: ['系统无法正确解析AI响应'],
      disclaimer: '本报告由AI生成，仅供参考，不构成医疗诊断或治疗建议。请咨询专业医生获取正式医疗意见。'
    };
  }
};

/**
 * 生成报告标题
 * @param {Object} medicalData 医疗数据
 * @returns {string} 生成的报告标题
 */
const generateReportTitle = (medicalData) => {
  try {
    // 获取患者姓名和病历名称
    const patientName = medicalData.patient?.name || '未知患者';
    const diseaseName = medicalData.disease?.name || '未知病历';
    
    // 生成报告标题，格式：患者姓名_病历名称_辅医智能分析报告
    const reportTitle = `${patientName}_${diseaseName}_辅医智能分析报告`;
    console.log(`[AI报告] 生成报告标题: ${reportTitle}`);
    
    return reportTitle;
  } catch (error) {
    console.error('[AI报告] 生成标题失败:', error);
    // 返回默认标题，避免整个流程失败
    return '辅医智能分析报告';
  }
};

/**
 * 生成LLM请求正文
 * @param {Object} medicalData 医疗数据
 * @param {string} targetRegion 目标地区
 * @returns {string} 生成的LLM请求
 */
const generateLLMPrompt = (medicalData, targetRegion) => {
  try {
    console.log('开始生成LLM请求');
    
    // 获取患者基本信息
    const patient = medicalData.patient;
    const disease = medicalData.disease;
    const records = medicalData.records || [];
    
    // 构建患者基本信息
    let patientInfo = `患者信息：\n`;
    patientInfo += `姓名：${patient.name || '未知'}\n`;
    patientInfo += `性别：${patient.gender === 'MALE' ? '男' : patient.gender === 'FEMALE' ? '女' : '未知'}\n`;
    
    // 计算年龄
    if (patient.birthDate) {
      const birthDate = new Date(patient.birthDate);
      const today = new Date();
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      
      // 如果今年的生日还没过，年龄减1
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }
      
      patientInfo += `年龄：${age}岁\n`;
    }
    
    // 添加身高体重信息
    if (patient.height) patientInfo += `身高：${patient.height}cm\n`;
    if (patient.weight) patientInfo += `体重：${patient.weight}kg\n`;
    
    // 添加既往史
    if (patient.medical_history) {
      patientInfo += `既往史：${patient.medical_history}\n`;
    }
    
    // 添加过敏史
    if (patient.allergies) {
      patientInfo += `过敏史：${patient.allergies}\n`;
    }
    
    // 构建病历信息
    let diseaseInfo = `\n病历信息：\n`;
    diseaseInfo += `病历名称：${disease.name || '未知'}\n`;
    diseaseInfo += `主诉：${disease.chief_complaint || '未记录'}\n`;
    diseaseInfo += `现病史：${disease.present_illness_history || '未记录'}\n`;
    diseaseInfo += `检查结果：${disease.examination_results || '未记录'}\n`;
    diseaseInfo += `诊断结果：${disease.diagnosis || '未记录'}\n`;
    
    // 添加记录信息
    let recordsInfo = `\n就诊记录：\n`;
    if (records && records.length > 0) {
      records.forEach((record, index) => {
        recordsInfo += `记录${index + 1} [${new Date(record.created_at).toLocaleDateString()}]：\n`;
        recordsInfo += `${record.content || '无内容'}\n\n`;
      });
    } else {
      recordsInfo += `暂无就诊记录\n`;
    }
    
    // 添加目标地区信息
    let regionInfo = '';
    if (targetRegion) {
      regionInfo = `\n患者意向就医地区：${targetRegion}\n`;
    }
    
    // 构建最终请求
    const finalPrompt = `
请作为资深医疗AI助手，基于以下患者信息和医疗记录，生成一份全面的医疗分析报告。

${patientInfo}
${diseaseInfo}
${recordsInfo}
${regionInfo}

请提供以下格式的JSON响应：
{
  "summary": "对患者病情的综合分析，500字左右",
  "differentialDiagnosis": {
    "possibleConditions": [
      {
        "condition": "可能的诊断名称",
        "probability": 概率百分比,
        "description": "对该诊断的说明",
        "evidenceFrom": ["支持该诊断的证据1", "支持该诊断的证据2"],
        "confirmationMethods": ["建议的确诊方法1", "建议的确诊方法2"]
      }
    ],
    "followUpQuestions": ["建议进一步询问的问题1", "建议进一步询问的问题2"]
  },
  "emergencyGuidance": {
    "isEmergency": 紧急状态(布尔值),
    "immediateActions": ["紧急情况下应采取的措施1", "紧急情况下应采取的措施2"],
    "nextSteps": ["后续步骤1", "后续步骤2"]
  },
  "hospitalRecommendations": {
    "targetRegion": "${targetRegion || ''}",
    "hospitals": [
      {
        "name": "医院名称",
        "level": "医院级别",
        "department": "推荐科室",
        "matchScore": 匹配得分,
        "advantages": ["医院优势1", "医院优势2"],
        "contactInfo": {
          "phone": "联系电话",
          "website": "官网地址",
          "appointmentPlatform": "预约平台"
        }
      }
    ]
  },
  "treatmentPlan": {
    "options": [
      {
        "name": "治疗方案名称",
        "description": "治疗方案描述",
        "suitabilityScore": 适合度得分,
        "prognosisData": {
          "survivalRate": "存活率信息",
          "remissionRate": "缓解率信息",
          "recurrenceRisk": "复发风险"
        },
        "budgetEstimation": {
          "currency": "CNY",
          "minCost": 最低花费,
          "maxCost": 最高花费,
          "insuranceCoverage": "医保覆盖情况"
        },
        "followUpPlan": ["后续随访计划1", "后续随访计划2"]
      }
    ],
    "followUp": ["总体随访建议1", "总体随访建议2"]
  },
  "lifestyleAndMentalHealth": {
    "lifestyle": {
      "diet": ["饮食建议1", "饮食建议2"],
      "exercise": ["运动建议1", "运动建议2"],
      "habits": ["习惯调整建议1", "习惯调整建议2"]
    },
    "mentalHealth": {
      "copingStrategies": ["心理应对策略1", "心理应对策略2"],
      "resources": ["心理健康资源1", "心理健康资源2"]
    }
  },
  "dashboardData": {
    "status": "状态简述",
    "trend": "stable/improving/worsening",
    "riskLevel": "low/medium/high",
    "isEmergency": 紧急状态(布尔值),
    "topHospital": "顶级推荐医院",
    "budgetRange": "预算范围"
  },
  "riskWarnings": ["风险提示1", "风险提示2"],
  "disclaimer": "免责声明文本"
}

重要提示：
1. 所有内容必须基于患者的真实情况分析，严禁臆断
2. 保持专业、准确，符合医学规范
3. 适当考虑患者的年龄、性别及病史
4. 对于不确定信息，应明确指出并建议进一步检查
5. 推荐的医院和科室必须真实存在且适合患者情况
6. 返回格式必须是有效的JSON格式
`;

    console.log('LLM请求生成完成');
    return finalPrompt;
  } catch (error) {
    console.error('生成LLM请求时出错:', error);
    throw new Error(`生成LLM请求失败: ${error.message}`);
  }
};

/**
 * 解析LLM响应结果
 * @param {string} llmResponse LLM响应结果
 * @param {Object} medicalData 医疗数据
 * @returns {Object} 解析后的报告内容
 */
const parseLLMResponse = async (llmResponse, medicalData) => {
  try {
    console.log('开始解析LLM响应');
    
    // 处理LLM返回的内容
    const jsonContent = processLlmResponse(llmResponse);
    
    // 补充dashboardData
    if (jsonContent.dashboardData) {
      const dashboardData = jsonContent.dashboardData;
      
      // 确保dashboardData包含所有需要的字段
      if (!dashboardData.status) dashboardData.status = '分析完成';
      if (!dashboardData.trend) dashboardData.trend = 'stable';
      if (!dashboardData.riskLevel) dashboardData.riskLevel = 'medium';
      if (dashboardData.isEmergency === undefined) dashboardData.isEmergency = false;
      
      // 如果有紧急情况，更新dashboardData
      if (jsonContent.emergencyGuidance && jsonContent.emergencyGuidance.isEmergency) {
        dashboardData.isEmergency = true;
        dashboardData.riskLevel = 'high';
      }
      
      // 如果有医院推荐，更新topHospital
      if (jsonContent.hospitalRecommendations && 
          jsonContent.hospitalRecommendations.hospitals && 
          jsonContent.hospitalRecommendations.hospitals.length > 0) {
        dashboardData.topHospital = jsonContent.hospitalRecommendations.hospitals[0].name;
      }
      
      // 如果有治疗方案，更新budgetRange
      if (jsonContent.treatmentPlan && 
          jsonContent.treatmentPlan.options && 
          jsonContent.treatmentPlan.options.length > 0) {
        const option = jsonContent.treatmentPlan.options[0];
        if (option.budgetEstimation) {
          const currency = option.budgetEstimation.currency || 'CNY';
          const minCost = option.budgetEstimation.minCost;
          const maxCost = option.budgetEstimation.maxCost;
          if (minCost !== undefined && maxCost !== undefined) {
            dashboardData.budgetRange = `${minCost}-${maxCost} ${currency === 'CNY' ? '元' : currency}`;
          }
        }
      }
    }
    
    console.log('LLM响应解析完成');
    return jsonContent;
  } catch (error) {
    console.error('解析LLM响应时出错:', error);
    throw new Error(`解析LLM响应失败: ${error.message}`);
  }
};

/**
 * 生成AI报告
 * @param {string} diseaseId 病理ID
 * @param {string} patientId 患者ID
 * @param {string} userId 用户ID
 * @param {string} targetRegion 患者意向就医地区
 * @param {string} existingReportId 现有报告ID，如果提供则更新该报告而不是创建新报告
 * @returns {Promise<Object>} 生成的AI报告
 */
const generateAIReport = async (diseaseId, patientId, userId, targetRegion, existingReportId = null) => {
  let aiReport = null;
  let startTime = Date.now();
  
  // 创建一个处理状态标志，防止并发更新问题
  const processingKey = `${diseaseId}_${patientId}_${userId}_processing`;
  const isProcessing = global[processingKey];
  
  if (isProcessing) {
    console.log(`[AI报告] 已有相同请求正在处理中: ${processingKey}`);
    throw new Error('已有相同请求正在处理中，请等待处理完成');
  }
  
  // 设置处理中标志
  global[processingKey] = true;
  
  try {
    console.log(`[开始] 生成AI报告 - 病历ID:${diseaseId}, 患者ID:${patientId}, 用户ID:${userId}, 目标地区:${targetRegion || '未指定'}, 现有报告ID:${existingReportId || '无'}`);
    
    // 1. 获取医疗数据（提前获取，以便使用患者和病理信息生成标题）
    console.log(`[AI报告] 获取医疗数据开始 - 耗时:${Date.now() - startTime}ms`);
    const medicalData = await getMedicalData(diseaseId, patientId);
    console.log(`[AI报告] 获取医疗数据完成 - 耗时:${Date.now() - startTime}ms`);
    
    // 生成标题
    const reportTitle = generateReportTitle(medicalData);
    
    // 查找现有报告或创建新报告
    if (existingReportId) {
      // 使用现有报告ID
      console.log(`[AI报告] 更新现有报告 ID:${existingReportId}`);
      aiReport = await AIReport.query().findById(existingReportId);
      
      if (!aiReport) {
        throw new Error(`未找到ID为${existingReportId}的报告`);
      }
      
      if (aiReport.status !== 'PROCESSING') {
        console.log(`[AI报告] 报告状态已为 ${aiReport.status}，无需再次生成`);
        global[processingKey] = false;
        return { success: true, aiReport: aiReport };
      }
    } else {
      // 创建新报告
      console.log(`[AI报告] 创建新报告记录`);
      aiReport = await AIReport.query().insert({
        id: uuidv4(),
        disease_id: diseaseId,
        patient_id: patientId,
        user_id: userId,
        title: reportTitle,
        template_type: 'COMPREHENSIVE_ANALYSIS',
        status: 'PROCESSING',
        content: {
          summary: '正在生成分析报告...',
          emergencyGuidance: { isEmergency: false },
          hospitalRecommendations: { hospitals: [] },
          treatmentPlan: { options: [] },
          lifestyleAndMentalHealth: { lifestyle: {}, mentalHealth: {} },
          dashboardData: { status: '处理中' },
          disclaimer: '报告生成中，请稍候...'
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });
    }
    
    // 使用事务保证数据一致性
    const trx = await AIReport.startTransaction();
    
    try {
      // 2. 生成LLM请求正文
      console.log(`[AI报告] 生成LLM请求开始 - 耗时:${Date.now() - startTime}ms`);
      const promptBody = generateLLMPrompt(medicalData, targetRegion);
      console.log(`[AI报告] 生成LLM请求完成 - 耗时:${Date.now() - startTime}ms`);
      
      // 3. 调用LLM模型 - 设置超时时间为150秒，只尝试一次
      console.log(`[AI报告] 调用LLM模型开始 - 耗时:${Date.now() - startTime}ms`);
      // 使用超时参数150秒，不再尝试多次
      const llmResponse = await callLLMModel(promptBody, { timeout: 150000, maxRetries: 0 });
      console.log(`[AI报告] 调用LLM模型完成 - 耗时:${Date.now() - startTime}ms`);
      
      // 4. 解析LLM响应结果
      console.log(`[AI报告] 解析LLM响应开始 - 耗时:${Date.now() - startTime}ms`);
      const parsedReport = await parseLLMResponse(llmResponse, medicalData);
      console.log(`[AI报告] 解析LLM响应完成 - 耗时:${Date.now() - startTime}ms`);
      
      // 5. 更新AI报告记录
      console.log(`[AI报告] 更新报告记录开始 - 耗时:${Date.now() - startTime}ms`);
      
      // 查询报告当前状态，确保只更新PROCESSING状态的报告
      const currentReport = await AIReport.query(trx).findById(aiReport.id);
      
      if (!currentReport) {
        throw new Error(`报告${aiReport.id}不存在`);
      }
      
      if (currentReport.status !== 'PROCESSING') {
        console.log(`[AI报告] 报告${aiReport.id}状态已变更为${currentReport.status}，不再更新`);
        await trx.commit();
        global[processingKey] = false;
        return { success: true, aiReport: currentReport };
      }
      
      // 更新报告内容
      const updatedReport = await AIReport.query(trx)
        .findById(aiReport.id)
        .patch({
          status: 'COMPLETED',
          content: parsedReport,
          llm_raw_response: llmResponse,
          updated_at: new Date().toISOString()
        })
        .returning('*');
      
      // 6. 创建相关记录 (采用备份版本的正确逻辑，并内联)
      console.log(`[AI报告] 创建相关记录(修正后)开始 - 耗时:${Date.now() - startTime}ms`);
      const nowForRecord = new Date().toISOString();
      // parsedReport 变量应包含从LLM解析后的完整报告对象
      // reportTitle 变量应包含已生成的报告标题
      const record = await Record.query(trx).insert({
        id: uuidv4(),
        disease_id: diseaseId,
        patient_id: patientId,
        user_id: userId,
        title: `${reportTitle} - AI摘要`,
        content: parsedReport.summary, // 使用LLM的摘要
        record_type: 'AI_ANALYSIS',
        primary_type: 'MEDICAL',
        is_deleted: false,
        reference_id: aiReport.id,
        record_date: nowForRecord,
        created_by: userId,
        created_at: nowForRecord,
        updated_at: nowForRecord,
        is_private: false,
        is_important: false,
        severity: 'MODERATE',
        data: JSON.stringify({
          hospitalRecommendations: parsedReport.hospitalRecommendations,
          lifestyle: parsedReport.lifestyle || parsedReport.lifestyleAndMentalHealth?.lifestyle,
          mentalHealth: parsedReport.mentalHealth || parsedReport.lifestyleAndMentalHealth?.mentalHealth,
          dashboardData: {
            status: parsedReport.dashboardData?.status,
            trend: parsedReport.dashboardData?.trend,
            riskLevel: parsedReport.dashboardData?.riskLevel,
            keyIndicators: parsedReport.dashboardData?.keyIndicators
          },
          riskWarnings: parsedReport.riskWarnings,
          bmiRecommendations: parsedReport.bmiRecommendations,
          source: {
            type: 'AI_REPORT',
            reportId: aiReport.id,
            generatedAt: nowForRecord
          }
        })
      });
      console.log(`[AI报告] 相关记录创建完成(修正后): ${record.id} - 耗时:${Date.now() - startTime}ms`);
      
      // 7. 更新报告关联的记录ID
      const finalReport = await AIReport.query(trx)
        .findById(aiReport.id)
        .patch({
          record_id: record.id,
          updated_at: new Date().toISOString()
        })
        .returning('*');
      
      // 提交事务
      await trx.commit();
      console.log(`[AI报告] 报告生成和更新完成，总耗时:${Date.now() - startTime}ms`);
      
      // 清除处理中标志
      global[processingKey] = false;
      
      return { 
        success: true, 
        aiReport: finalReport,
        recordId: record.id
      };
    } catch (error) {
      // 回滚事务
      if (trx) await trx.rollback();
      
      // 更新报告状态为失败
      console.error(`[AI报告] 生成失败:`, error);
      
      try {
        const failedReport = await AIReport.query()
          .findById(aiReport.id)
          .patch({
            status: 'FAILED',
            error_message: `生成报告失败: ${error.message || '未知错误'}`,
            updated_at: new Date().toISOString()
          })
          .returning('*');
        
        console.log(`[AI报告] 已更新报告状态为FAILED, ID:${aiReport.id}`);
        
        global[processingKey] = false;
        throw error;
      } catch (updateError) {
        console.error(`[AI报告] 更新报告状态失败:`, updateError);
        global[processingKey] = false;
        throw error;
      }
    }
  } catch (error) {
    // 清除处理中标志
    global[processingKey] = false;
    throw error;
  }
};

module.exports = {
  generateAIReport,
  getMedicalData,
  processLlmResponse,
  generateReportTitle,
  generateLLMPrompt,
  parseLLMResponse
}; 