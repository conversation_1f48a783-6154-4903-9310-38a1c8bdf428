const { Model, snakeCaseMappers } = require('objection');
const { v4: uuidv4 } = require('uuid');

class Patient extends Model {
  // 定义表名
  static get tableName() {
    return 'patients';
  }

  static get idColumn() {
    return 'id';
  }

  // 添加下划线命名映射配置
  static get columnNameMappers() {
    return snakeCaseMappers();
  }

  // 定义JSON模式验证
  static get jsonSchema() {
    return {
      type: 'object',
      required: ['user_id', 'name', 'gender'],
      properties: {
        id: { type: 'string' },
        userId: { type: 'string' },
        name: { type: 'string', minLength: 1, maxLength: 100 },
        gender: { type: 'string', enum: ['男', '女', '其他'] },
        birthDate: { type: ['string', 'null'] },
        phoneNumber: { type: ['string', 'null'] },
        email: { type: ['string', 'null'] },
        address: { type: ['string', 'null'] },
        idCard: { type: ['string', 'null'] },
        medicareCard: { type: ['string', 'null'] },
        medicareLocation: { type: ['string', 'null'] },
        bloodType: { type: ['string', 'null'] },
        emergencyContactName: { type: ['string', 'null'] },
        emergencyContactPhone: { type: ['string', 'null'] },
        emergencyContactRelationship: { type: ['string', 'null'] },
        pastMedicalHistory: { type: ['string', 'null'] },
        familyMedicalHistory: { type: ['string', 'null'] },
        allergyHistory: { type: ['string', 'null'] },
        lastVisitDate: { type: ['string', 'null'] },
        height: { type: ['number', 'null'] },
        weight: { type: ['number', 'null'] },
        bmi: { type: ['number', 'null'] },
        isPrimary: { type: 'integer', default: 0 },
        createdAt: { type: 'string' },
        updatedAt: { type: 'string' },
        deletedAt: { type: ['string', 'null'] }
      }
    };
  }

  // 在查询中自动过滤已删除的记录
  static get modifiers() {
    return {
      notDeleted(builder) {
        builder.whereNull('deleted_at');
      }
    };
  }

  $beforeInsert() {
    this.id = this.id || uuidv4();
    this.createdAt = new Date().toISOString();
    this.updatedAt = this.createdAt;
    
    // 如果有身高和体重，自动计算BMI
    if (this.height && this.weight && this.height > 0) {
      // BMI = 体重(kg) / (身高(m) * 身高(m))
      this.bmi = this.weight / ((this.height / 100) * (this.height / 100));
      // 四舍五入到小数点后一位
      this.bmi = Math.round(this.bmi * 10) / 10;
    }
  }

  $beforeUpdate() {
    this.updatedAt = new Date().toISOString();
    
    // 如果更新了身高或体重，重新计算BMI
    if ((this.height || (this.$old && this.$old.height)) && (this.weight || (this.$old && this.$old.weight))) {
      const height = this.height || (this.$old && this.$old.height);
      const weight = this.weight || (this.$old && this.$old.weight);
      
      if (height > 0) {
        // BMI = 体重(kg) / (身高(m) * 身高(m))
        this.bmi = weight / ((height / 100) * (height / 100));
        // 四舍五入到小数点后一位
        this.bmi = Math.round(this.bmi * 10) / 10;
      }
    }
  }

  static get relationMappings() {
    const Disease = require('./Disease');
    const User = require('./User');
    
    return {
      diseases: {
        relation: Model.HasManyRelation,
        modelClass: Disease,
        join: {
          from: 'patients.id',
          to: 'diseases.patient_id'
        }
      },
      user: {
        relation: Model.BelongsToOneRelation,
        modelClass: User,
        join: {
          from: 'patients.user_id',
          to: 'users.id'
        }
      }
    };
  }
}

module.exports = Patient; 