/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.table('records', function(table) {
    // 添加reference_id字段，用于存储关联ID（如AI报告ID）
    table.string('reference_id').nullable();
    
    // 添加索引以提高查询性能
    table.index('reference_id');
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.table('records', function(table) {
    // 回滚时删除字段
    table.dropColumn('reference_id');
  });
};
