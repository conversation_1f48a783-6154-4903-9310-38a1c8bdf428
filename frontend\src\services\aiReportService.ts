import apiClient from './apiClient';
import { API_PATHS } from '../config/apiPaths';

/**
 * AI报告状态
 */
export type AIReportStatus = 'PROCESSING' | 'COMPLETED' | 'FAILED';

/**
 * AI报告内容
 */
export interface AIReportContent {
  summary: string;
  emergencyGuidance: {
    isEmergency: boolean;
    immediateActions: string[];
    nextSteps: string[];
  };
  hospitalRecommendations: {
    targetRegion: string;
    hospitals: Array<{
      name: string;
      department: string;
      address: string;
      contact: string;
    }>;
  };
  treatmentPlan: {
    options: Array<{
      name: string;
      description: string;
      suitabilityScore: number;
      prognosisData: {
        survivalRate: string;
        remissionRate: string;
        recurrenceRisk: string;
      };
      budgetEstimation: {
        minCost: number;
        maxCost: number;
        currency: string;
        insuranceCoverage: string;
      };
      followUpPlan: string[];
    }>;
    followUp: string[];
  };
  lifestyleAndMentalHealth: {
    lifestyle: {
      diet: string[];
      exercise: string[];
      habits: string[];
    };
    mentalHealth: {
      copingStrategies: string[];
      resources: string[];
    };
  };
  dashboardData: {
    status: string;
    trend: string;
    riskLevel: string;
    isEmergency: boolean;
    topHospital: string;
    budgetRange: string;
  };
  disclaimer: string;
}

/**
 * AI报告
 */
export interface AIReport {
  id: string;
  disease_id: string;
  patient_id: string;
  user_id: string;
  title: string;
  template_type: string;
  status: AIReportStatus;
  content: AIReportContent;
  record_id?: string;
  error_message?: string;
  created_at: string;
  updated_at: string;
}

/**
 * 创建AI报告参数
 */
export interface CreateAIReportParams {
  diseaseId: string;
  patientId: string;
  targetRegion?: string;
}

/**
 * API响应
 */
export interface APIResponse<T> {
  status: 'SUCCESS' | 'PROCESSING' | 'FAILED';
  message: string;
  data: T;
}

/**
 * 创建AI报告
 * @param params 创建参数
 * @param waitForCompletion 是否等待报告完成，默认为false
 * @returns Promise<AIReport>
 */
export const createAIReport = async (params: CreateAIReportParams, waitForCompletion: boolean = false): Promise<AIReport> => {
  try {
    console.log('[createAIReport] 开始创建AI报告，参数(驼峰):', params);
    console.log('[createAIReport] 等待完成模式:', waitForCompletion ? '是' : '否');

    // 使用API_PATHS常量确保路径一致性
    const response = await apiClient.post(API_PATHS.AI_REPORT.CREATE, {
      ...params,
      waitForCompletion // 传递给后端，决定是否等待生成完成
    });

    // 记录接口响应信息
    console.log('[createAIReport] 收到后端响应 (原始):', response.data);

    // 确保返回数据符合预期结构
    if (!response.data) {
      throw new Error('服务器返回了空响应');
    }

    // 转换为驼峰命名风格 (如果需要)
    const responseData = response.data;

    // 记录转换后的响应
    console.log('[createAIReport] 收到后端响应 (转换后驼峰):', responseData);

    // 检查是否有明确的失败状态
    if (responseData.status === 'FAILED') {
      console.error('[createAIReport] 报告创建失败:', responseData.message, responseData.error);
      throw new Error(responseData.message || 'LLM服务暂时不可用，请稍后再试');
    }

    // 检查是否有aiReport字段
    if (!responseData.aiReport) {
      console.error('[createAIReport] 响应中缺少aiReport字段:', responseData);
      throw new Error('服务器返回了不完整的数据');
    }

    // 检查aiReport的状态
    const aiReportStatus = responseData.aiReport.status;

    if (aiReportStatus === 'FAILED') {
      const errorMessage = responseData.aiReport.error_message || 'LLM服务暂时不可用，请稍后再试';
      console.error('[createAIReport] 报告状态为FAILED:', errorMessage);
      throw new Error(errorMessage);
    }

    if (aiReportStatus === 'COMPLETED') {
      console.log('[createAIReport] 报告已完成');
      // 返回符合AIReport类型的数据，使用下划线命名方式
      return {
        ...responseData.aiReport,
        record_id: responseData.recordId || responseData.aiReport.record_id,
        status: 'COMPLETED'
      };
    }

    if (aiReportStatus === 'PROCESSING') {
      console.log('[createAIReport] 报告正在处理中');
      // 返回符合AIReport类型的数据，使用下划线命名方式
      return {
        ...responseData.aiReport,
        record_id: responseData.recordId || responseData.aiReport.record_id,
        status: 'PROCESSING'
      };
    }

    // 未知状态
    console.log('[createAIReport] 返回未知状态:', {
      aiReport: responseData.aiReport,
      recordId: responseData.recordId,
      status: 'FAILED',
      message: '未知的响应状态'
    });

    // 返回符合AIReport类型的数据，使用下划线命名方式
    return {
      ...responseData.aiReport,
      record_id: responseData.recordId || responseData.aiReport.record_id,
      status: 'FAILED',
      error_message: '未知的响应状态'
    };
  } catch (error) {
    console.error('[createAIReport] 捕获错误:', error);
    // 使用类型断言处理unknown类型
    const err = error as any;
    const errorMessage = err.response?.data?.message || err.message || 'AI报告生成失败';
    console.error('[createAIReport] 错误消息:', errorMessage);

    // 将错误转发给调用者
    throw new Error(errorMessage);
  }
};

/**
 * 轮询报告状态
 * @param reportId 报告ID
 * @param maxAttempts 最大尝试次数
 * @param interval 轮询间隔(毫秒)
 * @param statusCallback 状态回调函数
 * @returns Promise<AIReport>
 */
export const pollReportStatus = async (
  reportId: string,
  maxAttempts: number = 60,
  interval: number = 3000,
  statusCallback?: (status: string, report: any, attempts: number) => void
): Promise<AIReport> => {
  console.log(`[pollReportStatus] 开始轮询报告状态, ID: ${reportId}, 最大尝试次数: ${maxAttempts}, 间隔: ${interval}ms`);

  // 如果关闭了轮询，直接获取报告
  if (maxAttempts <= 0) {
    console.log(`[pollReportStatus] 轮询已禁用，直接获取报告`);
    return await getAIReport(reportId);
  }

  // 开始轮询
  let attempts = 0;
  let lastError = null;

  const poll = async (): Promise<AIReport> => {
    attempts++;
    console.log(`[pollReportStatus] 尝试 ${attempts}/${maxAttempts} 获取报告`);

    try {
      // 使用我们上面定义的getAIReport函数
      const report = await getAIReport(reportId);

      // 回调通知状态
      if (statusCallback) {
        statusCallback(report.status, report, attempts);
      }

      // 根据状态决定是否继续轮询
      if (report.status === 'COMPLETED') {
        console.log(`[pollReportStatus] 报告生成完成, ID: ${reportId}`);
        return report;
      }

      if (report.status === 'PROCESSING') {
        console.log(`[pollReportStatus] 报告仍在生成中, ID: ${reportId}`);
        if (attempts >= maxAttempts) {
          // 超过最大尝试次数，但生成可能依然在后台继续
          console.warn(`[pollReportStatus] 达到最大尝试次数(${maxAttempts})，但报告仍在处理中`);
          report.status = 'TIMEOUT' as any; // 添加自定义状态
          return report;
        }

        // 等待指定时间后再次轮询
        await new Promise(resolve => setTimeout(resolve, interval));
        return poll();
      }

      if (report.status === 'FAILED') {
        console.error(`[pollReportStatus] 报告生成失败, ID: ${reportId}`);
        return report;
      }

      // 未知状态，视为失败
      console.error(`[pollReportStatus] 未知报告状态: ${report.status}`);
      report.status = 'FAILED';
      report.error_message = `未知状态: ${report.status}`;
      return report;
    } catch (error) {
      console.error(`[pollReportStatus] 轮询出错 (尝试 ${attempts}/${maxAttempts}):`, error);
      lastError = error;

      // 处理"已处理中"状态的特殊情况
      const err = error as any;
      if (err.message && (
        err.message.includes('已有相同病例的报告正在处理中') ||
        err.message.includes('正在生成中')
      )) {
        console.log(`[pollReportStatus] 检测到报告已经在处理中`);
        if (statusCallback) {
          statusCallback('ALREADY_PROCESSING', { id: reportId, error_message: err.message }, attempts);
        }

        // 继续轮询这个已经在处理中的报告
        if (attempts < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, interval));
          return poll();
        } else {
          return {
            id: reportId,
            disease_id: '',
            patient_id: '',
            user_id: '',
            title: '',
            template_type: '',
            status: 'TIMEOUT' as any,
            content: {} as AIReportContent,
            error_message: '轮询超时，但报告可能仍在生成中',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };
        }
      }

      // 处理网络错误，这可能只是临时问题，我们可以继续尝试
      if (err.message === 'Network Error' || err.message.includes('CORS') || err.message.includes('网络请求错误')) {
        console.warn(`[pollReportStatus] 网络错误，将在 ${interval}ms 后重试`);

        if (statusCallback) {
          statusCallback('NETWORK_ERROR', { id: reportId, error_message: err.message }, attempts);
        }

        // 如果还有重试次数，继续轮询
        if (attempts < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, interval));
          return poll();
        }
      }

      // 如果还有重试次数，尝试再次轮询
      if (attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, interval));
        return poll();
      }

      // 达到最大尝试次数，抛出错误
      console.error(`[pollReportStatus] 达到最大尝试次数(${maxAttempts})，轮询失败`);
      throw new Error(`轮询失败: ${err.message || '未知错误'}`);
    }
  };

  return poll();
};

/**
 * 重新生成AI报告
 * @param params 创建参数
 * @returns Promise<AIReport>
 */
export const regenerateAIReport = async (params: CreateAIReportParams): Promise<AIReport> => {
  try {
    console.log('[AI报告] 开始重新生成报告', params);

    const response = await apiClient.post<APIResponse<AIReport>>('/api/ai-reports/regenerate', params);

    // 处理不同的状态
    if (response.data.status === 'SUCCESS') {
      console.log('[AI报告] 报告重新生成成功', response.data);
      return response.data.data;
    }

    if (response.data.status === 'PROCESSING') {
      console.log('[AI报告] 报告正在重新生成中', response.data);
      // 返回处理中的报告
      return response.data.data;
    }

    // 如果是其他状态，抛出错误
    throw new Error(response.data.message || '重新生成报告失败');
  } catch (error) {
    console.error('[AI报告] 重新生成报告失败', error);
    throw error;
  }
};

/**
 * 获取AI报告
 * @param reportId 报告ID
 * @returns Promise<AIReport>
 */
export const getAIReport = async (reportId: string): Promise<AIReport> => {
  try {
    console.log(`[getAIReport] 开始获取AI报告: ${reportId}`);

    // 添加防缓存参数
    const timestamp = new Date().getTime();

    // 设置请求配置，添加缓存控制头
    const config = {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      },
      params: {
        _t: timestamp // 添加时间戳参数
      }
    };

    // 使用API_PATHS常量确保路径一致性
    const response = await apiClient.get<APIResponse<AIReport>>(
      API_PATHS.AI_REPORT.DETAILS.replace(':id', reportId),
      config
    );

    if (!response.data) {
      throw new Error('服务器返回了空响应');
    }

    if (response.data.status === 'FAILED') {
      console.error('[getAIReport] 获取报告失败:', response.data.message);
      throw new Error(response.data.message || '获取报告失败，请稍后再试');
    }

    return response.data.data;
  } catch (error) {
    console.error('[getAIReport] 捕获错误:', error);
    // 使用类型断言处理unknown类型
    const err = error as any;
    const errorMessage = err.response?.data?.message || err.message || '获取AI报告失败';
    console.error('[getAIReport] 错误消息:', errorMessage);

    // 如果是CORS错误，提供更具体的错误信息
    if (err.message === 'Network Error') {
      throw new Error('网络请求错误：可能是CORS策略限制，请检查网络连接或联系管理员');
    }

    throw new Error(errorMessage);
  }
};