/**
 * AI报告服务
 * 整合匿名化、构建提示和调用LLM的过程，实现完整的AI报告生成流程
 */
const { v4: uuidv4 } = require('uuid');
const { anonymizeMedicalData } = require('./anonymizationService');
const { buildPrompt } = require('./promptService');
const { callLlmService } = require('./llmService');
const { AIReport } = require('../../models/aiReport');
const Patient = require('../../models/Patient');
const Disease = require('../../models/Disease');
const Record = require('../../models/Record');
const fs = require('fs');
const path = require('path');

/**
 * 新增：辅助函数 - 计算BMI
 * @param {number} weightKg 体重（千克）
 * @param {number} heightCm 身高（厘米）
 * @returns {number|null} BMI值或null（如果输入无效）
 */
const calculateBMI = (weightKg, heightCm) => {
  if (weightKg === null || weightKg === undefined || heightCm === null || heightCm === undefined || parseFloat(heightCm) === 0) {
    return null;
  }
  const weight = parseFloat(weightKg);
  const heightM = parseFloat(heightCm) / 100;
  if (isNaN(weight) || isNaN(heightM) || heightM === 0) {
    return null;
  }
  const bmi = weight / (heightM * heightM);
  return parseFloat(bmi.toFixed(1)); // 保留一位小数
};

/**
 * 新增：辅助函数 - 尝试从文本中提取SUVmax值
 * @param {string} textToSearch 要搜索的文本
 * @returns {string} 提取的SUVmax值或"待更新"
 */
const extractSuvMax = (textToSearch) => {
  if (!textToSearch || typeof textToSearch !== 'string') return '待更新';
  const suvRegex = /SUVmax[^\\d]*([\\d.]+)/i;
  const match = textToSearch.match(suvRegex);
  return match && match[1] ? match[1] : '待更新';
};

/**
 * 获取医疗数据
 * @param {string} diseaseId 病理ID
 * @param {string} patientId 患者ID
 * @returns {Promise<Object>} 患者、病理和记录数据
 */
const getMedicalData = async (diseaseId, patientId) => {
  // 获取患者信息
  const patient = await Patient.query().findById(patientId);
  if (!patient) {
    throw new Error('未找到患者信息');
  }

  // 获取病理信息
  const disease = await Disease.query().findById(diseaseId);
  if (!disease) {
    throw new Error('未找到病理信息');
  }

  // 获取相关记录
  const records = await Record.query()
    .where({ disease_id: diseaseId })
    .orderBy('created_at', 'asc'); // 按时间顺序排序

  return {
    patient,
    disease,
    records
  };
};

/**
 * 处理LLM返回的内容
 * @param {string} content LLM返回的内容
 * @returns {Object} 解析后的JSON内容
 */
const processLlmResponse = (content) => {
  try {
    console.log('===== 开始处理LLM响应 =====');
    console.log('原始响应内容:', content);

    // 尝试解析JSON
    let jsonContent;

    // 有时LLM会在JSON前后添加文本，我们需要提取JSON部分
    const jsonMatch = content.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      console.log('找到JSON内容:', jsonMatch[0]);
      jsonContent = JSON.parse(jsonMatch[0]);
    } else {
      console.error('未找到有效的JSON内容');
      throw new Error('未找到有效的JSON内容');
    }

    // 新增：优先处理 emergencyGuidance 如果它是字符串的情况
    if (typeof jsonContent.emergencyGuidance === 'string') {
      console.warn('[AIReportService] LLM返回的emergencyGuidance是字符串，将转换为对象结构。原始文本将存入summary。');
      const guidanceText = jsonContent.emergencyGuidance;
      jsonContent.emergencyGuidance = {
        isEmergency: guidanceText.includes('紧急') || guidanceText.includes('立即就医'), // 根据文本内容初步判断是否紧急
        summary: guidanceText,
        immediateActions: [guidanceText], // 将原始文本作为一项措施
        nextSteps: ['请结合临床判断并遵循医嘱。'] // 通用后续步骤
      };
    }

    // 验证必要字段是否存在
    const requiredFields = [
      'summary',
      'emergencyGuidance',
      'hospitalRecommendations',
      'treatmentPlan',
      'lifestyleAndMentalHealth',
      'dashboardData',
      'disclaimer'
    ];

    // 先确保disclaimer字段存在，因为这是一个固定值
    if (!jsonContent.disclaimer) {
      console.log('添加缺失的disclaimer字段');
      jsonContent.disclaimer = '本报告由AI生成，仅供参考，不构成医疗诊断或治疗建议。请咨询专业医生获取正式医疗意见。';
    }

    const missingFields = requiredFields.filter(field => !jsonContent[field]);
    if (missingFields.length > 0) {
      console.warn(`LLM响应缺少以下字段: ${missingFields.join(', ')}`);

      // 为缺失字段提供默认值
      missingFields.forEach(field => {
        console.log(`为缺失字段 ${field} 提供默认值`);
        switch (field) {
          case 'summary':
            jsonContent.summary = '无法生成病情摘要，信息不足';
            break;
          case 'emergencyGuidance':
            jsonContent.emergencyGuidance = {
              isEmergency: false,
              immediateActions: [],
              nextSteps: []
            };
            break;
          case 'hospitalRecommendations':
            jsonContent.hospitalRecommendations = {
              targetRegion: '',
              hospitals: []
            };
            break;
          case 'treatmentPlan':
            jsonContent.treatmentPlan = {
              options: [],
              followUp: []
            };
            break;
          case 'lifestyleAndMentalHealth':
            jsonContent.lifestyleAndMentalHealth = {
              lifestyle: {
                diet: [
                  '保持均衡饮食，增加蔬菜水果摄入',
                  '控制盐分和糖分摄入',
                  '保持充分的水分摄入，每天至少8杯水',
                  '减少加工食品和高脂肪食物的摄入'
                ],
                exercise: [
                  '根据自身情况进行适量运动，每周至少150分钟中等强度活动',
                  '避免长时间久坐，每小时起身活动5分钟',
                  '可以选择步行、游泳或太极等低强度运动开始'
                ],
                habits: [
                  '保持规律的作息时间，确保充足睡眠',
                  '避免或减少烟酒摄入',
                  '养成定期体检的习惯'
                ]
              },
              mentalHealth: {
                copingStrategies: [
                  '学习简单的呼吸放松技巧，帮助缓解焦虑',
                  '保持社交联系，与亲友分享感受',
                  '设定合理的期望，接受治疗过程中的起伏'
                ],
                resources: [
                  '考虑寻求专业心理咨询师的帮助',
                  '加入病友支持小组，分享经验和情感支持',
                  '使用正念冥想等应用程序辅助放松'
                ]
              }
            };
            break;
          case 'dashboardData':
            jsonContent.dashboardData = {
              status: '未知',
              trend: 'stable',
              riskLevel: 'medium',
              isEmergency: false,
              topHospital: '',
              budgetRange: ''
            };
            break;
          case 'disclaimer':
            jsonContent.disclaimer = '本报告由AI生成，仅供参考，不构成医疗诊断或治疗建议。请咨询专业医生获取正式医疗意见。';
            break;
          default:
            jsonContent[field] = null;
        }
      });
    }

    // 修复紧急处置指南可能被包含在summary中的问题
    if (jsonContent.summary && jsonContent.summary.includes('紧急')) {
      console.log('检测到紧急处置指南可能被包含在summary中，进行分离处理');

      // 确保emergencyGuidance是对象
      if (!jsonContent.emergencyGuidance || typeof jsonContent.emergencyGuidance !== 'object') {
        console.log('[AIReportService] emergencyGuidance不是对象，创建默认结构');
        jsonContent.emergencyGuidance = {
          isEmergency: false,
          summary: '',
          immediateActions: [],
          nextSteps: []
        };
      }

      // 如果emergencyGuidance的immediateActions不存在或不是数组，初始化为空数组
      if (!Array.isArray(jsonContent.emergencyGuidance.immediateActions)) {
        console.log('[AIReportService] emergencyGuidance.immediateActions不是数组，初始化为空数组');
        jsonContent.emergencyGuidance.immediateActions = [];
      }

      // 如果emergencyGuidance的nextSteps不存在或不是数组，初始化为空数组
      if (!Array.isArray(jsonContent.emergencyGuidance.nextSteps)) {
        console.log('[AIReportService] emergencyGuidance.nextSteps不是数组，初始化为空数组');
        jsonContent.emergencyGuidance.nextSteps = [];
      }

      // 如果emergencyGuidance的immediateActions为空，但summary中包含紧急处置信息
      if (jsonContent.emergencyGuidance.immediateActions.length === 0 &&
          (jsonContent.summary.includes('紧急处置') || jsonContent.summary.includes('立即就医'))) {

        // 尝试提取紧急处置信息
        const emergencyRegex = /紧急处置[：:]([\s\S]*?)(?=\n\n|\n##|$)/i;
        const match = jsonContent.summary.match(emergencyRegex);

        if (match && match[1]) {
          // 从summary中移除这部分内容
          jsonContent.summary = jsonContent.summary.replace(match[0], '').trim();

          // 提取的紧急处置信息
          const emergencyInfo = match[1].trim();

          // 更新emergencyGuidance
          jsonContent.emergencyGuidance.isEmergency = true;
          jsonContent.emergencyGuidance.summary = emergencyInfo; // 添加summary字段
          jsonContent.emergencyGuidance.immediateActions = [
            '请立即前往最近的医疗机构就诊',
            '携带所有相关的检查资料',
            emergencyInfo
          ];
          jsonContent.emergencyGuidance.nextSteps = [
            '遵循医生建议进行后续检查和治疗',
            '定期随访监测病情变化'
          ];
        }
      }
    }

    // 检查lifestyleAndMentalHealth是否有实质内容
    if (jsonContent.lifestyleAndMentalHealth) {
      const lifestyle = jsonContent.lifestyleAndMentalHealth.lifestyle || {};
      const mentalHealth = jsonContent.lifestyleAndMentalHealth.mentalHealth || {};

      // 检查diet数组是否为空或不存在
      if (!lifestyle.diet || lifestyle.diet.length === 0) {
        console.log('添加默认饮食建议');
        jsonContent.lifestyleAndMentalHealth.lifestyle = jsonContent.lifestyleAndMentalHealth.lifestyle || {};
        jsonContent.lifestyleAndMentalHealth.lifestyle.diet = [
          '保持均衡饮食，增加蔬菜水果摄入',
          '控制盐分和糖分摄入',
          '保持充分的水分摄入，每天至少8杯水',
          '减少加工食品和高脂肪食物的摄入'
        ];
      }

      // 检查exercise数组是否为空或不存在
      if (!lifestyle.exercise || lifestyle.exercise.length === 0) {
        console.log('添加默认运动建议');
        jsonContent.lifestyleAndMentalHealth.lifestyle = jsonContent.lifestyleAndMentalHealth.lifestyle || {};
        jsonContent.lifestyleAndMentalHealth.lifestyle.exercise = [
          '根据自身情况进行适量运动，每周至少150分钟中等强度活动',
          '避免长时间久坐，每小时起身活动5分钟',
          '可以选择步行、游泳或太极等低强度运动开始'
        ];
      }

      // 检查habits数组是否为空或不存在
      if (!lifestyle.habits || lifestyle.habits.length === 0) {
        console.log('添加默认习惯调整建议');
        jsonContent.lifestyleAndMentalHealth.lifestyle = jsonContent.lifestyleAndMentalHealth.lifestyle || {};
        jsonContent.lifestyleAndMentalHealth.lifestyle.habits = [
          '保持规律的作息时间，确保充足睡眠',
          '避免或减少烟酒摄入',
          '养成定期体检的习惯'
        ];
      }

      // 检查copingStrategies数组是否为空或不存在
      if (!mentalHealth.copingStrategies || mentalHealth.copingStrategies.length === 0) {
        console.log('添加默认心理应对策略');
        jsonContent.lifestyleAndMentalHealth.mentalHealth = jsonContent.lifestyleAndMentalHealth.mentalHealth || {};
        jsonContent.lifestyleAndMentalHealth.mentalHealth.copingStrategies = [
          '学习简单的呼吸放松技巧，帮助缓解焦虑',
          '保持社交联系，与亲友分享感受',
          '设定合理的期望，接受治疗过程中的起伏'
        ];
      }

      // 检查resources数组是否为空或不存在
      if (!mentalHealth.resources || mentalHealth.resources.length === 0) {
        console.log('添加默认心理资源建议');
        jsonContent.lifestyleAndMentalHealth.mentalHealth = jsonContent.lifestyleAndMentalHealth.mentalHealth || {};
        jsonContent.lifestyleAndMentalHealth.mentalHealth.resources = [
          '考虑寻求专业心理咨询师的帮助',
          '加入病友支持小组，分享经验和情感支持',
          '使用正念冥想等应用程序辅助放松'
        ];
      }
    }

    // 确保 hospitalRecommendations 和 treatmentPlan 内部数组的健壮性 (如果需要)
    // (此处省略，根据实际情况添加，当前主要问题在emergencyGuidance)

    // 再次确保 emergencyGuidance 内部结构的完整性
    if (jsonContent.emergencyGuidance) {
      // 确保 isEmergency 存在且为布尔值
      if (typeof jsonContent.emergencyGuidance.isEmergency !== 'boolean') {
        console.warn('[AIReportService] emergencyGuidance.isEmergency 不是布尔值或缺失，设置为默认值 false');
        if (jsonContent.emergencyGuidance.isEmergency === undefined) {
            jsonContent.emergencyGuidance.isEmergency = false;
        }
      }

      // 确保 summary 字段存在 (如果是由字符串转换而来，它已经有了)
      // 如果 emergencyGuidance 是对象但缺少 summary，也需要填充
      if (typeof jsonContent.emergencyGuidance.summary !== 'string' || (jsonContent.emergencyGuidance.summary && jsonContent.emergencyGuidance.summary.trim() === '')) {
        console.warn('[AIReportService] emergencyGuidance.summary 不是有效字符串或缺失/为空，尝试填充或设置默认值');
        jsonContent.emergencyGuidance.summary = jsonContent.emergencyGuidance.immediateActions && jsonContent.emergencyGuidance.immediateActions.length > 0 && typeof jsonContent.emergencyGuidance.immediateActions[0] === 'string'
                                              ? jsonContent.emergencyGuidance.immediateActions[0] // 尝试使用第一个行动项作为摘要
                                              : '请查看具体行动建议。';
      }

      // 确保 immediateActions 是数组
      if (!Array.isArray(jsonContent.emergencyGuidance.immediateActions)) {
        console.warn('[AIReportService] emergencyGuidance.immediateActions 不是数组或缺失，将尝试使用summary或设置默认值');
        jsonContent.emergencyGuidance.immediateActions = jsonContent.emergencyGuidance.summary ? [jsonContent.emergencyGuidance.summary] : ['请遵循医嘱。'];
      }

      // 确保 nextSteps 是数组
      if (!Array.isArray(jsonContent.emergencyGuidance.nextSteps)) {
        console.warn('[AIReportService] emergencyGuidance.nextSteps 不是数组或缺失，设置为默认值');
        jsonContent.emergencyGuidance.nextSteps = ['定期复查，监测病情变化。'];
      }
    } else {
      // 如果经历了之前的默认值填充后 emergencyGuidance 依然不存在或为null/undefined
      // 但作为最终防线，再次确保它存在且结构正确
      console.warn('[AIReportService] emergencyGuidance 字段在最终检查前仍缺失或无效，强制设置默认结构');
      jsonContent.emergencyGuidance = {
        isEmergency: false,
        summary: 'AI未能提供紧急处置建议，请咨询医生。',
        immediateActions: ['具体行动请咨询专业医生。'],
        nextSteps: ['请遵循医嘱并定期复查。']
      };
    }

    // 最终检查emergencyGuidance结构完整性
    if (!jsonContent.emergencyGuidance || typeof jsonContent.emergencyGuidance !== 'object') {
      console.warn('[AIReportService] 最终检查: emergencyGuidance不是对象，创建默认结构');
      jsonContent.emergencyGuidance = {
        isEmergency: false,
        summary: 'AI未能提供紧急处置建议，请咨询医生。',
        immediateActions: ['具体行动请咨询专业医生。'],
        nextSteps: ['请遵循医嘱并定期复查。']
      };
    } else {
      // 确保emergencyGuidance.summary存在且是字符串
      if (typeof jsonContent.emergencyGuidance.summary !== 'string' || !jsonContent.emergencyGuidance.summary) {
        console.warn('[AIReportService] emergencyGuidance.summary 不是有效字符串或缺失/为空，尝试填充或设置默认值');

        // 尝试使用emergencyGuidance的其他信息构建summary
        if (Array.isArray(jsonContent.emergencyGuidance.immediateActions) && jsonContent.emergencyGuidance.immediateActions.length > 0) {
          jsonContent.emergencyGuidance.summary = jsonContent.emergencyGuidance.immediateActions.join('。 ');
        } else {
          jsonContent.emergencyGuidance.summary = 'AI未能提供紧急处置建议，请咨询医生。';
        }
      }

      // 确保immediateActions是数组
      if (!Array.isArray(jsonContent.emergencyGuidance.immediateActions)) {
        console.warn('[AIReportService] emergencyGuidance.immediateActions 不是数组或缺失，将尝试使用summary或设置默认值');

        if (typeof jsonContent.emergencyGuidance.summary === 'string' && jsonContent.emergencyGuidance.summary) {
          jsonContent.emergencyGuidance.immediateActions = [jsonContent.emergencyGuidance.summary];
        } else {
          jsonContent.emergencyGuidance.immediateActions = ['具体行动请咨询专业医生。'];
        }
      }

      // 确保nextSteps是数组
      if (!Array.isArray(jsonContent.emergencyGuidance.nextSteps)) {
        console.warn('[AIReportService] emergencyGuidance.nextSteps 不是数组或缺失，设置为默认值');
        jsonContent.emergencyGuidance.nextSteps = ['请遵循医嘱并定期复查。'];
      }
    }

    // 在函数返回前，最终确认并打印 emergencyGuidance 的结构
    console.log('[AIReportService DEBUG] Final structure of emergencyGuidance before returning from processLlmResponse:', JSON.stringify(jsonContent.emergencyGuidance, null, 2));

    console.log('处理后的JSON内容:', JSON.stringify(jsonContent, null, 2));
    console.log('===== LLM响应处理完成 =====');

    return jsonContent;
  } catch (error) {
    console.error('解析LLM响应失败:', error);
    console.error('错误详情:', error.stack);
    // 返回基本结构，避免系统崩溃
    return {
      summary: '无法解析AI模型响应，请重试。',
      differentialDiagnosis: { possibleConditions: [], followUpQuestions: [] },
      emergencyGuidance: { isEmergency: false, immediateActions: [], nextSteps: [] },
      hospitalRecommendations: { targetRegion: '', hospitals: [] },
      treatmentPlan: { options: [], followUp: [] },
      lifestyleAndMentalHealth: {
        lifestyle: {
          diet: [
            '保持均衡饮食，增加蔬菜水果摄入',
            '控制盐分和糖分摄入',
            '保持充分的水分摄入，每天至少8杯水',
            '减少加工食品和高脂肪食物的摄入'
          ],
          exercise: [
            '根据自身情况进行适量运动，每周至少150分钟中等强度活动',
            '避免长时间久坐，每小时起身活动5分钟',
            '可以选择步行、游泳或太极等低强度运动开始'
          ],
          habits: [
            '保持规律的作息时间，确保充足睡眠',
            '避免或减少烟酒摄入',
            '养成定期体检的习惯'
          ]
        },
        mentalHealth: {
          copingStrategies: [
            '学习简单的呼吸放松技巧，帮助缓解焦虑',
            '保持社交联系，与亲友分享感受',
            '设定合理的期望，接受治疗过程中的起伏'
          ],
          resources: [
            '考虑寻求专业心理咨询师的帮助',
            '加入病友支持小组，分享经验和情感支持',
            '使用正念冥想等应用程序辅助放松'
          ]
        }
      },
      dashboardData: {
        status: '解析错误',
        trend: 'stable',
        riskLevel: 'medium',
        isEmergency: false,
        topHospital: '',
        budgetRange: ''
      },
      riskWarnings: ['系统无法正确解析AI响应'],
      disclaimer: '本报告由AI生成，仅供参考，不构成医疗诊断或治疗建议。请咨询专业医生获取正式医疗意见。'
    };
  }
};

/**
 * 生成AI报告
 * @param {string} diseaseId 病理ID
 * @param {string} patientId 患者ID
 * @param {string} userId 用户ID
 * @param {string} targetRegion 患者意向就医地区
 * @returns {Promise<Object>} 生成的AI报告
 */
const generateAIReport = async (diseaseId, patientId, userId, targetRegion) => {
  let aiReport = null;
  try {
    // 1. 获取医疗数据（提前获取，以便使用患者和病理信息生成标题）
    const medicalData = await getMedicalData(diseaseId, patientId);

    // 生成报告标题: (患者名)_(病理名)_辅医智能分析报告
    const patientName = medicalData.patient.name || '患者';
    const diseaseName = medicalData.disease.name || '病历';
    const reportTitle = `${patientName}_${diseaseName}_辅医智能分析报告`;

    // 创建初始报告状态
      aiReport = await AIReport.query().insert({
        id: uuidv4(),
        disease_id: diseaseId,
        patient_id: patientId,
        user_id: userId,
        title: reportTitle,
        template_type: 'COMPREHENSIVE_ANALYSIS',
        status: 'PROCESSING',
        content: {
          summary: '正在生成分析报告...',
          emergencyGuidance: { isEmergency: false },
          hospitalRecommendations: { hospitals: [] },
          treatmentPlan: { options: [] },
          lifestyleAndMentalHealth: { lifestyle: {}, mentalHealth: {} },
          dashboardData: { status: '处理中' },
          disclaimer: '报告生成中，请稍候...'
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    // 3. 匿名化数据
    const { anonymizedData, mapping } = anonymizeMedicalData(medicalData);

    // 添加意向就医地区
    if (targetRegion) {
      anonymizedData.targetRegion = targetRegion;
    }

    // 4. 保存匿名化映射关系
    await AIReport.query()
      .findById(aiReport.id)
      .patch({
        anonymized_info: mapping,
        updated_at: new Date().toISOString()
      });

    // 5. 构建提示
    const { systemPrompt, userPrompt } = buildPrompt(anonymizedData);

    // 添加调试日志
    console.log('===== AI报告生成过程 =====');
    console.log('病历ID:', diseaseId);
    console.log('患者ID:', patientId);
    console.log('用户ID:', userId);
    console.log('目标地区:', targetRegion || '未指定');
    console.log('匿名化数据样本:', JSON.stringify(anonymizedData).substring(0, 300) + '...');

    // 检查环境变量
    if (process.env.LLM_DEBUG_MODE === 'true') {
      console.log('===== LLM提示内容 =====');
      console.log('系统提示:', systemPrompt);
      console.log('用户提示:', userPrompt);
      console.log('=====================');

      // 将提示内容保存到文件（仅开发环境）
      if (process.env.NODE_ENV === 'development') {
        const debugDir = path.join(__dirname, '../../../debug');
        if (!fs.existsSync(debugDir)) {
          fs.mkdirSync(debugDir, { recursive: true });
        }

        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        fs.writeFileSync(
          path.join(debugDir, `prompt_${timestamp}.json`),
          JSON.stringify({ systemPrompt, userPrompt, anonymizedData }, null, 2)
        );
        console.log('已将提示内容保存到:', path.join(debugDir, `prompt_${timestamp}.json`));
      }

      // 如果是仅调试模式，生成模拟响应
      if (process.env.LLM_DEBUG_ONLY === 'true') {
        console.log('LLM调试模式：生成模拟AI报告');

        // 创建与报告关联的记录
        const record = await Record.query().insert({
          id: uuidv4(),
          disease_id: diseaseId,
          patient_id: patientId,
          user_id: userId,
          title: `${patientName}_${diseaseName}_辅医智能分析报告记录`,
          content: '这是一个调试模式生成的报告，无实际分析内容。',
          record_type: 'AI_ANALYSIS',
          primary_type: 'MEDICAL',
          severity: 'MODERATE',
          is_private: false,
          is_important: false,
          record_date: new Date().toISOString(),
          created_by: userId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          // 提取关键信息保存到data字段
          data: JSON.stringify({
            // 医院推荐信息
            hospitalRecommendations: {
              targetRegion: targetRegion || '未指定',
              hospitals: []
            },

            // 生活方式和心理健康建议
            lifestyle: {
              diet: ["保持均衡饮食，多摄入蔬果"],
              exercise: ["根据身体状况适当进行轻度运动"],
              habits: ["保持良好作息"]
            },
            mentalHealth: {
              copingStrategies: ["保持心情愉悦"],
              resources: ["可咨询专业心理医生"]
            },

            // 病情状态信息
            dashboardData: {
              status: '调试模式',
              trend: 'stable',
              riskLevel: 'low',
              isEmergency: false,
              topHospital: '无',
              budgetRange: '无'
            },

            // 风险警告
            riskWarnings: ['这是调试模式生成的风险提示。'],

            // BMI建议(如果有)
            bmiRecommendations: null,

            // 来源信息
            source: {
              type: 'AI_REPORT',
              reportId: aiReport.id,
              generatedAt: new Date().toISOString()
            }
          })
        });

        // 创建调试模式的内容
        const debugContent = {
          summary: '这是调试模式生成的AI报告摘要，未调用实际的LLM服务。',
          differentialDiagnosis: {
            possibleConditions: []
          },
          emergencyGuidance: {
            isEmergency: false,
            immediateActions: [],
            nextSteps: []
          },
          hospitalRecommendations: {
            targetRegion: targetRegion || '未指定',
            hospitals: []
          },
          treatmentPlan: {
            options: []
          },
          lifestyleAndMentalHealth: {
            lifestyle: {
              diet: ["保持均衡饮食，多摄入蔬果"],
              exercise: ["根据身体状况适当进行轻度运动"],
              habits: ["保持良好作息"]
            },
            mentalHealth: {
              copingStrategies: ["保持心情愉悦"],
              resources: ["可咨询专业心理医生"]
            }
          },
          dashboardData: {
            status: '调试模式',
            trend: 'stable',
            riskLevel: 'low',
            isEmergency: false,
            topHospital: '无',
            budgetRange: '无'
          },
          riskWarnings: ['这是调试模式生成的风险提示。'],
          is_chronic_disease: false
        };

        // 更新报告状态和内容
        await AIReport.query().findById(aiReport.id).patch({
          record_id: record.id,
          content: debugContent,
          llm_raw_response: JSON.stringify({ debug: true }),
          status: 'COMPLETED',
          updated_at: new Date().toISOString()
        });

        // 重新获取更新后的报告
        aiReport = await AIReport.query().findById(aiReport.id);

        return {
          aiReport,
          recordId: record.id
        };
      }
    }

    // 6. 调用LLM服务
    try {
      console.log('开始调用LLM服务，生成AI报告内容...');
      const llmResponse = await callLlmService(userPrompt, {
        systemPrompt,
        temperature: 0.2 // 设置较低的温度值以提高一致性
      });

      // 7. 处理LLM响应
      const processedContent = processLlmResponse(llmResponse.content);

      // 8. 添加BMI相关建议（如果患者有身高体重数据）
      if (medicalData.patient && medicalData.patient.height && medicalData.patient.weight) {
        const height = medicalData.patient.height;
        const weight = medicalData.patient.weight;
        const bmi = medicalData.patient.bmi || (weight / ((height / 100) * (height / 100)));

        // 根据BMI值确定状态和颜色
        let bmiStatus, bmiColor;
        let recommendations = [];

        if (bmi < 18.5) {
          bmiStatus = '偏瘦';
          bmiColor = '#FFA500'; // 橙色
          recommendations = [
            '增加优质蛋白质和健康脂肪的摄入，如鱼类、坚果和橄榄油',
            '适量增加热量摄入，可增加进餐频次',
            '进行力量训练以增加肌肉质量',
            '咨询医生了解是否需要营养补充剂'
          ];
        } else if (bmi < 24) {
          bmiStatus = '正常';
          bmiColor = '#008000'; // 绿色
          recommendations = [
            '保持目前的饮食习惯和体重',
            '坚持规律运动，每周至少150分钟中等强度活动',
            '定期监测体重，保持健康生活方式',
            '均衡摄入各类营养素，保持良好饮食结构'
          ];
        } else if (bmi < 28) {
          bmiStatus = '超重';
          bmiColor = '#FFA500'; // 橙色
          recommendations = [
            '控制每日热量摄入，增加蔬菜水果比例',
            '减少精制碳水化合物和饱和脂肪的摄入',
            '增加有氧运动，每周至少150-300分钟',
            '考虑制定减重计划，每周减重0.5-1公斤为宜'
          ];
        } else {
          bmiStatus = '肥胖';
          bmiColor = '#FF0000'; // 红色
          recommendations = [
            '在医生指导下制定减重计划',
            '严格控制热量摄入，增加高纤维食物',
            '每天进行至少30分钟中等强度有氧运动',
            '考虑咨询营养师获取个性化饮食建议',
            '定期监测血压、血糖和血脂等指标'
          ];
        }

        // 添加BMI推荐到报告内容
        processedContent.bmiRecommendations = {
          bmiValue: parseFloat(bmi.toFixed(1)),
          bmiStatus: {
            status: bmiStatus,
            color: bmiColor
          },
          recommendations: recommendations
        };

        // 如果生活方式建议中没有明确提到BMI相关的建议，可以添加一些
        if (processedContent.lifestyleAndMentalHealth &&
            processedContent.lifestyleAndMentalHealth.lifestyle) {

          // 确保饮食建议存在
          if (!processedContent.lifestyleAndMentalHealth.lifestyle.diet) {
            processedContent.lifestyleAndMentalHealth.lifestyle.diet = [];
          }

          // 确保运动建议存在
          if (!processedContent.lifestyleAndMentalHealth.lifestyle.exercise) {
            processedContent.lifestyleAndMentalHealth.lifestyle.exercise = [];
          }

          // 添加一些BMI相关的生活方式建议，如果现有建议较少
          if (processedContent.lifestyleAndMentalHealth.lifestyle.diet.length < 2) {
            if (bmi < 18.5) {
              processedContent.lifestyleAndMentalHealth.lifestyle.diet.push(
                '增加高蛋白食物摄入，如鸡胸肉、鱼类、豆类和坚果'
              );
            } else if (bmi >= 24) {
              processedContent.lifestyleAndMentalHealth.lifestyle.diet.push(
                '减少高热量、高脂肪食物摄入，控制总热量'
              );
            }
          }

          if (processedContent.lifestyleAndMentalHealth.lifestyle.exercise.length < 2) {
            if (bmi < 18.5) {
              processedContent.lifestyleAndMentalHealth.lifestyle.exercise.push(
                '进行力量训练，帮助增加肌肉质量'
              );
            } else if (bmi >= 24) {
              processedContent.lifestyleAndMentalHealth.lifestyle.exercise.push(
                '增加有氧运动频率，每周至少5天，每天30分钟以上'
              );
            }
          }
        }
      }

      // 9. 创建与报告关联的记录
      const record = await Record.query().insert({
        id: uuidv4(),
        disease_id: diseaseId,
        patient_id: patientId,
        user_id: userId,
        title: `${patientName}_${diseaseName}_辅医智能分析报告记录`,
        content: processedContent.summary,
        record_type: 'AI_ANALYSIS',
        primary_type: 'MEDICAL',
        severity: processedContent.emergencyGuidance.isEmergency ? 'HIGH' : 'MODERATE',
        is_important: processedContent.emergencyGuidance.isEmergency,
        is_private: false,
        record_date: new Date().toISOString(),
        created_by: userId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        // 提取关键信息保存到data字段
        data: JSON.stringify({
          // 医院推荐信息
          hospitalRecommendations: processedContent.hospitalRecommendations,

          // 生活方式和心理健康建议
          lifestyle: processedContent.lifestyleAndMentalHealth?.lifestyle || {
            diet: [],
            exercise: [],
            habits: []
          },
          mentalHealth: processedContent.lifestyleAndMentalHealth?.mentalHealth || {
            copingStrategies: [],
            resources: []
          },

          // --- 修改 dashboardData 开始 ---
          dashboardData: {
            // 首先展开 processedContent 中已有的 dashboardData 属性
            ...(processedContent.dashboardData || {}),
            // 然后确保/覆盖我们期望的 status, trend, riskLevel
            status: processedContent.dashboardData?.status || '治疗中',
            trend: processedContent.dashboardData?.trend || '稳定',
            riskLevel: processedContent.dashboardData?.riskLevel || '中风险',
            // 最后，构建 keyIndicators 数组
            keyIndicators: [
              { name: "LDH", value: "待更新" },
              { name: "β2微球蛋白", value: "待更新" },
              { name: "Deauville评分", value: "待评估" },
              {
                name: "SUVmax",
                value: extractSuvMax(processedContent.summary || medicalData.disease?.examination_results || '')
              },
              {
                name: "BMI",
                value: ((bmiVal) => bmiVal !== null ? String(bmiVal) : "待更新")(calculateBMI(medicalData.patient?.weight, medicalData.patient?.height))
              }
            ]
          },
          // --- 修改 dashboardData 结束 ---

          // 风险警告
          riskWarnings: processedContent.riskWarnings || [],

          // BMI建议(如果有)
          bmiRecommendations: processedContent.bmiRecommendations || null,

          // 来源信息
          source: {
            type: 'AI_REPORT',
            reportId: aiReport.id,
            generatedAt: new Date().toISOString()
          }
        })
      });

      // 10. 更新报告状态和内容
      await AIReport.query().findById(aiReport.id).patch({
        record_id: record.id,
        content: processedContent,
        llm_raw_response: JSON.stringify(llmResponse.rawResponse),
        status: 'COMPLETED',
        updated_at: new Date().toISOString()
      });

      // 重新获取更新后的报告
      aiReport = await AIReport.query().findById(aiReport.id);

      return {
        aiReport,
        recordId: record.id
      };
    } catch (llmError) {
      console.error('LLM服务调用或处理失败:', llmError);

      // 更新报告状态为失败
      await AIReport.query().findById(aiReport.id).patch({
            status: 'FAILED',
        error_message: `LLM服务调用失败: ${llmError.message}`,
            updated_at: new Date().toISOString()
      });

      // 抛出错误，让API明确返回失败状态
      throw new Error(`生成AI报告失败: LLM服务调用错误 - ${llmError.message}`);
    }
  } catch (error) {
    console.error('生成AI报告失败:', error);

    // 更新报告状态为失败
    if (aiReport) {
      await AIReport.query().findById(aiReport.id).patch({
        status: 'FAILED',
        error_message: error.message,
        updated_at: new Date().toISOString()
      });
    }

    throw error;
  }
};

module.exports = {
  generateAIReport,
  getMedicalData,
  processLlmResponse
};