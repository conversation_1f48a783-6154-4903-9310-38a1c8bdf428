/**
 * CORS调试工具 - 用于检测和调试CORS相关问题
 */

/**
 * 检查CORS配置是否正确
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
function debugCorsHeaders(req, res) {
  console.log('=== CORS调试信息 ===');
  console.log('请求方法:', req.method);
  console.log('请求路径:', req.path);
  console.log('请求源 (Origin):', req.headers.origin);
  console.log('请求查询参数:', req.query);
  console.log('请求头:', JSON.stringify(req.headers, null, 2));
  
  // 查看请求的具体内容
  if (req.method === 'OPTIONS') {
    console.log('预检请求头部:');
    console.log('Access-Control-Request-Method:', req.headers['access-control-request-method']);
    console.log('Access-Control-Request-Headers:', req.headers['access-control-request-headers']);
  }
  
  // 记录响应头信息
  res.on('finish', () => {
    const responseHeaders = res.getHeaders ? res.getHeaders() : res._headers;
    console.log('响应状态码:', res.statusCode);
    console.log('响应头:', JSON.stringify(responseHeaders, null, 2));
    
    // 检查是否设置了关键CORS头
    const hasCorsOrigin = !!responseHeaders['access-control-allow-origin'];
    const hasCorsHeaders = !!responseHeaders['access-control-allow-headers'];
    const hasCorsMethods = !!responseHeaders['access-control-allow-methods'];
    const hasExposedHeaders = !!responseHeaders['access-control-expose-headers'];
    const hasCredentials = !!responseHeaders['access-control-allow-credentials'];
    
    console.log('CORS头部检查结果:');
    console.log('- Access-Control-Allow-Origin:', hasCorsOrigin ? '已设置 ✅' : '未设置 ❌');
    console.log('- Access-Control-Allow-Headers:', hasCorsHeaders ? '已设置 ✅' : '未设置 ❌');
    console.log('- Access-Control-Allow-Methods:', hasCorsMethods ? '已设置 ✅' : '未设置 ❌');
    console.log('- Access-Control-Expose-Headers:', hasExposedHeaders ? `已设置 ✅ (${responseHeaders['access-control-expose-headers']})` : '未设置 ❌');
    console.log('- Access-Control-Allow-Credentials:', hasCredentials ? '已设置 ✅' : '未设置 ❌');
    
    // 检查CORS配置问题
    const isOriginWildcard = responseHeaders['access-control-allow-origin'] === '*';
    if (isOriginWildcard && hasCredentials) {
      console.log('⚠️ 警告: Access-Control-Allow-Origin设置为"*"，而同时设置了Access-Control-Allow-Credentials为true，这是无效的配置');
    }
    
    console.log('=== CORS调试结束 ===');
  });
}

/**
 * 中间件：为所有下载接口添加额外的CORS头部
 */
function addDownloadCorsHeaders(req, res, next) {
  // 专门为下载路径添加额外CORS头
  if (req.path.includes('/attachments/download/')) {
    // 获取请求的Origin，如果没有则使用*
    const origin = req.headers.origin || '*';
    
    // 检查是否为OPTIONS预检请求
    const isOptionsRequest = req.method === 'OPTIONS';
    
    // 检查是否包含服务上下文标志
    const hasServiceContext = req.query.service_context === 'true';
    
    console.log(`处理附件下载请求: ${req.path}, 方法: ${req.method}, Origin: ${origin}, 服务上下文: ${hasServiceContext}`);
    
    // 确保已设置CORS头，即使请求没有经过主CORS中间件
    res.header('Access-Control-Allow-Origin', origin);
    
    // 只有当Origin不是通配符时才可以设置凭证允许
    if (origin !== '*') {
      res.header('Access-Control-Allow-Credentials', 'true');
    }
    
    res.header('Access-Control-Allow-Methods', 'GET, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    res.header('Access-Control-Expose-Headers', 'Content-Disposition, Content-Type, Content-Length, Content-Range, Content-Description');
    
    // 如果是服务上下文请求，添加更多详细的日志
    if (hasServiceContext) {
      console.log('服务上下文附件下载请求，查询参数:', req.query);
      console.log('Authorization头部存在:', !!req.headers.authorization);
    }
    
    // 如果是预检请求，立即响应
    if (isOptionsRequest) {
      console.log('处理附件下载预检请求，路径:', req.path);
      return res.status(204).send();
    }
    
    console.log('已为下载附件请求添加CORS头部');
  }
  
  next();
}

module.exports = {
  debugCorsHeaders,
  addDownloadCorsHeaders
}; 