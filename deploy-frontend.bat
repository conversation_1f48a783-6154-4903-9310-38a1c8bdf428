@echo off
chcp 65001 >nul
echo 🚀 开始部署前端...

REM 检查是否在正确的目录
if not exist "frontend" (
    echo ❌ 错误：请在项目根目录运行此脚本
    pause
    exit /b 1
)

REM 进入前端目录
cd frontend

echo 📦 检查前端依赖...
if not exist "node_modules" (
    echo 📥 安装前端依赖...
    npm install
)

echo 🔨 构建前端项目...
npm run build

if %errorlevel% neq 0 (
    echo ❌ 前端构建失败
    pause
    exit /b 1
)

echo ✅ 前端构建成功

REM 检查构建文件
if not exist "build" (
    echo ❌ 构建目录不存在
    pause
    exit /b 1
)

echo 📋 构建文件信息：
dir build\static\js\main.*.js

echo.
echo 🎉 前端构建完成！
echo.
echo 📤 部署到服务器的步骤：
echo 1. 将整个 frontend/build 目录上传到服务器
echo 2. 替换服务器上的 /home/<USER>/HKB/frontend/build 目录
echo 3. 重启 Nginx 服务
echo.
echo 🔧 服务器操作命令：
echo    sudo systemctl reload nginx
echo.
echo 💡 或者使用 Git 同步：
echo    cd /home/<USER>/HKB
echo    git add .
echo    git commit -m "Fix CORS and HTTPS issues"
echo    git push
echo    # 然后在服务器上：
echo    git pull
echo    cd frontend
echo    npm run build
echo.

REM 创建部署包
echo 📦 创建部署包...
cd ..
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%YYYY%%MM%%DD%-%HH%%Min%%Sec%"

powershell Compress-Archive -Path "frontend\build" -DestinationPath "frontend-build-%timestamp%.zip" -Force
echo ✅ 部署包已创建：frontend-build-%timestamp%.zip

echo.
echo 🎯 下一步：
echo 1. 提交代码到 Git
echo 2. 在服务器上拉取最新代码
echo 3. 重新构建前端
echo 4. 重启 Nginx

pause
