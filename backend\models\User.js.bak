const { Model, snakeCaseMappers } = require('objection');
const { v4: uuidv4 } = require('uuid');

class User extends Model {
  static get tableName() {
    return 'users';
  }

  static get idColumn() {
    return 'id';
  }

  static get columnNameMappers() {
    return snakeCaseMappers();
  }

  $beforeInsert() {
    this.id = this.id || uuidv4();
    this.createdAt = new Date().toISOString();
    this.updatedAt = this.createdAt;
    this.isActive = this.isActive ?? true;
  }

  $beforeUpdate() {
    this.updatedAt = new Date().toISOString();
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['username', 'passwordHash'],
      properties: {
        id: { type: 'string' },
        username: { type: 'string', minLength: 3, maxLength: 50 },
        email: { type: ['string', 'null'] },
        phoneNumber: { type: ['string', 'null'] },
        passwordHash: { type: 'string' },
        avatar: { type: ['string', 'null'] },
        role: { type: 'string', enum: ['USER', 'ADMIN'] },
        level: { type: 'string', enum: ['FREE', 'BASIC', 'PREMIUM', 'UNLIMITED'] },
        activeDiseaseLimit: { type: 'integer' },
        aiUsageCount: { type: 'integer' },
        aiUsageResetAt: { type: ['string', 'null'] },
        familyMemberLimit: { type: 'integer' },
        isActive: { type: 'boolean' },
        createdAt: { type: 'string' },
        updatedAt: { type: 'string' },
        lastLoginAt: { type: ['string', 'null'] },
        deletedAt: { type: ['string', 'null'] }
      }
    };
  }

  static get relationMappings() {
    const Patient = require('./Patient');
    
    return {
      patients: {
        relation: Model.HasManyRelation,
        modelClass: Patient,
        join: {
          from: 'users.id',
          to: 'patients.userId'
        }
      }
    };
  }
}

module.exports = User; 