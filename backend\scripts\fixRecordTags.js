/**
 * 修复服务用户创建的记录中custom_tags和stage_tags字段内容互换的问题以及is_important字段缺失问题
 * 此脚本只处理服务用户创建的记录，修正stage_tags、custom_tags和is_important字段
 * 用法: node scripts/fixRecordTags.js
 */

const knex = require('knex');
const { Model } = require('objection');
const path = require('path');
const db = require('../config/database');
const Record = require('../models/Record');
const ServiceRecord = require('../models/ServiceRecord');

// 设置objection连接
Model.knex(db);

/**
 * 检查字符串是否是JSON数组
 * @param {string} str 待检查的字符串
 * @returns {boolean} 是否是JSON数组
 */
function isJsonArray(str) {
  if (!str || typeof str !== 'string') return false;
  try {
    const parsed = JSON.parse(str);
    return Array.isArray(parsed);
  } catch (e) {
    return false;
  }
}

/**
 * 修复服务用户创建的记录
 */
async function fixServiceRecordTags() {
  console.log('开始修复服务用户创建的记录字段...');
  
  try {
    // 获取所有服务记录
    const serviceRecords = await ServiceRecord.query()
      .select('record_id')
      .whereNotNull('record_id');
      
    if (!serviceRecords || serviceRecords.length === 0) {
      console.log('未找到服务记录，跳过此步骤');
      return;
    }
    
    const serviceRecordIds = serviceRecords.map(sr => sr.record_id).filter(Boolean);
    
    if (serviceRecordIds.length === 0) {
      console.log('所有服务记录的record_id都为空，跳过此步骤');
      return;
    }
    
    console.log(`找到 ${serviceRecordIds.length} 条服务用户创建的有效记录ID`);
    
    // 获取所有服务用户创建的记录
    const records = await Record.query()
      .whereIn('id', serviceRecordIds);
      
    console.log(`找到 ${records.length} 条需要处理的记录`);
    
    let tagsFixedCount = 0;
    let importantFixedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;
    
    // 依次处理每条记录
    for (const record of records) {
      try {
        const stageTags = record.stage_tags;
        const customTags = record.custom_tags;
        const isImportant = record.is_important;
        
        let patchData = {};
        let needsPatch = false;
        
        // 检查是否需要修复标签 - 服务用户记录不会把JSON数组写入customTags字段，但可能错误写入stageTags
        const stageTagsIsJsonArray = isJsonArray(stageTags);
        const customTagsIsJsonArray = isJsonArray(customTags);
        
        // 如果stageTags是普通字符串，而customTags是JSON数组，说明字段写入错误，需要交换
        if (!stageTagsIsJsonArray && customTagsIsJsonArray) {
          console.log(`记录 ID: ${record.id}, 需要交换标签字段`);
          patchData.stage_tags = customTags;  // 原来的customTags放入stage_tags
          patchData.custom_tags = stageTags;   // 原来的stageTags放入custom_tags
          needsPatch = true;
          tagsFixedCount++;
        } 
        
        // 检查并修复is_important字段
        if (isImportant === null || isImportant === undefined) {
          console.log(`记录 ID: ${record.id}, 需要修复is_important字段`);
          patchData.is_important = false; // 默认设置为false
          needsPatch = true;
          importantFixedCount++;
        }
        
        // 执行更新
        if (needsPatch) {
          // 更新记录
          await Record.query()
            .findById(record.id)
            .patch(patchData);
            
          console.log(`记录 ID: ${record.id} 已修复`);
        } else {
          console.log(`记录 ID: ${record.id} 无需修复`);
          skippedCount++;
        }
      } catch (err) {
        console.error(`处理记录 ${record.id} 时出错:`, err);
        errorCount++;
      }
    }
    
    console.log('\n修复完成:');
    console.log(`- 总记录数: ${records.length}`);
    console.log(`- 修复标签记录数: ${tagsFixedCount}`);
    console.log(`- 修复重要性标记记录数: ${importantFixedCount}`);
    console.log(`- 跳过记录数: ${skippedCount}`);
    console.log(`- 错误记录数: ${errorCount}`);
    
  } catch (error) {
    console.error('修复过程中发生错误:', error);
  } finally {
    // 关闭数据库连接会在最后runAllFixes中处理
  }
}

/**
 * 检查并修复stage_node和stage_phase字段
 */
async function fixStageFieldsForAllRecords() {
  console.log('\n开始检查并修复所有记录的stage_node和stage_phase字段...');
  
  try {
    // 获取所有记录
    const records = await Record.query()
      .whereNull('stage_node')
      .whereNotNull('stage_phase');
      
    console.log(`找到 ${records.length} 条可能需要修复stage_node的记录`);
    
    let fixedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;
    
    // 阶段到节点的映射关系
    const phaseToNodeMap = {
      'INITIAL': 'INITIAL_VISIT',
      'DIAGNOSIS': 'DIAGNOSIS',
      'TREATMENT': 'TREATMENT',
      'RECOVERY': 'FOLLOW_UP'
    };
    
    // 依次处理每条记录
    for (const record of records) {
      try {
        const stagePhase = record.stage_phase;
        const stageNode = record.stage_node;
        
        // 如果有阶段但没有节点，则根据阶段设置对应的默认节点
        if (stagePhase && !stageNode && phaseToNodeMap[stagePhase]) {
          console.log(`记录 ID: ${record.id}, 根据阶段 ${stagePhase} 设置默认节点 ${phaseToNodeMap[stagePhase]}`);
          
          // 更新记录
          await Record.query()
            .findById(record.id)
            .patch({
              stage_node: phaseToNodeMap[stagePhase]
            });
            
          fixedCount++;
        } else {
          console.log(`记录 ID: ${record.id} - 无法确定默认节点或无需修复`);
          skippedCount++;
        }
      } catch (err) {
        console.error(`处理记录 ${record.id} 时出错:`, err);
        errorCount++;
      }
    }
    
    console.log('\n阶段节点修复完成:');
    console.log(`- 总记录数: ${records.length}`);
    console.log(`- 修复记录数: ${fixedCount}`);
    console.log(`- 跳过记录数: ${skippedCount}`);
    console.log(`- 错误记录数: ${errorCount}`);
    
  } catch (error) {
    console.error('修复过程中发生错误:', error);
  }
}

/**
 * 打印修复完成的统计结果
 */
function printFixSummary() {
  console.log('\n================================================================');
  console.log('所有修复脚本执行完成！');
  console.log('================================================================');
  console.log('以下问题已修复:');
  console.log('1. 服务用户创建的记录中stage_tags和custom_tags字段内容互换的问题');
  console.log('2. 服务用户创建的记录中is_important字段缺失的问题');
  console.log('3. 记录中stage_node字段缺失但有stage_phase的问题');
  console.log('================================================================');
}

// 运行修复程序
async function runAllFixes() {
  try {
    await fixServiceRecordTags();
    await fixStageFieldsForAllRecords();
    printFixSummary();
  } catch (err) {
    console.error('修复过程中发生错误:', err);
  } finally {
    // 关闭数据库连接
    await db.destroy();
    console.log('数据库连接已关闭');
    process.exit(0);
  }
}

// 执行所有修复
runAllFixes().catch(err => {
  console.error('脚本执行失败:', err);
  process.exit(1);
}); 