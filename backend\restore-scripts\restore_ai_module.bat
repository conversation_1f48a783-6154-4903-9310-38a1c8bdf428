@echo off
echo ===== AI模块完全恢复程序 =====

echo 1. 创建恢复环境...
mkdir D:\AZ\backup_current_ai_module 2>nul
mkdir D:\AZ\backup_current_ai_module\services 2>nul
mkdir D:\AZ\backup_current_ai_module\models 2>nul
mkdir D:\AZ\backup_current_ai_module\controllers 2>nul
mkdir D:\AZ\backup_current_ai_module\routes 2>nul

echo 2. 正在备份当前模块...
xcopy /E /I /H /Y D:\AZ\backend\services\aiReport D:\AZ\backup_current_ai_module\services\aiReport
xcopy /E /I /H /Y D:\AZ\backend\models\aiReport D:\AZ\backup_current_ai_module\models\aiReport
xcopy /E /I /H /Y D:\AZ\backend\routes\aiReportRoutes.js D:\AZ\backup_current_ai_module\routes
xcopy /E /I /H /Y D:\AZ\backend\controllers\aiReport D:\AZ\backup_current_ai_module\controllers\aiReport 2>nul
echo 当前模块已备份至 D:\AZ\backup_current_ai_module

echo 3. 正在清除当前有问题的模块...
rd /s /q D:\AZ\backend\services\aiReport 2>nul
rd /s /q D:\AZ\backend\models\aiReport 2>nul
rd /s /q D:\AZ\backend\controllers\aiReport 2>nul
del D:\AZ\backend\routes\aiReportRoutes.js 2>nul

echo 4. 正在从备份恢复完好的模块...
xcopy /E /I /H /Y D:\AZ\backups\LLM_BACKUP\backend\services\aiReport D:\AZ\backend\services\aiReport
xcopy /E /I /H /Y D:\AZ\backups\LLM_BACKUP\backend\models\aiReport D:\AZ\backend\models\aiReport
xcopy /E /I /H /Y D:\AZ\backups\LLM_BACKUP\backend\controllers\aiReport D:\AZ\backend\controllers\aiReport
mkdir D:\AZ\backend\routes\aiReport 2>nul
xcopy /E /I /H /Y D:\AZ\backups\LLM_BACKUP\backend\routes\aiReport D:\AZ\backend\routes\aiReport

echo 5. 创建前后端API路径兼容层...
node D:\AZ\backend\restore-scripts\create_api_compatibility.js

echo 6. 创建命名规则适配器...
node D:\AZ\backend\restore-scripts\create_naming_adapter.js

echo 7. 恢复数据库表结构...
node D:\AZ\backend\restore-scripts\restore_ai_table.js

echo 8. 更新应用配置文件...
node D:\AZ\backend\restore-scripts\update_config.js

echo 9. 重启服务...
echo 请手动执行以下命令重启服务:
echo cd D:\AZ\backend
echo pm2 restart all 或 npm run dev

echo ===== AI模块恢复完成 =====
pause 