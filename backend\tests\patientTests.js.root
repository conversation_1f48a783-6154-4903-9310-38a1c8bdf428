const axios = require('axios');

// 患者API测试
async function testPatientCRUD() {
  // 这里需要一个有效的JWT令牌
  // 实际使用时请替换为通过登录获取的有效令牌
  // 此处仅作为示例，实际运行可能会因为认证问题失败
  const token = 'your_jwt_token'; 
  const headers = { Authorization: `Bearer ${token}` };
  
  try {
    console.log('===== 开始测试患者API =====');
    
    // 1. 获取患者列表
    console.log('\n1. 获取患者列表:');
    const listResponse = await axios.get('http://localhost:3000/patients', { headers });
    console.log(`获取成功，共 ${listResponse.data.length} 个患者`);
    
    // 2. 添加患者
    console.log('\n2. 添加新患者:');
    const patientData = {
      name: '测试患者-' + new Date().getTime().toString().substr(-4),
      gender: '男',
      phoneNumber: '13800138000',
      birthDate: '1990-01-01',
      bloodType: 'A',
      isPrimary: 0, // 不设为本人
    };
    
    try {
      const createResponse = await axios.post('http://localhost:3000/patients', patientData, { headers });
      console.log('添加患者成功:', createResponse.data);
      
      // 3. 获取患者详情
      console.log('\n3. 获取患者详情:');
      const patientId = createResponse.data.patient.id;
      const getResponse = await axios.get(`http://localhost:3000/patients/${patientId}`, { headers });
      console.log('获取患者详情成功:', getResponse.data);
      
      // 4. 编辑患者
      console.log('\n4. 编辑患者:');
      const updateData = {
        name: '更新患者-' + new Date().getTime().toString().substr(-4),
        gender: '女',
        phoneNumber: '13900139000',
        birthDate: '1991-02-02',
        bloodType: 'B',
        isPrimary: 1, // 设为本人
      };
      
      const updateResponse = await axios.put(`http://localhost:3000/patients/${patientId}`, updateData, { headers });
      console.log('编辑患者成功:', updateResponse.data);
      
      // 5. 验证isPrimary逻辑
      console.log('\n5. 验证isPrimary逻辑:');
      console.log('获取更新后的患者列表，检查是否只有一个本人档案');
      const checkPrimaryResponse = await axios.get('http://localhost:3000/patients', { headers });
      const primaryPatients = checkPrimaryResponse.data.filter(p => p.isPrimary === 1);
      console.log(`共有 ${primaryPatients.length} 个本人档案`);
      
      if (primaryPatients.length === 1) {
        console.log('isPrimary逻辑验证成功：只有一个本人档案');
      } else {
        console.error('isPrimary逻辑验证失败：存在多个本人档案');
      }
      
      // 6. 删除患者（需要ADMIN权限）
      console.log('\n6. 尝试删除患者（如果无ADMIN权限可能会失败）:');
      try {
        const deleteResponse = await axios.delete(`http://localhost:3000/patients/${patientId}`, { headers });
        console.log('删除患者成功:', deleteResponse.data);
      } catch (deleteError) {
        console.log('删除患者失败（可能是权限问题）:', deleteError.response?.data || deleteError.message);
      }
    } catch (createError) {
      console.error('创建患者失败（可能已达到患者数量上限）:', createError.response?.data || createError.message);
      
      // 尝试获取已有患者进行测试
      const patientsResponse = await axios.get('http://localhost:3000/patients', { headers });
      if (patientsResponse.data.length > 0) {
        const existingPatient = patientsResponse.data[0];
        console.log(`使用已有患者 ${existingPatient.name} (ID: ${existingPatient.id}) 继续测试`);
        
        // 编辑已有患者
        console.log('\n4. 编辑已有患者:');
        const updateData = {
          name: existingPatient.name,
          gender: existingPatient.gender,
          phoneNumber: existingPatient.phoneNumber || '13800138000',
          birthDate: existingPatient.birthDate,
          bloodType: existingPatient.bloodType,
          isPrimary: existingPatient.isPrimary ? 0 : 1, // 切换isPrimary状态
        };
        
        const updateResponse = await axios.put(`http://localhost:3000/patients/${existingPatient.id}`, updateData, { headers });
        console.log('编辑患者成功:', updateResponse.data);
      } else {
        console.log('没有可用的患者进行测试');
      }
    }
  } catch (error) {
    console.error('测试失败:', error.response?.data || error.message);
  }
  
  console.log('\n===== 患者API测试完成 =====');
}

// 执行测试
testPatientCRUD(); 