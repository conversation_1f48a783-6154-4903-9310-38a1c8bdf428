import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  CircularProgress,
  Card,
  CardContent,
  Grid,
  Menu,
  ListItemIcon,
  ListItemText,
  Tooltip,
  LinearProgress
} from '@mui/material';
import {
  Search as SearchIcon,
  Edit as EditIcon,
  Block as BlockIcon,
  CheckCircle as CheckCircleIcon,
  MoreVert as MoreVertIcon,
  PersonAddAlt1 as PersonAddIcon,
  Visibility as VisibilityIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { useTheme, ThemeProvider, createTheme } from '@mui/material/styles';
import { useMediaQuery } from '@mui/material';
import axios from 'axios';
import { API_BASE_URL } from '../../config/api';
import { API_PATHS } from '../../config/apiPaths';
import { useSnackbar } from 'notistack';
import { useAuthStore } from '../../store/authStore';

// 用户对象接口
interface User {
  id: string;
  username: string;
  email: string;
  role: 'USER' | 'SERVICE' | 'ADMIN';
  userLevel: 'PERSONAL' | 'FAMILY' | 'PROFESSIONAL';
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
  quotaRemaining: number;
  quotaTotal: number;
  createdAt: string;
  lastLogin?: string;
}

/**
 * 用户管理页面组件
 * 用于系统管理员管理所有用户
 */
const UserManagementPage: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { enqueueSnackbar } = useSnackbar();
  const { user: currentUser } = useAuthStore();

  // 用户列表状态
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 搜索和分页状态
  const [searchKeyword, setSearchKeyword] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // 编辑用户对话框状态
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [currentEditUser, setCurrentEditUser] = useState<User | null>(null);
  const [editUserRole, setEditUserRole] = useState<string>('');
  const [editUserLevel, setEditUserLevel] = useState<string>('');
  const [editUserStatus, setEditUserStatus] = useState<string>('');
  const [editUserQuota, setEditUserQuota] = useState<number>(0);
  const [isUpdating, setIsUpdating] = useState(false);

  // 确认对话框状态
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [confirmDialogTitle, setConfirmDialogTitle] = useState('');
  const [confirmDialogContent, setConfirmDialogContent] = useState('');
  const [confirmDialogAction, setConfirmDialogAction] = useState<() => Promise<void>>(() => Promise.resolve());

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedUserForMenu, setSelectedUserForMenu] = useState<User | null>(null);

  // Create a theme with reduced font size
  const currentGlobalTheme = theme; // Use the existing theme directly
  const reducedFontSizeTheme = createTheme(currentGlobalTheme, {
    typography: {
      ...currentGlobalTheme.typography,
      h1: { ...currentGlobalTheme.typography.h1, fontSize: currentGlobalTheme.typography.h1?.fontSize ? `calc(${currentGlobalTheme.typography.h1.fontSize} - 0.4rem)`:'5.6rem' },
      h2: { ...currentGlobalTheme.typography.h2, fontSize: currentGlobalTheme.typography.h2?.fontSize ? `calc(${currentGlobalTheme.typography.h2.fontSize} - 0.3rem)`:'3.45rem' },
      h3: { ...currentGlobalTheme.typography.h3, fontSize: currentGlobalTheme.typography.h3?.fontSize ? `calc(${currentGlobalTheme.typography.h3.fontSize} - 0.25rem)`:'2.75rem' },
      h4: { ...currentGlobalTheme.typography.h4, fontSize: currentGlobalTheme.typography.h4?.fontSize ? `calc(${currentGlobalTheme.typography.h4.fontSize} - 0.2rem)`:'1.925rem' },
      h5: { ...currentGlobalTheme.typography.h5, fontSize: '1.0rem' }, // Original: 1.5rem (example)
      h6: { ...currentGlobalTheme.typography.h6, fontSize: '0.85rem' }, // Original: 1.25rem (example)
      subtitle1: { ...currentGlobalTheme.typography.subtitle1, fontSize: '0.75rem' },
      subtitle2: { ...currentGlobalTheme.typography.subtitle2, fontSize: '0.65rem' },
      body1: { ...currentGlobalTheme.typography.body1, fontSize: '0.75rem' },
      body2: { ...currentGlobalTheme.typography.body2, fontSize: '0.65rem' },
      button: { ...currentGlobalTheme.typography.button, fontSize: '0.65rem' },
      caption: { ...currentGlobalTheme.typography.caption, fontSize: '0.55rem' },
      overline: { ...currentGlobalTheme.typography.overline, fontSize: '0.55rem' },
    },
    components: {
      ...currentGlobalTheme.components,
      MuiInputLabel: {
        styleOverrides: {
          root: { fontSize: '0.75rem' }
        }
      },
      MuiMenuItem: {
        styleOverrides: {
          root: { fontSize: '0.75rem' }
        }
      },
      MuiTableCell: {
        styleOverrides: {
          root: { fontSize: '0.65rem' },
          head: { fontSize: '0.7rem', fontWeight: 'bold' }
        }
      },
      MuiChip: {
        styleOverrides: {
          label: { fontSize: '0.55rem' },
          labelSmall: { fontSize: '0.5rem' }
        }
      },
      MuiButton: {
        styleOverrides: {
          sizeSmall: { fontSize: '0.6rem' },
          sizeMedium: { fontSize: '0.65rem' },
          sizeLarge: { fontSize: '0.75rem' }
        }
      },
      MuiDialogTitle: {
        styleOverrides: {
          root: { fontSize: '0.85rem' }
        }
      },
      MuiDialogContentText: {
        styleOverrides: {
          root: { fontSize: '0.75rem' }
        }
      },
      MuiFormControlLabel: {
        styleOverrides: {
          label: { fontSize: '0.75rem' }
        }
      },
      MuiAlert: { // Assuming MuiAlert might be used, though not explicitly seen in provided snippets for UserManagementPage
        styleOverrides: {
          message: { fontSize: '0.65rem' },
        }
      },
      MuiTablePagination: { // For pagination text
        styleOverrides: {
          caption: { fontSize: '0.65rem' },
          selectLabel: { fontSize: '0.65rem' },
          displayedRows: { fontSize: '0.65rem' }, // For "1-10 of 20"
          actions: { // For buttons like "Next page"
            '& .MuiIconButton-root': {
               // fontSize: '0.8rem', // Icons might not need font size
            }
          }
        }
      }
    }
  });

  // 初始加载用户数据
  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => {
    fetchUsers();
  }, []);

  // 当搜索关键字变化时，过滤用户列表
  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => {
    filterUsers();
  }, [searchKeyword, users]);

  // 获取所有用户
  const fetchUsers = async () => {
    setLoading(true);
    setError(null);
    console.log('[UserManagementPage] fetchUsers: Fetching users...');
    try {
      // 使用API_PATHS常量确保路径一致性
      const response = await axios.get(`${API_BASE_URL}${API_PATHS.ADMIN.USERS}?_t=${new Date().getTime()}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      // 确保API返回的数据符合User接口定义
      const userData: User[] = Array.isArray(response.data)
        ? response.data.map((user: any) => ({
            id: user.id,
            username: user.username,
            email: user.email,
            role: user.role as 'USER' | 'SERVICE' | 'ADMIN',
            userLevel: user.userLevel as 'PERSONAL' | 'FAMILY' | 'PROFESSIONAL',
            status: user.status as 'ACTIVE' | 'INACTIVE' | 'SUSPENDED',
            quotaRemaining: user.quotaRemaining || 0,
            quotaTotal: user.quotaTotal || 0,
            createdAt: user.createdAt,
            lastLogin: user.lastLogin
          }))
        : [];

      console.log('[UserManagementPage] fetchUsers: Users fetched successfully:', userData);
      setUsers(userData);
      setFilteredUsers(userData);
    } catch (err: any) {
      console.error('获取用户列表失败:', err);
      setError(err.response?.data?.message || '获取用户列表失败');
      enqueueSnackbar('获取用户列表失败', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // 根据关键字过滤用户
  const filterUsers = () => {
    if (!searchKeyword.trim()) {
      setFilteredUsers(users);
      return;
    }

    const keyword = searchKeyword.toLowerCase().trim();
    const filtered = users.filter(user =>
      user.username.toLowerCase().includes(keyword) ||
      user.email.toLowerCase().includes(keyword) ||
      user.role.toLowerCase().includes(keyword)
    );

    setFilteredUsers(filtered);
    setPage(0); // 重置页码
  };

  // 处理搜索输入变化
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchKeyword(event.target.value);
  };

  // 处理分页变化
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  // 处理每页行数变化
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // 打开编辑用户对话框
  const handleEditUser = (user: User) => {
    setCurrentEditUser(user);
    setEditUserRole(user.role);
    setEditUserLevel(user.userLevel);
    setEditUserStatus(user.status);
    setEditUserQuota(user.quotaTotal);
    setEditDialogOpen(true);
  };

  // 关闭编辑用户对话框
  const handleCloseEditDialog = () => {
    setEditDialogOpen(false);
    setCurrentEditUser(null);
  };

  // 打开确认对话框
  const openConfirmDialog = (title: string, content: string, action: () => Promise<void>) => {
    setConfirmDialogTitle(title);
    setConfirmDialogContent(content);
    setConfirmDialogAction(() => action);
    setConfirmDialogOpen(true);
  };

  // 关闭确认对话框
  const handleCloseConfirmDialog = () => {
    setConfirmDialogOpen(false);
  };

  // 执行确认对话框的操作
  const handleConfirmAction = async () => {
    try {
      await confirmDialogAction();
      handleCloseConfirmDialog();
    } catch (error) {
      console.error('执行操作失败:', error);
    }
  };

  // 保存用户编辑
  const handleSaveUserEdit = async () => {
    if (!currentEditUser) return;

    setIsUpdating(true);

    try {
      // 使用API_PATHS常量确保路径一致性
      const response = await axios.put(`${API_BASE_URL}${API_PATHS.ADMIN.USERS}/${currentEditUser.id}`, {
        role: editUserRole,
        userLevel: editUserLevel,
        status: editUserStatus,
        quotaTotal: editUserQuota
      }, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      // 确保API返回的数据符合User接口
      const updatedUser: User = {
        id: response.data.id,
        username: response.data.username,
        email: response.data.email,
        role: response.data.role as 'USER' | 'SERVICE' | 'ADMIN',
        userLevel: response.data.userLevel as 'PERSONAL' | 'FAMILY' | 'PROFESSIONAL',
        status: response.data.status as 'ACTIVE' | 'INACTIVE' | 'SUSPENDED',
        quotaRemaining: response.data.quotaRemaining || 0,
        quotaTotal: response.data.quotaTotal || 0,
        createdAt: response.data.createdAt,
        lastLogin: response.data.lastLogin
      };

      // 更新本地状态
      const updatedUsers = users.map(user =>
        user.id === currentEditUser.id ? updatedUser : user
      );

      setUsers(updatedUsers);
      enqueueSnackbar('用户信息已更新', { variant: 'success' });
      handleCloseEditDialog();
    } catch (err: any) {
      console.error('更新用户失败:', err);
      enqueueSnackbar(err.response?.data?.message || '更新用户失败', { variant: 'error' });
    } finally {
      setIsUpdating(false);
    }
  };

  // 更改用户状态（激活/停用）
  const handleToggleUserStatus = async (user: User) => {
    const newStatus = user.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE';
    const action = user.status === 'ACTIVE' ? '停用' : '激活';

    openConfirmDialog(
      `确认${action}用户`,
      `您确定要${action}用户 "${user.username}" 吗？`,
      async () => {
        try {
          console.log(`[UserManagementPage] handleToggleUserStatus: Attempting to set status to ${newStatus} for user ${user.username}`);
          // 使用API_PATHS常量确保路径一致性
          await axios.patch(`${API_BASE_URL}${API_PATHS.ADMIN.USERS}/${user.id}/status`, {
            status: newStatus
          }, {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('token')}`
            }
          });
          enqueueSnackbar(`用户 ${user.username} 已${newStatus === 'ACTIVE' ? '启用' : '禁用'}`, { variant: 'success' });
          console.log(`[UserManagementPage] handleToggleUserStatus: Status for ${user.username} updated. Calling fetchUsers().`);
          fetchUsers();
        } catch (err: any) {
          console.error('更新用户状态失败:', err);
          enqueueSnackbar(`更新用户状态失败: ${err.response?.data?.message || err.message}`, { variant: 'error' });
        }
      }
    );
  };

  // 获取用户角色显示名称
  const getRoleName = (role: string) => {
    switch (role) {
      case 'ADMIN': return '系统管理员';
      case 'SERVICE': return '服务用户';
      case 'USER': return '普通用户';
      default: return role;
    }
  };

  // 获取用户等级显示名称
  const getUserLevelName = (level: string) => {
    switch (level) {
      case 'PERSONAL': return '个人';
      case 'FAMILY': return '家庭';
      case 'PROFESSIONAL': return '专业';
      default: return level;
    }
  };

  // 获取用户状态显示名称及颜色
  const getUserStatusInfo = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return { label: '正常', color: 'success' as const };
      case 'INACTIVE':
        return { label: '已停用', color: 'error' as const };
      case 'SUSPENDED':
        return { label: '已暂停', color: 'warning' as const };
      default:
        return { label: status, color: 'default' as const };
    }
  };

  // 格式化日期
  const formatDate = (dateString?: string) => {
    if (!dateString) return '从未登录';

    const date = new Date(dateString);
    return isNaN(date.getTime())
      ? '无效日期'
      : date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        });
  };

  // 计算配额使用百分比
  const calculateQuotaPercentage = (remaining: number, total: number) => {
    if (total === 0) return 0;
    return Math.max(0, Math.min(100, Math.round((1 - remaining / total) * 100)));
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, user: User) => {
    setAnchorEl(event.currentTarget);
    setSelectedUserForMenu(user);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedUserForMenu(null);
  };

  const handleEditFromMenu = () => {
    if (selectedUserForMenu) {
      handleEditUser(selectedUserForMenu);
    }
    handleMenuClose();
  };

  const handleToggleStatusFromMenu = () => {
    if (selectedUserForMenu) {
      handleToggleUserStatus(selectedUserForMenu);
    }
    handleMenuClose();
  };

  // 表格列定义 (memoized for performance)
  const columns = useMemo(() => [
    { id: 'username', label: '用户名', minWidth: 170 },
    { id: 'email', label: '邮箱', minWidth: 170 },
    { id: 'role', label: '角色', minWidth: 100 },
    { id: 'userLevel', label: '级别', minWidth: 100 },
    { id: 'status', label: '状态', minWidth: 100 },
    { id: 'quota', label: 'AI配额', minWidth: 120 },
    { id: 'createdAt', label: '注册时间', minWidth: 170 },
    { id: 'lastLogin', label: '最后登录', minWidth: 170 },
    { id: 'actions', label: '操作', minWidth: 120, align: 'right' as const },
  ], []);

  return (
    <ThemeProvider theme={reducedFontSizeTheme}>
      <Box sx={{ /* px: { xs: '10px', md: 3 }, */ py: { xs: 2, md: 3 } }}>
        <Typography variant="h6" component="h1" gutterBottom>
          用户管理
        </Typography>

        {/* Search and Actions Bar */}
        <Paper elevation={1} sx={{ p: { xs: '10px', sm: 2}, mb: 3 }}>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, alignItems: 'center', px: 0 }}>
            <Box sx={{ flex: '1 1 300px', minWidth: 0 }}>
              <TextField
                fullWidth
                variant="outlined"
                size="small"
                placeholder="搜索用户 (用户名, 邮箱, 角色)..."
                value={searchKeyword}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Box>
            <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
              <Button
                variant="outlined"
                color="primary"
                startIcon={<RefreshIcon />}
                onClick={() => fetchUsers()}
                disabled={loading}
              >
                刷新
              </Button>
            </Box>
          </Box>
        </Paper>

        {/* Main Content Area */}
        {loading && <CircularProgress sx={{ display: 'block', margin: 'auto', my: 4 }} />}
        {error && <Typography color="error" sx={{ textAlign: 'center', my: 4 }}>{error}</Typography>}

        {!loading && !error && (
          <>
            {isMobile ? (
              <Box>
                {filteredUsers.length === 0 ? (
                  <Typography sx={{ textAlign: 'center', my: 4 }}>未找到用户。</Typography>
                ) : (
                  (rowsPerPage > 0
                    ? filteredUsers.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                    : filteredUsers
                  ).map((user) => (
                    <Card key={user.id} sx={{ mb: 2, boxShadow: 3 }}>
                      <CardContent sx={{ px: '10px', '&:last-child': { pb: '10px' } }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                          <Typography variant="h6" component="div" sx={{ fontWeight: 'bold' }}>
                            {user.username}
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Chip
                              label={getUserStatusInfo(user.status).label}
                              color={getUserStatusInfo(user.status).color as any}
                              size="small"
                              sx={{ mr: 1 }}
                            />
                            <IconButton size="small" onClick={(event) => handleMenuOpen(event, user)} sx={{ p: 0.5 }}>
                              <MoreVertIcon />
                            </IconButton>
                          </Box>
                        </Box>
                        <Typography sx={{ mb: 0.5 }} color="text.secondary">
                          {user.email}
                        </Typography>
                        <Typography variant="body2">
                          角色: {getRoleName(user.role)} | 级别: {getUserLevelName(user.userLevel)}
                        </Typography>
                        <Typography variant="body2">
                          AI 配额: {user.quotaTotal - user.quotaRemaining} / {user.quotaTotal}
                        </Typography>
                        <Typography variant="caption" display="block" sx={{ mt: 1, color: 'text.disabled' }}>
                          注册于: {formatDate(user.createdAt)}
                        </Typography>
                        {user.lastLogin && (
                          <Typography variant="caption" display="block" sx={{ color: 'text.disabled' }}>
                            最后登录: {formatDate(user.lastLogin)}
                          </Typography>
                        )}
                      </CardContent>
                    </Card>
                  ))
                )}
                <Menu
                  anchorEl={anchorEl}
                  open={Boolean(anchorEl)}
                  onClose={handleMenuClose}
                >
                  <MenuItem onClick={handleEditFromMenu}>
                    <ListItemIcon>
                      <EditIcon fontSize="small" />
                    </ListItemIcon>
                    <ListItemText>编辑用户</ListItemText>
                  </MenuItem>
                  <MenuItem onClick={handleToggleStatusFromMenu}>
                    <ListItemIcon>
                      {selectedUserForMenu?.status === 'ACTIVE' ?
                        <BlockIcon fontSize="small" /> :
                        <CheckCircleIcon fontSize="small" />
                      }
                    </ListItemIcon>
                    <ListItemText>
                      {selectedUserForMenu?.status === 'ACTIVE' ? '禁用用户' : '激活用户'}
                    </ListItemText>
                  </MenuItem>
                </Menu>
                {filteredUsers.length > 0 && (
                  <TablePagination
                    component="div"
                    count={filteredUsers.length}
                    rowsPerPage={rowsPerPage}
                    page={page}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                    rowsPerPageOptions={[5, 10, 25]}
                    labelRowsPerPage="每页:"
                    sx={{ mt: 2 }}
                  />
                )}
              </Box>
            ) : (
              <Paper sx={{ width: '100%', overflow: 'hidden' }}>
                <TableContainer sx={{ maxHeight: 'calc(100vh - 300px)' }}>
                  <Table stickyHeader aria-label="sticky table">
                    <TableHead>
                      <TableRow>
                        {columns.map((column) => (
                          <TableCell
                            key={column.id}
                            align={column.align}
                            style={{ minWidth: column.minWidth, fontWeight: 'bold' }}
                          >
                            {column.label}
                          </TableCell>
                        ))}
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {filteredUsers.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={columns.length} align="center">
                            未找到用户。
                          </TableCell>
                        </TableRow>
                      ) : (
                        (rowsPerPage > 0
                          ? filteredUsers.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                          : filteredUsers
                        ).map((user) => (
                          <TableRow hover role="checkbox" tabIndex={-1} key={user.id}>
                            <TableCell>{user.username}</TableCell>
                            <TableCell>{user.email}</TableCell>
                            <TableCell>{getRoleName(user.role)}</TableCell>
                            <TableCell>{getUserLevelName(user.userLevel)}</TableCell>
                            <TableCell>
                              <Chip
                                label={getUserStatusInfo(user.status).label}
                                color={getUserStatusInfo(user.status).color as any}
                                size="small"
                              />
                            </TableCell>
                            <TableCell>
                              {user.quotaTotal - user.quotaRemaining} / {user.quotaTotal}
                            </TableCell>
                            <TableCell>{formatDate(user.createdAt)}</TableCell>
                            <TableCell>{user.lastLogin ? formatDate(user.lastLogin) : '-'}</TableCell>
                            <TableCell align="right">
                              <IconButton onClick={() => handleEditUser(user)} size="small" sx={{ mr: 0.5 }}>
                                <EditIcon fontSize="small" />
                              </IconButton>
                              <IconButton onClick={() => handleToggleUserStatus(user)} size="small">
                                {user.status === 'ACTIVE' ?
                                  <BlockIcon fontSize="small" color="error" /> :
                                  <CheckCircleIcon fontSize="small" color="success" />
                                }
                              </IconButton>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
                {filteredUsers.length > 0 && (
                  <TablePagination
                    rowsPerPageOptions={[10, 25, 50, 100]}
                    component="div"
                    count={filteredUsers.length}
                    rowsPerPage={rowsPerPage}
                    page={page}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                    labelRowsPerPage="每页行数:"
                  />
                )}
              </Paper>
            )}
          </>
        )}

        {/* 编辑用户对话框 */}
        <Dialog open={editDialogOpen} onClose={handleCloseEditDialog} fullWidth maxWidth="sm">
          <DialogTitle>编辑用户 - {currentEditUser?.username}</DialogTitle>
          <DialogContent>
            <FormControl fullWidth margin="dense">
              <InputLabel id="edit-role-label">角色</InputLabel>
              <Select
                labelId="edit-role-label"
                value={editUserRole}
                label="角色"
                onChange={(e) => setEditUserRole(e.target.value)}
              >
                <MenuItem value="USER">普通用户</MenuItem>
                <MenuItem value="SERVICE">服务用户</MenuItem>
                <MenuItem value="ADMIN">系统管理员</MenuItem>
              </Select>
            </FormControl>
            <FormControl fullWidth margin="dense">
              <InputLabel id="edit-level-label">等级</InputLabel>
              <Select
                labelId="edit-level-label"
                value={editUserLevel}
                label="等级"
                onChange={(e) => setEditUserLevel(e.target.value)}
              >
                <MenuItem value="PERSONAL">个人</MenuItem>
                <MenuItem value="FAMILY">家庭</MenuItem>
                <MenuItem value="PROFESSIONAL">专业</MenuItem>
              </Select>
            </FormControl>
            <FormControl fullWidth margin="dense">
              <InputLabel id="edit-status-label">状态</InputLabel>
              <Select
                labelId="edit-status-label"
                value={editUserStatus}
                label="状态"
                onChange={(e) => setEditUserStatus(e.target.value)}
              >
                <MenuItem value="ACTIVE">{getUserStatusInfo('ACTIVE').label}</MenuItem>
                <MenuItem value="INACTIVE">{getUserStatusInfo('INACTIVE').label}</MenuItem>
                <MenuItem value="SUSPENDED">{getUserStatusInfo('SUSPENDED').label}</MenuItem>
              </Select>
            </FormControl>
            <TextField
              margin="dense"
              label="AI 配额上限"
              type="number"
              fullWidth
              value={editUserQuota}
              onChange={(e) => setEditUserQuota(Math.max(0, parseInt(e.target.value, 10) || 0))}
              InputProps={{
                inputProps: { min: 0 }
              }}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseEditDialog} color="inherit">取消</Button>
            <Button onClick={handleSaveUserEdit} color="primary" disabled={isUpdating}>
              {isUpdating ? <CircularProgress size={24} /> : '保存'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* 确认操作对话框 */}
        <Dialog
          open={confirmDialogOpen}
          onClose={() => setConfirmDialogOpen(false)}
        >
          <DialogTitle>{confirmDialogTitle}</DialogTitle>
          <DialogContent>
            <DialogContentText>{confirmDialogContent}</DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setConfirmDialogOpen(false)} color="inherit">取消</Button>
            <Button onClick={handleConfirmAction} color="primary" autoFocus>
              确认
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </ThemeProvider>
  );
};

export default UserManagementPage;