import { create } from 'zustand';

interface AIUsageState {
  aiUsageCount: number;
  maxAiUsage: number;
  setAiUsageCount: (count: number) => void;
  setMaxAiUsage: (max: number) => void;
  incrementAiUsage: () => void;
  resetAiUsage: () => void;
}

export const useAiUsageStore = create<AIUsageState>((set) => ({
  aiUsageCount: 0,
  maxAiUsage: 10, // 默认每月10次
  setAiUsageCount: (count) => set({ aiUsageCount: count }),
  setMaxAiUsage: (max) => set({ maxAiUsage: max }),
  incrementAiUsage: () => set((state) => ({ aiUsageCount: state.aiUsageCount + 1 })),
  resetAiUsage: () => set({ aiUsageCount: 0 }),
})); 