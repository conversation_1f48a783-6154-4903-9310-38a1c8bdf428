import React, { useState, useEffect, useCallback } from 'react';
import { 
  Box, 
  Typography, 
  TextField, 
  Button, 
  Stack,
  Divider,
  Alert,
  Avatar,
  CircularProgress,
  TableContainer,
} from '@mui/material';
import { useQuery, useMutation } from '@tanstack/react-query';
import { getUserProfile, updateUserProfile, logout, getProfileWithDirectFetch } from '../services/authService';
import { UserProfile } from '../types/user';
import EditIcon from '@mui/icons-material/Edit';
import LogoutIcon from '@mui/icons-material/Logout';
import { useAuthStore } from '../store/authStore';
import statisticsService from '../services/statisticsService';
import { useNavigate } from 'react-router-dom';
import { useAiUsage } from '../context/AiUsageContext';

// 定义格式化存储空间的函数
const formatStorageSize = (sizeInKB: number | undefined): string => {
  if (sizeInKB === undefined || sizeInKB === null) return '未知';
  if (sizeInKB === -1) return '无限制';
  
  if (sizeInKB < 1024) {
    return `${sizeInKB} KB`;
  } else if (sizeInKB < 1024 * 1024) {
    return `${(sizeInKB / 1024).toFixed(2)} MB`;
  } else {
    return `${(sizeInKB / (1024 * 1024)).toFixed(2)} GB`;
  }
};

// 个人资料页面组件
const ProfilePage: React.FC = () => {
  const navigate = useNavigate(); // 添加导航钩子
  
  // 使用直接fetch获取的数据状态
  const [directFetchData, setDirectFetchData] = useState<UserProfile | null>(null);
  const [isDirectFetching, setIsDirectFetching] = useState(false);
  const [directFetchError, setDirectFetchError] = useState<Error | null>(null);
  
  // 用户限制数据的独立状态
  const [userLimits, setUserLimits] = useState({
    maxPatients: undefined as number | undefined,
    maxPathologies: undefined as number | undefined,
    maxAttachmentSize: undefined as number | undefined,
    maxTotalStorage: undefined as number | undefined,
    maxAiUsed: undefined as number | undefined
  });
  const [isLoadingLimits, setIsLoadingLimits] = useState(false);
  
  // 使用React Query获取用户资料
  const { 
    data: profile, 
    isLoading, 
    isError, 
    error,
    refetch 
  } = useQuery<UserProfile, Error>({
    queryKey: ['userProfile'],
    queryFn: getUserProfile
  });
  
  // 获取数据 - 结合两种来源的数据
  const userData = directFetchData || profile;
  
  // 使用AI使用上下文
  const { aiUsageCount, maxAiUsage, refreshAiUsage } = useAiUsage();
  
  // 使用直接fetch方法获取用户数据
  const handleDirectFetch = useCallback(async () => {
    try {
      setIsDirectFetching(true);
      setDirectFetchError(null);
      
      console.log('开始直接获取用户数据...');
      const data = await getProfileWithDirectFetch();
      
      console.log('直接获取的用户数据:', {
        ...data,
        avatar: data.avatar
      });
      
      setDirectFetchData(data);
      
      // 同时也通过React Query刷新数据
      refetch();
      
      // 刷新AI使用数据
      refreshAiUsage();
    } catch (err) {
      console.error('直接获取数据失败:', err);
      setDirectFetchError(err as Error);
    } finally {
      setIsDirectFetching(false);
    }
  }, [refetch, refreshAiUsage]);
  
  // 首次加载时使用直接获取方式
  useEffect(() => {
    handleDirectFetch();
  }, [handleDirectFetch]);
  
  // 直接从统计服务获取用户限制数据
  const fetchUserLimits = useCallback(async () => {
    if (!useAuthStore.getState().isAuthenticated) {
      console.log('用户未认证，跳过获取限制数据');
      return;
    }
    
    try {
      setIsLoadingLimits(true);
      console.log('开始获取用户限制数据...');
      
      const statistics = await statisticsService.getUserStatistics();
      
      if (statistics.userLimits) {
        console.log('获取到的用户限制数据:', statistics.userLimits);
        
        setUserLimits({
          maxPatients: statistics.userLimits.maxPatients,
          maxPathologies: statistics.userLimits.maxPathologies,
          maxAttachmentSize: statistics.userLimits.maxAttachmentSize,
          maxTotalStorage: statistics.userLimits.maxTotalStorage,
          maxAiUsed: statistics.userLimits.maxAiUsed
        });
      }
    } catch (error) {
      console.error('获取用户限制数据失败:', error);
    } finally {
      setIsLoadingLimits(false);
    }
  }, []);
  
  // 在用户认证状态变化或组件挂载时获取限制数据
  useEffect(() => {
    const isAuthenticated = useAuthStore.getState().isAuthenticated;
    
    if (isAuthenticated) {
      fetchUserLimits();
    }
    
    // 监听认证状态变化
    const unsubscribe = useAuthStore.subscribe((state) => {
      if (state.isAuthenticated) {
        fetchUserLimits();
      }
    });
    
    return () => {
      unsubscribe();
    };
  }, [fetchUserLimits]);
  
  // 用户加载后，如果用户数据有限制信息，合并到本地状态
  useEffect(() => {
    if (userData) {
      const newLimits = { ...userLimits };
      let hasUpdates = false;
      
      // 检查用户数据中的限制字段是否存在
      if (userData.maxPatients !== undefined) {
        newLimits.maxPatients = userData.maxPatients;
        hasUpdates = true;
      }
      
      if (userData.maxPathologies !== undefined) {
        newLimits.maxPathologies = userData.maxPathologies;
        hasUpdates = true;
      }
      
      if (userData.maxAttachmentSize !== undefined) {
        newLimits.maxAttachmentSize = userData.maxAttachmentSize;
        hasUpdates = true;
      }
      
      if (userData.maxTotalStorage !== undefined) {
        newLimits.maxTotalStorage = userData.maxTotalStorage;
        hasUpdates = true;
      }
      
      if (userData.maxAiUsed !== undefined) {
        newLimits.maxAiUsed = userData.maxAiUsed;
        hasUpdates = true;
      }
      
      // 如果有更新，则更新状态
      if (hasUpdates) {
        console.log('从用户数据中获取到限制信息:', newLimits);
        setUserLimits(newLimits);
      }
    }
  }, [userData, userLimits, setUserLimits]);
  
  // 添加调试日志
  useEffect(() => {
    if (userData) {
      console.log('获取到的Profile数据详情:', JSON.stringify(userData, null, 2));
      
      const cachedAvatar = localStorage.getItem('userAvatar');
      console.log('本地存储的头像URL:', cachedAvatar);
      
      console.log('用户头像值:', {
        avatar: userData.avatar,
        类型: typeof userData.avatar,
        长度: userData.avatar !== undefined ? 
          (userData.avatar !== null ? userData.avatar.length : 'null') : 
          'undefined'
      });
      
      console.log('用户限制字段值:', {
        maxPatients: userData.maxPatients,
        maxPathologies: userData.maxPathologies,
        maxAttachmentSize: userData.maxAttachmentSize,
        maxTotalStorage: userData.maxTotalStorage,
        maxPatients类型: typeof userData.maxPatients,
        maxPathologies类型: typeof userData.maxPathologies,
        maxAttachmentSize类型: typeof userData.maxAttachmentSize,
        maxTotalStorage类型: typeof userData.maxTotalStorage,
        maxAiUsed: userData.maxAiUsed,
        maxAiUsed类型: typeof userData.maxAiUsed
      });
    }
    
    console.log('当前本地存储的用户限制数据:', userLimits);
  }, [userData, userLimits]);
  
  // 表单状态
  const [email, setEmail] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [avatar, setAvatar] = useState('');
  const [editMode, setEditMode] = useState(false);
  const [updateError, setUpdateError] = useState<string | null>(null);
  const [updateSuccess, setUpdateSuccess] = useState<string | null>(null);
  
  // 当profile数据加载完成后，更新表单状态
  useEffect(() => {
    if (userData) {
      console.log('Profile组件获取到的用户数据:', {
        ...userData,
        头像: userData.avatar,
        头像类型: typeof userData.avatar,
        头像内容: userData.avatar !== null && userData.avatar !== undefined ? userData.avatar : '空'
      });

      setEmail(userData.email);
      setPhoneNumber(userData.phoneNumber || '');
      
      const backupAvatar = localStorage.getItem('userAvatar');
      const avatarFromData = userData.avatar !== null && userData.avatar !== undefined && userData.avatar !== "" 
        ? userData.avatar 
        : '';
      const finalAvatar = avatarFromData || backupAvatar || '';
      setAvatar(finalAvatar);
      
      console.log('Profile数据加载后设置头像:', {
        原始值: userData.avatar,
        本地缓存值: backupAvatar,
        类型: typeof userData.avatar,
        最终设置值: finalAvatar
      });
    }
  }, [userData]);
  
  // 确保数据刷新
  useEffect(() => {
    const refreshProfileData = async () => {
      if (profile) { // 仅当profile存在时才执行
        try {
          await refetch();
          console.log('Profile组件已刷新用户数据(profile变化触发)');
        } catch (error) {
          console.error('刷新用户资料失败(profile变化触发):', error);
        }
      }
    };
    refreshProfileData();
  }, [profile, refetch]); // 添加 profile 和 refetch 到依赖数组
  
  const updateProfileMutation = useMutation({
    mutationFn: updateUserProfile,
    onSuccess: () => {
      setUpdateSuccess('个人资料更新成功');
      setEditMode(false);
      refetch();
      setTimeout(() => setUpdateSuccess(null), 3000);
    },
    onError: (error: any) => {
      setUpdateError(error.response?.data?.error || '更新个人资料失败');
      setTimeout(() => setUpdateError(null), 3000);
    }
  });
  
  const handleEdit = () => {
    if (userData) { // 使用 userData 而不是 profile
      setEmail(userData.email);
      setPhoneNumber(userData.phoneNumber || '');
      setAvatar(userData.avatar || '');
    }
    setEditMode(true);
  };
  
  const handleCancel = () => {
    setEditMode(false);
  };
  
  const handleSave = () => {
    if (avatar && !isValidUrl(avatar)) {
      setUpdateError('头像URL格式无效，请输入正确的URL地址');
      return;
    }
    updateProfileMutation.mutate({
      email,
      phoneNumber: phoneNumber || undefined,
      avatar: avatar
    }, {
      onSuccess: () => {
        handleDirectFetch();
        // 更新后重新获取限制数据
        fetchUserLimits();
      }
    });
  };

  const isValidUrl = (url: string) => {
    try {
      new URL(url);
      return true;
    } catch (e) {
      return false;
    }
  };

  const handleAvatarPreview = () => {
    if (avatar && isValidUrl(avatar)) {
      window.open(avatar, '_blank');
    } else {
      setUpdateError('头像URL无效，无法预览');
      setTimeout(() => setUpdateError(null), 3000);
    }
  };
  
  const handleLogout = () => {
    logout();
  };
  
  const getInitial = (username: string) => {
    return username ? username.charAt(0).toUpperCase() : '?';
  };
  
  // 处理导航到患者创建页面
  const handleNavigateToCreatePatient = () => {
    navigate('/patients');
  };
  
  return (
    <Box sx={{ 
      m: 0, 
      width: '100%',
      px: '10px'  // 添加左右内边距各10px
    }}>
      {/* 页面标题和操作按钮区域 */}
      <Box sx={{ display: 'flex', alignItems: 'flex-end', justifyContent: 'space-between', mb: 0 }}>
        <Typography 
          variant="h5" 
          component="h1" 
          sx={{ 
            fontWeight: 500,
            fontSize: { xs: '1.1rem', sm: '1.3rem' },
            mb: 0
          }}
        >
          个人资料
        </Typography>
        <Box sx={{ flexShrink: 0, ml: 3 }}>
          <Button
            variant="outlined"
            startIcon={<LogoutIcon />}
            onClick={handleLogout}
            color="error"
            sx={{ minWidth: 80, px: 2, fontSize: '0.75rem', mb: '3px' }}
          >
            退出登录
          </Button>
        </Box>
      </Box>
      <Divider sx={{ mb: { xs: 2, md: 3 } }} />
      
      {isLoading || isDirectFetching ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}><CircularProgress /></Box>
      ) : isError || directFetchError ? (
        <Alert severity="error" sx={{ mb: 3 }}>{error?.message || directFetchError?.message || '获取用户资料失败'}</Alert>
      ) : (
        <>
          {updateSuccess && <Alert severity="success" sx={{ mb: 3 }}>{updateSuccess}</Alert>}
          {updateError && <Alert severity="error" sx={{ mb: 3 }}>{updateError}</Alert>}
          
          <TableContainer sx={{ 
            border: '1px solid #e0e0e0',
            borderRadius: 2,
            overflow: 'hidden',
            mb: 3
          }}>
            {/* 个人资料卡片 */}
            <Box sx={{ p: { xs: 2, md: 3 }, bgcolor: 'background.paper' }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" sx={{ fontWeight: 500, fontSize: '1rem' }}>个人资料</Typography>
                {!editMode ? (
                  <Button 
                    variant="outlined" 
                    size="small" 
                    color="primary" 
                    onClick={handleEdit} 
                    startIcon={<EditIcon />}
                    sx={{ fontSize: '0.75rem' }}
                  >
                    编辑资料
                  </Button>
                ) : (
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button 
                      variant="outlined" 
                      size="small" 
                      color="inherit" 
                      onClick={handleCancel}
                      sx={{ fontSize: '0.75rem' }}
                    >
                      取消
                    </Button>
                    <Button 
                      variant="contained" 
                      size="small" 
                      color="primary" 
                      onClick={handleSave}
                      sx={{ fontSize: '0.75rem' }}
                    >
                      保存
                    </Button>
                  </Box>
                )}
              </Box>
              <Divider sx={{ mb: 2 }} />
              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={3} alignItems="flex-start">
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: { xs: '100%', sm: 'auto' } }}>
                  <Avatar src={avatar || ''} alt={userData?.username || '用户'} sx={{ width: { xs: 100, sm: 120 }, height: { xs: 100, sm: 120 }, mb: 2 }}>
                    {getInitial(userData?.username || '用户')}
                  </Avatar>
                  {editMode && (
                    <TextField 
                      label="头像URL" 
                      variant="outlined" 
                      fullWidth 
                      size="small" 
                      value={avatar} 
                      onChange={(e) => setAvatar(e.target.value)} 
                      helperText="输入图片URL或清空使用默认头像" 
                      sx={{ 
                        mb: 2, 
                        width: { xs: '100%', sm: 200 },
                        '& .MuiInputBase-root': { fontSize: '0.75rem' },
                        '& .MuiInputLabel-root': { fontSize: '0.75rem' },
                        '& .MuiFormHelperText-root': { fontSize: '0.65rem' }
                      }}
                    />
                  )}
                  {editMode && avatar && (
                    <Button 
                      variant="outlined" 
                      size="small" 
                      onClick={handleAvatarPreview}
                      sx={{ fontSize: '0.75rem' }}
                    >
                      预览头像
                    </Button>
                  )}
                </Box>
                <Stack spacing={2} sx={{ flexGrow: 1, width: '100%' }}>
                  <Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom sx={{ fontSize: '0.7rem' }}>用户名</Typography>
                    <Typography variant="body1" sx={{ fontWeight: 500, fontSize: '0.875rem' }}>{userData?.username}</Typography>
                  </Box>
                  <Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom sx={{ fontSize: '0.7rem' }}>电子邮箱</Typography>
                    {editMode ? (
                      <TextField 
                        variant="outlined" 
                        fullWidth 
                        size="small" 
                        value={email} 
                        onChange={(e) => setEmail(e.target.value)} 
                        placeholder="输入您的电子邮箱"
                        sx={{ 
                          '& .MuiInputBase-root': { fontSize: '0.75rem' },
                          '& .MuiInputLabel-root': { fontSize: '0.75rem' }
                        }}
                      />
                    ) : (
                      <Typography variant="body1" sx={{ fontSize: '0.875rem' }}>{userData?.email}</Typography>
                    )}
                  </Box>
                  <Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom sx={{ fontSize: '0.7rem' }}>手机号码</Typography>
                    {editMode ? (
                      <TextField 
                        variant="outlined" 
                        fullWidth 
                        size="small" 
                        value={phoneNumber} 
                        onChange={(e) => setPhoneNumber(e.target.value)} 
                        placeholder="输入您的手机号码"
                        sx={{ 
                          '& .MuiInputBase-root': { fontSize: '0.75rem' },
                          '& .MuiInputLabel-root': { fontSize: '0.75rem' }
                        }}
                      />
                    ) : (
                      <Typography variant="body1" sx={{ fontSize: '0.875rem' }}>{userData?.phoneNumber || '未设置'}</Typography>
                    )}
                  </Box>
                  <Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom sx={{ fontSize: '0.7rem' }}>账户级别</Typography>
                    <Typography variant="body1" sx={{ fontSize: '0.875rem' }}>
                      {userData?.level === 'PERSONAL' && '个人用户'}
                      {userData?.level === 'FAMILY' && '家庭用户'} 
                      {userData?.level === 'PROFESSIONAL' && '专业用户'}
                      {userData?.level === 'ENTERPRISE' && '企业用户'}
                      {!(userData?.level && ['PERSONAL', 'FAMILY', 'PROFESSIONAL', 'ENTERPRISE'].includes(userData.level)) && '未知'}
                    </Typography>
                  </Box>
                  <Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom sx={{ fontSize: '0.7rem' }}>注册时间</Typography>
                    <Typography variant="body1" sx={{ fontSize: '0.875rem' }}>
                      {userData?.createdAt ? new Date(userData.createdAt).toLocaleString('zh-CN') : '未知'}
                    </Typography>
                  </Box>
                </Stack>
              </Stack>
            </Box>
          </TableContainer>
          
          {/* 添加创建本人病历的醒目按钮 */}
          <TableContainer sx={{ 
            border: '1px solid #e0e0e0',
            borderRadius: 2,
            overflow: 'hidden',
            mb: 3
          }}>
            <Box sx={{ 
              p: { xs: 3, md: 4 }, 
              bgcolor: '#f5f9ff', // 浅蓝色背景吸引注意
              textAlign: 'center'
            }}>
              <Typography variant="h6" sx={{ mb: 2, color: '#1976d2', fontSize: '1rem' }}>
                开始创建您的健康档案
              </Typography>
              <Typography variant="body1" sx={{ mb: 3, color: 'text.secondary', fontSize: '0.875rem' }}>
                创建您的个人健康档案，记录疾病、治疗和健康状况，帮助您更好地管理健康信息。
              </Typography>
              <Button 
                variant="contained" 
                size="large" 
                color="primary" 
                onClick={handleNavigateToCreatePatient}
                sx={{ 
                  px: 4, 
                  py: 1.5, 
                  fontSize: '0.875rem',
                  fontWeight: 'bold',
                  boxShadow: '0 4px 10px rgba(25, 118, 210, 0.3)' // 添加阴影更醒目
                }}
              >
                建立本人病历档案
              </Button>
            </Box>
          </TableContainer>
          
          {/* 账户限制信息卡片 */}
          <TableContainer sx={{ 
            border: '1px solid #e0e0e0',
            borderRadius: 2,
            overflow: 'hidden',
            mb: 3
          }}>
            <Box sx={{ p: { xs: 2, md: 3 }, bgcolor: 'background.paper' }}>
              <Typography variant="h6" sx={{ fontWeight: 500, mb: 2, fontSize: '1rem' }}>账户限制</Typography>
              <Divider sx={{ mb: 2 }} />
              {isLoadingLimits ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}><CircularProgress size={24} /></Box>
              ) : (
                <Stack spacing={2}>
                  <Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom sx={{ fontSize: '0.7rem' }}>最大患者数</Typography>
                    <Typography variant="body1" sx={{ fontSize: '0.875rem' }}>
                      {(() => {
                        const value = userLimits.maxPatients;
                        if (value === -1) return '无限制';
                        if (value !== undefined && value !== null) return value;
                        return '未知';
                      })()}
                    </Typography>
                  </Box>
                  <Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom sx={{ fontSize: '0.7rem' }}>每患者最大病理数</Typography>
                    <Typography variant="body1" sx={{ fontSize: '0.875rem' }}>
                      {(() => {
                        const value = userLimits.maxPathologies;
                        if (value === -1) return '无限制';
                        if (value !== undefined && value !== null) return value;
                        return '未知';
                      })()}
                    </Typography>
                  </Box>
                  <Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom sx={{ fontSize: '0.7rem' }}>每附件最大尺寸</Typography>
                    <Typography variant="body1" sx={{ fontSize: '0.875rem' }}>
                      {(() => {
                        const value = userLimits.maxAttachmentSize;
                        if (value === -1) return '无限制';
                        if (value !== undefined && value !== null) return formatStorageSize(Number(value));
                        return '未知';
                      })()}
                    </Typography>
                  </Box>
                  <Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom sx={{ fontSize: '0.7rem' }}>总存储空间</Typography>
                    <Typography variant="body1" sx={{ fontSize: '0.875rem' }}>
                      {(() => {
                        const value = userLimits.maxTotalStorage;
                        if (value === -1) return '无限制';
                        if (value !== undefined && value !== null) return formatStorageSize(Number(value));
                        return '未知';
                      })()}
                    </Typography>
                  </Box>
                  <Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom sx={{ fontSize: '0.7rem' }}>家庭成员数上限</Typography>
                    <Typography variant="body1" sx={{ fontSize: '0.875rem' }}>{userData?.familyMemberLimit ?? '未知'}</Typography>
                  </Box>
                  <Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom sx={{ fontSize: '0.7rem' }}>AI使用次数</Typography>
                    <Typography variant="body1" sx={{ fontSize: '0.875rem' }}>
                      {aiUsageCount === undefined || aiUsageCount === null ? '未知' : aiUsageCount} / 
                      {maxAiUsage === undefined || maxAiUsage === null ? '未知' : (maxAiUsage === -1 ? '无限制' : maxAiUsage)}
                    </Typography>
                  </Box>
                </Stack>
              )}
            </Box>
          </TableContainer>
        </>
      )}
    </Box>
  );
};

export default ProfilePage; 