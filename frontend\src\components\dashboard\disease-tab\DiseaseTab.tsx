import React, { useState, useEffect, useRef, useCallback } from 'react';
import { 
  Box, 
  Typography, 
  Paper,
  Container,
  Stack,
  useTheme,
  Skeleton,
  Fade
} from '@mui/material';
import { usePatientDiseaseContext } from '../../../context/PatientDiseaseContext';
// 导入新的数据获取工具
import { fetchDiseaseTabData, extractAIReportStructuredData } from '../../../utils/diseaseFetcher';

// 导入所有子组件
import ActionBar from './ActionBar';
import PatientInfoCard from './PatientInfoCard';
import DiseaseInfoCard from './DiseaseInfoCard';
import DiseaseTimeline from './DiseaseTimeline';
import MedicalAdviceCard from './MedicalAdviceCard';
import HospitalRecommendationCard from './HospitalRecommendationCard';
import AIReportCard from './AIReportCard';

/**
 * DiseaseTab 病理页面组件
 * 集成所有病理相关子组件，提供完整的病理信息展示
 */
const DiseaseTab: React.FC = () => {
  const theme = useTheme();
  const { selectedPatientId, selectedDiseaseId } = usePatientDiseaseContext();
  
  // 数据状态
  const [disease, setDisease] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  
  // 患者数据
  const [patient, setPatient] = useState<any>(null);
  
  // 记录状态
  const [records, setRecords] = useState<any[]>([]);
  
  // AI报告状态
  const [aiReport, setAIReport] = useState<any>(null);
  const [allReports, setAllReports] = useState<any[]>([]);
  
  // 从AI报告中提取的结构化数据
  const [aiData, setAIData] = useState<{
    trend?: string;
    trendDetails?: any;
    summary?: string;
    medicalAdvice?: string[];
    lifestyleAdvice?: string[];
    exerciseAdvice?: string[];
    dietAdvice?: string[];
    psychologicalAdvice?: string[];
    hospitalRecommendations?: any[];
    hasStructuredData: boolean;
    imagingFindings?: any;
    recommendedTests?: string[];
    survivalPrediction?: any;
    isDemo?: boolean;
    status?: string;
    riskLevel?: string;
  }>({
    hasStructuredData: false
  });
  
  // 页面渲染状态
  const [pageReady, setPageReady] = useState<boolean>(false);
  
  // 使用ref跟踪上一次加载的ID，防止重复加载
  const previousDiseaseIdRef = useRef<string | null | undefined>(null);
  const previousPatientIdRef = useRef<string | null | undefined>(null);
  const isLoadingRef = useRef<boolean>(false);
  const lastLoadTimeRef = useRef<number>(0);
  
  // 数据加载函数 - 使用useCallback以便可以在依赖项中使用
  const loadDiseaseData = useCallback(async () => {
    // 如果已经在加载中，跳过
    if (isLoadingRef.current) {
      console.log('【防重复】数据加载已在进行中，跳过此次调用');
      return;
    }
    
    // 判断是否重复加载相同ID
    if (selectedDiseaseId === previousDiseaseIdRef.current &&
        selectedPatientId === previousPatientIdRef.current) {
      console.log('【防重复】相同的ID组合，跳过重复加载');
      return;
    }
    
    // 检查加载频率 - 至少间隔1秒
    const now = Date.now();
    if (now - lastLoadTimeRef.current < 1000) {
      console.log('【节流】数据加载请求过于频繁，跳过此次调用');
      return;
    }
    
    if (!selectedDiseaseId) {
      // 重置所有状态
      setDisease(null);
      setPatient(null);
      setRecords([]);
      setAIReport(null);
      setAllReports([]);
      setAIData({ hasStructuredData: false });
      setPageReady(false);
      return;
    }
    
    try {
      // 更新refs以跟踪状态
      previousDiseaseIdRef.current = selectedDiseaseId;
      previousPatientIdRef.current = selectedPatientId;
      isLoadingRef.current = true;
      lastLoadTimeRef.current = now;
      
      // 显示加载状态
      setLoading(true);
      setPageReady(false);
      
      console.time('病理Tab数据加载');
      console.log(`【数据加载】开始加载 diseaseId=${selectedDiseaseId}, patientId=${selectedPatientId}`);
      
      // 使用新的集中数据获取函数
      const tabData = await fetchDiseaseTabData(selectedDiseaseId, selectedPatientId);
      
      // 更新各状态
      setDisease(tabData.disease);
      setPatient(tabData.patient);
      setRecords(tabData.stageNodeRecords || []);
      
      // 设置AI报告
      if (tabData.aiReport) {
        setAIReport(tabData.aiReport);
        setAllReports([tabData.aiReport]); // 目前只获取最新一条，可以根据需要修改
        
        // 提取AI报告结构化数据
        const structuredData = extractAIReportStructuredData(tabData.aiReport.content);
        setAIData(structuredData);
        
        console.log('【数据处理】成功提取AI报告数据');
      } else {
        setAIReport(null);
        setAllReports([]);
        setAIData({ hasStructuredData: false });
      }
      
      console.timeEnd('病理Tab数据加载');
      
      // 更新加载状态和错误状态
      setLoading(false);
      setError(null);
      
      // 使用短延迟设置页面就绪状态，提供平滑过渡
      setTimeout(() => {
        setPageReady(true);
      }, 100);
    } catch (err: any) {
      console.error('获取病理数据失败:', err);
      setError(err.message || '获取病理数据失败，请稍后再试');
      setLoading(false);
      setPageReady(true); // 即使有错误也设置页面就绪以显示错误信息
    } finally {
      isLoadingRef.current = false;
    }
  }, [selectedDiseaseId, selectedPatientId]);
  
  // 使用useEffect调用加载函数
  useEffect(() => {
    loadDiseaseData();
  }, [loadDiseaseData]);
  
  // 如果未选择病理，显示提示信息
  if (!selectedDiseaseId) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Paper 
          elevation={0} 
          sx={{ 
            p: 4, 
            textAlign: 'center',
            borderRadius: 2,
            border: `1px solid #e0e0e0`
          }}
        >
          <Typography variant="h5" color="text.secondary" gutterBottom>
            请先选择一个病理
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
            在上方的病理列表中选择一个病理，查看详细信息
          </Typography>
        </Paper>
      </Container>
    );
  }
  
  // 加载中状态 - 使用骨架屏代替简单的加载指示器
  if (loading && !pageReady) {
    return (
      <Container maxWidth="xl" sx={{ py: 3 }}>
        {/* 操作栏骨架屏 */}
        <Box sx={{ mb: 3 }}>
          <Skeleton variant="rectangular" width="100%" height={60} sx={{ borderRadius: 1 }} />
        </Box>
        
        {/* 内容区骨架屏 */}
        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 3 }}>
          {/* 左侧列骨架屏 */}
          <Box sx={{ width: { xs: '100%', md: '33%', lg: '25%' } }}>
            <Stack spacing={3}>
              <Skeleton variant="rectangular" width="100%" height={200} sx={{ borderRadius: 2 }} />
              <Skeleton variant="rectangular" width="100%" height={220} sx={{ borderRadius: 2 }} />
              <Skeleton variant="rectangular" width="100%" height={300} sx={{ borderRadius: 2 }} />
            </Stack>
          </Box>
          
          {/* 中间列骨架屏 */}
          <Box sx={{ width: { xs: '100%', md: '67%', lg: '50%' } }}>
            <Skeleton variant="rectangular" width="100%" height={500} sx={{ borderRadius: 2 }} />
          </Box>
          
          {/* 右侧列骨架屏 */}
          <Box sx={{ width: { xs: '100%', lg: '25%' }, display: { xs: 'block', lg: 'block' } }}>
            <Stack spacing={3}>
              <Skeleton variant="rectangular" width="100%" height={400} sx={{ borderRadius: 2 }} />
              <Skeleton variant="rectangular" width="100%" height={200} sx={{ borderRadius: 2 }} />
            </Stack>
          </Box>
        </Box>
      </Container>
    );
  }
  
  // 加载失败状态
  if (error) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Paper 
          elevation={0} 
          sx={{ 
            p: 4, 
            textAlign: 'center',
            borderRadius: 2,
            border: `1px solid ${theme.palette.error.main}`
          }}
        >
          <Typography variant="h5" color="error" gutterBottom>
            加载失败
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
            {error}
          </Typography>
        </Paper>
      </Container>
    );
  }
  
  // 正常渲染状态 - 添加渐变效果
  return (
    <Fade in={pageReady} timeout={300}>
      <Container maxWidth="xl" sx={{ py: 3 }}>
        {/* 操作栏 */}
        <Box sx={{ mb: 3 }}>
          <ActionBar 
            disease={disease} 
            hasAIReport={!!aiReport} 
            selectedDiseaseId={selectedDiseaseId} 
          />
        </Box>
        
        {/* 内容区 - 使用Stack替代Grid布局 */}
        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 3 }}>
          {/* 左侧列 - 基础信息 */}
          <Box sx={{ width: { xs: '100%', md: '33%', lg: '25%' } }}>
            <Stack spacing={3}>
              <PatientInfoCard patient={patient} loading={loading} />
              <DiseaseInfoCard disease={disease} loading={loading} survivalPrediction={aiData.survivalPrediction} />
              <DiseaseTimeline 
                disease={disease} 
                loading={loading} 
                stages={records.filter(r => r.stage_node || r.stageNode)}
                aiData={aiData}
              />
            </Stack>
          </Box>
          
          {/* 中间列 - 病理概述与信息 */}
          <Box sx={{ width: { xs: '100%', md: '67%', lg: '50%' } }}>
            <Stack spacing={3}>
              <AIReportCard 
                aiReport={aiReport} 
                allReports={allReports}
                diseaseId={selectedDiseaseId} 
                loading={loading}
                patient={patient}
                extractedSummary={aiData.summary}
              />
            </Stack>
          </Box>
          
          {/* 右侧列 - 建议与推荐 */}
          <Box sx={{ width: { xs: '100%', lg: '25%' }, display: { xs: 'block', lg: 'block' } }}>
            <Stack spacing={3}>
              <MedicalAdviceCard 
                aiReport={aiReport} 
                loading={loading} 
                medicalAdvice={aiData.medicalAdvice}
                lifestyleAdvice={aiData.lifestyleAdvice}
                exerciseAdvice={aiData.exerciseAdvice}
                dietAdvice={aiData.dietAdvice}
                psychologicalAdvice={aiData.psychologicalAdvice}
                imagingFindings={aiData.imagingFindings}
                recommendedTests={aiData.recommendedTests}
                isDemo={aiData.isDemo}
              />
              <HospitalRecommendationCard 
                aiReport={aiReport} 
                loading={loading} 
                hospitalRecommendations={aiData.hospitalRecommendations}
                isDemo={aiData.isDemo}
              />
            </Stack>
          </Box>
        </Box>
      </Container>
    </Fade>
  );
};

export default DiseaseTab; 