import { useState, useEffect } from 'react';
import { useServiceUserContext } from '../context/ServiceUserContext';

/**
 * 服务上下文Hook，用于管理服务用户的上下文状态逻辑
 * 抽象了ServiceRecordManage和ServiceReportManage等组件中的共享逻辑
 */
export function useServiceContext() {
  const serviceContext = useServiceUserContext();
  const [showContextSelector, setShowContextSelector] = useState(false);
  const [error, setError] = useState('');

  // 检查服务上下文是否有效
  const isValidContext = serviceContext.authorizationId && serviceContext.patientId;
  
  // 检查服务上下文是否完整（包含病理ID）
  const isCompleteContext = isValidContext && serviceContext.diseaseId;
  
  // 首次加载时，如果上下文不存在，显示选择器
  useEffect(() => {
    if (!isValidContext) {
      setShowContextSelector(true);
    }
  }, [isValidContext]);
  
  // 打开上下文选择器
  const openContextSelector = () => {
    setShowContextSelector(true);
  };
  
  // 关闭上下文选择器
  const closeContextSelector = () => {
    setShowContextSelector(false);
  };
  
  // 验证上下文并执行回调
  const validateContext = (callback: () => void, requireDisease = false) => {
    if (!serviceContext.authorizationId || !serviceContext.patientId) {
      setError('请先选择授权患者');
      setShowContextSelector(true);
      return false;
    }
    
    if (requireDisease && !serviceContext.diseaseId) {
      setError('请选择病理');
      return false;
    }
    
    callback();
    return true;
  };
  
  return {
    serviceContext,
    showContextSelector,
    setShowContextSelector,
    error,
    setError,
    isValidContext,
    isCompleteContext,
    openContextSelector,
    closeContextSelector,
    validateContext
  };
} 