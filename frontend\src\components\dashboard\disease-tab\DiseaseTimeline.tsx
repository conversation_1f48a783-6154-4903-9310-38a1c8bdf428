import React, { useMemo, useEffect, useState } from 'react';
import { 
  Box, 
  Typography, 
  Stepper, 
  Step, 
  StepLabel, 
  StepConnector,
  stepConnectorClasses,
  StepIconProps,
  CircularProgress,
  Divider
} from '@mui/material';
import { styled, useTheme } from '@mui/material/styles';
import {
  Check as CheckIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon
} from '@mui/icons-material';
import BaseCard from './BaseCard';
import { getRecords } from '../../../services/recordService';

// 使用与DiseaseStageTimeline相同的阶段枚举
export enum StageNodeEnum {
  INITIAL_VISIT = 'INITIAL_VISIT',
  DIAGNOSIS = 'DIAGNOSIS',
  TREATMENT = 'TREATMENT',
  FOLLOW_UP = 'FOLLOW_UP',
  PROGNOSIS = 'PROGNOSIS',
  ARCHIVE = 'ARCHIVE'
}

// 节点中文名称映射
const NODE_NAMES: Record<string, string> = {
  [StageNodeEnum.INITIAL_VISIT]: '初诊',
  [StageNodeEnum.DIAGNOSIS]: '确诊',
  [StageNodeEnum.TREATMENT]: '治疗',
  [StageNodeEnum.FOLLOW_UP]: '随访',
  [StageNodeEnum.PROGNOSIS]: '预后',
  [StageNodeEnum.ARCHIVE]: '封档'
};

const formatDateString = (dateStr: string | Date) => {
  if (!dateStr) return '日期未知';
  try {
    const dateObj = typeof dateStr === 'string' ? new Date(dateStr) : dateStr;
    // 返回 月/日 格式
    return `${dateObj.getMonth() + 1}/${dateObj.getDate()}`;
  } catch (e) {
    return '日期无效';
  }
};

const getStageStatus = (index: number, activeStep: number): 0 | 1 | 2 => { // 0: 未开始, 1: 进行中, 2: 已完成
  if (index < activeStep) return 2; // 已完成
  if (index === activeStep) return 1; // 进行中
  return 0; // 未开始
};

interface DiseaseTimelineProps {
  disease: any;
  stages?: any[]; // 病理阶段数据（记录列表）
  loading?: boolean;
  aiData?: {
    trend?: string;
    trendDetails?: any;
    hasStructuredData: boolean;
    status?: string;    // 添加status字段
    riskLevel?: string; // 添加riskLevel字段
  };
}

interface StageRecord {
  id: string;
  stage_node?: string;  // 使用可选字段
  stageNode?: string;   // 驼峰命名可选
  node?: string;        // 更简短的可选名称
  recordDate: string | Date;
  title: string;
  recordType?: string;  // 记录类型
  stage_phase?: string; // 病程阶段可选
  stagePhase?: string;  // 病程阶段驼峰命名可选
  is_important?: boolean;
  is_private?: boolean;
  created_at?: string | Date; // 创建时间
  createdAt?: string | Date;  // 创建时间驼峰命名
  // 其他可能的字段
  [key: string]: any;  // 添加索引签名允许任意其他字段
}

// 自定义连接器样式
const CustomConnector = styled(StepConnector)(({ theme }) => ({
  [`&.${stepConnectorClasses.alternativeLabel}`]: {
    top: 15,
    left: 'calc(-50% + 12px)',
    right: 'calc(50% + 12px)',
  },
  [`&.${stepConnectorClasses.active}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      backgroundImage: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.light} 100%)`,
    },
  },
  [`&.${stepConnectorClasses.completed}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      backgroundImage: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.light} 100%)`,
    },
  },
  [`& .${stepConnectorClasses.line}`]: {
    height: 3,
    border: 0,
    backgroundColor:
      theme.palette.mode === 'dark' ? theme.palette.grey[700] : theme.palette.grey[300],
    borderRadius: 1,
  },
}));

// 自定义图标组件
const CustomStepIconRoot = styled('div')<{
  ownerState: { completed?: boolean; active?: boolean };
}>(({ theme, ownerState }) => ({
  color: theme.palette.mode === 'dark' ? theme.palette.grey[700] : theme.palette.grey[300],
  display: 'flex',
  height: 33,
  alignItems: 'center',
  ...(ownerState.active && {
    color: theme.palette.primary.main,
  }),
  ...(ownerState.completed && {
    color: theme.palette.primary.main,
  }),
  '& .QontoStepIcon-completedIcon': {
    color: theme.palette.primary.main,
    zIndex: 1,
    fontSize: 24,
  },
  '& .QontoStepIcon-circle': {
    width: 10,
    height: 10,
    borderRadius: '50%',
    backgroundColor: 'currentColor',
  },
}));

// 自定义步骤图标
function CustomStepIcon(props: StepIconProps) {
  const { active, completed, className } = props;

  return (
    <CustomStepIconRoot ownerState={{ completed, active }} className={className}>
      {completed ? (
        <CheckIcon className="QontoStepIcon-completedIcon" />
      ) : (
        <div className="QontoStepIcon-circle" />
      )}
    </CustomStepIconRoot>
  );
}

/**
 * 病程发展组件
 * 显示病情趋势和病理发展阶段
 */
const DiseaseTimeline: React.FC<DiseaseTimelineProps> = ({ 
  disease,
  stages = [], 
  loading = false,
  aiData
}) => {
  const theme = useTheme();
  const [records, setRecords] = useState<StageRecord[]>([]);
  const [loadingRecords, setLoadingRecords] = useState<boolean>(false);
  const [processedStages, setProcessedStages] = useState<any[]>([]);
  // 添加强制刷新状态
  const [lastUpdate, setLastUpdate] = useState<number>(Date.now());

  // 用于强制刷新组件的计时器
  useEffect(() => {
    console.log('【病程发展】组件加载, aiData:', aiData);
    // 3秒后强制刷新一次，确保正确显示趋势
    const timer = setTimeout(() => {
      setLastUpdate(Date.now());
      console.log('【病程发展】强制刷新触发');
    }, 3000);
    return () => clearTimeout(timer);
  }, [aiData]);

  // 获取记录数据 - 仅在没有传入stages时才获取
  useEffect(() => {
    // 如果已有stages数据，则直接使用
    if (stages && stages.length > 0) {
      console.log('使用传入的stages数据:', stages.length, '条记录');
      setRecords(stages as StageRecord[]);
      setLoadingRecords(false);
      return;
    }

    const fetchRecordsData = async () => {
      if (!disease?.id) {
        setRecords([]);
        return;
      }
      
      try {
        setLoadingRecords(true);
        
        // 获取所有记录，通过前端筛选出带有阶段节点的记录
        const response = await getRecords({ 
          diseaseId: disease.id,
          limit: 1000 // 增加记录限制，确保获取所有病程节点记录
        });
        
        let recordsData: any[] = [];
        
        if (Array.isArray(response)) {
          recordsData = response;
        } else if (response && typeof response === 'object' && response.records) {
          recordsData = response.records;
        }
        
        console.log(`【病程发展】获取到总记录数量: ${recordsData.length}`);
        
        // 筛选出包含阶段节点信息的记录
        const stageRecords = recordsData.filter(record => {
          const hasNode = record.stage_node || record.stageNode || record.node || 
                         (record.recordType === 'STAGE_CHANGE' ? record.stage_phase || record.stagePhase : null);
          return !!hasNode;
        });
        
        console.log(`【病程发展】筛选出包含阶段节点的记录: ${stageRecords.length}条`);
        setRecords(stageRecords);
        
      } catch (err) {
        console.error('获取病理记录失败:', err);
        setRecords([]);
      } finally {
        setLoadingRecords(false);
      }
    };
    
    fetchRecordsData();
  }, [disease?.id, stages]);
  
  // 处理记录，提取并排序病程节点 - 与 DiseaseStageTimeline 相同的逻辑
  useEffect(() => {
    if (!records || records.length === 0) {
      console.log('【阶段提取】无记录数据，无法提取病程阶段');
      setProcessedStages([]);
      return;
    }

    console.log('【阶段提取】开始从', records.length, '条记录中提取病程阶段');
    
    // 按节点类型分组记录，并找出每种节点最早的记录
    const stageNodeMap = new Map<string, StageRecord>();
    
    // 标准化阶段顺序
    const nodeOrder = [
      StageNodeEnum.INITIAL_VISIT,
      StageNodeEnum.DIAGNOSIS, 
      StageNodeEnum.TREATMENT,
      StageNodeEnum.FOLLOW_UP,
      StageNodeEnum.PROGNOSIS,
      StageNodeEnum.ARCHIVE
    ];

    // 记录提取到的阶段节点数量
    let extractedNodeCount = 0;
    
    // 遍历记录，按节点类型分组
    records.forEach((record, index) => {
      // 适配不同的字段名称
      const nodeType = record.stage_node || record.stageNode || record.node || 
                        (record.recordType === 'STAGE_CHANGE' ? record.stage_phase || record.stagePhase : null);
      
      if (!nodeType) {
        if (index < 5) { // 仅输出前几条记录，避免日志过多
          console.log('【阶段提取】记录无阶段节点信息:', record.id, record.title);
        }
        return;
      }
      
      extractedNodeCount++;
      
      // 映射可能的字段值到标准枚举值
      let standardNodeType = nodeType;
      
      // 处理不同格式的字段值
      if (nodeType === 'INITIAL' || nodeType === 'initial' || nodeType === '初诊') {
        standardNodeType = StageNodeEnum.INITIAL_VISIT;
      } else if (nodeType === 'DIAGNOSIS' || nodeType === 'diagnosis' || nodeType === '确诊') {
        standardNodeType = StageNodeEnum.DIAGNOSIS;
      } else if (nodeType === 'TREATMENT' || nodeType === 'treatment' || nodeType === '治疗') {
        standardNodeType = StageNodeEnum.TREATMENT;
      } else if (nodeType === 'FOLLOW_UP' || nodeType === 'follow_up' || nodeType === 'followUp' || nodeType === '随访') {
        standardNodeType = StageNodeEnum.FOLLOW_UP;
      } else if (nodeType === 'PROGNOSIS' || nodeType === 'prognosis' || nodeType === '预后') {
        standardNodeType = StageNodeEnum.PROGNOSIS;
      } else if (nodeType === 'ARCHIVE' || nodeType === 'archive' || nodeType === 'CLOSED' || nodeType === 'closed' || nodeType === '封档' || nodeType === '归档') {
        standardNodeType = StageNodeEnum.ARCHIVE;
      }
      
      console.log(`【阶段提取】找到阶段节点 [${index}]: ${nodeType} -> ${standardNodeType}, 标题: ${record.title}`);
      
      // 确保有日期数据
      let recordDate: Date;
      if (record.recordDate instanceof Date) {
        recordDate = record.recordDate;
      } else if (record.recordDate) {
        recordDate = new Date(record.recordDate);
      } else if (record.created_at) {
        recordDate = new Date(record.created_at);
      } else if (record.createdAt) {
        recordDate = new Date(record.createdAt);
      } else {
        recordDate = new Date(); // 默认为当前时间
      }
      
      // 检查这个节点类型是否已有记录，如果有则比较日期，保留最早的
      if (stageNodeMap.has(standardNodeType)) {
        const existingRecord = stageNodeMap.get(standardNodeType)!;
        
        // 确保有现有记录的日期
        let existingDate: Date;
        if (existingRecord.recordDate instanceof Date) {
          existingDate = existingRecord.recordDate;
        } else if (existingRecord.recordDate) {
          existingDate = new Date(existingRecord.recordDate);
        } else if (existingRecord.created_at) {
          existingDate = new Date(existingRecord.created_at);
        } else if (existingRecord.createdAt) {
          existingDate = new Date(existingRecord.createdAt);
        } else {
          existingDate = new Date(); // 默认为当前时间
        }
        
        // 如果新记录日期更早，则替换
        if (recordDate < existingDate) {
          stageNodeMap.set(standardNodeType, { ...record, stage_node: standardNodeType });
        }
      } else {
        // 第一次遇到这个节点类型
        stageNodeMap.set(standardNodeType, { ...record, stage_node: standardNodeType });
      }
    });
    
    // 根据标准顺序创建处理后的阶段数组
    const stages = nodeOrder
      .filter(node => stageNodeMap.has(node))
      .map((node, index) => {
        const record = stageNodeMap.get(node)!;
        return {
          stage: node,
          name: NODE_NAMES[node] || node,
          date: record.recordDate instanceof Date ? 
            record.recordDate : new Date(record.recordDate || record.created_at || record.createdAt || Date.now()),
          isCompleted: true, // 存在记录的节点视为已完成
          isActive: false, // 稍后设置活跃节点
          record
        };
      });
    
    // 设置最"晚"的节点为活跃节点（按病程顺序，不是日期）
    if (stages.length > 0) {
      const activeIdx = stages.length - 1;
      stages[activeIdx].isActive = true;
    }
    
    console.log(`【阶段提取】共处理 ${records.length} 条记录，提取到 ${extractedNodeCount} 个有效阶段节点，最终生成 ${stages.length} 个病程阶段`);
    if (stages.length > 0) {
      console.log('【阶段提取】处理后的阶段:', stages.map(s => `${s.name}(${formatDate(s.date).year}${formatDate(s.date).monthDay})`).join(' -> '));
      console.log('【阶段提取】活跃阶段:', stages.find(s => s.isActive)?.name);
    } else {
      console.log('【阶段提取】警告: 未能提取到任何有效病程阶段，将使用 disease.stage 作为回退');
    }
    
    setProcessedStages(stages);
  }, [records]);

  // 整合显示用的阶段数据
  const displayStages = useMemo(() => {
    // 获取当前疾病处于的阶段（从disease对象或处理后的阶段中获取）
    let currentStage = StageNodeEnum.INITIAL_VISIT; // 默认初始阶段
    
    if (processedStages.length > 0) {
      // 如果有处理后的阶段，使用活跃阶段
      const activeStage = processedStages.find(stage => stage.isActive);
      if (activeStage) {
        currentStage = activeStage.stage;
      }
    } else if (disease?.stage) {
      // 否则，从disease对象转换
      const stageMap: Record<string, StageNodeEnum> = {
        'INITIAL': StageNodeEnum.INITIAL_VISIT,
        'DIAGNOSIS': StageNodeEnum.DIAGNOSIS,
        'TREATMENT': StageNodeEnum.TREATMENT,
        'FOLLOW_UP': StageNodeEnum.FOLLOW_UP,
        'PROGNOSIS': StageNodeEnum.PROGNOSIS,
        'ARCHIVE': StageNodeEnum.ARCHIVE,
        'CLOSED': StageNodeEnum.ARCHIVE
      };
      currentStage = stageMap[disease.stage] || StageNodeEnum.INITIAL_VISIT;
    }
    
    // 找出当前阶段在标准顺序中的索引
    const nodeOrder = [
      StageNodeEnum.INITIAL_VISIT,
      StageNodeEnum.DIAGNOSIS, 
      StageNodeEnum.TREATMENT,
      StageNodeEnum.FOLLOW_UP,
      StageNodeEnum.PROGNOSIS,
      StageNodeEnum.ARCHIVE
    ];
    const currentStageIndex = nodeOrder.indexOf(currentStage);
    
    // 创建完整的6个阶段
    const fullStages = nodeOrder.map((node, index) => {
      // 查找是否有对应的处理后阶段
      const processedStage = processedStages.find(s => s.stage === node);
      
      // 如果有处理后的阶段数据，使用它的信息
      if (processedStage) {
        return {
          id: node,
          name: NODE_NAMES[node],
          date: processedStage.date,
          completed: index <= currentStageIndex,
          active: processedStage.isActive
        };
      }
      
      // 否则创建默认阶段
      return {
        id: node,
        name: NODE_NAMES[node],
        date: null,
        completed: index <= currentStageIndex,
        active: index === currentStageIndex
      };
    });
    
    console.log('【阶段显示】生成完整的6个阶段节点，当前阶段:', NODE_NAMES[currentStage]);
    
    return fullStages;
  }, [processedStages, disease?.stage]);
  
  // 格式化日期 - 两行显示年份和月日
  const formatDate = (dateStr: string | Date) => {
    if (!dateStr) return { year: '', monthDay: '' };
    
    try {
      const date = dateStr instanceof Date ? dateStr : new Date(dateStr);
      const year = date.getFullYear().toString();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      
      return {
        year: `${year}年`,
        monthDay: `${month}/${day}`
      };
    } catch (e) {
      return { year: '', monthDay: '' };
    }
  };
  
  // 计算病情趋势
  const trend = useMemo(() => {
    console.log('【趋势判断】开始计算趋势, aiData:', aiData, '当前时间戳:', lastUpdate);
    
    // 首先使用传入的aiData中的trend数据
    if (aiData?.trend) {
      const aiTrend = typeof aiData.trend === 'string' ? aiData.trend.toUpperCase() : '';
      console.log('【趋势判断】从传入的aiData中获取trend数据:', aiTrend);
      
      // 映射AI报告中的趋势值到前端显示值
      if (aiTrend.includes('IMPROVING') || 
          aiTrend.includes('REMISSION') || 
          aiTrend.includes('好转') || 
          aiTrend.includes('缓解')) {
        console.log('【趋势判断】确定为: improving (好转)');
        return 'improving';
      } else if (aiTrend.includes('STABLE') || 
                aiTrend.includes('稳定') || 
                aiTrend.includes('平稳')) {
        console.log('【趋势判断】确定为: stable (稳定)');
        return 'stable';
      } else if (aiTrend.includes('WORSENING') || 
                aiTrend.includes('CRITICAL') || 
                aiTrend.includes('进展') || 
                aiTrend.includes('恶化') || 
                aiTrend.includes('加重')) {
        console.log('【趋势判断】确定为: needs_attention (需要警惕)');
        return 'needs_attention';
      }
    }

    // 使用status和riskLevel辅助判断
    if (aiData?.status || aiData?.riskLevel || aiData?.trend) {
      console.log('【趋势判断】使用status、trend和riskLevel辅助判断:', {
        status: aiData?.status,
        trend: aiData?.trend,
        riskLevel: aiData?.riskLevel
      });
      
      // 如果trend是进展期，需要警惕
      if (aiData?.trend && typeof aiData.trend === 'string' && (
          aiData.trend.includes('进展') ||
          aiData.trend.includes('恶化') ||
          aiData.trend.includes('危')
      )) {
        console.log('【趋势判断】trend包含"进展/恶化/危"，确定为: needs_attention');
        return 'needs_attention';
      }
      
      // 如果状态是高危或风险等级高，需要警惕
      if ((aiData?.status && typeof aiData.status === 'string' && (
            aiData.status.includes('高危') || 
            aiData.status.includes('危重') || 
            aiData.status.includes('HIGH')
          )) || 
          (aiData?.riskLevel && typeof aiData.riskLevel === 'string' && (
            aiData.riskLevel.includes('高') || 
            aiData.riskLevel.includes('HIGH')
          ))) {
        console.log('【趋势判断】status或riskLevel为高风险，确定为: needs_attention');
        return 'needs_attention';
      }
      
      // 如果状态是稳定或风险等级低，趋势良好
      if ((aiData?.trend && typeof aiData.trend === 'string' && (
            aiData.trend.includes('稳定') || 
            aiData.trend.includes('平稳')
          )) ||
          (aiData?.status && typeof aiData.status === 'string' && (
            aiData.status.includes('稳定') || 
            aiData.status.includes('良好') || 
            aiData.status.includes('STABLE')
          )) || 
          (aiData?.riskLevel && typeof aiData.riskLevel === 'string' && (
            aiData.riskLevel.includes('低') || 
            aiData.riskLevel.includes('LOW')
          ))) {
        console.log('【趋势判断】评估为稳定状态，确定为: stable');
        return 'stable';
      }
    }

    // 如果没有传入aiData或aiData中无trend数据，尝试从disease.aiReport获取
    if (disease?.aiReport) {
      try {
        // 尝试解析JSON数据
        let reportData;
        if (typeof disease.aiReport === 'string') {
          reportData = JSON.parse(disease.aiReport);
        } else {
          reportData = disease.aiReport;
        }

        // 优先使用AI报告中的trend字段
        if (reportData?.disease_status?.trend) {
          const aiTrend = reportData.disease_status.trend.toUpperCase();
          console.log('【趋势判断】从disease.aiReport中提取到trend数据:', aiTrend);
          
          // 映射AI报告中的趋势值到前端显示值
          if (aiTrend.includes('IMPROVING') || 
              aiTrend.includes('REMISSION') || 
              aiTrend.includes('好转') || 
              aiTrend.includes('缓解')) {
            return 'improving';
          } else if (aiTrend.includes('STABLE') || 
                    aiTrend.includes('稳定') || 
                    aiTrend.includes('平稳')) {
            return 'stable';
          } else if (aiTrend.includes('WORSENING') || 
                    aiTrend.includes('CRITICAL') || 
                    aiTrend.includes('进展') || 
                    aiTrend.includes('恶化') || 
                    aiTrend.includes('加重')) {
            return 'needs_attention';
          }
        }
        
        // 如果没有直接的trend字段，尝试分析症状状态
        if (reportData?.symptoms_analysis?.current_symptoms) {
          const symptoms = reportData.symptoms_analysis.current_symptoms;
          
          // 计算症状严重度和变化趋势的综合评分
          let severeSymptomCount = 0;
          let improvedSymptomCount = 0;
          let unchangedSymptomCount = 0;
          let worsenedSymptomCount = 0;
          
          symptoms.forEach((symptom: {
            severity?: string;
            change?: string;
            name?: string;
          }) => {
            // 检查症状严重程度
            if (symptom.severity === 'SEVERE' || symptom.severity === 'MODERATE') {
              severeSymptomCount++;
            }
            
            // 检查症状变化趋势
            if (symptom.change === 'IMPROVED') {
              improvedSymptomCount++;
            } else if (symptom.change === 'UNCHANGED') {
              unchangedSymptomCount++;
            } else if (symptom.change === 'WORSENED' || symptom.change === 'NEW') {
              worsenedSymptomCount++;
            }
          });
          
          console.log('【趋势判断】症状分析:', {
            总症状数: symptoms.length,
            严重症状数: severeSymptomCount,
            改善症状数: improvedSymptomCount,
            稳定症状数: unchangedSymptomCount,
            恶化症状数: worsenedSymptomCount
          });
          
          // 基于症状分析判断趋势
          if (worsenedSymptomCount > improvedSymptomCount) {
            return 'needs_attention';
          } else if (improvedSymptomCount > worsenedSymptomCount && severeSymptomCount === 0) {
            return 'improving';
          } else {
            return 'stable';
          }
        }
      } catch (error) {
        console.error('解析AI报告数据出错:', error);
      }
    }
    
    // 如果无法从AI报告获取数据，尝试从记录提取的阶段判断
    if (processedStages.length > 0) {
      const activeStage = processedStages.find(stage => stage.isActive);
      if (activeStage) {
        if (activeStage.stage === StageNodeEnum.FOLLOW_UP || 
            activeStage.stage === StageNodeEnum.PROGNOSIS) {
          return 'improving';
        } else if (activeStage.stage === StageNodeEnum.TREATMENT) {
          return 'stable';
        } else {
          return 'needs_attention';
        }
      }
    }
    
    // 最后尝试使用disease.stage作为回退选项
    if (disease?.stage === 'FOLLOW_UP' || disease?.stage === 'PROGNOSIS') {
      return 'improving';
    } else if (disease?.stage === 'TREATMENT') {
      return 'stable';
    } else {
      return 'needs_attention';
    }
  }, [aiData, disease, processedStages, lastUpdate]);
  
  // 获取趋势相关UI元素
  const getTrendUI = () => {
    // 如果有明确的状态和风险等级，显示更详细的信息
    const trendInfo = aiData?.trend ? `${aiData.trend}` : '';
    const statusInfo = aiData?.status ? `${aiData.status}` : '';
    const riskInfo = aiData?.riskLevel ? `${aiData.riskLevel}风险` : '';
    
    // 优先使用trend进行显示，然后是status+riskLevel组合
    let statusText = trendInfo;
    if (!statusText && statusInfo && riskInfo) {
      statusText = `${statusInfo}(${riskInfo})`;
    } else if (!statusText) {
      statusText = statusInfo || riskInfo;
    }

    switch (trend) {
      case 'improving':
        return {
          icon: <TrendingUpIcon sx={{ color: 'success.main' }} />,
          color: 'success',
          text: statusText || '好转中'
        };
      case 'stable':
        return {
          icon: <TrendingFlatIcon sx={{ color: 'info.main' }} />,
          color: 'info',
          text: statusText || '稳定'
        };
      case 'needs_attention':
        return {
          icon: <TrendingDownIcon sx={{ color: 'warning.main' }} />,
          color: 'warning',
          text: statusText || '需要警惕'
        };
      default:
        return {
          icon: <TrendingFlatIcon sx={{ color: 'info.main' }} />,
          color: 'info',
          text: statusText || '未知'
        };
    }
  };
  
  const renderTrendUI = (): React.ReactElement | null => {
    const trendData = getTrendUI();
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1, color: `${trendData.color}.main` }}>
        {trendData.icon}
        <Typography variant="subtitle2" sx={{ ml: 1 }}>{trendData.text}</Typography>
      </Box>
    );
  };

  if (loading || loadingRecords) {
    return (
      <BaseCard title="病程发展" loading={true}>
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress size={30} />
        </Box>
      </BaseCard>
    );
  }
  
  // 简化渲染逻辑
  if (!processedStages || processedStages.length === 0) {
    // 如果没有提取出任何阶段，显示空状态
    return (
      <BaseCard title="病程发展">
        {renderTrendUI()} {/* 即使没有阶段也尝试显示趋势 */}
        <Typography variant="body2" color="text.secondary" align="center" sx={{ p: 2, fontSize: '0.75rem'}}>
          暂无详细病程发展信息
        </Typography>
      </BaseCard>
    );
  }
  
  return (
    <BaseCard 
      title="病程发展"
      subTitle={disease?.name ? `关于"${disease.name}"的病程` : ''}
      allowFullscreen
    >
      {/* 渲染病情趋势 */}
      {renderTrendUI()}
      
      <Stepper alternativeLabel activeStep={displayStages.findIndex(stage => stage.active === true)} connector={<CustomConnector />}>
        {displayStages.map((stage, index) => {
          // 获取阶段状态，以确定颜色
          const status = getStageStatus(index, displayStages.findIndex(s => s.active === true));
          let labelColor = theme.palette.text.secondary;
          if (status === 1) labelColor = theme.palette.primary.main; // 进行中
          if (status === 2) labelColor = theme.palette.primary.main; // 已完成
          
          const nodeName = stage.name;
          const stageDate = formatDateString(stage.date);
          
          return (
            <Step key={stage.id || index}>
              <StepLabel 
                StepIconComponent={CustomStepIcon}
                sx={{ 
                  '& .MuiStepLabel-label': {
                    color: labelColor,
                    fontWeight: status === 1 ? 'bold' : 'normal',
                    fontSize: '0.75rem',
                    mt: 0.5
                  }
                }}
              >
                {nodeName}
                <Typography variant="caption" display="block" sx={{ fontSize: '0.65rem', color: theme.palette.text.disabled }}>
                  {stageDate}
                </Typography>
              </StepLabel>
            </Step>
          );
        })}
      </Stepper>
      
      {/* 显示当前活动阶段的详情 */}
      {(() => {
        const activeStageDetails = displayStages.find(stage => stage.active === true);
        if (activeStageDetails) {
          return (
            <Box sx={{ mt: 2, p: 1.5, bgcolor: theme.palette.background.default, borderRadius: 1, border: `1px solid ${theme.palette.divider}` }}>
              <Typography variant="subtitle2" sx={{ mb: 0.5, fontWeight: 'bold' }}>
                当前阶段: {activeStageDetails.name}
              </Typography>
              <Typography variant="caption" color="text.secondary" sx={{ display: 'block', fontSize: '0.7rem' }}>
                记录于: {formatDateString(activeStageDetails.date)}
              </Typography>
            </Box>
          );
        }
        return null;
      })()}

      {/* 显示特别说明(treatment字段) - 恢复之前被错误删除的JSX */}
      {disease?.treatment && (
        <>
          <Divider sx={{ my: 1.5 }} />
          <Box>
            <Typography variant="body2" sx={{ fontWeight: 500, mb: 0.5, fontSize: '0.75rem' }}>
              特别说明:
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
              {disease.treatment}
            </Typography>
          </Box>
        </>
      )}
    </BaseCard>
  );
};

export default DiseaseTimeline; 