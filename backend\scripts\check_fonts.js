/**
 * 字体检查脚本
 * 用于检查系统中可用的中文字体
 */

const fs = require('fs');
const path = require('path');
const os = require('os');
const { execSync } = require('child_process');

// 获取操作系统平台
const platform = os.platform();
console.log(`当前操作系统平台: ${platform}`);

// 检查字体目录
const fontDirectories = platform === 'win32'
  ? ['C:\\Windows\\Fonts']
  : platform === 'darwin'
    ? ['/Library/Fonts', '/System/Library/Fonts']
    : [
        '/usr/share/fonts',
        '/usr/local/share/fonts',
        '/usr/share/fonts/truetype',
        '/usr/share/fonts/opentype',
        '/usr/share/fonts/truetype/wqy',
        '/usr/share/fonts/opentype/noto'
      ];

// 检查本地字体目录
const localFontDir = path.join(__dirname, '..', 'fonts');
if (fs.existsSync(localFontDir)) {
  fontDirectories.push(localFontDir);
}

// 检查每个字体目录
fontDirectories.forEach(dir => {
  console.log(`\n检查字体目录: ${dir}`);
  
  if (!fs.existsSync(dir)) {
    console.log(`  目录不存在`);
    return;
  }
  
  try {
    // 获取目录中的所有文件
    const files = fs.readdirSync(dir);
    
    // 过滤出字体文件
    const fontFiles = files.filter(file => 
      file.endsWith('.ttf') || 
      file.endsWith('.ttc') || 
      file.endsWith('.otf')
    );
    
    if (fontFiles.length === 0) {
      console.log(`  未找到字体文件`);
      return;
    }
    
    console.log(`  找到 ${fontFiles.length} 个字体文件`);
    
    // 过滤出可能的中文字体
    const chineseFonts = fontFiles.filter(file => 
      file.toLowerCase().includes('chinese') ||
      file.toLowerCase().includes('cjk') ||
      file.toLowerCase().includes('ming') ||
      file.toLowerCase().includes('song') ||
      file.toLowerCase().includes('hei') ||
      file.toLowerCase().includes('kai') ||
      file.toLowerCase().includes('gothic') ||
      file.toLowerCase().includes('noto') ||
      file.toLowerCase().includes('wqy')
    );
    
    if (chineseFonts.length > 0) {
      console.log(`  找到 ${chineseFonts.length} 个可能的中文字体:`);
      chineseFonts.forEach(font => {
        console.log(`    - ${font}`);
      });
    } else {
      console.log(`  未找到可能的中文字体`);
    }
  } catch (err) {
    console.error(`  读取目录失败: ${err.message}`);
  }
});

// 如果是Linux系统，尝试使用fc-list命令
if (platform === 'linux') {
  console.log('\n尝试使用fc-list命令获取中文字体:');
  try {
    const fcListOutput = execSync('fc-list :lang=zh').toString();
    const fontPaths = fcListOutput.split('\n').filter(line => line.trim() !== '');
    
    console.log(`  找到 ${fontPaths.length} 个中文字体:`);
    fontPaths.forEach(fontPath => {
      console.log(`    - ${fontPath}`);
    });
  } catch (err) {
    console.error(`  执行fc-list命令失败: ${err.message}`);
  }
}

// 检查本地字体目录
console.log(`\n检查本地字体目录: ${localFontDir}`);
if (!fs.existsSync(localFontDir)) {
  console.log(`  创建本地字体目录`);
  try {
    fs.mkdirSync(localFontDir, { recursive: true });
    console.log(`  目录创建成功`);
  } catch (err) {
    console.error(`  创建目录失败: ${err.message}`);
  }
} else {
  console.log(`  目录已存在`);
  
  try {
    const files = fs.readdirSync(localFontDir);
    const fontFiles = files.filter(file => 
      file.endsWith('.ttf') || 
      file.endsWith('.ttc') || 
      file.endsWith('.otf')
    );
    
    if (fontFiles.length > 0) {
      console.log(`  找到 ${fontFiles.length} 个字体文件:`);
      fontFiles.forEach(font => {
        console.log(`    - ${font}`);
      });
    } else {
      console.log(`  未找到字体文件`);
    }
  } catch (err) {
    console.error(`  读取目录失败: ${err.message}`);
  }
}

console.log('\n字体检查完成');
