const bcrypt = require('bcrypt');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

rl.question('请输入您要生成哈希的密码: ', (password) => {
  rl.close();

  const saltRounds = 10; // 推荐的 bcrypt salt 轮数

  bcrypt.hash(password, saltRounds, function(err, hash) {
    if (err) {
      console.error('生成密码哈希时出错:', err);
      process.exit(1);
    } else {
      console.log('\n生成的密码哈希 (请复制此字符串):');
      console.log(hash);
      process.exit(0);
    }
  });
});