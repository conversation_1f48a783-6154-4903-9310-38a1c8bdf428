import React from 'react';
import { Box, Typography, Chip, Tooltip, Stack } from '@mui/material';
import {
  LockOutlined as PrivateIcon,
} from '@mui/icons-material';
import BaseCard from './BaseCard';

interface DiseaseInfoCardProps {
  disease: any;
  loading?: boolean;
  survivalPrediction?: any;
}

/**
 * 病理信息卡片组件
 * 显示病理基本信息，包括名称、诊断日期、阶段等
 */
const DiseaseInfoCard: React.FC<DiseaseInfoCardProps> = ({ disease, loading = false, survivalPrediction }) => {
  
  if (!disease && !loading) {
    return (
      <BaseCard title="病理信息" loading={loading}>
        <Typography variant="body2" color="text.secondary" align="center" sx={{ fontSize: '0.75rem' }}>
          未找到病理信息
        </Typography>
      </BaseCard>
    );
  }
  
  // 检查隐私状态，只有在确定是隐私时才显示标签
  const headerActionContent = disease?.isPrivate === 1 || disease?.isPrivate === true ? (
    <Tooltip title="此病理已设为隐私状态，仅自己可见">
      <Chip
        icon={<PrivateIcon sx={{ fontSize: '0.8rem !important' }} />}
        label="隐私"
        size="small"
        color="secondary"
        sx={{
          bgcolor: 'secondary.main',
          color: 'white',
          fontSize: '0.7rem',
          height: 22
        }}
      />
    </Tooltip>
  ) : null;
  
  return (
    <BaseCard 
      title="病理信息" 
      loading={loading}
      headerAction={headerActionContent}
    >
      <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
        {/* 仅保留name、description和treatment三个字段 */}
        <Stack spacing={2}>
          {/* 诊断名称 */}
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 0.5 }}>
              诊断:
            </Typography>
            <Typography variant="body2">
              {disease?.name || '暂无'}
            </Typography>
          </Box>
          
          {/* 疾病描述 */}
          {disease?.description && (
            <Box sx={{ mb: 0.5 }}>
              <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                详细描述:
              </Typography>
              <Typography variant="body2">
                {disease.description}
              </Typography>
            </Box>
          )}
          
          {/* 治疗信息 */}
          {disease?.treatment && (
            <Box sx={{ mb: 0.5 }}>
              <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                治疗方案:
              </Typography>
              <Typography variant="body2">
                {disease.treatment}
              </Typography>
            </Box>
          )}
        </Stack>
      </Box>
    </BaseCard>
  );
};

export default DiseaseInfoCard; 