import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Box, 
  Paper, 
  Typography, 
  TextField, 
  Button, 
  Stack,
  Divider,
  Alert,
  CircularProgress,
  IconButton,
  InputAdornment
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { changePassword } from '../services/authService';
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';

// 自定义样式组件，隐藏浏览器默认的密码可见性控件
const HideRevealStyles = styled('style')({
  '@global': {
    'input::-ms-reveal, input::-ms-clear, input::-webkit-contacts-auto-fill-button, input::-webkit-credentials-auto-fill-button, input::-webkit-strong-password-auto-fill-button': {
      display: 'none !important',
      visibility: 'hidden !important',
      opacity: '0 !important',
      pointerEvents: 'none !important',
      position: 'absolute !important',
      right: '0 !important'
    }
  }
});

// 密码复杂度验证函数
const validatePasswordStrength = (password: string): { isValid: boolean, message: string } => {
  // 至少8个字符
  if (password.length < 8) {
    return { isValid: false, message: '密码长度至少为8个字符' };
  }
  
  // 至少包含一个数字
  if (!/\d/.test(password)) {
    return { isValid: false, message: '密码必须包含至少一个数字' };
  }
  
  // 至少包含一个小写字母
  if (!/[a-z]/.test(password)) {
    return { isValid: false, message: '密码必须包含至少一个小写字母' };
  }
  
  // 至少包含一个大写字母
  if (!/[A-Z]/.test(password)) {
    return { isValid: false, message: '密码必须包含至少一个大写字母' };
  }
  
  // 至少包含一个特殊字符
  if (!/[!@#$%^&*()_+\-=[{};':"\\\\|,.<>/?]/.test(password)) {
    return { isValid: false, message: '密码必须包含至少一个特殊字符' };
  }
  
  return { isValid: true, message: '密码强度良好' };
};

// 修改密码页面组件
const ChangePasswordPage: React.FC = () => {
  const navigate = useNavigate();
  
  // 密码修改表单状态
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [passwordSuccess, setPasswordSuccess] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // 密码显示状态
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  // 切换密码显示状态的处理函数
  const handleTogglePasswordVisibility = (field: 'current' | 'new' | 'confirm') => {
    if (field === 'current') {
      setShowCurrentPassword(!showCurrentPassword);
    } else if (field === 'new') {
      setShowNewPassword(!showNewPassword);
    } else {
      setShowConfirmPassword(!showConfirmPassword);
    }
  };
  
  // 密码强度验证
  const passwordValidation = newPassword ? validatePasswordStrength(newPassword) : { isValid: true, message: '' };
  
  // 处理修改密码
  const handleChangePassword = async () => {
    // 重置错误信息
    setPasswordError(null);
    
    // 验证密码
    if (!currentPassword) {
      setPasswordError('请输入当前密码');
      return;
    }
    
    if (!newPassword) {
      setPasswordError('请输入新密码');
      return;
    }
    
    if (newPassword !== confirmPassword) {
      setPasswordError('两次输入的密码不一致');
      return;
    }
    
    // 密码复杂度验证
    if (!passwordValidation.isValid) {
      setPasswordError(passwordValidation.message);
      return;
    }
    
    // 检查新密码是否与当前密码相同
    if (newPassword === currentPassword) {
      setPasswordError('新密码不能与当前密码相同');
      return;
    }
    
    // 设置提交状态
    setIsSubmitting(true);
    
    try {
      // 调用API修改密码
      await changePassword({
        currentPassword,
        newPassword
      });
      
      // 修改成功
      setPasswordSuccess('密码修改成功，即将返回个人资料页');
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
      
      // 3秒后自动返回个人资料页
      setTimeout(() => {
        navigate('/profile');
      }, 3000);
    } catch (error: any) {
      // 详细记录错误信息用于调试
      console.error('密码修改详细错误信息:', {
        error,
        responseStatus: error.status || error.response?.status,
        responseData: error.responseData || error.response?.data,
        message: error.message
      });
      
      // 处理错误情况
      if (error.status === 401 || error.response?.status === 401) {
        setPasswordError('当前密码不正确');
      } else if (error.status === 400 || error.response?.status === 400) {
        // 尝试直接获取服务器返回的错误消息
        const serverErrorMessage = error.message || 
                                  error.responseData?.error || 
                                  error.response?.data?.error || 
                                  '请求参数有误，请确认填写了所有必填项';
        setPasswordError(serverErrorMessage);
      } else if (error.message) {
        // 使用增强的错误对象中的消息
        setPasswordError(error.message);
      } else if (error.responseData?.error || error.response?.data?.error) {
        // 从响应数据中提取错误消息
        setPasswordError(error.responseData?.error || error.response?.data?.error);
      } else {
        // 默认错误消息
        setPasswordError('密码修改失败，请稍后重试');
      }
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // 密码强度指示
  const getPasswordStrengthHelperText = () => {
    return "密码要求: 至少8个字符，必须包含大写字母、小写字母、数字和特殊字符";
  };
  
  return (
    <Box sx={{ p: 2, maxWidth: 600, mx: 'auto' }}>
      <HideRevealStyles />
      <Paper 
        elevation={0} 
        sx={{ 
          p: 3,
          border: 'none'
        }}
      >
        <Typography 
          variant="h5" 
          gutterBottom
          sx={{ fontSize: { xs: '1.1rem', sm: '1.3rem' }, fontWeight: 500 }}
        >
          修改密码
        </Typography>
      
        <Divider sx={{ mb: 2 }} />
      
        {/* 显示错误信息 */}
        {passwordError && (
          <Alert severity="error" sx={{ mb: 2, '& .MuiAlert-message': { fontSize: '0.75rem' } }}>
            {passwordError}
          </Alert>
        )}
        
        {/* 显示成功信息 */}
        {passwordSuccess && (
          <Alert severity="success" sx={{ mb: 2, '& .MuiAlert-message': { fontSize: '0.75rem' } }}>
            {passwordSuccess}
          </Alert>
        )}

        <form onSubmit={(e) => {
          e.preventDefault();
          handleChangePassword();
        }}>
          <Stack spacing={2} sx={{ 
            '& .MuiInputBase-root': { fontSize: '0.75rem' }, 
            '& .MuiInputLabel-root': { fontSize: '0.75rem' },
            '& .MuiFormHelperText-root': { fontSize: '0.7rem' }
          }}>
          <TextField
            label="当前密码"
            type={showCurrentPassword ? "text" : "password"}
            value={currentPassword}
            onChange={(e) => setCurrentPassword(e.target.value)}
            fullWidth
            required
            disabled={isSubmitting}
            size="small"
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={() => handleTogglePasswordVisibility('current')}
                    edge="end"
                    size="small"
                  >
                    {showCurrentPassword ? <VisibilityOff fontSize="small" /> : <Visibility fontSize="small" />}
                  </IconButton>
                </InputAdornment>
                ),
            }}
          />
          
          <TextField
            label="新密码"
            type={showNewPassword ? "text" : "password"}
            value={newPassword}
            onChange={(e) => setNewPassword(e.target.value)}
            fullWidth
            required
            disabled={isSubmitting}
            size="small"
            error={newPassword !== '' && !passwordValidation.isValid}
            helperText={getPasswordStrengthHelperText()}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={() => handleTogglePasswordVisibility('new')}
                    edge="end"
                    size="small"
                  >
                    {showNewPassword ? <VisibilityOff fontSize="small" /> : <Visibility fontSize="small" />}
                  </IconButton>
                </InputAdornment>
                ),
            }}
          />
          
          <TextField
            label="确认新密码"
            type={showConfirmPassword ? "text" : "password"}
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            fullWidth
            required
            disabled={isSubmitting}
            size="small"
            error={newPassword !== confirmPassword && confirmPassword !== ''}
            helperText={newPassword !== confirmPassword && confirmPassword !== '' ? '两次输入的密码不一致' : ''}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={() => handleTogglePasswordVisibility('confirm')}
                    edge="end"
                    size="small"
                  >
                    {showConfirmPassword ? <VisibilityOff fontSize="small" /> : <Visibility fontSize="small" />}
                  </IconButton>
                </InputAdornment>
                ),
            }}
          />
          
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            mt: 2,
            flexDirection: { xs: 'column', sm: 'row' }, // 移动端垂直排列按钮
            gap: { xs: 1, sm: 0 } // 按钮间距
          }}>
            <Button 
              variant="outlined" 
              onClick={() => navigate('/profile')}
              disabled={isSubmitting}
              sx={{ 
                mr: { xs: 0, sm: 1 },
                width: { xs: '100%', sm: 'auto' }, // 移动端按钮占满宽度
                fontSize: '0.75rem'
              }}
            >
              返回
            </Button>
            
            <Button 
              variant="contained" 
              type="submit"
              disabled={isSubmitting}
              sx={{ 
                width: { xs: '100%', sm: 'auto' }, // 移动端按钮占满宽度
                fontSize: '0.75rem'
              }}
            >
              {isSubmitting ? (
                <>
                  <CircularProgress size={20} color="inherit" sx={{ mr: 1 }} />
                  提交中...
                </>
              ) : '修改密码'}
            </Button>
          </Box>
        </Stack>
        </form>
      </Paper>
    </Box>
  );
};

export default ChangePasswordPage; 