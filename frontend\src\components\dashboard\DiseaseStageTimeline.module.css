/* 时间线容器样式 */
.timelineContainer {
  width: 100%;
  overflow-x: auto;
  position: relative;
  padding: 16px 0;
  margin-top: 12px;
}

/* 时间线内容样式 */
.timelineContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 450px; /* 确保在小屏幕上也能看到所有节点 */
  position: relative;
}

/* 时间线背景线 - 为时间线添加整体背景线 */
.timelineContent::before {
  content: '';
  position: absolute;
  top: 12px;
  left: 20px;
  right: 20px;
  height: 2px;
  background-color: #e2e8f0;
  z-index: 0;
}

/* 箭头样式 - 给连接线添加箭头，表示方向 */
.connector {
  position: relative;
}

.connector::after {
  content: '→';
  position: absolute;
  top: -8px;
  right: 5px;
  color: #718096;
  font-size: 16px;
  opacity: 0.6;
}

/* 响应式调整 */
@media (max-width: 600px) {
  .timelineContainer {
    padding: 10px 0;
  }
  
  /* 调整小屏幕下节点的大小 */
  .stageNode {
    transform: scale(0.85);
  }
  
  /* 调整小屏幕下标签的大小 */
  .stageLabel {
    font-size: 0.65rem !important;
  }
  
  .dateLabel {
    font-size: 0.6rem !important;
  }
  
  .connector::after {
    font-size: 14px;
    top: -7px;
    right: 3px;
  }
}

/* 节点悬停效果 */
.nodeWrapper {
  cursor: pointer;
  transition: transform 0.2s ease;
  position: relative;
  z-index: 2;
}

.nodeWrapper:hover {
  transform: scale(1.1);
}

/* 标签动画效果 */
.chip {
  transition: all 0.3s ease;
}

.chip:hover {
  transform: translateY(-2px);
}

/* 进度指示器 - 增强当前活跃节点的视觉效果 */
.activeNode {
  position: relative;
}

.activeNode::before {
  content: '';
  position: absolute;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(237,137,54,0.2) 0%, rgba(237,137,54,0) 70%);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.3;
  }
  100% {
    transform: scale(0.95);
    opacity: 0.7;
  }
} 