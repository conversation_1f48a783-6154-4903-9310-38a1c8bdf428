const { Model, snakeCaseMappers } = require('objection');
const path = require('path');

class Attachment extends Model {
  static get tableName() {
    return 'attachments';
  }

  static get idColumn() {
    return 'id';
  }

  // 添加下划线命名映射配置
  static get columnNameMappers() {
    return snakeCaseMappers();
  }

  // 字段验证规则
  static get jsonSchema() {
    return {
      type: 'object',
      required: ['name', 'type', 'size', 'path', 'record_id'],
      properties: {
        id: { type: 'string' },
        name: { type: 'string', minLength: 1, maxLength: 255 },
        type: { type: 'string', maxLength: 100 },
        size: { type: 'integer' },
        path: { type: 'string', maxLength: 500 },
        record_id: { type: 'string' },
        description: { type: ['string', 'null'], maxLength: 1000 },
        is_private: { type: 'boolean', default: false },
        metadata: { type: ['object', 'null'] },
        deleted_at: { type: ['string', 'null'] }
      }
    };
  }

  // 关系定义
  static get relationMappings() {
    const Record = require('./Record');

    return {
      // 记录关系
      record: {
        relation: Model.BelongsToOneRelation,
        modelClass: Record,
        join: {
          from: 'attachments.record_id',
          to: 'records.id'
        }
      }
    };
  }

  // 软删除方法
  softDelete() {
    return this.$query().patch({
      deleted_at: new Date().toISOString()
    });
  }

  // 获取公共URL
  getPublicUrl() {
    // 根据实际部署环境配置
    return `/attachments/${path.basename(this.path)}`;
  }

  // 获取完整路径
  getFullPath(basePath) {
    return path.join(basePath, this.path);
  }

  // 获取元数据
  get parsedMetadata() {
    if (!this.metadata) return {};
    try {
      return typeof this.metadata === 'string' ? JSON.parse(this.metadata) : this.metadata;
    } catch (e) {
      return {};
    }
  }

  // 设置元数据
  set parsedMetadata(value) {
    this.metadata = JSON.stringify(value);
  }
}

module.exports = Attachment; 