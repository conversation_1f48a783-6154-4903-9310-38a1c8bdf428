#!/bin/bash

# 进入项目根目录
cd "$(pwd)"

# 添加规则到.gitignore文件
if grep -q "backend/uploads/" .gitignore; then
  echo "规则已存在于.gitignore文件中"
else
  echo -e "\n# 忽略上传文件目录" >> .gitignore
  echo "backend/uploads/*" >> .gitignore
  echo "# 但保留目录本身和.gitkeep文件" >> .gitignore
  echo "!backend/uploads/.gitkeep" >> .gitignore
  echo "规则已添加到.gitignore文件"
fi

# 确保uploads目录存在
mkdir -p backend/uploads

# 创建.gitkeep文件
touch backend/uploads/.gitkeep
echo "已创建.gitkeep文件"

# 从Git索引中移除uploads目录中的所有文件，但保留本地文件
git rm -r --cached backend/uploads/ 2>/dev/null || true
git add backend/uploads/.gitkeep
echo "已从Git索引中移除uploads目录中的文件"

echo "完成！现在您可以提交这些更改："
echo "git add .gitignore"
echo "git commit -m \"添加规则：忽略backend/uploads目录中的文件\""
