import React, { useEffect } from 'react';
import { BrowserRouter, Routes, Route, Navigate, Outlet } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import LoginPage from './pages/Login';
import RegisterPage from './pages/Register';
import ProfilePage from './pages/Profile';
import ChangePasswordPage from './pages/ChangePassword';
import SubscriptionPage from './pages/Subscription';
import PatientsPage from './pages/PatientsPage';
import PatientDetail from './pages/PatientDetail';
import DiseasePage from './pages/DiseasePage';
import DiseaseDetail from './pages/DiseaseDetail';
import RecordCreatePage from './pages/RecordCreatePage';
import RecordManagePage from './pages/RecordManagePage';
import RecordDetailPage from './pages/RecordDetailPage';
import RecordEditPage from './pages/RecordEditPage';
import Dashboard from './pages/Dashboard';
import AIAssistantPage from './pages/ai-assistant/AIAssistantPage';
import AIReportDetailPage from './pages/ai-assistant/AIReportDetailPage';
import AIReportConfigPage from './pages/ai-assistant/AIReportConfigPage';
import ServiceAIAssistantPage from './pages/service-ai-reports/ServiceAIAssistantPage';
import AdminPage from './pages/AdminPage';
import AuthorizationManage from './pages/AuthorizationManage';
import ServiceRecordManagePage from './pages/ServiceRecordManagePage';
import ServiceReportManage from './pages/ServiceReportManage';
import ServiceAuthorizationManage from './pages/ServiceAuthorizationManage';
import ServiceDiseasePage from './pages/ServiceDiseasePage';
import ServiceRecordDetailPage from './pages/ServiceRecordDetailPage';
import ServiceRecordFormPage from './pages/ServiceRecordFormPage';
import ProtectedRoute from './components/ProtectedRoute';
import Layout from './components/Layout';
import ThemeProvider from './theme/ThemeProvider';
import { PatientDiseaseProvider } from './context/PatientDiseaseContext';
import { ServiceUserProvider } from './context/ServiceUserContext';
import { AppProvider } from './context/AppContext';
import { ErrorProvider } from './context/ErrorContext';
import { AiUsageProvider } from './context/AiUsageContext';
import DiseaseTabTest from './pages/DiseaseTabTest';
import authorizationService from './services/authorizationService';
// 导入API探测工具
import { detectApiPathMode, getApiPathMode } from './utils/apiDetector';
// 导入管理布局和路由
import AdminLayout from './pages/admin/AdminLayout';
import AdminRoutes from './pages/admin/AdminRoutes';

// 创建React Query客户端
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1, // 失败后重试1次
      refetchOnWindowFocus: false, // 窗口聚焦时不重新获取数据
    },
  },
});

// 定义WebSocket类型
interface WebSocketWithUrl extends WebSocket {
  url: string;
}

// App组件
function App() {
  // 初始化WebSocket连接 - 使用useRef保存WebSocket实例，避免重复连接
  const wsRef = React.useRef<WebSocket | null>(null);
  
  // 初始化API路径探测
  useEffect(() => {
    try {
      console.log('[App] 开始初始化API路径探测...');
      detectApiPathMode().then(() => {
        const mode = getApiPathMode();
        console.log(`[App] API路径探测完成，当前模式: ${mode}`);
      }).catch(error => {
        console.error('[App] API路径探测失败:', error);
      });
    } catch (error) {
      console.error('[App] API路径探测初始化失败，但应用将继续运行:', error);
    }
  }, []);
  
  // 初始化WebSocket连接
  useEffect(() => {
    let ws: WebSocketWithUrl | null = null;
    let isComponentMounted = true;
    let cleanupFunction: (() => void) | undefined;
    
    // 添加错误处理，确保WebSocket问题不会影响应用运行
    const setupWebsocket = async () => {
      try {
        // 只有在登录状态下才初始化WebSocket
        const token = localStorage.getItem('token');
        if (!token) {
          console.log('[App] 未检测到登录状态，不初始化WebSocket连接');
          
          // 如果有WebSocket连接但没有token，关闭连接
          if (wsRef.current) {
            console.log('[App] 关闭现有WebSocket连接，因为没有token');
            try {
              wsRef.current.close();
              wsRef.current = null;
            } catch (closeError) {
              console.info('[App] 关闭WebSocket连接时发生错误:', closeError);
            }
          }
          return;
        }
        
        console.log('[App] 应用检测到登录状态，开始初始化WebSocket连接');
        
        // 如果已经有WebSocket实例且连接正常，则不重新创建
        if (wsRef.current && (wsRef.current.readyState === WebSocket.OPEN || wsRef.current.readyState === WebSocket.CONNECTING)) {
          console.log('[App] WebSocket连接已存在，不需要重新创建');
          return;
        }
        
        // 设置连接超时处理
        const connectionTimeout = setTimeout(() => {
          console.info('[App] WebSocket连接初始化超时');
          // 不抛出错误，让应用继续运行
        }, 5000); // 5秒超时
        
        try {
          // 创建新的WebSocket连接，添加错误处理
          ws = await authorizationService.initWebSocket() as WebSocketWithUrl;
          clearTimeout(connectionTimeout);
          
          // 如果连接失败或组件已卸载，直接返回
          if (!ws || !isComponentMounted) {
            console.info('[App] WebSocket连接初始化失败或组件已卸载，但应用将继续运行');
            return;
          }
          
          wsRef.current = ws; // 保存WebSocket实例
          
          // 添加连接成功日志
          console.log('[App] WebSocket连接已成功初始化', {
            连接状态: ws ? ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'][ws.readyState] : '未连接',
            连接URL: ws.url ? ws.url.substring(0, ws.url.indexOf('?')) + '?token=***' : '未知'
          });
          
          // 定义清理函数
          cleanupFunction = () => {
            clearTimeout(connectionTimeout);
            try {
              if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
                console.log('[App] App组件卸载，关闭WebSocket连接');
                const currentWs = wsRef.current;
                wsRef.current = null;
                currentWs.close();
              }
            } catch (closeError) {
              console.info('[App] 关闭WebSocket连接时发生错误:', closeError);
              // 忽略关闭错误
            }
          };
        } catch (wsError) {
          // 清除超时定时器
          clearTimeout(connectionTimeout);
          console.info('[App] WebSocket连接失败，但应用将继续运行:', wsError);
        }
      } catch (error) {
        // 捕获所有错误，确保WebSocket初始化问题不会影响应用
        console.info('[App] WebSocket初始化中发生错误，但应用将继续运行:', error);
      }
    };
    
    // 执行设置，使用延迟确保其他组件已经加载
    setTimeout(() => {
      setupWebsocket().catch(error => {
        console.error('[App] WebSocket设置过程中出现未捕获的错误:', error);
      });
    }, 1000); // 延迟1秒初始化WebSocket
    
    // 返回清理函数
    return () => {
      isComponentMounted = false;
      if (cleanupFunction) cleanupFunction();
    };
  }, []);
  
  // 页面可见性变化时处理WebSocket连接
  useEffect(() => {
    const handleVisibilityChange = async () => {
      if (document.visibilityState === 'visible') {
        try {
          const ws = await authorizationService.initWebSocket() as WebSocketWithUrl;
          // 只有在成功连接时才更新引用
          if (ws) {
            wsRef.current = ws;
            console.log('[App] 页面变为可见时重新连接WebSocket成功');
          } else {
            console.info('[App] 页面变为可见时重新连接WebSocket失败');
          }
        } catch (error) {
          console.info('[App] 页面变为可见时重新连接WebSocket失败:', error);
        }
      }
    };
    
    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <ErrorProvider>
          <BrowserRouter>
            <AppProvider>
              <AiUsageProvider>
                <PatientDiseaseProvider>
                  <ServiceUserProvider>
                    <Routes>
                      {/* 公共路由 */}
                      <Route path="/login" element={<LoginPage />} />
                      <Route path="/register" element={<RegisterPage />} />
                      
                      {/* 保护路由 - 需要登录才能访问 */}
                      <Route element={<ProtectedRoute><Outlet /></ProtectedRoute>}>
                        {/* 带布局的路由 */}
                        <Route element={<Layout><Outlet /></Layout>}>
                            <Route path="/dashboard" element={<Dashboard />} />
                          <Route path="/profile" element={<ProfilePage />} />
                          <Route path="/profile/change-password" element={<ChangePasswordPage />} />
                          <Route path="/profile/subscription" element={<SubscriptionPage />} />
                          <Route path="/patients" element={<PatientsPage />} />
                          <Route path="/patients/:id" element={<PatientDetail />} />
                          <Route path="/diseases" element={<DiseasePage />} />
                          <Route path="/diseases/:id" element={<DiseaseDetail />} />
                          {/* 记录相关路由 */}
                          <Route path="/records" element={<RecordCreatePage />} />
                          <Route path="/records/new" element={<RecordCreatePage />} />
                          <Route path="/records/manage" element={<RecordManagePage />} />
                          <Route path="/records/:id" element={<RecordDetailPage />} />
                          <Route path="/records/:id/edit" element={<RecordEditPage />} />
                          {/* 辅医模块路由 */}
                          <Route path="/ai-assistant" element={<AIAssistantPage />} />
                          <Route path="/ai-assistant/reports/:reportId" element={<AIReportDetailPage />} />
                          <Route path="/ai-assistant/config" element={<AIReportConfigPage />} />
                          {/* 服务用户AI报告路由 */}
                          <Route path="/service-ai-reports" element={<ServiceAIAssistantPage />} />
                          <Route path="/service-ai-reports/reports/:reportId" element={<AIReportDetailPage />} />
                          {/* 授权和服务管理路由 */}
                          <Route path="/authorizations" element={<AuthorizationManage />} />
                          <Route path="/service-diseases" element={<ServiceDiseasePage />} />
                          <Route path="/service-records" element={<ServiceRecordManagePage />} />
                          <Route path="/service-records/create" element={<ServiceRecordFormPage />} />
                          <Route path="/service-records/:id" element={<ServiceRecordDetailPage />} />
                          <Route path="/service-records/:id/edit" element={<ServiceRecordFormPage />} />
                          <Route path="/service-reports" element={<ServiceReportManage />} />
                          <Route path="/service-authorizations" element={<ServiceAuthorizationManage />} />
                          {/* 测试路由 */}
                          <Route path="/disease-tab-test/:diseaseId" element={<DiseaseTabTest />} />
                          {/* 系统管理路由入口 */}
                          <Route path="/admin" element={<AdminPage />} />
                        </Route>
                        
                        {/* 系统管理子路由 - 使用独立布局 */}
                        <Route path="/admin" element={<AdminLayout />}>
                          <Route path="*" element={<AdminRoutes />} />
                        </Route>
                      </Route>
                      
                      {/* 默认重定向到登录页 */}
                      <Route path="*" element={<Navigate to="/login" replace />} />
                    </Routes>
                  </ServiceUserProvider>
                </PatientDiseaseProvider>
              </AiUsageProvider>
            </AppProvider>
          </BrowserRouter>
        </ErrorProvider>
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;
