import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Button,
  useTheme,
  TextField,
  IconButton
} from '@mui/material';
import FilterListIcon from '@mui/icons-material/FilterList';
import SearchIcon from '@mui/icons-material/Search';
import CloseIcon from '@mui/icons-material/Close';
import RefreshIcon from '@mui/icons-material/Refresh';

// 简化状态选项
const statusOptions = [
  { value: 'ALL', label: '全部状态' },
  { value: 'ACTIVE', label: '已激活' },
  { value: 'INACTIVE', label: '未激活' } // 表示除了ACTIVE以外的所有状态
];

export interface AuthorizationFilterValues {
  search: string;
  status: string;
  role: string;
  privacyLevel: string;
}

interface AuthorizationFilterProps {
  onFilter: (filters: AuthorizationFilterValues) => void;
  onReset: () => void;
  isAuthorizer: boolean; // 是否为授权人视图
}

/**
 * 授权筛选组件
 * 支持关键词搜索和授权状态筛选
 */
const AuthorizationFilter: React.FC<AuthorizationFilterProps> = ({
  onFilter,
  onReset,
  isAuthorizer
}) => {
  const theme = useTheme();
  // const isMobile = useMediaQuery(theme.breakpoints.down('md')); // Removed unused variable
  
  // 筛选值状态
  const [filters, setFilters] = useState<AuthorizationFilterValues>({
    search: '',
    status: 'ALL',
    role: 'ALL',
    privacyLevel: 'ALL'
  });
  
  // 处理筛选值变化
  const handleFilterChange = (field: keyof AuthorizationFilterValues, value: string) => {
    const newFilters = { ...filters, [field]: value };
    setFilters(newFilters);
    
    // 立即应用筛选
    onFilter(newFilters);
  };
  
  // 重置所有筛选
  const handleReset = () => {
    const defaultFilters = {
      search: '',
      status: 'ALL',
      role: 'ALL',
      privacyLevel: 'ALL'
    };
    setFilters(defaultFilters);
    onReset();
  };
  
  return (
    <Paper
      elevation={0}
      sx={{
        p: 2,
        mb: 3,
        borderRadius: 2,
        border: '1px solid',
        borderColor: theme.palette.divider
      }}
    >
      {/* 筛选标题 */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <FilterListIcon sx={{ mr: 1, color: 'primary.main' }} />
        <Typography variant="subtitle1" fontWeight={600}>筛选{isAuthorizer ? '我授权的' : '授权给我的'}</Typography>
      </Box>
      
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        {/* 搜索关键词 */}
        <Box>
          <TextField
            fullWidth
            variant="outlined"
            size="small"
            label="搜索"
            value={filters.search}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            InputProps={{
              endAdornment: (
                <IconButton
                  size="small"
                  onClick={() => filters.search && handleFilterChange('search', '')}
                >
                  {filters.search ? <CloseIcon fontSize="small" /> : <SearchIcon fontSize="small" />}
                </IconButton>
              )
            }}
            placeholder={`搜索用户名、手机号、邮箱或患者名称`}
            helperText="支持模糊搜索：用户名、手机号、邮箱和患者名称"
          />
        </Box>
        
        {/* 筛选选项区域 */}
        <Box sx={{ display: 'flex', gap: 2 }}>
          {/* 状态筛选 */}
          <Box sx={{ flex: 1 }}>
            <FormControl fullWidth size="small">
              <InputLabel>授权状态</InputLabel>
              <Select
                value={filters.status}
                label="授权状态"
                onChange={(e) => handleFilterChange('status', e.target.value)}
              >
                {statusOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </Box>
      </Box>
      
      {/* 筛选操作按钮 */}
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2, gap: 1 }}>
        <Button
          variant="outlined"
          size="small"
          startIcon={<RefreshIcon />}
          onClick={handleReset}
        >
          重置筛选
        </Button>
      </Box>
      
      {/* 活跃筛选条件展示 */}
      {(filters.status !== 'ALL' || filters.search) && (
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 2 }}>
          <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
            活跃筛选:
          </Typography>
          
          {filters.search && (
            <Chip 
              size="small" 
              label={`搜索: ${filters.search}`} 
              onDelete={() => handleFilterChange('search', '')}
            />
          )}
          
          {filters.status !== 'ALL' && (
            <Chip 
              size="small" 
              label={`状态: ${statusOptions.find(o => o.value === filters.status)?.label}`} 
              onDelete={() => handleFilterChange('status', 'ALL')}
            />
          )}
        </Box>
      )}
    </Paper>
  );
};

export default AuthorizationFilter; 