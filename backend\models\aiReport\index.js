/**
 * AI报告相关模型定义
 */
const { Model } = require('objection');
const { v4: uuidv4 } = require('uuid');

/**
 * AI报告模型
 */
class AIReport extends Model {
  static get tableName() {
    return 'ai_reports';
  }

  static get idColumn() {
    return 'id';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['disease_id', 'patient_id', 'user_id', 'title', 'content', 'status'],
      properties: {
        id: { type: 'string' },
        disease_id: { type: 'string' },
        patient_id: { type: 'string' },
        user_id: { type: 'string' },
        record_id: { type: ['string', 'null'] },
        title: { type: 'string' },
        template_type: { type: 'string', enum: ['COMPREHENSIVE_ANALYSIS'] },
        anonymized_info: { type: ['object', 'null'] },
        llm_raw_response: { type: ['string', 'null'] },
        content: { type: 'object' },
        pdf_url: { type: ['string', 'null'] },
        status: { type: 'string', enum: ['PROCESSING', 'COMPLETED', 'FAILED'] },
        error_message: { type: ['string', 'null'] },
        is_deleted: { type: 'boolean', default: false },
        created_at: { type: 'string' },
        updated_at: { type: 'string' }
      }
    };
  }

  $beforeInsert() {
    this.id = this.id || uuidv4();
    this.created_at = new Date().toISOString();
    this.updated_at = this.created_at;
    this.status = this.status || 'PROCESSING';
    this.template_type = this.template_type || 'COMPREHENSIVE_ANALYSIS';
    this.is_deleted = this.is_deleted || false;
  }

  $beforeUpdate() {
    this.updated_at = new Date().toISOString();
  }

  // 软删除方法
  softDelete(deletedById) {
    const updateData = {
      is_deleted: true
    };
    
    // 如果提供了删除者ID，记录它
    if (deletedById) {
      updateData.deleted_by = deletedById;
    }
    
    return this.$query().patch(updateData);
  }

  static get relationMappings() {
    const Disease = require('../Disease');
    const Patient = require('../Patient');
    const User = require('../User');
    const Record = require('../Record');

    return {
      disease: {
        relation: Model.BelongsToOneRelation,
        modelClass: Disease,
        join: {
          from: 'ai_reports.disease_id',
          to: 'diseases.id'
        }
      },
      patient: {
        relation: Model.BelongsToOneRelation,
        modelClass: Patient,
        join: {
          from: 'ai_reports.patient_id',
          to: 'patients.id'
        }
      },
      user: {
        relation: Model.BelongsToOneRelation,
        modelClass: User,
        join: {
          from: 'ai_reports.user_id',
          to: 'users.id'
        }
      },
      record: {
        relation: Model.BelongsToOneRelation,
        modelClass: Record,
        join: {
          from: 'ai_reports.record_id',
          to: 'records.id'
        }
      }
    };
  }
}

/**
 * AI报告配置模型
 */
class AIReportConfig extends Model {
  static get tableName() {
    return 'ai_report_configs';
  }

  static get idColumn() {
    return 'id';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      properties: {
        id: { type: 'integer' },
        user_visible_fields: { 
          type: 'array',
          default: ['summary', 'emergencyGuidance', 'hospitalRecommendations', 'lifestyleAndMentalHealth']
        },
        service_visible_fields: { 
          type: 'array',
          default: [
            'summary', 
            'differentialDiagnosis', 
            'emergencyGuidance', 
            'hospitalRecommendations', 
            'treatmentPlan', 
            'budgetEstimation', 
            'crossRegionGuidance', 
            'lifestyleAndMentalHealth', 
            'riskWarnings'
          ] 
        },
        anonymization_rules: { type: ['object', 'null'] },
        llm_prompt: { type: ['string', 'null'] },
        llm_response: { type: ['string', 'null'] },
        quota_config: { 
          type: 'object',
          default: {
            'PERSONAL': 3,
            'FAMILY': 10,
            'PROFESSIONAL': 30
          }
        },
        created_at: { type: 'string' },
        updated_at: { type: 'string' }
      }
    };
  }

  $beforeInsert() {
    this.created_at = new Date().toISOString();
    this.updated_at = this.created_at;
  }

  $beforeUpdate() {
    this.updated_at = new Date().toISOString();
  }
}

/**
 * AI报告配额模型
 */
class AIReportQuota extends Model {
  static get tableName() {
    return 'ai_report_quotas';
  }

  static get idColumn() {
    return 'id';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['user_id'],
      properties: {
        id: { type: 'string' },
        user_id: { type: 'string' },
        monthly_quota: { type: 'integer', default: 3 },
        used_this_month: { type: 'integer', default: 0 },
        total_used: { type: 'integer', default: 0 },
        last_reset_date: { type: 'string' },
        additional_quota: { type: 'integer', default: 0 },
        created_at: { type: 'string' },
        updated_at: { type: 'string' }
      }
    };
  }

  $beforeInsert() {
    this.id = this.id || uuidv4();
    this.created_at = new Date().toISOString();
    this.updated_at = this.created_at;
    this.monthly_quota = parseInt(this.monthly_quota) || 3;
    this.used_this_month = parseInt(this.used_this_month) || 0;
    this.total_used = parseInt(this.total_used) || 0;
    this.additional_quota = parseInt(this.additional_quota) || 0;
    this.last_reset_date = this.last_reset_date || new Date().toISOString();
  }

  $beforeUpdate() {
    this.updated_at = new Date().toISOString();
    if (this.used_this_month !== undefined) {
      this.used_this_month = parseInt(this.used_this_month) || 0;
    }
    if (this.total_used !== undefined) {
      this.total_used = parseInt(this.total_used) || 0;
    }
    if (this.monthly_quota !== undefined) {
      this.monthly_quota = parseInt(this.monthly_quota) || 3;
    }
    if (this.additional_quota !== undefined) {
      this.additional_quota = parseInt(this.additional_quota) || 0;
    }
  }

  static get relationMappings() {
    const User = require('../User');

    return {
      user: {
        relation: Model.BelongsToOneRelation,
        modelClass: User,
        join: {
          from: 'ai_report_quotas.user_id',
          to: 'users.id'
        }
      }
    };
  }
}

module.exports = {
  AIReport,
  AIReportConfig,
  AIReportQuota
}; 