import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import UserManagementPage from './UserManagementPage';
import AuditLogsPage from './AuditLogsPage';
import MaintenancePage from './MaintenancePage';
import AIReportManagementPage from './AIReportManagementPage';
import PatientsManagementPage from './PatientsManagementPage';
import DiseasesManagementPage from './DiseasesManagementPage';
import RecordsManagementPage from './RecordsManagementPage';
import AuthorizationsManagementPage from './AuthorizationsManagementPage';

/**
 * 管理模块路由组件
 * 处理系统管理相关的所有路由
 */
const AdminRoutes: React.FC = () => {
  return (
    <Routes>
      {/* 用户管理路由 */}
      <Route path="users" element={<UserManagementPage />} />
      
      {/* AI报告管理路由 */}
      <Route path="ai-reports" element={<AIReportManagementPage />} />
      
      {/* 患者数据管理 */}
      <Route path="patients" element={<PatientsManagementPage />} />
      
      {/* 病理数据管理 */}
      <Route path="diseases" element={<DiseasesManagementPage />} />
      
      {/* 就诊记录管理 */}
      <Route path="records" element={<RecordsManagementPage />} />
      
      {/* 授权管理 */}
      <Route path="authorizations" element={<AuthorizationsManagementPage />} />
      
      {/* 系统维护 */}
      <Route path="maintenance" element={<MaintenancePage />} />
      
      {/* 审计日志 */}
      <Route path="audit-logs" element={<AuditLogsPage />} />
      
      {/* 默认重定向到用户管理 */}
      <Route path="" element={<Navigate to="users" replace />} />
      
      {/* 404未找到页面 */}
      <Route path="*" element={<div>页面未找到</div>} />
    </Routes>
  );
};

export default AdminRoutes; 