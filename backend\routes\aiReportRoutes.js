/**
 * AI报告路由兼容适配器
 * 将新版API路径映射到旧版路由处理器
 */
const express = require('express');
const router = express.Router();
const aiReportController = require('../controllers/aiReport/aiReportController');
const { authenticate } = require('../src/middleware/auth');

// 确保所有请求都经过身份验证
router.use(authenticate);

// 直接暴露控制器方法，避免中间转发
// 获取指定病理的所有AI报告
router.get('/', aiReportController.getAIReports);

// 检查用户配额
router.get('/quota', aiReportController.checkUserQuota);

// 获取报告显示配置
router.get('/config', aiReportController.getReportConfig);

// 保存报告显示配置
router.post('/config', aiReportController.saveReportConfig);

// 获取单个AI报告详情
router.get('/:reportId', aiReportController.getAIReport);

// 下载AI报告PDF
router.get('/:reportId/pdf', aiReportController.downloadPDF);

// 创建新的AI报告
router.post('/', aiReportController.createAIReport);

// 重新生成报告
router.post('/regenerate', aiReportController.regenerateReport);

// 删除AI报告
router.delete('/:reportId', aiReportController.deleteAIReport);

// 手动修复卡在PROCESSING状态的报告 - 仅限管理员使用
router.post('/fix-stuck-reports', aiReportController.fixStuckReports);

module.exports = router;
