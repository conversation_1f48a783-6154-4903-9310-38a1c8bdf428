/**
 * 创建简单字体文件
 * 使用PDFKit支持的字体格式
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

// 字体目录
const fontDir = path.join(__dirname, '..', 'fonts');

// 确保字体目录存在
if (!fs.existsSync(fontDir)) {
  fs.mkdirSync(fontDir, { recursive: true });
  console.log(`创建字体目录: ${fontDir}`);
}

// 使用已知PDFKit支持的字体
const fontUrls = {
  // 思源黑体 - Adobe开源的中文字体 (单个OTF文件，PDFKit支持)
  'SourceHanSansCN-Regular.otf': 'https://github.com/adobe-fonts/source-han-sans/raw/release/OTF/SimplifiedChinese/SourceHanSansSC-Regular.otf',
  
  // 思源宋体 - Adobe开源的中文字体 (单个OTF文件，PDFKit支持)
  'SourceHanSerifCN-Regular.otf': 'https://github.com/adobe-fonts/source-han-serif/raw/release/OTF/SimplifiedChinese/SourceHanSerifSC-Regular.otf',
  
  // Noto Sans SC - Google开源的中文字体 (单个OTF文件，PDFKit支持)
  'NotoSansSC-Regular.otf': 'https://github.com/googlefonts/noto-cjk/raw/main/Sans/OTF/SimplifiedChinese/NotoSansSC-Regular.otf'
};

/**
 * 下载文件
 * @param {string} url - 下载URL
 * @param {string} dest - 目标文件路径
 * @returns {Promise<void>}
 */
function downloadFile(url, dest) {
  return new Promise((resolve, reject) => {
    console.log(`开始下载: ${url} 到 ${dest}`);
    
    const file = fs.createWriteStream(dest);
    
    https.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`下载失败，状态码: ${response.statusCode}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`下载完成: ${dest}`);
        resolve();
      });
    }).on('error', (err) => {
      fs.unlink(dest, () => {}); // 删除部分下载的文件
      reject(err);
    });
  });
}

/**
 * 主函数
 */
async function main() {
  console.log('=== 创建简单字体文件 ===');
  
  // 下载字体
  for (const [fontName, url] of Object.entries(fontUrls)) {
    const fontPath = path.join(fontDir, fontName);
    
    if (fs.existsSync(fontPath)) {
      console.log(`字体已存在: ${fontPath}`);
      continue;
    }
    
    try {
      await downloadFile(url, fontPath);
      console.log(`成功下载字体: ${fontName}`);
    } catch (err) {
      console.error(`下载字体失败: ${fontName}`, err);
    }
  }
  
  // 列出已安装的字体
  console.log('\n已安装的字体:');
  const installedFonts = fs.readdirSync(fontDir);
  
  if (installedFonts.length === 0) {
    console.log('未找到任何字体！');
  } else {
    installedFonts.forEach(font => console.log(`- ${font}`));
  }
  
  console.log('\n字体安装完成！');
  console.log('PDF生成时将使用这些字体，确保在任何环境下都能正确显示中文。');
}

// 执行主函数
main().catch(err => {
  console.error('脚本执行失败:', err);
  process.exit(1);
});
