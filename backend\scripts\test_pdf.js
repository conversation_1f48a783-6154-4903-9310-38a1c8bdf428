/**
 * PDF生成测试脚本
 * 用于测试PDF中文字体显示
 */

const fs = require('fs');
const path = require('path');
const PDFDocument = require('pdfkit');
const os = require('os');

// 创建输出目录
const outputDir = path.join(__dirname, '..', 'test-pdfs');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// 获取操作系统平台
const platform = os.platform();
console.log(`当前操作系统平台: ${platform}`);

// 定义不同系统的字体路径
let fontPath = '';
let alternateFonts = [];

if (platform === 'win32') {
  // Windows系统
  fontPath = 'C:\\Windows\\Fonts\\simhei.ttf'; // 黑体
  alternateFonts = [
    'C:\\Windows\\Fonts\\NotoSansSC-VF.ttf',  // Noto Sans SC
    'C:\\Windows\\Fonts\\NotoSerifSC-VF.ttf', // Noto Serif SC
    'C:\\Windows\\Fonts\\simkai.ttf',         // 楷体
    'C:\\Windows\\Fonts\\msyh.ttf',           // 微软雅黑
    'C:\\Windows\\Fonts\\simsun.ttc',         // 宋体
    'C:\\Windows\\Fonts\\simfang.ttf'         // 仿宋
  ];
} else if (platform === 'linux') {
  // Linux系统 (如Ubuntu)
  fontPath = '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc'; // 文泉驿微米黑
  alternateFonts = [
    '/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc', // Noto Sans CJK SC
    '/usr/share/fonts/opentype/noto/NotoSerifCJK-Regular.ttc', // Noto Serif CJK SC
    '/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc', // 文泉驿正黑
    '/usr/share/fonts/opentype/noto/NotoSansCJK-Bold.ttc', // Noto Sans CJK SC Bold
    '/usr/share/fonts/opentype/noto/NotoSerifCJK-Bold.ttc' // Noto Serif CJK SC Bold
  ];
} else if (platform === 'darwin') {
  // macOS系统
  fontPath = '/Library/Fonts/Arial Unicode.ttf';
  alternateFonts = [
    '/System/Library/Fonts/PingFang.ttc', // 苹方
    '/Library/Fonts/STHeiti Light.ttc', // 华文黑体
    '/System/Library/Fonts/STHeiti Light.ttc'
  ];
}

// 创建PDF文档
const doc = new PDFDocument({
  margin: 50,
  info: {
    Title: 'PDF中文字体测试',
    Author: '测试脚本',
    Subject: '字体测试',
    Keywords: '字体,测试,PDF',
    CreationDate: new Date()
  }
});

// 创建输出流
const outputPath = path.join(outputDir, 'test.pdf');
const stream = fs.createWriteStream(outputPath);
doc.pipe(stream);

// 添加标题
doc.fontSize(24).text('PDF中文字体测试', { align: 'center' });
doc.moveDown();

// 使用默认字体
doc.fontSize(14).text('默认字体测试:', { continued: false });
doc.fontSize(12).text('这是一段中文测试文本。这段文字用于测试PDF中的中文显示效果。');
doc.moveDown();

// 尝试注册中文字体
let fontLoaded = false;

// 尝试首选字体
if (fs.existsSync(fontPath)) {
  try {
    doc.registerFont('ChineseFont', fontPath);
    doc.font('ChineseFont');
    console.log('成功加载首选中文字体:', fontPath);
    fontLoaded = true;
    
    doc.fontSize(14).text(`首选字体测试 (${path.basename(fontPath)}):`, { continued: false });
    doc.fontSize(12).text('这是一段中文测试文本。这段文字用于测试PDF中的中文显示效果。');
    doc.moveDown();
  } catch (fontErr) {
    console.error('加载首选字体失败:', fontErr);
  }
}

// 尝试替代字体
if (!fontLoaded) {
  console.warn('警告：首选中文字体文件不存在或加载失败:', fontPath);
  
  for (const altFont of alternateFonts) {
    if (fs.existsSync(altFont)) {
      try {
        doc.registerFont('ChineseFont', altFont);
        doc.font('ChineseFont');
        console.log('成功加载替代中文字体:', altFont);
        
        doc.fontSize(14).text(`替代字体测试 (${path.basename(altFont)}):`, { continued: false });
        doc.fontSize(12).text('这是一段中文测试文本。这段文字用于测试PDF中的中文显示效果。');
        doc.moveDown();
        
        fontLoaded = true;
        break;
      } catch (altFontErr) {
        console.error('加载替代字体失败:', altFontErr);
        continue;
      }
    }
  }
}

// 如果无法加载任何字体，使用pdfkit内置字体并记录警告
if (!fontLoaded) {
  console.error('错误：无法找到任何可用的中文字体，PDF中的中文可能显示为乱码');
}

// 添加一些测试内容
doc.font('Helvetica').fontSize(14).text('测试内容:', { continued: false });
doc.moveDown();

// 添加一些常见的中文字符
const chineseChars = [
  '中文字体测试',
  '这是一段中文文本',
  '测试PDF中的中文显示效果',
  '确保中文字符能够正确显示',
  '包括一些特殊字符：！@#￥%……&*（）——+',
  '数字和英文：1234567890 abcdefghijklmnopqrstuvwxyz',
  '医疗相关术语：高血压、糖尿病、心脏病、肺炎、骨折'
];

chineseChars.forEach(text => {
  doc.fontSize(12).text(text);
});

// 完成PDF
doc.end();

console.log(`PDF已保存到: ${outputPath}`);
console.log('请检查PDF文件，确认中文字符是否正确显示');
