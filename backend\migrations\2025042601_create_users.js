exports.up = function (knex) {
  return knex.schema.createTable('users', (table) => {
    table.uuid('id').primary();
    table.string('username').unique().notNullable();
    table.string('email').unique().notNullable();
    table.string('password_hash').notNullable();
    table.string('phone_number').unique();
    table.string('avatar');
    table.string('role').notNullable().index();
    table.string('level').notNullable().index();
    table.boolean('is_active').notNullable().defaultTo(true);
    table.timestamp('last_login_at', { useTz: true }).nullable();
    table.integer('active_disease_limit').notNullable();
    table.integer('ai_usage_count').notNullable();
    table.timestamp('ai_usage_reset_at', { useTz: true }).nullable();
    table.integer('family_member_limit').notNullable();
    table.timestamp('updated_at', { useTz: true }).notNullable();
  });
};
exports.down = function (knex) {
  return knex.schema.dropTable('users');
}; 