import { APP_CONFIG } from './appConfig';

// API版本和基础路径
export const API_VERSION = APP_CONFIG.API.VERSION;
export const API_BASE = APP_CONFIG.API.BASE_PATH;

// API基础URL
export const API_BASE_URL = APP_CONFIG.API.BASE_URL;

// WebSocket基础URL
export const WS_BASE_URL = APP_CONFIG.API.WS_URL;

// API路径配置
export const API_PATHS = {
  // 认证相关
  AUTH: {
    LOGIN: '/api/login',
    REGISTER: '/api/register',
    LOGOUT: '/api/logout',
    REFRESH: '/api/refresh',
    CHANGE_PASSWORD: '/api/user/change-password', // 修正路径，使用/api/user前缀
  },

  // 用户相关
  USER: {
    PROFILE: '/api/users/profile',
    LIMITS: '/api/user/limits',
    SEARCH: '/api/users/search',
    SUBSCRIPTION: '/api/users/subscription',
  },

  // 服务相关
  SERVICE: {
    // 用户搜索
    USERS: '/api/users',
    // 授权管理
    AUTHORIZATION: {
      BASE: '/api/authorizations',
      AS_AUTHORIZER: '/api/authorizations/as-authorizer',
      AS_AUTHORIZED: '/api/authorizations/as-authorized',
      DETAILS: '/api/authorizations/:id',
      STATUS: '/api/authorizations/:id/status',
      PRIVACY: '/api/authorizations/:id/privacy-level',
      REVOKE: '/api/authorizations/:id/revoke'
    },
    // 服务记录
    RECORDS: '/api/service-records', // 修正路径，与后端路由保持一致
    // 服务报告
    REPORTS: '/api/service-reports', // 修正路径，与后端路由保持一致
    // 疾病管理
    DISEASES: '/api/diseases',
  },

  // 患者相关
  PATIENT: {
    BASE: '/api/patients',
    DETAILS: '/api/patients/:id',
    CREATE: '/api/patients',
    UPDATE: '/api/patients/:id',
    DELETE: '/api/patients/:id',
    RECORDS: '/api/patients/records',
    DISEASES: '/api/patients/:id/diseases'
  },

  // 标签相关
  TAG: {
    BASE: '/api/tags',
    DETAILS: '/api/tags/:id',
    CREATE: '/api/tags',
    UPDATE: '/api/tags/:id',
    DELETE: '/api/tags/:id'
  },

  // 通知相关
  NOTIFICATION: {
    BASE: '/api/notifications',
    READ: '/api/notifications/:id/read',
    UNREAD: '/api/notifications/unread',
    CLEAR: '/api/notifications/clear'
  },

  // AI报告相关
  AI_REPORT: {
    BASE: '/api/ai-reports',
    DETAILS: '/api/ai-reports/:id',
    CREATE: '/api/ai-reports',
    UPDATE: '/api/ai-reports/:id',
    DELETE: '/api/ai-reports/:id',
    QUOTA: '/api/ai-reports/quota',
    CONFIG: '/api/ai-reports/config',
  },

  // WebSocket配置
  WEBSOCKET: {
    DEFAULT_PORT: '3005',
    PATH: '/ws'
  },

  // 系统相关
  SYSTEM: {
    HEALTH: '/api/health',
    VERSION: '/api/version',
    CONFIG: '/api/config'
  },

  // 管理相关
  ADMIN: {
    USERS: '/api/admin/users',
    PATIENTS: '/api/admin/patients',
    RECORDS: '/api/admin/records',
    DISEASES: '/api/admin/diseases',
    AI_REPORTS: '/api/admin/ai-reports',
    AUDIT_LOGS: '/api/admin/audit-logs',
  },
};

// 获取完整API路径
export function getFullApiPath(path: string): string {
  if (path.startsWith('/')) {
    return `${API_BASE}${path}`;
  }
  return `${API_BASE}/${path}`;
}

// 获取WebSocket URL
export function getWebSocketUrl(): string {
  try {
    // 使用配置的 WebSocket URL
    const wsUrl = APP_CONFIG.API.WS_URL;
    if (!wsUrl) {
      throw new Error('WebSocket URL未配置');
    }

    // 确保URL格式正确
    if (!wsUrl.startsWith('ws://') && !wsUrl.startsWith('wss://')) {
      throw new Error('WebSocket URL格式不正确');
    }

    // 在生产环境中使用固定的URL
    if (process.env.NODE_ENV === 'production') {
      return 'ws://************:3005/ws';
    }

    // 在开发环境中使用本地URL
    return 'ws://localhost:3005/ws';
  } catch (error) {
    console.error('[API配置] 获取WebSocket URL失败:', error);
    // 返回一个默认的WebSocket URL
    return process.env.NODE_ENV === 'production' 
      ? 'ws://************:3005/ws'
      : 'ws://localhost:3005/ws';
  }
}

// 替换路径参数
export function replacePathParams(path: string, params: Record<string, string>): string {
  let result = path;
  Object.entries(params).forEach(([key, value]) => {
    result = result.replace(`:${key}`, value);
  });
  return result;
}

// 构建查询字符串
export function buildQueryString(params: Record<string, any>): string {
  const queryParams = new URLSearchParams();
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      queryParams.append(key, String(value));
    }
  });
  const queryString = queryParams.toString();
  return queryString ? `?${queryString}` : '';
}

// 获取带时间戳的URL（防止缓存）
export function getUrlWithTimestamp(path: string): string {
  return `${path}${buildQueryString({ _t: Date.now() })}`;
}