import apiClient from './apiClient';
import { API_PATHS } from '../config/apiPaths';
import { logApiError } from '../utils/apiErrorMonitor';
import { ServiceRecord, Disease, ServiceRecordCreateResponse, ApiResponse as ServiceApiResponse } from '../types/serviceTypes';

/**
 * 获取服务用户创建的记录
 */
export const getServiceRecords = async (): Promise<ServiceApiResponse<ServiceRecord[]>> => {
  try {
    const path = API_PATHS.SERVICE.RECORDS;
    console.log(`[serviceRecordService] 获取服务记录，路径: ${path}`);
    
    const response = await apiClient.get(path);
    return response.data;
  } catch (error) {
    console.error('[serviceRecordService] 获取服务记录失败:', error);
    logApiError(error);
    throw error;
  }
};

/**
 * 获取被授权用户的记录列表
 * @param authorizationId - 授权ID
 */
export const getAuthorizedUserRecords = async (authorizationId: string): Promise<ServiceApiResponse<ServiceRecord[]>> => {
  try {
    const path = `${API_PATHS.SERVICE.RECORDS}/authorized/${authorizationId}/records`;
    console.log(`[serviceRecordService] 获取授权用户记录，路径: ${path}`);
    
    const response = await apiClient.get(path);
    return response.data;
  } catch (error) {
    console.error('[serviceRecordService] 获取授权用户记录失败:', error);
    logApiError(error);
    throw error;
  }
};

// 记录创建数据接口
interface ServiceRecordCreateData {
  authorizationId: string;
  patientId?: string;
  diseaseId?: string;
  title: string;
  content: string;
  recordType?: string;
  primaryType?: string;
  recordDate?: string;
  tags?: string[];
  status?: string;
  stagePhase?: string;
  stageNode?: string;
  stageTags?: string;
  isImportant?: boolean;
  customTags?: string;
  severity?: string;
}

/**
 * 创建服务记录
 * @param data - 记录数据
 */
export const createServiceRecord = async (data: Partial<ServiceRecord>): Promise<ServiceApiResponse<ServiceRecord>> => {
  try {
    const path = API_PATHS.SERVICE.RECORDS;
    console.log(`[serviceRecordService] 创建服务记录，路径: ${path}`);
    
    const response = await apiClient.post(path, data);
    return response.data;
  } catch (error) {
    console.error('[serviceRecordService] 创建服务记录失败:', error);
    logApiError(error);
    throw error;
  }
};

// 记录更新数据接口
interface ServiceRecordUpdateData {
  title?: string;
  content?: string;
  recordType?: string;
  primaryType?: string;
  recordDate?: string;
  tags?: string[];
  status?: string;
  stagePhase?: string;
  stageNode?: string;
  stageTags?: string;
  isImportant?: boolean;
  customTags?: string;
  severity?: string;
}

/**
 * 更新服务记录
 * @param id - 记录ID
 * @param data - 更新数据
 */
export const updateServiceRecord = async (id: string, data: Partial<ServiceRecord>): Promise<ServiceApiResponse<ServiceRecord>> => {
  try {
    const path = `${API_PATHS.SERVICE.RECORDS}/${id}`;
    console.log(`[serviceRecordService] 更新服务记录，路径: ${path}`);
    
    const response = await apiClient.put(path, data);
    return response.data;
  } catch (error) {
    console.error('[serviceRecordService] 更新服务记录失败:', error);
    logApiError(error);
    throw error;
  }
};

/**
 * 删除服务记录
 * @param id - 记录ID
 */
export const deleteServiceRecord = async (id: string): Promise<ServiceApiResponse<void>> => {
  try {
    const path = `${API_PATHS.SERVICE.RECORDS}/${id}`;
    console.log(`[serviceRecordService] 删除服务记录，路径: ${path}`);
    
    const response = await apiClient.delete(path);
    return response.data;
  } catch (error) {
    console.error('[serviceRecordService] 删除服务记录失败:', error);
    logApiError(error);
    throw error;
  }
};

// 添加缓存机制
const patientCache: Record<string, {data: any, timestamp: number}> = {};
// 添加失败请求记录机制，用于存储已知会失败的请求标识符
const failedAuthIds = new Set<string>();
// 添加失败的上下文请求记录
const failedContextRequests = new Set<string>();

/**
 * 获取授权下的患者
 * @param authId - 授权ID
 */
export const getAuthorizedPatients = async (authId: string) => {
  try {
    console.log(`[服务记录] 开始获取授权下的患者 - 授权ID: ${authId}`);
    
    // 检查是否为已知失败的授权
    if (failedAuthIds.has(authId)) {
      console.log(`[服务记录] 跳过已知失败的授权ID: ${authId}`);
      return { success: true, data: [] };
    }
    
    // ======== 强制检查授权级别 - 开始 ========
    
    // 1. 从本地存储获取当前服务上下文信息
    const serviceContextStr = localStorage.getItem('serviceUserContext');
    let privacyLevel = null;
    
    if (serviceContextStr) {
      try {
        const serviceContext = JSON.parse(serviceContextStr);
        console.log(`[服务记录] 获取到服务上下文:`, serviceContext);
        
        // 检查两种可能的属性名
        privacyLevel = serviceContext.privacyLevel || serviceContext.privacy_level;
        console.log(`[服务记录] 获取到权限级别: ${privacyLevel}`);
      } catch (e) {
        console.error(`[服务记录] 解析服务上下文失败:`, e);
      }
    } else {
      console.log(`[服务记录] 未找到服务上下文数据`);
    }
    
    // 2. 检查授权表中是否有此授权
    try {
      // 尝试从localStorage中获取授权列表
      const authorizationsStr = localStorage.getItem('authorizedList');
      if (authorizationsStr) {
        const authorizations = JSON.parse(authorizationsStr);
        // 添加类型定义解决隐式any类型问题
        const foundAuth = authorizations.find((auth: {
          id: string;
          privacyLevel?: string;
          privacy_level?: string;
        }) => auth.id === authId);
        
        if (foundAuth) {
          // 可能在上下文中找不到，但在授权列表中有
          privacyLevel = foundAuth.privacyLevel || foundAuth.privacy_level;
          console.log(`[服务记录] 从授权列表找到授权级别: ${privacyLevel}`);
        }
      }
    } catch (e) {
      console.error(`[服务记录] 检查授权列表失败:`, e);
    }
    
    // 3. 如果是基础授权级别或处于安全考虑，默认拒绝
    if (privacyLevel === 'BASIC' || !privacyLevel) {
      console.log(`[服务记录] 基础授权级别或无法确定权限，不发送请求 - 授权ID: ${authId}, 级别: ${privacyLevel || '未知'}`);
      // 添加到已知失败列表，避免重复请求
      failedAuthIds.add(authId);
      return { success: true, data: [] };
    }
    
    // ======== 强制检查授权级别 - 结束 ========
    
    // 检查缓存
    const cacheKey = `patients_${authId}`;
    const cachedData = patientCache[cacheKey];
    if (cachedData && Date.now() - cachedData.timestamp < 5 * 60 * 1000) { // 5分钟缓存
      console.log(`[服务记录] 使用缓存的患者数据 - 授权ID: ${authId}`);
      return cachedData.data;
    }
    
    // 构建请求路径
    const path = `${API_PATHS.SERVICE.RECORDS}/authorized/${authId}/patients`;
    console.log(`[服务记录] 请求路径: ${path}`);
    
    // 发送请求
    const response = await apiClient.get(path);
    console.log(`[服务记录] 获取患者数据成功 - 授权ID: ${authId}`);
    
    // 更新缓存
    patientCache[cacheKey] = {
      data: response.data,
      timestamp: Date.now()
    };
    
    return response.data;
  } catch (error) {
    console.error(`[服务记录] 获取授权下的患者失败 - 授权ID: ${authId}:`, error);
    logApiError(error);
    
    // 添加到已知失败列表
    failedAuthIds.add(authId);
    
    return { success: true, data: [] };
  }
};

/**
 * 获取授权下的疾病
 * @param authorizationId - 授权ID
 * @param patientId - 患者ID
 */
export const getAuthorizedDiseases = async (authorizationId: string, patientId: string): Promise<ServiceApiResponse<Disease[]>> => {
  try {
    const path = `${API_PATHS.SERVICE.RECORDS}/authorized/${authorizationId}/patients/${patientId}/diseases`;
    console.log(`[serviceRecordService] 获取授权下的疾病，路径: ${path}`);
    
    const response = await apiClient.get(path);
    return response.data;
  } catch (error) {
    console.error('[serviceRecordService] 获取授权下的疾病失败:', error);
    logApiError(error);
    throw error;
  }
};

/**
 * 根据授权ID获取服务记录
 * @param authorizationId - 授权ID
 */
export const getServiceRecordsByAuth = async (authorizationId: string): Promise<ServiceApiResponse<ServiceRecord[]>> => {
  try {
    const path = `${API_PATHS.SERVICE.RECORDS}/by-auth/${authorizationId}`;
    console.log(`[serviceRecordService] 根据授权ID获取服务记录，路径: ${path}`);
    
    const response = await apiClient.get(path);
    return response.data;
  } catch (error) {
    console.error('[serviceRecordService] 根据授权ID获取服务记录失败:', error);
    logApiError(error);
    throw error;
  }
};

/**
 * 根据上下文获取服务记录
 * @param params - 上下文参数
 */
export const getServiceRecordsByContext = async (params: {
  authorizationId: string;
  patientId: string;
  diseaseId: string;
}): Promise<ServiceApiResponse<ServiceRecord[]>> => {
  try {
    const path = `${API_PATHS.SERVICE.RECORDS}/context`;
    console.log(`[serviceRecordService] 根据上下文获取服务记录，路径: ${path}`);
    
    const response = await apiClient.get(path, { params });
    return response.data;
  } catch (error) {
    console.error('[serviceRecordService] 根据上下文获取服务记录失败:', error);
    logApiError(error);
    throw error;
  }
};

/**
 * 获取服务记录详情
 * @param id - 记录ID
 */
export const getServiceRecordById = async (id: string): Promise<ServiceApiResponse<ServiceRecord>> => {
  try {
    const path = `${API_PATHS.SERVICE.RECORDS}/${id}`;
    console.log(`[serviceRecordService] 获取服务记录详情，路径: ${path}`);
    
    const response = await apiClient.get(path);
    return response.data;
  } catch (error) {
    console.error('[serviceRecordService] 获取服务记录详情失败:', error);
    logApiError(error);
    throw error;
  }
};

/**
 * 获取服务授权列表
 */
export const getServiceAuthorizations = async (): Promise<ServiceApiResponse<any[]>> => {
  try {
    const path = API_PATHS.SERVICE.AUTHORIZATION.AS_AUTHORIZED;
    console.log(`[serviceRecordService] 获取服务授权列表，路径: ${path}`);
    
    const response = await apiClient.get(path);
    return response.data;
  } catch (error) {
    console.error('[serviceRecordService] 获取服务授权列表失败:', error);
    logApiError(error);
    throw error;
  }
};

/**
 * 上传附件到记录
 * @param recordId - 记录ID
 * @param file - 文件对象
 */
export const uploadAttachment = async (recordId: string, file: File): Promise<ServiceApiResponse<any>> => {
  try {
    console.log(`[serviceRecordService] 开始上传附件 - 记录ID: ${recordId}, 文件: ${file.name}`);
    
    // 检查recordId参数是否存在
    if (!recordId) {
      const errorMsg = '记录ID不能为空';
      console.error(`[serviceRecordService] 上传附件失败 - ${errorMsg}`);
      // 抛出异常而不是返回错误响应，保持与其他API错误处理一致
      throw new Error(errorMsg);
    }
    
    const formData = new FormData();
    
    // 生成带时间戳的文件名，防止同名文件
    const timestamp = new Date().getTime();
    const fileNameParts = file.name.split('.');
    const extension = fileNameParts.pop();
    const nameWithoutExtension = fileNameParts.join('.');
    const newFileName = `${nameWithoutExtension}_${timestamp}.${extension}`;
    
    // 创建一个新的Blob对象，保持内容不变，但重命名文件
    // 对文件名进行编码处理，解决中文名称问题
    const encodedFileName = encodeURIComponent(newFileName);
    console.log(`[serviceRecordService] 原始文件名: ${file.name}, 新文件名: ${newFileName}, 编码后文件名: ${encodedFileName}`);
    
    // 创建新的文件对象，保留类型但更改文件名
    const newFile = new File([file], newFileName, { type: file.type });
    
    // 添加文件到表单
    formData.append('file', newFile);
    formData.append('recordId', recordId);
    formData.append('service_context', 'true'); // 标识为服务上下文
    formData.append('encodedFileName', encodedFileName); // 传递编码后的文件名
    formData.append('originalFileName', newFileName); // 传递带时间戳的文件名
    
    // 使用正确的附件上传API端点
    const path = `/records/attachments/upload`;
    console.log(`[serviceRecordService] 上传附件，路径: ${path}`);
    
    const response = await apiClient.post(path, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    
    return {
      success: true,
      data: response.data
    };
  } catch (error) {
    console.error('[serviceRecordService] 上传附件错误:', error);
    logApiError('上传附件失败', error);
    throw error;
  }
};

/**
 * 获取记录的附件
 * @param recordId - 记录ID
 */
export const getRecordAttachments = async (recordId: string): Promise<ServiceApiResponse<any[]>> => {
  try {
    if (!recordId) {
      throw new Error('记录ID不能为空');
    }
    
    console.log(`[serviceRecordService] 获取记录附件 - 记录ID: ${recordId}`);
    
    // 添加service_context参数，表明这是服务上下文查询
    const path = `/records/attachments/record/${recordId}?service_context=true`;
    
    const response = await apiClient.get(path);
    
    return {
      success: true,
      data: response.data
    };
  } catch (error) {
    console.error('[serviceRecordService] 获取记录附件错误:', error);
    logApiError('获取记录附件失败', error);
    throw error;
  }
};

/**
 * 下载附件
 * @param attachmentId - 附件ID
 * @param fileName - 文件名（可选）
 */
export const downloadAttachment = async (attachmentId: string, fileName?: string): Promise<void> => {
  try {
    console.log(`[serviceRecordService] 开始下载附件 - 附件ID: ${attachmentId}`);
    
    if (!attachmentId) {
      throw new Error('附件ID不能为空');
    }
    
    // 生成下载URL，包含服务上下文标识
    const downloadUrl = `${apiClient.defaults.baseURL}/records/attachments/download/${attachmentId}?service_context=true`;
    
    console.log(`[serviceRecordService] 下载URL: ${downloadUrl}`);
    
    // 创建一个临时链接元素并模拟点击以触发下载
    const link = document.createElement('a');
    link.href = downloadUrl;
    
    // 如果提供了文件名，设置下载文件名
    if (fileName) {
      link.download = fileName;
    }
    
    // 添加到DOM并点击
    document.body.appendChild(link);
    link.click();
    
    // 清理DOM
    setTimeout(() => {
      document.body.removeChild(link);
    }, 100);
  } catch (error) {
    console.error('[serviceRecordService] 下载附件错误:', error);
    logApiError('下载附件失败', error);
    throw error;
  }
};

// 导出所有方法作为默认对象
const serviceRecordService = {
  getServiceRecords,
  getAuthorizedUserRecords,
  createServiceRecord,
  updateServiceRecord,
  deleteServiceRecord,
  getAuthorizedPatients,
  getAuthorizedDiseases,
  getServiceRecordsByAuth,
  getServiceRecordsByContext,
  getServiceRecordById,
  getServiceAuthorizations,
  uploadAttachment,
  getRecordAttachments,
  downloadAttachment
}; 

export default serviceRecordService; 