import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { UserProfile } from '../types/user'; // 导入 UserProfile 类型

// 用户类型定义
export interface User {
  id?: string;
  username?: string;
  email?: string;
  avatar?: string;
  role?: string;
  level?: string;
  phoneNumber?: string;
  createdAt?: string;
  // 添加其他属性...
}

// 认证状态类型定义
interface AuthState {
  token: string | null;
  user: UserProfile | null; // 使用 UserProfile 类型
  // id 和 userLevel 可以从 user 对象中获取，不再单独存储
  isAuthenticated: boolean;
  setToken: (token: string) => void;
  setUser: (user: UserProfile) => void; // 参数使用 UserProfile 类型
  clearToken: () => void;
  clearAuth: () => void; // 清除所有认证信息
  login: (token: string, user: UserProfile) => void; // 参数使用 UserProfile 类型
  logout: () => void;
}

// 添加在初始化时解析token获取用户ID的逻辑
const extractUserIdFromToken = (token: string): string | null => {
  try {
    if (!token) return null;
    
    const base64Url = token.split('.')[1];
    if (!base64Url) return null;
    
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
    
    const payload = JSON.parse(jsonPayload);
    return payload.id || null;
  } catch (e) {
    console.error('解析token失败:', e);
    return null;
  }
};

// 检查本地存储中的token并解析用户ID
const token = localStorage.getItem('token');
const userId = token ? extractUserIdFromToken(token) : null;
if (userId) {
  localStorage.setItem('userId', userId);
  console.log('从token中提取并保存用户ID:', userId);
}

// 设置用户信息的处理函数，确保头像字段正确处理，并保留所有其他字段
const processUserData = (user: UserProfile | null): UserProfile | null => {
  if (!user) return null;
  
  // 创建一个新的用户对象，以避免直接修改状态
  const processedUser = { ...user }; 
  
  // 确保avatar字段存在且类型正确
  if (processedUser.avatar === null || processedUser.avatar === undefined) {
    processedUser.avatar = ''; // 将null或undefined转换为空字符串
  }
  
  // 确保所有用户限制字段都是数字类型，或者保持 undefined
  const fieldsToConvert: (keyof UserProfile)[] = ['maxPatients', 'maxPathologies', 'maxAttachmentSize', 'maxTotalStorage', 'activeDiseaseLimit', 'aiUsageCount', 'familyMemberLimit', 'maxAiUsed'];
  
  fieldsToConvert.forEach(field => {
    if (processedUser[field] !== undefined && processedUser[field] !== null) {
      (processedUser as any)[field] = Number(processedUser[field]);
    }
  });
  
  console.log('[authStore] 用户数据处理结果:', {
    处理前头像: user.avatar,
    处理后头像: processedUser.avatar,
    处理后用户数据: processedUser // 打印完整的处理后用户数据
  });
  
  return processedUser;
};

// 创建认证状态存储
export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      token: localStorage.getItem('token') || null,
      user: null, // 初始化为 null
      isAuthenticated: !!localStorage.getItem('token'),
      
      setToken: (token) => set({ token }),
      
      setUser: (user) => {
        const processedUser = processUserData(user);
        set({ 
          user: processedUser, 
          // id 和 userLevel 可以从 processedUser 中获取，无需单独更新
          // isAuthenticated 状态应在登录/登出时明确设置
        });
      },
      
      clearToken: () => set({ token: null }),
      
      clearAuth: () => {
        localStorage.removeItem('token');
        localStorage.removeItem('userId'); // 确保userId也被清除
        set({ 
          token: null, 
          user: null, 
          isAuthenticated: false
        });
      },
      
      login: (token, user) => {
        localStorage.setItem('token', token);
        
        if (user && user.id) {
          localStorage.setItem('userId', user.id);
        }
        
        const processedUser = processUserData(user);
        
        set({
          token,
          user: processedUser,
          isAuthenticated: true
        });
      },
      
      logout: () => {
        localStorage.removeItem('token');
        localStorage.removeItem('userId');
        set({ 
          token: null, 
          user: null, 
          isAuthenticated: false 
        });
      }
    }),
    {
      name: 'auth-storage', // localStorage的键名
      // 可选：自定义哪些部分的状态被持久化
      // partialize: (state) => ({ token: state.token, user: state.user, isAuthenticated: state.isAuthenticated }),
    }
  )
); 