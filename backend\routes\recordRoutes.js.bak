/**
 * 记录路由管理
 * 提供记录的基本增删改查功能
 */
const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');
const { JWT_SECRET } = require('../src/config');
const knex = require('knex')(require('../knexfile').development);
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// 配置上传存储
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../uploads');
    // 确保目录存在
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // 添加时间戳和UUID到文件名以避免重复
    const uniqueSuffix = Date.now() + '-' + uuidv4();
    cb(null, uniqueSuffix + '-' + file.originalname);
  }
});

const upload = multer({ 
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 } // 限制文件大小为10MB
});

// 身份验证中间件
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers.authorization;
  if (!authHeader) return res.status(401).json({ error: '未授权' });
  
  const token = authHeader.split(' ')[1];
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) return res.status(403).json({ error: '令牌无效' });
    req.user = user;
    next();
  });
};

// 检查并创建records表
const ensureRecordsTable = async () => {
  const hasTable = await knex.schema.hasTable('records');
  if (!hasTable) {
    await knex.schema.createTable('records', table => {
      table.uuid('id').primary();
      table.uuid('user_id').notNullable();
      table.uuid('patient_id').notNullable();
      table.uuid('disease_id').notNullable();
      table.string('title').notNullable();
      table.text('content').nullable();
      table.dateTime('record_date').notNullable();
      table.json('record_type').nullable();
      table.string('primary_type').nullable();
      table.string('stage_phase').nullable();
      table.string('stage_node').nullable();
      table.string('severity').nullable();
      table.boolean('is_important').defaultTo(false);
      table.boolean('is_private').defaultTo(false);
      table.string('custom_tags').nullable();
      table.uuid('created_by').notNullable();
      table.timestamp('created_at').defaultTo(knex.fn.now());
      table.timestamp('updated_at').defaultTo(knex.fn.now());
      table.timestamp('deleted_at').nullable();
      table.json('data').nullable(); // 结构化数据
    });
    console.log('已创建records表');
  }
  
  // 检查附件表
  const hasAttachmentsTable = await knex.schema.hasTable('attachments');
  if (!hasAttachmentsTable) {
    await knex.schema.createTable('attachments', table => {
      table.uuid('id').primary();
      table.uuid('record_id').notNullable();
      table.string('file_name').notNullable();
      table.string('file_path').notNullable();
      table.string('file_type').nullable();
      table.integer('file_size').nullable();
      table.uuid('uploaded_by').notNullable();
      table.timestamp('uploaded_at').defaultTo(knex.fn.now());
    });
    console.log('已创建attachments表');
  }
};

// 创建新记录
router.post('/', authenticateToken, async (req, res) => {
  try {
    await ensureRecordsTable();
    
    const userId = req.user.id;
    const recordData = req.body;
    
    // 验证必填字段
    if (!recordData.patientId || !recordData.diseaseId || !recordData.title) {
      return res.status(400).json({ error: '患者、病理和标题为必填项' });
    }
    
    // 处理记录类型 - 确保它是JSON格式
    let recordType = recordData.recordType || [];
    if (typeof recordType === 'string') {
      try {
        recordType = JSON.parse(recordType);
      } catch (e) {
        // 如果解析失败，可能是逗号分隔的字符串
        recordType = recordType.split(',').map(type => type.trim());
      }
    }
    
    // 创建记录ID
    const recordId = uuidv4();
    
    // 插入记录
    await knex('records').insert({
      id: recordId,
      user_id: userId,
      patient_id: recordData.patientId,
      disease_id: recordData.diseaseId,
      title: recordData.title,
      content: recordData.content,
      record_date: recordData.recordDate,
      record_type: JSON.stringify(recordType),
      primary_type: recordData.primaryType,
      stage_phase: recordData.stagePhase,
      stage_node: recordData.stageNode,
      severity: recordData.severity,
      is_important: recordData.isImportant === true,
      is_private: recordData.isPrivate === true,
      custom_tags: recordData.customTags,
      created_by: userId,
      created_at: knex.fn.now(),
      updated_at: knex.fn.now(),
      data: JSON.stringify(recordData.data || {})
    });
    
    console.log('记录创建成功:', recordId);
    
    // 返回创建的记录
    const record = await knex('records').where('id', recordId).first();
    res.status(201).json(record);
  } catch (error) {
    console.error('创建记录失败:', error);
    res.status(500).json({ error: error.message });
  }
});

// 获取记录列表
router.get('/', authenticateToken, async (req, res) => {
  try {
    await ensureRecordsTable();
    
    const userId = req.user.id;
    const { patientId, diseaseId, isImportant, page = 1, limit = 20 } = req.query;
    
    // 构建查询
    let query = knex('records')
      .where('user_id', userId)
      .whereNull('deleted_at')
      .orderBy('record_date', 'desc');
      
    // 应用筛选条件
    if (patientId) {
      query = query.where('patient_id', patientId);
    }
    
    if (diseaseId) {
      query = query.where('disease_id', diseaseId);
    }
    
    if (isImportant === 'true') {
      query = query.where('is_important', true);
    }
    
    // 分页
    const offset = (page - 1) * limit;
    query = query.offset(offset).limit(limit);
    
    // 执行查询
    const records = await query;
    const total = await knex('records')
      .where('user_id', userId)
      .whereNull('deleted_at')
      .count('id as count')
      .first();
      
    // 解析记录类型
    records.forEach(record => {
      if (record.record_type && typeof record.record_type === 'string') {
        try {
          record.recordType = JSON.parse(record.record_type);
          delete record.record_type; // 删除下划线版本，保留驼峰版本用于前端
        } catch (e) {
          record.recordType = [];
        }
      }
      
      if (record.data && typeof record.data === 'string') {
        try {
          record.data = JSON.parse(record.data);
        } catch (e) {
          record.data = {};
        }
      }
      
      // 转换其他字段为前端使用的驼峰命名
      record.patientId = record.patient_id;
      record.diseaseId = record.disease_id;
      record.userId = record.user_id;
      record.createdBy = record.created_by;
      record.recordDate = record.record_date;
      record.primaryType = record.primary_type;
      record.typeTagsJson = record.type_tags_json;
      record.stageTags = record.stage_tags;
      record.stageNode = record.stage_node;
      record.stagePhase = record.stage_phase;
      record.isPrivate = record.is_private;
      record.isImportant = record.is_important;
      record.customTags = record.custom_tags;
      record.deletedAt = record.deleted_at;
      record.createdAt = record.created_at;
      record.updatedAt = record.updated_at;
      
      // 删除下划线版本
      delete record.patient_id;
      delete record.disease_id;
      delete record.user_id;
      delete record.created_by;
      delete record.record_date;
      delete record.primary_type;
      delete record.type_tags_json;
      delete record.stage_tags;
      delete record.stage_node;
      delete record.stage_phase;
      delete record.is_private;
      delete record.is_important;
      delete record.custom_tags;
      delete record.deleted_at;
      delete record.created_at;
      delete record.updated_at;
    });
    
    res.json({
      records,
      meta: {
        currentPage: parseInt(page),
        totalItems: total ? total.count : 0,
        itemsPerPage: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('获取记录列表失败:', error);
    res.status(500).json({ error: error.message });
  }
});

// 获取单个记录
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    await ensureRecordsTable();
    
    const { id } = req.params;
    const userId = req.user.id;
    
    const record = await knex('records')
      .where('id', id)
      .whereNull('deleted_at')
      .first();
      
    if (!record) {
      return res.status(404).json({ error: '记录不存在' });
    }
    
    // 检查权限 - 只能查看自己的记录
    if (record.user_id !== userId) {
      return res.status(403).json({ error: '无权访问此记录' });
    }
    
    // 解析记录类型和数据
    if (record.record_type && typeof record.record_type === 'string') {
      try {
        record.recordType = JSON.parse(record.record_type);
        delete record.record_type;
      } catch (e) {
        record.recordType = [];
      }
    }
    
    if (record.data && typeof record.data === 'string') {
      try {
        record.data = JSON.parse(record.data);
      } catch (e) {
        record.data = {};
      }
    }
    
    // 获取记录的附件
    const attachments = await knex('attachments')
      .where('record_id', id)
      .select();
      
    // 将附件字段从下划线命名转为驼峰命名
    const formattedAttachments = attachments.map(attachment => {
      return {
        id: attachment.id,
        recordId: attachment.record_id,
        fileName: attachment.file_name,
        filePath: attachment.file_path,
        fileType: attachment.file_type,
        fileSize: attachment.file_size,
        uploadedBy: attachment.uploaded_by,
        uploadedAt: attachment.uploaded_at,
        createdAt: attachment.created_at,
        updatedAt: attachment.updated_at
      };
    });
    
    // 转换其他字段为前端使用的驼峰命名
    const formattedRecord = {
      id: record.id,
      title: record.title,
      content: record.content,
      data: record.data,
      recordDate: record.record_date,
      description: record.description,
      patientId: record.patient_id,
      diseaseId: record.disease_id,
      userId: record.user_id,
      createdBy: record.created_by,
      recordType: record.recordType,
      primaryType: record.primary_type,
      typeTagsJson: record.type_tags_json,
      stageTags: record.stage_tags,
      stageNode: record.stage_node,
      stagePhase: record.stage_phase,
      severity: record.severity,
      isPrivate: record.is_private,
      isImportant: record.is_important,
      customTags: record.custom_tags,
      deletedAt: record.deleted_at,
      createdAt: record.created_at,
      updatedAt: record.updated_at,
      attachments: formattedAttachments
    };
    
    res.json(formattedRecord);
  } catch (error) {
    console.error('获取记录详情失败:', error);
    res.status(500).json({ error: error.message });
  }
});

// 更新记录
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    await ensureRecordsTable();
    
    const { id } = req.params;
    const userId = req.user.id;
    const updateData = req.body;
    
    // 检查记录是否存在
    const record = await knex('records')
      .where('id', id)
      .whereNull('deleted_at')
      .first();
      
    if (!record) {
      return res.status(404).json({ error: '记录不存在' });
    }
    
    // 检查权限 - 只能更新自己的记录
    if (record.user_id !== userId && record.created_by !== userId) {
      return res.status(403).json({ error: '无权更新此记录' });
    }
    
    // 处理记录类型
    let recordType = updateData.recordType || record.record_type;
    if (typeof recordType === 'string') {
      try {
        recordType = JSON.parse(recordType);
      } catch (e) {
        // 如果解析失败，可能是逗号分隔的字符串
        recordType = recordType.split(',').map(type => type.trim());
      }
    } else if (Array.isArray(recordType)) {
      // 已经是数组，不需要处理
    } else {
      recordType = [];
    }
    
    // 更新记录
    await knex('records')
      .where('id', id)
      .update({
        title: updateData.title || record.title,
        content: updateData.content !== undefined ? updateData.content : record.content,
        record_date: updateData.recordDate || record.record_date,
        record_type: JSON.stringify(recordType),
        primary_type: updateData.primaryType || record.primary_type,
        stage_phase: updateData.stagePhase || record.stage_phase,
        stage_node: updateData.stageNode || record.stage_node,
        severity: updateData.severity || record.severity,
        is_important: updateData.isImportant !== undefined ? updateData.isImportant : record.is_important,
        is_private: updateData.isPrivate !== undefined ? updateData.isPrivate : record.is_private,
        custom_tags: updateData.customTags !== undefined ? updateData.customTags : record.custom_tags,
        updated_at: knex.fn.now(),
        data: updateData.data ? JSON.stringify(updateData.data) : record.data
      });
    
    console.log('记录更新成功:', id);
    
    // 返回更新后的记录
    const updatedRecord = await knex('records').where('id', id).first();
    
    // 转换为驼峰命名返回给前端
    const formattedRecord = {
      id: updatedRecord.id,
      title: updatedRecord.title,
      content: updatedRecord.content,
      recordDate: updatedRecord.record_date,
      description: updatedRecord.description,
      patientId: updatedRecord.patient_id,
      diseaseId: updatedRecord.disease_id,
      userId: updatedRecord.user_id,
      createdBy: updatedRecord.created_by,
      severity: updatedRecord.severity,
      isPrivate: updatedRecord.is_private,
      isImportant: updatedRecord.is_important,
      customTags: updatedRecord.custom_tags,
      deletedAt: updatedRecord.deleted_at,
      createdAt: updatedRecord.created_at,
      updatedAt: updatedRecord.updated_at
    };
    
    // 解析记录类型
    if (updatedRecord.record_type) {
      try {
        formattedRecord.recordType = JSON.parse(updatedRecord.record_type);
      } catch (e) {
        formattedRecord.recordType = [];
      }
    }
    
    // 解析数据字段
    if (updatedRecord.data) {
      try {
        formattedRecord.data = JSON.parse(updatedRecord.data);
      } catch (e) {
        formattedRecord.data = {};
      }
    }
    
    res.json(formattedRecord);
  } catch (error) {
    console.error('更新记录失败:', error);
    res.status(500).json({ error: error.message });
  }
});

// 删除记录 (软删除)
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    await ensureRecordsTable();
    
    const { id } = req.params;
    const userId = req.user.id;
    
    // 检查记录是否存在
    const record = await knex('records')
      .where('id', id)
      .whereNull('deleted_at')
      .first();
      
    if (!record) {
      return res.status(404).json({ error: '记录不存在' });
    }
    
    // 检查权限 - 只能删除自己的记录
    if (record.user_id !== userId && record.created_by !== userId) {
      return res.status(403).json({ error: '无权删除此记录' });
    }
    
    // 软删除 - 设置deleted_at
    await knex('records')
      .where('id', id)
      .update({
        deleted_at: knex.fn.now()
      });
    
    console.log('记录删除成功:', id);
    res.json({ message: '记录已删除' });
  } catch (error) {
    console.error('删除记录失败:', error);
    res.status(500).json({ error: error.message });
  }
});

// 上传附件
router.post('/attachments/upload', authenticateToken, upload.single('file'), async (req, res) => {
  try {
    await ensureRecordsTable();
    
    if (!req.file) {
      return res.status(400).json({ error: '未找到上传的文件' });
    }
    
    const { recordId } = req.body;
    const userId = req.user.id;
    
    // 检查记录是否存在
    const record = await knex('records')
      .where('id', recordId)
      .whereNull('deleted_at')
      .first();
      
    if (!record) {
      // 删除已上传的文件
      fs.unlinkSync(req.file.path);
      return res.status(404).json({ error: '记录不存在' });
    }
    
    // 检查权限
    if (record.user_id !== userId && record.created_by !== userId) {
      // 删除已上传的文件
      fs.unlinkSync(req.file.path);
      return res.status(403).json({ error: '无权为此记录上传附件' });
    }
    
    // 保存附件信息
    const attachmentId = uuidv4();
    await knex('attachments').insert({
      id: attachmentId,
      record_id: recordId,
      file_name: req.file.originalname,
      file_path: req.file.path,
      file_type: req.file.mimetype,
      file_size: req.file.size,
      uploaded_by: userId,
      uploaded_at: knex.fn.now()
    });
    
    console.log('附件上传成功:', attachmentId);
    
    // 返回附件信息
    const attachment = await knex('attachments').where('id', attachmentId).first();
    res.status(201).json(attachment);
  } catch (error) {
    console.error('上传附件失败:', error);
    
    // 如果上传过程中出错，删除已上传的文件
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    
    res.status(500).json({ error: error.message });
  }
});

// 获取记录的附件
router.get('/attachments/record/:recordId', authenticateToken, async (req, res) => {
  try {
    await ensureRecordsTable();
    
    const { recordId } = req.params;
    const userId = req.user.id;
    
    // 检查记录是否存在
    const record = await knex('records')
      .where('id', recordId)
      .whereNull('deleted_at')
      .first();
      
    if (!record) {
      return res.status(404).json({ error: '记录不存在' });
    }
    
    // 检查权限
    if (record.user_id !== userId && record.created_by !== userId) {
      return res.status(403).json({ error: '无权访问此记录的附件' });
    }
    
    // 获取附件列表
    const attachments = await knex('attachments')
      .where('record_id', recordId)
      .select();
      
    res.json(attachments);
  } catch (error) {
    console.error('获取附件列表失败:', error);
    res.status(500).json({ error: error.message });
  }
});

// 删除附件
router.delete('/attachments/:id', authenticateToken, async (req, res) => {
  try {
    await ensureRecordsTable();
    
    const { id } = req.params;
    const userId = req.user.id;
    
    // 获取附件信息
    const attachment = await knex('attachments')
      .where('id', id)
      .first();
      
    if (!attachment) {
      return res.status(404).json({ error: '附件不存在' });
    }
    
    // 检查记录权限
    const record = await knex('records')
      .where('id', attachment.record_id)
      .whereNull('deleted_at')
      .first();
      
    if (!record) {
      return res.status(404).json({ error: '关联的记录不存在' });
    }
    
    // 检查权限
    if (record.user_id !== userId && record.created_by !== userId && attachment.uploaded_by !== userId) {
      return res.status(403).json({ error: '无权删除此附件' });
    }
    
    // 删除文件
    if (fs.existsSync(attachment.file_path)) {
      fs.unlinkSync(attachment.file_path);
    }
    
    // 从数据库中删除附件记录
    await knex('attachments').where('id', id).delete();
    
    console.log('附件删除成功:', id);
    res.json({ message: '附件已删除' });
  } catch (error) {
    console.error('删除附件失败:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router; 