import React, { createContext, useContext, useState, /*useEffect,*/ ReactNode, useCallback } from 'react';

// 服务用户上下文接口
interface ServiceContextType {
  // 基本关系信息
  authorizationId: string | null;
  patientId: string | null;
  diseaseId: string | null;
  ownerUserId: string | null;
  
  // 权限和状态信息
  privacyLevel: 'BASIC' | 'STANDARD' | 'FULL' | null;
  status: string | null;
  
  // 附加信息
  patientName: string | null;
  diseaseName: string | null;
  
  // 设置方法
  setAuthorization: (authId: string | null, ownerUserId?: string | null, privacyLevel?: 'BASIC' | 'STANDARD' | 'FULL' | null, status?: string | null) => void;
  setPatient: (patientId: string | null, patientName?: string | null) => void;
  setDisease: (diseaseId: string | null, diseaseName?: string | null) => void;
  clearContext: () => void;
  // 兼容旧版API，设置任意组合的上下文字段
  setContext: (context: Partial<ServiceContextType>) => void;
}

// 默认值
const defaultContext: ServiceContextType = {
  authorizationId: null,
  patientId: null,
  diseaseId: null,
  ownerUserId: null,
  privacyLevel: null,
  status: null,
  patientName: null,
  diseaseName: null,
  setAuthorization: () => {},
  setPatient: () => {},
  setDisease: () => {},
  clearContext: () => {},
  setContext: () => {}
};

// 创建上下文
const ServiceUserContext = createContext<ServiceContextType | undefined>(undefined);

// 提供者Props接口
interface ServiceUserProviderProps {
  children: ReactNode;
}

// 上下文提供者组件
export const ServiceUserProvider: React.FC<ServiceUserProviderProps> = ({ children }) => {
  // 存储在localStorage中的键名
  const STORAGE_KEY = 'serviceUserContext';
  
  // 初始化状态，优先从localStorage读取
  const getInitialState = (): ServiceContextType => {
    try {
      const storedContext = localStorage.getItem(STORAGE_KEY);
      if (storedContext) {
        const parsedContext = JSON.parse(storedContext);
        return { ...defaultContext, ...parsedContext };
      }
    } catch (error) {
      console.error('[ServiceUserContext] 读取服务用户上下文失败:', error);
    }
    return defaultContext;
  };
  
  const [context, setContextState] = useState<ServiceContextType>(getInitialState);
  
  // 保存上下文到localStorage的辅助函数
  const saveToStorage = useCallback((updatedContext: Partial<ServiceContextType>) => {
    try {
      // 排除方法字段
      const { setContext, setAuthorization, setPatient, setDisease, clearContext, ...contextToStore } = {
        ...context,
        ...updatedContext
      };
        localStorage.setItem(STORAGE_KEY, JSON.stringify(contextToStore));
      // 减少日志输出
      // console.log('[ServiceUserContext] 上下文已保存到localStorage:', contextToStore);
      } catch (error) {
      console.error('[ServiceUserContext] 保存服务用户上下文失败:', error);
      }
  }, [context]);
  
  // 设置授权信息
  const setAuthorization = useCallback((authId: string | null, ownerUserId?: string | null, privacyLevel?: 'BASIC' | 'STANDARD' | 'FULL' | null, status?: string | null) => {
    // 减少日志输出
    // console.log('[ServiceUserContext] 设置授权信息:', { authId, ownerUserId, privacyLevel, status });
    
    const update: Partial<ServiceContextType> = { authorizationId: authId };
    
    if (ownerUserId !== undefined) update.ownerUserId = ownerUserId;
    if (privacyLevel !== undefined) update.privacyLevel = privacyLevel;
    if (status !== undefined) update.status = status;
    
    setContextState(prev => {
      const updatedContext = { ...prev, ...update };
      saveToStorage(updatedContext);
      return updatedContext;
    });
  }, [saveToStorage]);
  
  // 设置患者信息
  const setPatient = useCallback((patientId: string | null, patientName?: string | null) => {
    // 减少日志输出
    // console.log('[ServiceUserContext] 设置患者信息:', { patientId, patientName });
    
    // 如果患者ID变更，清除病理选择
    const clearDisease = patientId !== context.patientId;
    
    const update: Partial<ServiceContextType> = { 
      patientId,
      ...(patientName !== undefined ? { patientName } : {})
    };
    
    // 如果患者变更，清除病理选择
    if (clearDisease) {
      update.diseaseId = null;
      update.diseaseName = null;
    }
    
    setContextState(prev => {
      const updatedContext = { ...prev, ...update };
      saveToStorage(updatedContext);
      return updatedContext;
    });
  }, [context.patientId, saveToStorage]);
  
  // 设置病理信息
  const setDisease = useCallback((diseaseId: string | null, diseaseName?: string | null) => {
    // 安全检查 - 不允许在没有患者的情况下设置一个非空的病理ID
    if (!context.patientId && diseaseId) {
      console.error('[ServiceUserContext] 错误：尝试在没有患者ID的情况下设置病理ID，操作被取消');
      return;
    }
    
    // 减少日志输出
    // console.log('[ServiceUserContext] 设置病理信息:', { diseaseId, diseaseName });
    
    const update: Partial<ServiceContextType> = { 
      diseaseId,
      ...(diseaseName !== undefined ? { diseaseName } : {})
    };
    
    setContextState(prev => {
      const updatedContext = { ...prev, ...update };
      saveToStorage(updatedContext);
      return updatedContext;
    });
  }, [context.patientId, saveToStorage]);
  
  // 兼容旧版API - 直接设置上下文字段 (移到 clearContext 之前)
  const setContext = useCallback((newContext: Partial<ServiceContextType>) => {
    setContextState(prevContext => {
      const updatedContext = { ...prevContext, ...newContext };
      saveToStorage(updatedContext);
      return updatedContext;
    });
  }, [saveToStorage]);
  
  // 清除上下文
  const clearContext = useCallback(() => {
    // 减少日志输出
    // console.log('[ServiceUserContext] 开始清除上下文状态...');

    // 先从本地存储中移除
    try {
    localStorage.removeItem(STORAGE_KEY);
      
      // 清除所有相关存储项
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('service') || key.includes('Context') || key.includes('Disease')) {
          // 减少日志输出
          // console.log(`[ServiceUserContext] 清除相关存储项: ${key}`);
          localStorage.removeItem(key);
        }
      });
      
      // 减少日志输出
      // console.log('[ServiceUserContext] 已从localStorage清除上下文数据及相关状态');
    } catch (error) {
      console.error('[ServiceUserContext] 清除localStorage时出错:', error);
    }
    
    // 然后更新状态
    const resetContext = {
      ...defaultContext,
      setContext,
      setAuthorization,
      setPatient,
      setDisease,
      clearContext
    };
    
    setContextState(resetContext);
    // 减少日志输出
    // console.log('[ServiceUserContext] 上下文状态已重置为默认值');
    
    // 触发全局事件以通知其他组件上下文已重置
    window.dispatchEvent(new CustomEvent('serviceContextReset'));
    
    // 刷新页面以确保UI完全重置
    window.location.reload();
  }, [setContext, setAuthorization, setPatient, setDisease]);
  
  // 提供带有方法的完整上下文
  const contextValue: ServiceContextType = {
    ...context,
    setContext,
    setAuthorization,
    setPatient,
    setDisease,
    clearContext
  };
  
  return (
    <ServiceUserContext.Provider value={contextValue}>
      {children}
    </ServiceUserContext.Provider>
  );
};

// 自定义钩子，用于在组件中使用上下文
export const useServiceUserContext = (): ServiceContextType => {
  const context = useContext(ServiceUserContext);
  if (context === undefined) {
    throw new Error('useServiceUserContext必须在ServiceUserProvider内部使用');
  }
  return context;
}; 