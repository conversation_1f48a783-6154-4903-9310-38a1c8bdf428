import apiClient from './apiClient';
import { authOnlyClient } from './authApiClient';
import { useAuthStore } from '../store/authStore';
import { RegisterRequest, UserProfile } from '../types/user';
import { API_PATHS } from '../config/apiPaths';
import { logApiError } from '../utils/apiErrorMonitor';

// 添加登录响应接口
export interface LoginResponse {
  token: string;
  user: any;
  redirectTo?: string;
}

// 登录服务：发送用户名和密码到服务器并获取token
export const login = async (username: string, password: string): Promise<LoginResponse> => {
  try {
    console.log('[authService] 开始登录请求...');

    // 获取登录API路径
    const loginPath = API_PATHS.AUTH.LOGIN;
    console.log(`[authService] 使用登录路径: ${loginPath}`);

    // 使用authOnlyClient，因为登录不需要/api前缀
    const response = await authOnlyClient.post(loginPath, { username, password });
    const { token, user } = response.data;

    console.log('[authService] 登录响应:', {
      tokenReceived: !!token,
      userReceived: !!user,
      userData: user ? `ID:${user.id}, 用户名:${user.username}, 角色:${user.role}` : 'undefined'
    });

    if (token) {
      // 从token中提取用户ID
      try {
        const tokenParts = token.split('.');
        if (tokenParts.length === 3) {
          const payload = JSON.parse(atob(tokenParts[1]));
          if (payload.id) {
            console.log('[authService] 从token中提取的用户ID:', payload.id);
            localStorage.setItem('userId', payload.id);
          }
        }
      } catch (e) {
        console.error('[authService] 无法从token中提取用户ID:', e);
      }

      // 在localStorage和Zustand store中都保存token
      localStorage.setItem('token', token);
      console.log('[authService] Token已保存到localStorage');

      // 设置token和用户信息
      useAuthStore.getState().login(token, user || {});
      console.log('[authService] 令牌已保存到Zustand store');

      // 如果没有收到用户数据，尝试获取用户信息
      if (!user) {
        console.log('[authService] 未收到用户数据，尝试获取用户信息...');
        try {
          await fetchUserProfile();
        } catch (profileError) {
          console.error('[authService] 获取用户信息失败，但登录已成功:', profileError);

          // 抛出特殊错误对象，包含登录成功但获取资料失败的标记
          const loginSuccessProfileError: any = new Error('登录成功但获取用户资料失败');
          loginSuccessProfileError.isLoginSuccessProfileFailed = true;
          loginSuccessProfileError.token = token;
          throw loginSuccessProfileError;
        }
      }

      // 检查是否需要重定向到之前保存的页面
      const redirectPath = localStorage.getItem('redirectAfterLogin');
      if (redirectPath) {
        console.log('[authService] 登录成功，将重定向到之前的页面:', redirectPath);
        localStorage.removeItem('redirectAfterLogin'); // 清除重定向记录
        return { token, user, redirectTo: redirectPath };
      }
    } else {
      console.error('[authService] 登录响应中没有token');
    }

    return { token, user };
  } catch (error) {
    // 如果是我们自定义的特殊错误（登录成功但获取资料失败），则继续抛出
    if ((error as any).isLoginSuccessProfileFailed) {
      throw error;
    }

    // 记录API错误
    logApiError(error);
    console.error('[authService] 登录失败:', error);
    throw error;
  }
};

// 获取用户信息
const getUserInfo = async (queryParams: string = ''): Promise<any> => {
  console.log(`[authService] 开始获取用户信息`);

  // 获取用户资料路径
  const profilePath = API_PATHS.USER.PROFILE + queryParams;
  console.log(`[authService] 获取用户信息，路径: ${profilePath}`);

  try {
    // 使用apiClient发送请求
    const response = await apiClient.get(profilePath);

    // 检查响应中的头像字段
    if (response.data) {
      console.log(`[authService] 成功获取用户信息，检查头像字段:`, {
        avatar: response.data.avatar,
        avatar类型: typeof response.data.avatar,
        avatar是否为null: response.data.avatar === null,
        avatar是否为undefined: response.data.avatar === undefined,
        avatar长度: response.data.avatar !== null && response.data.avatar !== undefined ?
          response.data.avatar.length : '不适用'
      });

      // 确保avatar字段一定存在且类型正确
      if (response.data.avatar === null) {
        response.data.avatar = ''; // 将null转换为空字符串
      }
    }

    return response.data;
  } catch (error: any) {
    console.error(`[authService] 获取用户信息失败:`, {
      错误消息: error.message,
      状态码: error.response?.status
    });

    // 记录API错误
    logApiError(error);
    throw error;
  }
};

// 跟踪上次API调用时间，避免短时间内频繁调用
let lastProfileFetchTime = 0;
const FETCH_COOLDOWN = 2000; // 2秒内不重复获取用户信息

// 获取用户信息并保存到store
export const fetchUserProfile = async () => {
  try {
    // 检查是否在冷却时间内，如果是则直接使用store中的数据
    const now = Date.now();
    if (now - lastProfileFetchTime < FETCH_COOLDOWN) {
      const userData = useAuthStore.getState().user;
      if (userData && userData.id) {
        console.log('[authService] 使用缓存的用户信息，跳过API调用');
        return userData;
      }
    }

    console.log('[authService] 开始获取用户信息...');

    // 添加时间戳参数避免缓存
    const timestamp = new Date().getTime();
    const queryParams = `?_t=${timestamp}`;

    // 记录当前token信息（部分打码）
    const token = localStorage.getItem('token');
    if (token) {
      const tokenPrefix = token.substring(0, 10) + '...';
      console.log(`[authService] 当前token: ${tokenPrefix}`);

      // 检查token格式
      const tokenParts = token.split('.');
      const isValidJWT = tokenParts.length === 3;
      if (!isValidJWT) {
        console.warn('[authService] 当前token不是有效的JWT格式');
      }

      // 如果是有效JWT，尝试解析并记录有效期
      if (isValidJWT) {
        try {
          const payload = JSON.parse(atob(tokenParts[1]));
          const currentTime = Math.floor(Date.now() / 1000);
          console.log('[authService] Token信息:', {
            userId: payload.id,
            exp: payload.exp,
            当前时间: currentTime,
            过期剩余秒数: payload.exp - currentTime,
            是否已过期: payload.exp < currentTime
          });
        } catch (e) {
          console.error('[authService] 无法解析token内容:', e);
        }
      }
    } else {
      console.warn('[authService] 未找到token');
    }

    // 获取用户信息
    const userData = await getUserInfo(queryParams);

    if (userData) {
      console.log('[authService] 获取到的完整用户数据:', JSON.stringify(userData, null, 2));

      // 记录用户权益数据类型
      console.log('[authService] 用户权益数据类型:', {
        maxPatients: typeof userData.maxPatients,
        maxPathologies: typeof userData.maxPathologies,
        maxAttachmentSize: typeof userData.maxAttachmentSize,
        maxTotalStorage: typeof userData.maxTotalStorage
      });

      // 确保所有用户限制数据都是数字类型
      if (userData.maxPatients !== undefined && userData.maxPatients !== null) {
        userData.maxPatients = Number(userData.maxPatients);
      }

      if (userData.maxPathologies !== undefined && userData.maxPathologies !== null) {
        userData.maxPathologies = Number(userData.maxPathologies);
      }

      if (userData.maxAttachmentSize !== undefined && userData.maxAttachmentSize !== null) {
        userData.maxAttachmentSize = Number(userData.maxAttachmentSize);
      }

      if (userData.maxTotalStorage !== undefined && userData.maxTotalStorage !== null) {
        userData.maxTotalStorage = Number(userData.maxTotalStorage);
      }

      // 更新用户状态
      useAuthStore.getState().setUser(userData);

      // 更新上次获取时间
      lastProfileFetchTime = now;

      return userData;
    } else {
      console.error('[authService] 获取到的用户数据为空');
      throw new Error('无法获取用户信息');
    }
  } catch (error) {
    console.error('[authService] 获取用户信息失败:', error);
    logApiError(error);
    throw error;
  }
};

// 注册新用户
export const register = async (userData: RegisterRequest) => {
  try {
    console.log('[authService] 开始注册新用户...');
    const registerPath = API_PATHS.AUTH.REGISTER;

    // 创建一个新对象，进行字段名转换，解决命名规范不一致问题
    const backendData = {
      username: userData.username,
      email: userData.email,
      password: userData.password,
      role: userData.role,
      // 前端用驼峰命名，后端用下划线命名，进行转换
      phone_number: userData.phoneNumber
    };

    console.log('[authService] 转换后的注册数据:', {
      ...backendData,
      password: '***隐藏***'
    });

    const response = await authOnlyClient.post(registerPath, backendData);
    return response.data;
  } catch (error) {
    console.error('[authService] 注册失败:', error);
    logApiError(error);
    throw error;
  }
};

// 登出
export const logout = () => {
  console.log('[authService] 用户登出');
  useAuthStore.getState().clearAuth();
  localStorage.removeItem('userId');
};

// 获取用户资料
export const getUserProfile = async () => {
  try {
    const userData = await fetchUserProfile();

    // 确保返回的用户数据中avatar字段处理正确
    if (userData) {
      console.log('[authService] getUserProfile返回用户数据:', {
        ...userData,
        avatar: userData.avatar,
        avatar类型: typeof userData.avatar,
        avatar是否为null: userData.avatar === null
      });

      // 确保avatar字段存在且类型正确
      if (userData.avatar === null) {
        userData.avatar = ''; // 将null转换为空字符串
      }
    }

    return userData;
  } catch (error) {
    console.error('[authService] getUserProfile错误:', error);
    throw error;
  }
};

// 辅助函数：更新本地用户数据
const updateLocalUserData = async (responseData: any, data: any) => {
  if (!responseData) return;

  console.log('[authService] 用户资料更新成功, 收到响应:', {
    ...responseData,
    user: responseData.user ? {
      ...responseData.user,
      avatar: responseData.user.avatar,
      avatar类型: typeof responseData.user.avatar,
      avatar长度: responseData.user.avatar !== undefined ?
        (responseData.user.avatar !== null ? responseData.user.avatar.length : 'null') :
        'undefined'
    } : 'undefined'
  });

  // 更新本地用户对象的相关字段
  const currentUser = useAuthStore.getState().user;
  if (currentUser) {
    const updatedUser = {
      ...currentUser,
      email: data.email || currentUser.email,
      phoneNumber: data.phoneNumber || currentUser.phoneNumber,
      // 直接使用响应中的avatar值，确保与后端保持一致
      avatar: responseData.user?.avatar
    };
    useAuthStore.getState().setUser(updatedUser);
    console.log('[authService] 本地用户数据已更新:', {
      ...updatedUser,
      avatar: updatedUser.avatar,
      avatar类型: typeof updatedUser.avatar,
      avatar长度: updatedUser.avatar !== undefined ?
        (updatedUser.avatar !== null ? updatedUser.avatar.length : 'null') :
        'undefined'
    });
  }

  // 立即重新获取用户资料以确保更新成功
  try {
    const refreshedProfile = await fetchUserProfile();
    console.log('[authService] 重新获取的用户资料:', {
      ...refreshedProfile,
      avatar: refreshedProfile.avatar,
      avatar类型: typeof refreshedProfile.avatar,
      avatar长度: refreshedProfile.avatar !== undefined ?
        (refreshedProfile.avatar !== null ? refreshedProfile.avatar.length : 'null') :
        'undefined'
    });
  } catch (profileError) {
    console.warn('[authService] 更新后重新获取用户资料失败，但不影响更新结果:', profileError);
  }
};

// 更新用户资料
export const updateUserProfile = async (data: {
  email?: string;
  phoneNumber?: string;
  avatar?: string;
}) => {
  try {
    console.log('[authService] 开始更新用户资料...');

    // 获取当前用户ID
    const userId = localStorage.getItem('userId');
    if (!userId) {
      throw new Error('[authService] 无法更新资料：未找到用户ID');
    }

    // 构建API路径 - 使用正确的API路径
    const updatePath = `/user/profile`;
    console.log(`[authService] 使用更新资料路径: ${updatePath}`);

    // 调试：记录原始数据和类型信息
    console.log('[authService] 前端原始数据:', {
      ...data,
      avatar: data.avatar,
      avatar类型: typeof data.avatar,
      avatar长度: data.avatar !== undefined ? data.avatar.length : 'undefined'
    });

    // 创建安全的头像URL - 解决CORS和特殊字符问题
    let safeAvatarUrl = data.avatar;

    // 如果URL存在且不为空字符串，尝试进行编码和清理
    if (safeAvatarUrl && safeAvatarUrl.trim() !== '') {
      try {
        // 检查是否为有效URL
        new URL(safeAvatarUrl);

        // 如果是外部URL，确保使用https
        if (safeAvatarUrl.startsWith('http:')) {
          safeAvatarUrl = safeAvatarUrl.replace('http:', 'https:');
          console.log('[authService] 将HTTP转换为HTTPS:', safeAvatarUrl);
        }

        // 特殊字符转义处理
        safeAvatarUrl = encodeURI(decodeURI(safeAvatarUrl));
      } catch (urlError) {
        console.warn('[authService] 无效的头像URL:', urlError);
        // 如果URL无效，使用空字符串
        safeAvatarUrl = '';
      }
    }

    // 转换字段名以符合后端API要求（从驼峰命名转为下划线命名）
    const postData = {
      email: data.email,
      phone_number: data.phoneNumber,
      // 使用处理后的安全URL
      avatar: safeAvatarUrl
    };

    console.log('[authService] 发送用户资料更新请求:', {
      ...postData,
      avatar: postData.avatar,
      avatar类型: typeof postData.avatar,
      avatar长度: postData.avatar !== undefined ? postData.avatar.length : 'undefined'
    });

    // 使用环境变量中的API URL，生产环境使用HTTPS
    const baseApiUrl = process.env.REACT_APP_API_URL || (process.env.NODE_ENV === 'production' ? 'https://hkb.life' : 'http://localhost:3001');

    // 获取当前token
    const token = localStorage.getItem('token');
    if (!token) {
      console.error('[authService] 无法更新资料：未找到用户令牌');
      throw new Error('无法更新资料：未找到用户令牌');
    }

    // 准备请求头和请求体
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
      'Accept': 'application/json',
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache'
    };

    const requestBody = JSON.stringify(postData);

    // 尝试使用带/api前缀的路径
    let fullApiUrl = `${baseApiUrl}/api/user/profile`;
    console.log('[authService] 尝试更新用户资料，URL:', fullApiUrl);

    try {
      // 首先尝试带/api前缀的路径
      let response = await fetch(fullApiUrl, {
        method: 'PUT',
        headers,
        body: requestBody
      });

      // 检查响应类型和状态
      if (!response.ok) {
        console.warn(`[authService] 带/api前缀的更新请求失败: ${response.status}，尝试不带前缀的路径`);
        throw new Error(`请求失败: ${response.status}`);
      }

      // 如果成功，解析JSON
      const responseData = await response.json();
      console.log('[authService] 更新用户资料成功:', responseData);

      // 处理成功响应
      // 重要修复：保存avatar到localStorage，作为本地备份
      if (responseData && responseData.user && responseData.user.avatar !== undefined) {
        localStorage.setItem('userAvatar', responseData.user.avatar || '');
        console.log('[authService] 已将头像URL保存到localStorage:', responseData.user.avatar);
      }

      // 更新本地用户数据
      await updateLocalUserData(responseData, data);

      return responseData;

    } catch (prefixError) {
      // 如果带前缀的请求失败，尝试不带前缀的路径
      console.log('[authService] 带前缀的更新请求失败，尝试不带前缀的路径:', prefixError);

      // 使用不带/api前缀的路径
      fullApiUrl = `${baseApiUrl}/user/profile`;
      console.log('[authService] 尝试备用更新路径:', fullApiUrl);

      const response = await fetch(fullApiUrl, {
        method: 'PUT',
        headers,
        body: requestBody
      });

      let responseData;
      if (response.ok) {
        responseData = await response.json();
        console.log('[authService] 使用备用路径更新成功:', responseData);

        // 处理成功响应
        // 重要修复：保存avatar到localStorage，作为本地备份
        if (responseData && responseData.user && responseData.user.avatar !== undefined) {
          localStorage.setItem('userAvatar', responseData.user.avatar || '');
          console.log('[authService] 已将头像URL保存到localStorage:', responseData.user.avatar);
        }

        // 更新本地用户数据
        await updateLocalUserData(responseData, data);
      } else {
        console.error('[authService] 所有更新请求都失败:', response.status, response.statusText);
        throw new Error(`更新请求失败: ${response.status} ${response.statusText}`);
      }

      return responseData;
    }

    // 这个函数现在在try和catch块中都返回responseData
    // 所以这里不会执行到，但为了代码完整性保留
    return null;
  } catch (error) {
    console.error('[authService] 更新用户资料失败:', error);
    logApiError(error);
    throw error;
  }
};

// 获取用户资料的备用方法 - 绕过所有缓存
export const getProfileWithDirectFetch = async () => {
  try {
    console.log('[authService] 使用直接获取方式获取用户资料...');

    // 获取当前用户ID
    const userId = localStorage.getItem('userId');
    if (!userId) {
      throw new Error('[authService] 无法获取用户资料：未找到用户ID');
    }

    // 获取token
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('[authService] 无法获取用户资料：未找到token');
    }

    // 使用环境变量中的API URL，生产环境使用HTTPS
    const baseApiUrl = process.env.REACT_APP_API_URL || (process.env.NODE_ENV === 'production' ? 'https://hkb.life' : 'http://localhost:3001');

    // 准备请求头
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Accept': 'application/json' // 明确指定接受JSON响应
    };

    // 添加时间戳防止缓存
    const timestamp = new Date().getTime();

    // 尝试使用带/api前缀的路径
    let fullApiUrl = `${baseApiUrl}/api/user/profile?_t=${timestamp}`;
    console.log('[authService] 尝试获取用户资料，URL:', fullApiUrl);

    try {
      // 首先尝试带/api前缀的路径
      let response = await fetch(fullApiUrl, {
        method: 'GET',
        headers
      });

      // 检查响应类型
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        console.warn('[authService] 带/api前缀的请求返回了非JSON响应，尝试不带前缀的路径');
        throw new Error('非JSON响应');
      }

      if (!response.ok) {
        console.warn(`[authService] 带/api前缀的请求失败: ${response.status}，尝试不带前缀的路径`);
        throw new Error(`请求失败: ${response.status}`);
      }

      // 如果成功，解析JSON
      const userData = await response.json();

      console.log('[authService] 直接获取用户资料成功:', {
        ...userData,
        avatar: userData.avatar,
        avatar类型: typeof userData.avatar
      });

      // 确保头像字段正确处理
      if (userData.avatar === null) {
        userData.avatar = '';
      }

      // 查看localStorage中是否有备份的头像
      const backupAvatar = localStorage.getItem('userAvatar');
      if (backupAvatar && (!userData.avatar || userData.avatar === '')) {
        console.log('[authService] 从localStorage恢复头像URL:', backupAvatar);
        userData.avatar = backupAvatar;
      }

      // 更新本地存储
      useAuthStore.getState().setUser(userData);

      return userData;

    } catch (prefixError) {
      // 如果带前缀的请求失败，尝试不带前缀的路径
      console.log('[authService] 带前缀的请求失败，尝试不带前缀的路径:', prefixError);

      // 使用不带/api前缀的路径
      fullApiUrl = `${baseApiUrl}/user/profile?_t=${timestamp}`;
      console.log('[authService] 尝试备用路径:', fullApiUrl);

      const response = await fetch(fullApiUrl, {
        method: 'GET',
        headers
      });

      // 检查响应类型
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        console.error('[authService] 服务器返回了非JSON响应:', contentType);
        throw new Error('服务器返回了非JSON响应，请检查API配置');
      }

      if (!response.ok) {
        throw new Error(`请求失败: ${response.status} ${response.statusText}`);
      }

      const userData = await response.json();

      console.log('[authService] 使用备用路径获取用户资料成功:', {
        ...userData,
        avatar: userData.avatar,
        avatar类型: typeof userData.avatar
      });

      // 确保头像字段正确处理
      if (userData.avatar === null) {
        userData.avatar = '';
      }

      // 查看localStorage中是否有备份的头像
      const backupAvatar = localStorage.getItem('userAvatar');
      if (backupAvatar && (!userData.avatar || userData.avatar === '')) {
        console.log('[authService] 从localStorage恢复头像URL:', backupAvatar);
        userData.avatar = backupAvatar;
      }

      // 更新本地存储
      useAuthStore.getState().setUser(userData);

      return userData;
    }
  } catch (error) {
    console.error('[authService] 直接获取用户资料失败:', error);
    throw error;
  }
};

// 修改密码
export const changePassword = async (data: {
  currentPassword: string;
  newPassword: string;
}) => {
  try {
    console.log('[authService] 开始修改密码...');

    // 构建API路径
    const passwordPath = `${API_PATHS.AUTH.CHANGE_PASSWORD}`;
    console.log(`[authService] 使用修改密码路径: ${passwordPath}`);

    // 发送修改密码请求 - 使用POST方法
    const response = await apiClient.post(passwordPath, {
      currentPassword: data.currentPassword,
      newPassword: data.newPassword
    });

    console.log('[authService] 密码修改成功');
    return response.data;
  } catch (error: any) {
    console.error('[authService] 修改密码失败:', error);

    // 添加更多错误处理，以便在UI中显示具体的错误消息
    if (error.response) {
      console.log(`[authService] 服务器返回错误: ${error.response.status}`, error.response.data);

      // 尝试从响应中提取错误信息
      const errorMessage = error.response.data?.error || '修改密码失败';

      // 创建一个包含额外信息的错误对象
      const enhancedError = {
        ...error,
        message: errorMessage,
        status: error.response.status,
        responseData: error.response.data
      };

      // 记录API错误
      logApiError(enhancedError);

      // 抛出增强的错误
      throw enhancedError;
    }

    // 记录API错误
    logApiError(error);
    throw error;
  }
};