import React, { useState } from 'react';
import { 
  TextField, 
  Chip,
  Box,
  InputAdornment,
  Stack
} from '@mui/material';

interface SimpleTagInputProps {
  value: string;
  onChange: (value: string) => void;
  helperText?: string;
  error?: boolean;
}

/**
 * 简单的标签输入组件，支持输入多个标签，用逗号分隔
 * 此版本不再显示下拉菜单，直接由用户输入标签
 */
export const SimpleTagInput: React.FC<SimpleTagInputProps> = ({
  value,
  onChange,
  helperText = "输入标签，用逗号分隔",
  error = false
}) => {
  const [inputValue, setInputValue] = useState("");
  
  // 将逗号分隔的字符串转换为数组
  const valueArray = value ? value.split(',').map(tag => tag.trim()).filter(Boolean) : [];
  
  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };
  
  // 处理键盘按键事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      addTag();
    }
  };
  
  // 添加标签
  const addTag = () => {
    if (!inputValue.trim()) return;
    
    // 移除可能的逗号
    const newTag = inputValue.trim().replace(/,/g, '');
    
    if (newTag) {
      // 检查标签是否已存在
      if (!valueArray.includes(newTag)) {
        // 添加新标签并更新value
        const newValueArray = [...valueArray, newTag];
        onChange(newValueArray.join(', '));
      }
      setInputValue('');
    }
  };
  
  // 删除标签
  const handleDelete = (tagToDelete: string) => {
    const newValueArray = valueArray.filter(tag => tag !== tagToDelete);
    onChange(newValueArray.join(', '));
  };
  
  // 处理失去焦点事件，添加当前输入的标签
  const handleBlur = () => {
    if (inputValue.trim()) {
      addTag();
    }
  };
  
  return (
    <Box>
      {/* 已选择的标签显示 */}
      {valueArray.length > 0 && (
        <Stack direction="row" spacing={1} sx={{ mb: 1, flexWrap: 'wrap', gap: 0.5 }}>
          {valueArray.map((tag, index) => (
            <Chip
              key={index}
              label={tag}
              color="primary"
              size="small"
              onDelete={() => handleDelete(tag)}
            />
          ))}
        </Stack>
      )}
      
      {/* 标签输入框 */}
      <TextField
        fullWidth
        size="small"
        label="自定义标签"
        placeholder="添加标签..."
        helperText={helperText}
        error={error}
        value={inputValue}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        onBlur={handleBlur}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Box component="span" sx={{ color: 'text.secondary' }}>
                #
              </Box>
            </InputAdornment>
          ),
        }}
      />
    </Box>
  );
};

export default SimpleTagInput; 