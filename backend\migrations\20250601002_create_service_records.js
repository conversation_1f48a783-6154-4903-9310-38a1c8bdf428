/**
 * 创建服务记录表，用于服务用户为授权用户创建的记录
 */
exports.up = function(knex) {
  return knex.schema.createTable('service_records', table => {
    // 主键
    table.uuid('id').primary();
    
    // 关联到记录表
    table.uuid('record_id').notNullable().references('id').inTable('records').onDelete('CASCADE');
    
    // 关联到用户授权表
    table.uuid('authorization_id').notNullable().references('id').inTable('user_authorizations');
    
    // 创建者ID(服务用户)
    table.uuid('service_user_id').notNullable().references('id').inTable('users');
    
    // 记录所有者(普通用户)
    table.uuid('owner_user_id').notNullable().references('id').inTable('users');
    
    // 时间戳
    table.timestamp('created_at', { useTz: true }).notNullable().defaultTo(knex.fn.now());
    table.timestamp('updated_at', { useTz: true }).notNullable().defaultTo(knex.fn.now());
    
    // 索引
    table.index(['record_id']);
    table.index(['authorization_id']);
    table.index(['service_user_id']);
    table.index(['owner_user_id']);
  });
};

exports.down = function(knex) {
  return knex.schema.dropTableIfExists('service_records');
}; 