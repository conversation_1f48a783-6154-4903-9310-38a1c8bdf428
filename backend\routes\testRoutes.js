/**
 * 测试相关路由
 * 提供各种测试功能的API端点
 */
const express = require('express');
const router = express.Router();

/**
 * 测试severity字段处理
 * @route POST /test-severity
 * @desc 测试Record模型中severity字段的处理逻辑
 * @access 公开
 */
router.post('/test-severity', async (req, res) => {
  try {
    const { severity } = req.body;
    
    console.log('接收到的severity值:', severity, '类型:', typeof severity);
    
    // 创建一个模拟的Record对象来测试severity处理
    const Record = require('../models/Record');
    const { v4: uuidv4 } = require('uuid');
    
    // 模拟parseJson过程
    const record = Record.fromJson({
      id: uuidv4(),
      title: '测试记录',
      severity: severity
    });
    
    console.log('Record.fromJson处理后的值:', record.severity, '类型:', typeof record.severity);
    
    // 不实际写入数据库，只测试模型处理逻辑
    res.json({
      originalValue: severity,
      originalType: typeof severity,
      processedValue: record.severity,
      processedType: typeof record.severity
    });
  } catch (error) {
    console.error('测试出错:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router; 