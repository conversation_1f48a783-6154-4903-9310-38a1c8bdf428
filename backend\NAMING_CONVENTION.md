# 命名规范

## 数据库命名规范

为确保我们的代码库保持一致性，特别是在将来迁移到PostgreSQL等其他数据库时减少风险，我们采用以下命名规范：

### 数据库命名

1. **表名**：使用小写加下划线分隔的复数形式，例如：`users`, `patient_records`, `tag_categories`

2. **列名**：使用小写加下划线（snake_case），例如：
   - `first_name`（而非`firstName`）
   - `created_at`（而非`createdAt`）
   - `patient_id`（而非`patientId`）

3. **主键**：统一使用`id`作为主键名称

4. **外键**：使用`[表名单数]_id`格式，例如：`user_id`, `patient_id`

5. **布尔值字段**：使用`is_`或`has_`前缀，例如：`is_active`, `is_deleted`, `has_attachments`

6. **时间字段**：
   - `created_at`：创建时间
   - `updated_at`：更新时间
   - `deleted_at`：软删除时间标记

## JavaScript代码命名规范

在JavaScript代码中使用驼峰命名法，依赖Objection.js模型的`snakeCaseMappers()`自动进行命名转换。

1. **变量命名**：使用小驼峰(camelCase)命名
   ```javascript
   const patientId = '...';
   let isActive = true;
   ```

2. **类命名**：使用大驼峰(PascalCase)命名
   ```javascript
   class PatientRecord { ... }
   ```

3. **常量命名**：全大写加下划线
   ```javascript
   const MAX_UPLOAD_SIZE = 10 * 1024 * 1024;
   ```

## 数据库访问规范

1. **优先使用Objection.js模型**：模型已配置了`snakeCaseMappers()`，可以自动处理命名转换
   ```javascript
   // 推荐
   const record = await Record.query()
     .where('patientId', patientId)
     .first();
   ```

2. **如必须使用Knex直接操作数据库**，使用下划线命名的列名：
   ```javascript
   // 正确
   await knex('patients').where('user_id', userId).select('*');
   
   // 错误 - 不要使用驼峰命名
   await knex('patients').where('userId', userId).select('*');
   ```

## 数据库迁移规范

1. **表创建**：使用下划线命名所有表名和列名
   ```javascript
   knex.schema.createTable('patient_records', table => {
     table.uuid('id').primary();
     table.uuid('patient_id').notNullable();
     table.timestamp('created_at').defaultTo(knex.fn.now());
   });
   ```

2. **迁移文件命名**：使用日期前缀加描述性名称
   ```
   20240720_normalize_column_names.js
   ```

## 前后端交互

在前后端交互方面，API JSON响应和请求都使用驼峰命名(camelCase)，与前端JavaScript的命名习惯保持一致。

由于后端模型已配置了`snakeCaseMappers()`，Objection.js会自动处理驼峰命名和下划线命名之间的转换，无需额外编写转换代码。

## 代码库命名修改计划

1. 创建数据库迁移脚本，将所有驼峰命名的列改为下划线命名
2. 更新所有直接使用knex的代码，使用下划线命名的列名
3. 确保所有新建的表和列都遵循下划线命名规范 