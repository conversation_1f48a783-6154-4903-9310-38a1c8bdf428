const express = require('express');
const router = express.Router();
const { authenticate } = require('../src/middleware/auth');
const serviceRecordController = require('../controllers/serviceRecordController');

// 获取服务用户创建的记录
router.get('/', authenticate, serviceRecordController.getServiceRecords);

// ===== 特定路径路由 - 必须放在通用路由前面 =====

// 获取被授权用户的记录列表
router.get('/authorized/:authorizationId/records', authenticate, serviceRecordController.getAuthorizedUserRecords);

// 获取授权用户的患者列表
router.get('/authorized/:authorizationId/patients', authenticate, serviceRecordController.getAuthorizedPatients);

// 获取授权用户的病理列表
router.get('/authorized/:authorizationId/diseases', authenticate, serviceRecordController.getAuthorizedDiseases);

// 根据授权ID获取服务记录
router.get('/by-auth/:authorizationId', authenticate, serviceRecordController.getServiceRecords);

// 根据上下文(授权ID、患者ID和疾病ID)获取服务记录
router.get('/context', authenticate, serviceRecordController.getServiceRecordsByContext);

// ===== 通用路由 - 必须放在特定路径路由后面 =====

// 创建服务记录
router.post('/', authenticate, serviceRecordController.createServiceRecord);

// 更新服务记录 - 使用id参数，与前端保持一致
router.put('/:id', authenticate, serviceRecordController.updateServiceRecord);

// 删除服务记录 - 使用id参数，与前端保持一致
router.delete('/:id', authenticate, serviceRecordController.deleteServiceRecord);

// 获取单个服务记录详情 - 必须放在最后，避免与其他路由冲突
router.get('/:id', authenticate, serviceRecordController.getServiceRecordById);

module.exports = router;