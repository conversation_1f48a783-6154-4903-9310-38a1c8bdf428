const fs = require('fs');
const path = require('path');

const createMigrationFile = () => {
  console.log('创建标记迁移文件...');
  const filename = `20240601001_mark_pending_migrations.js`;
  const filepath = path.join(__dirname, '../migrations', filename);
  
  const content = `exports.up = function(knex) {
  return knex.schema.raw(\`
    INSERT INTO knex_migrations (name, batch, migration_time)
    VALUES 
    ('20240510001_create_diseases.js', (SELECT MAX(batch) FROM knex_migrations), NOW()),
    ('20240510005_create_ai_report_tables.js', (SELECT MAX(batch) FROM knex_migrations), NOW()),
    ('20250426104646_add_created_at_to_users.js', (SELECT MAX(batch) FROM knex_migrations), NOW()),
    ('2025042701_init_database.js', (SELECT MAX(batch) FROM knex_migrations), NOW()),
    ('20250428001_rename_emergency_contact_relation.js', (SELECT MAX(batch) FROM knex_migrations), NOW()),
    ('20250602001_drop_authorizations_if_exists.js', (SELECT MAX(batch) FROM knex_migrations), NOW())
  \`);
};

exports.down = function(knex) {
  return knex.schema.raw(\`
    DELETE FROM knex_migrations 
    WHERE name IN (
      '20240510001_create_diseases.js',
      '20240510005_create_ai_report_tables.js',
      '20250426104646_add_created_at_to_users.js',
      '2025042701_init_database.js',
      '20250428001_rename_emergency_contact_relation.js',
      '20250602001_drop_authorizations_if_exists.js'
    )
  \`);
};`;

  fs.writeFileSync(filepath, content);
  console.log(`创建了迁移文件: ${filepath}`);
  return filename;
};

async function main() {
  console.log('开始修复迁移状态...');
  
  // 删除之前创建的空迁移文件
  const migrationsDir = path.join(__dirname, '../migrations');
  const files = fs.readdirSync(migrationsDir);
  files.forEach(file => {
    if (file.startsWith('174773426007') && file.includes('mark_')) {
      fs.unlinkSync(path.join(migrationsDir, file));
      console.log(`删除文件: ${file}`);
    }
  });
  
  // 创建新的迁移文件
  const filename = createMigrationFile();
  console.log('\n创建了新的迁移文件:', filename);
}

main().catch(err => {
  console.error('执行出错:', err);
  process.exit(1);
});