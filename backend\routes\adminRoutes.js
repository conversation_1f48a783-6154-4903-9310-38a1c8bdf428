const express = require('express');
const router = express.Router();
const knex = require('knex')(require('../knexfile').development);
const { auth, adminOnly } = require('../src/middleware/auth');
const path = require('path');
const fs = require('fs').promises;
const { v4: uuidv4 } = require('uuid');
const os = require('os');

// 记录类型中文映射
const RECORD_TYPE_LABELS = {
  'SELF_DESCRIPTION': '自述',
  'SYMPTOM': '症状',
  'EXAMINATION': '检查',
  'LAB_TEST': '化验',
  'DIAGNOSIS': '诊断',
  'TREATMENT': '治疗',
  'HOSPITALIZATION': '住院',
  'MEDICATION': '用药',
  'SURGERY': '手术',
  'MONITORING': '监测',
  'PHYSICAL_THERAPY': '理疗',
  'DISCHARGE': '出院',
  'APPOINTMENT': '预约',
  'REPORT': '报告',
  'FOLLOW_UP': '随访',
  'PROGNOSIS': '预后',
  'AUX_DIAGNOSIS': '辅诊',
  'NURSING': '护理',
  'REVISIT': '复诊',
  'REFERRAL': '转诊',
  'PSYCHOLOGY': '心理',
  'REHABILITATION': '康复',
  'ASSESSMENT': '评估',
  'OTHER': '其他',
  'AI_ANALYSIS': '辅医智能分析报告'
};

// 管理员获取所有用户列表
router.get('/users', auth, adminOnly, async (req, res) => {
  try {
    console.log('[adminRoutes] 获取所有用户列表');
    
    // 查询参数
    const { 
      search, // 搜索关键字
      role, // 角色筛选
      level, // 级别筛选
      status, // 状态筛选
      page = 0, 
      pageSize = 50 
    } = req.query;
    
    // 构建查询
    let query = knex('users')
      .select(
        'users.id',
        'users.username',
        'users.email',
        'users.phone_number as phoneNumber',
        'users.role',
        'users.level as userLevel',
        'users.active_disease_limit as activeDiseaseLimit',
        'users.family_member_limit as familyMemberLimit',
        'users.created_at as createdAt',
        'users.last_login_at as lastLogin',
        'users.is_active as isActive'
      )
      .leftJoin('ai_report_quotas', 'users.id', 'ai_report_quotas.user_id')
      .select(
        knex.raw('COALESCE(ai_report_quotas.used_this_month, 0) as "usedCount"'),
        knex.raw('COALESCE(ai_report_quotas.monthly_quota, 0) + COALESCE(ai_report_quotas.additional_quota, 0) as "totalCount"')
      );
    
    // 添加搜索条件
    if (search) {
      query = query.where(function() {
        this.where('username', 'like', `%${search}%`)
          .orWhere('email', 'like', `%${search}%`);
      });
    }
    
    // 添加角色筛选
    if (role) {
      query = query.where('role', role);
    }
    
    // 添加级别筛选
    if (level) {
      query = query.where('level', level);
    }
    
    // 添加状态筛选
    if (status === 'active') {
      query = query.where('is_active', true);
    } else if (status === 'inactive') {
      query = query.where('is_active', false);
    }
    
    // 分页
    if (page !== undefined && pageSize !== undefined) {
      query = query.offset(page * pageSize).limit(pageSize);
    }
    
    // 获取用户数据
    const users = await query;
    
    console.log(`[adminRoutes] 查询到 ${users.length} 个用户`);
    
    // 处理用户数据，添加状态和配额信息
    const processedUsers = users.map(user => {
      const currentStatus = user.isActive ? 'ACTIVE' : 'INACTIVE';
      console.log(`[adminRoutes] GET /users: Processing user ${user.username}, DB is_active: ${user.isActive}, Mapped status: ${currentStatus}`);
      return {
        id: user.id,
        username: user.username,
        email: user.email,
        phoneNumber: user.phoneNumber,
        role: user.role,
        userLevel: user.userLevel,
        status: currentStatus,
        quotaRemaining: Math.max(0, (user.totalCount || 0) - (user.usedCount || 0)),
        quotaTotal: user.totalCount || 0,
        createdAt: user.createdAt,
        lastLogin: user.lastLogin,
      };
    });
    
    res.json(processedUsers);
  } catch (error) {
    console.error('[adminRoutes] 获取用户列表失败:', error);
    res.status(500).json({ error: '获取用户列表失败', message: error.message });
  }
});

// 管理员获取单个用户详情
router.get('/users/:userId', auth, adminOnly, async (req, res) => {
  try {
    const { userId } = req.params;
    console.log(`[adminRoutes] 获取用户详情, 用户ID: ${userId}`);
    
    // 获取用户数据
    const user = await knex('users')
      .where('users.id', userId)
      .leftJoin('ai_report_quotas', 'users.id', 'ai_report_quotas.user_id')
      .select(
        'users.id',
        'users.username',
        'users.email',
        'users.phone_number as phoneNumber',
        'users.role',
        'users.level as userLevel',
        'users.active_disease_limit as activeDiseaseLimit',
        'users.family_member_limit as familyMemberLimit',
        'users.created_at as createdAt',
        'users.last_login_at as lastLogin',
        'users.is_active as isActive',
        knex.raw('COALESCE(ai_report_quotas.used_this_month, 0) as "aiUsageCount"'),
        knex.raw('COALESCE(ai_report_quotas.monthly_quota, 0) + COALESCE(ai_report_quotas.additional_quota, 0) as "aiUsageLimit"')
      )
      .first();
    
    if (!user) {
      return res.status(404).json({ error: '用户不存在' });
    }
    
    // 添加用户活动统计
    const patientCount = await knex('patients')
      .where('user_id', userId)
      .whereNull('deleted_at')
      .count('id as count')
      .first();
    
    const diseaseCount = await knex('diseases')
      .where('user_id', userId)
      .where('is_deleted', false)
      .count('id as count')
      .first();
    
    const recordCount = await knex('records')
      .where('user_id', userId)
      .where('is_deleted', false)
      .count('id as count')
      .first();
    
    const aiReportCount = await knex('ai_reports')
      .where('user_id', userId)
      .count('id as count')
      .first();
    
    // 获取用户等级限制
    const levelLimits = await knex('user_level_limits')
      .where('level_type', user.userLevel)
      .first();
    
    // 整理返回数据
    const userData = {
      ...user,
      status: user.isActive ? 'ACTIVE' : 'INACTIVE',
      quotaRemaining: Math.max(0, (user.aiUsageLimit || 0) - (user.aiUsageCount || 0)),
      quotaTotal: user.aiUsageLimit || 0,
      stats: {
        patientCount: patientCount ? patientCount.count : 0,
        diseaseCount: diseaseCount ? diseaseCount.count : 0,
        recordCount: recordCount ? recordCount.count : 0,
        aiReportCount: aiReportCount ? aiReportCount.count : 0
      },
      limits: levelLimits ? {
        maxPatients: levelLimits.max_patients,
        maxDiseases: levelLimits.max_pathologies,
        maxAiReports: levelLimits.max_ai_used,
        maxStorage: levelLimits.max_total_storage
      } : null
    };
    
    delete userData.isActive; // 移除isActive字段，已转换为status
    // delete userData.aiUsageCount; // 保留，因为前端可能仍在使用
    // delete userData.aiUsageLimit; // 保留，因为前端可能仍在使用
    
    res.json(userData);
  } catch (error) {
    console.error('[adminRoutes] 获取用户详情失败:', error);
    res.status(500).json({ error: '获取用户详情失败', message: error.message });
  }
});

// 管理员更新用户信息
router.put('/users/:userId', auth, adminOnly, async (req, res) => {
  const { userId } = req.params;
  let { role, userLevel, status, quotaTotal: quotaTotalFromRequest } = req.body;
  console.log(`[adminRoutes] 请求更新用户信息, 用户ID: ${userId}`, req.body);

  let parsedQuotaTotal;

  try {
    if (quotaTotalFromRequest !== undefined) {
      if (typeof quotaTotalFromRequest === 'string') {
        const tempParsed = parseInt(quotaTotalFromRequest, 10);
        if (!isNaN(tempParsed) && Number.isInteger(tempParsed) && tempParsed >= 0) {
          parsedQuotaTotal = tempParsed;
          console.log(`[adminRoutes] quotaTotal 从字符串 '${quotaTotalFromRequest}' 解析为数字: ${parsedQuotaTotal}`);
        } else {
          return res.status(400).json({ error: '配额值必须是非负整数的字符串或数字形式' });
        }
      } else if (typeof quotaTotalFromRequest === 'number') {
        if (Number.isInteger(quotaTotalFromRequest) && quotaTotalFromRequest >= 0) {
          parsedQuotaTotal = quotaTotalFromRequest;
          console.log(`[adminRoutes] quotaTotal 提供为数字: ${parsedQuotaTotal}`);
        } else {
          return res.status(400).json({ error: '配额值必须是非负整数' });
        }
      } else {
        return res.status(400).json({ error: '配额值格式无效，必须是字符串或数字' });
      }
    }

    if (role && !['USER', 'SERVICE', 'ADMIN'].includes(role)) {
      return res.status(400).json({ error: '无效的用户角色' });
    }
    if (userLevel && !['PERSONAL', 'FAMILY', 'PROFESSIONAL'].includes(userLevel)) {
      return res.status(400).json({ error: '无效的用户等级' });
    }
    if (status && !['ACTIVE', 'INACTIVE', 'SUSPENDED'].includes(status)) {
      return res.status(400).json({ error: '无效的用户状态' });
    }

    await knex.transaction(async (trx) => {
      const currentUserData = await trx('users').where('id', userId).first();
      if (!currentUserData) {
        const error = new Error('用户不存在');
        error.status = 404;
        throw error;
      }

      let currentQuotaData = await trx('ai_report_quotas').where('user_id', userId).first();
      if (!currentQuotaData) {
        currentQuotaData = { monthly_quota: 0, additional_quota: 0, used_this_month: 0 };
        console.log(`[adminRoutes] 用户 ${userId} 当前无配额记录，将使用默认值进行比较和审计.`);
      }
      const oldTotalQuota = (currentQuotaData.monthly_quota || 0) + (currentQuotaData.additional_quota || 0);

      const userUpdateData = { updated_at: knex.fn.now() };
      if (role) userUpdateData.role = role;
      if (status) userUpdateData.is_active = (status === 'ACTIVE');

      let finalQuotaToSet; // This will be the new monthly_quota for AI reports

      if (userLevel && userLevel !== currentUserData.level) {
        console.log(`[adminRoutes] 用户等级从 ${currentUserData.level} 变为 ${userLevel}. 更新等级及相关用户限制.`);
        userUpdateData.level = userLevel;

        const newLevelConfig = await trx('user_level_limits')
          .where('level_type', userLevel)
          .select('max_ai_used', 'max_pathologies', 'max_patients') // Added max_patients
          .first();

        console.log(`[adminRoutes] 新等级 (${userLevel}) 在 user_level_limits 表中的配置:`, newLevelConfig);

        if (newLevelConfig) {
          if (parsedQuotaTotal !== undefined) {
            finalQuotaToSet = parsedQuotaTotal;
            console.log(`[adminRoutes] AI配额将根据请求中显式提供的 quotaTotal (${finalQuotaToSet}) 进行设置.`);
          } else {
            if (typeof newLevelConfig.max_ai_used === 'number' && newLevelConfig.max_ai_used >= 0) {
              finalQuotaToSet = newLevelConfig.max_ai_used;
              console.log(`[adminRoutes] AI配额将根据新等级 ${userLevel} 的默认 max_ai_used (${finalQuotaToSet}) 自动调整.`);
            } else {
              console.warn(`[adminRoutes] 新等级 ${userLevel} 的 max_ai_used (${newLevelConfig.max_ai_used}) 无效或未配置. AI配额不会因此自动调整.`);
            }
          }

          if (typeof newLevelConfig.max_pathologies === 'number' && newLevelConfig.max_pathologies >= 0) {
            userUpdateData.active_disease_limit = newLevelConfig.max_pathologies;
            console.log(`[adminRoutes] users.active_disease_limit 更新为: ${userUpdateData.active_disease_limit} (来自 max_pathologies).`);
          } else {
            console.warn(`[adminRoutes] 新等级 ${userLevel} 的 max_pathologies (${newLevelConfig.max_pathologies}) 无效或未配置. active_disease_limit 未更新.`);
          }
          
          // Update family_member_limit from user_level_limits.max_patients
          if (typeof newLevelConfig.max_patients === 'number' && newLevelConfig.max_patients >= 0) {
            userUpdateData.family_member_limit = newLevelConfig.max_patients;
            console.log(`[adminRoutes] users.family_member_limit 更新为: ${userUpdateData.family_member_limit} (来自 max_patients).`);
          } else {
            console.warn(`[adminRoutes] 新等级 ${userLevel} 的 max_patients (${newLevelConfig.max_patients}) 无效或未配置. family_member_limit 未更新.`);
          }

        } else {
          console.warn(`[adminRoutes] 未找到等级 ${userLevel} 在 user_level_limits 表中的配置. AI配额, active_disease_limit, family_member_limit 可能不会自动更新.`);
          if (parsedQuotaTotal !== undefined) {
            finalQuotaToSet = parsedQuotaTotal;
             console.log(`[adminRoutes] 尽管等级配置未找到, 但请求中显式提供了 quotaTotal (${finalQuotaToSet}), 将用于AI配额.`);
          }
        }
      } else if (userLevel) { // Level provided in request but it's the same as current
        userUpdateData.level = userLevel; // Still ensure it's part of the update if provided
        if (parsedQuotaTotal !== undefined) {
          finalQuotaToSet = parsedQuotaTotal;
          console.log(`[adminRoutes] 用户等级未变. 配额将根据请求中显式提供的 quotaTotal (${finalQuotaToSet}) 进行设置.`);
        }
      } else { // No userLevel in request
          if (parsedQuotaTotal !== undefined) {
            finalQuotaToSet = parsedQuotaTotal;
            console.log(`[adminRoutes] 用户等级未在请求中提供. 配额将根据请求中显式提供的 quotaTotal (${finalQuotaToSet}) 进行设置.`);
          }
      }

      const quotaTotalForAuditAfter = finalQuotaToSet !== undefined ? finalQuotaToSet : oldTotalQuota;

      const auditLogEntry = {
        id: uuidv4(),
        admin_id: req.user_id,
        action_type: 'USER_UPDATE',
        target_id: userId,
        action_details: JSON.stringify({
          before: {
            role: currentUserData.role,
            level: currentUserData.level,
            is_active: currentUserData.is_active,
            quotaTotal: oldTotalQuota,
            active_disease_limit: currentUserData.active_disease_limit,
            family_member_limit: currentUserData.family_member_limit,
          },
          after: {
            role: userUpdateData.role || currentUserData.role,
            level: userUpdateData.level || currentUserData.level,
            is_active: (userUpdateData.is_active !== undefined) ? userUpdateData.is_active : currentUserData.is_active,
            quotaTotal: quotaTotalForAuditAfter,
            active_disease_limit: userUpdateData.active_disease_limit !== undefined ? userUpdateData.active_disease_limit : currentUserData.active_disease_limit,
            family_member_limit: userUpdateData.family_member_limit !== undefined ? userUpdateData.family_member_limit : currentUserData.family_member_limit,
          }
        }),
        created_at: new Date().toISOString()
      };
      await trx('admin_audit_logs').insert(auditLogEntry);

      if (Object.keys(userUpdateData).length > 1 || (Object.keys(userUpdateData).length === 1 && !userUpdateData.updated_at)) {
        await trx('users').where('id', userId).update(userUpdateData);
        console.log(`[adminRoutes] 用户 ${userId} 的用户表已更新:`, userUpdateData);
      } else {
        console.log(`[adminRoutes] 用户 ${userId} 的用户表无字段变更 (除 updated_at).`);
      }

      if (finalQuotaToSet !== undefined) {
        console.log(`[adminRoutes] 更新用户 ${userId} 的 AI 配额: monthly_quota=${finalQuotaToSet}, 保持 additional_quota 不变`);
        const quotaUpdatePayload = {
          user_id: userId,
          monthly_quota: finalQuotaToSet,
          updated_at: knex.fn.now(),
        };

        const existingQuotaRecord = await trx('ai_report_quotas').where({ user_id: userId }).first();
        if (existingQuotaRecord) {
          await trx('ai_report_quotas').where({ user_id: userId }).update(quotaUpdatePayload);
          console.log(`[adminRoutes] 用户 ${userId} 的现有配额记录已更新.`);
        } else {
          console.log(`[adminRoutes] 用户 ${userId} 不存在配额记录，将创建新记录.`);
          await trx('ai_report_quotas').insert({
            id: uuidv4(),
            ...quotaUpdatePayload,
            used_this_month: 0,
            total_used: 0,
            last_reset_date: knex.fn.now()
          });
          console.log(`[adminRoutes] 用户 ${userId} 的新配额记录已创建.`);
        }
      } else {
         console.log(`[adminRoutes] finalQuotaToSet 未定义, AI配额表将不会更新.`);
      }

      const updatedUser = await trx('users')
        .where('users.id', userId)
        .leftJoin('ai_report_quotas', 'users.id', 'ai_report_quotas.user_id')
        .select(
          'users.id',
          'users.username',
          'users.email',
          'users.phone_number as phoneNumber',
          'users.role',
          'users.level as userLevel',
          'users.active_disease_limit as activeDiseaseLimit',
          'users.family_member_limit as familyMemberLimit',
          'users.created_at as createdAt',
          'users.last_login_at as lastLogin',
          'users.is_active',
          knex.raw('COALESCE(ai_report_quotas.used_this_month, 0) as "usedCount"'),
          knex.raw('COALESCE(ai_report_quotas.monthly_quota, 0) + COALESCE(ai_report_quotas.additional_quota, 0) as "totalQuotaAmount"')
        )
        .first();
      
      const responseUser = {
        ...updatedUser,
        status: updatedUser.is_active ? 'ACTIVE' : 'INACTIVE',
        quotaRemaining: Math.max(0, (updatedUser.totalQuotaAmount || 0) - (updatedUser.usedCount || 0)),
        quotaTotal: updatedUser.totalQuotaAmount || 0,
      };
      delete responseUser.is_active; 
      delete responseUser.totalQuotaAmount;

      res.json(responseUser);
    });

  } catch (error) {
    console.error(`[adminRoutes] 更新用户失败 (用户ID: ${userId}):`, error);
    if (error.status === 404) {
      return res.status(404).json({ error: error.message });
    }
    res.status(500).json({ error: '更新用户失败', message: error.message });
  }
});

// 管理员修改用户状态
router.patch('/users/:userId/status', auth, adminOnly, async (req, res) => {
  try {
    const { userId } = req.params;
    const { status } = req.body;
    
    if (!status || !['ACTIVE', 'INACTIVE', 'SUSPENDED'].includes(status)) {
      return res.status(400).json({ error: '无效的状态值' });
    }
    
    console.log(`[adminRoutes] 修改用户状态, 用户ID: ${userId}, 新状态: ${status}`);
    
    // 检查用户是否存在
    const user = await knex('users').where('id', userId).first();
    if (!user) {
      return res.status(404).json({ error: '用户不存在' });
    }
    
    // 当前的活动状态
    const currentIsActive = user.is_active;
    console.log(`[adminRoutes] PATCH /users/:userId/status: User ${userId}, Current is_active: ${currentIsActive}, Requested status: ${status}`);
    
    // 新的活动状态
    let newIsActive;
    if (status === 'ACTIVE') newIsActive = true;
    else if (status === 'INACTIVE' || status === 'SUSPENDED') newIsActive = false;
    else newIsActive = currentIsActive; // 保持不变
    
    // 审计日志条目
    const auditLogEntry = {
      id: uuidv4(),
      admin_id: req.user_id,
      action_type: 'USER_STATUS_CHANGE',
      target_id: userId,
      action_details: JSON.stringify({
        before: { status: currentIsActive ? 'ACTIVE' : 'INACTIVE' },
        after: { status }
      }),
      created_at: new Date().toISOString()
    };
    
    // 使用事务确保数据一致性
    await knex.transaction(async (trx) => {
      // 更新用户状态
      await trx('users')
        .where('id', userId)
        .update({
          is_active: newIsActive,
          updated_at: new Date().toISOString()
        });
      console.log(`[adminRoutes] PATCH /users/:userId/status: User ${userId}, DB is_active updated to: ${newIsActive}`);
      
      // 记录审计日志
      await trx('admin_audit_logs').insert(auditLogEntry);
    });
    
    res.json({ 
      message: '用户状态已更新',
      status 
    });
  } catch (error) {
    console.error('[adminRoutes] 修改用户状态失败:', error);
    res.status(500).json({ error: '修改用户状态失败', message: error.message });
  }
});

// 获取所有审计日志
router.get('/audit-logs', auth, adminOnly, async (req, res) => {
  try {
    // 查询参数
    const { 
      action_type,
      admin_id,
      start_date,
      end_date,
      page = 0, 
      pageSize = 50 
    } = req.query;
    
    console.log('[adminRoutes] 获取审计日志', req.query);
    
    // 构建查询
    let query = knex('admin_audit_logs')
      .select(
        'admin_audit_logs.id',
        'admin_audit_logs.admin_id',
        'admin_audit_logs.action_type',
        'admin_audit_logs.target_id',
        'admin_audit_logs.action_details',
        'admin_audit_logs.created_at',
        'users.username as admin_username'
      )
      .leftJoin('users', 'admin_audit_logs.admin_id', 'users.id')
      .orderBy('admin_audit_logs.created_at', 'desc');
    
    // 添加过滤条件
    if (action_type) {
      query = query.where('action_type', action_type);
    }
    
    if (admin_id) {
      query = query.where('admin_id', admin_id);
    }
    
    if (start_date) {
      query = query.where('created_at', '>=', start_date);
    }
    
    if (end_date) {
      query = query.where('created_at', '<=', end_date);
    }
    
    // 分页
    if (page !== undefined && pageSize !== undefined) {
      query = query.offset(page * pageSize).limit(pageSize);
    }
    
    // 执行查询
    const logs = await query;
    
    // 格式化日志
    const formattedLogs = logs.map(log => ({
      id: log.id,
      adminId: log.admin_id,
      adminUsername: log.admin_username,
      actionType: log.action_type,
      targetId: log.target_id,
      actionDetails: JSON.parse(log.action_details),
      createdAt: log.created_at
    }));
    
    res.json(formattedLogs);
  } catch (error) {
    console.error('[adminRoutes] 获取审计日志失败:', error);
    res.status(500).json({ error: '获取审计日志失败', message: error.message });
  }
});

// 获取系统信息
router.get('/system-info', auth, adminOnly, async (req, res) => {
  try {
    console.log('[adminRoutes] 获取系统信息');
    
    // 获取 SQLite 数据库信息
    const dbPath = path.resolve(__dirname, '../db.sqlite');
    const dbStats = await fs.stat(dbPath);
    const dbSize = dbStats.size;
    
    // 获取表数量
    // PostgreSQL兼容查询：从pg_catalog.pg_tables获取表信息
      const tables = await knex.raw(`SELECT tablename AS name FROM pg_catalog.pg_tables WHERE schemaname NOT IN ('pg_catalog', 'information_schema')`);
    const tableCount = tables.length;
    
    // 获取系统信息
    const cpus = os.cpus();
    const systemInfo = {
      os: {
        platform: os.platform(),
        version: os.release(),
        memory: {
          total: os.totalmem(),
          free: os.freemem()
        },
        cpus: {
          model: cpus.length > 0 ? cpus[0].model : '未知',
          speed: cpus.length > 0 ? cpus[0].speed : 0,
          cores: cpus.length
        }
      },
      app: {
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        uptime: process.uptime() * 1000, // 转换为毫秒
        nodeVersion: process.version
      },
      storage: {
        total: 0, // 这个需要使用特定方法获取，可根据需求修改
        free: 0,  // 这个需要使用特定方法获取，可根据需求修改
        used: 0   // 这个需要使用特定方法获取，可根据需求修改
      },
      database: {
        type: 'SQLite',
        version: '3', // SQLite 3
        size: dbSize,
        tables: tableCount
      }
    };
    
    // 为了演示，我们假设总存储是100GB，已用是20GB
    systemInfo.storage.total = 100 * 1024 * 1024 * 1024; // 100 GB
    systemInfo.storage.used = 20 * 1024 * 1024 * 1024;  // 20 GB
    systemInfo.storage.free = systemInfo.storage.total - systemInfo.storage.used;
    
    // 记录审计日志
    const auditLogEntry = {
      id: uuidv4(),
      admin_id: req.user_id,
      action_type: 'SYSTEM_INFO_VIEW',
      target_id: null,
      action_details: JSON.stringify({
        timestamp: new Date().toISOString()
      }),
      created_at: new Date().toISOString()
    };
    
    await knex('admin_audit_logs').insert(auditLogEntry);
    
    res.json(systemInfo);
  } catch (error) {
    console.error('[adminRoutes] 获取系统信息失败:', error);
    res.status(500).json({ error: '获取系统信息失败', message: error.message });
  }
});

// 获取在线会话
router.get('/active-sessions', auth, adminOnly, async (req, res) => {
  try {
    console.log('[adminRoutes] 获取在线会话');
    
    // 在实际系统中，我们需要从会话存储中获取活跃会话
    // 由于这里没有实际的会话存储，我们模拟一些数据
    
    // 获取最近登录的用户作为"活跃"用户
    const recentUsers = await knex('users')
      .whereNotNull('last_login_at')
      .orderBy('last_login_at', 'desc')
      .limit(10);
    
    const mockSessions = recentUsers.map(user => {
      const lastLogin = new Date(user.last_login_at);
      const now = new Date();
      const duration = now.getTime() - lastLogin.getTime();
      
      return {
        id: uuidv4(),
        userId: user.id,
        username: user.username,
        ip: '127.0.0.1', // 模拟IP地址
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36', // 模拟用户代理
        lastActivity: user.last_login_at,
        duration: duration // 会话持续时间（毫秒）
      };
    });
    
    // 记录审计日志
    const auditLogEntry = {
      id: uuidv4(),
      admin_id: req.user_id,
      action_type: 'ACTIVE_SESSIONS_VIEW',
      target_id: null,
      action_details: JSON.stringify({
        sessionCount: mockSessions.length
      }),
      created_at: new Date().toISOString()
    };
    
    await knex('admin_audit_logs').insert(auditLogEntry);
    
    res.json(mockSessions);
  } catch (error) {
    console.error('[adminRoutes] 获取在线会话失败:', error);
    res.status(500).json({ error: '获取在线会话失败', message: error.message });
  }
});

// 终止会话
router.delete('/active-sessions/:sessionId', auth, adminOnly, async (req, res) => {
  try {
    const { sessionId } = req.params;
    console.log(`[adminRoutes] 终止会话: ${sessionId}`);
    
    // 在实际系统中，我们需要从会话存储中删除指定会话
    // 由于这里没有实际的会话存储，我们只记录审计日志
    
    const auditLogEntry = {
      id: uuidv4(),
      admin_id: req.user_id,
      action_type: 'SESSION_TERMINATE',
      target_id: sessionId,
      action_details: JSON.stringify({
        timestamp: new Date().toISOString()
      }),
      created_at: new Date().toISOString()
    };
    
    await knex('admin_audit_logs').insert(auditLogEntry);
    
    res.json({ message: '会话已终止' });
  } catch (error) {
    console.error('[adminRoutes] 终止会话失败:', error);
    res.status(500).json({ error: '终止会话失败', message: error.message });
  }
});

// 获取AI设置
router.get('/ai-settings', auth, adminOnly, async (req, res) => {
  try {
    console.log('[adminRoutes] 获取AI设置');
    
    // 在实际系统中，我们需要从数据库或配置文件中获取AI设置
    // 由于这里没有实际的AI设置表，我们模拟一些数据
    
    const aiSettings = {
      modelName: 'gpt-4',
      maxTokens: 8192,
      temperature: 0.7,
      isEnabled: true,
      dailyQuotaLimit: 100,
      apiKey: 'sk-xxxxxxxxxxxxxxxxxxxx', // 实际中不应返回完整API密钥
      endpointUrl: 'https://api.openai.com/v1/chat/completions'
    };
    
    // 记录审计日志
    const auditLogEntry = {
      id: uuidv4(),
      admin_id: req.user_id,
      action_type: 'AI_SETTINGS_VIEW',
      target_id: null,
      action_details: JSON.stringify({
        timestamp: new Date().toISOString()
      }),
      created_at: new Date().toISOString()
    };
    
    await knex('admin_audit_logs').insert(auditLogEntry);
    
    res.json(aiSettings);
  } catch (error) {
    console.error('[adminRoutes] 获取AI设置失败:', error);
    res.status(500).json({ error: '获取AI设置失败', message: error.message });
  }
});

// 更新AI设置
router.put('/ai-settings', auth, adminOnly, async (req, res) => {
  try {
    const settings = req.body;
    console.log('[adminRoutes] 更新AI设置:', settings);
    
    // 验证必需字段
    if (settings.isEnabled === undefined || 
        !settings.modelName || 
        !settings.endpointUrl || 
        settings.maxTokens === undefined || 
        settings.temperature === undefined ||
        settings.dailyQuotaLimit === undefined) {
      return res.status(400).json({ error: 'AI设置缺少必需字段' });
    }
    
    // 记录审计日志
    const auditLogEntry = {
      id: uuidv4(),
      admin_id: req.user_id,
      action_type: 'AI_SETTINGS_UPDATE',
      target_id: null,
      action_details: JSON.stringify({
        before: {
          modelName: 'gpt-4', // 实际中应从数据库获取原值
          maxTokens: 8192,
          temperature: 0.7,
          isEnabled: true,
          dailyQuotaLimit: 100,
          endpointUrl: 'https://api.openai.com/v1/chat/completions'
        },
        after: {
          modelName: settings.modelName,
          maxTokens: settings.maxTokens,
          temperature: settings.temperature,
          isEnabled: settings.isEnabled,
          dailyQuotaLimit: settings.dailyQuotaLimit,
          endpointUrl: settings.endpointUrl
        }
      }),
      created_at: new Date().toISOString()
    };
    
    await knex('admin_audit_logs').insert(auditLogEntry);
    
    res.json({ 
      message: 'AI设置已更新',
      settings 
    });
  } catch (error) {
    console.error('[adminRoutes] 更新AI设置失败:', error);
    res.status(500).json({ error: '更新AI设置失败', message: error.message });
  }
});

// 清除系统缓存
router.post('/clear-cache', auth, adminOnly, async (req, res) => {
  try {
    console.log('[adminRoutes] 清除系统缓存');
    
    // 在实际系统中，我们需要实现清除缓存的逻辑
    // 这里只记录审计日志
    
    const auditLogEntry = {
      id: uuidv4(),
      admin_id: req.user_id,
      action_type: 'SYSTEM_CACHE_CLEAR',
      target_id: null,
      action_details: JSON.stringify({
        timestamp: new Date().toISOString()
      }),
      created_at: new Date().toISOString()
    };
    
    await knex('admin_audit_logs').insert(auditLogEntry);
    
    // 等待一段时间模拟清除缓存操作
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    res.json({ message: '系统缓存已清除' });
  } catch (error) {
    console.error('[adminRoutes] 清除系统缓存失败:', error);
    res.status(500).json({ error: '清除系统缓存失败', message: error.message });
  }
});

// 获取所有AI报告
router.get('/ai-reports', auth, adminOnly, async (req, res) => {
  try {
    console.log('[adminRoutes] 获取所有AI报告');
    
    // 查询参数
    const { 
      search, // 搜索关键字
      status, // 状态筛选
      userId, // 用户ID筛选
      startDate, // 开始日期
      endDate, // 结束日期
      page = 0, 
      pageSize = 50 
    } = req.query;
    
    // 1. 构建基础查询 (仅包含表、连接和过滤条件)
    let baseQuery = knex('ai_reports')
      .leftJoin('users', 'ai_reports.user_id', 'users.id')
      .leftJoin('patients', 'ai_reports.patient_id', 'patients.id');
    
    // 添加搜索条件
    if (search) {
      baseQuery = baseQuery.where(function() { // 确保赋值回 baseQuery
        this.where('ai_reports.title', 'like', `%${search}%`)
          .orWhere('users.username', 'like', `%${search}%`)
          .orWhere('patients.name', 'like', `%${search}%`);
      });
    }
    
    // 添加状态筛选
    if (status) {
      baseQuery = baseQuery.where('ai_reports.status', status); // 确保赋值回 baseQuery
    }
    
    // 添加用户ID筛选
    if (userId) {
      baseQuery = baseQuery.where('ai_reports.user_id', userId); // 确保赋值回 baseQuery
    }
    
    // 添加日期范围筛选
    if (startDate) {
      baseQuery = baseQuery.where('ai_reports.created_at', '>=', startDate); // 确保赋值回 baseQuery
    }
    
    if (endDate) {
      // 为结束日期添加一天，以包含整个结束日期
      const endDateObj = new Date(endDate);
      endDateObj.setDate(endDateObj.getDate() + 1);
      const nextDay = endDateObj.toISOString().split('T')[0];
      baseQuery = baseQuery.where('ai_reports.created_at', '<', nextDay); // 确保赋值回 baseQuery
    }
    
    // 2. 获取总数用于分页 (克隆基础查询)
    const countClone = baseQuery.clone();
    const countResult = await countClone.count('ai_reports.id as totalReports').first();
    const totalCount = countResult && countResult.totalReports ? parseInt(countResult.totalReports.toString(), 10) : 0;

    // 3. 获取AI报告数据 (克隆基础查询并添加select, orderBy, limit, offset)
    const dataClone = baseQuery.clone();
    let reportsQuery = dataClone.select(
        'ai_reports.id',
        'ai_reports.user_id as userId',
        'users.username as username',
        'ai_reports.disease_id as diseaseId',
        'ai_reports.patient_id as patientId',
        'patients.name as patientName',
        'ai_reports.title',
        'ai_reports.template_type as templateType',
        'ai_reports.status',
        'ai_reports.error_message as errorMessage',
        'ai_reports.created_at as createdAt',
        'ai_reports.updated_at as updatedAt'
      )
      .orderBy('ai_reports.created_at', 'desc');
    
    // 分页
    if (page !== undefined && pageSize !== undefined) {
      const numericPage = parseInt(page.toString(), 10);
      const numericPageSize = parseInt(pageSize.toString(), 10);
      reportsQuery = reportsQuery.limit(numericPageSize).offset(numericPage * numericPageSize);
    }
    
    // 获取AI报告数据
    const reports = await reportsQuery;
    
    // 处理报告数据，添加模板类型中文名称
    const processedReports = reports.map(report => ({
      ...report,
      templateTypeLabel: report.templateType === 'AI_ANALYSIS' ? '辅医智能分析报告' : report.templateType
    }));
    
    console.log(`[adminRoutes] 查询到 ${reports.length} 个AI报告, 总数: ${totalCount}`);
    
    // 审计日志条目
    const auditLogEntry = {
      id: uuidv4(),
      admin_id: req.user_id,
      action_type: 'AI_REPORTS_VIEW',
      target_id: null,
      action_details: JSON.stringify({
        search,
        status,
        userId,
        startDate,
        endDate,
        count: reports.length
      }),
      created_at: new Date().toISOString()
    };
    
    await knex('admin_audit_logs').insert(auditLogEntry);
    
    res.json({
      reports: processedReports,
      pagination: {
        total: totalCount,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        pages: Math.ceil(totalCount / pageSize)
      }
    });
  } catch (error) {
    console.error('[adminRoutes] 获取AI报告列表失败:', error);
    res.status(500).json({ error: '获取AI报告列表失败', message: error.message });
  }
});

// 获取AI报告详情
router.get('/ai-reports/:reportId', auth, adminOnly, async (req, res) => {
  try {
    const { reportId } = req.params;
    console.log(`[adminRoutes] 获取AI报告详情, 报告ID: ${reportId}`);
    
    // 获取AI报告数据
    const report = await knex('ai_reports')
      .where('id', reportId)
      .first();
    
    if (!report) {
      return res.status(404).json({ error: 'AI报告不存在' });
    }
    
    // 获取用户信息
    const user = await knex('users')
      .where('id', report.user_id)
      .select('id', 'username', 'email')
      .first();
    
    // 获取患者信息
    const patient = await knex('patients')
      .where('id', report.patient_id)
      .select('id', 'name', 'gender', 'birth_date as birthDate')
      .first();
    
    // 获取病理信息
    const disease = await knex('diseases')
      .select(
        'diseases.id',
        'diseases.name',
        'diseases.stage',
        'diseases.diagnosis_date as diagnosisDate',
        'diseases.description',
        'diseases.treatment',
        'diseases.patient_id as patientId',
        'patients.name as patientName',
        'diseases.user_id as userId',
        'users.username',
        'diseases.is_deleted as isDeleted',
        'diseases.created_at as createdAt',
        'diseases.updated_at as updatedAt'
      )
      .leftJoin('patients', 'diseases.patient_id', 'patients.id')
      .leftJoin('users', 'diseases.user_id', 'users.id')
      .where('diseases.id', report.disease_id)
      .first();
    
    if (!disease) {
      return res.status(404).json({ error: '病理不存在' });
    }
    
    // 审计日志条目
    const auditLogEntry = {
      id: uuidv4(),
      admin_id: req.user_id,
      action_type: 'AI_REPORT_DETAIL_VIEW',
      target_id: reportId,
      action_details: JSON.stringify({
        report_id: reportId,
        user_id: report.user_id,
        patient_id: report.patient_id,
        disease_id: report.disease_id
      }),
      created_at: new Date().toISOString()
    };
    
    await knex('admin_audit_logs').insert(auditLogEntry);
    
    // 将下划线命名转换为驼峰命名，并添加关联实体信息
    const formattedReport = {
      id: report.id,
      userId: report.user_id,
      user: user,
      diseaseId: report.disease_id,
      disease: disease,
      patientId: report.patient_id,
      patient: patient,
      title: report.title,
      templateType: report.template_type,
      templateTypeLabel: report.template_type === 'AI_ANALYSIS' ? '辅医智能分析报告' : report.template_type,
      content: report.content ? (typeof report.content === 'string' ? JSON.parse(report.content) : report.content) : null,
      anonymizedInfo: report.anonymized_info ? (typeof report.anonymized_info === 'string' ? JSON.parse(report.anonymized_info) : report.anonymized_info) : null,
      status: report.status,
      errorMessage: report.error_message,
      createdAt: report.created_at,
      updatedAt: report.updated_at
    };
    
    res.json(formattedReport);
  } catch (error) {
    console.error('[adminRoutes] 获取AI报告详情失败:', error);
    res.status(500).json({ error: '获取AI报告详情失败', message: error.message });
  }
});

// 删除AI报告
router.delete('/ai-reports/:reportId', auth, adminOnly, async (req, res) => {
  try {
    const { reportId } = req.params;
    const deletionReason = req.body.reason || '管理员删除';
    
    console.log(`[adminRoutes] 删除AI报告, 报告ID: ${reportId}, 原因: ${deletionReason}`);
    
    // 获取报告信息用于审计日志
    const report = await knex('ai_reports')
      .where('id', reportId)
      .first();
    
    if (!report) {
      return res.status(404).json({ error: 'AI报告不存在' });
    }
    
    // 使用事务确保数据一致性
    await knex.transaction(async (trx) => {
      // 删除报告
      await trx('ai_reports')
        .where('id', reportId)
        .delete();
      
      // 记录审计日志
      const auditLogEntry = {
        id: uuidv4(),
        admin_id: req.user_id,
        action_type: 'REPORT_DELETE',
        target_id: reportId,
        action_details: JSON.stringify({
          report_id: reportId,
          user_id: report.user_id,
          reason: deletionReason,
          title: report.title,
          created_at: report.created_at
        }),
        created_at: new Date().toISOString()
      };
      
      await trx('admin_audit_logs').insert(auditLogEntry);
    });
    
    res.json({ 
      message: 'AI报告已删除',
      reportId
    });
  } catch (error) {
    console.error('[adminRoutes] 删除AI报告失败:', error);
    res.status(500).json({ error: '删除AI报告失败', message: error.message });
  }
});

// 获取所有患者列表
router.get('/patients', auth, adminOnly, async (req, res) => {
  try {
    console.log('[adminRoutes] 获取所有患者列表');
    
    // 查询参数
    const { 
      search, // 搜索关键字
      gender, // 性别筛选
      userId, // 用户ID筛选
      page = 0, 
      pageSize = 50 
    } = req.query;
    
    // 构建基础查询 (仅包含表、连接和过滤条件)
    let baseQuery = knex('patients')
      .leftJoin('users', 'patients.user_id', 'users.id')
      .whereNull('patients.deleted_at');
    
    // 添加搜索条件
    if (search) {
      baseQuery = baseQuery.where(function() {
        this.where('patients.name', 'like', `%${search}%`)
            .orWhere('patients.phone_number', 'like', `%${search}%`)
            .orWhere('patients.email', 'like', `%${search}%`);
      });
    }
    
    // 添加性别筛选
    if (gender) {
      baseQuery = baseQuery.where('patients.gender', gender);
    }
    
    // 添加用户ID筛选
    if (userId) {
      baseQuery = baseQuery.where('patients.user_id', userId);
    }
    
    // 获取总数用于分页 (克隆基础查询)
    const countClone = baseQuery.clone();
    const countResult = await countClone.count('patients.id as totalPatients').first();
    const totalCount = countResult && countResult.totalPatients ? parseInt(countResult.totalPatients.toString(), 10) : 0;
    
    // 获取患者数据 (克隆基础查询并添加select, orderBy, limit, offset)
    const dataClone = baseQuery.clone();
    let patientsQuery = dataClone.select(
        'patients.id',
        'patients.name',
        'patients.gender',
        'patients.phone_number as phoneNumber',
        'patients.email',
        'patients.birth_date as birthDate',
        'patients.address',
        'patients.user_id as userId',
        'patients.is_primary as isPrimary',
        'patients.created_at as createdAt',
        'users.username'
      )
      .orderBy('patients.created_at', 'desc');
    
    // 分页
    if (page !== undefined && pageSize !== undefined) {
      const numericPage = parseInt(page.toString(), 10);
      const numericPageSize = parseInt(pageSize.toString(), 10);
      patientsQuery = patientsQuery.limit(numericPageSize).offset(numericPage * numericPageSize);
    }
    
    // 获取患者数据
    const patients = await patientsQuery;
    
    console.log(`[adminRoutes] 查询到 ${patients.length} 个患者, 总数: ${totalCount}`);
    
    // 审计日志条目
    const auditLogEntry = {
      id: uuidv4(),
      admin_id: req.user_id,
      action_type: 'PATIENTS_VIEW',
      target_id: null,
      action_details: JSON.stringify({
        search,
        gender,
        userId,
        count: patients.length
      }),
      created_at: new Date().toISOString()
    };
    
    await knex('admin_audit_logs').insert(auditLogEntry);
    
    res.json({
      patients,
      total: totalCount,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    });
  } catch (error) {
    console.error('[adminRoutes] 获取患者列表失败:', error);
    res.status(500).json({ error: '获取患者列表失败', message: error.message });
  }
});

// 获取患者详情
router.get('/patients/:patientId', auth, adminOnly, async (req, res) => {
  try {
    const { patientId } = req.params;
    console.log(`[adminRoutes] 获取患者详情, 患者ID: ${patientId}`);
    
    // 获取患者数据
    const patient = await knex('patients')
      .select(
        'patients.id',
        'patients.name',
        'patients.gender',
        'patients.phone_number as phoneNumber',
        'patients.email',
        'patients.birth_date as birthDate',
        'patients.id_card as idCard',
        'patients.medicare_card as medicareCard',
        'patients.medicare_location as medicareLocation',
        'patients.address',
        'patients.emergency_contact_name as emergencyContactName',
        'patients.emergency_contact_phone as emergencyContactPhone',
        'patients.emergency_contact_relationship as emergencyContactRelationship',
        'patients.past_medical_history as pastMedicalHistory',
        'patients.family_medical_history as familyMedicalHistory',
        'patients.allergy_history as allergyHistory',
        'patients.blood_type as bloodType',
        'patients.last_visit_date as lastVisitDate',
        'patients.height',
        'patients.weight',
        'patients.bmi',
        'patients.user_id as userId',
        'patients.is_primary as isPrimary',
        'patients.created_at as createdAt',
        'patients.updated_at as updatedAt',
        'users.username'
      )
      .leftJoin('users', 'patients.user_id', 'users.id')
      .where('patients.id', patientId)
      .whereNull('patients.deleted_at')
      .first();
    
    if (!patient) {
      return res.status(404).json({ error: '患者不存在' });
    }
    
    // 获取患者相关统计
    const diseaseCount = await knex('diseases')
      .where('patient_id', patientId)
      .where('is_deleted', false)
      .count('id as count')
      .first();
    
    const recordCount = await knex('records')
      .where('patient_id', patientId)
      .where('is_deleted', false)
      .count('id as count')
      .first();
    
    const aiReportCount = await knex('ai_reports')
      .where('patient_id', patientId)
      .count('id as count')
      .first();
    
    // 审计日志条目
    const auditLogEntry = {
      id: uuidv4(),
      admin_id: req.user_id,
      action_type: 'PATIENT_DETAIL_VIEW',
      target_id: patientId,
      action_details: JSON.stringify({
        patient_id: patientId,
        user_id: patient.userId
      }),
      created_at: new Date().toISOString()
    };
    
    await knex('admin_audit_logs').insert(auditLogEntry);
    
    res.json({
      ...patient,
      stats: {
        diseaseCount: diseaseCount ? diseaseCount.count : 0,
        recordCount: recordCount ? recordCount.count : 0,
        aiReportCount: aiReportCount ? aiReportCount.count : 0
      }
    });
  } catch (error) {
    console.error('[adminRoutes] 获取患者详情失败:', error);
    res.status(500).json({ error: '获取患者详情失败', message: error.message });
  }
});

// 删除患者
router.delete('/patients/:patientId', auth, adminOnly, async (req, res) => {
  try {
    const { patientId } = req.params;
    console.log(`[adminRoutes] 删除患者, 患者ID: ${patientId}`);
    
    // 获取患者信息用于审计日志
    const patient = await knex('patients')
      .where('id', patientId)
      .first();
    
    if (!patient) {
      return res.status(404).json({ error: '患者不存在' });
    }
    
    // 执行软删除 - 更新deleted_at
    await knex.transaction(async (trx) => {
      // 更新患者的deleted_at字段（保持不变，患者表使用deleted_at）
      await trx('patients')
        .where('id', patientId)
        .update({
          deleted_at: new Date().toISOString()
        });
      
      // 同时软删除与该患者相关的疾病
      await trx('diseases')
        .where('patient_id', patientId)
        .update({
          is_deleted: true,
          updated_at: new Date().toISOString()
        });
      
      // 同时软删除与该患者相关的记录
      await trx('records')
        .where('patient_id', patientId)
        .update({
          is_deleted: true,
          updated_at: new Date().toISOString()
        });
      
      // 记录审计日志
      const auditLogEntry = {
        id: uuidv4(),
        admin_id: req.user_id,
        action_type: 'PATIENT_DELETE',
        target_id: patientId,
        action_details: JSON.stringify({
          patient_id: patientId,
          patient_name: patient.name,
          user_id: patient.user_id
        }),
        created_at: new Date().toISOString()
      };
      
      await trx('admin_audit_logs').insert(auditLogEntry);
    });
    
    res.json({ 
      message: '患者数据已删除',
      patientId
    });
  } catch (error) {
    console.error('[adminRoutes] 删除患者失败:', error);
    res.status(500).json({ error: '删除患者失败', message: error.message });
  }
});

// 获取所有病理列表
router.get('/diseases', auth, adminOnly, async (req, res) => {
  try {
    console.log('[adminRoutes] 获取所有病理列表');
    
    // 查询参数
    const { 
      search, // 搜索关键字
      stage, // 阶段筛选
      type, // 类型筛选
      patientId, // 患者ID筛选
      page = 0, 
      pageSize = 50 
    } = req.query;
    
    // 构建基础查询 (仅包含表、连接和过滤条件)
    let baseQuery = knex('diseases')
      .leftJoin('patients', 'diseases.patient_id', 'patients.id')
      .leftJoin('users', 'diseases.user_id', 'users.id')
      .where('diseases.is_deleted', false);
    
    // 添加搜索条件
    if (search) {
      baseQuery = baseQuery.where(function() {
        this.where('diseases.name', 'like', `%${search}%`)
            .orWhere('patients.name', 'like', `%${search}%`);
      });
    }
    
    // 添加阶段筛选
    if (stage) {
      baseQuery = baseQuery.where('diseases.stage', stage);
    }
    
    // 添加类型筛选
    if (type) {
      // baseQuery = baseQuery.where('diseases.type', type); // 由于type列不存在，注释掉此筛选
      console.warn('[adminRoutes] Warning: diseases.type column does not exist, type filter ignored.');
    }
    
    // 添加患者ID筛选
    if (patientId) {
      baseQuery = baseQuery.where('diseases.patient_id', patientId);
    }
    
    // 获取总数用于分页 (克隆基础查询)
    const countClone = baseQuery.clone();
    const countResult = await countClone.count('diseases.id as totalDiseases').first();
    const totalCount = countResult && countResult.totalDiseases ? parseInt(countResult.totalDiseases.toString(), 10) : 0;
    
    // 获取病理数据 (克隆基础查询并添加select, orderBy, limit, offset)
    const dataClone = baseQuery.clone();
    let diseasesQuery = dataClone.select(
        'diseases.id',
        'diseases.name',
        'diseases.stage',
        'diseases.diagnosis_date as diagnosisDate',
        'diseases.description',
        'diseases.treatment',
        'diseases.patient_id as patientId',
        'patients.name as patientName',
        'diseases.user_id as userId',
        'users.username',
        'diseases.is_deleted as isDeleted',
        'diseases.created_at as createdAt'
      )
      .orderBy('diseases.created_at', 'desc');
    
    // 分页
    if (page !== undefined && pageSize !== undefined) {
      const numericPage = parseInt(page.toString(), 10);
      const numericPageSize = parseInt(pageSize.toString(), 10);
      diseasesQuery = diseasesQuery.limit(numericPageSize).offset(numericPage * numericPageSize);
    }
    
    // 获取病理数据
    const diseases = await diseasesQuery;
    
    console.log(`[adminRoutes] 查询到 ${diseases.length} 个病理, 总数: ${totalCount}`);
    
    // 审计日志条目
    const auditLogEntry = {
      id: uuidv4(),
      admin_id: req.user_id,
      action_type: 'DISEASES_VIEW',
      target_id: null,
      action_details: JSON.stringify({
        search,
        stage,
        type,
        patientId,
        count: diseases.length
      }),
      created_at: new Date().toISOString()
    };
    
    await knex('admin_audit_logs').insert(auditLogEntry);
    
    res.json({
      diseases,
      total: totalCount,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    });
  } catch (error) {
    console.error('[adminRoutes] 获取病理列表失败:', error);
    res.status(500).json({ error: '获取病理列表失败', message: error.message });
  }
});

// 删除病理
router.delete('/diseases/:diseaseId', auth, adminOnly, async (req, res) => {
  try {
    const { diseaseId } = req.params;
    console.log(`[adminRoutes] 删除病理, 病理ID: ${diseaseId}`);
    
    // 获取病理信息用于审计日志
    const disease = await knex('diseases')
      .select('id', 'name', 'patient_id', 'user_id')
      .where('id', diseaseId)
      .first();
    
    if (!disease) {
      return res.status(404).json({ error: '病理不存在' });
    }
    
    // 执行软删除 - 更新is_deleted
    await knex.transaction(async (trx) => {
      // 更新病理的is_deleted字段
      await trx('diseases')
        .where('id', diseaseId)
        .update({
          is_deleted: true
        });
      
      // 同时软删除与该病理相关的记录
      await trx('records')
        .where('disease_id', diseaseId)
        .update({
          is_deleted: true
        });
      
      // 记录审计日志
      const auditLogEntry = {
        id: uuidv4(),
        admin_id: req.user_id,
        action_type: 'DISEASE_DELETE',
        target_id: diseaseId,
        action_details: JSON.stringify({
          disease_id: diseaseId,
          disease_name: disease.name,
          patient_id: disease.patient_id,
          user_id: disease.user_id
        }),
        created_at: new Date().toISOString()
      };
      
      await trx('admin_audit_logs').insert(auditLogEntry);
    });
    
    res.json({ 
      message: '病理数据已删除',
      diseaseId
    });
  } catch (error) {
    console.error('[adminRoutes] 删除病理失败:', error);
    res.status(500).json({ error: '删除病理失败', message: error.message });
  }
});

// 获取所有就诊记录列表
router.get('/records', auth, adminOnly, async (req, res) => {
  try {
    console.log('[adminRoutes] 获取所有就诊记录列表');
    
    // 查询参数
    const { 
      search, // 搜索关键字
      recordType, // 记录类型筛选
      patientId, // 患者ID筛选
      diseaseId, // 病理ID筛选
      startDate, // 开始日期
      endDate, // 结束日期
      page = 0, 
      pageSize = 50 
    } = req.query;
    
    // 构建基础查询 (仅包含表、连接和过滤条件)
    let baseQuery = knex('records')
      .leftJoin('patients', 'records.patient_id', 'patients.id')
      .leftJoin('diseases', 'records.disease_id', 'diseases.id')
      .leftJoin('users', 'records.user_id', 'users.id')
      .where('records.is_deleted', false);
    
    // 添加搜索条件
    if (search) {
      baseQuery = baseQuery.where(function() {
        this.where('records.title', 'like', `%${search}%`)
            .orWhere('records.content', 'like', `%${search}%`)
            .orWhere('patients.name', 'like', `%${search}%`)
            .orWhere('diseases.name', 'like', `%${search}%`);
      });
    }
    
    // 添加记录类型筛选
    if (recordType) {
      baseQuery = baseQuery.where('records.record_type', recordType);
    }
    
    // 添加患者ID筛选
    if (patientId) {
      baseQuery = baseQuery.where('records.patient_id', patientId);
    }
    
    // 添加病理ID筛选
    if (diseaseId) {
      baseQuery = baseQuery.where('records.disease_id', diseaseId);
    }
    
    // 添加日期范围筛选
    if (startDate) {
      baseQuery = baseQuery.where('records.record_date', '>=', startDate);
    }
    
    if (endDate) {
      // 为结束日期添加一天，以包含整个结束日期
      const endDateObj = new Date(endDate);
      endDateObj.setDate(endDateObj.getDate() + 1);
      const nextDay = endDateObj.toISOString().split('T')[0];
      baseQuery = baseQuery.where('records.record_date', '<', nextDay);
    }
    
    // 获取总数用于分页 (克隆基础查询)
    const countClone = baseQuery.clone();
    const countResult = await countClone.count('records.id as totalRecords').first();
    const totalCount = countResult && countResult.totalRecords ? parseInt(countResult.totalRecords.toString(), 10) : 0;
    
    // 获取就诊记录数据 (克隆基础查询并添加select, orderBy, limit, offset)
    const dataClone = baseQuery.clone();
    let recordsQuery = dataClone.select(
        'records.id',
        'records.title',
        'records.content',
        'records.record_date as visitDate',
        'records.record_type as recordType',
        'records.patient_id as patientId',
        'patients.name as patientName',
        'diseases.name as diseaseName',
        'users.username',
        'records.created_at as createdAt'
      )
      .orderBy('records.created_at', 'desc');
    
    // 分页
    if (page !== undefined && pageSize !== undefined) {
      const numericPage = parseInt(page.toString(), 10);
      const numericPageSize = parseInt(pageSize.toString(), 10);
      recordsQuery = recordsQuery.limit(numericPageSize).offset(numericPage * numericPageSize);
    }
    
    // 获取就诊记录数据
    const records = await recordsQuery;
    
    // 添加记录类型中文标签
    const processedRecords = records.map(record => ({
      ...record,
      recordTypeLabel: RECORD_TYPE_LABELS[record.recordType] || record.recordType
    }));
    
    console.log(`[adminRoutes] 查询到 ${records.length} 个就诊记录, 总数: ${totalCount}`);
    
    // 审计日志条目
    const auditLogEntry = {
      id: uuidv4(),
      admin_id: req.user_id,
      action_type: 'RECORDS_VIEW',
      target_id: null,
      action_details: JSON.stringify({
        search,
        recordType,
        patientId,
        diseaseId,
        startDate,
        endDate,
        count: records.length
      }),
      created_at: new Date().toISOString()
    };
    
    await knex('admin_audit_logs').insert(auditLogEntry);
    
    res.json({
      records: processedRecords,
      total: totalCount,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    });
  } catch (error) {
    console.error('[adminRoutes] 获取就诊记录列表失败:', error);
    res.status(500).json({ error: '获取就诊记录列表失败', message: error.message });
  }
});

// 获取就诊记录详情
router.get('/records/:recordId', auth, adminOnly, async (req, res) => {
  try {
    const { recordId } = req.params;
    console.log(`[adminRoutes] 获取就诊记录详情, 记录ID: ${recordId}`);
    
    // 获取就诊记录数据
    const record = await knex('records')
      .select(
        'records.id',
        'records.title',
        'records.content',
        'records.record_date as visitDate',
        'records.record_type as recordType',
        'records.patient_id as patientId',
        'patients.name as patientName',
        'diseases.name as diseaseName',
        'users.username',
        'records.created_at as createdAt',
        'records.updated_at as updatedAt'
      )
      .leftJoin('patients', 'records.patient_id', 'patients.id')
      .leftJoin('diseases', 'records.disease_id', 'diseases.id')
      .leftJoin('users', 'records.user_id', 'users.id')
      .where('records.id', recordId)
      .where('records.is_deleted', false)
      .first();
    
    if (!record) {
      return res.status(404).json({ error: '就诊记录不存在' });
    }
    
    // 添加记录类型中文标签
    record.recordTypeLabel = RECORD_TYPE_LABELS[record.recordType] || record.recordType;
    
    // 审计日志条目
    const auditLogEntry = {
      id: uuidv4(),
      admin_id: req.user_id,
      action_type: 'RECORD_DETAIL_VIEW',
      target_id: recordId,
      action_details: JSON.stringify({
        record_id: recordId,
        patient_id: record.patientId,
        disease_id: record.diseaseId,
        user_id: record.userId
      }),
      created_at: new Date().toISOString()
    };
    
    await knex('admin_audit_logs').insert(auditLogEntry);
    
    res.json(record);
  } catch (error) {
    console.error('[adminRoutes] 获取就诊记录详情失败:', error);
    res.status(500).json({ error: '获取就诊记录详情失败', message: error.message });
  }
});

// 删除就诊记录
router.delete('/records/:recordId', auth, adminOnly, async (req, res) => {
  try {
    const { recordId } = req.params;
    console.log(`[adminRoutes] 删除就诊记录, 记录ID: ${recordId}`);
    
    // 获取记录信息用于审计日志
    const record = await knex('records')
      .select('id', 'title', 'patient_id', 'disease_id', 'user_id')
      .where('id', recordId)
      .first();
    
    if (!record) {
      return res.status(404).json({ error: '就诊记录不存在' });
    }
    
    // 执行软删除 - 更新is_deleted
    await knex.transaction(async (trx) => {
      // 更新记录的is_deleted字段
      await trx('records')
        .where('id', recordId)
        .update({
          is_deleted: true,
          updated_at: new Date().toISOString()
        });
      
      // 记录审计日志
      const auditLogEntry = {
        id: uuidv4(),
        admin_id: req.user_id,
        action_type: 'RECORD_DELETE',
        target_id: recordId,
        action_details: JSON.stringify({
          record_id: recordId,
          record_title: record.title,
          patient_id: record.patient_id,
          disease_id: record.disease_id,
          user_id: record.user_id
        }),
        created_at: new Date().toISOString()
      };
      
      await trx('admin_audit_logs').insert(auditLogEntry);
    });
    
    res.json({ 
      message: '就诊记录已删除',
      recordId
    });
  } catch (error) {
    console.error('[adminRoutes] 删除就诊记录失败:', error);
    res.status(500).json({ error: '删除就诊记录失败', message: error.message });
  }
});

// 获取授权关系列表
router.get('/authorizations', auth, adminOnly, async (req, res) => {
  try {
    console.log('[adminRoutes] 获取授权关系列表');
    
    const { 
      search, 
      status, 
      // accessType, // privacy_level in DB
      userId, 
      page = 0, 
      pageSize = 50 
    } = req.query;
    
    // 构建基础查询 (仅包含表、连接和过滤条件)
    let baseQuery = knex('user_authorizations')
      .leftJoin('users as grantor', 'user_authorizations.authorizer_id', 'grantor.id') // Corrected join column
      .leftJoin('users as grantee', 'user_authorizations.authorized_id', 'grantee.id'); // Corrected join column
    
    // 添加搜索条件
    if (search) {
      baseQuery = baseQuery.where(function() {
        this.where('grantor.username', 'like', `%${search}%`)
            .orWhere('grantee.username', 'like', `%${search}%`);
      });
    }
    
    // 添加状态筛选
    if (status) {
      baseQuery = baseQuery.where('user_authorizations.status', status);
    }
    
    // if (accessType) { // Filter by privacy_level if needed, after frontend adapts
    //   baseQuery = baseQuery.where('user_authorizations.privacy_level', accessType);
    // }
    
    // 添加用户ID筛选
    if (userId) {
      baseQuery = baseQuery.where(function() {
        this.where('user_authorizations.authorizer_id', userId) // Corrected column
            .orWhere('user_authorizations.authorized_id', userId); // Corrected column
      });
    }
    
    // 获取总数用于分页 (克隆基础查询)
    const countClone = baseQuery.clone();
    const countResult = await countClone.count('user_authorizations.id as totalAuths').first();
    const totalCount = countResult && countResult.totalAuths ? parseInt(countResult.totalAuths.toString(), 10) : 0;
    
    // 获取授权数据 (克隆基础查询并添加select, orderBy, limit, offset)
    const dataClone = baseQuery.clone();
    let authQuery = dataClone.select(
        'user_authorizations.id',
        'user_authorizations.authorizer_id as grantorId',
        'grantor.username as grantorName',
        'user_authorizations.authorized_id as granteeId',
        'grantee.username as granteeName',
        'user_authorizations.privacy_level as accessType',
        'user_authorizations.patient_id as targetId',
        knex.raw("CASE WHEN user_authorizations.patient_id IS NULL THEN 'ALL' ELSE 'PATIENT' END as scope"),
        'user_authorizations.status',
        'user_authorizations.created_at as createdAt',
        'user_authorizations.activated_at as expiresAt'
      )
      .orderBy('user_authorizations.created_at', 'desc');
    
    // 分页
    if (page !== undefined && pageSize !== undefined) {
      const numericPage = parseInt(page.toString(), 10);
      const numericPageSize = parseInt(pageSize.toString(), 10);
      authQuery = authQuery.limit(numericPageSize).offset(numericPage * numericPageSize);
    }
    
    // 获取授权关系数据
    const authorizations = await authQuery;

    // Post-process scope and targetName if necessary, current derivation is basic
    for (const auth of authorizations) {
      if (auth.scope === 'PATIENT' && auth.targetId) {
        const patient = await knex('patients')
          .select('name')
          .where('id', auth.targetId)
          .first();
        auth.targetName = patient ? patient.name : null;
      } else {
        auth.targetName = null; // Or some other representation for 'ALL'
      }
      // expiresAt might need re-evaluation based on actual logic for user_authorizations.activated_at or other date fields
    }

    console.log(`[adminRoutes] 查询到 ${authorizations.length} 个授权关系, 总数: ${totalCount}`);
    
    const auditLogEntry = {
      id: uuidv4(),
      admin_id: req.user_id, // Ensure req.user_id is populated by auth middleware
      action_type: 'AUTHORIZATIONS_VIEW',
      target_id: null,
      action_details: JSON.stringify({
        search,
        status,
        // accessType, 
        userId,
        count: authorizations.length
      }),
      created_at: new Date().toISOString()
    };
    
    await knex('admin_audit_logs').insert(auditLogEntry);
    
    res.json({
      authorizations,
      total: totalCount,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    });
  } catch (error) {
    console.error('[adminRoutes] 获取授权关系列表失败:', error);
    res.status(500).json({ error: '获取授权关系列表失败', message: error.message });
  }
});

// 获取授权关系详情
router.get('/authorizations/:authId', auth, adminOnly, async (req, res) => {
  try {
    const { authId } = req.params;
    console.log(`[adminRoutes] 获取授权关系详情, 授权ID: ${authId}`);
    
    const auth = await knex('user_authorizations') 
      .select(
        'user_authorizations.id',
        'user_authorizations.authorizer_id as grantorId', // Corrected
        'grantor.username as grantorName',
        'grantor.email as grantorEmail',
        'user_authorizations.authorized_id as granteeId', // Corrected
        'grantee.username as granteeName',
        'grantee.email as granteeEmail',
        'user_authorizations.privacy_level as accessType', // Corrected
        // 'user_authorizations.scope',
        // 'user_authorizations.target_id as targetId',
        'user_authorizations.patient_id as targetId', // Assuming patient_id is targetId
        knex.raw("CASE WHEN user_authorizations.patient_id IS NULL THEN 'ALL' ELSE 'PATIENT' END as scope"),
        'user_authorizations.status',
        'user_authorizations.created_at as createdAt',
        'user_authorizations.updated_at as updatedAt',
        // 'user_authorizations.expires_at as expiresAt'
        'user_authorizations.activated_at as expiresAt' // Assuming similar usage
      )
      .leftJoin('users as grantor', 'user_authorizations.authorizer_id', 'grantor.id') // Corrected
      .leftJoin('users as grantee', 'user_authorizations.authorized_id', 'grantee.id') // Corrected
      .where('user_authorizations.id', authId)
      .first();
    
    if (!auth) {
      return res.status(404).json({ error: '授权关系不存在' });
    }
    
    if (auth.scope === 'PATIENT' && auth.targetId) {
      const patient = await knex('patients')
        .select('name', 'id')
        .where('id', auth.targetId)
        .first();
      auth.targetName = patient ? patient.name : null;
      auth.targetInfo = patient || null;
    } else {
      auth.targetName = null;
      auth.targetInfo = null;
    }
    
    const auditLogEntry = {
      id: uuidv4(),
      admin_id: req.user_id, // Ensure req.user_id is populated
      action_type: 'AUTHORIZATION_DETAIL_VIEW',
      target_id: authId,
      action_details: JSON.stringify({
        auth_id: authId,
        grantor_id: auth.grantorId, // Now authorizer_id aliased
        grantee_id: auth.granteeId  // Now authorized_id aliased
      }),
      created_at: new Date().toISOString()
    };
    
    await knex('admin_audit_logs').insert(auditLogEntry);
    
    res.json(auth);
  } catch (error) {
    console.error('[adminRoutes] 获取授权关系详情失败:', error);
    res.status(500).json({ error: '获取授权关系详情失败', message: error.message });
  }
});

// 创建授权关系
router.post('/authorizations', auth, adminOnly, async (req, res) => {
  try {
    const {
      grantorId, // This will be authorizer_id
      granteeId, // This will be authorized_id
      accessType, // This will be privacy_level
      scope, // Need to map to patient_id or other logic
      targetId, // Potentially patient_id
      status,
      // expiresAt // Not directly in user_authorizations, consider activated_at or other field
      createdBy, // From migration: table.uuid('created_by').notNullable().references('id').inTable('users');
      privacyLevel // Explicitly use if frontend sends this, maps to privacy_level in DB
    } = req.body;
    
    console.log(`[adminRoutes] 创建授权关系: ${grantorId} -> ${granteeId}`);
    
    const authorizer_id = grantorId;
    const authorized_id = granteeId;
    const effectivePrivacyLevel = privacyLevel || accessType; // Use specific if available

    if (!authorizer_id || !authorized_id || !effectivePrivacyLevel || !status || !createdBy) {
      return res.status(400).json({ error: '缺少必填字段: grantorId, granteeId, accessType/privacyLevel, status, createdBy' });
    }
    
    const grantor = await knex('users').where('id', authorizer_id).first();
    const grantee = await knex('users').where('id', authorized_id).first();
    
    if (!grantor) return res.status(404).json({ error: '授权者不存在' });
    if (!grantee) return res.status(404).json({ error: '被授权者不存在' });
    if (authorizer_id === authorized_id) return res.status(400).json({ error: '不能自行授权' });

    let patient_id = null;
    if (scope === 'PATIENT' && targetId) {
      const patient = await knex('patients').where('id', targetId).first();
      if (!patient) return res.status(404).json({ error: '授权目标患者不存在' });
      patient_id = targetId;
    } else if (scope !== 'ALL' && scope !== 'PATIENT') {
        // Current DB design does not directly support DISEASE or RECORD scope for user_authorizations
        console.warn(`[adminRoutes] Scope ${scope} is not directly supported by user_authorizations table. Authorization will be general or patient-specific.`);
        // Depending on stricter requirements, might return 400 error here.
    }
    
    const authData = {
      id: uuidv4(),
      authorizer_id, // Corrected
      authorized_id, // Corrected
      privacy_level: effectivePrivacyLevel, // Corrected
      patient_id, // Derived from scope and targetId
      status,
      created_by: createdBy || req.user_id, // Ensure createdBy is provided or use logged-in admin
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      // activated_at: status === 'ACTIVE' ? new Date().toISOString() : null, // Example if activated_at is used for active status
    };
    
    await knex.transaction(async (trx) => {
      await trx('user_authorizations').insert(authData);
      
      const auditLogEntry = {
        id: uuidv4(),
        admin_id: req.user_id,
        action_type: 'AUTHORIZATION_CREATE',
        target_id: authData.id,
        action_details: JSON.stringify({
          authorizer_id,
          authorized_id,
          privacy_level: effectivePrivacyLevel,
          patient_id,
          status
        }),
        created_at: new Date().toISOString()
      };
      await trx('admin_audit_logs').insert(auditLogEntry);
    });
    
    const createdAuth = await knex('user_authorizations')
      .select(
        'user_authorizations.id',
        'user_authorizations.authorizer_id as grantorId',
        'grantor.username as grantorName',
        'user_authorizations.authorized_id as granteeId',
        'grantee.username as granteeName',
        'user_authorizations.privacy_level as accessType',
        knex.raw("CASE WHEN user_authorizations.patient_id IS NULL THEN 'ALL' ELSE 'PATIENT' END as scope"),
        'user_authorizations.patient_id as targetId',
        'user_authorizations.status',
        'user_authorizations.created_at as createdAt'
        // 'user_authorizations.activated_at as expiresAt' // Re-evaluate if needed
      )
      .leftJoin('users as grantor', 'user_authorizations.authorizer_id', 'grantor.id')
      .leftJoin('users as grantee', 'user_authorizations.authorized_id', 'grantee.id')
      .where('user_authorizations.id', authData.id)
      .first();
    
    res.status(201).json(createdAuth);
  } catch (error) {
    console.error('[adminRoutes] 创建授权关系失败:', error);
    if (error.code === 'SQLITE_CONSTRAINT' && error.message.includes('UNIQUE constraint failed: user_authorizations.authorizer_id, user_authorizations.authorized_id, user_authorizations.patient_id')) {
      return res.status(409).json({ error: '已存在相同的授权关系 (授权人、被授权人、患者的组合必须唯一)' });
    }
    res.status(500).json({ error: '创建授权关系失败', message: error.message });
  }
});

// 更新授权关系
router.put('/authorizations/:authId', auth, adminOnly, async (req, res) => {
  try {
    const { authId } = req.params;
    const {
      accessType, // This will be privacy_level
      scope,      // Need to map to patient_id or other logic
      targetId,   // Potentially patient_id
      status,
      // expiresAt // Not directly in user_authorizations, consider activated_at or other field
      privacyLevel // Explicitly use if frontend sends this
    } = req.body;
    
    console.log(`[adminRoutes] 更新授权关系: ${authId}`);
    
    const effectivePrivacyLevel = privacyLevel || accessType;

    if (!effectivePrivacyLevel || !status) { // Scope can be tricky, might not be required for all updates
      return res.status(400).json({ error: '缺少必填字段: accessType/privacyLevel, status' });
    }
    
    const authEntry = await knex('user_authorizations').where('id', authId).first();
    if (!authEntry) return res.status(404).json({ error: '授权关系不存在' });

    let patient_id = authEntry.patient_id; // Default to existing
    if (scope === 'PATIENT' && targetId) {
      const patient = await knex('patients').where('id', targetId).first();
      if (!patient) return res.status(404).json({ error: '授权目标患者不存在' });
      patient_id = targetId;
    } else if (scope === 'ALL') {
      patient_id = null; // Explicitly set to null for 'ALL' scope
    } else if (scope && scope !== 'PATIENT') {
        console.warn(`[adminRoutes] Update with scope ${scope} is not directly changing patient_id unless it's PATIENT or ALL.`);
    }
    
    const updateData = {
      privacy_level: effectivePrivacyLevel, // Corrected
      patient_id, // Updated based on scope and targetId
      status,
      updated_at: new Date().toISOString(),
      // activated_at: status === 'ACTIVE' && !authEntry.activated_at ? new Date().toISOString() : authEntry.activated_at, // Example logic
      // revoked_at: status === 'REVOKED' && !authEntry.revoked_at ? new Date().toISOString() : authEntry.revoked_at, // Example logic
    };

    // Handle expiresAt if it means to clear activated_at or revoked_at, or if it maps to another DB field
    // if (expiresAt === null) { updateData.activated_at = null; } 
    // else if (expiresAt) { updateData.activated_at = new Date(expiresAt).toISOString(); }
    
    await knex.transaction(async (trx) => {
      await trx('user_authorizations')
        .where('id', authId)
        .update(updateData);
      
      const auditLogEntry = {
        id: uuidv4(),
        admin_id: req.user_id,
        action_type: 'AUTHORIZATION_UPDATE',
        target_id: authId,
        action_details: JSON.stringify({
          before: {
            privacy_level: authEntry.privacy_level,
            patient_id: authEntry.patient_id,
            status: authEntry.status,
            // activated_at: authEntry.activated_at
          },
          after: {
            privacy_level: effectivePrivacyLevel,
            patient_id,
            status,
            // activated_at: updateData.activated_at
          }
        }),
        created_at: new Date().toISOString()
      };
      await trx('admin_audit_logs').insert(auditLogEntry);
    });
    
    const updatedAuth = await knex('user_authorizations')
      .select(
        'user_authorizations.id',
        'user_authorizations.authorizer_id as grantorId',
        'grantor.username as grantorName',
        'user_authorizations.authorized_id as granteeId',
        'grantee.username as granteeName',
        'user_authorizations.privacy_level as accessType',
        knex.raw("CASE WHEN user_authorizations.patient_id IS NULL THEN 'ALL' ELSE 'PATIENT' END as scope"),
        'user_authorizations.patient_id as targetId',
        'user_authorizations.status',
        'user_authorizations.created_at as createdAt'
        // 'user_authorizations.activated_at as expiresAt'
      )
      .leftJoin('users as grantor', 'user_authorizations.authorizer_id', 'grantor.id')
      .leftJoin('users as grantee', 'user_authorizations.authorized_id', 'grantee.id')
      .where('user_authorizations.id', authId)
      .first();

    // Post-process targetName for the response
    if (updatedAuth.scope === 'PATIENT' && updatedAuth.targetId) {
      const patient = await knex('patients')
        .select('name')
        .where('id', updatedAuth.targetId)
        .first();
      updatedAuth.targetName = patient ? patient.name : null;
    } else {
      updatedAuth.targetName = null;
    }
    
    res.json(updatedAuth);
  } catch (error) {
    console.error('[adminRoutes] 更新授权关系失败:', error);
    if (error.code === 'SQLITE_CONSTRAINT' && error.message.includes('UNIQUE constraint failed')) {
      return res.status(409).json({ error: '更新失败，可能导致已存在的唯一授权冲突 (授权人、被授权人、患者的组合必须唯一)' });
    }
    res.status(500).json({ error: '更新授权关系失败', message: error.message });
  }
});

// 删除授权关系
router.delete('/authorizations/:authId', auth, adminOnly, async (req, res) => {
  try {
    const { authId } = req.params;
    console.log(`[adminRoutes] 删除授权关系: ${authId}`);
    
    const auth = await knex('user_authorizations') 
      .select(
        'id', 
        'authorizer_id', // Corrected
        'authorized_id', // Corrected
        'privacy_level', // Corrected
        'patient_id',    // Corrected
        'status'
      )
      .where('id', authId)
      .first();
    
    if (!auth) {
      return res.status(404).json({ error: '授权关系不存在' });
    }
    
    await knex.transaction(async (trx) => {
      await trx('user_authorizations')
        .where('id', authId)
        .delete();
      
      const auditLogEntry = {
        id: uuidv4(),
        admin_id: req.user_id,
        action_type: 'AUTHORIZATION_DELETE',
        target_id: authId,
        action_details: JSON.stringify({
          auth_id: authId,
          authorizer_id: auth.authorizer_id, // Corrected
          authorized_id: auth.authorized_id, // Corrected
          privacy_level: auth.privacy_level, // Corrected
          patient_id: auth.patient_id,       // Corrected
          status: auth.status
        }),
        created_at: new Date().toISOString()
      };
      await trx('admin_audit_logs').insert(auditLogEntry);
    });
    
    res.json({ 
      message: '授权关系已删除',
      authId
    });
  } catch (error) {
    console.error('[adminRoutes] 删除授权关系失败:', error);
    res.status(500).json({ error: '删除授权关系失败', message: error.message });
  }
});

module.exports = router;