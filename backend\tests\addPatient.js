const axios = require('axios');
const jwt = require('jsonwebtoken');
const knex = require('knex')(require('../knexfile').development);

// API基础URL
const API_URL = 'http://localhost:3000';
const JWT_SECRET = 'your_jwt_secret';

// 获取测试用户并生成令牌
async function getTestUserAndToken() {
  try {
    const user = await knex('users').where({ username: 'testuser' }).first();
    
    if (!user) {
      throw new Error('测试用户不存在，请先运行 createTestUser.js');
    }
    
    const token = jwt.sign({ id: user.id, username: user.username }, JWT_SECRET, {
      expiresIn: '15m'
    });
    
    return { user, token };
  } catch (error) {
    console.error('获取测试用户失败:', error);
    throw error;
  }
}

// 测试添加患者
async function testAddPatient(token) {
  try {
    console.log('开始测试添加患者...');
    
    // 测试患者数据
    const patient = {
      name: '测试患者',
      gender: '男',
      phoneNumber: '13800138000',
      birthDate: new Date().toISOString().split('T')[0],
      bloodType: 'A'
    };
    
    // 发送请求
    const response = await axios.post(`${API_URL}/patients`, patient, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    
    console.log('添加患者成功!');
    console.log('患者数据:', response.data);
    
    return response.data;
  } catch (error) {
    console.error('添加患者失败:', error.response?.data || error.message);
    throw error;
  }
}

// 测试获取患者列表
async function testGetPatients(token) {
  try {
    console.log('\n开始测试获取患者列表...');
    
    const response = await axios.get(`${API_URL}/patients`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    
    console.log('获取患者列表成功!');
    console.log(`共有 ${response.data.length} 名患者`);
    console.log('患者列表:', response.data);
    
    return response.data;
  } catch (error) {
    console.error('获取患者列表失败:', error.response?.data || error.message);
    throw error;
  }
}

// 测试删除患者
async function testDeletePatient(token, patientId) {
  try {
    console.log(`\n开始测试删除患者 (ID: ${patientId})...`);
    
    const response = await axios.delete(`${API_URL}/patients/${patientId}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    
    console.log('删除患者成功!');
    console.log('响应:', response.data);
    
    return response.data;
  } catch (error) {
    console.error('删除患者失败:', error.response?.data || error.message);
    throw error;
  }
}

// 运行所有测试
async function runTests() {
  try {
    // 获取测试用户和令牌
    const { token } = await getTestUserAndToken();
    
    // 添加患者测试
    const newPatient = await testAddPatient(token);
    
    // 获取患者列表测试
    await testGetPatients(token);
    
    // 删除患者测试
    if (newPatient && newPatient.id) {
      await testDeletePatient(token, newPatient.id);
      
      // 确认患者已被删除
      console.log('\n验证患者已被删除...');
      const patients = await testGetPatients(token);
      const deleted = !patients.some(p => p.id === newPatient.id);
      console.log(`患者 ${newPatient.id} 已删除: ${deleted ? '是' : '否'}`);
    }
    
    console.log('\n所有测试完成!');
  } catch (error) {
    console.error('测试过程中发生错误:', error);
  } finally {
    await knex.destroy();
  }
}

// 运行测试
runTests(); 