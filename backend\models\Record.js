const { Model, snakeCaseMappers } = require('objection');
const path = require('path');

class Record extends Model {
  // 添加下划线命名映射配置
  static get columnNameMappers() {
    return snakeCaseMappers();
  }

  static get tableName() {
    return 'records';
  }

  static get idColumn() {
    return 'id';
  }

  // 数据转换钩子
  $formatJson(json) {
    json = super.$formatJson(json);
    
    // 确保data是对象
    if (json.data && typeof json.data === 'string') {
      try {
        json.data = JSON.parse(json.data);
      } catch (e) {
        console.log('无法解析JSON data字段:', e);
        json.data = {};
      }
    }
    
    return json;
  }
  
  $parseJson(json, opt) {
    const parsed = super.$parseJson(json, opt);
    
    console.log('Record.$parseJson 接收数据:', JSON.stringify(parsed, null, 2));
    
    // 处理data字段
    if (parsed.data !== undefined) {
      if (typeof parsed.data === 'string') {
        try {
          parsed.data = JSON.parse(parsed.data);
          console.log('Record: 成功解析data字符串为对象');
        } catch (e) {
          console.log('Record: 解析data字段失败:', e);
          parsed.data = {};
        }
      } else if (parsed.data === null) {
        parsed.data = {};
      }
    }
    
    // 处理severity字段 - 保持为枚举字符串值
    if (parsed.severity !== undefined) {
      if (typeof parsed.severity === 'string') {
        // 将字符串转为大写，确保格式一致
        const validSeverities = ['MILD', 'MODERATE', 'SEVERE', 'CRITICAL'];
        const normalizedSeverity = parsed.severity.toUpperCase?.() || parsed.severity;
        
        // 如果是数字字符串，转换为对应的枚举
        if (!isNaN(parseInt(normalizedSeverity))) {
          const severityNum = parseInt(normalizedSeverity);
          switch (severityNum) {
            case 1: parsed.severity = 'MILD'; break;
            case 2: parsed.severity = 'MODERATE'; break;
            case 3: parsed.severity = 'SEVERE'; break;
            case 4: parsed.severity = 'CRITICAL'; break;
            default: parsed.severity = 'MODERATE'; // 默认为中等
          }
        } else if (validSeverities.includes(normalizedSeverity)) {
          // 如果是有效的枚举值，直接使用标准化的大写值
          parsed.severity = normalizedSeverity;
        } else {
          // 无效的字符串值，设为默认值
          parsed.severity = 'MODERATE';
        }
      } else if (typeof parsed.severity === 'number') {
        // 如果是数字，转换为对应的枚举
        switch (parsed.severity) {
          case 1: parsed.severity = 'MILD'; break;
          case 2: parsed.severity = 'MODERATE'; break;
          case 3: parsed.severity = 'SEVERE'; break;
          case 4: parsed.severity = 'CRITICAL'; break;
          default: parsed.severity = 'MODERATE'; // 默认为中等
        }
      } else if (parsed.severity === null) {
        parsed.severity = 'MODERATE'; // 默认为中等
      }
    }
    
    console.log('Record.$parseJson 处理后数据:', JSON.stringify(parsed, null, 2));
    return parsed;
  }

  // 字段验证规则
  static get jsonSchema() {
    return {
      type: 'object',
      required: ['title'],
      properties: {
        id: { type: 'string' },
        title: { type: 'string', minLength: 1, maxLength: 255 },
        content: { type: ['string', 'null'] },
        data: { 
          anyOf: [
            { type: 'object' },
            { type: 'null' },
            { type: 'string' }  // 允许字符串类型，会在模型方法中处理
          ],
          default: {}
        },
        recordDate: { type: ['string', 'null'] },
        description: { type: ['string', 'null'], maxLength: 1000 },
        patientId: { type: ['string', 'null'] },
        diseaseId: { type: ['string', 'null'] },
        userId: { type: ['string', 'null'] },  // 允许为null，以便在路由中设置
        user_id: { type: ['string', 'null'] },
        createdBy: { type: ['string', 'null'] },
        updatedBy: { type: ['string', 'null'] }, // 更新操作执行者
        deletedBy: { type: ['string', 'null'] }, // 删除操作执行者
        recordType: { 
          anyOf: [
            { type: 'string' },
            { type: 'array', items: { type: 'string' } }
          ]
        },
        record_type: { 
          anyOf: [
            { type: 'string' },
            { type: 'array', items: { type: 'string' } }
          ]
        },
        reference_id: { type: ['string', 'null'] }, // 添加reference_id字段
        primaryType: { type: ['string', 'null'] },
        typeTagsJson: { type: ['string', 'null'] },
        stageTags: { type: ['string', 'null'] },
        stageNode: { type: ['string', 'null'] },
        stagePhase: { type: ['string', 'null'] },
        severity: { 
          anyOf: [
            { type: 'string' },
            { type: 'integer' }, 
            { type: 'null' }
          ],
          default: 'MODERATE'
        },
        isPrivate: { type: 'boolean', default: false },
        isImportant: { type: 'boolean', default: false },
        isDeleted: { type: 'boolean', default: false }, // 添加is_deleted字段
        is_deleted: { type: 'boolean', default: false }, // 添加下划线命名版本
        customTags: { type: ['string', 'null'] },
        deletedAt: { type: ['string', 'null'] }
      }
    };
  }

  // 关系定义
  static get relationMappings() {
    const User = require('./User');
    const Patient = require('./Patient');
    const Disease = require('./Disease');
    const Attachment = require('./Attachment');

    return {
      // 用户关系
      user: {
        relation: Model.BelongsToOneRelation,
        modelClass: User,
        join: {
          from: 'records.userId',
          to: 'users.id'
        }
      },
      // 创建者关系
      creator: {
        relation: Model.BelongsToOneRelation,
        modelClass: User,
        join: {
          from: 'records.createdBy',
          to: 'users.id'
        }
      },
      // 更新者关系
      updater: {
        relation: Model.BelongsToOneRelation,
        modelClass: User,
        join: {
          from: 'records.updatedBy',
          to: 'users.id'
        }
      },
      // 删除者关系
      deleter: {
        relation: Model.BelongsToOneRelation,
        modelClass: User,
        join: {
          from: 'records.deletedBy',
          to: 'users.id'
        }
      },
      // 患者关系
      patient: {
        relation: Model.BelongsToOneRelation,
        modelClass: Patient,
        join: {
          from: 'records.patientId',
          to: 'patients.id'
        }
      },
      // 疾病关系
      disease: {
        relation: Model.BelongsToOneRelation,
        modelClass: Disease,
        join: {
          from: 'records.diseaseId',
          to: 'diseases.id'
        }
      },
      // 附件关系
      attachments: {
        relation: Model.HasManyRelation,
        modelClass: Attachment,
        join: {
          from: 'records.id',
          to: 'attachments.record_id'
        }
      }
    };
  }

  // 软删除方法
  softDelete(deletedById) {
    const updateData = {
      deleted_at: new Date().toISOString(),
      is_deleted: true
    };
    
    // 如果提供了删除者ID，记录它
    if (deletedById) {
      updateData.deleted_by = deletedById;
    }
    
    return this.$query().patch(updateData);
  }

  // 获取类型标签
  get typeTags() {
    if (!this.typeTagsJson) return [];
    try {
      return JSON.parse(this.typeTagsJson);
    } catch (e) {
      return [];
    }
  }

  // 设置类型标签
  set typeTags(value) {
    this.typeTagsJson = JSON.stringify(value);
  }

  // 获取自定义标签
  get tags() {
    if (!this.customTags) return [];
    try {
      return this.customTags.split(',').filter(tag => tag.trim());
    } catch (e) {
      return [];
    }
  }

  // 设置自定义标签
  set tags(value) {
    if (Array.isArray(value)) {
      this.customTags = value.join(',');
    }
  }
}

module.exports = Record; 