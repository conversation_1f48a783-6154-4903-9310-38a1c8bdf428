export interface Patient {
  id: string;
  name: string;
  gender: string;
  phoneNumber: string;
  email: string;
  birthDate: string;
  idCard: string;
  medicareCard: string;
  medicareLocation: string;
  address: string;
  emergencyContactName: string;
  emergencyContactPhone: string;
  emergencyContactRelationship: string;
  pastMedicalHistory: string;
  familyMedicalHistory: string;
  allergyHistory: string;
  bloodType: string;
  lastVisitDate: string;
  userId: string;
  isPrimary?: number;
  height?: number | string;
  weight?: number | string;
  bmi?: number;
  createdAt: string;
  updatedAt: string;
  isAuthorized?: boolean;
} 