# 数据库命名规范迁移计划

## 背景

我们当前的数据库表结构中存在列名命名不一致的问题，有些使用驼峰命名(camelCase)，有些使用下划线命名(snake_case)。为了确保在将来迁移到PostgreSQL等其他数据库时减少风险，并提高代码库一致性，我们决定统一使用下划线命名规范。

## 目标

1. 所有数据库表名和列名统一使用下划线命名(snake_case)
2. 所有直接使用knex的代码更新为使用下划线命名的列名
3. 由于模型层已经配置了Objection.js的`snakeCaseMappers()`，应用代码中仍使用驼峰命名(camelCase)

## 实施计划

### 阶段一：迁移准备

1. 创建并记录当前所有表结构 ✅
2. 创建完整的列名映射表（驼峰命名 → 下划线命名）✅
3. 创建命名规范文档 ✅
4. 创建测试环境备份数据库

### 阶段二：数据库迁移

1. 执行数据库迁移脚本`20240720_normalize_column_names.js`
   - 此脚本将所有驼峰命名的列改为下划线命名
   - 包含回滚功能，以防需要恢复
2. 验证迁移结果，确保所有列名已正确转换
3. 更新所有现有的迁移文件，以反映新的命名规范
   - 虽然不会重新执行，但保持代码库一致性

### 阶段三：代码更新

1. 更新`ensureRecordsTable`等动态创建表的函数 ✅
2. 更新所有直接使用knex的查询，使用下划线命名
   - `router.post('/')`创建记录接口 ✅
   - `router.get('/')`查询记录接口
   - `router.put('/:id')`更新记录接口
   - `router.delete('/:id')`删除记录接口
   - 其他使用knex的地方
3. 确保所有新增代码遵循命名规范

### 阶段四：测试与验证

1. 执行完整的API测试，确保所有功能正常工作
2. 测试记录创建、查询、更新和删除操作
3. 验证所有与数据库交互的功能都能正常工作

### 阶段五：部署计划

1. 数据库备份
2. 停机时间窗口通知（如需要）
3. 部署迁移脚本和更新后的代码
4. 验证生产环境功能
5. 有问题时回滚计划

## 时间线

- 准备阶段：1天
- 迁移实施：1天
- 测试与验证：2天
- 部署：半天
- 总计：4.5天

## 风险与缓解措施

| 风险 | 缓解措施 |
|------|----------|
| 迁移失败导致数据损坏 | 事先创建完整数据备份，确保回滚脚本正常工作 |
| 部分功能在新命名规范下无法工作 | 全面测试所有API和功能点，特别是使用直接SQL和knex的地方 |
| 前端API交互问题 | 由于Objection.js的`snakeCaseMappers()`会自动处理转换，前端API应该不受影响 |

## 后续工作

1. 更新开发指南，明确命名规范要求
2. 添加代码质量检查，以防止创建不符合命名规范的新代码
3. 持续监控应用性能，确保命名规范变更没有引入性能问题 