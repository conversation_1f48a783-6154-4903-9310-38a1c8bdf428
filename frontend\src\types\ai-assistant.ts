// API响应类型
export interface APIResponse<T> {
  status: 'SUCCESS' | 'ERROR' | 'PROCESSING';
  message?: string;
  data?: T;
}

// AI报告参数
// 定义创建AI报告时所需的参数类型
export interface CreateAIReportParams {
  diseaseId: string; // 病理ID，这是必需的
  patientId: string; // 患者ID，这是必需的
  
  // 根据 aiReportService.ts 中的使用情况，添加 templateType 属性
  // 如果 templateType 是可选的，可以使用问号: templateType?: string;
  // 如果它是必需的，则为: templateType: string;
  // 假设它是可选的，并且有一个默认值 'COMPREHENSIVE_ANALYSIS'
  templateType?: string; // AI报告的模板类型，例如：'COMPREHENSIVE_ANALYSIS', 'QUICK_SUMMARY' 等

  // 根据 aiReportService.ts 中的使用情况，确保 targetRegion 属性存在
  // 如果 targetRegion 是可选的，可以使用问号: targetRegion?: string;
  // 假设它是可选的
  targetRegion?: string; // 目标区域，用于医院推荐等
}

// AI报告响应
export interface CreateAIReportResponse {
  aiReport: AIReport | null | undefined;
  recordId: string;
  status: 'SUCCESS' | 'FAILED' | 'ALREADY_PROCESSING';
  message?: string;
}

// 报告可见性配置
export interface ReportVisibilityConfig {
  userVisibleFields: string[];
  serviceVisibleFields: string[];
}

// 治疗方案选项
export interface TreatmentOption {
  name: string;
  description: string;
  suitabilityScore?: number;
  prognosisData?: {
    survivalRate?: string;
    remissionRate?: string;
    recurrenceRisk?: string;
  };
  budgetEstimation?: {
    minCost: number;
    maxCost: number;
    currency?: string;
    insuranceCoverage?: string;
  };
}

// 医院信息
export interface Hospital {
  name: string;
  level: string;
  department: string;
  matchScore: number;
  reasons?: string[];
  advantages?: string[];
  contact?: string;
  contactInfo?: {
    website?: string;
    phone?: string;
    wechatPublic?: string;
    appointmentPlatform?: string;
  };
}

// BMI推荐
export interface BMIRecommendations {
  recommendations: string[];
  bmiValue?: number;
  bmiStatus?: {
    status: string;
    color: string;
  };
}

// 预算评估
export interface BudgetEstimation {
  low: {
    range: string;
    tradeOffs: string[];
  };
  medium: {
    range: string;
    tradeOffs: string[];
  };
  high: {
    range: string;
    tradeOffs: string[];
  };
}

/**
 * AI报告状态
 */
export type AIReportStatus = 
  | 'PENDING'      // 等待处理
  | 'PROCESSING'   // 处理中
  | 'COMPLETED'    // 已完成
  | 'FAILED'       // 失败
  | 'UNKNOWN';     // 未知状态

/**
 * AI报告
 */
export interface AIReport {
  id: string;
  diseaseId: string;
  patientId: string;
  userId: string;
  title: string;
  templateType: string;
  status: AIReportStatus;
  content: AIReportContent;
  recordId?: string;
  errorMessage?: string;  // 添加错误信息字段
  patientName?: string; // 添加患者名称，可选
  diseaseName?: string; // 添加病理名称，可选
  createdAt: string;
  updatedAt: string;
  anonymizedInfo?: { [key: string]: string };
  llmRawResponse?: string;
  pdfUrl?: string;
}

// 匿名化信息类型
export interface AnonymizedInfo {
  originalName: string;
  anonymizedName: string;
}

// AI报告内容
export interface AIReportContent {
  summary: string;
  differentialDiagnosis: {
    possibleConditions: Array<{
      condition: string;
      probability: number;
      description: string;
    }>;
  };
  emergencyGuidance: {
    isEmergency: boolean;
    immediateActions: string[];
    nextSteps: string[];
  };
  hospitalRecommendations: {
    targetRegion: string;
    hospitals: Hospital[];
  };
  treatmentPlan: {
    options: TreatmentOption[];
    followUp?: string[];
  };
  lifestyleAndMentalHealth: {
    lifestyle: {
      diet: string[];
      exercise: string[];
      habits: string[];
    };
    mentalHealth: {
      copingStrategies: string[];
      resources: string[];
    };
  };
  dashboardData: {
    status: string;
    trend: string;
    riskLevel: string;
    isEmergency: boolean;
    topHospital: string;
    budgetRange: string;
  };
  riskWarnings: string[];
  is_chronic_disease: boolean;
  bmiRecommendations?: BMIRecommendations;
  budgetEstimation?: BudgetEstimation;
  disclaimer?: string;
}

// AI报告创建接口的真实响应类型
export interface CreateAIReportAPIResponse {
  status: 'SUCCESS' | 'ERROR' | 'PROCESSING' | 'ALREADY_PROCESSING';
  message?: string;
  aiReport?: AIReport | null;
  recordId?: string;
}