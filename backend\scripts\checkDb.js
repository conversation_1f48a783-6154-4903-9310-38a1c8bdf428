// 数据库检查脚本
const path = require('path');
const db = require('../config/database');

async function checkDatabase() {
  console.log('正在检查数据库...');
  
  try {
    // 1. 检查授权表结构
    console.log('\n==== 授权表结构 ====');
    const authTableInfo = await db.table('user_authorizations').columnInfo();
    console.log(authTableInfo);
    
    // 2. 查看授权记录示例
    console.log('\n==== 授权记录示例 ====');
    const authSamples = await db.table('user_authorizations').select('*').limit(3);
    console.log(JSON.stringify(authSamples, null, 2));
    
    // 3. 检查记录表结构
    console.log('\n==== 记录表结构 ====');
    const recordTableInfo = await db.table('records').columnInfo();
    console.log(recordTableInfo);
    
    // 4. 查看记录示例
    console.log('\n==== 记录示例 ====');
    const recordSamples = await db.table('records').select('*').limit(3);
    console.log(JSON.stringify(recordSamples, null, 2));
    
    // 5. 检查服务记录表结构
    console.log('\n==== 服务记录表结构 ====');
    const serviceRecordTableInfo = await db.table('service_records').columnInfo();
    console.log(serviceRecordTableInfo);
    
    // 6. 查看服务记录示例
    console.log('\n==== 服务记录示例 ====');
    const serviceRecordSamples = await db.table('service_records').select('*').limit(3);
    console.log(JSON.stringify(serviceRecordSamples, null, 2));
    
    // 7. 检查用户表的BASIC权限用户
    console.log('\n==== BASIC权限的授权关系 ====');
    const basicAuthUsers = await db.table('user_authorizations')
      .where('privacy_level', 'BASIC')
      .select('*')
      .limit(5);
    console.log(JSON.stringify(basicAuthUsers, null, 2));
  } catch (error) {
    console.error('数据库检查失败:', error);
  } finally {
    // 关闭数据库连接
    await db.destroy();
    console.log('\n数据库检查完成');
  }
}

checkDatabase(); 