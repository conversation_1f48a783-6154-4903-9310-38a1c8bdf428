/**
 * 服务记录管理页面
 * 
 * 本页面用于管理服务用户为授权用户创建的记录
 * 
 * 最近修改:
 * - 优化数据加载逻辑，支持多种字段名格式
 * - 首先使用getServiceRecordsByContext API获取精确匹配的记录
 * - 如果上下文API无结果，则使用getServiceRecordsByAuth API并进行本地过滤
 * - 增强了调试日志，帮助识别记录匹配问题
 */

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  TablePagination,
  Chip,
  Snackbar,
  Card,
  CardContent,
  useTheme,
  // useMediaQuery, // Removed
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Switch,
  InputAdornment,
  Avatar
} from '@mui/material';
import { alpha, ThemeProvider, createTheme } from '@mui/material/styles';

// 图标
import AddIcon from '@mui/icons-material/Add';
import SearchIcon from '@mui/icons-material/Search';
import VisibilityOutlined from '@mui/icons-material/VisibilityOutlined';
import EditOutlined from '@mui/icons-material/EditOutlined';
import DeleteOutlined from '@mui/icons-material/DeleteOutlined';
// import BookmarkIcon from '@mui/icons-material/Bookmark'; // Removed
// import ExpandMoreIcon from '@mui/icons-material/ExpandMore'; // Removed
// import ExpandLessIcon from '@mui/icons-material/ExpandLess'; // Removed
import RefreshIcon from '@mui/icons-material/Refresh';
import DescriptionOutlined from '@mui/icons-material/DescriptionOutlined';
import SickOutlined from '@mui/icons-material/SickOutlined';
import HealthAndSafetyOutlined from '@mui/icons-material/HealthAndSafetyOutlined';
import ScienceOutlined from '@mui/icons-material/ScienceOutlined';
import MedicalInformationOutlined from '@mui/icons-material/MedicalInformationOutlined';
import MedicationOutlined from '@mui/icons-material/MedicationOutlined';
import NoteAltOutlined from '@mui/icons-material/NoteAltOutlined';
import PersonOutlined from '@mui/icons-material/PersonOutlined';
import AccessTimeOutlined from '@mui/icons-material/AccessTimeOutlined';
import GroupOutlined from '@mui/icons-material/GroupOutlined';

// 服务与上下文
import { useServiceUserContext } from '../context/ServiceUserContext';
import ServiceContextBar from '../components/service/ServiceContextBar';
import ServiceContextSelector from '../components/service/ServiceContextSelector';

// 数据服务
import * as serviceRecordService from '../services/serviceRecordService';
import { useAuthStore } from '../store/authStore';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';

// 类型定义
import { 
  RecordTypeEnum, 
  RecordTypeNames, 
  SeverityEnum, 
  SeverityNames 
} from '../types/recordEnums';

// 从RecordManagePage复用的常量和工具函数
// 个人标签主色
const PERSONAL_TAG_COLOR = '#3498db';
const PERSONAL_TAG_LIGHT_COLOR = `${PERSONAL_TAG_COLOR}20`; // 20%透明度的背景色，增加可见度

// 记录类型颜色映射 - 使用枚举值
const recordTypeColors: Record<string, {main: string, light: string, dark: string, icon: React.ReactNode}> = {
  // 基本类型
  [RecordTypeEnum.SELF_DESCRIPTION]: {
    main: '#607d8b',  // 蓝灰色
    light: '#eceff1',
    dark: '#37474f',
    icon: <DescriptionOutlined fontSize="small" />
  },
  [RecordTypeEnum.SYMPTOM]: {
    main: '#d32f2f',  // 红色
    light: '#ffebee',
    dark: '#b71c1c',
    icon: <SickOutlined fontSize="small" />
  },
  [RecordTypeEnum.EXAMINATION]: {
    main: '#7b1fa2',  // 紫色
    light: '#f3e5f5',
    dark: '#4a148c',
    icon: <HealthAndSafetyOutlined fontSize="small" />
  },
  [RecordTypeEnum.LAB_TEST]: {
    main: '#9c27b0',  // 浅紫色
    light: '#f3e5f5',
    dark: '#6a1b9a',
    icon: <ScienceOutlined fontSize="small" />
  },
  [RecordTypeEnum.DIAGNOSIS]: {
    main: '#1976d2',  // 蓝色
    light: '#e3f2fd',
    dark: '#0d47a1',
    icon: <MedicalInformationOutlined fontSize="small" />
  },
  [RecordTypeEnum.TREATMENT]: {
    main: '#388e3c',  // 绿色
    light: '#e8f5e9',
    dark: '#1b5e20',
    icon: <MedicationOutlined fontSize="small" />
  },
  // 默认
  'OTHER': {
    main: '#757575',  // 灰色
    light: '#f5f5f5',
    dark: '#424242',
    icon: <NoteAltOutlined fontSize="small" />
  }
  // ... 添加其他记录类型的颜色映射
};

// 严重程度颜色映射 - This was marked as unused by ESLint
// const severityColors: Record<string, {main: string, light: string, dark: string}> = {
//   [SeverityEnum.CRITICAL]: {
//     main: '#d32f2f', // 红色
//     light: '#ffebee',
//     dark: '#b71c1c'
//   },
//   [SeverityEnum.SEVERE]: {
//     main: '#f57c00', // 橙色
//     light: '#fff3e0',
//     dark: '#e65100'
//   },
//   [SeverityEnum.MODERATE]: {
//     main: '#fbc02d', // 黄色
//     light: '#fffde7',
//     dark: '#f9a825'
//   },
//   [SeverityEnum.MILD]: {
//     main: '#4caf50', // 绿色
//     light: '#e8f5e9',
//     dark: '#2e7d32'
//   },
//   'NORMAL': {
//     main: '#2196f3', // 蓝色
//     light: '#e3f2fd',
//     dark: '#1565c0'
//   }
// };

// 添加权限级别颜色配置
const privacyLevelStyles: Record<string, {bg: string, color: string, border: string, label: string}> = {
  'BASIC': { 
    bg: '#ECEFF1', 
    color: '#546E7A', 
    border: '#78909C',
    label: '基础授权' 
  },
  'STANDARD': { 
    bg: '#E3F2FD', 
    color: '#1565C0', 
    border: '#1E88E5',
    label: '标准授权' 
  },
  'FULL': { 
    bg: '#E8F5E9', 
    color: '#2E7D32', 
    border: '#43A047',
    label: '完整授权' 
  }
};

// 服务记录管理页面组件
const ServiceRecordManagePage: React.FC = () => {
  const navigate = useNavigate();
  const currentTheme = useTheme();
  const user = useAuthStore((state) => state.user);
  const serviceContext = useServiceUserContext();
  
  // Define smallerFontTheme here, after initial hooks
  const smallerFontTheme = createTheme({
    ...currentTheme,
    typography: {
      ...currentTheme.typography,
      h1: { ...currentTheme.typography.h1, fontSize: '5.0rem' },
      h2: { ...currentTheme.typography.h2, fontSize: '3.0rem' },
      h3: { ...currentTheme.typography.h3, fontSize: '2.2rem' },
      h4: { ...currentTheme.typography.h4, fontSize: '1.5rem' },
      h5: { ...currentTheme.typography.h5, fontSize: '1.0rem' },
      h6: { ...currentTheme.typography.h6, fontSize: '0.85rem' },
      subtitle1: { ...currentTheme.typography.subtitle1, fontSize: '0.75rem' },
      subtitle2: { ...currentTheme.typography.subtitle2, fontSize: '0.65rem' },
      body1: { ...currentTheme.typography.body1, fontSize: '0.75rem' },
      body2: { ...currentTheme.typography.body2, fontSize: '0.65rem' },
      button: { ...currentTheme.typography.button, fontSize: '0.65rem' },
      caption: { ...currentTheme.typography.caption, fontSize: '0.55rem' },
      overline: { ...currentTheme.typography.overline, fontSize: '0.55rem' },
    },
    components: {
      ...currentTheme.components,
      MuiPaper: {
        defaultProps: {
          variant: 'outlined',
        },
      },
      MuiCard: {
        defaultProps: {
          variant: 'outlined',
        },
      },
      MuiDialog: {
        defaultProps: {
          PaperProps: {
            variant: 'outlined',
          }
        }
      },
      MuiSelect: { // For the dropdown menu
        defaultProps: {
          MenuProps: {
            PaperProps: {
              variant: 'outlined',
            }
          }
        }
      },
      MuiInputLabel: { styleOverrides: { root: { fontSize: '0.75rem' } } },
      MuiMenuItem: { styleOverrides: { root: { fontSize: '0.75rem' } } },
      MuiTableCell: { styleOverrides: { root: { fontSize: '0.65rem' }, head: { fontSize: '0.7rem', fontWeight: 'bold' } } },
      MuiChip: {
        defaultProps: {
          variant: 'outlined',
        },
        styleOverrides: { 
          label: { fontSize: '0.55rem' }, 
          labelSmall: { fontSize: '0.5rem' } 
        }
      },
      MuiButton: {
        defaultProps: {
          variant: 'outlined',
          disableElevation: true,
        },
        styleOverrides: {
          sizeSmall: { fontSize: '0.6rem' },
          sizeMedium: { fontSize: '0.65rem' },
          sizeLarge: { fontSize: '0.75rem' }
        }
      },
      MuiDialogTitle: { styleOverrides: { root: { fontSize: '0.85rem' } } },
      MuiDialogContentText: { styleOverrides: { root: { fontSize: '0.75rem' } } },
      MuiFormControlLabel: { styleOverrides: { label: { fontSize: '0.75rem' } } },
      MuiAlert: { 
        defaultProps: {
          variant: 'outlined',
        },
        styleOverrides: { 
          message: { fontSize: '0.65rem' },
          root: { 
            boxShadow: 'none',
          }
        } 
      },
      MuiBreadcrumbs: { styleOverrides: { li: { fontSize: '0.65rem' } } },
    }
  });
  
  // 基础状态
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [records, setRecords] = useState<any[]>([]);
  const [showContextSelector, setShowContextSelector] = useState(false);
  const [notification, setNotification] = useState({
    open: false,
    message: '',
    type: 'success' as 'success' | 'error' | 'info' | 'warning'
  });
  
  // 搜索和筛选状态
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    type: '',
    severity: '',
    startDate: '',
    endDate: '',
    showMine: false // 添加只看我创建的过滤开关状态
  });
  
  // 分页状态
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(9);
  
  // 删除确认状态
  const [recordToDeleteId, setRecordToDeleteId] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  
  // 判断是否有编辑权限
  const hasEditPermission = useMemo(() => {
    // 获取当前权限级别
    const privacyLevel = serviceContext.privacyLevel;
    
    if (user?.role === 'ADMIN') return true; // 管理员始终有权限
    
    // 根据授权级别判断权限
    switch(privacyLevel) {
      case 'FULL':
        return true; // 完全授权可以编辑
      case 'STANDARD':
        return true; // 标准授权可以编辑自己创建的记录
      case 'BASIC':
      default:
        return false; // 基础授权只读
    }
  }, [user?.role, serviceContext.privacyLevel]);
  
  // 获取记录数据
  const fetchRecords = useCallback(async () => {
    if (!serviceContext.patientId || !serviceContext.diseaseId || !serviceContext.authorizationId) {
      console.log("[服务记录管理] 上下文不完整，不加载记录:", {
        authorizationId: serviceContext.authorizationId,
        patientId: serviceContext.patientId,
        diseaseId: serviceContext.diseaseId
      });
      
      if (!serviceContext.authorizationId) {
        setShowContextSelector(true);
        setError('请先选择授权关系');
      } else if (!serviceContext.patientId) {
        setShowContextSelector(true);
        setError('请先选择患者');
      } else if (!serviceContext.diseaseId) {
        setShowContextSelector(true);
        setError('请先选择病理');
      }
      
      setRecords([]);
      setLoading(false);
      return;
    }
    
    // 断言上下文信息已存在（非空）
    const authorizationId = serviceContext.authorizationId as string;
    const patientId = serviceContext.patientId as string;
    const diseaseId = serviceContext.diseaseId as string;
    
    setLoading(true);
    setError('');
    try {
      // 方法一：使用专门的上下文API获取记录
      console.log(`[服务记录管理] 使用上下文API获取记录`, {
        authorizationId,
        patientId,
        diseaseId
      });
      
      const contextResponse = await serviceRecordService.getServiceRecordsByContext({
        authorizationId,
        patientId,
        diseaseId
      });
      
      console.log(`[服务记录管理] 上下文API响应:`, contextResponse);
      
      if (contextResponse.success && Array.isArray(contextResponse.data) && contextResponse.data.length > 0) {
        console.log(`[服务记录管理] 通过上下文API获取到 ${contextResponse.data.length} 条记录`);
        // 使用上下文API获取的记录
        setRecords(contextResponse.data);
      } else {
        // 备选方法：如果上下文API无结果，尝试使用旧方法并进行本地过滤
        console.log(`[服务记录管理] 上下文API无结果，尝试使用授权ID API`);
        
        const response = await serviceRecordService.getServiceRecordsByAuth(authorizationId);
        
        if (response && response.success && Array.isArray(response.data)) {
          console.log(`[服务记录管理] 获取到 ${response.data.length} 条授权记录`);
          
          // 记录原始数据样本以便调试
          if (response.data.length > 0) {
            console.log('[服务记录管理] 第一条记录样本:', response.data[0]);
          }
          
          // 手动根据patientId和diseaseId过滤记录，兼容不同格式的字段命名
          const filteredData = response.data.filter((record: any) => {
            // 获取记录中的患者ID和疾病ID，兼容驼峰和下划线命名
            let recordPatientId;
            let recordDiseaseId;
            
            // 处理记录可能是嵌套对象的情况
            if (record.record) {
              // 从record子对象获取字段
              recordPatientId = record.record.patientId || record.record.patient_id;
              recordDiseaseId = record.record.diseaseId || record.record.disease_id;
            } else {
              // 直接从记录对象获取字段
              recordPatientId = record.patientId || record.patient_id;
              recordDiseaseId = record.diseaseId || record.disease_id;
            }
            
            // 检查记录是否由服务用户创建（如果有create_by或created_by字段）
            const createdBy = record.created_by || record.createdBy || record.create_by || record.createBy;
            // const isCreatedByServiceUser = createdBy === user?.id; // This variable is not used
            
            // 打印匹配信息用于调试
            const patientMatches = recordPatientId === patientId;
            const diseaseMatches = recordDiseaseId === diseaseId;
            
            if (!patientMatches || !diseaseMatches) {
              console.log('[服务记录管理] 记录未匹配:', {
                记录ID: record.id,
                记录患者ID: recordPatientId,
                期望患者ID: patientId,
                患者匹配: patientMatches,
                记录疾病ID: recordDiseaseId,
                期望疾病ID: diseaseId,
                疾病匹配: diseaseMatches,
                创建者ID: createdBy,
                当前用户ID: user?.id,
                // 服务用户创建: isCreatedByServiceUser // This variable is not used
              });
            }
            
            // 筛选条件：患者ID和疾病ID必须匹配
            return patientMatches && diseaseMatches;
          });
          
          console.log(`[服务记录管理] 过滤后剩余 ${filteredData.length} 条记录`);
          setRecords(filteredData);
        } else {
          console.error('[服务记录管理] 获取记录返回非数组数据:', response);
          setRecords([]);
          setError('获取记录数据格式错误');
        }
      }
    } catch (err: any) {
      console.error('[服务记录管理] 获取记录失败:', err);
      setError(`获取记录失败: ${err.message || '未知错误'}`);
      setRecords([]);
    } finally {
      setLoading(false);
    }
  }, [serviceContext.authorizationId, serviceContext.patientId, serviceContext.diseaseId, user?.id]);
  
  // 初始化时获取数据
  useEffect(() => {
    fetchRecords();
  }, [fetchRecords]);

  // 处理搜索框变化
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };
  
  // 当上下文选择器关闭后重新获取数据
  const handleContextSelectorClose = () => {
    setShowContextSelector(false);
    fetchRecords();
  };
  
  // 确认删除
  const confirmDelete = async () => {
    if (!recordToDeleteId) return;
    
    try {
      await serviceRecordService.deleteServiceRecord(recordToDeleteId);
      
      // 删除成功后更新本地记录
      setRecords(prev => prev.filter(r => r.id !== recordToDeleteId));
      
      setNotification({
        open: true,
        message: '记录已成功删除',
        type: 'success'
      });
    } catch (err: any) {
      console.error('[服务记录管理] 删除记录失败:', err);
      setNotification({
        open: true,
        message: `删除记录失败: ${err.message || '未知错误'}`,
        type: 'error'
      });
    } finally {
      setDeleteDialogOpen(false);
      setRecordToDeleteId(null);
    }
  };
  
  // 格式化日期时间
  const formatDateTime = (dateTime: string): string => {
    try {
      return format(new Date(dateTime), 'yyyy-MM-dd HH:mm', { locale: zhCN });
    } catch (err) {
      return dateTime || '';
    }
  };
  
  // 获取记录类型名称
  const getRecordTypeName = (type: string | string[]): string => {
    if (Array.isArray(type)) {
      return type.map(t => RecordTypeNames[t as RecordTypeEnum] || t).join(', ');
    }
    return RecordTypeNames[type as RecordTypeEnum] || type;
  };
  
  // 获取记录类型完整颜色对象
  const getRecordTypeFullColors = (type: string): {main: string, light: string, dark: string, icon: React.ReactNode} => {
    return recordTypeColors[type] || recordTypeColors['OTHER'] || { 
      main: '#757575', 
      light: '#f5f5f5', 
      dark: '#424242',
      icon: <NoteAltOutlined fontSize="small" />
    };
  };
  
  // 解析记录类型
  const parseRecordType = (recordType: any): string[] => {
    if (!recordType) return ['OTHER'];
    
    if (typeof recordType === 'string') {
      // 如果是JSON字符串，尝试解析
      try {
        const parsed = JSON.parse(recordType);
        if (Array.isArray(parsed)) return parsed;
        if (typeof parsed === 'string') return [parsed];
        return ['OTHER'];
      } catch (e) {
        // 不是JSON字符串，直接作为单个类型
        return [recordType];
      }
    }
    
    if (Array.isArray(recordType)) return recordType;
    
    // 如果是其他类型，默认为OTHER
    return ['OTHER'];
  };
  
  // 获取用于显示的类型信息
  const getDisplayTypeInfo = (record: any) => {
    const recordTypes = parseRecordType(record.type);
    
    // 如果有多个类型
    if (recordTypes.length > 1) {
      return `${getRecordTypeName(recordTypes[0])} +${recordTypes.length - 1}`;
    }
    
    // 单个类型
    return getRecordTypeName(recordTypes[0]);
  };
  
  // 过滤记录
  const filteredRecords = useMemo(() => {
    console.log('[服务记录管理] 过滤记录，原始记录:', records);
    if (!records || records.length === 0) return [];
    
    // 获取当前用户ID
    const currentUserId = user?.id;
    console.log('[服务记录管理] 当前用户ID:', currentUserId);
    
    // 计算删除记录的数量
    const deletedRecords = records.filter(record => record.deleted_at || record.deletedAt);
    if (deletedRecords.length > 0) {
      console.log(`[服务记录管理] 过滤掉 ${deletedRecords.length} 条已删除记录`);
    }
    
    const filtered = records.filter(record => {
      // 排除已删除的记录
      if (record.deleted_at || record.deletedAt) {
        return false;
      }
      
      // 检测记录的创建者ID
      const createdById = record.created_by || record.createdBy || record.user_id || record.userId;
      const isCreatedByMe = createdById === currentUserId;
      
      // 调试输出
      if (filters.showMine) {
        console.log(`[服务记录管理] 记录创建者检查: ID=${record.id}, 创建者=${createdById}, 当前用户=${currentUserId}, 是否匹配=${isCreatedByMe}`);
      }
      
      // 搜索词过滤（标题、内容、标签）
      const searchLower = searchTerm.toLowerCase();
      const matchesSearch = !searchTerm || 
        (record.title && record.title.toLowerCase().includes(searchLower)) ||
        (record.content && record.content.toLowerCase().includes(searchLower)) ||
        (record.tags && record.tags.some((tag: string) => tag.toLowerCase().includes(searchLower)));
      
      // 类型过滤
      const recordTypes = parseRecordType(record.type);
      const matchesType = !filters.type || recordTypes.includes(filters.type);
      
      // 严重程度过滤
      const matchesSeverity = !filters.severity || record.severity === filters.severity;
      
      // 日期过滤
      const recordDate = new Date(record.createdAt || record.created_at);
      const matchesStartDate = !filters.startDate || 
        recordDate >= new Date(filters.startDate);
      const matchesEndDate = !filters.endDate || 
        recordDate <= new Date(filters.endDate);
      
      // 创建者过滤 - 只看我创建的
      const matchesCreator = !filters.showMine || isCreatedByMe;
      
      return matchesSearch && matchesType && matchesSeverity && 
        matchesStartDate && matchesEndDate && matchesCreator;
    });
    
    console.log('[服务记录管理] 过滤后的记录:', filtered);
    return filtered;
  }, [records, searchTerm, filters, user?.id]);
  
  // 分页处理
  const paginatedRecords = useMemo(() => {
    return filteredRecords.slice(
      page * rowsPerPage,
      page * rowsPerPage + rowsPerPage
    );
  }, [filteredRecords, page, rowsPerPage]);
  
  // 调试信息
  useEffect(() => {
    console.log('[服务记录管理] 渲染组件，上下文:', {
      authorizationId: serviceContext.authorizationId,
      patientId: serviceContext.patientId,
      diseaseId: serviceContext.diseaseId,
      records: records.length,
      filteredRecords: filteredRecords.length,
      paginatedRecords: paginatedRecords.length
    });
  }, [
    serviceContext.authorizationId,
    serviceContext.patientId,
    serviceContext.diseaseId,
    records.length,
    filteredRecords.length,
    paginatedRecords.length
  ]);
  
  // 处理页码变化
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };
  
  // 处理每页行数变化
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };
  
  // 查看记录详情
  const handleViewRecord = (id: string) => {
    navigate(`/service-records/${id}`);
  };
  
  // 编辑记录
  const handleEditRecord = (id: string) => {
    navigate(`/service-records/${id}/edit`);
  };
  
  // 请求删除记录
  const handleDeleteRecord = (id: string) => {
    // 检查是否有删除权限
    if (!hasEditPermission) {
      setNotification({
        open: true,
        message: '您没有删除此记录的权限',
        type: 'warning'
      });
      return;
    }
    
    setRecordToDeleteId(id);
    setDeleteDialogOpen(true);
  };
  
  // 取消删除
  const cancelDelete = () => {
    setDeleteDialogOpen(false);
    setRecordToDeleteId(null);
  };
  
  // 关闭通知
  const handleCloseNotification = () => {
    setNotification(prev => ({ ...prev, open: false }));
  };
  
  // 渲染权限级别提示
  const renderPrivacyLevelAlert = () => {
    const privacyLevel = serviceContext.privacyLevel;
    
    if (!privacyLevel) return null;
    
    let message = '';
    let severity: 'info' | 'warning' | 'success' = 'info';
    
    switch(privacyLevel) {
      case 'FULL':
        message = '您拥有完整授权，可以查看、创建和编辑所有记录。';
        severity = 'success';
        break;
      case 'STANDARD':
        message = '您拥有标准授权，可以查看所有记录，但只能编辑自己创建的记录。';
        severity = 'info';
        break;
      case 'BASIC':
        message = '您拥有基础授权，只能查看记录，无法创建或编辑。';
        severity = 'warning';
        break;
      default:
        message = '未知授权级别，请联系管理员。';
        severity = 'warning';
    }
    
    return (
      <Alert severity={severity} sx={{ mb: 2, paddingLeft: '10px', paddingRight: '10px' }}>
        {message}
      </Alert>
    );
  };

  // 记录卡片组件
  const RecordCard = ({ record }: { record: any }) => {
    // 获取当前用户ID
    const currentUserId = user?.id;
    
    // 检测记录的创建者ID（支持多种可能的字段名）
    const createdById = record.created_by || record.createdBy || record.user_id || record.userId;
    const isCreatedByCurrentUser = createdById === currentUserId;
    
    const recordTypes = parseRecordType(record.type);
    const mainType = recordTypes[0];
    const typeColor = getRecordTypeFullColors(mainType);
    const privacyLevel = serviceContext.privacyLevel || 'BASIC';
    
    return (
      <Card 
        sx={{ 
          borderRadius: '8px',
          overflow: 'hidden',
          // boxShadow: '0 2px 8px rgba(0,0,0,0.08)', // Removed for wireframe
          transition: 'transform 0.2s, border-color 0.2s', // Adjusted transition
          '&:hover': {
            transform: 'translateY(-4px)',
            // boxShadow: '0 8px 16px rgba(0,0,0,0.12)' // Removed for wireframe
            borderColor: currentTheme.palette.primary.light, // Example hover effect for border
          },
          // 为当前用户创建的记录添加一个微妙的背景色
          ...(isCreatedByCurrentUser && {
            backgroundColor: alpha(currentTheme.palette.primary.light, 0.05) // Adjusted background for visibility with outlined cards
          }),
          height: '100%',
          display: 'flex',
          flexDirection: 'column'
        }}
        onClick={() => handleViewRecord(record.id)}
      >
        {/* 记录类型顶部横条 */}
        <Box sx={{ 
          backgroundColor: typeColor.main,
          height: '8px',
          width: '100%'
        }} />
        
        <CardContent sx={{ flex: '1 0 auto', pb: 1 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Avatar 
                sx={{ 
                  width: 32, 
                  height: 32, 
                  backgroundColor: typeColor.light,
                  color: typeColor.dark,
                  border: `1px solid ${typeColor.main}`
                }}
              >
                {typeColor.icon}
              </Avatar>
              <Typography variant="h6" component="div" sx={{ fontWeight: 500 }}>
              {record.title}
              </Typography>
            </Box>
            
              {isCreatedByCurrentUser && (
                <Chip 
                  label="我创建的" 
                  size="small" 
                sx={{ 
                  backgroundColor: '#e8f5e9',
                  color: '#2e7d32',
                  borderColor: '#81c784',
                  height: 20, 
                  fontWeight: 500
                }} 
              />
            )}
          </Box>
          
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 1.5 }}>
            <Box sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              gap: 0.5,
              color: 'text.secondary',
              fontSize: '0.75rem'
            }}>
              <AccessTimeOutlined sx={{ fontSize: '0.875rem' }} />
              {formatDateTime(record.createdAt || record.created_at)}
            </Box>
            
            <Box sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              gap: 0.5,
              color: 'text.secondary',
              fontSize: '0.75rem'
            }}>
              {isCreatedByCurrentUser ? (
                <>
                  <PersonOutlined sx={{ fontSize: '0.875rem' }} />
                  我
                </>
              ) : (
                <>
                  <GroupOutlined sx={{ fontSize: '0.875rem' }} />
                  其他用户
                </>
              )}
            </Box>
            
            <Chip 
              label={getDisplayTypeInfo(record)} 
              size="small" 
              sx={{ 
                backgroundColor: typeColor.light,
                color: typeColor.dark,
                borderColor: typeColor.main,
                fontWeight: 500,
                height: 20,
                fontSize: '0.7rem'
              }}
            />
          </Box>
          
          <Typography 
            variant="body2" 
            color="text.secondary" 
            sx={{ 
              mb: 1.5,
              lineHeight: '1.5',
              overflow: 'hidden',
              display: '-webkit-box',
              WebkitLineClamp: 3,
              WebkitBoxOrient: 'vertical',
              textOverflow: 'ellipsis'
            }}
          >
            {record.content || '无内容'}
          </Typography>
          
          {record.tags && record.tags.length > 0 && (
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
              {record.tags.map((tag: string, index: number) => (
                <Chip
                  key={index} 
                  label={tag} 
                  size="small" 
                  sx={{ 
                    backgroundColor: PERSONAL_TAG_LIGHT_COLOR,
                    color: PERSONAL_TAG_COLOR,
                    fontSize: '0.7rem',
                    height: 20
                  }}
                />
              ))}
            </Box>
          )}
        </CardContent>
        
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'flex-end', 
          p: 1,
          borderTop: '1px solid #f0f0f0',
          mt: 'auto',
          backgroundColor: privacyLevelStyles[privacyLevel]?.bg || '#f5f5f5'
        }}>
          {/* 查看按钮 - 始终可用 */}
          <Tooltip title="查看详情">
            <IconButton 
              size="small"
              sx={{ 
                color: currentTheme.palette.primary.main,
                '&:hover': { backgroundColor: alpha(currentTheme.palette.primary.main, 0.1) } 
              }}
              onClick={(e) => {
                e.stopPropagation();
                handleViewRecord(record.id);
              }}
            >
              <VisibilityOutlined fontSize="small" />
            </IconButton>
          </Tooltip>
          
          {/* 编辑按钮 - 根据权限显示 */}
          {(hasEditPermission && (privacyLevel === 'FULL' || isCreatedByCurrentUser)) && (
            <Tooltip title="编辑">
              <IconButton 
                size="small"
                sx={{ 
                  color: currentTheme.palette.warning.main,
                  '&:hover': { backgroundColor: alpha(currentTheme.palette.warning.main, 0.1) }
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  handleEditRecord(record.id);
                }}
              >
                <EditOutlined fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
          
          {/* 删除按钮 - 根据权限显示 */}
          {(hasEditPermission && (privacyLevel === 'FULL' || isCreatedByCurrentUser)) && (
            <Tooltip title="删除">
              <IconButton 
                size="small"
                sx={{ 
                  color: currentTheme.palette.error.main,
                  '&:hover': { backgroundColor: alpha(currentTheme.palette.error.main, 0.1) }
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  handleDeleteRecord(record.id);
                }}
              >
                <DeleteOutlined fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
        </Box>
      </Card>
    );
  };
  
  return (
    <ThemeProvider theme={smallerFontTheme}>
      <Box sx={{ paddingLeft: '10px', paddingRight: '10px' }}>
        {/* 服务上下文组件 */}
        <Box sx={{ mb: 3, paddingTop: '10px' }}>
            <ServiceContextBar onSelectContext={() => setShowContextSelector(true)} />
        </Box>
        
        {/* 页面标题和操作区 */}
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center', 
          mb: 3,
          flexWrap: 'wrap',
          gap: 1,
          paddingLeft: '10px',
          paddingRight: '10px'
        }}>
          <Typography 
            variant="h5" 
            component="h1" 
            gutterBottom
            sx={{ fontWeight: 500 }}
          >
            记录管理
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button 
              variant="outlined"
              color="primary" 
              startIcon={<AddIcon />} 
              onClick={() => navigate('/service-records/create')}
              disabled={!hasEditPermission || loading}
            >
              新建记录
            </Button>
            
            <Button 
              startIcon={<RefreshIcon />} 
              variant="outlined" 
              onClick={fetchRecords}
              disabled={loading || 
                !serviceContext.authorizationId || 
                !serviceContext.patientId || 
                !serviceContext.diseaseId}
            >
              刷新
            </Button>
          </Box>
        </Box>
        
        {/* 权限提示 */}
        {renderPrivacyLevelAlert()}
        
        {/* 错误提示 */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        {/* 搜索和筛选区域 */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {/* 搜索框 */}
            <TextField
              fullWidth
              placeholder="搜索记录标题、内容或标签..."
              variant="outlined"
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
            
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', alignItems: 'center' }}>
              {/* 记录类型筛选 */}
              <FormControl sx={{ minWidth: 200, flex: 1 }} variant="outlined">
                <InputLabel id="type-filter-label" shrink={true}>按类型筛选</InputLabel>
                <Select
                  labelId="type-filter-label"
                  id="type-filter"
                  value={filters.type}
                  label="按类型筛选"
                  onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value }))}
                  displayEmpty
                  notched={true}
                  sx={{ '& .MuiSelect-select': { display: 'flex', alignItems: 'center' } }}
                  renderValue={(selected) => {
                    if (!selected) return "所有类型";
                    return RecordTypeNames[selected as RecordTypeEnum] || selected;
                  }}
                >
                  <MenuItem value="">所有类型</MenuItem>
                  {Object.entries(RecordTypeNames).map(([value, label]) => (
                    <MenuItem key={value} value={value}>
                      {label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              
              {/* 严重程度筛选 */}
              <FormControl sx={{ minWidth: 200, flex: 1 }} variant="outlined">
                <InputLabel id="severity-filter-label" shrink={true}>按严重程度筛选</InputLabel>
                <Select
                  labelId="severity-filter-label"
                  id="severity-filter"
                  value={filters.severity}
                  label="按严重程度筛选"
                  onChange={(e) => setFilters(prev => ({ ...prev, severity: e.target.value }))}
                  displayEmpty
                  notched={true}
                  sx={{ '& .MuiSelect-select': { display: 'flex', alignItems: 'center' } }}
                  renderValue={(selected) => {
                    if (!selected) return "所有级别";
                    return SeverityNames[selected as SeverityEnum] || selected;
                  }}
                >
                  <MenuItem value="">所有级别</MenuItem>
                  {Object.entries(SeverityNames).map(([value, label]) => (
                    <MenuItem key={value} value={value}>
                      {label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              
              {/* 只看我创建的切换 */}
              <FormControl sx={{ display: 'flex', alignItems: 'center', minWidth: 150 }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Switch
                    checked={filters.showMine}
                    onChange={e => setFilters(prev => ({ ...prev, showMine: e.target.checked }))}
                    color="primary"
                  />
                  <Typography variant="body2">只看我创建的</Typography>
                </Box>
              </FormControl>
            </Box>
          </Box>
        </Paper>
        
        {/* 加载指示器 */}
        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        )}
        
        {/* 无数据提示 */}
        {!loading && filteredRecords.length === 0 && (
          <Paper sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="body1" color="text.secondary">
              没有找到匹配的记录
            </Typography>
          </Paper>
        )}
        
        {/* 记录卡片网格 */}
        {!loading && filteredRecords.length > 0 && (
          <>
            <Box sx={{ 
              display: 'grid', 
              gridTemplateColumns: { 
                xs: '1fr', 
                sm: 'repeat(2, 1fr)', 
                md: 'repeat(3, 1fr)' 
              },
              gap: 2,
              mb: 2
            }}>
              {paginatedRecords.map(record => (
                <RecordCard key={record.id} record={record} />
              ))}
            </Box>
            
            {/* 分页控件 */}
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
              <TablePagination
                component="div"
                count={filteredRecords.length}
                page={page}
                onPageChange={handleChangePage}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                rowsPerPageOptions={[9, 18, 27]}
                labelRowsPerPage="每页行数:"
                labelDisplayedRows={({ from, to, count }) => 
                  `${from}-${to} 共 ${count !== -1 ? count : `超过 ${to}`}`
                }
              />
            </Box>
          </>
        )}
        
        {/* 上下文选择器对话框 */}
        {showContextSelector && (
          <ServiceContextSelector
            onComplete={handleContextSelectorClose}
          />
        )}
        
        {/* 删除确认对话框 */}
        <Dialog
          open={deleteDialogOpen}
          onClose={cancelDelete}
        >
          <DialogTitle>确认删除</DialogTitle>
          <DialogContent>
            <Typography>
              您确定要删除这条记录吗？此操作无法撤销。
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={cancelDelete}>取消</Button>
            <Button 
              onClick={confirmDelete} 
              color="error"
              variant="contained"
            >
              删除
            </Button>
          </DialogActions>
        </Dialog>
        
        {/* 消息通知 */}
        <Snackbar
          open={notification.open}
          autoHideDuration={6000}
          onClose={handleCloseNotification}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert 
            onClose={handleCloseNotification} 
            severity={notification.type}
            variant="outlined"
          >
            {notification.message}
          </Alert>
        </Snackbar>
      </Box>
    </ThemeProvider>
  );
};

export default ServiceRecordManagePage;