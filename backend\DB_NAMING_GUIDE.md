# 数据库命名规范指南

## 命名规范概述

本项目采用以下命名规范：

1. **数据库层**：所有数据库表名和列名使用**下划线命名法(snake_case)**
2. **应用层代码**：模型属性和JavaScript变量使用**驼峰命名法(camelCase)**

这种分层命名策略通过Objection.js的`snakeCaseMappers()`自动进行转换，使开发人员在各个层级都能使用该层级最适合的命名风格。

## 具体规范

### 数据库命名（下划线命名法）

- **表名**：使用复数形式，全部小写，单词之间用下划线连接
  - 示例：`users`, `patients`, `medical_records`, `patient_logs`

- **列名**：全部小写，单词之间用下划线连接
  - 示例：`id`, `first_name`, `last_name`, `birth_date`, `updated_at`

- **主键**：通常命名为`id`

- **外键**：使用模式`<表名单数>_id`
  - 示例：`user_id`, `patient_id`, `record_id`

- **索引**：使用模式`idx_<表名>_<列名>`
  - 示例：`idx_patients_user_id`, `idx_records_created_at`

- **布尔字段**：使用`is_`或`has_`前缀
  - 示例：`is_active`, `is_deleted`, `has_insurance`

- **时间戳字段**：使用`_at`后缀
  - 示例：`created_at`, `updated_at`, `deleted_at`

### 代码层命名（驼峰命名法）

在JavaScript代码和模型定义中，使用驼峰命名法，但在进行数据库操作时，需要注意以下几点：

- **直接使用knex查询**时，列名必须使用下划线命名法：

```javascript
// 正确 ✅
knex('users').where('last_login_at', '>', date);

// 错误 ❌
knex('users').where('lastLoginAt', '>', date);
```

- **使用Objection.js模型**时，属性使用驼峰命名法：

```javascript
// 正确 ✅
const user = await User.query().findById(id);
console.log(user.lastLoginAt);

// 模型定义中的静态属性也使用驼峰命名法
static get jsonSchema() {
  return {
    type: 'object',
    required: ['username', 'password_hash'],
    properties: {
      id: { type: 'string' },
      username: { type: 'string' },
      // ... 其他属性
    }
  };
}
```

## 自动转换

本项目使用Objection.js的`snakeCaseMappers()`进行自动转换，确保了：

1. 从数据库读取数据时，下划线命名的列会自动转换为驼峰命名的属性
2. 将数据写入数据库时，驼峰命名的属性会自动转换为下划线命名的列

示例配置：

```javascript
// Model.js
const { Model, snakeCaseMappers } = require('objection');

class BaseModel extends Model {
  static get columnNameMappers() {
    return snakeCaseMappers();
  }
}
```

## 添加新表或新列时的注意事项

1. **创建迁移文件**：使用knex命令创建新的迁移文件
   ```
   npx knex migrate:make create_new_table
   ```

2. **定义表结构**：在迁移文件中使用下划线命名法定义表名和列名
   ```javascript
   exports.up = function(knex) {
     return knex.schema.createTable('medical_appointments', table => {
       table.uuid('id').primary();
       table.uuid('patient_id').notNullable().references('id').inTable('patients');
       table.date('appointment_date').notNullable();
       table.string('appointment_type').notNullable();
       table.boolean('is_confirmed').defaultTo(false);
       table.timestamps(true, true);
     });
   };
   ```

3. **定义模型**：在模型定义中使用驼峰命名法
   ```javascript
   // MedicalAppointment.js
   class MedicalAppointment extends BaseModel {
     static get tableName() {
       return 'medical_appointments';
     }
     
     static get jsonSchema() {
       return {
         type: 'object',
         required: ['patient_id', 'appointment_date', 'appointment_type'],
         properties: {
           id: { type: 'string' },
           patientId: { type: 'string' },
           appointmentDate: { type: 'string', format: 'date' },
           appointmentType: { type: 'string' },
           isConfirmed: { type: 'boolean' },
           createdAt: { type: 'string', format: 'date-time' },
           updatedAt: { type: 'string', format: 'date-time' }
         }
       };
     }
   }
   ```

## 最佳实践

1. **保持一致性**：确保所有新表和列都遵循既定命名规范
2. **避免直接SQL**：尽量使用Objection.js模型或knex构建器，避免直接编写SQL
3. **测试转换**：添加新表或列后，测试数据的读写是否正确进行了命名转换
4. **文档记录**：为新表和列添加适当的注释和文档
5. **代码审查**：进行代码审查时，特别关注命名规范的遵循情况 