import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  CircularProgress,
  Alert,
  Tooltip,
  Switch,
  FormControlLabel,
  IconButton,
  DialogContentText,
  useMediaQuery,
  useTheme,
  Snackbar,
  ThemeProvider,
  createTheme
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import RefreshIcon from '@mui/icons-material/Refresh';
import DeleteIcon from '@mui/icons-material/Delete';
import FilterListIcon from '@mui/icons-material/FilterList';
import { useAuthStore } from '../store/authStore';
import authorizationService, { Authorization as BaseAuthorization } from '../services/authorizationService';
import AuthorizationCard from '../components/authorization/AuthorizationCard';
import AuthorizationFilter, { AuthorizationFilterValues } from '../components/authorization/AuthorizationFilter';
import { getPatients } from '../services/patientService';
import { Patient } from '../types/patient';

// 扩展授权类型，使其支持实际API返回的字段
interface Authorization extends BaseAuthorization {
  // 定义明确的类型扩展，处理API返回的多样化字段
  authorized?: {
    id?: string;
    username?: string;
    email?: string;
    phone_number?: string;
    role?: string;
  };
  authorizer?: {
    id?: string;
    username?: string;
  };
  patient?: {
    id?: string;
    name?: string;
  };
}

// 授权状态映射
const statusMap: Record<string, { label: string, color: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' }> = {
  PENDING_AUTHORIZER: { label: '待确认', color: 'warning' },
  PENDING_AUTHORIZED: { label: '待确认', color: 'warning' },
  ACTIVE: { label: '已激活', color: 'success' },
  REVOKED: { label: '已撤销', color: 'error' }
};

// 角色映射
const roleMap: Record<string, { label: string, color: 'default' | 'primary' | 'secondary' | 'info' }> = {
  ADMIN: { label: '管理员', color: 'secondary' },
  SERVICE: { label: '服务用户', color: 'primary' },
  USER: { label: '普通用户', color: 'info' }
};

// 权限级别映射
const privacyLevelMap: Record<string, { label: string, description: string }> = {
  BASIC: { label: '基础权限', description: '只读信息' },
  STANDARD: { label: '标准权限', description: '读写增加的信息' },
  FULL: { label: '完整权限', description: '读写所有信息' }
};

// 授权管理页面组件
const AuthorizationManage: React.FC = () => {
  const [authorizersData, setAuthorizersData] = useState<Authorization[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [selectedUser, setSelectedUser] = useState('');
  const [selectedPrivacyLevel, setSelectedPrivacyLevel] = useState('STANDARD');
  const [selectedPatient, setSelectedPatient] = useState('');
  const [patientsData, setPatientsData] = useState<Patient[]>([]);
  const [loadingPatients, setLoadingPatients] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);
  const [searchError, setSearchError] = useState('');
  const [searchStatus, setSearchStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [authToDelete, setAuthToDelete] = useState<Authorization | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  
  // 使用统一的筛选状态
  const [filterVisible, setFilterVisible] = useState(true);
  const [filters, setFilters] = useState<AuthorizationFilterValues>({
    search: '',
    status: 'ALL',
    role: 'ALL',
    privacyLevel: 'ALL'
  });
  
  // 添加通知状态
  const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    type: 'success' | 'error' | 'info' | 'warning';
  }>({
    open: false,
    message: '',
    type: 'info'
  });
  
  const authStore = useAuthStore();
  const user = authStore.user;
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  // 创建一个字体大小减小2号的主题
  const smallerFontTheme = createTheme({
    ...theme,
    typography: {
      ...theme.typography,
      h5: {
        ...theme.typography.h5,
        fontSize: '1.0rem',  // 进一步减小h5字体
      },
      h6: {
        ...theme.typography.h6,
        fontSize: '0.85rem', // 进一步减小h6字体
      },
      body1: {
        ...theme.typography.body1,
        fontSize: '0.75rem', // 进一步减小body1字体
      },
      body2: {
        ...theme.typography.body2,
        fontSize: '0.65rem', // 进一步减小body2字体
      },
      caption: {
        ...theme.typography.caption,
        fontSize: '0.55rem', // 进一步减小caption字体
      },
      button: {
        ...theme.typography.button,
        fontSize: '0.65rem', // 进一步减小按钮字体
      },
    },
    components: {
      ...theme.components,
      MuiChip: {
        styleOverrides: {
          label: {
            fontSize: '0.55rem', // 进一步减小Chip标签字体
          }
        }
      },
      MuiListItemText: {
        styleOverrides: {
          primary: {
            fontSize: '0.75rem', // 进一步减小列表项主要文本字体
          },
          secondary: {
            fontSize: '0.65rem', // 进一步减小列表项次要文本字体
          }
        }
      },
      MuiInputLabel: {
        styleOverrides: {
          root: {
            fontSize: '0.65rem', // 进一步减小输入标签字体
          }
        }
      },
      MuiMenuItem: {
        styleOverrides: {
          root: {
            fontSize: '0.65rem', // 进一步减小菜单项字体
          }
        }
      },
      MuiTableCell: {
        styleOverrides: {
          root: {
            fontSize: '0.65rem', // 进一步减小表格单元格字体
          },
          head: {
            fontSize: '0.7rem', // 进一步减小表头字体
            fontWeight: 'bold',
          }
        }
      },
      MuiDialogTitle: {
        styleOverrides: {
          root: {
            fontSize: '0.9rem', // 进一步减小对话框标题字体
          }
        }
      },
      MuiDialogContentText: {
        styleOverrides: {
          root: {
            fontSize: '0.65rem', // 进一步减小对话框内容文本字体
          }
        }
      },
      MuiFormControlLabel: {
        styleOverrides: {
          label: {
            fontSize: '0.65rem', // 进一步减小表单控件标签字体
          }
        }
      },
      MuiAlert: {
        styleOverrides: {
          message: {
            fontSize: '0.65rem', // 进一步减小警告信息字体
          }
        }
      }
    }
  });

  // 加载授权数据
  const loadAuthorizationData = async () => {
    setLoading(true);
    setError('');
    try {
      // 只获取我授权的数据
      const asAuthorizerResponse = await authorizationService.getAuthorizationsAsAuthorizer();
      if (asAuthorizerResponse.success) {
        console.log('获取授权数据成功:', asAuthorizerResponse.data);
        console.log('当前用户:', {
          id: user?.id,
          username: user?.username,
          role: user?.role
        });
        
        if (asAuthorizerResponse.data && asAuthorizerResponse.data.length > 0) {
          // 检查第一条记录是否包含授权人ID
          const firstAuth = asAuthorizerResponse.data[0] as Authorization;
          console.log('授权记录详情:', {
            id: firstAuth.id,
            authorizer_id: firstAuth.authorizer_id,
            authorized_id: firstAuth.authorized_id,
            status: firstAuth.status,
            当前用户ID: user?.id,
            是否授权人: user?.id === firstAuth.authorizer_id,
            所有字段: Object.keys(firstAuth)
          });
        }
        
        setAuthorizersData(asAuthorizerResponse.data);
      } else {
        // 处理从授权服务返回的错误对象
        console.warn('获取授权数据失败:', asAuthorizerResponse.message);
        setError(asAuthorizerResponse.message || '获取授权数据失败');
        
        // 如果是认证错误，显示更友好的提示
        if (asAuthorizerResponse.authError) {
          console.log('捕获到认证错误，显示友好提示');
          setError('您的登录信息已过期，请重新登录后再访问此页面');
          
          // 可选: 如果确定需要重新登录，可以延迟一段时间后跳转到登录页
          // setTimeout(() => {
          //   localStorage.setItem('redirectAfterLogin', window.location.pathname);
          //   window.location.href = '/login';
          // }, 3000);
        }
        
        // 始终返回空数组，避免显示旧数据
        setAuthorizersData([]);
      }
    } catch (err: any) {
      console.error('加载授权数据失败:', err);
      setError(err.message || '加载授权数据失败');
      setAuthorizersData([]);
    } finally {
      setLoading(false);
    }
  };

  // 加载患者列表
  const loadPatients = async () => {
    setLoadingPatients(true);
    try {
      const patientsData = await getPatients();
      setPatientsData(patientsData);
    } catch (err) {
      console.error('加载患者列表失败:', err);
    } finally {
      setLoadingPatients(false);
    }
  };

  // 首次加载
  useEffect(() => {
    loadAuthorizationData();
  }, []);  // eslint-disable-line react-hooks/exhaustive-deps

  // 打开添加授权对话框
  const handleOpenAddDialog = () => {
    setOpenAddDialog(true);
    setSearchQuery('');
    setSearchResults([]);
    setSelectedUser('');
    setSelectedPatient('');
    setSelectedPrivacyLevel('STANDARD');
    loadPatients();
  };

  // 关闭添加授权对话框
  const handleCloseAddDialog = () => {
    setOpenAddDialog(false);
  };

  // 搜索服务用户
  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      setSearchError('请输入搜索关键词');
      setSearchStatus('error');
      return;
    }
    
    setSearchLoading(true);
    setSearchError('');
    setSearchStatus('loading');
    
    try {
      const response = await authorizationService.searchServiceUsers(searchQuery);
      if (response.success) {
        setSearchResults(response.data);
        setSearchStatus('success');
        if (response.data.length === 0) {
          setSearchError('未找到匹配的服务用户');
        }
      }
    } catch (err: any) {
      console.error('搜索服务用户失败:', err);
      setSearchError(err.message || '搜索服务用户失败');
      setSearchStatus('error');
    } finally {
      setSearchLoading(false);
    }
  };

  // 搜索查询变化
  const handleSearchQueryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    // 当用户开始输入时，重置搜索状态
    if (searchStatus !== 'idle') {
      setSearchStatus('idle');
      setSearchError('');
    }
  };

  // 创建授权
  const handleCreateAuthorization = async () => {
    if (!selectedUser) {
      setError('请选择一个服务用户');
      return;
    }

    // 获取当前用户ID
    const userId = user?.id || authStore.user?.id || localStorage.getItem('userId');
    
    if (!userId) {
      console.error('用户信息不完整:', { user, authStoreUser: authStore.user });
      setError('用户信息不完整，请尝试重新登录');
      return;
    }

    // 检查是否试图向自己申请授权
    if (userId === selectedUser) {
      setError('不能向自己申请授权');
      setNotification({
        open: true,
        message: '不能向自己申请授权，请选择其他服务用户',
        type: 'error'
      });
      return;
    }

    try {
      // 创建授权请求时，发送者是当前用户ID (authorizerId)，接收者是选中的服务用户ID (authorizedId)
      const response = await authorizationService.createAuthorization({
        authorizerId: userId,
        authorizedId: selectedUser,
        patientId: selectedPatient || undefined, // 如果未选择患者，则不传递patientId
        privacyLevel: selectedPrivacyLevel
      });
      
      if (response.success) {
        handleCloseAddDialog();
        loadAuthorizationData();
        
        // 添加成功提示
        setNotification({
          open: true,
          message: selectedPatient 
            ? `已成功创建针对特定患者的授权`
            : `已成功创建针对所有患者的授权`,
          type: 'success'
        });
      }
    } catch (err: any) {
      console.error('创建授权失败:', err);
      
      // 获取详细的错误信息
      const errorMsg = err.response?.data?.message || err.message || '创建授权失败';
      
      // 判断错误是否与患者相关
      const isPatientRelatedError = errorMsg.includes('患者');
      
      setError(errorMsg);
      
      // 如果是患者相关错误，显示更详细的提示
      if (isPatientRelatedError) {
        const patientName = selectedPatient 
          ? patientsData.find(p => p.id === selectedPatient)?.name || '选中患者' 
          : '所有患者';
        
        const serviceName = searchResults.find(u => u.id === selectedUser)?.username || '该服务用户';
        
        setNotification({
          open: true,
          message: `您已授权${serviceName}访问${patientName}，无需重复授权`,
          type: 'warning'
        });
      }
    }
  };

  // 更新授权状态
  const handleUpdateStatus = async (id: string, switchValue: boolean) => {
    try {
      console.log('普通用户更新授权状态:', { id, switchValue });
      const response = await authorizationService.updateAuthorizationStatus(id, switchValue);
      console.log('普通用户更新授权状态响应:', response);
      
      if (response.success) {
        // 更新成功，显示通知
        setNotification({
          open: true,
          message: switchValue ? '已成功批准授权' : '已成功取消批准',
          type: 'success'
        });
        
        // 刷新授权列表数据
        await loadAuthorizationData();
        
        // 在UI中直接更新对应授权项的状态，避免等待刷新
        setAuthorizersData(prev => 
          prev.map(auth => {
            if (auth.id === id) {
              // 判断当前用户是授权人还是被授权人
              const isUserAuthorizer = auth.authorizer_id === (user?.id || '');
              
              // 根据用户角色更新对应的开关状态
              if (isUserAuthorizer) {
                return {
                  ...auth,
                  authorizer_switch: switchValue,
                  status: getUpdatedStatus({
                    authorizer_switch: switchValue,
                    authorized_switch: auth.authorized_switch
                  })
                };
              } else {
                return {
                  ...auth,
                  authorized_switch: switchValue,
                  status: getUpdatedStatus({
                    authorizer_switch: auth.authorizer_switch,
                    authorized_switch: switchValue
                  })
                };
              }
            }
            return auth;
          })
        );
      } else {
        setError(response.message || '更新授权状态失败');
      }
    } catch (err: any) {
      console.error('更新授权状态失败:', err);
      setError(err.message || '更新授权状态失败');
    }
  };
  
  // 辅助函数：根据开关状态推导授权状态
  const getUpdatedStatus = ({ authorizer_switch, authorized_switch }: { authorizer_switch?: boolean, authorized_switch?: boolean }): string => {
    // 将undefined转换为false
    const authorizerSwitch = !!authorizer_switch;
    const authorizedSwitch = !!authorized_switch;
    
    if (authorizerSwitch && authorizedSwitch) {
      return 'ACTIVE'; // 双方都开，激活
    } else if (!authorizerSwitch && !authorizedSwitch) {
      return 'REVOKED'; // 双方都关，撤销
    } else if (authorizerSwitch && !authorizedSwitch) {
      return 'PENDING_AUTHORIZED'; // 授权人开，被授权人关，等待被授权人确认
    } else {
      return 'PENDING_AUTHORIZER'; // 授权人关，被授权人开，等待授权人确认
    }
  };

  // 更新权限级别
  const handleUpdatePrivacyLevel = async (id: string, privacyLevel: string) => {
    try {
      // 获取授权记录
      const auth = authorizersData.find(a => a.id === id);
      if (!auth) {
        setError('找不到授权记录');
        return;
      }
      
      // 添加详细调试信息
      console.log('更新权限级别请求:', {
        授权ID: id,
        新权限级别: privacyLevel,
        当前用户: user?.id,
        授权人ID: auth.authorizer_id,
        授权记录详情: {
          status: auth.status,
          privacy_level: auth.privacy_level || auth.privacyLevel,
          authorizer_switch: auth.authorizer_switch,
          authorized_switch: auth.authorized_switch
        }
      });
      
      const response = await authorizationService.updateAuthorizationPrivacyLevel(id, privacyLevel);
      
      console.log('更新权限级别响应:', response);
      
      if (response.success) {
        loadAuthorizationData();
      }
    } catch (err: any) {
      console.error('更新权限级别失败:', err);
      const errorMsg = err.response?.data?.message || err.message || '更新权限级别失败';
      console.error('错误详情:', {
        状态码: err.response?.status,
        错误消息: errorMsg,
        请求URL: err.config?.url,
        请求方法: err.config?.method
      });
      setError(errorMsg);
    }
  };

  // 打开删除授权对话框
  const handleOpenDeleteDialog = (auth: Authorization) => {
    setAuthToDelete(auth);
    setOpenDeleteDialog(true);
  };

  // 关闭删除授权对话框
  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    setAuthToDelete(null);
  };

  // 删除授权
  const handleDeleteAuthorization = async () => {
    if (!authToDelete) return;
    
    setDeleteLoading(true);
    try {
      console.log('删除授权:', { id: authToDelete.id });
      const response = await authorizationService.deleteRevokedAuthorization(authToDelete.id);
      
      if (response.success) {
        // 删除成功后更新列表，从本地数据中移除
        setAuthorizersData(prev => prev.filter(auth => auth.id !== authToDelete.id));
        handleCloseDeleteDialog();
      }
    } catch (err: any) {
      console.error('删除授权失败:', err);
      setError(err.message || '删除授权失败');
    } finally {
      setDeleteLoading(false);
    }
  };

  // 筛选处理
  const handleFilter = (newFilters: AuthorizationFilterValues) => {
    setFilters(newFilters);
  };

  // 重置筛选
  const handleResetFilter = () => {
    setFilters({
      search: '',
      status: 'ALL',
      role: 'ALL',
      privacyLevel: 'ALL'
    });
  };

  // 筛选数据
  const filteredData = authorizersData.filter(auth => {
    // 搜索筛选 - 扩展到多个字段
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      const username = auth.authorized?.username?.toLowerCase() || '';
      const email = auth.authorized?.email?.toLowerCase() || '';
      const phone = auth.authorized?.phone_number?.toLowerCase() || '';
      const patientName = auth.patient?.name?.toLowerCase() || '';
      
      // 如果搜索词不匹配任何一个字段，就过滤掉
      if (!username.includes(searchLower) && 
          !email.includes(searchLower) && 
          !phone.includes(searchLower) && 
          !patientName.includes(searchLower)) {
        return false;
      }
    }
    
    // 状态筛选
    if (filters.status !== 'ALL') {
      if (filters.status === 'ACTIVE' && auth.status !== 'ACTIVE') {
        return false;
      } else if (filters.status === 'INACTIVE' && auth.status === 'ACTIVE') {
        // 未激活包括所有非ACTIVE状态
        return false;
      }
    }
    
    return true;
  });

  // 获取筛选后的授权数据
  const getFilteredAuthorizations = (): Authorization[] => {
    return authorizersData.filter(auth => {
      // 搜索筛选 - 扩展到多个字段
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        const username = auth.authorized?.username?.toLowerCase() || '';
        const email = auth.authorized?.email?.toLowerCase() || '';
        const phone = auth.authorized?.phone_number?.toLowerCase() || '';
        const patientName = auth.patient?.name?.toLowerCase() || '';
        
        // 如果搜索词不匹配任何一个字段，就过滤掉
        if (!username.includes(searchLower) && 
            !email.includes(searchLower) && 
            !phone.includes(searchLower) && 
            !patientName.includes(searchLower)) {
          return false;
        }
      }
      
      // 状态筛选
      if (filters.status !== 'ALL') {
        if (filters.status === 'ACTIVE' && auth.status !== 'ACTIVE') {
          return false;
        } else if (filters.status === 'INACTIVE' && auth.status === 'ACTIVE') {
          // 未激活包括所有非ACTIVE状态
          return false;
        }
      }
      
      return true;
    });
  };

  // 渲染权限级别选择器
  const renderPrivacyLevel = (auth: Authorization) => {
    const { id, privacyLevel, privacy_level, status } = auth;
    const actualPrivacyLevel = privacyLevel || privacy_level || 'STANDARD';
    const isRevoked = status === 'REVOKED';
    
    // 只有已撤销状态禁用下拉框，完全移除用户验证逻辑
    const disabled = isRevoked;
    
    console.log('权限级别渲染:', {
      授权ID: id,
      当前权限级别: actualPrivacyLevel,
      撤销状态: isRevoked,
      禁用状态: disabled
    });
    
    return (
      <FormControl size="small" sx={{ minWidth: 120 }} disabled={disabled}>
        <Select
          value={actualPrivacyLevel}
          onChange={(e) => {
            console.log('选择权限级别:', {
              授权ID: id,
              新权限级别: e.target.value,
              当前用户: user?.id
            });
            handleUpdatePrivacyLevel(id, e.target.value as string);
          }}
          size="small"
        >
          {Object.entries(privacyLevelMap).map(([key, { label }]) => (
            <MenuItem key={key} value={key}>
              {label}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    );
  };

  // 修改移动端卡片渲染函数
  const renderMobileCards = () => {
    if (!filteredData || filteredData.length === 0) {
      return (
        <Box sx={{ py: 4, textAlign: 'center' }}>
          <Typography color="text.secondary">
            {authorizersData.length > 0 ? '没有符合筛选条件的授权记录' : '暂无授权记录'}
          </Typography>
          {authorizersData.length > 0 && filteredData.length === 0 && (
            <Button 
              variant="text" 
              color="primary" 
              onClick={handleResetFilter}
              sx={{ mt: 1 }}
            >
              清除筛选条件
            </Button>
          )}
        </Box>
      );
    }

    return (
      <Box sx={{ mt: 2 }}>
        {filteredData.map((auth) => (
          <AuthorizationCard
            key={auth.id}
            auth={auth}
            isAuthorizer={true}
            onSwitchChange={handleUpdateStatus}
            onDelete={handleOpenDeleteDialog}
            onPrivacyLevelChange={handleUpdatePrivacyLevel}
          />
        ))}
      </Box>
    );
  };

  // 处理登录
  const handleLogin = () => {
    console.log('[授权管理] 执行登录跳转函数');
    // 保存当前页面路径，以便登录后可以返回
    const currentPath = window.location.pathname;
    localStorage.setItem('redirectAfterLogin', currentPath);
    
    console.log('[授权管理] 登录跳转前的状态:', {
      当前路径: currentPath,
      token是否存在: !!localStorage.getItem('token'),
      userId是否存在: !!localStorage.getItem('userId')
    });
    
    // 清除认证信息
    localStorage.removeItem('token');
    localStorage.removeItem('userId');
    useAuthStore.getState().clearAuth();
    
    console.log('[授权管理] 即将跳转到登录页');
    window.location.href = '/login'; 
  };

  // 渲染认证错误UI
  const renderAuthErrorUI = () => {
    // 检查错误信息中是否包含认证相关关键词
    const isAuthError = error && (
      error.includes('登录') || 
      error.includes('认证') || 
      error.includes('身份验证') || 
      error.includes('token') || 
      error.includes('过期')
    );
    
    if (isAuthError) {
      return (
        <Box sx={{ mb: 3 }}>
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
          <Button 
            variant="contained" 
            color="primary" 
            onClick={handleLogin}
            sx={{ mr: 1 }}
          >
            重新登录
          </Button>
          <Button 
            variant="outlined"
            onClick={() => window.location.reload()}
          >
            刷新页面
          </Button>
        </Box>
      );
    }
    
    // 如果是一般错误，只显示错误信息
    if (error) {
      return (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      );
    }
    
    return null;
  };

  return (
    <ThemeProvider theme={smallerFontTheme}>
      <Box sx={{ p: 2 }}>
        {/* 页面标题 */}
        <Typography variant="h5" gutterBottom>
          授权管理
        </Typography>
        
        {/* 错误提示 */}
        {error && error.includes('登录信息已过期') ? (
          renderAuthErrorUI()
        ) : error ? (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        ) : null}
        
        {/* 操作栏 */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2, flexWrap: 'wrap', gap: 1 }}>
          <Button
            variant="outlined"
            color="primary"
            startIcon={<FilterListIcon />}
            onClick={() => setFilterVisible(!filterVisible)}
          >
            {filterVisible ? '隐藏筛选' : '显示筛选'}
          </Button>
          
          <Box>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleOpenAddDialog}
              sx={{ mr: 1 }}
            >
              添加授权
            </Button>
            
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={loadAuthorizationData}
              disabled={loading}
            >
              刷新
            </Button>
          </Box>
        </Box>
        
        {/* 筛选面板 */}
        {filterVisible && (
          <Box sx={{ mb: 2 }}>
            <AuthorizationFilter 
              onFilter={handleFilter} 
              onReset={handleResetFilter} 
              isAuthorizer={true}
            />
          </Box>
        )}
        
        {/* 加载指示器 */}
        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        )}
        
        {/* 移动端使用卡片视图 */}
        {isMobile ? (
          renderMobileCards()
        ) : (
          /* 桌面端使用表格视图 */
          <Paper>
            <TableContainer sx={{ maxHeight: 'calc(100vh - 280px)' }}>
              <Table stickyHeader>
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ width: '15%', fontWeight: 'bold' }}>被授权用户</TableCell>
                    <TableCell sx={{ width: '10%', fontWeight: 'bold' }}>角色</TableCell>
                    <TableCell sx={{ width: '10%', fontWeight: 'bold' }}>状态</TableCell>
                    <TableCell sx={{ width: '15%', fontWeight: 'bold' }}>授权级别</TableCell>
                    <TableCell sx={{ width: '15%', fontWeight: 'bold' }}>患者</TableCell>
                    <TableCell sx={{ width: '15%', fontWeight: 'bold' }}>创建时间</TableCell>
                    <TableCell sx={{ width: '10%', fontWeight: 'bold' }}>操作</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {!loading && authorizersData.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} align="center">
                        没有匹配的授权记录
                      </TableCell>
                    </TableRow>
                  ) : (
                    getFilteredAuthorizations().map((auth: Authorization) => (
                      <TableRow key={auth.id}>
                        <TableCell>
                          <Tooltip title={`用户ID: ${auth.authorized_id || auth.authorizedId}`}>
                            <span>{auth.authorized?.username || '未知'}</span>
                          </Tooltip>
                        </TableCell>
                        <TableCell>
                          {auth.authorized?.role ? (
                            <Chip
                              label={roleMap[auth.authorized.role]?.label || auth.authorized.role}
                              color={roleMap[auth.authorized.role]?.color || 'default'}
                              size="small"
                            />
                          ) : '未知'}
                        </TableCell>
                        <TableCell>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={auth.status === 'ACTIVE'}
                                onChange={(e) => handleUpdateStatus(auth.id, e.target.checked)}
                                size="small"
                              />
                            }
                            label={typeof auth.status === 'string' && statusMap[auth.status] 
                              ? statusMap[auth.status].label 
                              : (typeof auth.status === 'string' ? auth.status : '未知')}
                          />
                        </TableCell>
                        <TableCell>
                          {renderPrivacyLevel(auth)}
                        </TableCell>
                        <TableCell>
                          {auth.patient?.name || '所有患者'}
                        </TableCell>
                        <TableCell>
                          {(auth.created_at || auth.createdAt) && new Date(auth.created_at || auth.createdAt).toLocaleString('zh-CN', {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </TableCell>
                        <TableCell>
                          <Tooltip title="删除授权">
                            <IconButton
                              size="small"
                              onClick={() => handleOpenDeleteDialog(auth)}
                              color="error"
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        )}
  
        {/* 添加授权对话框 */}
        <Dialog open={openAddDialog} onClose={() => setOpenAddDialog(false)} fullWidth maxWidth="sm">
          <DialogTitle>添加新授权</DialogTitle>
          <DialogContent dividers>
            <Box sx={{ mb: 2 }}>
              <TextField
                label="搜索用户"
                fullWidth
                value={searchQuery}
                onChange={handleSearchQueryChange}
                variant="outlined"
                helperText="输入用户名或电子邮件以搜索"
                InputProps={{
                  endAdornment: (
                    <Button 
                      variant="contained" 
                      size="small" 
                      onClick={handleSearch}
                      disabled={searchLoading || searchQuery.length < 3}
                    >
                      {searchLoading ? '搜索中...' : '搜索'}
                    </Button>
                  ),
                }}
              />
              {searchError && (
                <Alert severity="error" sx={{ mt: 1 }}>
                  {searchError}
                </Alert>
              )}
              
              {searchResults.length > 0 && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle1">搜索结果:</Typography>
                  <Paper variant="outlined" sx={{ maxHeight: 200, overflow: 'auto', mt: 1 }}>
                    {searchResults.map((user) => (
                      <Box 
                        key={user.id}
                        sx={{ 
                          p: 1, 
                          cursor: 'pointer',
                          '&:hover': { bgcolor: 'action.hover' },
                          bgcolor: selectedUser === user.id ? 'action.selected' : 'inherit'
                        }}
                        onClick={() => setSelectedUser(user.id)}
                      >
                        <Typography variant="body1">{user.username}</Typography>
                        <Typography variant="body2" color="text.secondary">{user.email}</Typography>
                      </Box>
                    ))}
                  </Paper>
                </Box>
              )}
            </Box>
            
            {selectedUser && (
              <>
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel id="privacy-level-label">授权级别</InputLabel>
                  <Select
                    labelId="privacy-level-label"
                    value={selectedPrivacyLevel}
                    label="授权级别"
                    onChange={(e) => setSelectedPrivacyLevel(e.target.value)}
                  >
                    <MenuItem value="BASIC">{privacyLevelMap.BASIC.label} - {privacyLevelMap.BASIC.description}</MenuItem>
                    <MenuItem value="STANDARD">{privacyLevelMap.STANDARD.label} - {privacyLevelMap.STANDARD.description}</MenuItem>
                    <MenuItem value="FULL">{privacyLevelMap.FULL.label} - {privacyLevelMap.FULL.description}</MenuItem>
                  </Select>
                </FormControl>
                
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel id="patient-select-label">患者</InputLabel>
                  <Select
                    labelId="patient-select-label"
                    value={selectedPatient}
                    label="患者"
                    onChange={(e) => setSelectedPatient(e.target.value)}
                  >
                    <MenuItem value="">
                      <em>所有患者</em>
                    </MenuItem>
                    {patientsData.map((patient) => (
                      <MenuItem key={patient.id} value={patient.id}>
                        {patient.name}
                      </MenuItem>
                    ))}
                  </Select>
                  {loadingPatients && <CircularProgress size={20} sx={{ ml: 1 }} />}
                </FormControl>
              </>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenAddDialog(false)}>取消</Button>
            <Button 
              onClick={handleCreateAuthorization}
              disabled={!selectedUser}
              variant="contained"
              color="primary"
            >
              创建授权
            </Button>
          </DialogActions>
        </Dialog>
  
        {/* 删除确认对话框 */}
        <Dialog open={openDeleteDialog} onClose={handleCloseDeleteDialog}>
          <DialogTitle>确认删除</DialogTitle>
          <DialogContent>
            <DialogContentText>
              {authToDelete && (
                <>确定要删除授予 <strong>{authToDelete.authorized?.username || '未知用户'}</strong> 的授权吗？此操作不可撤销。</>
              )}
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDeleteDialog}>取消</Button>
            <Button 
              onClick={handleDeleteAuthorization} 
              color="error" 
              disabled={deleteLoading}
              startIcon={deleteLoading ? <CircularProgress size={20} /> : null}
            >
              {deleteLoading ? '删除中...' : '删除'}
            </Button>
          </DialogActions>
        </Dialog>
        
        {/* 通知消息 */}
        <Snackbar
          open={notification.open}
          autoHideDuration={6000}
          onClose={() => setNotification({ ...notification, open: false })}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert 
            onClose={() => setNotification({ ...notification, open: false })} 
            severity={notification.type}
            sx={{ width: '100%' }}
          >
            {notification.message}
          </Alert>
        </Snackbar>
      </Box>
    </ThemeProvider>
  );
};

export default AuthorizationManage;