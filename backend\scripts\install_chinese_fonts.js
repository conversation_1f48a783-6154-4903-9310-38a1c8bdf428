/**
 * 中文字体安装脚本
 * 用于安装本地中文字体，解决PDF生成中文乱码问题
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const os = require('os');

// 字体目录
const fontDir = path.join(__dirname, '..', 'fonts');

// 确保字体目录存在
if (!fs.existsSync(fontDir)) {
  fs.mkdirSync(fontDir, { recursive: true });
  console.log(`创建字体目录: ${fontDir}`);
}

// 检查字体目录中的字体文件
console.log('检查字体目录中的字体文件...');
let fontFiles = [];
try {
  fontFiles = fs.readdirSync(fontDir)
    .filter(file => file.endsWith('.ttf') || file.endsWith('.otf') || file.endsWith('.ttc'));

  if (fontFiles.length > 0) {
    console.log('找到以下字体文件:');
    fontFiles.forEach(file => console.log(`- ${file}`));
  } else {
    console.log('未找到字体文件，请手动将字体文件放入字体目录中。');
    console.log(`字体目录路径: ${fontDir}`);
    console.log('推荐的字体文件:');
    console.log('- SourceHanSansSC-Regular.otf (思源黑体)');
    console.log('- NotoSansSC-Regular.ttf (Noto Sans SC)');
  }
} catch (err) {
  console.error('读取字体目录失败:', err);
}

/**
 * 安装字体到系统
 * @param {string} fontPath - 字体文件路径
 */
function installFontToSystem(fontPath) {
  const platform = os.platform();

  try {
    if (platform === 'win32') {
      // Windows系统
      console.log('Windows系统不需要额外安装，字体文件将直接从应用程序目录加载');
    } else if (platform === 'linux') {
      // Linux系统
      console.log('尝试安装字体到Linux系统...');

      // 创建用户字体目录
      const userFontDir = path.join(os.homedir(), '.fonts');
      if (!fs.existsSync(userFontDir)) {
        fs.mkdirSync(userFontDir, { recursive: true });
      }

      // 复制字体到用户字体目录
      const fontName = path.basename(fontPath);
      const destPath = path.join(userFontDir, fontName);
      fs.copyFileSync(fontPath, destPath);

      // 更新字体缓存
      try {
        execSync('fc-cache -f -v');
        console.log('字体缓存已更新');
      } catch (err) {
        console.warn('无法更新字体缓存，可能需要手动运行: sudo fc-cache -f -v');
      }
    } else if (platform === 'darwin') {
      // macOS系统
      console.log('尝试安装字体到macOS系统...');

      // 创建用户字体目录
      const userFontDir = path.join(os.homedir(), 'Library/Fonts');
      if (!fs.existsSync(userFontDir)) {
        fs.mkdirSync(userFontDir, { recursive: true });
      }

      // 复制字体到用户字体目录
      const fontName = path.basename(fontPath);
      const destPath = path.join(userFontDir, fontName);
      fs.copyFileSync(fontPath, destPath);

      console.log(`字体已安装到: ${destPath}`);
    }
  } catch (err) {
    console.error('安装字体到系统时出错:', err);
  }
}

/**
 * 主函数
 */
function main() {
  console.log('=== 中文字体安装脚本 ===');

  // 如果没有找到字体文件，提示用户
  if (fontFiles.length === 0) {
    console.log('\n请按照以下步骤操作:');
    console.log('1. 在本地计算机上下载中文字体文件');
    console.log('   - 思源黑体: https://github.com/adobe-fonts/source-han-sans/releases');
    console.log('   - Noto Sans SC: https://fonts.google.com/noto/specimen/Noto+Sans+SC');
    console.log(`2. 将字体文件上传到服务器的 ${fontDir} 目录`);
    console.log('3. 再次运行此脚本');
    return;
  }

  // 安装所有找到的字体
  let installedAny = false;

  for (const fontFile of fontFiles) {
    const fontPath = path.join(fontDir, fontFile);

    try {
      // 尝试安装到系统
      installFontToSystem(fontPath);
      installedAny = true;
    } catch (err) {
      console.error(`安装字体 ${fontFile} 失败:`, err);
    }
  }

  if (installedAny) {
    console.log('\n字体安装完成！');
    console.log('PDF生成时应该能够正确显示中文了。');
  } else {
    console.log('\n没有安装新字体。');
  }

  console.log('\n如果PDF中仍然显示乱码，请尝试手动安装中文字体，或联系系统管理员。');
}

// 执行主函数
try {
  main();
} catch (err) {
  console.error('脚本执行失败:', err);
  process.exit(1);
}
