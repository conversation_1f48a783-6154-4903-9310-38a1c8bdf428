import axios, { AxiosResponse, AxiosRequestConfig } from 'axios';
import apiClient from '../apiClient';
import { 
  AIReport, 
  CreateAIReportParams, 
  CreateAIReportResponse,
  ReportVisibilityConfig,
  AIReportContent, // 确保 AIReportContent 也从标准类型导入
  AIReportStatus
} from '../../types/ai-assistant';
import { getToken } from '../../utils/auth';
import { useAuthStore } from '../../store/authStore';
import dataCache from '../../utils/dataCache';
import { API_PATHS } from '../../config/apiPaths'; // 确保 API_PATHS 已导入

// 模拟数据开关
// const MOCK_ENABLED = process.env.REACT_APP_MOCK_ENABLED === 'true'; // 未使用，已注释
const AI_REPORT_CACHE_ENABLED = process.env.REACT_APP_AI_REPORT_CACHE_ENABLED === 'true';

// 辅助函数：深度转换对象键为驼峰命名 (确保这里是唯一的定义)
const toCamelCaseDeep = (obj: any): any => {
  if (Array.isArray(obj)) {
    return obj.map(v => toCamelCaseDeep(v));
  } else if (obj !== null && typeof obj === 'object' && obj.constructor === Object) {
    return Object.keys(obj).reduce(
      (result, key) => {
        const camelCaseKey = key.replace(/([-_][a-z])/gi, ($1) => 
          $1.toUpperCase().replace('-', '').replace('_', '')
        );
        result[camelCaseKey] = toCamelCaseDeep(obj[key]);
        return result;
      },
      {} as any
    );
  }
  return obj;
};

// 辅助函数：深度转换对象键为蛇形命名
// ESLint 报告此函数未使用。如果确认不需要将前端驼峰参数转换为蛇形后再发送给后端，
// 或者 apiClient 已统一处理，则可以保持注释或移除。
/*
const toSnakeCaseDeep = (obj: any): any => {
  if (Array.isArray(obj)) {
    return obj.map(v => toSnakeCaseDeep(v));
  } else if (obj !== null && typeof obj === 'object' && obj.constructor === Object) {
    return Object.keys(obj).reduce(
      (result, key) => {
        const snakeCaseKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
        const finalKey = snakeCaseKey.startsWith('_') ? snakeCaseKey.substring(1) : snakeCaseKey;
        result[finalKey] = toSnakeCaseDeep(obj[key]);
        return result;
      },
      {} as any
    );
  }
  return obj;
};
*/

/**
 * AI报告缓存配置
 */
const AI_REPORT_CACHE_EXPIRY = 5 * 60 * 1000; // AI报告缓存5分钟

// 创建请求去重控制
const processingRequests = new Map<string, Promise<{aiReport: AIReport, status: string}>>();

/**
 * 获取指定病理的所有AI报告
 * @param diseaseId 病理ID
 * @returns AI报告列表
 */
export const getAIReports = async (params?: { diseaseId?: string }): Promise<AIReport[]> => {
  console.log('[aiReportService] 开始获取AI报告列表...');
  console.log('[aiReportService] 请求参数:', params);
  
  try {
    const response = await apiClient.get('/api/ai-reports', { params });
    console.log('[aiReportService] 获取到报告列表响应:', response);
    
    // 检查响应数据
    if (!response || !response.data) {
      console.error('[aiReportService] 响应数据为空');
      return [];
    }

    // 确保响应数据是数组
    const reports = Array.isArray(response.data) ? response.data : [];
    console.log('[aiReportService] 处理后的报告数据:', reports);
    
    return reports.map(report => ({
      ...report,
      createdAt: report.createdAt || new Date().toISOString(),
      updatedAt: report.updatedAt || new Date().toISOString(),
      status: report.status || 'UNKNOWN',
      title: report.title || 'AI分析报告',
      patientName: report.patientName || '未知患者',
      diseaseName: report.diseaseName || '未知病理'
    }));
  } catch (error) {
    console.error('[aiReportService] 获取报告列表失败:', error);
    throw error;
  }
};

/**
 * 获取单个AI报告详情
 * @param reportId 报告ID
 * @returns AI报告详情
 */
export const getAIReport = async (reportId: string): Promise<AIReport> => {
  try {
    if (AI_REPORT_CACHE_ENABLED) {
      const cacheKey = `ai-report:${reportId}`;
      const cachedData = dataCache.get<AIReport>(cacheKey);
      if (cachedData) {
        console.log(`[aiReportService] 使用缓存的AI报告详情, reportId: ${reportId}`);
        return cachedData;
      }
    }

    // 添加时间戳参数，防止缓存
    const timestamp = new Date().getTime();
    
    // 添加缓存控制头，确保获取最新数据
    const response = await apiClient.get<any>(`/api/ai-reports/${reportId}?_t=${timestamp}`, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      },
      timeout: 10000 // 增加超时时间到10秒
    });
    
    const responseData = toCamelCaseDeep(response.data); // responseData 现在是驼峰命名的报告对象

    // TODO: 确认 getMockAIReport 是否仍然需要或应该如何处理
    // const report = responseData.aiReport as AIReport; // || getMockAIReport();  // 旧代码
    const report = responseData as AIReport; // || getMockAIReport(); // 修改后的代码：假设 responseData 本身就是 AIReport 对象
    
    if (AI_REPORT_CACHE_ENABLED && report) { // 确保 report 存在再缓存
      dataCache.set(`ai-report:${reportId}`, report, null, {
        expiry: AI_REPORT_CACHE_EXPIRY
      });
    }
    
    return report; // 现在应该返回正确的报告对象
  } catch (error) {
    console.error('获取AI报告详情失败', error); // 此处的 console.error 仍然可能在API调用失败时触发
    throw error; // 将错误抛出，由调用方（如 AIReportDetailPage.tsx）处理
  }
};

/**
 * 创建AI报告
 * @param params 创建参数
 * @returns Promise<{aiReport: AIReport, status: string}>
 */
export const createAIReport = async (params: CreateAIReportParams): Promise<{aiReport: AIReport, status: string}> => {
  try {
    console.log('[createAIReport] 开始创建AI报告，参数(驼峰):', params);
    
    // 生成请求唯一键
    const requestKey = `${params.diseaseId}:${params.patientId}`;
    
    // 检查是否有相同参数的请求正在处理中
    if (processingRequests.has(requestKey)) {
      console.warn('[createAIReport] 检测到重复请求，正在复用已有请求', requestKey);
      return processingRequests.get(requestKey)!;
    }
    
    // 创建并存储请求Promise
    const requestPromise = (async () => {
      try {
        // 添加请求时间戳，帮助服务端识别重复请求
        const requestWithTimestamp = {
          ...params,
          clientRequestId: `${Math.random().toString(36).substring(2, 15)}_${Date.now()}`
        };
        
        // 将创建报告的POST请求超时时间增加到180秒
        // 使用API_PATHS常量确保路径一致性
        const response = await apiClient.post(API_PATHS.AI_REPORT.CREATE, requestWithTimestamp, { timeout: 180000 });
        
        // 记录接口响应信息
        console.log('[createAIReport] 收到后端响应 (原始):', response.data);
        
        // 确保返回数据符合预期结构
        if (!response.data) {
          throw new Error('服务器返回了空响应');
        }
        
        // 转换为驼峰命名风格 (如果需要)
        const responseData = response.data;
        
        // 记录转换后的响应
        console.log('[createAIReport] 收到后端响应 (转换后驼峰):', responseData);
        
        // 检查是否已经有报告正在处理中
        if (responseData.status === 'ALREADY_PROCESSING') {
          console.log('[createAIReport] 已有报告正在生成中', responseData);
          
          // 检查是否有aiReport字段
          if (!responseData.aiReport) {
            console.error('[createAIReport] 响应中缺少aiReport字段:', responseData);
            throw new Error('服务器返回了不完整的数据');
          }
          
          // 返回已有的报告和状态信息
          return {
            aiReport: responseData.aiReport,
            status: responseData.status
          };
        }
        
        // 检查是否有明确的失败状态
        if (responseData.status === 'FAILED') {
          console.error('[createAIReport] 报告创建失败:', responseData.message, responseData.error);
          throw new Error(responseData.message || 'LLM服务暂时不可用，请稍后再试');
        }
        
        // 检查是否有aiReport字段
        if (!responseData.aiReport) {
          console.error('[createAIReport] 响应中缺少aiReport字段:', responseData);
          throw new Error('服务器返回了不完整的数据');
        }
        
        // 返回报告和状态信息
        return {
          aiReport: responseData.aiReport,
          status: responseData.status
        };
      } finally {
        // 无论成功失败，都从Map中移除这个请求
        setTimeout(() => {
          processingRequests.delete(requestKey);
          console.log('[createAIReport] 请求完成，已从处理中移除', requestKey);
        }, 1000); // 延迟1秒移除，防止极端情况下的并发问题
      }
    })();
    
    // 保存请求Promise
    processingRequests.set(requestKey, requestPromise);
    
    // 等待并返回结果
    return await requestPromise;
  } catch (error) {
    console.error('[createAIReport] 捕获错误:', error);
    // 使用类型断言处理unknown类型
    const err = error as any;
    const errorMessage = err.response?.data?.message || err.message || 'AI报告生成失败';
    console.error('[createAIReport] 错误消息:', errorMessage);
    
    // 将错误转发给调用者
    throw new Error(errorMessage);
  }
};

/**
 * 轮询AI报告状态，直到报告完成或失败
 * @param reportId 报告ID
 * @param maxAttempts 最大尝试次数
 * @param onStatusUpdate 状态更新回调
 * @returns Promise<AIReport>
 */
export const pollReportStatus = async (
  reportId: string, 
  maxAttempts: number = 60,
  onStatusUpdate?: (status: string, report: AIReport, attempts: number) => void
): Promise<AIReport> => {
  try {
    let attempts = 0;
    let lastStatus = '';
    let cachedReportIds: string[] = [];

    // 计算下一次轮询的等待时间
    const getNextInterval = (attempt: number): number => {
      if (attempt === 0) return 30000;  // 第一次等待30秒
      if (attempt === 1) return 20000;  // 第二次等待20秒
      if (attempt === 2) return 10000;  // 第三次等待10秒
      return 5000;  // 之后每5秒一次
    };

    // 创建轮询函数
    const poll = async (): Promise<AIReport> => {
      attempts++;
      const nextInterval = getNextInterval(attempts - 1);
      console.log(`[AI报告] 轮询状态 (${attempts}/${maxAttempts}), 下次等待 ${nextInterval/1000} 秒`, reportId);
      
      try {
        // 添加当前ID到缓存，防止同时轮询多个报告
        if (!cachedReportIds.includes(reportId)) {
          cachedReportIds.push(reportId);
          if (cachedReportIds.length > 5) {
            cachedReportIds.shift();
          }
        }
        
        // 获取报告状态
        let report: AIReport;
        try {
          report = await getAIReport(reportId);
        } catch (fetchError) {
          console.error(`[AI报告] 获取报告失败，尝试备用方法:`, fetchError);

          // 如果获取失败，等待后重试
          if (attempts < maxAttempts) {
            console.log(`[AI报告] 等待后重试 (${attempts}/${maxAttempts})...`);
            await new Promise(resolve => setTimeout(resolve, nextInterval));
            return poll();
          } else {
            throw new Error('获取报告状态超时');
          }
        }
        
        // 打印完整报告状态便于调试
        console.log(`[AI报告] 获取到报告状态:`, {
          id: report.id,
          status: report.status,
          errorMessage: report.errorMessage,
          lastStatus: lastStatus,
          attempts: attempts,
          nextInterval: nextInterval
        });
        
        // 增强状态检查
        const status = report.status?.toUpperCase() || '';
        const isCompleted = status === 'COMPLETED' || status === 'SUCCESS';
        const isFailed = status === 'FAILED' || status === 'ERROR';
        
        // 如果状态有变化，通知回调
        if (status !== lastStatus) {
          lastStatus = status;
          if (onStatusUpdate) {
            onStatusUpdate(status, report, attempts);
          }
        }
        
        // 如果报告已完成或失败，返回结果
        if (isCompleted) {
          console.log('[AI报告] 报告已完成', report);
          return report;
        }
        
        if (isFailed) {
          console.error('[AI报告] 报告生成失败', {
            errorMessage: report.errorMessage,
            report
          });
          
          if (onStatusUpdate) {
            onStatusUpdate('FAILED', report, attempts);
          }
          
          throw new Error(`报告生成失败: ${report.errorMessage || '未知错误'}`);
        }
        
        // 如果达到最大尝试次数，抛出异常
        if (attempts >= maxAttempts) {
          console.error('[AI报告] 轮询超过最大尝试次数', {
            reportId,
            attempts,
            maxAttempts
          });
          
          if (onStatusUpdate) {
            const errorReport = { 
              ...report,
              status: 'FAILED' as AIReportStatus,
              errorMessage: `超过最大轮询次数 ${maxAttempts}，请稍后刷新页面检查结果`
            };
            onStatusUpdate('TIMEOUT', errorReport, attempts);
          }
          
          throw new Error(`报告生成超时，请稍后刷新页面检查结果`);
        }
        
        // 等待指定时间后继续轮询
        await new Promise(resolve => setTimeout(resolve, nextInterval));
        return poll();
      } catch (error) {
        // 如果是特定错误，直接抛出
        if (error instanceof Error && 
           (error.message.includes('报告生成失败') || 
            error.message.includes('超时'))) {
          throw error;
        }
        
        console.error(`[AI报告] 轮询过程中出错 (${attempts}/${maxAttempts})`, error);
        
        // 如果是最后一次尝试，直接抛出错误
        if (attempts >= maxAttempts) {
          throw new Error(`轮询报告状态失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
        
        // 否则等待后重试，使用当前间隔的1.5倍
        const retryInterval = nextInterval * 1.5;
        console.log(`[AI报告] ${retryInterval/1000}秒后重试...`);
        await new Promise(resolve => setTimeout(resolve, retryInterval));
        return poll();
      }
    };
    
    // 开始轮询
    return poll();
  } catch (error) {
    console.error('[AI报告] 轮询过程中发生错误', error);
    throw error;
  }
};

/**
 * 删除AI报告
 * @param reportId 报告ID
 * @returns 操作结果
 */
export const deleteAIReport = async (reportId: string): Promise<{ success: boolean }> => {
  try {
    let diseaseId: string | null = null;
    if (AI_REPORT_CACHE_ENABLED) {
      try {
        const report = await getAIReport(reportId);
        if (report) { // 确保 report 存在
          diseaseId = report.diseaseId;
        }
      } catch (e) {
        console.warn('获取报告详情失败，无法精确清除缓存:', e);
      }
    }
    
    const response = await apiClient.delete<any>(`/api/ai-reports/${reportId}`);
    const responseData = toCamelCaseDeep(response.data);
    
    if (AI_REPORT_CACHE_ENABLED) {
      dataCache.delete(`ai-report:${reportId}`);
      if (diseaseId) {
        dataCache.clearByPrefix(`ai-reports:${diseaseId}`);
      } else {
        dataCache.clearByPrefix('ai-reports:'); // 清除所有疾病报告列表缓存，如果无法确定 diseaseId
      }
    }
    
    return { success: responseData.status === 'SUCCESS' }; // 假设成功响应包含 status: 'SUCCESS'
  } catch (error) {
    console.error('删除AI报告失败', error);
    throw error;
  }
};

/**
 * 下载AI报告PDF
 * @param reportId 报告ID
 * @returns 下载的Blob对象
 */
export const downloadAIReportPDF = async (reportId: string): Promise<void> => {
  try {
    const response = await apiClient.get(`/api/ai-reports/${reportId}/pdf`, {
      responseType: 'blob'
    });
    
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `ai-report-${reportId}.pdf`);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('[downloadAIReportPDF] 下载PDF失败', error);
    throw error;
  }
};

/**
 * 获取报告显示配置
 * @returns 报告显示配置
 */
export const getReportConfig = async (): Promise<ReportVisibilityConfig> => {
  try {
    const response = await apiClient.get<any>('/api/ai-reports/config');
    return toCamelCaseDeep(response.data) as ReportVisibilityConfig;
  } catch (error) {
    console.error('获取报告配置失败', error);
    throw error;
  }
};

/**
 * 保存报告显示配置（仅管理员可用）
 * @param config 报告显示配置
 * @returns 操作结果
 */
export const saveReportConfig = async (config: ReportVisibilityConfig): Promise<{ success: boolean }> => {
  try {
    const response = await apiClient.post<any>(`/api/ai-reports/config`, config);
    const responseData = toCamelCaseDeep(response.data);
    return { success: responseData.status === 'SUCCESS' }; // 假设成功响应包含 status: 'SUCCESS'
  } catch (error) {
    console.error('保存报告配置失败', error);
    throw error;
  }
};

/**
 * 检查用户当前配额使用情况
 * @returns 配额使用状态
 */
export const checkUserQuota = async (): Promise<{ used: number; max: number; canUse: boolean }> => {
  try {
    console.log('[aiReportService] 开始检查用户配额...');
    const response = await apiClient.get<any>('/api/ai-reports/quota');
    console.log('[aiReportService] 获取到配额响应:', response.data);

    // 转换响应数据为驼峰命名
    const responseData = toCamelCaseDeep(response.data);
    return responseData;
  } catch (error) {
    console.error('[aiReportService] 检查用户配额失败:', error);
    throw error;
  }
};

/**
 * 重新生成AI报告
 * @param params 创建参数
 * @returns Promise<{aiReport: AIReport, status: string}>
 */
export const regenerateAIReport = async (params: CreateAIReportParams): Promise<{aiReport: AIReport, status: string}> => {
  console.log('[regenerateAIReport] 开始重新生成AI报告，参数:', params);
    
  const requestKey = `regenerate:${params.diseaseId}:${params.patientId}`;
  
  if (processingRequests.has(requestKey)) {
    console.warn('[regenerateAIReport] 检测到重复的重新生成请求，正在复用已有请求', requestKey);
    // 类型断言，因为我们知道Map中存储的是此Promise类型
    return processingRequests.get(requestKey)! as Promise<{aiReport: AIReport, status: string}>; 
  }

  const requestPromise = (async (): Promise<{aiReport: AIReport, status: string}> => {
    try {
      const paramsWithTimestamp = {
        ...params,
        clientRequestId: `${Math.random().toString(36).substring(2, 15)}_${Date.now()}`,
        isRegenerate: true, // 添加此标志以帮助后端识别
      };

      // 将重新生成报告的POST请求超时时间增加到180秒
      // CreateAIReportResponse 包含 aiReport: AIReport | null | undefined
      // 我们需要确保返回 {aiReport: AIReport, status: string}
      const response = await apiClient.post<
        // 后端实际返回 CreateAIReportResponse 类型
        CreateAIReportResponse 
      >(
        API_PATHS.AI_REPORT.CREATE, // 使用 /ai-reports 端点
        paramsWithTimestamp,
        { timeout: 180000 } // 修改超时时间
      );
      
      console.log('[regenerateAIReport] 收到后端响应 (原始):', response.data);
      
      if (!response.data || !response.data.aiReport || !response.data.status) {
        console.error('[regenerateAIReport] 响应不完整:', response.data);
        throw new Error('服务器返回了不完整的数据（缺少aiReport或status）');
      }

      // 即使 status 是 FAILED 或 ALREADY_PROCESSING，aiReport 也可能存在
      // 但为了满足 Promise<{aiReport: AIReport, status: string}> 类型，
      // 如果 aiReport 为 null/undefined，我们需要处理这种情况
      if (!response.data.aiReport) {
           // 如果aiReport为空，但状态不是成功，这是个问题，但为了类型匹配先抛错
          // 或者可以构造一个临时的AIReport对象，但这可能不是期望行为
          console.error('[regenerateAIReport] 响应中aiReport为空，但类型要求非空', response.data);
          throw new Error('重新生成报告后，响应中的aiReport为空。');
    }
    
    return {
        aiReport: response.data.aiReport, // 此处aiReport已确保非空
        status: response.data.status
      };

    } catch (error) {
      console.error('[regenerateAIReport] 捕获错误:', error);
      const err = error as any;
      const errorMessage = err.response?.data?.message || err.message || 'AI报告重新生成失败';
      console.error('[regenerateAIReport] 错误消息:', errorMessage);
      throw new Error(errorMessage);
    } finally {
      setTimeout(() => {
        processingRequests.delete(requestKey);
        console.log('[regenerateAIReport] 请求完成，已从处理中移除', requestKey);
      }, 1000);
    }
  })();

  processingRequests.set(requestKey, requestPromise);
  return requestPromise;
};

// 模拟数据 (如果 MOCK_ENABLED 确实不再使用，此函数也可能变为未使用)
/* // getMockAIReport 似乎在 getAIReport 中被注释掉了，如果确认不用，可以移除
const getMockAIReport = (): AIReport => ({
  id: '1',
  diseaseId: '1',
  patientId: '1',
  userId: '1',
  title: '模拟AI分析报告 1',
  templateType: 'COMPREHENSIVE_ANALYSIS',
  status: 'COMPLETED',
  content: {
    summary: '模拟的报告摘要',
    differentialDiagnosis: { possibleConditions: [] },
    emergencyGuidance: { isEmergency: false, immediateActions: [], nextSteps: [] },
    hospitalRecommendations: { 
      targetRegion: '北京',
      hospitals: [] 
    },
    treatmentPlan: { options: [] },
    lifestyleAndMentalHealth: {
      lifestyle: { diet: [], exercise: [], habits: [] },
      mentalHealth: { copingStrategies: [], resources: [] }
    },
    dashboardData: {
      status: '稳定',
      trend: 'stable',
      riskLevel: 'low',
      isEmergency: false,
      topHospital: '北京协和医院',
      budgetRange: '10000-20000'
    },
    riskWarnings: [],
    is_chronic_disease: false
  },
  recordId: '1',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
});
*/