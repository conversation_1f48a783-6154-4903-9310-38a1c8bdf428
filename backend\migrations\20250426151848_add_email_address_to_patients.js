/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return Promise.all([
    knex.schema.hasColumn('patients', 'email').then(exists => {
      if (!exists) {
        return knex.schema.table('patients', (table) => {
          table.string('email').nullable();
        });
      }
      return Promise.resolve();
    }),
    knex.schema.hasColumn('patients', 'address').then(exists => {
      if (!exists) {
        return knex.schema.table('patients', (table) => {
          table.string('address').nullable();
        });
      }
      return Promise.resolve();
    })
  ]);
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return Promise.all([
    knex.schema.hasColumn('patients', 'email').then(exists => {
      if (exists) {
        return knex.schema.table('patients', (table) => {
          table.dropColumn('email');
        });
      }
      return Promise.resolve();
    }),
    knex.schema.hasColumn('patients', 'address').then(exists => {
      if (exists) {
        return knex.schema.table('patients', (table) => {
          table.dropColumn('address');
        });
      }
      return Promise.resolve();
    })
  ]);
};
