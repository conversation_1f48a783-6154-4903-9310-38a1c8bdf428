const axios = require('axios');
const chalk = require('chalk');

// API基础URL，使用我们配置的3001端口
const API_URL = 'http://localhost:3001';

// 存储测试数据
const testData = {
  token: null,
  userId: null,
  patients: []
};

// 测试用户登录信息
const TEST_USER = {
  username: 'testadmin',
  password: 'test123'
};

/**
 * 格式化响应时间
 * @param {number} ms - 响应时间(毫秒)
 * @returns {string} - 格式化的时间字符串
 */
function formatTime(ms) {
  if (ms < 1000) {
    return `${ms}ms`;
  } else {
    return `${(ms / 1000).toFixed(2)}s`;
  }
}

/**
 * 记录测试结果
 * @param {string} testName - 测试名称
 * @param {number} time - 执行时间
 * @param {boolean} success - 是否成功
 * @param {string} message - 附加信息
 */
function logResult(testName, time, success, message = '') {
  const status = success ? chalk.green('✓ 成功') : chalk.red('✗ 失败');
  const timeStr = chalk.yellow(formatTime(time));
  console.log(`${testName}: ${status} - 耗时: ${timeStr}`);
  if (message) {
    console.log(`  ${message}`);
  }
}

/**
 * 测量函数执行时间
 * @param {Function} fn - 要测量的异步函数
 * @returns {Promise<{time: number, result: any, success: boolean}>} - 执行结果
 */
async function measureTime(fn) {
  const start = Date.now();
  try {
    const result = await fn();
    const time = Date.now() - start;
    return { time, result, success: true };
  } catch (error) {
    const time = Date.now() - start;
    return { time, error, success: false };
  }
}

/**
 * 获取JWT令牌
 */
async function getToken() {
  const { time, result, success } = await measureTime(async () => {
    const response = await axios.post(`${API_URL}/login`, TEST_USER);
    testData.token = response.data.token;
    return response.data;
  });

  logResult('登录获取令牌', time, success, success ? 
    `获取Token成功` : 
    `登录失败: ${JSON.stringify(result?.error?.response?.data || {})}`);
    
  return testData.token;
}

/**
 * 测试添加患者
 */
async function testAddPatient() {
  const headers = { Authorization: `Bearer ${testData.token}` };
  
  // 测试数据
  const patientData = {
    name: `测试患者-${Date.now()}`,
    gender: '男',
    phoneNumber: '13800138000',
    birthDate: '1990-01-01',
    bloodType: 'A',
    isPrimary: 1 // 设置为本人
  };
  
  const { time, result, success } = await measureTime(async () => {
    const response = await axios.post(`${API_URL}/patients`, patientData, { headers });
    if (response.data && response.data.patient) {
      testData.patients.push(response.data.patient.id);
    }
    return response.data;
  });

  logResult('添加患者', time, success, success ? 
    `患者ID: ${result.patient?.id}, 姓名: ${patientData.name}` : 
    `添加失败: ${JSON.stringify(result?.error?.response?.data || {})}`);
    
  // 返回新添加的患者ID
  return success ? result.patient?.id : null;
}

/**
 * 测试获取患者列表
 */
async function testGetPatients() {
  const headers = { Authorization: `Bearer ${testData.token}` };
  
  const { time, result, success } = await measureTime(async () => {
    const response = await axios.get(`${API_URL}/patients`, { headers });
    return response.data;
  });

  logResult('获取患者列表', time, success, success ? 
    `获取成功, 共 ${result.length} 个患者` : 
    `获取失败: ${JSON.stringify(result?.error?.response?.data || {})}`);
    
  // 测试索引性能
  if (time < 100) {
    console.log(chalk.green(`  索引性能良好 (${time}ms < 100ms)`));
  } else {
    console.log(chalk.yellow(`  索引性能可能需要优化 (${time}ms > 100ms)`));
  }
  
  return success ? result : null;
}

/**
 * 测试获取单个患者
 * @param {string} patientId - 患者ID
 */
async function testGetPatient(patientId) {
  if (!patientId) {
    console.log(chalk.yellow('  未提供患者ID，跳过获取患者详情测试'));
    return null;
  }
  
  const headers = { Authorization: `Bearer ${testData.token}` };
  
  const { time, result, success } = await measureTime(async () => {
    const response = await axios.get(`${API_URL}/patients/${patientId}`, { headers });
    return response.data;
  });

  logResult('获取患者详情', time, success, success ? 
    `患者ID: ${result.id}, 姓名: ${result.name}` : 
    `获取失败: ${JSON.stringify(result?.error?.response?.data || {})}`);
    
  return success ? result : null;
}

/**
 * 测试更新患者
 * @param {string} patientId - 患者ID
 */
async function testUpdatePatient(patientId) {
  if (!patientId) {
    console.log(chalk.yellow('  未提供患者ID，跳过更新患者测试'));
    return null;
  }
  
  const headers = { Authorization: `Bearer ${testData.token}` };
  
  // 更新数据
  const updateData = {
    name: `更新患者-${Date.now()}`,
    gender: '女',
    phoneNumber: '13900139000',
    birthDate: '1991-02-02',
    bloodType: 'B'
  };
  
  const { time, result, success } = await measureTime(async () => {
    const response = await axios.put(`${API_URL}/patients/${patientId}`, updateData, { headers });
    return response.data;
  });

  logResult('更新患者', time, success, success ? 
    `更新成功, 姓名: ${updateData.name}` : 
    `更新失败: ${JSON.stringify(result?.error?.response?.data || {})}`);
    
  return success ? result : null;
}

/**
 * 测试isPrimary逻辑
 */
async function testIsPrimaryLogic() {
  const headers = { Authorization: `Bearer ${testData.token}` };
  
  // 1. 获取当前患者列表
  const patients = await testGetPatients();
  if (!patients) return;
  
  // 2. 找出当前设置为本人的患者
  const primaryPatients = patients.filter(p => p.isPrimary === 1);
  console.log(`  当前有 ${primaryPatients.length} 个本人档案`);
  
  if (primaryPatients.length === 0) {
    console.log(chalk.yellow('  当前没有本人档案，无法进行测试'));
    return;
  }
  
  // 3. 取第一个非本人患者，如果都是本人则创建一个新的
  let nonPrimaryPatient = patients.find(p => p.isPrimary !== 1);
  
  if (!nonPrimaryPatient) {
    console.log(chalk.yellow('  没有非本人档案，创建一个新的'));
    // 创建一个非本人档案
    await testAddPatient();
    // 重新获取患者列表
    const updatedPatients = await testGetPatients();
    nonPrimaryPatient = updatedPatients.find(p => p.isPrimary !== 1);
  }
  
  if (!nonPrimaryPatient) {
    console.log(chalk.red('  无法创建或找到非本人档案，测试失败'));
    return;
  }
  
  // 4. 将非本人档案设置为本人
  console.log(`  将患者 ${nonPrimaryPatient.name} (ID: ${nonPrimaryPatient.id}) 设置为本人`);
  
  const { time, result, success } = await measureTime(async () => {
    const response = await axios.put(`${API_URL}/patients/${nonPrimaryPatient.id}`, {
      ...nonPrimaryPatient,
      isPrimary: 1
    }, { headers });
    return response.data;
  });

  logResult('切换本人档案', time, success, success ? 
    `切换成功` : 
    `切换失败: ${JSON.stringify(result?.error?.response?.data || {})}`);
    
  // 5. 验证是否只有一个本人档案
  const finalPatients = await testGetPatients();
  const finalPrimaryPatients = finalPatients.filter(p => p.isPrimary === 1);
  
  if (finalPrimaryPatients.length === 1) {
    console.log(chalk.green('  本人档案逻辑验证成功：只有一个本人档案'));
  } else {
    console.log(chalk.red(`  本人档案逻辑验证失败：有 ${finalPrimaryPatients.length} 个本人档案`));
  }
}

/**
 * 测试删除患者
 * @param {string} patientId - 患者ID
 */
async function testDeletePatient(patientId) {
  if (!patientId) {
    console.log(chalk.yellow('  未提供患者ID，跳过删除患者测试'));
    return;
  }
  
  const headers = { Authorization: `Bearer ${testData.token}` };
  
  const { time, result, success } = await measureTime(async () => {
    const response = await axios.delete(`${API_URL}/patients/${patientId}`, { headers });
    return response.data;
  });

  logResult('删除患者', time, success, success ? 
    `删除成功` : 
    `删除失败: ${JSON.stringify(result?.error?.response?.data || {})}`);
    
  // 从测试数据中移除
  if (success) {
    const index = testData.patients.indexOf(patientId);
    if (index !== -1) {
      testData.patients.splice(index, 1);
    }
  }
  
  return success;
}

/**
 * 清理测试数据
 */
async function cleanup() {
  console.log(chalk.cyan('\n清理测试数据...'));
  
  const headers = { Authorization: `Bearer ${testData.token}` };
  
  for (const patientId of testData.patients) {
    try {
      await axios.delete(`${API_URL}/patients/${patientId}`, { headers });
      console.log(`  已删除患者 ${patientId}`);
    } catch (error) {
      console.log(chalk.red(`  删除患者 ${patientId} 失败`));
    }
  }
  
  console.log(chalk.green('清理完成'));
}

/**
 * 运行所有测试
 */
async function runTests() {
  console.log(chalk.cyan('开始患者API测试...\n'));
  
  // 获取令牌
  const token = await getToken();
  if (!token) {
    console.log(chalk.red('未能获取有效的JWT令牌，测试终止'));
    return;
  }
  
  // 测试添加患者
  const patientId = await testAddPatient();
  
  // 测试获取患者列表
  await testGetPatients();
  
  // 测试获取单个患者
  await testGetPatient(patientId);
  
  // 测试更新患者
  await testUpdatePatient(patientId);
  
  // 测试isPrimary逻辑
  await testIsPrimaryLogic();
  
  // 测试删除患者
  await testDeletePatient(patientId);
  
  // 清理测试数据
  await cleanup();
  
  console.log(chalk.cyan('\n所有测试完成'));
}

// 执行测试
runTests(); 