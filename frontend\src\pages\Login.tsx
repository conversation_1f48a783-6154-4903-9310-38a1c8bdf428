import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { 
  Box, 
  Paper, 
  Typography, 
  TextField, 
  Button, 
  Stack,
  Link,
  Alert,
  Checkbox,
  FormControlLabel,
  InputAdornment,
  IconButton,
  CircularProgress
} from '@mui/material';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import * as authService from '../services/authService';
import { LoginResponse } from '../services/authService';

// 登录页面组件
const LoginPage: React.FC = () => {
  // 导航和位置钩子
  const navigate = useNavigate();
  const location = useLocation();
  
  // 表单状态
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // 检查是否有保存的用户名
  useEffect(() => {
    const savedUsername = localStorage.getItem('rememberUser');
    if (savedUsername) {
      setUsername(savedUsername);
      setRememberMe(true);
    }
  }, []);

  // 登录后的跳转路径，默认为个人资料页
  const from = (location.state as any)?.from?.pathname || '/dashboard';

  // 处理登录表单提交
  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setLoading(true);
    setError('');
    
    try {
      // 使用authService.login登录
      console.log('正在调用登录服务...');
      const result: LoginResponse = await authService.login(username, password).catch(err => {
        // 特殊处理：如果登录成功但获取用户信息失败
        if (err.isLoginSuccessProfileFailed) {
          console.warn('登录成功但获取用户资料失败，继续处理登录成功流程');
          return { token: err.token, user: null } as LoginResponse;
        }
        throw err;
      });
      
      console.log('登录结果:', { 
        token: result.token ? '已接收' : '未接收', 
        user: result.user ? '已接收' : '未接收',
        redirectTo: result.redirectTo || '无'
      });
      
      if (result.token) {
        // 如果勾选了记住我，保存用户名
        if (rememberMe) {
          localStorage.setItem('rememberUser', username);
        } else {
          localStorage.removeItem('rememberUser');
        }
        
        // 确保用户信息正确存储
        if (result.user && result.user.id) {
          console.log('用户ID已保存:', result.user.id);
        }
        
        // 检查是否有特定的重定向路径
        if (result.redirectTo) {
          console.log('根据登录服务的建议重定向到:', result.redirectTo);
          navigate(result.redirectTo, { replace: true });
        } else {
          console.log('登录成功，按一般流程导航到:', from);
          navigate(from, { replace: true });
        }
      } else {
        setError('登录失败: 未收到有效令牌');
      }
    } catch (err: any) {
      console.error('登录错误:', err);
      setError(err.response?.data?.error || err.response?.data?.message || '登录失败，请检查用户名和密码');
    } finally {
      setLoading(false);
    }
  };

  // 切换密码可见性
  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <Box sx={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      minHeight: '100vh',
      bgcolor: 'background.default', // 使用主题的背景色而不是硬编码的颜色
      padding: '10px' // 只保留10px的边距空间
    }}>
      <Paper 
        elevation={0} 
        sx={{ 
          p: 3, // 从p:4减少到p:3，减少总体内边距
          width: '100%', // 占满整个容器宽度，减去外部的10px padding
          maxWidth: { xs: '100%', sm: '500px' }, // 在大屏幕上限制最大宽度
          border: '1px solid',
          borderColor: 'divider', // 使用主题的分隔线颜色
          borderRadius: 2,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center'
        }}
      >
        {/* 兔子图标 */}
        <Box 
          component="img" 
          src="./assets/logo-rabbit.png" 
          alt="医生兔子图标"
          sx={{ 
            width: 90, 
            height: 90, 
            mb: 1,
            borderRadius: '50%',
            bgcolor: (theme) => theme.palette.mode === 'dark' ? '#232323' : '#f5f5f5' // 根据主题选择背景色
          }} 
        />

        {/* APP名称 - 字号减小2号并保持加粗 */}
        <Typography 
          variant="h5" 
          component="h1" 
          align="center" 
          sx={{ 
            color: '#3498db', 
            fontWeight: 700, 
            mb: 0,
            lineHeight: 1.1
          }}
        >
          慧看病
        </Typography>

        {/* Slogan - 字号再减小1号，进一步增大底部间距 */}
        <Typography 
          variant="caption" 
          align="center" 
          sx={{ mb: 5, color: 'text.secondary' }}
        >
          让看病从容一些
        </Typography>

        {/* 错误提示 */}
        {error && (
          <Alert severity="error" sx={{ mb: 1.5, width: '100%' }}>
            {error}
          </Alert>
        )}
        
        {/* 登录表单 */}
        <form onSubmit={handleSubmit} style={{ width: '100%' }}>
          <Stack spacing={1.5}>
            {/* 用户名输入框 - 主色调外框和文字 */}
            <TextField
              label="用户名/邮箱/手机号"
              variant="outlined"
              fullWidth
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              required
              size="small"
              autoComplete="username"
              placeholder="请输入用户名、邮箱或手机号"
              sx={{ 
                '&:hover .MuiInputLabel-root': {
                  color: '#3498db',
                },
                '& .MuiOutlinedInput-root': {
                  '&.Mui-focused fieldset': {
                    borderColor: '#3498db',
                  },
                  '&:hover fieldset': {
                    borderColor: '#3498db',
                  },
                },
                '& .MuiInputLabel-root.Mui-focused': {
                  color: '#3498db',
                },
              }}
            />
            
            {/* 密码输入框 - 主色调外框和文字，添加眼睛图标 */}
            <TextField
              label="密码"
              type={showPassword ? 'text' : 'password'}
              variant="outlined"
              fullWidth
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              size="small"
              autoComplete="current-password"
              sx={{ 
                '&:hover .MuiInputLabel-root': {
                  color: '#3498db',
                },
                '& .MuiOutlinedInput-root': {
                  '&.Mui-focused fieldset': {
                    borderColor: '#3498db',
                  },
                  '&:hover fieldset': {
                    borderColor: '#3498db',
                  },
                },
                '& .MuiInputLabel-root.Mui-focused': {
                  color: '#3498db',
                },
                mb: 1
              }}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={handleTogglePasswordVisibility}
                      edge="end"
                      size="small"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                )
              }}
            />
            
            {/* 记住我复选框和找回密码链接 - 紧贴密码框底部 */}
            <Box sx={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              mt: 0,
              mb: 1,
              px: 0,
              width: '100%'
            }}>
              <FormControlLabel
                control={
                  <Checkbox 
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                    size="small"
                    sx={{
                      color: '#3498db',
                      padding: '0px',
                      '&.Mui-checked': {
                        color: '#FF6F61',
                      },
                      '&:hover': {
                        color: '#FF6F61',
                      }
                    }}
                  />
                }
                label={<Typography variant="body2">记住我</Typography>}
                sx={{ 
                  marginRight: 0, 
                  marginLeft: 0,
                  '& .MuiTypography-root': { 
                    marginLeft: 0.5
                  } 
                }}
              />
              <Link 
                href="/forgot-password" 
                underline="hover" 
                sx={{ 
                  color: '#3498db',
                  fontSize: '0.875rem',
                  '&:hover': {
                    color: '#FF6F61',
                  }
                }}
              >
                找回密码
              </Link>
            </Box>
            
            <Button
              type="submit"
              variant="contained"
              fullWidth
              disableElevation
              sx={{ mt: 2.5 }} 
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : '登录'}
            </Button>
            <Box sx={{ textAlign: 'center', mt: 1 }}>
              <Typography variant="body2" component="span">
                还没有账号？ 
              </Typography>
              <Link 
                href="/register" 
                underline="hover" 
                sx={{ 
                  ml: 1, 
                  color: '#3498db',
                  '&:hover': {
                    color: '#FF6F61',
                  }
                }}
              >
                注册新账号
              </Link>
            </Box>
          </Stack>
        </form>

        {/* 版权声明 - 修改文字 */}
        <Typography 
          variant="caption" 
          align="center" 
          sx={{ mt: 3, color: 'text.secondary' }}
        >
          © 2025 慧看病 · 版权所有 楹旗
        </Typography>
      </Paper>
    </Box>
  );
};

export default LoginPage; 