/**
 * 检查数据库表结构的脚本
 * 用于验证数据库中是否存在所需的表
 */

const knex = require('knex')(require('../knexfile').development);

async function checkDatabaseTables() {
  try {
    console.log('开始检查数据库表结构...');
    
    // 检查用户授权表
    const hasUserAuthorizationsTable = await knex.schema.hasTable('user_authorizations');
    console.log(`表user_authorizations ${hasUserAuthorizationsTable ? '存在' : '不存在'}`);
    
    if (hasUserAuthorizationsTable) {
      // 获取表的列信息
      const columns = await knex('user_authorizations').columnInfo();
      console.log('user_authorizations表列结构:', Object.keys(columns));
      
      // 获取记录数量
      const authCount = await knex('user_authorizations').count('* as count').first();
      console.log(`user_authorizations表中有${authCount.count}条记录`);
    }
    
    // 检查记录表
    const hasRecordsTable = await knex.schema.hasTable('records');
    console.log(`表records ${hasRecordsTable ? '存在' : '不存在'}`);
    
    if (hasRecordsTable) {
      const recordColumns = await knex('records').columnInfo();
      console.log('records表列结构:', Object.keys(recordColumns));
    }
    
    // 检查服务记录表
    const hasServiceRecordsTable = await knex.schema.hasTable('service_records');
    console.log(`表service_records ${hasServiceRecordsTable ? '存在' : '不存在'}`);
    
    if (hasServiceRecordsTable) {
      const serviceRecordColumns = await knex('service_records').columnInfo();
      console.log('service_records表列结构:', Object.keys(serviceRecordColumns));
      
      // 获取记录数量
      const recordCount = await knex('service_records').count('* as count').first();
      console.log(`service_records表中有${recordCount.count}条记录`);
      
      // 检查第一条记录
      if (recordCount.count > 0) {
        const firstRecord = await knex('service_records').first();
        console.log('service_records表第一条记录:', firstRecord);
      }
    }
    
    // 检查附件表
    const hasAttachmentsTable = await knex.schema.hasTable('attachments');
    console.log(`表attachments ${hasAttachmentsTable ? '存在' : '不存在'}`);
    
    if (hasAttachmentsTable) {
      const attachmentColumns = await knex('attachments').columnInfo();
      console.log('attachments表列结构:', Object.keys(attachmentColumns));
    }
    
    console.log('数据库表结构检查完成');
  } catch (error) {
    console.error('检查数据库表结构时出错:', error);
  } finally {
    // 关闭数据库连接
    await knex.destroy();
  }
}

// 执行检查
checkDatabaseTables(); 