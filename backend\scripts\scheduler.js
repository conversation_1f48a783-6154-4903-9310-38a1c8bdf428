/**
 * 定时任务调度器
 * 
 * 用于执行系统维护和清理任务
 * 可以通过 node scripts/scheduler.js 来启动
 */

const path = require('path');
const fs = require('fs');
const { execSync } = require('child_process');

// 配置
const config = {
  // 任务运行间隔 (毫秒)
  runInterval: 15 * 60 * 1000, // 15分钟
  
  // 日志目录
  logDir: path.join(__dirname, '../../logs'),
  
  // 任务列表
  tasks: [
    {
      name: 'fix_processing_reports',
      script: path.join(__dirname, '../fix_processing_reports.js'),
      args: ['30'], // 30分钟超时
      enabled: true,
      lastRun: null,
      runEvery: 30 * 60 * 1000 // 每30分钟运行一次
    }
  ]
};

// 确保日志目录存在
if (!fs.existsSync(config.logDir)) {
  console.log(`创建日志目录: ${config.logDir}`);
  fs.mkdirSync(config.logDir, { recursive: true });
}

/**
 * 执行单个任务
 * @param {Object} task 任务配置
 */
function runTask(task) {
  if (!task.enabled) {
    console.log(`[${task.name}] 任务已禁用，跳过执行`);
    return;
  }
  
  // 检查是否应该运行
  const now = Date.now();
  if (task.lastRun && (now - task.lastRun < task.runEvery)) {
    console.log(`[${task.name}] 上次运行时间: ${new Date(task.lastRun).toISOString()}, 还不到执行时间，跳过`);
    return;
  }
  
  console.log(`[${task.name}] 开始执行...`);
  
  try {
    // 组合命令行参数
    const args = task.args ? task.args.join(' ') : '';
    const command = `node ${task.script} ${args}`;
    
    // 执行命令
    const output = execSync(command, { encoding: 'utf8' });
    
    // 记录任务执行结果到日志文件
    const logFile = path.join(config.logDir, `${task.name}_${new Date().toISOString().replace(/:/g, '-')}.log`);
    fs.writeFileSync(logFile, output);
    
    console.log(`[${task.name}] 执行成功，日志已保存到: ${logFile}`);
    
    // 更新上次运行时间
    task.lastRun = now;
  } catch (error) {
    console.error(`[${task.name}] 执行失败:`, error.message);
    
    // 记录错误到日志文件
    const errorLogFile = path.join(config.logDir, `${task.name}_error_${new Date().toISOString().replace(/:/g, '-')}.log`);
    fs.writeFileSync(errorLogFile, error.toString());
    
    console.error(`[${task.name}] 错误日志已保存到: ${errorLogFile}`);
  }
}

/**
 * 执行所有任务
 */
function runAllTasks() {
  console.log(`[调度器] ${new Date().toISOString()} - 开始执行计划任务...`);
  
  config.tasks.forEach(task => {
    runTask(task);
  });
  
  console.log(`[调度器] 任务执行完毕，等待下一次调度...`);
}

// 立即执行一次
runAllTasks();

// 设置定时执行
setInterval(runAllTasks, config.runInterval);

console.log(`调度器已启动，将每 ${config.runInterval / 60000} 分钟检查并执行任务`);
console.log('按 Ctrl+C 停止调度器...'); 