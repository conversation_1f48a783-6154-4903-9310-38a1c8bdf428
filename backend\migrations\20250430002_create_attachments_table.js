/**
 * 创建附件表
 */
exports.up = function(knex) {
  return knex.schema
    // 创建附件表
    .createTable('attachments', function(table) {
      table.uuid('id').primary(); // 附件ID
      table.string('name').notNullable(); // 文件名（带时间戳避免重名）
      table.string('type').notNullable(); // 文件类型
      table.integer('size').notNullable(); // 文件大小(KB)
      table.string('path', 512).notNullable(); // 文件存储路径
      table.uuid('record_id').notNullable(); // 关联的记录ID
      table.timestamps(true, true); // 创建和更新时间
      
      // 外键约束
      table.foreign('record_id').references('id').inTable('records').onDelete('CASCADE');
    })
    // 创建索引
    .then(function() {
      return knex.schema.table('attachments', function(table) {
        table.index('record_id'); // 记录ID索引
      });
    });
};

exports.down = function(knex) {
  return knex.schema.dropTableIfExists('attachments');
}; 