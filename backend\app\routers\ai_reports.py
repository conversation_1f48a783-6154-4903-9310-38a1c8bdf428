from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from typing import Optional
from pydantic import BaseModel
from datetime import datetime
from app.models.ai_report import AIReport
from app.models.user import User
from app.core.auth import get_current_user
from app.services.ai_report_service import AIReportService
from app.core.database import get_db
from sqlalchemy.orm import Session

router = APIRouter()
ai_report_service = AIReportService()

# ... existing code ...

@router.post("/regenerate")
async def regenerate_ai_report(
    request: CreateAIReportRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    重新生成AI报告
    """
    try:
        # 检查用户配额
        if not await ai_report_service.check_user_quota(current_user.id, db):
            raise HTTPException(status_code=403, detail="您的AI报告生成次数已用完，请联系客服")

        # 创建新的报告记录
        report = AIReport(
            user_id=current_user.id,
            disease_id=request.disease_id,
            patient_id=request.patient_id,
            target_region=request.target_region,
            status="PROCESSING",
            created_at=datetime.utcnow()
        )
        db.add(report)
        db.commit()
        db.refresh(report)

        # 在后台任务中生成报告
        background_tasks.add_task(
            ai_report_service.generate_report,
            report.id,
            request.disease_id,
            request.patient_id,
            request.target_region,
            db
        )

        return {
            "status": "PROCESSING",
            "message": "报告正在重新生成中",
            "aiReport": report
        }
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e)) 