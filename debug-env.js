#!/usr/bin/env node

// 调试环境变量脚本
// 用于检查构建时的环境变量配置

console.log('🔍 环境变量调试信息');
console.log('='.repeat(50));

// 基本环境信息
console.log('📋 基本环境信息:');
console.log(`NODE_ENV: ${process.env.NODE_ENV || '未设置'}`);
console.log(`PWD: ${process.env.PWD || '未设置'}`);
console.log(`当前工作目录: ${process.cwd()}`);

console.log('\n🔧 React 环境变量:');
// 检查所有 REACT_APP_ 开头的环境变量
const reactEnvVars = Object.keys(process.env)
  .filter(key => key.startsWith('REACT_APP_'))
  .sort();

if (reactEnvVars.length === 0) {
  console.log('❌ 未找到任何 REACT_APP_ 环境变量');
} else {
  reactEnvVars.forEach(key => {
    console.log(`${key}: ${process.env[key]}`);
  });
}

console.log('\n📁 环境文件检查:');
const fs = require('fs');
const path = require('path');

// 检查各种环境文件
const envFiles = [
  '.env',
  '.env.local',
  '.env.production',
  '.env.production.local'
];

envFiles.forEach(file => {
  const filePath = path.join(process.cwd(), file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file} 存在`);
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      console.log(`   内容预览: ${content.substring(0, 100)}${content.length > 100 ? '...' : ''}`);
    } catch (err) {
      console.log(`   ❌ 读取失败: ${err.message}`);
    }
  } else {
    console.log(`❌ ${file} 不存在`);
  }
});

console.log('\n🌐 API 配置检查:');
// 模拟前端配置逻辑
let baseURL = '';

if (process.env.NODE_ENV === 'development') {
  baseURL = 'http://localhost:3001';
} else if (process.env.NODE_ENV === 'production') {
  baseURL = 'https://hkb.life';
}

if (process.env.REACT_APP_API_URL) {
  baseURL = process.env.REACT_APP_API_URL.replace(/\/api\/?$/, '').replace(/\/$/, '');
}

console.log(`计算出的 baseURL: ${baseURL}`);

console.log('\n📦 Package.json 脚本:');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  if (packageJson.scripts) {
    Object.keys(packageJson.scripts).forEach(script => {
      if (script.includes('build') || script.includes('start')) {
        console.log(`${script}: ${packageJson.scripts[script]}`);
      }
    });
  }
} catch (err) {
  console.log(`❌ 无法读取 package.json: ${err.message}`);
}

console.log('\n' + '='.repeat(50));
console.log('🎯 建议检查项目:');
console.log('1. 确保 .env.production 文件存在且包含正确的 REACT_APP_API_URL');
console.log('2. 检查构建命令是否正确设置了 NODE_ENV=production');
console.log('3. 清除构建缓存后重新构建');
console.log('4. 检查是否有其他地方硬编码了 IP 地址');
