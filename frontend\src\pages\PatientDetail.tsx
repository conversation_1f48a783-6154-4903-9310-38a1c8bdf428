import React, { useState, useEffect, forwardRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
// import axios from 'axios'; // Removed unused import
import {
  Box,
  Typography,
  TextField,
  Button,
  Snackbar,
  <PERSON>ert as <PERSON><PERSON><PERSON>,
  AlertProps,
  // AlertColor, // Removed unused import
  CircularProgress,
  Paper,
  MenuItem,
  Divider,
  FormControl,
  InputLabel,
  Select
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import { useAuthStore } from '../store/authStore';
import apiClient from '../services/apiClient';

// Patient接口定义
interface Patient {
  id: string;
  name: string;
  gender: string;
  birthDate: string;
  phoneNumber: string;
  email: string;
  address: string;
  idCard: string;
  medicareCard: string;
  medicareLocation: string;
  emergencyContactName: string;
  emergencyContactPhone: string;
  emergencyContactRelationship: string;
  pastMedicalHistory: string;
  familyMedicalHistory: string;
  allergyHistory: string;
  bloodType: string;
  lastVisitDate: string;
  createdAt: string;
  updatedAt: string;
  isPrimary?: number;
  height?: number | string;
  weight?: number | string;
  bmi?: number;
}

// 通知类型定义
interface Notification {
  open: boolean;
  message: string;
  type: 'success' | 'error' | 'info' | 'warning';
}

// 格式化日期的辅助函数
const formatDate = (dateString: string | undefined): string => {
  if (!dateString) return '-';
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  } catch (e) {
    return '-';
  }
};

// 获取BMI状态描述
const getBMIStatus = (bmi: number): string => {
  if (bmi <= 0) return '';
  if (bmi < 18.5) return '偏瘦';
  if (bmi < 24) return '正常';
  if (bmi < 28) return '超重';
  if (bmi < 30) return '轻度肥胖';
  if (bmi < 40) return '中度肥胖';
  return '重度肥胖';
};

// 创建Alert组件的转发引用版本，解决类型问题
const Alert = forwardRef<HTMLDivElement, AlertProps>(
  function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
  }
);

// 患者详情组件
const PatientDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const token = useAuthStore((state) => state.token);
  const [patient, setPatient] = useState<Patient | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [editing, setEditing] = useState<boolean>(false);
  const [editedPatient, setEditedPatient] = useState<Partial<Patient>>({});
  const [notification, setNotification] = useState<Notification>({
    open: false,
    message: '',
    type: 'success'
  });

  // 获取患者详情
  useEffect(() => {
    const fetchPatient = async () => {
      try {
        setLoading(true);
        console.log('当前token:', token);

        if (!token) {
          console.log('未找到token，重定向到登录页');
          navigate('/login');
          return;
        }

        console.log(`准备发送请求到: /patients/${id}`);
        // 使用配置好的apiClient代替直接的axios
        const { data } = await apiClient.get(`/patients/${id}`);
        
        console.log('获取到患者数据:', data);
        setPatient(data);
        setEditedPatient(data);
        
        // 检查URL参数是否包含edit=true，如果是，则自动进入编辑模式
        const searchParams = new URLSearchParams(window.location.search);
        if (searchParams.get('edit') === 'true') {
          setEditing(true);
        }
        
        setLoading(false);
      } catch (err: any) {
        console.error('获取患者详情失败', err);
        console.error('详细错误信息:', err.response?.data, '状态码:', err.response?.status);
        setError('获取患者详情失败');
        setLoading(false);
      }
    };

    if (id) {
      fetchPatient();
    }
  }, [id, navigate, token]);

  // 处理输入变化
  const handleInputChange = (field: keyof Patient, value: string) => {
    setEditedPatient(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 开始编辑
  const handleEdit = () => {
    if (patient) {
      setEditedPatient(patient);
      setEditing(true);
    }
  };

  // 取消编辑
  const handleCancel = () => {
    setEditing(false);
    if (patient) {
      setEditedPatient(patient);
    }
  };

  // 保存编辑内容
  const handleSave = async () => {
    try {
      if (!token) {
        navigate('/login');
        return;
      }

      // 处理数据类型转换，确保身高、体重和BMI为数字类型
      const patientData = { ...editedPatient };
      if (patientData.height) {
        patientData.height = Number(patientData.height);
      }
      if (patientData.weight) {
        patientData.weight = Number(patientData.weight);
      }
      if (patientData.bmi) {
        patientData.bmi = Number(patientData.bmi);
      }

      console.log('发送更新数据:', patientData);
      
      // 使用apiClient代替直接的axios
      await apiClient.put(`/patients/${id}`, patientData);

      setPatient(prev => prev ? { ...prev, ...patientData } as Patient : null);
      setEditing(false);
      setNotification({
        open: true,
        message: '患者信息更新成功',
        type: 'success'
      });
    } catch (err: any) {
      console.error('更新患者信息失败', err);
      setNotification({
        open: true,
        message: '更新患者信息失败',
        type: 'error'
      });
    }
  };

  // 返回患者列表
  const handleBack = () => {
    navigate('/patients');
  };

  // 关闭通知
  const handleCloseNotification = () => {
    setNotification({
      ...notification,
      open: false
    });
  };

  return (
    <Box sx={{ 
      m: 0, // 移除外边距，使用Layout提供的统一10px间距
      width: '100%' 
    }}>
      <Box sx={{ 
        display: 'flex', 
        flexDirection: { xs: 'column', sm: 'row' }, 
        justifyContent: 'space-between', 
        alignItems: { xs: 'flex-start', sm: 'center' }, 
        mb: 3,
        gap: { xs: 2, sm: 0 } // 在移动端添加间距
      }}>
        <Typography 
          variant="h5" 
          component="h1"
          sx={{ fontWeight: 500, fontSize: { xs: '1.3rem', sm: '1.5rem' } }}
        >
          {patient ? `${patient.name} 的档案` : '患者详情'}
        </Typography>
        <Box sx={{ display: 'flex', width: { xs: '100%', sm: 'auto' } }}>
          <Button 
            variant="outlined"
            sx={{ mr: 1, flex: { xs: 1, sm: 'none' } }}
            onClick={handleBack}
          >
            返回
          </Button>
          {!editing ? (
            <Button 
              variant="contained"
              startIcon={<EditIcon />}
              onClick={handleEdit}
              sx={{ flex: { xs: 1, sm: 'none' } }}
            >
              编辑
            </Button>
          ) : (
            <>
              <Button
                variant="outlined"
                sx={{ mr: 1, flex: { xs: 1, sm: 'none' } }}
                onClick={handleCancel}
              >
                取消
              </Button>
              <Button
                variant="contained"
                color="primary"
                onClick={handleSave}
                sx={{ flex: { xs: 1, sm: 'none' } }}
              >
                保存
              </Button>
            </>
          )}
        </Box>
      </Box>

      <Divider sx={{ mb: { xs: 2, md: 3 } }} />

      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {!loading && !error && patient && (
        <Paper 
          elevation={0}
          sx={{ 
            p: { xs: 2, md: 3 }, 
            border: '1px solid #e0e0e0', 
            borderRadius: 2,
            width: '100%'
          }}
        >
          <Box>
            {/* 基本信息 */}
            <Typography variant="h6" sx={{ mb: 2, borderBottom: '1px solid #eee', pb: 1 }}>
              基本信息
            </Typography>
            <Box sx={{ mb: 4 }}>
              <Box sx={{ 
                display: 'grid', 
                gridTemplateColumns: { xs: '1fr', sm: 'repeat(3, 1fr)' },
                gap: 2
              }}>
                {editing ? (
                  <TextField
                    fullWidth
                    label="姓名"
                    value={editedPatient.name || ''}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    variant="outlined"
                  />
                ) : (
                  <Box>
                    <Typography variant="body2" color="text.secondary">姓名</Typography>
                    <Typography variant="body1">{patient.name}</Typography>
                  </Box>
                )}
                
                {editing ? (
                  <TextField
                    fullWidth
                    label="性别"
                    value={editedPatient.gender || ''}
                    onChange={(e) => handleInputChange('gender', e.target.value)}
                    variant="outlined"
                    select
                  >
                    <MenuItem value="MALE">男</MenuItem>
                    <MenuItem value="FEMALE">女</MenuItem>
                  </TextField>
                ) : (
                  <Box>
                    <Typography variant="body2" color="text.secondary">性别</Typography>
                    <Typography variant="body1">
                      {patient.gender === 'MALE' ? '男' : 
                       patient.gender === 'FEMALE' ? '女' : 
                       patient.gender || '-'}
                    </Typography>
                  </Box>
                )}
                
                {editing ? (
                  <TextField
                    fullWidth
                    label="出生日期"
                    type="date"
                    value={editedPatient.birthDate || ''}
                    onChange={(e) => handleInputChange('birthDate', e.target.value)}
                    variant="outlined"
                    InputLabelProps={{ shrink: true }}
                  />
                ) : (
                  <Box>
                    <Typography variant="body2" color="text.secondary">出生日期</Typography>
                    <Typography variant="body1">{formatDate(patient.birthDate)}</Typography>
                  </Box>
                )}
              </Box>
              
              {/* 身高体重BMI部分 */}
              <Box sx={{ 
                display: 'grid', 
                gridTemplateColumns: { xs: '1fr', sm: 'repeat(3, 1fr)' },
                gap: 2,
                mt: 2
              }}>
                {editing ? (
                  <TextField
                    fullWidth
                    label="身高(厘米)"
                    type="number"
                    value={editedPatient.height || ''}
                    onChange={(e) => {
                      const height = e.target.value;
                      handleInputChange('height', height);
                      // 计算BMI
                      const heightValue = parseFloat(height);
                      const weightValue = parseFloat(editedPatient.weight as string || '0');
                      if (heightValue > 0 && weightValue > 0) {
                        const heightM = heightValue / 100;
                        const bmiValue = Math.round((weightValue / (heightM * heightM)) * 10) / 10;
                        handleInputChange('bmi', bmiValue.toString());
                      }
                    }}
                    variant="outlined"
                    InputProps={{
                      inputProps: { min: 0, max: 300 }
                    }}
                  />
                ) : (
                  <Box>
                    <Typography variant="body2" color="text.secondary">身高</Typography>
                    <Typography variant="body1">{patient.height ? `${patient.height} 厘米` : '-'}</Typography>
                  </Box>
                )}
                
                {editing ? (
                  <TextField
                    fullWidth
                    label="体重(千克)"
                    type="number"
                    value={editedPatient.weight || ''}
                    onChange={(e) => {
                      const weight = e.target.value;
                      handleInputChange('weight', weight);
                      // 计算BMI
                      const weightValue = parseFloat(weight);
                      const heightValue = parseFloat(editedPatient.height as string || '0');
                      if (heightValue > 0 && weightValue > 0) {
                        const heightM = heightValue / 100;
                        const bmiValue = Math.round((weightValue / (heightM * heightM)) * 10) / 10;
                        handleInputChange('bmi', bmiValue.toString());
                      }
                    }}
                    variant="outlined"
                    InputProps={{
                      inputProps: { min: 0, max: 500 }
                    }}
                  />
                ) : (
                  <Box>
                    <Typography variant="body2" color="text.secondary">体重</Typography>
                    <Typography variant="body1">{patient.weight ? `${patient.weight} 千克` : '-'}</Typography>
                  </Box>
                )}
                
                {editing ? (
                  <TextField
                    fullWidth
                    label="BMI"
                    value={editedPatient.bmi || ''}
                    variant="outlined"
                    InputProps={{
                      readOnly: true,
                      endAdornment: editedPatient.bmi ? (
                        <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                          {getBMIStatus(Number(editedPatient.bmi))}
                        </Typography>
                      ) : null,
                    }}
                  />
                ) : (
                  <Box>
                    <Typography variant="body2" color="text.secondary">BMI</Typography>
                    <Typography variant="body1">
                      {patient.bmi ? 
                        `${patient.bmi} (${getBMIStatus(Number(patient.bmi))})` : 
                        '-'}
                    </Typography>
                  </Box>
                )}
              </Box>
              
              <Box sx={{ 
                display: 'grid', 
                gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)' },
                gap: 2,
                mt: 2
              }}>
                {editing ? (
                  <TextField
                    fullWidth
                    label="电话号码"
                    value={editedPatient.phoneNumber || ''}
                    onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
                    variant="outlined"
                  />
                ) : (
                  <Box>
                    <Typography variant="body2" color="text.secondary">电话号码</Typography>
                    <Typography variant="body1">{patient.phoneNumber || '-'}</Typography>
                  </Box>
                )}
                
                {editing ? (
                  <TextField
                    fullWidth
                    label="电子邮箱"
                    value={editedPatient.email || ''}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    variant="outlined"
                  />
                ) : (
                  <Box>
                    <Typography variant="body2" color="text.secondary">电子邮箱</Typography>
                    <Typography variant="body1">{patient.email || '-'}</Typography>
                  </Box>
                )}
              </Box>
              
              <Box sx={{ mt: 2 }}>
                {editing ? (
                  <TextField
                    fullWidth
                    label="地址"
                    value={editedPatient.address || ''}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                    variant="outlined"
                  />
                ) : (
                  <Box>
                    <Typography variant="body2" color="text.secondary">地址</Typography>
                    <Typography variant="body1">{patient.address || '-'}</Typography>
                  </Box>
                )}
              </Box>
            </Box>

            {/* 证件信息 */}
            <Typography variant="h6" sx={{ mb: 2, borderBottom: '1px solid #eee', pb: 1 }}>
              证件信息
            </Typography>
            <Box sx={{ mb: 4 }}>
              <Box sx={{ 
                display: 'grid', 
                gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)' },
                gap: 2
              }}>
                {editing ? (
                  <TextField
                    fullWidth
                    label="身份证号"
                    value={editedPatient.idCard || ''}
                    onChange={(e) => handleInputChange('idCard', e.target.value)}
                    variant="outlined"
                  />
                ) : (
                  <Box>
                    <Typography variant="body2" color="text.secondary">身份证号</Typography>
                    <Typography variant="body1">{patient.idCard || '-'}</Typography>
                  </Box>
                )}
                
                {editing ? (
                  <TextField
                    fullWidth
                    label="医保卡号"
                    value={editedPatient.medicareCard || ''}
                    onChange={(e) => handleInputChange('medicareCard', e.target.value)}
                    variant="outlined"
                  />
                ) : (
                  <Box>
                    <Typography variant="body2" color="text.secondary">医保卡号</Typography>
                    <Typography variant="body1">{patient.medicareCard || '-'}</Typography>
                  </Box>
                )}
              </Box>
              
              <Box sx={{ mt: 2 }}>
                {editing ? (
                  <TextField
                    fullWidth
                    label="医保所在地"
                    value={editedPatient.medicareLocation || ''}
                    onChange={(e) => handleInputChange('medicareLocation', e.target.value)}
                    variant="outlined"
                  />
                ) : (
                  <Box>
                    <Typography variant="body2" color="text.secondary">医保所在地</Typography>
                    <Typography variant="body1">{patient.medicareLocation || '-'}</Typography>
                  </Box>
                )}
              </Box>
            </Box>

            {/* 紧急联系人 */}
            <Typography variant="h6" sx={{ mb: 2, borderBottom: '1px solid #eee', pb: 1 }}>
              紧急联系人
            </Typography>
            <Box sx={{ mb: 4 }}>
              <Box sx={{ 
                display: 'grid', 
                gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)' },
                gap: 2
              }}>
                {editing ? (
                  <TextField
                    fullWidth
                    label="姓名"
                    value={editedPatient.emergencyContactName || ''}
                    onChange={(e) => handleInputChange('emergencyContactName', e.target.value)}
                    variant="outlined"
                  />
                ) : (
                  <Box>
                    <Typography variant="body2" color="text.secondary">姓名</Typography>
                    <Typography variant="body1">{patient.emergencyContactName || '-'}</Typography>
                  </Box>
                )}
                
                {editing ? (
                  <TextField
                    fullWidth
                    label="电话"
                    value={editedPatient.emergencyContactPhone || ''}
                    onChange={(e) => handleInputChange('emergencyContactPhone', e.target.value)}
                    variant="outlined"
                  />
                ) : (
                  <Box>
                    <Typography variant="body2" color="text.secondary">电话</Typography>
                    <Typography variant="body1">{patient.emergencyContactPhone || '-'}</Typography>
                  </Box>
                )}
              </Box>
            </Box>

            {/* 病史 */}
            <Typography variant="h6" sx={{ mb: 2, borderBottom: '1px solid #eee', pb: 1 }}>
              病史
            </Typography>
            <Box sx={{ mb: 4 }}>
              <Box sx={{ 
                display: 'grid', 
                gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)' },
                gap: 2
              }}>
                {editing ? (
                  <TextField
                    fullWidth
                    label="既往病史"
                    value={editedPatient.pastMedicalHistory || ''}
                    onChange={(e) => handleInputChange('pastMedicalHistory', e.target.value)}
                    variant="outlined"
                    multiline
                    rows={3}
                  />
                ) : (
                  <Box>
                    <Typography variant="body2" color="text.secondary">既往病史</Typography>
                    <Typography variant="body1">{patient.pastMedicalHistory || '无'}</Typography>
                  </Box>
                )}
                
                {editing ? (
                  <TextField
                    fullWidth
                    label="家族病史"
                    value={editedPatient.familyMedicalHistory || ''}
                    onChange={(e) => handleInputChange('familyMedicalHistory', e.target.value)}
                    variant="outlined"
                    multiline
                    rows={3}
                  />
                ) : (
                  <Box>
                    <Typography variant="body2" color="text.secondary">家族病史</Typography>
                    <Typography variant="body1">{patient.familyMedicalHistory || '无'}</Typography>
                  </Box>
                )}
              </Box>
              
              <Box sx={{ 
                display: 'grid', 
                gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)' },
                gap: 2,
                mt: 2
              }}>
                {editing ? (
                  <TextField
                    fullWidth
                    label="过敏史"
                    value={editedPatient.allergyHistory || ''}
                    onChange={(e) => handleInputChange('allergyHistory', e.target.value)}
                    variant="outlined"
                    multiline
                    rows={3}
                  />
                ) : (
                  <Box>
                    <Typography variant="body2" color="text.secondary">过敏史</Typography>
                    <Typography variant="body1">{patient.allergyHistory || '无'}</Typography>
                  </Box>
                )}
                
                {editing ? (
                  <TextField
                    fullWidth
                    label="血型"
                    value={editedPatient.bloodType || ''}
                    onChange={(e) => handleInputChange('bloodType', e.target.value)}
                    variant="outlined"
                    select
                  >
                    <MenuItem value="A">A型</MenuItem>
                    <MenuItem value="B">B型</MenuItem>
                    <MenuItem value="AB">AB型</MenuItem>
                    <MenuItem value="O">O型</MenuItem>
                    <MenuItem value="其他">其他</MenuItem>
                  </TextField>
                ) : (
                  <Box>
                    <Typography variant="body2" color="text.secondary">血型</Typography>
                    <Typography variant="body1">{patient.bloodType || '-'}</Typography>
                  </Box>
                )}
              </Box>
            </Box>

            {/* 就诊信息 */}
            <Typography variant="h6" sx={{ mb: 2, borderBottom: '1px solid #eee', pb: 1 }}>
              就诊信息
            </Typography>
            <Box>
              <Box sx={{ 
                display: 'grid', 
                gridTemplateColumns: { xs: '1fr', sm: 'repeat(3, 1fr)' },
                gap: 2
              }}>
                {editing ? (
                  <TextField
                    fullWidth
                    label="上次就诊日期"
                    type="date"
                    value={editedPatient.lastVisitDate || ''}
                    onChange={(e) => handleInputChange('lastVisitDate', e.target.value)}
                    variant="outlined"
                    InputLabelProps={{ shrink: true }}
                  />
                ) : (
                  <Box>
                    <Typography variant="body2" color="text.secondary">上次就诊日期</Typography>
                    <Typography variant="body1">{formatDate(patient.lastVisitDate) || '-'}</Typography>
                  </Box>
                )}
                
                {editing ? (
                  <FormControl fullWidth variant="outlined">
                    <InputLabel>是否本人档案</InputLabel>
                    <Select
                      value={(editedPatient.isPrimary !== undefined ? editedPatient.isPrimary : (patient.isPrimary || 0))}
                      onChange={(e) => handleInputChange('isPrimary', e.target.value as string)}
                      label="是否本人档案"
                    >
                      <MenuItem value={1}>是</MenuItem>
                      <MenuItem value={0}>否</MenuItem>
                    </Select>
                  </FormControl>
                ) : (
                  <Box>
                    <Typography variant="body2" color="text.secondary">是否本人档案</Typography>
                    <Typography variant="body1">{patient.isPrimary ? '是' : '否'}</Typography>
                  </Box>
                )}

                {editing ? (
                  <TextField
                    fullWidth
                    label="与用户关系"
                    value={editedPatient.emergencyContactRelationship || ''}
                    onChange={(e) => handleInputChange('emergencyContactRelationship', e.target.value)}
                    variant="outlined"
                    select
                  >
                    <MenuItem value="SELF">本人</MenuItem>
                    <MenuItem value="FAMILY">家属</MenuItem>
                    <MenuItem value="RELATIVE">亲戚</MenuItem>
                    <MenuItem value="FRIEND">朋友</MenuItem>
                  </TextField>
                ) : (
                  <Box>
                    <Typography variant="body2" color="text.secondary">与用户关系</Typography>
                    <Typography variant="body1">
                      {patient.emergencyContactRelationship === 'SELF' ? '本人' :
                       patient.emergencyContactRelationship === 'FAMILY' ? '家属' :
                       patient.emergencyContactRelationship === 'RELATIVE' ? '亲戚' :
                       patient.emergencyContactRelationship === 'FRIEND' ? '朋友' :
                       patient.emergencyContactRelationship || '-'}
                    </Typography>
                  </Box>
                )}
              </Box>
            </Box>
          </Box>
          
          {/* 底部操作按钮 - 仅在编辑模式下显示 */}
          {editing && (
            <Box sx={{ 
              display: 'flex', 
              justifyContent: 'flex-end', 
              mt: 4,
              pt: 2,
              borderTop: '1px solid #eee'
            }}>
              <Button
                variant="outlined"
                sx={{ mr: 1 }}
                onClick={handleCancel}
              >
                取消
              </Button>
              <Button
                variant="contained"
                color="primary"
                onClick={handleSave}
              >
                保存
              </Button>
            </Box>
          )}
        </Paper>
      )}
      
      {/* 操作结果通知 */}
      <Snackbar 
        open={notification.open} 
        autoHideDuration={6000} 
        onClose={handleCloseNotification}
      >
        <MuiAlert 
          onClose={handleCloseNotification} 
          severity={notification.type} 
          sx={{ width: '100%' }}
        >
          {notification.message}
        </MuiAlert>
      </Snackbar>
    </Box>
  );
};

export default PatientDetail; 