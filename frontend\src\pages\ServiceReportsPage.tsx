import React, { useState, useEffect, useMemo } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  Snackbar,
  Card,
  CardContent,
  useTheme,
  useMediaQuery,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Breadcrumbs,
  Link as MuiLink
} from '@mui/material';

// 图标
import AddIcon from '@mui/icons-material/Add';
import SearchOutlined from '@mui/icons-material/SearchOutlined';
import VisibilityOutlined from '@mui/icons-material/VisibilityOutlined';
import EditOutlined from '@mui/icons-material/EditOutlined';
import DeleteOutlined from '@mui/icons-material/DeleteOutlined';
import HistoryEduIcon from '@mui/icons-material/HistoryEdu';
import AssessmentIcon from '@mui/icons-material/Assessment';
import LockIcon from '@mui/icons-material/Lock';
import SettingsIcon from '@mui/icons-material/Settings';
import RefreshIcon from '@mui/icons-material/Refresh';

// 服务与上下文
import { useServiceUserContext } from '../context/ServiceUserContext';
import ServiceContextBar from '../components/service/ServiceContextBar';
import ServiceContextSelector from '../components/service/ServiceContextSelector';

// 数据服务
import * as serviceReportService from '../services/serviceReportService';
import { useAuthStore } from '../store/authStore';

// 服务报告管理页面组件
const ServiceReportsPage: React.FC = () => {
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const user = useAuthStore((state) => state.user);
  const serviceContext = useServiceUserContext();
  
  // 基础状态
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [reports, setReports] = useState<any[]>([]);
  const [showContextSelector, setShowContextSelector] = useState(false);
  const [notification, setNotification] = useState({
    open: false,
    message: '',
    type: 'success' as 'success' | 'error' | 'info' | 'warning'
  });
  
  // 搜索和筛选状态
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    reportType: '',
    startDate: '',
    endDate: ''
  });
  
  // 分页状态
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  
  // 删除确认状态
  const [reportToDeleteId, setReportToDeleteId] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  
  // 判断是否有编辑权限
  const hasEditPermission = useMemo(() => {
    // 获取当前权限级别
    const privacyLevel = serviceContext.privacyLevel;
    
    if (user?.role === 'ADMIN') return true; // 管理员始终有权限
    
    // 根据授权级别判断权限
    switch(privacyLevel) {
      case 'FULL':
        return true; // 完全授权可以编辑
      case 'STANDARD':
        return true; // 标准授权可以编辑自己创建的报告
      case 'BASIC':
      default:
        return false; // 基础授权只读
    }
  }, [user?.role, serviceContext.privacyLevel]);
  
  // 获取报告数据
  const fetchReports = async () => {
    if (!serviceContext.patientId || !serviceContext.diseaseId || !serviceContext.authorizationId) {
      console.log("[服务报告管理] 上下文不完整，不加载报告:", {
        authorizationId: serviceContext.authorizationId,
        patientId: serviceContext.patientId,
        diseaseId: serviceContext.diseaseId
      });
      
      if (!serviceContext.authorizationId) {
        setShowContextSelector(true);
        setError('请先选择授权关系');
      } else if (!serviceContext.patientId) {
        setShowContextSelector(true);
        setError('请先选择患者');
      } else if (!serviceContext.diseaseId) {
        setShowContextSelector(true);
        setError('请先选择病理');
      }
      
      setReports([]);
      setLoading(false);
      return;
    }
    
    // 断言上下文信息已存在（非空）
    const authorizationId = serviceContext.authorizationId as string;
    const patientId = serviceContext.patientId as string;
    const diseaseId = serviceContext.diseaseId as string;
    
    setLoading(true);
    setError('');
    try {
      // 在服务上下文中获取报告
      console.log(`[服务报告管理] 获取患者 ${patientId} 的疾病 ${diseaseId} 的报告`);
      
      const data = await serviceReportService.getServiceReportsByContext({
        authorizationId,
        patientId,
        diseaseId
      });
      
      if (Array.isArray(data)) {
        console.log(`[服务报告管理] 获取到 ${data.length} 条报告`);
        setReports(data);
      } else {
        console.error('[服务报告管理] 获取报告返回非数组数据:', data);
        setReports([]);
        setError('获取报告数据格式错误');
      }
    } catch (err: any) {
      console.error('[服务报告管理] 获取报告错误:', err);
      setError(err.message || '获取报告失败');
      setReports([]);
    } finally {
      setLoading(false);
    }
  };
  
  // 初次加载和上下文变化时获取数据
  useEffect(() => {
    if (!serviceContext.patientId || !serviceContext.diseaseId) {
      setShowContextSelector(true);
    } else {
      fetchReports();
    }
  }, [serviceContext.authorizationId, serviceContext.patientId, serviceContext.diseaseId]);
  
  // 处理删除确认
  const confirmDelete = async () => {
    if (!reportToDeleteId) return;
    
    setLoading(true);
    try {
      // 调用服务删除报告
      await serviceReportService.deleteServiceReport(reportToDeleteId);
      
      setNotification({
        open: true,
        message: '报告删除成功',
        type: 'success'
      });
      
      // 重新加载报告
      fetchReports();
    } catch (err: any) {
      console.error('[服务报告管理] 删除报告失败:', err);
      setNotification({
        open: true,
        message: err.message || '删除报告失败',
        type: 'error'
      });
    } finally {
      setLoading(false);
      setDeleteDialogOpen(false);
      setReportToDeleteId(null);
    }
  };
  
  // 工具函数 - 文本截断
  const truncateText = (text: string, maxLength: number): string => {
    if (!text) return '无内容';
    return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
  };
  
  // 工具函数 - 格式化日期时间
  const formatDateTime = (dateTime: string): string => {
    if (!dateTime) return '-';
    try {
      return new Date(dateTime).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (e) {
      return dateTime;
    }
  };
  
  // 工具函数 - 获取报告类型名称
  const getReportTypeName = (type: string): string => {
    const reportTypeNames: Record<string, string> = {
      'DIAGNOSTIC': '诊断报告',
      'PROGRESS': '进展报告',
      'SUMMARY': '总结报告',
      'TREATMENT': '治疗报告',
      'CONSULTATION': '咨询报告',
      'PATHOLOGY': '病理报告',
      'LAB': '检验报告',
      'IMAGING': '影像报告',
      'REFERRAL': '转诊报告',
      'DISCHARGE': '出院报告',
      'FOLLOW_UP': '随访报告',
      'OTHER': '其他报告'
    };
    
    return reportTypeNames[type] || type;
  };
  
  // 工具函数 - 获取报告类型颜色
  const getReportTypeColor = (type: string): string => {
    const reportTypeColors: Record<string, string> = {
      'DIAGNOSTIC': '#1976d2', // 蓝色
      'PROGRESS': '#43a047', // 绿色
      'SUMMARY': '#f57c00', // 橙色
      'TREATMENT': '#0097a7', // 青色
      'CONSULTATION': '#7b1fa2', // 紫色
      'PATHOLOGY': '#c2185b', // 粉色
      'LAB': '#00796b', // 青绿色
      'IMAGING': '#3f51b5', // 靛蓝色
      'REFERRAL': '#d32f2f', // 红色
      'DISCHARGE': '#fbc02d', // 黄色
      'FOLLOW_UP': '#5d4037', // 棕色
      'OTHER': '#757575' // 灰色
    };
    
    return reportTypeColors[type] || '#757575';
  };
  
  // 过滤报告
  const filteredReports = useMemo(() => {
    if (!reports || reports.length === 0) return [];
    
    return reports.filter(report => {
      // 搜索词过滤（标题、内容）
      const searchLower = searchTerm.toLowerCase();
      const matchesSearch = !searchTerm || 
        (report.title && report.title.toLowerCase().includes(searchLower)) ||
        (report.content && report.content.toLowerCase().includes(searchLower));
      
      // 类型过滤
      const matchesType = !filters.reportType || report.reportType === filters.reportType;
      
      // 日期过滤
      const reportDate = new Date(report.createdAt);
      const matchesStartDate = !filters.startDate || 
        reportDate >= new Date(filters.startDate);
      const matchesEndDate = !filters.endDate || 
        reportDate <= new Date(filters.endDate);
      
      return matchesSearch && matchesType && matchesStartDate && matchesEndDate;
    });
  }, [reports, searchTerm, filters]);
  
  // 分页处理
  const paginatedReports = useMemo(() => {
    return filteredReports.slice(
      page * rowsPerPage,
      page * rowsPerPage + rowsPerPage
    );
  }, [filteredReports, page, rowsPerPage]);
  
  // 处理页码变化
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };
  
  // 处理每页行数变化
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };
  
  // 查看报告详情
  const handleViewReport = (id: string) => {
    navigate(`/service-reports/${id}`);
  };
  
  // 编辑报告
  const handleEditReport = (id: string) => {
    navigate(`/service-reports/${id}/edit`);
  };
  
  // 删除报告
  const handleDeleteReport = (id: string) => {
    if (!hasEditPermission) {
      setNotification({
        open: true,
        message: '您没有删除报告的权限',
        type: 'error'
      });
      return;
    }
    
    setReportToDeleteId(id);
    setDeleteDialogOpen(true);
  };
  
  // 取消删除
  const cancelDelete = () => {
    setDeleteDialogOpen(false);
    setReportToDeleteId(null);
  };
  
  // 关闭通知
  const handleCloseNotification = () => {
    setNotification({
      ...notification,
      open: false
    });
  };
  
  // 显示服务上下文权限提示
  const renderPrivacyLevelAlert = () => {
    const privacyLevel = serviceContext.privacyLevel;
    
    if (!privacyLevel) return null;
    
    let alertProps: {severity: 'info' | 'warning' | 'error', text: string} = {
      severity: 'info',
      text: ''
    };
    
    switch(privacyLevel) {
      case 'BASIC':
        alertProps = {
          severity: 'info',
          text: '基础授权级别：您只能查看报告信息，不能进行编辑或删除操作。'
        };
        break;
      case 'STANDARD':
        alertProps = {
          severity: 'info',
          text: '标准授权级别：您可以查看报告信息，只能编辑自己创建的报告。'
        };
        break;
      case 'FULL':
        alertProps = {
          severity: 'info',
          text: '完全授权级别：您可以查看、编辑和删除所有报告信息。'
        };
        break;
      default:
        return null;
    }
    
    return (
      <Alert 
        severity={alertProps.severity} 
        sx={{ mb: 2 }}
        icon={<LockIcon />}
      >
        {alertProps.text}
      </Alert>
    );
  };
  
  return (
    <Box>
      {/* 面包屑导航 */}
      <Breadcrumbs sx={{ mb: 2 }}>
        <MuiLink 
          component={Link} 
          to="/dashboard" 
          color="inherit" 
          underline="hover"
        >
          看板
        </MuiLink>
        <Typography color="text.primary">服务报告管理</Typography>
      </Breadcrumbs>
      
      {/* 服务上下文信息栏 */}
      {serviceContext.patientId && (
        <ServiceContextBar 
          onSelectContext={() => setShowContextSelector(true)}
        />
      )}
      
      {/* 服务上下文选择器 */}
      {showContextSelector && (
        <ServiceContextSelector
          onComplete={() => {
            setShowContextSelector(false);
            fetchReports();
          }}
        />
      )}
      
      {/* 页面标题和操作区 */}
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        mb: 3,
        flexWrap: 'wrap',
        gap: 1
      }}>
        <Typography 
          variant="h5" 
          component="h1" 
          gutterBottom
          sx={{ fontWeight: 500 }}
        >
          报告管理
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<SettingsIcon />}
            onClick={() => setShowContextSelector(true)}
          >
            选择服务上下文
          </Button>
          
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={() => fetchReports()}
          >
            刷新
          </Button>
          
          {hasEditPermission && (
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => navigate('/service-reports/create')}
              disabled={!serviceContext.diseaseId}
            >
              创建报告
            </Button>
          )}
        </Box>
      </Box>
      
      {/* 权限提示 */}
      {renderPrivacyLevelAlert()}
      
      {/* 错误提示 */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}
      
      {/* 搜索框 */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <TextField
          fullWidth
          placeholder="搜索报告标题或内容..."
          variant="outlined"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <SearchOutlined sx={{ color: 'action.active', mr: 1 }} />
            ),
          }}
        />
      </Paper>
      
      {/* 加载指示器 */}
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      )}
      
      {/* 无数据提示 */}
      {!loading && filteredReports.length === 0 && (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography color="text.secondary" sx={{ mb: 2 }}>
            {searchTerm || filters.reportType || filters.startDate || filters.endDate
              ? '没有找到匹配的报告'
              : '该病理暂无报告'}
          </Typography>
          
          {hasEditPermission && serviceContext.diseaseId && (
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => navigate('/service-reports/create')}
            >
              创建第一份报告
            </Button>
          )}
        </Paper>
      )}
      
      {/* 报告列表 - 桌面端表格视图 */}
      {!loading && filteredReports.length > 0 && !isMobile && (
        <Paper sx={{ width: '100%', overflow: 'hidden' }}>
          <TableContainer>
            <Table size="medium">
              <TableHead>
                <TableRow>
                  <TableCell>报告标题</TableCell>
                  <TableCell>类型</TableCell>
                  <TableCell>创建时间</TableCell>
                  <TableCell>状态</TableCell>
                  <TableCell align="right">操作</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedReports.map((report) => (
                  <TableRow
                    key={report.id}
                    hover
                    sx={{ 
                      cursor: 'pointer',
                    }}
                    onClick={() => handleViewReport(report.id)}
                  >
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box
                          sx={{
                            width: 12,
                            height: 12,
                            borderRadius: '50%',
                            backgroundColor: getReportTypeColor(report.reportType),
                            mr: 1.5
                          }}
                        />
                        <Typography variant="body2">
                          {truncateText(report.title, 50)}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      {getReportTypeName(report.reportType)}
                    </TableCell>
                    <TableCell>
                      {formatDateTime(report.createdAt)}
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={report.status === 'PUBLISHED' ? '已发布' : '草稿'}
                        size="small"
                        color={report.status === 'PUBLISHED' ? 'success' : 'default'}
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell align="right">
                      <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                        <Tooltip title="查看">
                          <IconButton
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleViewReport(report.id);
                            }}
                          >
                            <VisibilityOutlined fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        
                        {hasEditPermission && (
                          <>
                            <Tooltip title="编辑">
                              <IconButton
                                size="small"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleEditReport(report.id);
                                }}
                              >
                                <EditOutlined fontSize="small" />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="删除">
                              <IconButton
                                size="small"
                                color="error"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDeleteReport(report.id);
                                }}
                              >
                                <DeleteOutlined fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </>
                        )}
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={filteredReports.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            labelRowsPerPage="每页行数"
            labelDisplayedRows={({ from, to, count }) => `${from}-${to} / ${count}`}
          />
        </Paper>
      )}
      
      {/* 报告列表 - 移动端卡片视图 */}
      {!loading && filteredReports.length > 0 && isMobile && (
        <Box sx={{ mb: 2 }}>
          {paginatedReports.map((report) => (
            <Card 
              key={report.id} 
              sx={{ 
                mb: 2,
                borderLeft: `4px solid ${getReportTypeColor(report.reportType)}`,
                cursor: 'pointer'
              }}
              onClick={() => handleViewReport(report.id)}
            >
              <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                <Box sx={{ 
                  display: 'flex', 
                  justifyContent: 'space-between',
                  mb: 1
                }}>
                  <Typography variant="subtitle1" component="div">
                    {truncateText(report.title, 40)}
                  </Typography>
                  <Chip
                    label={report.status === 'PUBLISHED' ? '已发布' : '草稿'}
                    size="small"
                    color={report.status === 'PUBLISHED' ? 'success' : 'default'}
                    variant="outlined"
                  />
                </Box>
                
                <Box sx={{ mb: 1 }}>
                  <Chip
                    label={getReportTypeName(report.reportType)}
                    size="small"
                    sx={{
                      backgroundColor: `${getReportTypeColor(report.reportType)}20`,
                      color: getReportTypeColor(report.reportType),
                      mr: 1,
                      mb: 1
                    }}
                    icon={<AssessmentIcon style={{ color: getReportTypeColor(report.reportType) }} />}
                  />
                </Box>
                
                <Box sx={{ 
                  display: 'flex', 
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}>
                  <Typography variant="body2" color="text.secondary">
                    {formatDateTime(report.createdAt)}
                  </Typography>
                  
                  <Box>
                    {hasEditPermission && (
                      <>
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditReport(report.id);
                          }}
                        >
                          <EditOutlined fontSize="small" />
                        </IconButton>
                        <IconButton
                          size="small"
                          color="error"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteReport(report.id);
                          }}
                        >
                          <DeleteOutlined fontSize="small" />
                        </IconButton>
                      </>
                    )}
                  </Box>
                </Box>
              </CardContent>
            </Card>
          ))}
          
          {/* 移动端分页控件 */}
          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={filteredReports.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            labelRowsPerPage="每页行数"
            labelDisplayedRows={({ from, to, count }) => `${from}-${to} / ${count}`}
          />
        </Box>
      )}
      
      {/* 删除确认对话框 */}
      <Dialog
        open={deleteDialogOpen}
        onClose={cancelDelete}
      >
        <DialogTitle>确认删除</DialogTitle>
        <DialogContent>
          <Typography>
            您确定要删除这份报告吗？此操作不可撤销。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={cancelDelete}>取消</Button>
          <Button 
            onClick={confirmDelete} 
            color="error"
            variant="contained"
          >
            删除
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* 通知消息 */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert 
          onClose={handleCloseNotification} 
          severity={notification.type}
          variant="filled"
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ServiceReportsPage; 