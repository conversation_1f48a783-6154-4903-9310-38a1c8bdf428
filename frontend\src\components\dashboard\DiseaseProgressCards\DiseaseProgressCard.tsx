import React, { useState } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Collapse, 
  Divider,
  IconButton,
  Chip,
  useTheme,
  alpha
} from '@mui/material';
import { 
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  ScheduleOutlined as ScheduleIcon,
  Lock as LockIcon
} from '@mui/icons-material';
import { usePatientDiseaseContext } from '../../../context/PatientDiseaseContext';
import './DiseaseProgressCard.css'; // 导入样式文件

// 病程阶段类型
export type StageType = '初诊' | '确诊' | '治疗' | '随访' | '预后' | '封档';

// 病程阶段颜色映射
const stageColors: Record<StageType, string> = {
  '初诊': '#E53935', // 红色 - 初诊
  '确诊': '#8E24AA', // 紫色 - 确诊
  '治疗': '#FB8C00', // 橙色 - 治疗
  '随访': '#FDD835', // 黄色 - 随访/恢复
  '预后': '#43A047', // 绿色 - 预后
  '封档': '#757575'  // 灰色 - 封档
};

// 疾病类型定义
interface DiseaseProps {
  id: string;
  name: string;
  days: number;  // 病程天数
  stages: {
    type: StageType;
    date: string;
    completed: boolean;
  }[];
  currentStage: number;  // 当前阶段索引
  isActive: boolean;  // 是否活跃状态
  isPrivate?: boolean; // 是否私密
}

/**
 * 病理进度卡片组件
 * 显示疾病名称、天数、时间轴和详细阶段信息
 */
const DiseaseProgressCard: React.FC<DiseaseProps> = ({
  id,
  name,
  days,
  stages,
  currentStage,
  isActive,
  isPrivate = false
}) => {
  const theme = useTheme();
  const [expanded, setExpanded] = useState(false);
  const { setSelectedDisease } = usePatientDiseaseContext();

  // 处理展开/收起
  const handleExpandClick = () => {
    setExpanded(!expanded);
  };

  // 处理选择疾病
  const handleSelectDisease = () => {
    setSelectedDisease(id);
  };

  // 渲染时间轴节点
  const renderNodes = () => {
    return stages.map((stage, index) => {
      const isCurrentStage = index === currentStage;
      const isCompleted = index <= currentStage;
      
      return (
        <Box key={index} className="stage-node-container">
          {/* 节点 */}
          <Box 
            className={`stage-node ${isCurrentStage ? 'active' : ''} ${isCompleted ? 'completed' : ''}`}
            sx={{ 
              backgroundColor: isCompleted 
                ? (isCurrentStage ? stageColors[stage.type] : alpha(stageColors[stage.type], 0.7))
                : theme.palette.grey[300],
              borderColor: isCompleted ? stageColors[stage.type] : theme.palette.grey[400],
            }}
          >
            {isCurrentStage && (
              <Box className="node-center" />
            )}
          </Box>
          
          {/* 阶段名称 */}
          <Typography 
            variant="caption" 
            className="stage-label"
            sx={{
              color: isCurrentStage ? 'text.primary' : 'text.secondary',
              fontWeight: isCurrentStage ? 'bold' : 'normal',
            }}
          >
            {stage.type}
          </Typography>
        </Box>
      );
    });
  };

  // 渲染连接线
  const renderConnectors = () => {
    return stages.slice(0, -1).map((stage, index) => {
      const isActive = index < currentStage;
      
      return (
        <Box 
          key={index}
          className="stage-connector"
          sx={{
            backgroundColor: isActive 
              ? stageColors[stage.type]
              : theme.palette.grey[300],
          }}
        />
      );
    });
  };

  return (
    <Paper
      elevation={0}
      sx={{
        border: '1px solid',
        borderColor: isActive ? 'primary.main' : 'divider',
        borderRadius: 2,
        overflow: 'hidden',
        transition: 'all 0.2s',
        mb: 2,
        '&:hover': {
          borderColor: 'primary.main',
          boxShadow: 1,
        },
        backgroundColor: isActive ? alpha(theme.palette.primary.light, 0.1) : 'background.paper',
      }}
    >
      {/* 概览部分 */}
      <Box
        sx={{
          p: 2,
          cursor: 'pointer',
          bgcolor: expanded ? alpha(theme.palette.action.selected, 0.3) : 'inherit',
        }}
        onClick={handleSelectDisease}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 'bold',
                fontSize: { xs: '1rem', sm: '1.1rem' },
              }}
            >
              {name}
            </Typography>
            <Box sx={{ display: 'flex', ml: 1, gap: 0.5 }}>
              <Chip
                size="small"
                label={`${days}天`}
                color={isActive ? 'primary' : 'default'}
                variant="outlined"
                sx={{ height: 20, fontSize: '0.7rem' }}
              />
              {isPrivate && (
                <Chip
                  size="small"
                  icon={<LockIcon style={{ fontSize: '0.7rem' }} />}
                  label="隐私"
                  sx={{ 
                    height: 20, 
                    fontSize: '0.7rem',
                    bgcolor: alpha('#9F7AEA', 0.2), 
                    color: '#9F7AEA',
                    '& .MuiChip-icon': { color: '#9F7AEA' }
                  }}
                />
              )}
            </Box>
          </Box>
          <IconButton size="small" onClick={(e) => {
            e.stopPropagation();
            handleExpandClick();
          }}>
            {expanded ? (
              <ExpandLessIcon fontSize="small" />
            ) : (
              <ExpandMoreIcon fontSize="small" />
            )}
          </IconButton>
        </Box>

        {/* 时间轴 - 新的实现 */}
        <Box className="timeline-container">
          <Box className="timeline-content">
            {renderNodes()}
          </Box>
          <Box className="timeline-connectors">
            {renderConnectors()}
          </Box>
        </Box>
      </Box>

      {/* 详细阶段信息 */}
      <Collapse in={expanded} timeout="auto" unmountOnExit>
        <Divider />
        <Box sx={{ p: 2, pt: 1.5 }}>
          {stages.map((stage, index) => (
            <Box
              key={index}
              sx={{
                display: 'flex',
                alignItems: 'center',
                mb: index === stages.length - 1 ? 0 : 1.5,
                opacity: stage.completed ? 1 : 0.5,
              }}
            >
              {/* 阶段指示点 */}
              <Box
                sx={{
                  width: 12,
                  height: 12,
                  borderRadius: '50%',
                  backgroundColor: stageColors[stage.type],
                  mr: 1.5,
                }}
              />

              {/* 阶段信息 */}
              <Box>
                <Typography
                  variant="subtitle2"
                  sx={{
                    fontWeight: 600,
                    color: index === currentStage ? 'primary.main' : 'text.primary',
                  }}
                >
                  {stage.type}
                </Typography>

                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <ScheduleIcon sx={{ fontSize: '0.9rem', color: 'text.disabled', mr: 0.5 }} />
                  <Typography variant="caption" color="text.secondary">
                    {stage.date}
                  </Typography>
                </Box>
              </Box>
            </Box>
          ))}
        </Box>
      </Collapse>
    </Paper>
  );
};

export default DiseaseProgressCard; 