/**
 * 通知服务
 * 处理系统通知的创建、查询和更新
 */
const { v4: uuidv4 } = require('uuid');
const Notification = require('../../models/Notification');

/**
 * 创建系统通知
 * @param {Object} params - 通知参数
 * @param {string} params.userId - 接收通知的用户ID
 * @param {string} params.title - 通知标题
 * @param {string} params.content - 通知内容
 * @param {string} params.category - 通知类别，例如'SYSTEM'、'MEDICAL'、'AI_REPORT'等
 * @param {Object} [params.metadata] - 可选，通知相关的元数据
 * @returns {Promise<Object>} 创建的通知对象
 */
const createSystemNotification = async ({ userId, title, content, category, metadata = {} }) => {
  try {
    console.log(`[通知服务] 创建系统通知: ${title} - 用户ID: ${userId}`);
    
    if (!userId || !title || !content || !category) {
      throw new Error('必须提供用户ID、标题、内容和类别');
    }
    
    const notification = await Notification.query().insert({
      id: uuidv4(),
      user_id: userId,
      title,
      content,
      category,
      is_read: false,
      metadata,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    });
    
    console.log(`[通知服务] 系统通知创建成功，ID: ${notification.id}`);
    return notification;
  } catch (error) {
    console.error(`[通知服务] 创建系统通知失败:`, error);
    throw new Error(`创建系统通知失败: ${error.message}`);
  }
};

/**
 * 查询用户未读通知
 * @param {string} userId - 用户ID
 * @returns {Promise<Array>} 未读通知列表
 */
const getUserUnreadNotifications = async (userId) => {
  try {
    console.log(`[通知服务] 查询用户未读通知 - 用户ID: ${userId}`);
    
    const notifications = await Notification.query()
      .where({ user_id: userId, is_read: false })
      .orderBy('created_at', 'desc');
    
    console.log(`[通知服务] 查询到${notifications.length}条未读通知`);
    return notifications.map(notification => notification.toApiFormat());
  } catch (error) {
    console.error(`[通知服务] 查询用户未读通知失败:`, error);
    throw new Error(`查询用户未读通知失败: ${error.message}`);
  }
};

/**
 * 查询用户所有通知
 * @param {string} userId - 用户ID
 * @param {Object} options - 查询选项
 * @param {number} [options.limit=20] - 返回数量限制
 * @param {number} [options.offset=0] - 偏移量
 * @returns {Promise<Object>} 包含通知列表和总数的对象
 */
const getUserNotifications = async (userId, { limit = 20, offset = 0 } = {}) => {
  try {
    console.log(`[通知服务] 查询用户所有通知 - 用户ID: ${userId}, 限制: ${limit}, 偏移: ${offset}`);
    
    // 查询总数
    const total = await Notification.query()
      .where({ user_id: userId })
      .count('id as count')
      .first();
    
    // 查询通知列表
    const notifications = await Notification.query()
      .where({ user_id: userId })
      .orderBy('created_at', 'desc')
      .limit(limit)
      .offset(offset);
    
    console.log(`[通知服务] 查询到${notifications.length}条通知，总数: ${total.count}`);
    
    return {
      total: parseInt(total.count) || 0,
      notifications: notifications.map(notification => notification.toApiFormat())
    };
  } catch (error) {
    console.error(`[通知服务] 查询用户所有通知失败:`, error);
    throw new Error(`查询用户所有通知失败: ${error.message}`);
  }
};

/**
 * 标记通知为已读
 * @param {string} notificationId - 通知ID
 * @returns {Promise<Object>} 更新的通知对象
 */
const markNotificationAsRead = async (notificationId) => {
  try {
    console.log(`[通知服务] 标记通知为已读 - 通知ID: ${notificationId}`);
    
    const notification = await Notification.query()
      .updateAndFetchById(notificationId, {
        is_read: true,
        read_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });
    
    if (!notification) {
      throw new Error('通知不存在');
    }
    
    console.log(`[通知服务] 通知已标记为已读 - 通知ID: ${notificationId}`);
    return notification.toApiFormat();
  } catch (error) {
    console.error(`[通知服务] 标记通知为已读失败:`, error);
    throw new Error(`标记通知为已读失败: ${error.message}`);
  }
};

/**
 * 批量标记用户通知为已读
 * @param {string} userId - 用户ID
 * @returns {Promise<number>} 更新的通知数量
 */
const markAllUserNotificationsAsRead = async (userId) => {
  try {
    console.log(`[通知服务] 批量标记用户通知为已读 - 用户ID: ${userId}`);
    
    const now = new Date().toISOString();
    const result = await Notification.query()
      .where({ user_id: userId, is_read: false })
      .patch({
        is_read: true,
        read_at: now,
        updated_at: now
      });
    
    console.log(`[通知服务] 已标记${result}条通知为已读`);
    return result;
  } catch (error) {
    console.error(`[通知服务] 批量标记用户通知为已读失败:`, error);
    throw new Error(`批量标记用户通知为已读失败: ${error.message}`);
  }
};

/**
 * 删除通知
 * @param {string} notificationId - 通知ID
 * @returns {Promise<boolean>} 删除是否成功
 */
const deleteNotification = async (notificationId) => {
  try {
    console.log(`[通知服务] 删除通知 - 通知ID: ${notificationId}`);
    
    const result = await Notification.query()
      .deleteById(notificationId);
    
    console.log(`[通知服务] 通知删除${result ? '成功' : '失败'} - 通知ID: ${notificationId}`);
    return result === 1;
  } catch (error) {
    console.error(`[通知服务] 删除通知失败:`, error);
    throw new Error(`删除通知失败: ${error.message}`);
  }
};

module.exports = {
  createSystemNotification,
  getUserUnreadNotifications,
  getUserNotifications,
  markNotificationAsRead,
  markAllUserNotificationsAsRead,
  deleteNotification
}; 