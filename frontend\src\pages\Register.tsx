import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Box, 
  Paper, 
  Typography, 
  TextField, 
  Button, 
  Stack,
  Link,
  Alert,
  MenuItem,
  InputAdornment,
  IconButton
} from '@mui/material';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import { register } from '../services/authService';

// 注册页面组件
const RegisterPage: React.FC = () => {
  // 导航钩子
  const navigate = useNavigate();
  
  // 表单状态
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // 切换密码可见性
  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // 切换确认密码可见性
  const handleToggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  // 处理注册
  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault(); // 阻止表单默认提交
    
    // 简单表单验证
    if (!username.trim() || !email.trim() || !password.trim()) {
      setError('用户名、邮箱和密码不能为空');
      return;
    }

    if (password !== confirmPassword) {
      setError('两次输入的密码不一致');
      return;
    }

    // 简单的邮箱格式验证
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('请输入有效的邮箱地址');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      // 调用注册服务
      await register({
        username,
        email,
        password,
        phoneNumber: phoneNumber || undefined
      });
      
      // 注册成功，显示成功信息并准备跳转
      setSuccess('注册成功！3秒后跳转到登录页...');
      
      // 3秒后跳转到登录页
      setTimeout(() => {
        navigate('/login');
      }, 3000);
    } catch (err: any) {
      // 注册失败，显示错误信息
      setError(err.response?.data?.error || '注册失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  // 输入框主色调样式
  const textFieldStyle = {
    '&:hover .MuiInputLabel-root': {
      color: '#3498db',
    },
    '& .MuiOutlinedInput-root': {
      '&.Mui-focused fieldset': {
        borderColor: '#3498db',
      },
      '&:hover fieldset': {
        borderColor: '#3498db',
      },
    },
    '& .MuiInputLabel-root.Mui-focused': {
      color: '#3498db',
    }
  };

  return (
    <Box sx={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      minHeight: '100vh',
      bgcolor: 'background.default', // 使用主题的背景色而不是硬编码的颜色
      padding: '10px' // 只保留10px的边距空间
    }}>
      <Paper 
        elevation={0} 
        sx={{ 
          p: 4, 
          width: '100%', // 占满整个容器宽度
          maxWidth: { xs: '100%', sm: '500px' }, // 在大屏幕上限制最大宽度
          border: '1px solid',
          borderColor: 'divider', // 使用主题的分隔线颜色
          borderRadius: 2,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center'
        }}
      >
        {/* 兔子图标 */}
        <Box 
          component="img" 
          src="./assets/logo-rabbit.png" 
          alt="医生兔子图标"
          sx={{ 
            width: 90, 
            height: 90, 
            mb: 1,
            borderRadius: '50%',
            bgcolor: (theme) => theme.palette.mode === 'dark' ? '#232323' : '#f5f5f5' // 根据主题选择背景色
          }} 
        />

        {/* APP名称 - 字号减小2号 */}
        <Typography 
          variant="h5" 
          component="h1" 
          align="center" 
          sx={{ 
            color: '#3498db', 
            fontWeight: 700, 
            mb: 0.5 
          }}
        >
          慧看病
        </Typography>

        {/* 页面说明 - 修改文本并减小字号 */}
        <Typography 
          variant="caption" 
          align="center" 
          sx={{ mb: 3, color: 'text.secondary' }}
        >
          用慧看病真的会看病哦！
        </Typography>

        {/* 错误提示 */}
        {error && (
          <Alert severity="error" sx={{ mb: 2, width: '100%' }}>
            {error}
          </Alert>
        )}

        {/* 成功提示 */}
        {success && (
          <Alert severity="success" sx={{ mb: 2, width: '100%' }}>
            {success}
          </Alert>
        )}
        
        {/* 注册表单 */}
        <form onSubmit={handleRegister} style={{ width: '100%' }}>
          <Stack spacing={2}>
            <TextField
              label="用户名"
              variant="outlined"
              fullWidth
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              required
              size="small"
              placeholder="请输入用户名"
              sx={textFieldStyle}
            />
            <TextField
              label="邮箱"
              type="email"
              variant="outlined"
              fullWidth
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              size="small"
              placeholder="请输入邮箱地址"
              sx={textFieldStyle}
            />
            <TextField
              label="密码"
              type={showPassword ? 'text' : 'password'}
              variant="outlined"
              fullWidth
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              size="small"
              placeholder="请输入密码"
              sx={textFieldStyle}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={handleTogglePasswordVisibility}
                      edge="end"
                      size="small"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                )
              }}
            />
            <TextField
              label="确认密码"
              type={showConfirmPassword ? 'text' : 'password'}
              variant="outlined"
              fullWidth
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
              size="small"
              placeholder="请再次输入密码"
              sx={textFieldStyle}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={handleToggleConfirmPasswordVisibility}
                      edge="end"
                      size="small"
                    >
                      {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                )
              }}
            />
            <TextField
              label="手机号码"
              variant="outlined"
              fullWidth
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
              size="small"
              placeholder="请输入手机号码（可选）"
              sx={textFieldStyle}
            />
            <Button
              type="submit"
              variant="contained"
              fullWidth
              disableElevation
              sx={{ mt: 2 }}
              disabled={loading || !!success}
            >
              {loading ? '注册中...' : '注册'}
            </Button>
            <Box sx={{ textAlign: 'center', mt: 2 }}>
              <Typography variant="body2" component="span">
                已有账号？ 
              </Typography>
              <Link 
                href="/login" 
                underline="hover" 
                sx={{ 
                  ml: 1, 
                  color: '#3498db',
                  '&:hover': {
                    color: '#FF6F61', // 辅色
                  }
                }}
              >
                返回登录
              </Link>
            </Box>
          </Stack>
        </form>

        {/* 版权声明 */}
        <Typography 
          variant="caption" 
          align="center" 
          sx={{ mt: 4, color: 'text.secondary' }}
        >
          © 2025 慧看病 · 版权所有 楹旗
        </Typography>
      </Paper>
    </Box>
  );
};

export default RegisterPage; 