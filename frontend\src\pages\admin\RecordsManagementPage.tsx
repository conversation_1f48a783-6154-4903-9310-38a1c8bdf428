import React, { useState, useEffect, useCallback } from 'react';
import { 
  Box, 
  Paper, 
  Typography, 
  Button, 
  TextField, 
  InputAdornment, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow, 
  TablePagination, 
  IconButton, 
  Chip, 
  Dialog, 
  DialogActions, 
  DialogContent, 
  DialogContentText, 
  DialogTitle, 
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Card,
  CardContent,
  Tooltip,
  CircularProgress,
  Divider,
  Alert,
  Link,
  CardActions,
  Menu,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import { 
  Search as SearchIcon,
  FilterList as FilterListIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  EventNote as RecordIcon,
  CalendarMonth as DateIcon,
  Person as PersonIcon,
  MedicalInformation as DiseaseIcon,
  Refresh as RefreshIcon,
  LocalHospital as HospitalIcon,
  MoreVert as MoreVertIcon
} from '@mui/icons-material';
import axios from 'axios';
import { API_BASE_URL } from '../../config/api';
import { useSnackbar } from 'notistack';
import { format } from 'date-fns';
import { useTheme, ThemeProvider, createTheme as createMuiTheme } from '@mui/material/styles';
import { useMediaQuery } from '@mui/material';

// 就诊记录接口
interface Record {
  id: string;
  title: string;
  content: string;
  visitDate: string;
  doctorName: string;
  hospitalName: string;
  patientId: string;
  patientName: string;
  diseaseId: string;
  diseaseName: string;
  userId: string;
  username?: string;
  createdAt: string;
  recordType: string;
  recordTypeLabel?: string;
}

/**
 * 就诊记录管理页面
 * 管理员可以查看所有用户的就诊记录
 */
const RecordsManagementPage: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { enqueueSnackbar } = useSnackbar();
  
  const reducedFontSizeTheme = createMuiTheme(theme, {
    typography: {
      h1: { ...theme.typography.h1, fontSize: theme.typography.h1?.fontSize ? `calc(${theme.typography.h1.fontSize} - 0.4rem)`:'5.6rem' },
      h2: { ...theme.typography.h2, fontSize: theme.typography.h2?.fontSize ? `calc(${theme.typography.h2.fontSize} - 0.3rem)`:'3.45rem' },
      h3: { ...theme.typography.h3, fontSize: theme.typography.h3?.fontSize ? `calc(${theme.typography.h3.fontSize} - 0.25rem)`:'2.75rem' },
      h4: { ...theme.typography.h4, fontSize: theme.typography.h4?.fontSize ? `calc(${theme.typography.h4.fontSize} - 0.2rem)`:'1.925rem' },
      h5: { fontSize: '1.0rem' },
      h6: { fontSize: '0.85rem' },
      subtitle1: { fontSize: '0.75rem' }, 
      subtitle2: { fontSize: '0.65rem' }, 
      body1: { fontSize: '0.75rem' }, 
      body2: { fontSize: '0.65rem' }, 
      button: { fontSize: '0.65rem' },
      caption: { fontSize: '0.55rem' },
      overline: { fontSize: '0.55rem' },
    },
    components: {
      MuiInputLabel: { styleOverrides: { root: { fontSize: '0.75rem' } } },
      MuiMenuItem: { styleOverrides: { root: { fontSize: '0.75rem' } } },
      MuiTableCell: { styleOverrides: { root: { fontSize: '0.65rem' }, head: { fontSize: '0.7rem', fontWeight: 'bold' } } },
      MuiChip: { styleOverrides: { label: { fontSize: '0.55rem' }, labelSmall: { fontSize: '0.5rem' } } },
      MuiButton: { styleOverrides: { sizeSmall: { fontSize: '0.6rem' }, sizeMedium: { fontSize: '0.65rem' }, sizeLarge: { fontSize: '0.75rem' } } },
      MuiDialogTitle: { styleOverrides: { root: { fontSize: '0.85rem' } } },
      MuiDialogContentText: { styleOverrides: { root: { fontSize: '0.75rem' } } },
      MuiFormControlLabel: { styleOverrides: { label: { fontSize: '0.75rem' } } },
      MuiAlert: { styleOverrides: { message: { fontSize: '0.65rem' } } },
      MuiTablePagination: { 
        styleOverrides: { 
          caption: { fontSize: '0.65rem' }, 
          selectLabel: { fontSize: '0.65rem' }, 
          displayedRows: { fontSize: '0.65rem' } 
        } 
      }
    }
  });
  
  // 记录列表状态
  const [records, setRecords] = useState<Record[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // 搜索和筛选状态
  const [searchKeyword, setSearchKeyword] = useState('');
  const [filterOpen, setFilterOpen] = useState(false);
  const [recordTypeFilter, setRecordTypeFilter] = useState<string>('');
  const [patientIdFilter, setPatientIdFilter] = useState<string>('');
  const [diseaseIdFilter, setDiseaseIdFilter] = useState<string>('');
  const [startDateFilter, setStartDateFilter] = useState<string>('');
  const [endDateFilter, setEndDateFilter] = useState<string>('');
  
  // 分页状态
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  
  // 删除确认对话框状态
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [recordToDelete, setRecordToDelete] = useState<Record | null>(null);
  
  // 记录详情对话框状态
  const [detailDialogOpen, setDetailDialogOpen] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<Record | null>(null);
  
  // 更多操作菜单状态 (for mobile cards)
  const [mobileMenuAnchorEl, setMobileMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedRecordForMenu, setSelectedRecordForMenu] = useState<Record | null>(null);
  
  // 初始加载就诊记录数据
  useEffect(() => {
    fetchRecords();
  }, [page, rowsPerPage]);
  
  // 获取就诊记录列表
  const fetchRecords = useCallback(async (filterParams = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      // 构建查询参数
      const params: any = {
        page,
        pageSize: rowsPerPage,
        ...filterParams
      };
      
      if (searchKeyword.trim()) {
        params.search = searchKeyword.trim();
      }
      
      if (recordTypeFilter) {
        params.recordType = recordTypeFilter;
      }
      
      if (patientIdFilter) {
        params.patientId = patientIdFilter;
      }
      
      if (diseaseIdFilter) {
        params.diseaseId = diseaseIdFilter;
      }
      
      if (startDateFilter) {
        params.startDate = startDateFilter;
      }
      
      if (endDateFilter) {
        params.endDate = endDateFilter;
      }
      
      const response = await axios.get(`${API_BASE_URL}/api/admin/records`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        },
        params
      });
      
      if (response.data) {
        setRecords(response.data.records || []);
        setTotalCount(response.data.total || 0);
      } else {
        setRecords([]);
        setTotalCount(0);
      }
    } catch (err: any) {
      console.error('获取就诊记录列表失败:', err);
      setError(err.response?.data?.message || '获取就诊记录列表失败');
      enqueueSnackbar('获取就诊记录列表失败', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  }, [page, rowsPerPage, searchKeyword, recordTypeFilter, patientIdFilter, diseaseIdFilter, startDateFilter, endDateFilter, enqueueSnackbar]);
  
  // 应用筛选条件
  const applyFilters = () => {
    setPage(0); // 重置到第一页
    fetchRecords({
      search: searchKeyword,
      recordType: recordTypeFilter,
      patientId: patientIdFilter,
      diseaseId: diseaseIdFilter,
      startDate: startDateFilter,
      endDate: endDateFilter
    });
  };
  
  // 重置筛选条件
  const resetFilters = () => {
    setSearchKeyword('');
    setRecordTypeFilter('');
    setPatientIdFilter('');
    setDiseaseIdFilter('');
    setStartDateFilter('');
    setEndDateFilter('');
    setPage(0);
    fetchRecords({});
  };
  
  // 处理搜索输入变化
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchKeyword(event.target.value);
  };
  
  // 处理回车键搜索
  const handleSearchKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      applyFilters();
    }
  };
  
  // 处理分页变化
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };
  
  // 处理每页行数变化
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };
  
  // 打开删除确认对话框
  const openDeleteDialog = (record: Record) => {
    setRecordToDelete(record);
    setDeleteDialogOpen(true);
  };
  
  // 关闭删除确认对话框
  const closeDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setRecordToDelete(null);
  };
  
  // 删除就诊记录
  const deleteRecord = async () => {
    if (!recordToDelete) return;
    
    try {
      await axios.delete(`${API_BASE_URL}/api/admin/records/${recordToDelete.id}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      enqueueSnackbar('就诊记录已成功删除', { variant: 'success' });
      
      // 关闭对话框并刷新列表
      closeDeleteDialog();
      fetchRecords();
    } catch (err: any) {
      console.error('删除就诊记录失败:', err);
      enqueueSnackbar('删除就诊记录失败: ' + (err.response?.data?.message || err.message), { variant: 'error' });
    }
  };
  
  // 查看记录详情
  const viewRecordDetail = (record: Record) => {
    setSelectedRecord(record);
    setDetailDialogOpen(true);
  };
  
  // 关闭详情对话框
  const closeDetailDialog = () => {
    setDetailDialogOpen(false);
    setSelectedRecord(null);
  };
  
  // 移动端菜单处理
  const handleMobileMenuOpen = (event: React.MouseEvent<HTMLElement>, record: Record) => {
    setMobileMenuAnchorEl(event.currentTarget);
    setSelectedRecordForMenu(record);
  };

  const handleMobileMenuClose = () => {
    setMobileMenuAnchorEl(null);
    setSelectedRecordForMenu(null);
  };

  const handleViewDetailFromMenu = () => {
    if (selectedRecordForMenu) {
      viewRecordDetail(selectedRecordForMenu);
    }
    handleMobileMenuClose();
  };

  const handleDeleteFromMenu = () => {
    if (selectedRecordForMenu) {
      openDeleteDialog(selectedRecordForMenu);
    }
    handleMobileMenuClose();
  };
  
  // 格式化时间
  const formatDateTime = (dateString: string) => {
    if (!dateString) return '未知';
    try {
      const date = new Date(dateString);
      return format(date, 'yyyy-MM-dd');
    } catch (error) {
      return '无效日期';
    }
  };
  
  // 获取就诊记录类型的显示文本
  const translateRecordType = (type: string, record?: Record) => {
    // 优先使用后端返回的 recordTypeLabel
    if (record?.recordTypeLabel) {
      return record.recordTypeLabel;
    }
    
    // 后备翻译逻辑
    switch (type) {
      case 'SELF_DESCRIPTION': return '自述';
      case 'SYMPTOM': return '症状';
      case 'EXAMINATION': return '检查';
      case 'LAB_TEST': return '化验';
      case 'DIAGNOSIS': return '诊断';
      case 'TREATMENT': return '治疗';
      case 'HOSPITALIZATION': return '住院';
      case 'MEDICATION': return '用药';
      case 'SURGERY': return '手术';
      case 'MONITORING': return '监测';
      case 'PHYSICAL_THERAPY': return '理疗';
      case 'DISCHARGE': return '出院';
      case 'APPOINTMENT': return '预约';
      case 'REPORT': return '报告';
      case 'FOLLOW_UP': return '随访';
      case 'PROGNOSIS': return '预后';
      case 'AUX_DIAGNOSIS': return '辅诊';
      case 'NURSING': return '护理';
      case 'REVISIT': return '复诊';
      case 'REFERRAL': return '转诊';
      case 'PSYCHOLOGY': return '心理';
      case 'REHABILITATION': return '康复';
      case 'ASSESSMENT': return '评估';
      case 'OTHER': return '其他';
      case 'AI_ANALYSIS': return '辅医智能分析报告';
      case 'VISIT': return '门诊';
      case 'PRESCRIPTION': return '处方';
      default: return type || '未知';
    }
  };
  
  // 获取记录类型标签颜色
  const getRecordTypeColor = (type: string) => {
    switch (type) {
      case 'VISIT': return 'primary';
      case 'HOSPITALIZATION': return 'secondary';
      case 'EXAMINATION': return 'info';
      case 'SURGERY': return 'error';
      case 'FOLLOW_UP': return 'success';
      case 'PRESCRIPTION': return 'warning';
      default: return 'default';
    }
  };
  
  return (
    <ThemeProvider theme={reducedFontSizeTheme}>
      <Box sx={{ py: { xs: 1, sm: 2 } }}>
        <Typography variant="h6" component="h1" gutterBottom sx={{ mb: { xs: 1.5, sm: 2 } }}>
          就诊记录管理
        </Typography>
        
        {/* 搜索和筛选区域 */}
        <Paper elevation={1} sx={{ p: { xs: '10px', sm: 2 }, mb: { xs: 1.5, sm: 3 } }}>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
            <Box sx={{ flex: '1 1 300px', minWidth: 0 }}>
              <TextField
                placeholder="搜索就诊记录..."
                variant="outlined"
                size="small"
                fullWidth
                value={searchKeyword}
                onChange={handleSearchChange}
                onKeyPress={handleSearchKeyPress}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Box>
            
            <Box sx={{ flex: '1 1 300px', display: 'flex', gap: 1, justifyContent: 'flex-end', alignItems: 'center' }}>
              <Button 
                variant="outlined" 
                color="primary"
                startIcon={<FilterListIcon />}
                onClick={() => setFilterOpen(!filterOpen)}
              >
                高级筛选
              </Button>
              
              <Button 
                variant="outlined" 
                color="primary"
                startIcon={<RefreshIcon />}
                onClick={() => fetchRecords()}
                disabled={loading}
              >
                刷新
              </Button>
            </Box>
            
            {/* 高级筛选面板 */}
            {filterOpen && (
              <Box sx={{ width: '100%' }}>
                <Card variant="outlined">
                  <CardContent>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                      <Box sx={{ flex: '1 1 200px', minWidth: 0 }}>
                        <FormControl fullWidth size="small">
                          <InputLabel id="record-type-filter-label">记录类型</InputLabel>
                          <Select
                            labelId="record-type-filter-label"
                            value={recordTypeFilter}
                            label="记录类型"
                            onChange={(e) => setRecordTypeFilter(e.target.value)}
                          >
                            <MenuItem value="">全部</MenuItem>
                            <MenuItem value="VISIT">门诊</MenuItem>
                            <MenuItem value="HOSPITALIZATION">住院</MenuItem>
                            <MenuItem value="EXAMINATION">检查</MenuItem>
                            <MenuItem value="SURGERY">手术</MenuItem>
                            <MenuItem value="FOLLOW_UP">随访</MenuItem>
                            <MenuItem value="PRESCRIPTION">处方</MenuItem>
                            <MenuItem value="OTHER">其他</MenuItem>
                          </Select>
                        </FormControl>
                      </Box>
                      
                      <Box sx={{ flex: '1 1 200px', minWidth: 0 }}>
                        <TextField
                          label="患者ID"
                          size="small"
                          fullWidth
                          value={patientIdFilter}
                          onChange={(e) => setPatientIdFilter(e.target.value)}
                        />
                      </Box>
                      
                      <Box sx={{ flex: '1 1 200px', minWidth: 0 }}>
                        <TextField
                          label="病理ID"
                          size="small"
                          fullWidth
                          value={diseaseIdFilter}
                          onChange={(e) => setDiseaseIdFilter(e.target.value)}
                        />
                      </Box>
                      
                      <Box sx={{ flex: '1 1 200px', minWidth: 0 }}>
                        <TextField
                          label="开始日期"
                          type="date"
                          size="small"
                          fullWidth
                          value={startDateFilter}
                          onChange={(e) => setStartDateFilter(e.target.value)}
                          InputLabelProps={{ shrink: true }}
                        />
                      </Box>
                      
                      <Box sx={{ flex: '1 1 200px', minWidth: 0 }}>
                        <TextField
                          label="结束日期"
                          type="date"
                          size="small"
                          fullWidth
                          value={endDateFilter}
                          onChange={(e) => setEndDateFilter(e.target.value)}
                          InputLabelProps={{ shrink: true }}
                        />
                      </Box>
                      
                      <Box sx={{ width: '100%', display: 'flex', justifyContent: 'flex-end', mt: 1 }}>
                        <Button 
                          variant="outlined" 
                          color="secondary" 
                          onClick={resetFilters}
                          sx={{ mr: 1 }}
                        >
                          重置
                        </Button>
                        <Button 
                          variant="contained" 
                          color="primary" 
                          onClick={applyFilters}
                        >
                          应用筛选
                        </Button>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Box>
            )}
          </Box>
        </Paper>
        
        {/* 移动端卡片视图 */}
        {isMobile && (
          <Box>
            {loading && <CircularProgress sx={{ display: 'block', margin: 'auto', my: 4 }} />}
            {error && <Typography color="error" sx={{ textAlign: 'center', my: 4 }}>{error}</Typography>}
            {!loading && !error && records.length === 0 && (
              <Typography sx={{ textAlign: 'center', my: 4 }}>未找到符合条件的就诊记录。</Typography>
            )}
            {!loading && !error && records.map((record) => (
              <Card key={record.id} sx={{ mb: 2, boxShadow: 3 }}>
                <CardContent sx={{ px: '10px', '&:last-child': { pb: '10px' } }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                    <Typography variant="subtitle1" component="div" sx={{ fontWeight: 'bold', flexGrow: 1, mr: 1, wordBreak: 'break-word' }}>
                      {record.title}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: -0.5 }}>
                      <Chip 
                        label={translateRecordType(record.recordType, record)} 
                        color={getRecordTypeColor(record.recordType)} 
                        size="small" 
                        sx={{ mr: 0.5 }}
                      />
                      <IconButton size="small" onClick={(event) => handleMobileMenuOpen(event, record)} sx={{ p: 0.5 }}>
                        <MoreVertIcon />
                      </IconButton>
                    </Box>
                  </Box>
                  
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    患者: <Link component="span" onClick={() => {
                      console.log('Navigate to patient', record.patientId);
                      // 通过过滤器实现导航到特定患者的记录列表
                      setPatientIdFilter(record.patientId);
                      applyFilters();
                    }} sx={{cursor: 'pointer'}}>{record.patientName || record.patientId}</Link>
                  </Typography>
                  {record.diseaseName && (
                    <Typography variant="body2" color="text.secondary">
                      相关病理: <Link component="span" onClick={() => {
                        console.log('Navigate to disease', record.diseaseId);
                        // 通过过滤器实现导航到特定病理的记录列表
                        setDiseaseIdFilter(record.diseaseId);
                        applyFilters();
                      }} sx={{cursor: 'pointer'}}>{record.diseaseName}</Link>
                    </Typography>
                  )}
                  <Typography variant="body2" color="text.secondary">
                    就诊日期: {formatDateTime(record.visitDate)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
                    <HospitalIcon fontSize="inherit" sx={{ mr: 0.5, color: 'text.secondary' }} /> {record.hospitalName || '未知医院'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    医生: {record.doctorName || '未知医生'}
                  </Typography>
                  {record.username && (
                    <Typography variant="caption" display="block" sx={{ mt: 1, color: 'text.disabled' }}>
                      记录者: {record.username}
                    </Typography>
                  )}
                   <Typography variant="caption" display="block" sx={{ color: 'text.disabled' }}>
                      创建于: {formatDateTime(record.createdAt)}
                    </Typography>
                </CardContent>
              </Card>
            ))}
             <Menu
              anchorEl={mobileMenuAnchorEl}
              open={Boolean(mobileMenuAnchorEl)}
              onClose={handleMobileMenuClose}
            >
              <MenuItem onClick={handleViewDetailFromMenu}>
                <ListItemIcon>
                  <VisibilityIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText>查看详情</ListItemText>
              </MenuItem>
              <MenuItem onClick={handleDeleteFromMenu}>
                <ListItemIcon>
                  <DeleteIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText>删除</ListItemText>
              </MenuItem>
            </Menu>
            {totalCount > 0 && (
              <TablePagination
                component="div"
                count={totalCount}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                rowsPerPageOptions={[5, 10, 25]}
                labelRowsPerPage="每页:"
                sx={{ mt: 2 }}
              />
            )}
          </Box>
        )}

        {/* 桌面端表格视图 */}
        {!isMobile && (
          <Paper sx={{ width: '100%', overflow: 'hidden' }}>
            <TableContainer sx={{ maxHeight: 'calc(100vh - 360px)' }}>
              <Table stickyHeader size="medium">
                <TableHead>
                  <TableRow>
                    <TableCell>记录标题</TableCell>
                    <TableCell>类型</TableCell>
                    <TableCell>患者</TableCell>
                    <TableCell>就诊日期</TableCell>
                    <TableCell>医院</TableCell>
                    <TableCell>医生</TableCell>
                    <TableCell align="center">操作</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={7} align="center">
                        <CircularProgress size={24} sx={{ my: 2 }} />
                        <Typography variant="body2" color="textSecondary" sx={{ ml: 2 }}>
                          加载中...
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ) : error ? (
                    <TableRow>
                      <TableCell colSpan={7} align="center">
                        <Typography variant="body2" color="error" sx={{ my: 2 }}>
                          {error}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ) : records.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} align="center">
                        <Typography variant="body2" color="textSecondary" sx={{ my: 2 }}>
                          {searchKeyword || recordTypeFilter || patientIdFilter || diseaseIdFilter || startDateFilter || endDateFilter ? 
                            '没有匹配的就诊记录' : '暂无就诊记录'}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ) : (
                    // 就诊记录列表
                    records.map((record) => (
                      <TableRow 
                        key={record.id}
                        hover
                        sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                      >
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <RecordIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                            <Tooltip title={record.title.length > 30 ? record.title : ''}>
                              <Typography variant="body2" sx={{ maxWidth: 200, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                                {record.title}
                              </Typography>
                            </Tooltip>
                          </Box>
                        </TableCell>
                        
                        <TableCell>
                          <Chip 
                            label={translateRecordType(record.recordType, record)} 
                            size="small" 
                            color={getRecordTypeColor(record.recordType)}
                            variant="outlined"
                          />
                        </TableCell>
                        
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <PersonIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                            <Tooltip title={`患者ID: ${record.patientId}`}>
                              <Link
                                component="button"
                                variant="body2"
                                onClick={() => {
                                  // Maybe navigate to patient page or filter by patient
                                  console.log('Filter by patient:', record.patientId);
                                  setPatientIdFilter(record.patientId);
                                  applyFilters();
                                }}
                              >
                                {record.patientName}
                              </Link>
                            </Tooltip>
                          </Box>
                        </TableCell>
                        
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <DateIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                            <Typography variant="body2">{formatDateTime(record.visitDate)}</Typography>
                          </Box>
                        </TableCell>
                        
                        <TableCell>
                           <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <HospitalIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                            <Tooltip title={record.hospitalName.length > 20 ? record.hospitalName : ''}>
                              <Typography variant="body2" sx={{ maxWidth: 150, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                                {record.hospitalName || '未知'}
                              </Typography>
                            </Tooltip>
                          </Box>
                        </TableCell>

                        <TableCell>
                          <Typography variant="body2">{record.doctorName || '未知'}</Typography>
                        </TableCell>
                        
                        <TableCell align="center">
                          <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                            <Tooltip title="查看详情">
                              <IconButton
                                size="small"
                                onClick={() => viewRecordDetail(record)}
                                sx={{ mr: 0.5 }}
                              >
                                <VisibilityIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                            
                            <Tooltip title="删除记录">
                              <IconButton
                                size="small"
                                color="error"
                                onClick={() => openDeleteDialog(record)}
                              >
                                <DeleteIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>
            
            {/* 分页控件 */}
            <TablePagination
              rowsPerPageOptions={[5, 10, 25, 50]}
              component="div"
              count={totalCount}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
              labelRowsPerPage="每页行数:"
              labelDisplayedRows={({ from, to, count }) => `${from}-${to} / 共${count}条`}
            />
          </Paper>
        )}
        
        {/* 记录详情对话框 */}
        <Dialog
          open={detailDialogOpen}
          onClose={closeDetailDialog}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <RecordIcon sx={{ mr: 1 }} />
              就诊记录详情
            </Box>
          </DialogTitle>
          <DialogContent dividers>
            {!selectedRecord ? (
              <Alert severity="error">未能加载就诊记录详情</Alert>
            ) : (
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>基本信息</Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                      <Box sx={{ flex: '1 1 calc(50% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">记录标题</Typography>
                        <Typography variant="body1" sx={{ wordBreak: 'break-word' }}>{selectedRecord.title}</Typography>
                      </Box>
                      <Box sx={{ flex: '1 1 calc(50% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">记录类型</Typography>
                        <Typography variant="body1">{translateRecordType(selectedRecord.recordType, selectedRecord)}</Typography>
                      </Box>
                      <Box sx={{ flex: '1 1 calc(50% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">就诊日期</Typography>
                        <Typography variant="body1">{formatDateTime(selectedRecord.visitDate)}</Typography>
                      </Box>
                      <Box sx={{ flex: '1 1 calc(50% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">就诊医院</Typography>
                        <Typography variant="body1">{selectedRecord.hospitalName || '未记录'}</Typography>
                      </Box>
                      <Box sx={{ flex: '1 1 calc(50% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">就诊医生</Typography>
                        <Typography variant="body1">{selectedRecord.doctorName || '未记录'}</Typography>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>患者与病理信息</Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                       <Box sx={{ flex: '1 1 calc(50% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">患者姓名</Typography>
                        <Typography variant="body1">{selectedRecord.patientName}</Typography>
                      </Box>
                      <Box sx={{ flex: '1 1 calc(50% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">患者ID</Typography>
                        <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>{selectedRecord.patientId}</Typography>
                      </Box>
                      <Box sx={{ flex: '1 1 calc(50% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">相关病理</Typography>
                        <Typography variant="body1">{selectedRecord.diseaseName || '未关联病理'}</Typography>
                      </Box>
                       <Box sx={{ flex: '1 1 calc(50% - 8px)', minWidth: '250px' }}>
                        <Typography variant="subtitle2" color="textSecondary">病理ID</Typography>
                        <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>{selectedRecord.diseaseId || 'N/A'}</Typography>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
                
                {selectedRecord.content && (
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>记录内容</Typography>
                      <Typography variant="body2" component="pre" sx={{ 
                        whiteSpace: 'pre-wrap',
                        backgroundColor: 'background.paper',
                        p: 2,
                        borderRadius: 1,
                        border: '1px solid',
                        borderColor: 'divider'
                      }}>
                        {selectedRecord.content}
                      </Typography>
                    </CardContent>
                  </Card>
                )}
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button 
              color="error" 
              onClick={() => {
                closeDetailDialog();
                if (selectedRecord) {
                  openDeleteDialog(selectedRecord);
                }
              }}
              startIcon={<DeleteIcon />}
            >
              删除记录
            </Button>
            <Button onClick={closeDetailDialog} color="primary">
              关闭
            </Button>
          </DialogActions>
        </Dialog>
        
        {/* 删除确认对话框 */}
        <Dialog
          open={deleteDialogOpen}
          onClose={closeDeleteDialog}
        >
          <DialogTitle id="delete-dialog-title">
            确认删除
          </DialogTitle>
          <DialogContent>
            <DialogContentText id="delete-dialog-description" component="div">
              您确定要删除就诊记录 
              {recordToDelete && (
                <Typography component="span" sx={{ fontWeight: 'bold' }}>
                  "{recordToDelete.title}"
                </Typography>
              )}
              吗？此操作不可恢复。
              {recordToDelete && (
                <Box sx={{ mt: 1, fontSize: '0.8em', color: 'text.secondary' }}>
                  <Typography component="div" variant="caption">ID: {recordToDelete.id}</Typography>
                  <Typography component="div" variant="caption">患者: {recordToDelete.patientName} (ID: {recordToDelete.patientId})</Typography>
                  <Typography component="div" variant="caption">病理: {recordToDelete.diseaseName} (ID: {recordToDelete.diseaseId})</Typography>
                  <Typography component="div" variant="caption">创建者: {recordToDelete.username || recordToDelete.userId}</Typography>
                </Box>
              )}
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={closeDeleteDialog}>
              取消
            </Button>
            <Button 
              onClick={deleteRecord} 
              color="error" 
              variant="contained"
              startIcon={<DeleteIcon />}
            >
              确认删除
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </ThemeProvider>
  );
};

export default RecordsManagementPage; 