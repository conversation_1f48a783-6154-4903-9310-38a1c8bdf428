import apiClient from './apiClient';
import { logApiError } from '../utils/apiErrorMonitor';

// 定义全局事件处理
type AiUsageUpdateListener = () => void;
const listeners: AiUsageUpdateListener[] = [];

/**
 * 注册AI使用更新监听器
 * @param listener 监听函数
 */
export const registerAiUsageUpdateListener = (listener: AiUsageUpdateListener): void => {
  if (!listeners.includes(listener)) {
    listeners.push(listener);
  }
};

/**
 * 移除AI使用更新监听器
 * @param listener 监听函数
 */
export const unregisterAiUsageUpdateListener = (listener: AiUsageUpdateListener): void => {
  const index = listeners.indexOf(listener);
  if (index !== -1) {
    listeners.splice(index, 1);
  }
};

/**
 * 触发所有监听器
 */
const notifyListeners = (): void => {
  listeners.forEach(listener => {
    try {
      listener();
    } catch (error) {
      console.error('AI使用监听器执行错误:', error);
    }
  });
};

/**
 * 增加AI使用次数
 * 当用户使用AI功能时调用
 */
export const incrementAiUsage = async (): Promise<boolean> => {
  try {
    // 使用带/api前缀的路径
    const incrementPath = '/api/user/ai-usage/increment';

    console.log('[aiUsageService] 调用增加AI使用次数API:', incrementPath);

    // 发送空对象作为请求体，确保请求格式正确
    const response = await apiClient.post(incrementPath, {});

    console.log('[aiUsageService] 增加AI使用次数响应:', response.status, response.data);

    if (response.status === 200 || response.status === 204) {
      // 通知所有监听器
      notifyListeners();
      return true;
    }

    return false;
  } catch (error) {
    console.error('增加AI使用次数失败:', error);
    logApiError(error);

    // 尝试备用路径
    try {
      console.log('[aiUsageService] 尝试使用备用路径');
      const backupPath = '/user/ai-usage/increment';
      const response = await apiClient.post(backupPath, {});

      console.log('[aiUsageService] 备用路径响应:', response.status, response.data);

      if (response.status === 200 || response.status === 204) {
        notifyListeners();
        return true;
      }

      return false;
    } catch (backupError) {
      console.error('备用路径也失败:', backupError);
      return false;
    }
  }
};

/**
 * 重置AI使用次数
 * 通常由系统自动调用，每月重置
 */
export const resetAiUsage = async (): Promise<boolean> => {
  try {
    // 使用带/api前缀的路径
    const resetPath = '/api/user/ai-usage/reset';

    console.log('[aiUsageService] 调用重置AI使用次数API:', resetPath);

    const response = await apiClient.post(resetPath, {});

    console.log('[aiUsageService] 重置AI使用次数响应:', response.status, response.data);

    if (response.status === 200 || response.status === 204) {
      // 通知所有监听器
      notifyListeners();
      return true;
    }

    return false;
  } catch (error) {
    console.error('重置AI使用次数失败:', error);
    logApiError(error);

    // 尝试备用路径
    try {
      console.log('[aiUsageService] 尝试使用备用路径');
      const backupPath = '/user/ai-usage/reset';
      const response = await apiClient.post(backupPath, {});

      console.log('[aiUsageService] 备用路径响应:', response.status, response.data);

      if (response.status === 200 || response.status === 204) {
        notifyListeners();
        return true;
      }

      return false;
    } catch (backupError) {
      console.error('备用路径也失败:', backupError);
      return false;
    }
  }
};

// 创建命名常量
const aiUsageService = {
  incrementAiUsage,
  resetAiUsage,
  registerAiUsageUpdateListener,
  unregisterAiUsageUpdateListener
};

export default aiUsageService;