import React, { useMemo } from 'react';
import { Box, Typography, Chip, Avatar, CircularProgress } from '@mui/material';
import {
  BloodtypeOutlined as BloodTypeIcon,
  HeightOutlined as HeightIcon,
  MonitorWeightOutlined as WeightIcon,
  SquareFoot as BMIIcon,
  Wc as GenderIcon,
  Fingerprint as IDIcon,
  Phone as PhoneIcon,
} from '@mui/icons-material';
import BaseCard from './BaseCard';

interface PatientInfoCardProps {
  patient: any;
  loading?: boolean;
}

interface InfoItemProps {
  icon: React.ReactNode;
  label: string;
  value: string;
  extra?: React.ReactNode;
}

/**
 * 患者信息卡片组件
 * 显示患者基本信息，包括姓名、性别、年龄、血型等
 */
const PatientInfoCard: React.FC<PatientInfoCardProps> = ({ patient, loading = false }) => {
  
  // 计算年龄
  const age = useMemo(() => {
    if (!patient?.birthDate) return '未知';
    
    try {
      const birthDate = new Date(patient.birthDate);
      const today = new Date();
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }
      
      return age;
    } catch (e) {
      console.error('计算年龄出错:', e);
      return '未知';
    }
  }, [patient?.birthDate]);
  
  // 计算BMI
  const bmi = useMemo(() => {
    if (!patient?.height || !patient?.weight) return null;
    
    const height = parseFloat(patient.height) / 100; // 转换为米
    const weight = parseFloat(patient.weight);
    
    if (isNaN(height) || isNaN(weight) || height <= 0 || weight <= 0) return null;
    
    const bmiValue = weight / (height * height);
    return bmiValue.toFixed(1);
  }, [patient?.height, patient?.weight]);
  
  // 获取BMI状态和颜色
  const getBMIStatus = (bmi: number | null) => {
    if (bmi === null) return { status: '未知', color: 'default' };
    
    if (bmi < 18.5) return { status: '偏瘦', color: 'warning' };
    if (bmi < 24) return { status: '正常', color: 'success' };
    if (bmi < 28) return { status: '超重', color: 'warning' };
    return { status: '肥胖', color: 'error' };
  };
  
  const bmiStatus = useMemo(() => getBMIStatus(bmi ? parseFloat(bmi) : null), [bmi]);
  
  // 获取性别显示文本
  const genderText = useMemo(() => {
    if (!patient?.gender) return '未知';
    
    const gender = patient.gender.toLowerCase();
    if (gender === 'male' || gender === '男') return '男';
    if (gender === 'female' || gender === '女') return '女';
    return '未知';
  }, [patient?.gender]);
  
  // 获取性别颜色和背景色 - 修改为男性使用主色，女性使用辅色
  const getGenderStyles = useMemo(() => {
    const gender = patient?.gender?.toLowerCase() || '';
    
    if (gender === 'male' || gender === '男') {
      return {
        avatarBgColor: 'primary.main', // 使用主色
        chipColor: 'primary',
        chipTextColor: 'white',
        chipBgColor: 'primary.main'
      };
    } else if (gender === 'female' || gender === '女') {
      return {
        avatarBgColor: 'secondary.main', // 使用辅色
        chipColor: 'secondary',
        chipTextColor: 'white',
        chipBgColor: 'secondary.main'
      };
    } else {
      return {
        avatarBgColor: 'grey.500',
        chipColor: 'default',
        chipTextColor: 'text.primary',
        chipBgColor: 'grey.300'
      };
    }
  }, [patient?.gender]);
  
  // 获取首字母头像
  const getAvatarText = (name: string) => {
    if (!name) return '?';
    return name.charAt(0).toUpperCase();
  };
  
  // 信息项组件
  const InfoItem: React.FC<InfoItemProps> = ({ icon, label, value, extra }) => (
    <Box sx={{ width: { xs: '45%', md: '30%' }, mb: 1.5 }}>
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        {icon}
        <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
          {label}: {value}
        </Typography>
        {extra}
      </Box>
    </Box>
  );
  
  if (loading) {
    return (
      <BaseCard title="患者信息" loading={true}>
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress size={30} />
        </Box>
      </BaseCard>
    );
  }
  
  if (!patient) {
    return (
      <BaseCard title="患者信息">
        <Typography variant="body2" color="text.secondary" align="center" sx={{ p: 2 }}>
          未找到患者信息
        </Typography>
      </BaseCard>
    );
  }
  
  return (
    <BaseCard 
      title="患者信息" 
      loading={loading}
      subTitle={patient?.relationship ? `关系: ${patient.relationship}` : undefined}
    >
      <Box sx={{ display: 'flex', flexDirection: 'column' }}>
        {/* 患者基本信息 */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Avatar 
            sx={{ 
              width: 50, 
              height: 50, 
              mr: 2, 
              bgcolor: getGenderStyles.avatarBgColor,
              fontSize: '1.25rem'
            }}
          >
            {getAvatarText(patient.name)}
          </Avatar>
          
          <Box>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
              <Typography variant="subtitle1" component="div" sx={{ mr: 1, fontSize: '0.9rem' }}>
                {patient.name || '未知姓名'}
              </Typography>
              
              <Chip 
                label={genderText} 
                color={getGenderStyles.chipColor as any}
                size="small"
                icon={<GenderIcon fontSize="small" sx={{ color: getGenderStyles.chipTextColor }} />}
                sx={{ 
                  height: 20, 
                  fontSize: '0.7rem', 
                  bgcolor: getGenderStyles.chipBgColor,
                  color: getGenderStyles.chipTextColor
                }}
              />
            </Box>
            
            <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
              {`${age}岁`}
            </Typography>
          </Box>
        </Box>
        
        {/* 详细信息列表 - 移除了出生日期和住址信息 */}
        <Box sx={{ mb: 0.5 }}>
          {patient.idNumber && (
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <IDIcon fontSize="small" sx={{ mr: 1, color: 'primary.main', fontSize: '1rem' }} />
              <Typography variant="body2" sx={{ fontSize: '0.75rem' }}>
                {patient.idNumber}
              </Typography>
            </Box>
          )}
          
          {patient.phone && (
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <PhoneIcon fontSize="small" sx={{ mr: 1, color: 'primary.main', fontSize: '1rem' }} />
              <Typography variant="body2" sx={{ fontSize: '0.75rem' }}>
                {patient.phone}
              </Typography>
            </Box>
          )}
          
          {patient.medicalHistory && (
            <Box sx={{ mt: 1 }}>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5, fontSize: '0.75rem' }}>
                病史:
              </Typography>
              <Typography variant="body2" sx={{ fontSize: '0.75rem' }}>
                {patient.medicalHistory}
              </Typography>
            </Box>
          )}
        </Box>
        
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: { xs: 1, md: 2 } }}>
          {/* 血型 - 使用彩色图标 */}
          {patient?.bloodType && (
            <InfoItem 
              icon={<BloodTypeIcon fontSize="small" sx={{ mr: 1, color: 'error.main' }} />}
              label="血型"
              value={`${patient.bloodType}型`}
            />
          )}
          
          {/* 身高 - 使用彩色图标 */}
          {patient?.height && (
            <InfoItem 
              icon={<HeightIcon fontSize="small" sx={{ mr: 1, color: 'info.main' }} />}
              label="身高"
              value={`${patient.height}cm`}
            />
          )}
          
          {/* 体重 - 使用彩色图标 */}
          {patient?.weight && (
            <InfoItem 
              icon={<WeightIcon fontSize="small" sx={{ mr: 1, color: 'secondary.main' }} />}
              label="体重"
              value={`${patient.weight}kg`}
            />
          )}
          
          {/* BMI - 使用彩色图标 */}
          {bmi && (
            <InfoItem 
              icon={<BMIIcon fontSize="small" sx={{ mr: 1, color: 'success.main' }} />}
              label="BMI"
              value={bmi}
              extra={
                <Chip 
                  label={bmiStatus.status} 
                  size="small" 
                  color={bmiStatus.color as any}
                  sx={{ ml: 1, height: 18, fontSize: '0.6rem' }} 
                />
              }
            />
          )}
        </Box>
      </Box>
    </BaseCard>
  );
};

export default PatientInfoCard; 