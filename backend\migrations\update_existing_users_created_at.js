/**
 * 这个脚本用于更新已有用户的created_at字段
 * 如果created_at为空，将其设置为：
 * 1. 如果有last_login_at，则使用last_login_at的值
 * 2. 如果没有last_login_at，则使用updated_at的值
 * 3. 如果都没有，则使用当前时间
 */
exports.up = async function(knex) {
  // 获取所有created_at为空的用户
  const users = await knex('users')
    .whereNull('created_at')
    .select('id', 'last_login_at', 'updated_at');
  
  console.log(`需要更新created_at的用户数量: ${users.length}`);
  
  // 为每个用户设置合适的created_at值
  for (const user of users) {
    const now = new Date().toISOString();
    let created_at = now;
    
    // 优先使用last_login_at，其次是updated_at，最后是当前时间
    if (user.last_login_at) {
      created_at = user.last_login_at;
    } else if (user.updated_at) {
      created_at = user.updated_at;
    }
    
    await knex('users')
      .where('id', user.id)
      .update({ created_at });
    
    console.log(`已更新用户ID ${user.id} 的created_at值为 ${created_at}`);
  }
  
  console.log('所有用户的created_at字段已更新完成');
};

exports.down = function(knex) {
  // 回滚代码（不做实际操作，因为不希望删除created_at值）
  return Promise.resolve();
}; 