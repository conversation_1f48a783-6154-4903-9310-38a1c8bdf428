/**
 * API性能测试脚本
 * 用于测试后端API的性能和功能
 */

const axios = require('axios');
const chalk = require('chalk');

// API基础URL
const API_BASE_URL = 'http://localhost:3001/api';
// 测试用户信息
const TEST_USER = {
  username: `testuser_${Date.now()}`,
  password: 'Test@123456',
  email: `test_${Date.now()}@example.com`,
  nickname: '测试用户'
};
// 存储测试数据
const testData = {
  token: null,
  userId: null,
  patients: []
};

/**
 * 格式化时间显示
 * @param {number} ms - 毫秒数
 * @returns {string} 格式化后的时间字符串
 */
function formatTime(ms) {
  if (ms < 1000) {
    return `${ms}ms`;
  } else {
    return `${(ms / 1000).toFixed(2)}s`;
  }
}

/**
 * 记录测试结果
 * @param {string} testName - 测试名称
 * @param {number} time - 执行时间（毫秒）
 * @param {boolean} success - 是否成功
 * @param {string} message - 附加信息
 */
function logResult(testName, time, success, message = '') {
  const status = success ? chalk.green('✓ 成功') : chalk.red('✗ 失败');
  const timeStr = chalk.yellow(formatTime(time));
  console.log(`${testName}: ${status} - 耗时: ${timeStr}`);
  if (message) {
    console.log(`  ${message}`);
  }
}

/**
 * 测量异步函数执行时间
 * @param {Function} fn - 要测量的异步函数
 * @returns {Promise<{time: number, result: any, success: boolean}>} 执行结果
 */
async function measureTime(fn) {
  const start = Date.now();
  try {
    const result = await fn();
    const time = Date.now() - start;
    return { time, result, success: true };
  } catch (error) {
    const time = Date.now() - start;
    return { time, error, success: false };
  }
}

/**
 * 测试用户注册功能
 */
async function testRegister() {
  const { time, result, success } = await measureTime(async () => {
    const response = await axios.post(`${API_BASE_URL}/users/register`, TEST_USER);
    testData.userId = response.data.id;
    return response.data;
  });

  logResult('用户注册', time, success, success ? 
    `创建用户: ${TEST_USER.username}` : 
    `注册失败: ${JSON.stringify(result?.error?.response?.data || {})}`);
}

/**
 * 测试用户登录功能
 * @param {string} username - 用户名
 */
async function testLogin(username = TEST_USER.username) {
  const { time, result, success } = await measureTime(async () => {
    const response = await axios.post(`${API_BASE_URL}/users/login`, {
      username,
      password: TEST_USER.password
    });
    // 保存token用于后续请求
    testData.token = response.data.token;
    return response.data;
  });

  logResult('用户登录', time, success, success ? 
    `获取Token: ${testData.token?.substring(0, 15)}...` : 
    `登录失败: ${JSON.stringify(result?.error?.response?.data || {})}`);
}

/**
 * 测试获取用户资料
 */
async function testGetProfile() {
  const { time, result, success } = await measureTime(async () => {
    const response = await axios.get(`${API_BASE_URL}/users/profile`, {
      headers: { Authorization: `Bearer ${testData.token}` }
    });
    return response.data;
  });

  logResult('获取用户资料', time, success, success ? 
    `用户资料: ${result.username}, ${result.nickname}` : 
    `获取失败: ${JSON.stringify(result?.error?.response?.data || {})}`);
}

/**
 * 测试添加患者
 * @param {number} count - 要添加的患者数量
 */
async function testAddPatients(count = 5) {
  console.log(chalk.cyan(`\n添加 ${count} 个测试患者:`));
  
  for (let i = 0; i < count; i++) {
    const patientData = {
      name: `测试患者${i+1}`,
      gender: i % 2 === 0 ? '男' : '女',
      phoneNumber: `1380000${1000 + i}`,
      email: `patient${i+1}@test.com`,
      birthDate: `199${i % 10}-01-01`,
      idCard: `11010119900101${1000 + i}`,
      address: '北京市朝阳区测试地址',
      emergencyContactName: '紧急联系人',
      emergencyContactPhone: '13900001234',
      bloodType: ['A', 'B', 'O', 'AB'][i % 4]
    };

    const { time, result, success } = await measureTime(async () => {
      const response = await axios.post(`${API_BASE_URL}/patients`, patientData, {
        headers: { Authorization: `Bearer ${testData.token}` }
      });
      // 保存患者ID用于后续测试
      testData.patients.push(response.data.id);
      return response.data;
    });

    logResult(`添加患者 #${i+1}`, time, success, success ? 
      `患者ID: ${result.id}, 姓名: ${patientData.name}` : 
      `添加失败: ${JSON.stringify(result?.error?.response?.data || {})}`);
  }
}

/**
 * 测试获取患者列表
 */
async function testGetPatients() {
  const { time, result, success } = await measureTime(async () => {
    const response = await axios.get(`${API_BASE_URL}/patients`, {
      headers: { Authorization: `Bearer ${testData.token}` }
    });
    return response.data;
  });

  logResult('获取患者列表', time, success, success ? 
    `获取了 ${result.patients?.length || 0} 个患者` : 
    `获取失败: ${JSON.stringify(result?.error?.response?.data || {})}`);
}

/**
 * 测试获取患者详情
 */
async function testGetPatientDetails() {
  if (testData.patients.length === 0) {
    console.log(chalk.yellow('没有测试患者可供测试'));
    return;
  }

  const patientId = testData.patients[0];
  const { time, result, success } = await measureTime(async () => {
    const response = await axios.get(`${API_BASE_URL}/patients/${patientId}`, {
      headers: { Authorization: `Bearer ${testData.token}` }
    });
    return response.data;
  });

  logResult('获取患者详情', time, success, success ? 
    `患者ID: ${result.id}, 姓名: ${result.name}` : 
    `获取失败: ${JSON.stringify(result?.error?.response?.data || {})}`);
}

/**
 * 测试更新患者信息
 */
async function testUpdatePatient() {
  if (testData.patients.length === 0) {
    console.log(chalk.yellow('没有测试患者可供测试'));
    return;
  }

  const patientId = testData.patients[0];
  const updateData = {
    address: '北京市海淀区更新后的测试地址',
    pastMedicalHistory: '测试病史记录',
    lastVisitDate: new Date().toISOString().split('T')[0]
  };

  const { time, result, success } = await measureTime(async () => {
    const response = await axios.put(`${API_BASE_URL}/patients/${patientId}`, updateData, {
      headers: { Authorization: `Bearer ${testData.token}` }
    });
    return response.data;
  });

  logResult('更新患者信息', time, success, success ? 
    `更新成功: ${result.id}, 地址: ${updateData.address}` : 
    `更新失败: ${JSON.stringify(result?.error?.response?.data || {})}`);
}

/**
 * 清理测试数据
 */
async function cleanup() {
  console.log(chalk.cyan('\n开始清理测试数据...'));
  
  // 删除测试患者
  for (const patientId of testData.patients) {
    try {
      await axios.delete(`${API_BASE_URL}/patients/${patientId}`, {
        headers: { Authorization: `Bearer ${testData.token}` }
      });
      console.log(chalk.gray(`已删除患者: ${patientId}`));
    } catch (error) {
      console.log(chalk.red(`删除患者失败: ${patientId}`));
    }
  }
  
  console.log(chalk.green('测试数据清理完成'));
}

/**
 * 运行所有API测试
 */
async function runApiTests() {
  console.log(chalk.bgBlue.white('\n开始API性能测试\n'));
  
  try {
    // 用户注册与登录测试
    await testRegister();
    await testLogin();
    await testGetProfile();
    
    // 患者管理测试
    await testAddPatients(3);
    await testGetPatients();
    await testGetPatientDetails();
    await testUpdatePatient();
    
    // 清理测试数据
    await cleanup();
    
    console.log(chalk.bgGreen.black('\n测试完成\n'));
  } catch (error) {
    console.error(chalk.bgRed.white('\n测试过程中出现未处理的错误:'));
    console.error(error);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  runApiTests();
}

module.exports = { runApiTests }; 