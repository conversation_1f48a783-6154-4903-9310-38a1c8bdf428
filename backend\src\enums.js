/**
 * 系统枚举类型定义文件
 * 所有枚举值必须与数据库迁移文件和模型定义保持一致
 */

// 角色枚举
const Role = {
  ADMIN: 'ADMIN',      // 管理员
  SERVICE: 'SERVICE',  // 服务人员
  USER: 'USER'         // 普通用户
};

// 用户等级枚举
const UserLevel = {
  PERSONAL: 'PERSONAL',         // 个人用户
  FAMILY: 'FAMILY',             // 家庭用户
  PROFESSIONAL: 'PROFESSIONAL'  // 专业用户
};

// 记录类型枚举
const RecordTypeEnum = {
  SELF_DESCRIPTION: 'SELF_DESCRIPTION', // 自述
  SYMPTOM: 'SYMPTOM',                   // 症状
  EXAMINATION: 'EXAMINATION',           // 检查
  LAB_TEST: 'LAB_TEST',                 // 实验室检查
  DIAGNOSIS: 'DIAGNOSIS',               // 诊断
  TREATMENT: 'TREATMENT',               // 治疗
  HOSPITALIZATION: 'HOSPITALIZATION',   // 住院
  MEDICATION: 'MEDICATION',             // 用药
  SURGERY: 'SURGERY',                   // 手术
  MONITORING: 'MONITORING',             // 监测
  PHYSICAL_THERAPY: 'PHYSICAL_THERAPY', // 物理治疗
  DISCHARGE: 'DISCHARGE',               // 出院
  APPOINTMENT: 'APPOINTMENT',           // 预约
  REPORT: 'REPORT',                     // 报告
  FOLLOW_UP: 'FOLLOW_UP',               // 随访
  PROGNOSIS: 'PROGNOSIS',               // 预后
  AUX_DIAGNOSIS: 'AUX_DIAGNOSIS',       // 辅助诊断
  NURSING: 'NURSING',                   // 护理
  REVISIT: 'REVISIT',                   // 复诊
  REFERRAL: 'REFERRAL',                 // 转诊
  PSYCHOLOGY: 'PSYCHOLOGY',             // 心理
  REHABILITATION: 'REHABILITATION',     // 康复
  ASSESSMENT: 'ASSESSMENT',             // 评估
  OTHER: 'OTHER'                        // 其他
};

// 阶段枚举
const StagePhaseEnum = {
  INITIAL: 'INITIAL',       // 初诊期
  DIAGNOSIS: 'DIAGNOSIS',   // 确诊期
  TREATMENT: 'TREATMENT',   // 治疗期
  RECOVERY: 'RECOVERY',     // 恢复期
  PROGNOSIS: 'PROGNOSIS'    // 预后期
};

// 阶段节点枚举
const StageNodeEnum = {
  INITIAL_VISIT: 'INITIAL_VISIT', // 发病
  DIAGNOSIS: 'DIAGNOSIS',         // 确诊
  TREATMENT: 'TREATMENT',         // 治疗
  FOLLOW_UP: 'FOLLOW_UP',         // 随访
  PROGNOSIS: 'PROGNOSIS',         // 预后
  ARCHIVE: 'ARCHIVE'              // 归档
};

// 严重程度枚举
const SeverityEnum = {
  MILD: 'MILD',           // 轻微
  MODERATE: 'MODERATE',   // 中等
  SEVERE: 'SEVERE',       // 严重
  CRITICAL: 'CRITICAL'    // 危重
};

// 性别枚举
const Gender = {
  MALE: 'MALE',     // 男
  FEMALE: 'FEMALE'  // 女
};

// 附件类型枚举
const AttachmentTypeEnum = {
  PDF: 'PDF',   // PDF文件
  JPG: 'JPG',   // JPG图片
  PNG: 'PNG',   // PNG图片
  TXT: 'TXT'    // 文本文件
};

// 血型枚举
const BloodType = {
  A: 'A',             // A型
  B: 'B',             // B型
  AB: 'AB',           // AB型
  O: 'O',             // O型
  UNKNOWN: 'UNKNOWN'  // 未知
};

// 疾病阶段枚举 (与StagePhaseEnum相同，但为了保持一致性单独列出)
const DiseaseStage = {
  INITIAL: 'INITIAL',       // 初诊期
  DIAGNOSIS: 'DIAGNOSIS',   // 确诊期
  TREATMENT: 'TREATMENT',   // 治疗期
  RECOVERY: 'RECOVERY',     // 恢复期
  PROGNOSIS: 'PROGNOSIS'    // 预后期
};

// 疾病状态枚举
const DiseaseStatus = {
  ACTIVE: 'ACTIVE',       // 活跃
  INACTIVE: 'INACTIVE',   // 非活跃
  CURED: 'CURED'          // 已治愈
};

module.exports = {
  Role,
  UserLevel,
  RecordTypeEnum,
  StagePhaseEnum,
  StageNodeEnum,
  SeverityEnum,
  Gender,
  AttachmentTypeEnum,
  BloodType,
  DiseaseStage,
  DiseaseStatus
}; 