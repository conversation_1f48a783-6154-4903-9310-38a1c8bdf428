import React, { useState } from 'react';
import { 
  Box, 
  Chip, 
  FormControl,
  FormHelperText,
  Typography,
  Divider,
  Tooltip,
  Button
} from '@mui/material';
import { 
  RecordTypeEnum, 
  RECORD_TYPE_LABELS, 
  RECORD_TYPE_GROUPS, 
  RECORD_TYPE_DESCRIPTIONS 
} from '../../types/recordEnums';
import { Refresh as RefreshIcon } from '@mui/icons-material';

// 记录类型分组标题
const GROUP_TITLES = [
  '常用医疗记录',
  '就诊与住院记录',
  '康复与专科记录',
  '其他记录类型'
];

// 最大选择数量
const MAX_SELECTION = 4;

interface TypeTagSelectorProps {
  selectedTypes: RecordTypeEnum[];
  onChange: (types: RecordTypeEnum[]) => void;
  primaryType: RecordTypeEnum | null;
  onPrimaryTypeChange: (type: RecordTypeEnum) => void;
  error?: string;
  helperText?: string;
  compact?: boolean; // 是否使用紧凑模式
}

/**
 * 记录类型标签选择器组件
 * 显示分组的记录类型标签，支持选择/取消选择和设置主要类型
 * 最多可选择4个类型，第一个选择的为主要类型(辅色)，其余为主色
 */
const TypeTagSelector: React.FC<TypeTagSelectorProps> = ({
  selectedTypes,
  onChange,
  primaryType,
  onPrimaryTypeChange,
  error,
  helperText,
  compact = false
}) => {
  const [hoverType, setHoverType] = useState<RecordTypeEnum | null>(null);

  // 处理标签点击事件
  const handleTagClick = (type: RecordTypeEnum) => {
    // 已选中的情况：切换为主要类型
    if (selectedTypes.includes(type)) {
      // 如果已经是主要类型，不做任何操作
      if (primaryType === type) return;
      
      // 否则设置为主要类型
      onPrimaryTypeChange(type);
    } 
    // 未选中但已达到最大选择数的情况：替换主要类型
    else if (selectedTypes.length >= MAX_SELECTION) {
      // 保持总数量不变，替换主要类型
      const newTypes = [...selectedTypes.filter((_, index) => index !== 0), type];
      onChange(newTypes);
      onPrimaryTypeChange(type);
    } 
    // 未选中且未达到最大选择数的情况：添加到已选列表
    else {
      const newTypes = [...selectedTypes, type];
      onChange(newTypes);
      
      // 如果这是第一个选择的类型，设置为主要类型
      if (selectedTypes.length === 0) {
        onPrimaryTypeChange(type);
      }
    }
  };

  // 双击取消选择
  const handleDoubleClick = (type: RecordTypeEnum) => {
    // 如果未选中，忽略
    if (!selectedTypes.includes(type)) return;
    
    // 从已选中列表中移除
    const newTypes = selectedTypes.filter(t => t !== type);
    onChange(newTypes);
    
    // 如果删除的是主要类型，需要更新主要类型
    if (primaryType === type && newTypes.length > 0) {
      onPrimaryTypeChange(newTypes[0]);
    } else if (newTypes.length === 0) {
      // 如果没有选中的类型，清空主要类型
      onPrimaryTypeChange(null as any);
    }
  };
  
  // 重置所有选择
  const handleReset = () => {
    // 清空所有选择
    onChange([]);
    onPrimaryTypeChange(null as any);
    
    // 触发自定义事件，通知父组件重置标题和内容
    const resetEvent = new CustomEvent('recordTypeReset', {
      bubbles: true,
      detail: { action: 'reset' }
    });
    document.dispatchEvent(resetEvent);
  };

  // 获取记录类型的显示名称
  const getTypeDisplayName = (type: RecordTypeEnum): string => {
    return RECORD_TYPE_LABELS[type];
  };

  return (
    <FormControl fullWidth error={!!error}>
      <Box sx={{ mb: compact ? 0.5 : 1, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        {/* <Typography variant={compact ? "subtitle2" : "subtitle1"} sx={{ fontWeight: 'normal' }}>
          记录类型
        </Typography> */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexGrow: 1, justifyContent: 'flex-end' }}>
          {selectedTypes.length > 0 && (
            <Typography variant="caption" sx={{ color: 'text.secondary' }}>
              已选: {selectedTypes.length}/{MAX_SELECTION}
            </Typography>
          )}
          {selectedTypes.length > 0 && (
            <Button 
              variant="text" 
              color="primary" 
              size="small" 
              startIcon={<RefreshIcon fontSize="small" />}
              onClick={handleReset}
              sx={{ minWidth: 'auto', p: '2px 8px', fontSize: '0.75rem' }}
            >
              重置
            </Button>
          )}
        </Box>
      </Box>
      
      <Box sx={{ 
        p: compact ? 0.8 : 0.6, // 减小内边距，让容器有更多可用空间
        mb: 1, 
        backgroundColor: 'background.paper',
        mx: -1 // 整体向外扩展，利用更多空间
      }}>
        {RECORD_TYPE_GROUPS.map((group, groupIndex) => (
          <Box key={groupIndex} sx={{ mb: groupIndex < RECORD_TYPE_GROUPS.length - 1 ? 0.8 : 0 }}>
            {!compact && (
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.4, px: 0.5 }}>
                <Typography 
                  variant="caption" 
                  component="div" 
                  sx={{ 
                    color: 'text.secondary', 
                    fontWeight: 500,
                    fontSize: '0.65rem',
                    backgroundColor: 'action.hover',
                    px: 0.8,
                    py: 0.1,
                    borderRadius: 1
                  }}
                >
                  {GROUP_TITLES[groupIndex]}
                </Typography>
                <Divider sx={{ ml: 1, flexGrow: 1 }} />
              </Box>
            )}
            
            <Box sx={{ 
              display: 'flex', 
              flexWrap: 'wrap', 
              gap: 0.2, // 更小的间距
              mb: 0.4,
              width: '100%', // 使用全宽
              justifyContent: 'space-between' // 平均分配空间
            }}>
              {group.map((type) => {
                const isSelected = selectedTypes.includes(type);
                const isPrimary = primaryType === type;
                const displayName = getTypeDisplayName(type);
                
                return (
                  <Box key={type.toString()} sx={{ 
                    width: 'calc(16.66% - 2px)', // 确保每行6个，减去一点空间给margin
                    mb: 0.2 
                  }}>
                    <Tooltip
                      title={
                        <Box>
                          <Typography variant="subtitle2">{displayName}</Typography>
                          <Typography variant="body2">{RECORD_TYPE_DESCRIPTIONS[type]}</Typography>
                          {isSelected && (
                            <Typography variant="caption" sx={{ display: 'block', mt: 0.5 }}>
                              {isPrimary 
                                ? '✓ 当前为主要类型' 
                                : '点击设为主要类型，双击取消选择'}
                            </Typography>
                          )}
                          {!isSelected && (
                            <Typography variant="caption" sx={{ display: 'block', mt: 0.5 }}>
                              {selectedTypes.length >= MAX_SELECTION 
                                ? '点击替换主要类型' 
                                : '点击选择此类型'}
                            </Typography>
                          )}
                        </Box>
                      }
                      placement="top"
                      arrow
                    >
                      <Chip
                        label={displayName}
                        size="small"
                        onClick={() => handleTagClick(type)}
                        onDoubleClick={() => handleDoubleClick(type)}
                        onMouseEnter={() => setHoverType(type)}
                        onMouseLeave={() => setHoverType(null)}
                        color={isSelected ? (isPrimary ? 'secondary' : 'primary') : 'default'}
                        variant={isSelected ? 'filled' : 'outlined'}
                        sx={{
                          m: 0,
                          height: '24px', // 减小高度
                          width: '100%', // 使标签占满容器宽度
                          cursor: 'pointer',
                          borderColor: isSelected ? undefined : 'divider',
                          ...(hoverType === type && !isSelected ? {
                            borderColor: 'primary.main',
                            backgroundColor: 'action.hover'
                          } : {}),
                          '& .MuiChip-label': {
                            px: 0.5, // 减小内部填充
                            fontSize: '0.68rem', // 减小字体大小
                            lineHeight: 1.2, // 减小行高
                            width: '100%', // 文字占满可用空间
                            textAlign: 'center' // 文字居中
                          }
                        }}
                      />
                    </Tooltip>
                  </Box>
                );
              })}
            </Box>
          </Box>
        ))}
      </Box>
      
      {/* 已选标签摘要 - 仅在有选择且紧凑模式时显示 */}
      {compact && selectedTypes.length > 0 && (
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 1 }}>
          {selectedTypes.map((type) => (
            <Chip
              key={type}
              label={getTypeDisplayName(type)}
              size="small"
              color={primaryType === type ? 'secondary' : 'primary'}
              onDelete={() => handleDoubleClick(type)}
              onClick={() => onPrimaryTypeChange(type)}
              sx={{ fontSize: '0.7rem' }}
            />
          ))}
        </Box>
      )}
      
      {/* 帮助文本 */}
      {(helperText || error) && (
        <FormHelperText sx={{ 
          m: 0, 
          fontSize: '0.6rem', 
          lineHeight: 1.1, 
          opacity: 0.85,
          px: 0.5,
          color: error ? 'error.main' : 'text.secondary' 
        }}>
          {error || helperText}
        </FormHelperText>
      )}
    </FormControl>
  );
};

export default TypeTagSelector; 