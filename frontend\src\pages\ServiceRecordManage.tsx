import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  TableCell,
  TableRow,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Alert,
  Tooltip,
  Tabs,
  Tab,
  Breadcrumbs
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import VisibilityIcon from '@mui/icons-material/Visibility';
import RefreshIcon from '@mui/icons-material/Refresh';
import { Link } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import { useServiceUserContext } from '../context/ServiceUserContext';
import serviceRecordService from '../services/serviceRecordService';
import ServiceContextBar from '../components/service/ServiceContextBar';
import ServiceContextSelector from '../components/service/ServiceContextSelector';
import ServiceItemList from '../components/common/ServiceItemList';
import TabPanel from '../components/common/TabPanel';
import { useServiceContext } from '../hooks/useServiceContext';
import { ServiceRecord, RecordType, PrimaryType, RecordStatus } from '../types/serviceTypes';
import { logError } from '../utils/errorHandler';

// 添加日期格式化函数
const formatDate = (dateString?: string): string => {
  if (!dateString) return '未知';
  
  try {
    return new Date(dateString).toLocaleDateString();
  } catch (e) {
    return '日期错误';
  }
};

// 服务记录管理页面组件
const ServiceRecordManage: React.FC = () => {
  const navigate = useNavigate();
  const serviceContext = useServiceUserContext();
  const { user } = useAuthStore();
  // 使用自定义Hook管理服务上下文
  const { 
    showContextSelector, 
    setShowContextSelector, 
    error, 
    setError, 
    isValidContext
  } = useServiceContext();

  const [tabValue, setTabValue] = useState(0);
  const [records, setRecords] = useState<ServiceRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<ServiceRecord | null>(null);
  
  // 加载服务记录数据，使用useCallback包装
  const loadRecordsData = useCallback(async () => {
    if (!serviceContext.authorizationId) return;
    
    setLoading(true);
    setError('');
    try {
      const response = await serviceRecordService.getServiceRecordsByAuth(serviceContext.authorizationId);
      if (response.success) {
        // 如果有选择患者，则过滤出与该患者相关的记录
        const filteredRecords = serviceContext.patientId 
          ? response.data.filter((record: ServiceRecord) => record.patientId === serviceContext.patientId) 
          : response.data;
        
        setRecords(filteredRecords);
      }
    } catch (err) {
      logError(err, '加载服务记录');
      setError('加载服务记录失败');
    } finally {
      setLoading(false);
    }
  }, [serviceContext.authorizationId, serviceContext.patientId, setError]);

  // 首次加载
  useEffect(() => {
    if (serviceContext.authorizationId && serviceContext.patientId) {
      loadRecordsData();
    }
  }, [serviceContext.authorizationId, serviceContext.patientId, loadRecordsData]);

  // 切换标签
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // 打开创建记录对话框
  const handleOpenCreateDialog = () => {
    navigate('/service-records/create');
  };

  // 打开编辑记录对话框
  const handleOpenEditDialog = (record: ServiceRecord) => {
    navigate(`/service-records/${record.id}/edit`);
  };

  // 打开删除记录对话框
  const handleOpenDeleteDialog = (record: ServiceRecord) => {
    setSelectedRecord(record);
    setOpenDeleteDialog(true);
  };

  // 关闭删除记录对话框
  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    setSelectedRecord(null);
  };

  // 删除记录
  const handleDeleteRecord = async () => {
    if (!selectedRecord) return;

    try {
      const response = await serviceRecordService.deleteServiceRecord(selectedRecord.id);
      if (response.success) {
        setOpenDeleteDialog(false);
        loadRecordsData();
      }
    } catch (err) {
      logError(err, '删除服务记录');
      setError('删除记录失败');
    }
  };

  // 渲染记录状态
  const renderRecordStatus = (status: RecordStatus) => {
    switch (status) {
      case 'ACTIVE':
        return <Chip label="活跃" color="success" size="small" />;
      case 'INACTIVE':
        return <Chip label="不活跃" color="default" size="small" />;
      case 'DELETED':
        return <Chip label="已删除" color="error" size="small" />;
      default:
        return <Chip label="未知状态" color="default" size="small" />;
    }
  };

  // 渲染记录类型
  const renderRecordType = (type: RecordType) => {
    switch (type) {
      case 'MEDICAL_RECORD':
        return <Chip label="病历" color="primary" size="small" variant="outlined" />;
      case 'LAB_RESULT':
        return <Chip label="化验结果" color="secondary" size="small" variant="outlined" />;
      case 'PRESCRIPTION':
        return <Chip label="处方" color="info" size="small" variant="outlined" />;
      case 'SURGERY':
        return <Chip label="手术记录" color="warning" size="small" variant="outlined" />;
      case 'CHECKUP':
        return <Chip label="体检" color="success" size="small" variant="outlined" />;
      default:
        return <Chip label="其他" color="default" size="small" variant="outlined" />;
    }
  };

  // 验证服务用户是否可以操作记录
  const canManageRecord = (record: ServiceRecord): boolean => {
    // 只有创建者或拥有FULL权限的服务用户才能编辑/删除记录
    if (!user) return false;
    
    return (
      record.serviceUserId === user.id || 
      serviceContext.privacyLevel === 'FULL'
    );
  };

  // 列定义
  const columns = [
    { id: 'title', label: '标题', minWidth: 200 },
    { id: 'recordType', label: '类型', minWidth: 120 },
    { id: 'createdAt', label: '创建时间', minWidth: 120 },
    { id: 'status', label: '状态', minWidth: 100 },
    { id: 'createdBy', label: '创建者', minWidth: 120 },
    { id: 'actions', label: '操作', minWidth: 150, align: 'right' as const }
  ];
  
  // 渲染表格行
  const renderRow = (record: ServiceRecord, index: number) => (
    <TableRow key={record.id} hover>
      <TableCell>
        <Tooltip title={record.title}>
          <Typography noWrap sx={{ maxWidth: 200 }}>
            {record.title}
          </Typography>
        </Tooltip>
      </TableCell>
      <TableCell>{renderRecordType(record.recordType as RecordType)}</TableCell>
      <TableCell>{formatDate(record.createdAt)}</TableCell>
      <TableCell>{renderRecordStatus(record.status as RecordStatus)}</TableCell>
      <TableCell>
        {record.serviceUser?.username || '未知'}
      </TableCell>
      <TableCell align="right">
        <Tooltip title="查看详情">
          <IconButton 
            size="small" 
            color="primary"
            onClick={() => navigate(`/service-records/${record.id}`)}
          >
            <VisibilityIcon fontSize="small" />
          </IconButton>
        </Tooltip>
        
        {canManageRecord(record) && (
          <>
            <Tooltip title="编辑">
              <IconButton 
                size="small" 
                color="primary"
                onClick={(e) => {
                  e.stopPropagation();
                  handleOpenEditDialog(record);
                }}
              >
                <EditIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            
            <Tooltip title="删除">
              <IconButton 
                size="small" 
                color="error"
                onClick={(e) => {
                  e.stopPropagation();
                  handleOpenDeleteDialog(record);
                }}
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </>
        )}
      </TableCell>
    </TableRow>
  );

  // 渲染权限级别提示
  const renderPrivacyLevelAlert = () => {
    if (!serviceContext.privacyLevel) return null;
    
    let alertSeverity: 'info' | 'warning' | 'success' | 'error' = 'info';
    let alertMessage = '';
    
    switch (serviceContext.privacyLevel) {
      case 'BASIC':
        alertSeverity = 'warning';
        alertMessage = '您当前拥有基础授权级别，只能查看授权信息，无法访问或操作记录。';
        break;
      case 'STANDARD':
        alertSeverity = 'info';
        alertMessage = '您当前拥有标准授权级别，可以创建和编辑自己的记录，但只能查看授权用户的记录。';
        break;
      case 'FULL':
        alertSeverity = 'success';
        alertMessage = '您当前拥有完整授权级别，可以读写授权用户和自己创建的所有记录。';
        break;
    }
    
    return (
      <Alert severity={alertSeverity} sx={{ mb: 2 }}>
        {alertMessage}
      </Alert>
    );
  };

  return (
    <Box>
      {/* 面包屑导航 */}
      <Breadcrumbs sx={{ mb: 2 }}>
        <Link to="/service-authorizations" style={{ textDecoration: 'none', color: 'inherit' }}>
          服务授权
        </Link>
        <Typography color="text.primary">服务记录管理</Typography>
      </Breadcrumbs>
      
      {/* 服务上下文状态栏 */}
      <ServiceContextBar
        onSelectContext={() => setShowContextSelector(true)}
      />
      
      {/* 上下文选择器 */}
      {showContextSelector && (
        <ServiceContextSelector
          onComplete={() => {
            setShowContextSelector(false);
            if (serviceContext.authorizationId && serviceContext.patientId) {
              loadRecordsData();
            }
          }}
        />
      )}
      
      {/* 页面标题和操作按钮 */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5">服务记录管理</Typography>
        <Box>
          <Button 
            variant="contained" 
            color="primary" 
            startIcon={<AddIcon />} 
            onClick={handleOpenCreateDialog}
            disabled={serviceContext.privacyLevel === 'BASIC' || loading}
            sx={{ mr: 1 }}
          >
            新建记录
          </Button>
          <Button 
            startIcon={<RefreshIcon />} 
            variant="outlined" 
            onClick={loadRecordsData}
            disabled={loading || !isValidContext}
          >
            刷新
          </Button>
        </Box>
      </Box>
      
      {/* 提示信息 */}
      {(!serviceContext.authorizationId || !serviceContext.patientId) && !showContextSelector && (
        <Alert severity="info" sx={{ mb: 2 }}>
          请先选择授权患者，才能查看和管理服务记录
        </Alert>
      )}
      
      {/* 错误提示 */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}
      
      {/* 权限级别提示 */}
      {renderPrivacyLevelAlert()}
      
      {/* 标签导航 */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
        <Tabs value={tabValue} onChange={handleTabChange}>
          <Tab label="所有记录" />
          <Tab label="我创建的" />
        </Tabs>
      </Box>
      
      {/* 记录列表 */}
      <TabPanel value={tabValue} index={0} id="service-record">
        <ServiceItemList
          columns={columns}
          data={records}
          loading={loading}
          error={error}
          emptyMessage="暂无记录数据"
          contextValid={Boolean(isValidContext)}
          renderRow={renderRow}
        />
      </TabPanel>
      
      <TabPanel value={tabValue} index={1} id="my-service-record">
        <ServiceItemList
          columns={columns}
          data={records.filter(record => record.serviceUserId === user?.id)}
          loading={loading}
          error={error}
          emptyMessage="暂无您创建的记录"
          contextValid={Boolean(isValidContext)}
          renderRow={renderRow}
        />
      </TabPanel>
      
      {/* 删除记录确认对话框 */}
      <Dialog open={openDeleteDialog} onClose={handleCloseDeleteDialog}>
        <DialogTitle>确认删除</DialogTitle>
        <DialogContent>
          <Typography>
            您确定要删除这条记录吗？此操作不可逆。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog}>取消</Button>
          <Button 
            onClick={handleDeleteRecord} 
            variant="contained" 
            color="error"
          >
            删除
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ServiceRecordManage; 