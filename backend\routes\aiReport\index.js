/**
 * AI报告相关路由
 */
const express = require('express');
const router = express.Router();
const aiReportController = require('../../controllers/aiReport/aiReportController');
const { isAuthenticated } = require('../../src/middleware/auth');

// 所有路由都需要身份验证
router.use(isAuthenticated);

// 获取指定病理的所有AI报告
router.get('/ai-reports', aiReportController.getAIReports);

// 检查用户配额（必须放在/:reportId路由之前）
router.get('/ai-reports/quota', aiReportController.checkUserQuota);

// 获取报告显示配置（必须放在/:reportId路由之前）
router.get('/ai-reports/config', aiReportController.getReportConfig);

// 保存报告显示配置（必须放在/:reportId路由之前）
router.post('/ai-reports/config', aiReportController.saveReportConfig);

// 获取单个AI报告详情
router.get('/ai-reports/:reportId', aiReportController.getAIReport);

// 下载AI报告PDF
router.options('/ai-reports/:reportId/pdf', (req, res) => {
  // 明确允许前端域名，不使用通配符
  const origin = req.headers.origin;
  res.header('Access-Control-Allow-Origin', origin);  // 允许特定的请求源
  res.header('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Expose-Headers', 'Content-Disposition, Content-Type');
  res.status(200).send();
});

// 确保PDF下载路由单独添加CORS处理，解决下载文件时的跨域问题
router.get('/ai-reports/:reportId/pdf', (req, res, next) => {
  // 明确允许前端域名，不使用通配符
  const origin = req.headers.origin;
  res.header('Access-Control-Allow-Origin', origin);  // 允许特定的请求源
  res.header('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Expose-Headers', 'Content-Disposition, Content-Type');
  
  // 继续处理请求
  next();
}, aiReportController.downloadPDF);

// 创建新的AI报告
router.post('/ai-reports', aiReportController.createAIReport);

// 删除AI报告
router.delete('/ai-reports/:reportId', aiReportController.deleteAIReport);

module.exports = router; 