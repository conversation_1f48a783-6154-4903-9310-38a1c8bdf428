/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.raw(`
    -- 禁用外键约束
    -- PRAGMA foreign_keys = OFF; -- Removed for PostgreSQL compatibility
    
    -- 创建临时表，确保包含PAUSED状态
    CREATE TABLE "temp_user_authorizations" (
      "id" uuid PRIMARY KEY,
      "authorizer_id" uuid NOT NULL,
      "authorized_id" uuid NOT NULL,
      "status" varchar(255) NOT NULL CHECK ("status" IN ('PENDING_AUTHORIZER', 'PENDING_AUTHORIZED', 'ACTIVE', 'REVOKED', 'PAUSED')),
      "privacy_level" varchar(255) NOT NULL,
      "patient_id" uuid,
      "created_by" uuid NOT NULL,
      "has_new_notification" boolean NOT NULL DEFAULT false,
      "created_at" timestamp with time zone NOT NULL,
      "updated_at" timestamp with time zone NOT NULL,
      "activated_at" timestamp with time zone,
      "revoked_at" timestamp with time zone,
      "authorizer_switch" boolean NOT NULL DEFAULT true,
      "authorized_switch" boolean NOT NULL DEFAULT false,
      "status_changed_at" timestamp with time zone,
      FOREIGN KEY ("authorizer_id") REFERENCES "users" ("id"),
      FOREIGN KEY ("authorized_id") REFERENCES "users" ("id"),
      FOREIGN KEY ("patient_id") REFERENCES "patients" ("id"),
      FOREIGN KEY ("created_by") REFERENCES "users" ("id"),
      UNIQUE ("authorizer_id", "authorized_id", "patient_id")
    );
    
    -- 复制数据
    INSERT INTO "temp_user_authorizations" SELECT * FROM "user_authorizations";
    
    -- 删除旧表
    DROP TABLE "user_authorizations";
    
    -- 重命名新表
    ALTER TABLE "temp_user_authorizations" RENAME TO "user_authorizations";
    
    -- 重建索引
    CREATE INDEX "user_authorizations_authorizer_id_index" ON "user_authorizations" ("authorizer_id");
    CREATE INDEX "user_authorizations_authorized_id_index" ON "user_authorizations" ("authorized_id");
    CREATE INDEX "user_authorizations_status_index" ON "user_authorizations" ("status");
    
    -- 重新启用外键约束
    -- PRAGMA foreign_keys = ON; -- Removed for PostgreSQL compatibility
  `);
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function(knex) {
  // 检查表是否存在
  const tableExists = await knex.schema.hasTable('user_authorizations');
  if (!tableExists) {
    console.log('表 user_authorizations 不存在，跳过回滚操作');
    return;
  }
  
  // 表存在时才执行回滚
  return knex.schema.raw(`
    -- 禁用外键约束
    -- PRAGMA foreign_keys = OFF; -- Removed for PostgreSQL compatibility
    
    -- 创建临时表，恢复原始约束
    CREATE TABLE "temp_user_authorizations" (
      "id" uuid PRIMARY KEY,
      "authorizer_id" uuid NOT NULL,
      "authorized_id" uuid NOT NULL,
      "status" varchar(255) NOT NULL CHECK ("status" IN ('PENDING_AUTHORIZER', 'PENDING_AUTHORIZED', 'ACTIVE', 'REVOKED')),
      "privacy_level" varchar(255) NOT NULL,
      "patient_id" uuid,
      "created_by" uuid NOT NULL,
      "has_new_notification" boolean NOT NULL DEFAULT false,
      "created_at" timestamp with time zone NOT NULL,
      "updated_at" timestamp with time zone NOT NULL,
      "activated_at" timestamp with time zone,
      "revoked_at" timestamp with time zone,
      "authorizer_switch" boolean NOT NULL DEFAULT true,
      "authorized_switch" boolean NOT NULL DEFAULT false,
      "status_changed_at" timestamp with time zone,
      FOREIGN KEY ("authorizer_id") REFERENCES "users" ("id"),
      FOREIGN KEY ("authorized_id") REFERENCES "users" ("id"),
      FOREIGN KEY ("patient_id") REFERENCES "patients" ("id"),
      FOREIGN KEY ("created_by") REFERENCES "users" ("id"),
      UNIQUE ("authorizer_id", "authorized_id", "patient_id")
    );
    
    -- 将非PAUSED数据复制到新表
    INSERT INTO "temp_user_authorizations" SELECT * FROM "user_authorizations" WHERE status != 'PAUSED';
    
    -- 删除旧表
    DROP TABLE "user_authorizations";
    
    -- 重命名新表
    ALTER TABLE "temp_user_authorizations" RENAME TO "user_authorizations";
    
    -- 重建索引
    CREATE INDEX "user_authorizations_authorizer_id_index" ON "user_authorizations" ("authorizer_id");
    CREATE INDEX "user_authorizations_authorized_id_index" ON "user_authorizations" ("authorized_id");
    CREATE INDEX "user_authorizations_status_index" ON "user_authorizations" ("status");
    
    -- 重新启用外键约束
    -- PRAGMA foreign_keys = ON; -- Removed for PostgreSQL compatibility
  `);
};