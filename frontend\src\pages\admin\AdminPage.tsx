import React, { useEffect } from 'react';
import { Box, Typography, Paper } from '@mui/material';
import { useNavigate, useLocation } from 'react-router-dom';
import AdminRoutes from './AdminRoutes';
import { useAuthStore } from '../../store/authStore';

/**
 * 系统管理主页面
 * 作为所有管理模块的容器
 */
const AdminPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuthStore();
  
  // 检查用户是否是管理员
  useEffect(() => {
    if (user && user.role !== 'ADMIN') {
      // 如果不是管理员，跳转到首页
      navigate('/dashboard', { replace: true });
    }
  }, [user, navigate]);
  
  // 如果用户未加载或不是管理员，显示加载中
  if (!user || user.role !== 'ADMIN') {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography>验证权限中...</Typography>
      </Box>
    );
  }
  
  return (
    <Box sx={{ p: { xs: 2, md: 3 } }}>
      <Paper 
        elevation={0} 
        sx={{ 
          p: { xs: 2, md: 3 },
          borderRadius: 2,
          bgcolor: 'background.default'
        }}
      >
        <AdminRoutes />
      </Paper>
    </Box>
  );
};

export default AdminPage; 