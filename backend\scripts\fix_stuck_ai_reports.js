/**
 * 修复卡在PROCESSING状态的AI报告脚本
 * 将PROCESSING状态超过30分钟的报告修改为FAILED状态
 * 
 * 建议设置为定时任务每小时运行一次:
 * ```
 * 0 * * * * cd /path/to/backend && node scripts/fix_stuck_ai_reports.js >> logs/ai_reports_fix.log 2>&1
 * ```
 */
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// 配置
const STUCK_MINUTES = 30; // 超过多少分钟视为卡住
const LOG_DIR = path.join(__dirname, '../logs');
const LOG_FILE = path.join(LOG_DIR, 'ai_reports_fix.log');

// 确保日志目录存在
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
}

// 将日志同时写入控制台和文件
const logStream = fs.createWriteStream(LOG_FILE, { flags: 'a' });
const logDate = new Date().toISOString();
let logClosed = false;

// 自定义日志函数
function log(message) {
  const formattedMessage = `[${new Date().toISOString()}] ${message}`;
  console.log(formattedMessage);
  if (!logClosed) {
    logStream.write(formattedMessage + '\n');
  }
}

// 数据库路径
const dbPath = path.join(__dirname, '../db.sqlite');
log(`数据库路径: ${dbPath}`);

// 创建数据库连接
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    log(`连接数据库失败: ${err.message}`);
    closeLog();
    process.exit(1);
  }
  log('已连接到数据库');
});

// 使用Promise包装查询
function runQuery(query, params = []) {
  return new Promise((resolve, reject) => {
    db.run(query, params, function(err) {
      if (err) reject(err);
      else resolve(this);
    });
  });
}

function getAllQuery(query, params = []) {
  return new Promise((resolve, reject) => {
    db.all(query, params, function(err, rows) {
      if (err) reject(err);
      else resolve(rows);
    });
  });
}

// 主函数
async function main() {
  try {
    log('======== 开始检查卡住的AI报告 ========');
    
    // 计算30分钟前的时间戳
    const cutoffDate = new Date();
    cutoffDate.setMinutes(cutoffDate.getMinutes() - STUCK_MINUTES);
    const cutoffTimestamp = cutoffDate.toISOString();
    
    log(`检查截止时间: ${cutoffTimestamp} (${STUCK_MINUTES}分钟前)`);
    
    // 查找卡住的报告
    const stuckReports = await getAllQuery(`
      SELECT id, disease_id, patient_id, user_id, created_at, updated_at
      FROM ai_reports
      WHERE status = 'PROCESSING' AND updated_at < ?
      ORDER BY updated_at ASC
    `, [cutoffTimestamp]);
    
    if (stuckReports.length === 0) {
      log('没有发现卡住的报告');
      closeAll();
      return;
    }
    
    log(`发现${stuckReports.length}个卡住的报告:`);
    stuckReports.forEach((report, index) => {
      log(`[${index + 1}] ID: ${report.id}, 创建: ${report.created_at}, 更新: ${report.updated_at}`);
    });
    
    // 开始事务
    await runQuery('BEGIN TRANSACTION');
    
    // 修复卡住的报告
    const errorMessage = `处理超时，系统自动标记为失败状态。报告处理超过${STUCK_MINUTES}分钟未完成。`;
    const result = await runQuery(`
      UPDATE ai_reports 
      SET status = 'FAILED', 
          error_message = ?,
          updated_at = datetime('now')
      WHERE status = 'PROCESSING' AND updated_at < ?
    `, [errorMessage, cutoffTimestamp]);
    
    log(`成功更新${result.changes}个报告状态从PROCESSING到FAILED`);
    
    // 提交事务
    await runQuery('COMMIT');
    log('已提交所有更改');
    
  } catch (err) {
    log(`脚本执行失败: ${err.message}`);
    try {
      await runQuery('ROLLBACK');
      log('已回滚事务');
    } catch (rollbackErr) {
      log(`回滚失败: ${rollbackErr.message}`);
    }
  } finally {
    closeAll();
  }
}

// 关闭数据库连接和日志
function closeAll() {
  try {
    db.close((err) => {
      if (err) {
        console.error(`关闭数据库连接失败: ${err.message}`);
      } else {
        log('数据库连接已关闭');
      }
      // 在数据库关闭后关闭日志
      closeLog();
    });
  } catch (err) {
    console.error(`关闭过程中出错: ${err.message}`);
    closeLog();
  }
}

// 关闭日志流
function closeLog() {
  if (!logClosed) {
    logClosed = true;
    logStream.end();
  }
}

// 执行主函数
main().catch(err => {
  log(`主函数执行失败: ${err.message}`);
  closeAll();
}); 