/**
 * 标签路由管理
 * 提供标签的基本增删改查功能
 */
const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');
const { JWT_SECRET } = require('../src/config');
const knex = require('knex')(require('../knexfile').development);

// 身份验证中间件
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers.authorization;
  if (!authHeader) return res.status(401).json({ error: '未授权' });
  
  const token = authHeader.split(' ')[1];
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) return res.status(403).json({ error: '令牌无效' });
    req.user = user;
    next();
  });
};

// 获取用户的所有自定义标签
router.get('/user-tags', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    
    // 检查表是否存在
    const hasTagsTable = await knex.schema.hasTable('tags');
    if (!hasTagsTable) {
      // 如果表不存在，返回空数组
      console.log('tags表不存在，返回空数组');
      return res.json([]);
    }
    
    const tags = await knex('tags')
      .where('createdBy', userId)
      .andWhere('type', 'user')
      .orderBy('name');
    
    console.log(`找到${tags.length}个标签`);
    res.json(tags);
  } catch (error) {
    console.error('获取用户标签失败:', error);
    res.status(500).json({ error: error.message });
  }
});

// 创建新标签
router.post('/create', authenticateToken, async (req, res) => {
  try {
    const { name } = req.body;
    const userId = req.user.id;
    
    if (!name || !name.trim()) {
      return res.status(400).json({ error: '标签名称不能为空' });
    }
    
    // 检查表是否存在
    const hasTagsTable = await knex.schema.hasTable('tags');
    if (!hasTagsTable) {
      // 创建标签表
      await knex.schema.createTable('tags', table => {
        table.uuid('id').primary().defaultTo(knex.raw('(UUID())'));
        table.string('name').notNullable();
        table.string('color').defaultTo('#2196F3');
        table.string('description').nullable();
        table.string('type').defaultTo('user');
        table.uuid('createdBy').nullable();
        table.timestamp('createdAt').defaultTo(knex.fn.now());
        table.timestamp('updatedAt').defaultTo(knex.fn.now());
      });
      console.log('已创建tags表');
    }
    
    // 查找是否已存在相同标签
    const existingTag = await knex('tags')
      .where('name', name.trim())
      .andWhere('createdBy', userId)
      .andWhere('type', 'user')
      .first();
      
    if (existingTag) {
      return res.json(existingTag);
    }
    
    // 创建新标签
    const tagId = uuidv4();
    await knex('tags').insert({
      id: tagId,
      name: name.trim(),
      type: 'user',
      createdBy: userId,
      createdAt: knex.fn.now(),
      updatedAt: knex.fn.now()
    });
    
    const newTag = await knex('tags').where('id', tagId).first();
    console.log('已创建新标签:', newTag);
    
    res.status(201).json(newTag);
  } catch (error) {
    console.error('创建标签失败:', error);
    res.status(500).json({ error: error.message });
  }
});

// 删除标签
router.delete('/:tagId', authenticateToken, async (req, res) => {
  try {
    const { tagId } = req.params;
    const userId = req.user.id;
    
    // 确保只能删除自己的标签
    const tag = await knex('tags')
      .where('id', tagId)
      .andWhere('createdBy', userId)
      .andWhere('type', 'user')
      .first();
      
    if (!tag) {
      return res.status(404).json({ error: '标签不存在或无权删除' });
    }
    
    await knex('tags').where('id', tagId).delete();
    console.log('已删除标签:', tagId);
    
    res.json({ message: '标签已删除' });
  } catch (error) {
    console.error('删除标签失败:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router; 