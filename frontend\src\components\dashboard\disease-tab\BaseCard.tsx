import React, { ReactNode, useState } from 'react';
import { 
  Box, 
  Paper, 
  Typography, 
  CircularProgress,
  useTheme,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
} from '@mui/material';
import {
  Fullscreen as FullscreenIcon,
  Close as CloseIcon
} from '@mui/icons-material';

interface BaseCardProps {
  title: string;
  subTitle?: string;
  loading?: boolean;
  children: ReactNode;
  elevation?: number;
  headerAction?: ReactNode;
  sx?: any;
  minHeight?: number | string;
  noPadding?: boolean;
  allowFullscreen?: boolean; // 是否允许全屏显示
}

/**
 * 基础卡片组件
 * 用于创建统一风格的信息卡片，支持标题、加载状态和自定义内容
 */
const BaseCard: React.FC<BaseCardProps> = ({
  title,
  subTitle,
  loading = false,
  children,
  elevation = 0,
  headerAction,
  sx = {},
  minHeight,
  noPadding = false,
  allowFullscreen = false
}) => {
  const theme = useTheme();
  const [fullScreenMode, setFullScreenMode] = useState(false);
  
  // 切换全屏模式
  const toggleFullScreen = () => {
    setFullScreenMode(!fullScreenMode);
  };
  
  return (
    <>
      <Paper
        elevation={elevation}
        sx={{
          height: 'auto',
          borderRadius: 2,
          border: '1px solid',
          borderColor: theme.palette.divider,
          overflow: 'visible', // 允许内容溢出
          ...sx
        }}
      >
        {/* 卡片标题 */}
        <Box 
          sx={{ 
            p: 2, 
            borderBottom: loading ? 'none' : `1px solid ${theme.palette.divider}`,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}
        >
          <Box>
            <Typography variant="subtitle1" fontWeight={600}>
              {title}
            </Typography>
            {subTitle && (
              <Typography variant="body2" color="text.secondary" fontSize="0.75rem">
                {subTitle}
              </Typography>
            )}
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {allowFullscreen && (
              <IconButton 
                size="small" 
                onClick={toggleFullScreen}
                sx={{ mr: headerAction ? 1 : 0 }}
              >
                <FullscreenIcon fontSize="small" />
              </IconButton>
            )}
            {headerAction && (
              <Box>
                {headerAction}
              </Box>
            )}
          </Box>
        </Box>
        
        {/* 卡片内容 */}
        <Box 
          sx={{ 
            p: noPadding ? 0 : 2,
            minHeight: minHeight,
            position: 'relative',
            maxHeight: 'none', // 移除高度限制
            overflow: 'visible' // 确保内容溢出可见
          }}
        >
          {loading ? (
            <Box 
              sx={{ 
                display: 'flex', 
                justifyContent: 'center', 
                alignItems: 'center',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                bgcolor: 'rgba(255,255,255,0.7)',
                zIndex: 1
              }}
            >
              <CircularProgress size={30} />
            </Box>
          ) : null}
          
          {children}
        </Box>
      </Paper>
      
      {/* 全屏模式对话框 */}
      <Dialog
        open={fullScreenMode}
        onClose={toggleFullScreen}
        fullWidth
        maxWidth="md"
        PaperProps={{
          sx: {
            minHeight: '80vh',
            maxHeight: '90vh',
            overflowY: 'auto', // 确保溢出内容可滚动
            display: 'flex',
            flexDirection: 'column'
          }
        }}
      >
        <DialogTitle sx={{ 
          display: 'flex', 
          justifyContent: 'space-between',
          alignItems: 'center',
          borderBottom: `1px solid ${theme.palette.divider}`,
          position: 'sticky',  // 使标题栏固定在顶部
          top: 0,
          zIndex: 10
        }}>
          <Typography variant="h6" component="div">{title}</Typography>
          <IconButton onClick={toggleFullScreen} size="small">
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent 
          sx={{ 
            pt: 3,
            pb: 4, // 增加底部内边距
            px: 3, // 增加左右内边距
            overflowY: 'auto', // 确保内容可滚动
            flex: 1, // 填充剩余空间
            display: 'flex',
            flexDirection: 'column'
          }}
        >
          <Box sx={{ 
            flex: 1, 
            overflow: 'visible',
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-word'
          }}>
            {children}
          </Box>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default BaseCard; 