import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Chip, 
  alpha,
  useTheme, 
  styled,
  Tooltip
} from '@mui/material';
import { 
  Lock as PrivateIcon,
  Warning as WarningIcon,
  LocalHospital as TreatmentIcon,
} from '@mui/icons-material';
import styles from './DiseaseStageTimeline.module.css';

// 病程节点枚举
export enum StageNodeEnum {
  INITIAL_VISIT = 'INITIAL_VISIT',
  DIAGNOSIS = 'DIAGNOSIS',
  TREATMENT = 'TREATMENT',
  FOLLOW_UP = 'FOLLOW_UP',
  PROGNOSIS = 'PROGNOSIS',
  ARCHIVE = 'ARCHIVE'
}

// 节点中文名称映射
const NODE_NAMES: Record<string, string> = {
  'INITIAL_VISIT': '初诊',
  'DIAGNOSIS': '确诊',
  'TREATMENT': '治疗',
  'FOLLOW_UP': '随访',
  'PROGNOSIS': '预后',
  'ARCHIVE': '封档'
};

// 节点颜色映射
const NODE_COLORS: Record<string, string> = {
  'INITIAL_VISIT': '#E53935', // 红色 - 初诊
  'DIAGNOSIS': '#8E24AA', // 紫色 - 确诊
  'TREATMENT': '#FB8C00', // 橙色 - 治疗
  'FOLLOW_UP': '#FDD835', // 黄色 - 随访
  'PROGNOSIS': '#43A047', // 绿色 - 预后
  'ARCHIVE': '#757575'  // 灰色 - 归档
};

// 节点点样式组件
const StageNode = styled(Box, {
  shouldForwardProp: (prop) => 
    prop !== 'color' && prop !== 'isActive' && prop !== 'isCompleted'
})<{
  color: string;
  isActive?: boolean;
  isCompleted?: boolean;
}>(({ theme, color, isActive, isCompleted }) => ({
  width: isActive ? 24 : 20,
  height: isActive ? 24 : 20,
  borderRadius: '50%',
  backgroundColor: isActive ? color : (isCompleted ? alpha(color, 0.7) : theme.palette.grey[300]),
  border: `2px solid ${isActive ? color : (isCompleted ? color : theme.palette.grey[400])}`,
  position: 'relative',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  transition: 'all 0.3s ease',
  '&::after': isActive ? {
    content: '""',
    position: 'absolute',
    width: 8,
    height: 8,
    borderRadius: '50%',
    backgroundColor: theme.palette.common.white,
  } : undefined,
  zIndex: 2,
  boxShadow: isActive ? `0 0 8px ${alpha(color, 0.5)}` : 'none',
}));

// 连接线样式组件
const StageConnector = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'color' && prop !== 'isCompleted'
})<{
  color?: string;
  isCompleted?: boolean;
}>(({ theme, color, isCompleted }) => ({
  height: 3,
  flexGrow: 1,
  backgroundColor: isCompleted ? color : theme.palette.grey[300],
  transition: 'all 0.3s ease',
  zIndex: 1
}));

// 病程阶段记录类型
interface StageRecord {
  id: string;
  stage_node: string;
  recordDate: string | Date;
  title: string;
  is_important?: boolean;
  is_private?: boolean;
  stage_phase?: string;
}

// 组件属性接口
interface DiseaseStageTimelineProps {
  diseaseName: string;
  dayCount?: number;
  records: StageRecord[];
  isPrivate?: boolean;
  severity?: string;
  stageName?: string;
}

/**
 * 病理阶段时间线组件
 * 基于记录中的 stage_node 和 recordDate 信息绘制病程节点时间线
 */
const DiseaseStageTimeline: React.FC<DiseaseStageTimelineProps> = ({
  diseaseName,
  dayCount = 0,
  records = [],
  isPrivate = false,
  severity = '',
  stageName = ''
}) => {
  const theme = useTheme();
  const [processedStages, setProcessedStages] = useState<any[]>([]);
  
  // 处理记录，提取并排序病程节点
  useEffect(() => {
    if (!records || records.length === 0) {
      setProcessedStages([]);
      return;
    }

    // 按节点类型分组记录，并找出每种节点最早的记录
    const stageNodeMap = new Map<string, StageRecord>();
    
    // 标准化阶段顺序
    const nodeOrder = [
      StageNodeEnum.INITIAL_VISIT,
      StageNodeEnum.DIAGNOSIS, 
      StageNodeEnum.TREATMENT,
      StageNodeEnum.FOLLOW_UP,
      StageNodeEnum.PROGNOSIS,
      StageNodeEnum.ARCHIVE
    ];
    
    // 遍历记录，按节点类型分组
    records.forEach(record => {
      if (!record.stage_node) return;
      
      const nodeType = record.stage_node;
      const recordDate = record.recordDate instanceof Date ? 
        record.recordDate : new Date(record.recordDate);
      
      // 检查这个节点类型是否已有记录，如果有则比较日期，保留最早的
      if (stageNodeMap.has(nodeType)) {
        const existingRecord = stageNodeMap.get(nodeType)!;
        const existingDate = existingRecord.recordDate instanceof Date ?
          existingRecord.recordDate : new Date(existingRecord.recordDate);
        
        // 如果新记录日期更早，则替换
        if (recordDate < existingDate) {
          stageNodeMap.set(nodeType, record);
        }
      } else {
        // 第一次遇到这个节点类型
        stageNodeMap.set(nodeType, record);
      }
    });
    
    // 根据标准顺序创建处理后的阶段数组
    const stages = nodeOrder
      .filter(node => stageNodeMap.has(node))
      .map((node, index) => {
        const record = stageNodeMap.get(node)!;
        return {
          stage: node,
          name: NODE_NAMES[node] || node,
          date: record.recordDate instanceof Date ? 
            record.recordDate : new Date(record.recordDate),
          isCompleted: true, // 存在记录的节点视为已完成
          isActive: false, // 稍后设置活跃节点
          record
        };
      });
    
    // 设置最"晚"的节点为活跃节点（按病程顺序，不是日期）
    if (stages.length > 0) {
      const activeIdx = stages.length - 1;
      stages[activeIdx].isActive = true;
    }
    
    setProcessedStages(stages);
  }, [records]);

  // 获取当前活跃的阶段
  const getActiveStage = () => {
    if (!processedStages || processedStages.length === 0) return null;
    return processedStages.find(stage => stage.isActive) || processedStages[processedStages.length - 1];
  };

  // 当前活跃阶段
  const activeStage = getActiveStage();
  
  // 格式化日期函数
  const formatDate = (date: Date): string => {
    try {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}/${month}/${day}`;
    } catch (err) {
      console.error('日期格式化错误:', err);
      return '';
    }
  };
  
  // 如果没有阶段记录，显示空状态
  if (processedStages.length === 0) {
    return (
      <Paper 
        elevation={0}
        sx={{
          p: 2,
          mb: 2,
          border: '1px solid',
          borderColor: 'divider',
          borderRadius: 2
        }}
      >
        <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 1 }}>
          {diseaseName}
          {dayCount > 0 && (
            <Typography component="span" sx={{ ml: 1, color: 'text.secondary', fontSize: '0.9rem' }}>
              ({dayCount}天)
            </Typography>
          )}
        </Typography>
        <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <Typography color="text.secondary" variant="body2">
            未找到病程节点记录
          </Typography>
        </Box>
      </Paper>
    );
  }
  
  return (
    <Paper 
      elevation={0}
      sx={{
        p: 2,
        mb: 2,
        border: '1px solid',
        borderColor: 'divider',
        borderRadius: 2
      }}
    >
      {/* 标题行 */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
        <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mr: 1 }}>
          {diseaseName}
          {dayCount > 0 && (
            <Typography component="span" sx={{ ml: 1, color: 'text.secondary', fontSize: '0.9rem' }}>
              ({dayCount}天)
            </Typography>
          )}
        </Typography>
        <Box sx={{ display: 'flex', flexGrow: 1 }} />
        <Box sx={{ display: 'flex', gap: 1 }}>
          {isPrivate && (
            <Chip
              size="small"
              label="隐私"
              icon={<PrivateIcon fontSize="small" />}
              className={styles.chip}
              sx={{ 
                bgcolor: alpha('#9F7AEA', 0.2), 
                color: '#9F7AEA',
                borderRadius: 1,
                '& .MuiChip-icon': { color: '#9F7AEA' }
              }}
            />
          )}
          {(severity === 'SEVERE' || severity === 'CRITICAL') && (
            <Chip
              size="small"
              label="重度"
              icon={<WarningIcon fontSize="small" />}
              className={styles.chip}
              sx={{ 
                bgcolor: alpha('#F56565', 0.2), 
                color: '#F56565',
                borderRadius: 1,
                '& .MuiChip-icon': { color: '#F56565' }
              }}
            />
          )}
          {stageName && (
            <Chip
              size="small"
              label={stageName}
              icon={<TreatmentIcon fontSize="small" />}
              className={styles.chip}
              sx={{ 
                bgcolor: alpha('#ED8936', 0.2), 
                color: '#ED8936',
                borderRadius: 1,
                '& .MuiChip-icon': { color: '#ED8936' }
              }}
            />
          )}
        </Box>
      </Box>
      
      {/* 副标题 - 病程阶段进展说明 */}
      <Typography 
        variant="body2" 
        color="text.secondary"
        sx={{ mb: 2, fontSize: '0.85rem' }}
      >
        病程阶段进展：显示病情从初诊到当前的发展过程，当前处于
        <Typography 
          component="span" 
          sx={{ 
            color: NODE_COLORS[activeStage?.stage] || 'text.primary',
            fontWeight: 'bold',
            mx: 0.5
          }}
        >
          {activeStage?.name || '未知阶段'}
        </Typography>
        阶段
      </Typography>
      
      {/* 时间线 */}
      <Box className={styles.timelineContainer}>
        <Box className={styles.timelineContent}>
          {processedStages.map((stage, index) => (
            <React.Fragment key={stage.stage}>
              <Tooltip title={`${stage.name} (${formatDate(stage.date)})`}>
                <Box 
                  sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}
                  className={`${styles.nodeWrapper} ${stage.isActive ? styles.activeNode : ''}`}
                >
                  <StageNode 
                    color={NODE_COLORS[stage.stage] || theme.palette.primary.main}
                    isActive={stage.isActive}
                    isCompleted={stage.isCompleted}
                    className={styles.stageNode}
                  />
                  <Typography 
                    variant="caption" 
                    className={styles.stageLabel}
                    sx={{ 
                      mt: 1, 
                      color: stage.isActive ? 'text.primary' : 'text.secondary',
                      fontWeight: stage.isActive ? 'bold' : 'normal',
                      fontSize: '0.7rem'
                    }}
                  >
                    {stage.name}
                  </Typography>
                  <Typography 
                    variant="caption" 
                    className={styles.dateLabel}
                    sx={{ 
                      fontSize: '0.65rem', 
                      color: 'text.secondary',
                      maxWidth: '60px',
                      textAlign: 'center',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }}
                  >
                    {formatDate(stage.date)}
                  </Typography>
                </Box>
              </Tooltip>
              
              {/* 连接线 - 如果不是最后一个节点则显示 */}
              {index < processedStages.length - 1 && (
                <StageConnector 
                  color={NODE_COLORS[stage.stage]}
                  isCompleted={stage.isCompleted && processedStages[index + 1].isCompleted}
                  sx={{ minWidth: { xs: '30px', sm: '50px', md: '60px' } }}
                  className={styles.connector}
                />
              )}
            </React.Fragment>
          ))}
        </Box>
      </Box>
    </Paper>
  );
};

export default DiseaseStageTimeline; 