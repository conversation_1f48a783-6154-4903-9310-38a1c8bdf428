/**
 * 修复卡在PROCESSING状态的AI报告脚本
 * 这是一个可按需运行的脚本，不需要通过定时任务调度
 * 运行方式: node scripts/fixStuckReports.js
 */
const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

// 初始化PrismaClient
const prisma = new PrismaClient();

// 时间阈值（单位：分钟）- 超过这个时间的PROCESSING报告将被标记为FAILED
const TIME_THRESHOLD_MINUTES = 5;

// 日志文件路径
const LOG_FILE = path.join(__dirname, '../logs/ai_reports_fix.log');

/**
 * 写入日志
 * @param {string} message 日志信息
 */
function logMessage(message) {
  const timestamp = new Date().toISOString();
  const logEntry = `[${timestamp}] ${message}\n`;
  
  console.log(message);
  
  // 确保日志目录存在
  const logDir = path.dirname(LOG_FILE);
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }
  
  // 追加日志
  fs.appendFileSync(LOG_FILE, logEntry);
}

/**
 * 修复卡在PROCESSING状态的报告
 */
async function fixStuckReports() {
  try {
    logMessage('开始修复卡在PROCESSING状态的报告');
    
    // 计算时间阈值
    const thresholdTime = new Date();
    thresholdTime.setMinutes(thresholdTime.getMinutes() - TIME_THRESHOLD_MINUTES);
    
    // 查询所有卡住的报告
    const stuckReports = await prisma.aIReports.findMany({
      where: {
        status: 'PROCESSING',
        createdAt: {
          lt: thresholdTime
        }
      },
      select: {
        id: true,
        diseaseId: true,
        patientId: true,
        userId: true,
        createdAt: true
      }
    });
    
    logMessage(`找到 ${stuckReports.length} 个卡住的报告`);
    
    // 修复每个卡住的报告
    for (const report of stuckReports) {
      logMessage(`修复报告 ID: ${report.id}, 创建时间: ${report.createdAt}`);
      
      try {
        await prisma.aIReports.update({
          where: { id: report.id },
          data: {
            status: 'FAILED',
            error_message: `报告生成超时(${TIME_THRESHOLD_MINUTES}分钟)，已自动标记为失败状态`,
            updatedAt: new Date()
          }
        });
        
        logMessage(`已成功将报告 ${report.id} 状态更新为 FAILED`);
      } catch (updateError) {
        logMessage(`更新报告 ${report.id} 失败: ${updateError.message}`);
      }
    }
    
    logMessage('报告修复完成');
    return { success: true, fixedCount: stuckReports.length };
  } catch (error) {
    logMessage(`修复过程出错: ${error.message}`);
    return { success: false, error: error.message };
  } finally {
    await prisma.$disconnect();
  }
}

// 如果直接运行脚本（而不是通过模块导入）
if (require.main === module) {
  fixStuckReports()
    .then((result) => {
      logMessage(`脚本执行结果: ${JSON.stringify(result)}`);
      process.exit(0);
    })
    .catch((error) => {
      logMessage(`脚本执行失败: ${error.message}`);
      process.exit(1);
    });
} else {
  // 如果是作为模块导入，导出函数
  module.exports = { fixStuckReports };
} 