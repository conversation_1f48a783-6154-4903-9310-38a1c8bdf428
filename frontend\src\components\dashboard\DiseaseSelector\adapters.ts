// frontend/src/components/dashboard/DiseaseSelector/adapters.ts

// 这些枚举类型将在DiseaseTimeline.tsx中定义，这里提前声明接口
export enum DiseaseStageEnum {
  INITIAL = '初诊',
  DIAGNOSIS = '确诊',
  TREATMENT = '治疗',
  FOLLOW_UP = '随访',
  PROGNOSIS = '预后'
}

// 阶段颜色映射
export const STAGE_COLORS: Record<DiseaseStageEnum, string> = {
  [DiseaseStageEnum.INITIAL]: '#F56565', // 红色
  [DiseaseStageEnum.DIAGNOSIS]: '#9F7AEA', // 紫色
  [DiseaseStageEnum.TREATMENT]: '#ED8936', // 橙色
  [DiseaseStageEnum.FOLLOW_UP]: '#ECC94B', // 黄色 
  [DiseaseStageEnum.PROGNOSIS]: '#48BB78', // 绿色
};

export enum LabelTypeEnum {
  PRIVATE = '隐私',
  IMPORTANT = '重要',
  TREATMENT_STAGE = '治疗阶段'
}

// 阶段节点接口
export interface DiseaseStageNode {
  stage: DiseaseStageEnum;
  date: Date;
  isActive: boolean;
  isCompleted: boolean;
  labels?: LabelTypeEnum[];
}

// 疾病信息接口
export interface DiseaseInfo {
  id: string;
  name: string;
  dayCount: number;
  stages: DiseaseStageNode[];
  privateNotes?: boolean;
  importantNotes?: string[];
  nextCheckupDate?: Date | null;
  status?: string; // 疾病状态
  diagnosisDate?: Date; // 诊断日期
  activeStage?: DiseaseStageEnum; // 活跃阶段
  records?: RecordInfo[]; // 原始记录数据
  patientId?: string; // 患者ID，用于安全检查
  isPrivate?: boolean | number; // 是否为私密病理
}

// 记录信息接口
export interface RecordInfo {
  id: string;
  title?: string;
  content?: string;
  type?: string | string[];
  stageNode?: string;
  stagePhase?: string;
  recordDate: string;
  stageNodeName?: string;
  stagePhaseName?: string;
  created_at?: string;
  record_date?: string;
  updated_at?: string;
}

// 阶段映射字段表
const STAGE_MAPPINGS: Record<string, DiseaseStageEnum> = {
  // 英文节点映射
  'INITIAL_VISIT': DiseaseStageEnum.INITIAL,
      'INITIAL': DiseaseStageEnum.INITIAL,
      'DIAGNOSIS': DiseaseStageEnum.DIAGNOSIS,
      'TREATMENT': DiseaseStageEnum.TREATMENT,
      'FOLLOW_UP': DiseaseStageEnum.FOLLOW_UP,
  'RECOVERY': DiseaseStageEnum.FOLLOW_UP,
      'PROGNOSIS': DiseaseStageEnum.PROGNOSIS,
  'ARCHIVE': DiseaseStageEnum.PROGNOSIS,
  
  // 中文节点映射
  '初诊': DiseaseStageEnum.INITIAL,
  '确诊': DiseaseStageEnum.DIAGNOSIS,
  '治疗': DiseaseStageEnum.TREATMENT,
  '随访': DiseaseStageEnum.FOLLOW_UP,
  '康复': DiseaseStageEnum.FOLLOW_UP,
  '预后': DiseaseStageEnum.PROGNOSIS,
  '封档': DiseaseStageEnum.PROGNOSIS,
  
  // 中文阶段映射
  '初诊阶段': DiseaseStageEnum.INITIAL,
  '确诊阶段': DiseaseStageEnum.DIAGNOSIS,
  '治疗阶段': DiseaseStageEnum.TREATMENT,
  '随访阶段': DiseaseStageEnum.FOLLOW_UP,
  '康复阶段': DiseaseStageEnum.FOLLOW_UP,
  '预后阶段': DiseaseStageEnum.PROGNOSIS
};

// 将后端阶段名称转换为前端阶段枚举
const mapStage = (stage: string | undefined): DiseaseStageEnum => {
  if (!stage) return DiseaseStageEnum.INITIAL;
  
  // 尝试直接映射
  const mappedStage = STAGE_MAPPINGS[stage];
  if (mappedStage) return mappedStage;
  
  // 如果直接映射失败，尝试归一化后映射
  let normalizedStage = String(stage).toUpperCase().trim();
  
  // 尝试使用归一化后的键匹配
  for (const key in STAGE_MAPPINGS) {
    if (key.toUpperCase() === normalizedStage) {
      return STAGE_MAPPINGS[key];
    }
  }
  
  // 尝试模糊匹配（检查部分包含）
  for (const key in STAGE_MAPPINGS) {
    const upperKey = key.toUpperCase();
    if (normalizedStage.includes(upperKey) || upperKey.includes(normalizedStage)) {
      return STAGE_MAPPINGS[key];
    }
  }
  
  // 尝试从阶段名称推断
  if (normalizedStage.includes('初') || normalizedStage.includes('INIT')) {
    return DiseaseStageEnum.INITIAL;
  } else if (normalizedStage.includes('诊') || normalizedStage.includes('DIAG')) {
    return DiseaseStageEnum.DIAGNOSIS;
  } else if (normalizedStage.includes('治') || normalizedStage.includes('TREAT')) {
    return DiseaseStageEnum.TREATMENT;
  } else if (normalizedStage.includes('随') || normalizedStage.includes('访') || normalizedStage.includes('FOLLOW')) {
    return DiseaseStageEnum.FOLLOW_UP;
  } else if (normalizedStage.includes('后') || normalizedStage.includes('PROG')) {
    return DiseaseStageEnum.PROGNOSIS;
  }
  
  console.warn(`未能匹配阶段名称: ${stage}，默认为初诊`);
  return DiseaseStageEnum.INITIAL;
};

// 安全地解析日期
const parseDate = (dateString: string | undefined | null): Date => {
  if (!dateString) return new Date(0);
  
  try {
    // 检查是否为有效日期字符串
    if (typeof dateString !== 'string') {
      console.error('日期不是字符串:', dateString);
      return new Date(0);
    }

    // 特殊处理YYYY-MM-DD格式
    const simpleDateRegex = /^(\d{4})-(\d{1,2})-(\d{1,2})$/;
    const simpleMatch = dateString.match(simpleDateRegex);
    if (simpleMatch) {
      const [, year, month, day] = simpleMatch;
      console.log(`检测到简单日期格式 YYYY-MM-DD: ${dateString}, 解析为: 年=${year}, 月=${month}, 日=${day}`);
      
      // 确保解析为本地日期，避免时区问题
      const parsedDate = new Date(parseInt(year, 10), parseInt(month, 10) - 1, parseInt(day, 10));
      console.log(`  -> 解析结果: ${parsedDate.toISOString()}`);
      return parsedDate;
    }

    // 尝试直接解析ISO格式日期
    const date = new Date(dateString);
    if (!isNaN(date.getTime())) {
      return date;
    }
    
    // 尝试解析其他格式
    // 匹配 YYYY-MM-DD 或 YYYY/MM/DD 格式
    const dateRegex = /^(\d{4})[-\/](\d{1,2})[-\/](\d{1,2})$/;
    const match = dateString.match(dateRegex);
    if (match) {
      const [, year, month, day] = match;
      const parsedDate = new Date(
        parseInt(year), 
        parseInt(month) - 1, // 月份从0开始
        parseInt(day)
      );
      return parsedDate;
    }
    
    console.error('无法解析日期格式:', dateString);
    return new Date(0);
  } catch (e) {
    console.error('日期解析错误:', e, dateString);
    return new Date(0);
  }
};

// 定义阶段优先级
const STAGE_PRIORITY: Record<DiseaseStageEnum, number> = {
  [DiseaseStageEnum.INITIAL]: 1,
  [DiseaseStageEnum.DIAGNOSIS]: 2,
  [DiseaseStageEnum.TREATMENT]: 3,
  [DiseaseStageEnum.FOLLOW_UP]: 4,
  [DiseaseStageEnum.PROGNOSIS]: 5
};

// 找出最新的活跃阶段 - 使用最靠后的阶段信息
const findCurrentStage = (disease: any, records: RecordInfo[] = []): DiseaseStageEnum => {
  console.log(`开始为病理[${disease.name}]查找当前活跃阶段，记录数量: ${records.length}`);
  
  // 如果没有记录，检查disease自身的阶段
  if (records.length === 0) {
    if (disease.stage) {
      const mappedStage = mapStage(disease.stage);
      console.log(`没有记录，使用病理自身的阶段属性: ${disease.stage} => ${mappedStage}`);
      return mappedStage;
    }
    if (disease.stage_name) {
      const mappedStage = mapStage(disease.stage_name);
      console.log(`没有记录，使用病理自身的阶段名称: ${disease.stage_name} => ${mappedStage}`);
      return mappedStage;
    }
    console.log(`没有记录，也没有病理阶段信息，返回初诊阶段`);
    return DiseaseStageEnum.INITIAL;
  }
  
  // 先按日期排序（从晚到早），这样最新的记录会排在前面
  const sortedRecords = [...records].sort((a, b) => {
    const dateA = parseDate(a.record_date || a.recordDate || a.created_at).getTime();
    const dateB = parseDate(b.record_date || b.recordDate || b.created_at).getTime();
    return dateB - dateA; // 降序排列，最新的记录在前
  });
  
  // 首先查找有明确阶段标记的记录
  const recordsWithStages = sortedRecords.filter(record => 
    record.stageNode || record.stageNodeName || record.stagePhase || record.stagePhaseName
  );
  
  console.log(`找到${recordsWithStages.length}条有阶段标记的记录`);
  
  // 如果有带阶段标记的记录，使用它们
  if (recordsWithStages.length > 0) {
    // 跟踪找到的最高优先级阶段
    let highestStage: DiseaseStageEnum = DiseaseStageEnum.INITIAL;
    let highestPriority = STAGE_PRIORITY[DiseaseStageEnum.INITIAL];
    
    // 遍历所有带阶段的记录，查找最靠后的阶段
    for (const record of recordsWithStages) {
      let stageMapped: DiseaseStageEnum | undefined;
      
      // 从各个阶段字段中提取
      if (record.stageNodeName) {
        stageMapped = mapStage(record.stageNodeName);
        if (stageMapped && STAGE_PRIORITY[stageMapped] > highestPriority) {
          highestStage = stageMapped;
          highestPriority = STAGE_PRIORITY[stageMapped];
          console.log(`发现更高优先级阶段(stageNodeName): ${record.stageNodeName} => ${stageMapped}, 优先级: ${highestPriority}`);
        }
      }
      
      if (record.stageNode) {
        stageMapped = mapStage(record.stageNode);
        if (stageMapped && STAGE_PRIORITY[stageMapped] > highestPriority) {
          highestStage = stageMapped;
          highestPriority = STAGE_PRIORITY[stageMapped];
          console.log(`发现更高优先级阶段(stageNode): ${record.stageNode} => ${stageMapped}, 优先级: ${highestPriority}`);
        }
      }
      
      if (record.stagePhaseName) {
        stageMapped = mapStage(record.stagePhaseName);
        if (stageMapped && STAGE_PRIORITY[stageMapped] > highestPriority) {
          highestStage = stageMapped;
          highestPriority = STAGE_PRIORITY[stageMapped];
          console.log(`发现更高优先级阶段(stagePhaseName): ${record.stagePhaseName} => ${stageMapped}, 优先级: ${highestPriority}`);
        }
      }
      
      if (record.stagePhase) {
        stageMapped = mapStage(record.stagePhase);
        if (stageMapped && STAGE_PRIORITY[stageMapped] > highestPriority) {
          highestStage = stageMapped;
          highestPriority = STAGE_PRIORITY[stageMapped];
          console.log(`发现更高优先级阶段(stagePhase): ${record.stagePhase} => ${stageMapped}, 优先级: ${highestPriority}`);
        }
      }
    }
    
    console.log(`基于记录阶段标记，选择阶段: ${highestStage}, 优先级: ${highestPriority}`);
    return highestStage;
  }
  
  // 如果没有带阶段标记的记录，基于记录数量推断阶段
  const recordCount = records.length;
  if (recordCount >= 10) {
    console.log(`基于记录数量(${recordCount}>=10)推断为预后阶段`);
    return DiseaseStageEnum.PROGNOSIS;
  } else if (recordCount >= 7) {
    console.log(`基于记录数量(${recordCount}>=7)推断为随访阶段`);
    return DiseaseStageEnum.FOLLOW_UP;
  } else if (recordCount >= 4) {
    console.log(`基于记录数量(${recordCount}>=4)推断为治疗阶段`);
    return DiseaseStageEnum.TREATMENT;
  } else if (recordCount >= 2) {
    console.log(`基于记录数量(${recordCount}>=2)推断为确诊阶段`);
    return DiseaseStageEnum.DIAGNOSIS;
  }
  
  // 只有一条记录，认为是初诊阶段
  console.log(`基于记录数量(${recordCount}=1)推断为初诊阶段`);
  return DiseaseStageEnum.INITIAL;
};

// 获取记录的日期
const getRecordDate = (record: RecordInfo): Date => {
  const dateString = record.recordDate || record.record_date || record.created_at || record.updated_at;
  return parseDate(dateString);
};

// 从记录数据构建阶段节点
const buildStagesFromRecords = (
  disease: any, 
  records: RecordInfo[] = []
): DiseaseStageNode[] => {
  console.log(`为病理[${disease.name}]构建阶段节点，共有${records.length}条记录`);
  
  // 获取参考日期 - 优先使用disease的诊断日期或创建日期
  const getReferenceDate = (): Date => {
    // 首先尝试使用诊断日期
    if (disease.diagnosis_date) {
      const diagDate = parseDate(disease.diagnosis_date);
      if (diagDate && diagDate.getTime() > 0 && diagDate.getFullYear() >= 2000) {
        return diagDate;
      }
    }
    
    // 其次尝试使用创建日期
    if (disease.created_at) {
      const createDate = parseDate(disease.created_at);
      if (createDate && createDate.getTime() > 0 && createDate.getFullYear() >= 2000) {
        return createDate;
      }
    }
    
    // 如果两者都不存在，只能使用系统日期
    return new Date();
  };
  
  // 获取参考日期
  const referenceDate = getReferenceDate();
  console.log(`病理[${disease.name}]的参考日期:`, referenceDate.toISOString());
  
  // 用于追踪每个阶段的最早日期
  const stageDates: Record<DiseaseStageEnum, Date> = {
    [DiseaseStageEnum.INITIAL]: new Date(0), // 初始化为旧日期
    [DiseaseStageEnum.DIAGNOSIS]: new Date(0),
    [DiseaseStageEnum.TREATMENT]: new Date(0),
    [DiseaseStageEnum.FOLLOW_UP]: new Date(0),
    [DiseaseStageEnum.PROGNOSIS]: new Date(0)
  };
  
  // 用于追踪每个阶段是否有记录
  const stageHasRecords: Record<DiseaseStageEnum, boolean> = {
    [DiseaseStageEnum.INITIAL]: false,
    [DiseaseStageEnum.DIAGNOSIS]: false,
    [DiseaseStageEnum.TREATMENT]: false,
    [DiseaseStageEnum.FOLLOW_UP]: false,
    [DiseaseStageEnum.PROGNOSIS]: false
  };
  
  // 先按日期排序（从早到晚）
  const sortedRecords = [...records].sort((a, b) => {
    // 优先使用有效的record_date字段进行排序
    const getValidDate = (r: RecordInfo) => {
      // 先检查record_date字段
      if (r.record_date) {
        const date = parseDate(r.record_date);
        if (date.getTime() > 0 && date.getFullYear() >= 2000) {
          return date.getTime();
        }
      }
      // 再尝试其他日期字段
      const otherDate = parseDate(r.recordDate || r.created_at || r.updated_at);
      if (otherDate.getTime() > 0 && otherDate.getFullYear() >= 2000) {
        return otherDate.getTime();
      }
      return 0; // 没有有效日期
    };
    
    const dateA = getValidDate(a);
    const dateB = getValidDate(b);
    
    // 如果都没有有效日期，保持原有顺序
    if (dateA === 0 && dateB === 0) return 0;
    // 如果只有一个有效日期，将有效日期的记录排在前面
    if (dateA === 0) return 1;
    if (dateB === 0) return -1;
    
    return dateA - dateB; // 升序排序，最早的记录在前
  });
  
  console.log(`排序后的记录：`, sortedRecords.map(r => ({
    id: r.id,
    date: r.record_date || r.recordDate || r.created_at,
    stage: r.stageNode || r.stageNodeName || r.stagePhase || r.stagePhaseName
  })));
  
  // 为每个阶段找到最早的记录
  for (const record of sortedRecords) {
    // 获取记录日期
    let recordDate: Date | null = null;
    
    // 优先使用record_date字段，但需要确保它是有效的
    if (record.record_date) {
      recordDate = parseDate(record.record_date);
      // 检查是否为有效日期（年份>=2000）
      if (recordDate.getTime() <= 0 || recordDate.getFullYear() < 2000) {
        console.log(`记录 ${record.id} 的record_date字段无效: ${record.record_date}`);
        recordDate = null;
      }
    }
    
    // 如果record_date无效，尝试使用其他日期字段
    if (!recordDate) {
      if (record.recordDate) {
        recordDate = parseDate(record.recordDate);
      } else if (record.created_at) {
        recordDate = parseDate(record.created_at);
      } else if (record.updated_at) {
        recordDate = parseDate(record.updated_at);
      }
      
      // 检查最终获取的日期是否有效
      if (!recordDate || recordDate.getTime() <= 0 || recordDate.getFullYear() < 2000) {
        console.log(`记录 ${record.id} 所有日期字段都无效，跳过`);
        continue; // 如果所有日期字段都无效，跳过此记录
      }
    }
    
    // 确定记录的阶段
    let stage: DiseaseStageEnum | undefined;
    
    // 尝试从各个字段获取阶段信息，按优先级排序
    if (record.stageNodeName) {
      stage = mapStage(record.stageNodeName);
    } else if (record.stageNode) {
      stage = mapStage(record.stageNode);
    } else if (record.stagePhaseName) {
      stage = mapStage(record.stagePhaseName);
    } else if (record.stagePhase) {
      stage = mapStage(record.stagePhase);
    }
    
    if (stage) {
      // 更新阶段有记录的标记
      stageHasRecords[stage] = true;
      
      // 只有当当前日期比已记录的更早或者没有日期时，才更新
      if (stageDates[stage].getTime() <= 0 || recordDate.getTime() < stageDates[stage].getTime()) {
        stageDates[stage] = new Date(recordDate);
        console.log(`为阶段 ${stage} 设置日期为 ${stageDates[stage].toISOString()} (来自记录ID: ${record.id})`);
      }
    }
  }
  
  // 获取病理的当前活跃阶段
  const currentStage = findCurrentStage(disease, records);
  console.log(`当前活跃阶段: ${currentStage}`);
  
  // 阶段顺序定义
  const stageOrder = [
    DiseaseStageEnum.INITIAL,
    DiseaseStageEnum.DIAGNOSIS,
    DiseaseStageEnum.TREATMENT,
    DiseaseStageEnum.FOLLOW_UP,
    DiseaseStageEnum.PROGNOSIS
  ];
  
  // 找出当前阶段的索引
  const currentStageIndex = stageOrder.indexOf(currentStage);
  console.log(`当前阶段索引: ${currentStageIndex} (${currentStage})`);
  
  // 如果初诊没有日期，尝试使用诊断日期
  if (!stageHasRecords[DiseaseStageEnum.INITIAL] || stageDates[DiseaseStageEnum.INITIAL].getTime() <= 0) {
    const diagnosisDate = parseDate(disease.diagnosis_date || disease.created_at);
    if (diagnosisDate.getTime() > 0 && diagnosisDate.getFullYear() >= 2000) {
      stageHasRecords[DiseaseStageEnum.INITIAL] = true;
      stageDates[DiseaseStageEnum.INITIAL] = diagnosisDate;
      console.log(`使用诊断日期作为初诊阶段日期: ${diagnosisDate.toISOString()}`);
    } else if (sortedRecords.length > 0) {
      // 如果有任何记录但没有初诊记录，使用最早的记录日期
      const firstRecord = sortedRecords[0];
      const firstDate = parseDate(firstRecord.record_date || firstRecord.recordDate || firstRecord.created_at);
      if (firstDate.getTime() > 0 && firstDate.getFullYear() >= 2000) {
        stageHasRecords[DiseaseStageEnum.INITIAL] = true;
        stageDates[DiseaseStageEnum.INITIAL] = firstDate;
        console.log(`使用最早记录日期作为初诊阶段日期: ${firstDate.toISOString()}`);
      }
    }
  }
  
  // 处理每个阶段的完成状态
  for (let i = 0; i < stageOrder.length; i++) {
    const stage = stageOrder[i];
    
    // 当前阶段标记为活跃
    if (i === currentStageIndex) {
      // 如果当前没有记录，标记为有记录
      if (!stageHasRecords[stage]) {
        stageHasRecords[stage] = true;
      }
      
      // 如果没有日期，设置一个日期
      if (stageDates[stage].getTime() <= 0) {
        // 尝试从前一个阶段推算
        if (i > 0 && stageDates[stageOrder[i-1]].getTime() > 0) {
          const prevDate = new Date(stageDates[stageOrder[i-1]]);
          prevDate.setDate(prevDate.getDate() + 7); // 设置为比前一阶段晚7天
          stageDates[stage] = prevDate;
          console.log(`为当前活跃阶段 ${stage} 设置推算日期: ${prevDate.toISOString()}`);
        } else {
          // 没有前置阶段日期，使用参考日期替代当前日期
          const defaultDate = new Date(referenceDate);
          stageDates[stage] = defaultDate;
          console.log(`为当前活跃阶段 ${stage} 使用参考日期: ${defaultDate.toISOString()}`);
        }
      }
    }
    // 之前的阶段标记为已完成
    else if (i < currentStageIndex) {
      if (!stageHasRecords[stage]) {
        stageHasRecords[stage] = true;
        console.log(`标记之前的阶段 ${stage} 为已完成`);
      }
      
      // 如果没有日期，推算一个合理的日期
      if (stageDates[stage].getTime() <= 0) {
        // 尝试基于后续阶段的日期
        for (let j = i + 1; j <= currentStageIndex; j++) {
          const nextStage = stageOrder[j];
          if (stageDates[nextStage].getTime() > 0) {
            const inferredDate = new Date(stageDates[nextStage]);
            inferredDate.setDate(inferredDate.getDate() - ((j - i) * 7)); // 每个阶段差7天
            stageDates[stage] = inferredDate;
            console.log(`为已完成阶段 ${stage} 设置推算日期: ${inferredDate.toISOString()}`);
            break;
          }
        }
        
        // 如果还没有日期，基于初诊日期
        if (stageDates[stage].getTime() <= 0 && stageDates[DiseaseStageEnum.INITIAL].getTime() > 0) {
          const inferredDate = new Date(stageDates[DiseaseStageEnum.INITIAL]);
          inferredDate.setDate(inferredDate.getDate() + (i * 7)); // 每个阶段间隔7天
          stageDates[stage] = inferredDate;
          console.log(`为已完成阶段 ${stage} 设置推算日期(基于初诊): ${inferredDate.toISOString()}`);
        }
      }
    }
    // 后续阶段标记为未完成
    else {
      stageHasRecords[stage] = false;
    }
  }
  
  // 构建所有阶段节点
  const stageNodes = stageOrder.map((stage, index) => {
    const isActive = index === currentStageIndex;
    const isCompleted = index < currentStageIndex;
  
    return {
      stage: stage,
      date: stageDates[stage],
      isActive: isActive,
      isCompleted: isCompleted,
      labels: []
    };
  });
  
  console.log(`生成的阶段节点:`, stageNodes);
  return stageNodes;
};

// 数据适配器 - 将后端数据转换为组件格式
export const adaptDiseaseData = (disease: any, records: RecordInfo[] = []): DiseaseInfo => {
  console.log(`适配病理[${disease.name}]数据:`, {
    diseaseData: disease,
    recordsCount: records.length,
    diagnosis_date_RAW: disease.diagnosis_date,
    diagnosis_date_TYPE: typeof disease.diagnosis_date,
    firstRecord: records.length > 0 ? records[0] : null,
    lastRecord: records.length > 0 ? records[records.length - 1] : null,
  });

  // 检查记录数组
  if (!records || records.length === 0) {
    console.warn(`警告: 病理[${disease.name}]没有记录数据传入`);
  } else {
    console.log(`病理[${disease.name}]有${records.length}条记录数据:`, records.map(r => ({
      id: r.id,
      title: r.title,
      date: r.record_date || r.recordDate
    })));
  }

  // 查找最早的记录日期
  let firstRecordDate: Date | null = null;
  let validRecordFound = false;
  
  // 直接使用病理的诊断日期(diagnosis_date)
  if (disease.diagnosis_date) {
    const diagnosisDate = parseDate(disease.diagnosis_date);
    if (diagnosisDate && diagnosisDate.getTime() > 0 && diagnosisDate.getFullYear() >= 2000) {
      firstRecordDate = diagnosisDate;
      validRecordFound = true;
      console.log(`病理[${disease.name}]使用诊断日期:`, {
        date: firstRecordDate.toISOString(),
        originalDate: disease.diagnosis_date
      });
    }
  }
  
  // 如果诊断日期无效，使用病理的创建日期(created_at)
  if (!validRecordFound && disease.created_at) {
    const createdDate = parseDate(disease.created_at);
    if (createdDate && createdDate.getTime() > 0 && createdDate.getFullYear() >= 2000) {
      firstRecordDate = createdDate;
      validRecordFound = true;
      console.log(`病理[${disease.name}]诊断日期无效，使用创建日期:`, {
        date: firstRecordDate.toISOString(),
        originalDate: disease.created_at
      });
    }
  }
  
  // 如果所有尝试都失败，使用系统日期
  if (!validRecordFound) {
    firstRecordDate = new Date();
    console.log(`病理[${disease.name}]没有有效的诊断日期或创建日期，使用系统日期:`, firstRecordDate.toISOString());
  }
  
  // 确定起始日期
  let startDate: Date = firstRecordDate!; // 使用非空断言，因为在这一行firstRecordDate一定不为null
  
  // 计算天数 - 从起始日期到当前的天数
  const today = new Date();
  
  // 计算天数 - 时间差转为天数，同一天应为0天
  const diffTime = today.getTime() - startDate.getTime();
  // 如果是未来日期，显示为0天；如果是今天，也是0天；如果是过去日期，计算实际天数
  const dayCount = diffTime < 0 ? 0 : 
                  (today.getFullYear() === startDate.getFullYear() && 
                   today.getMonth() === startDate.getMonth() && 
                   today.getDate() === startDate.getDate()) ? 0 : 
                  Math.max(1, Math.ceil(diffTime / (1000 * 60 * 60 * 24)));
  
  console.log(`病理[${disease.name}]的天数计算:`, {
    today: today.toISOString(), 
    startDate: startDate.toISOString(), 
    diffTime,
    todayDate: `${today.getFullYear()}-${today.getMonth()+1}-${today.getDate()}`,
    startDateFormatted: `${startDate.getFullYear()}-${startDate.getMonth()+1}-${startDate.getDate()}`,
    isSameDay: today.getFullYear() === startDate.getFullYear() && 
               today.getMonth() === startDate.getMonth() && 
               today.getDate() === startDate.getDate(),
    dayCount
  });
  
  // 从记录中构建阶段节点
  const stages = buildStagesFromRecords(disease, records);
  
  // 确保每个阶段节点都有有效的日期
  stages.forEach((stage, index) => {
    if (!stage.date || stage.date.getTime() <= 0 || stage.date.getFullYear() < 2000) {
      // 如果日期无效，根据索引生成一个合理的日期
      const defaultDate = new Date(startDate);
      defaultDate.setDate(defaultDate.getDate() + (index * 7)); // 每个阶段相隔7天
      stage.date = defaultDate;
      console.log(`修复阶段 ${stage.stage} 的无效日期为:`, stage.date.toISOString());
    }
  });
  
  console.log(`病理[${disease.name}]的阶段节点:`, stages);
  
  // 标记隐私属性
  const isPrivate = (value: any): boolean => {
    if (value === undefined || value === null) return false;
    if (typeof value === 'boolean') return value;
    if (typeof value === 'number') return value === 1;
    if (typeof value === 'string') return value === '1' || value.toLowerCase() === 'true';
    return false;
  };

  if (isPrivate(disease.is_private) || isPrivate(disease.isPrivate)) {
    stages.forEach(stage => {
      if (stage.isCompleted || stage.isActive) {
        stage.labels = stage.labels || [];
        stage.labels.push(LabelTypeEnum.PRIVATE);
      }
    });
  }
  
  // 获取活跃阶段
  const activeStage = findCurrentStage(disease, records);
  
  const result = {
    id: disease.id,
    name: disease.name,
    dayCount: dayCount, // 使用计算出的天数
    stages: stages,
    privateNotes: isPrivate(disease.is_private) || isPrivate(disease.isPrivate),
    importantNotes: disease.description ? [disease.description] : [],
    status: disease.status || 'ACTIVE', // 默认为活跃状态
    diagnosisDate: disease.diagnosis_date ? parseDate(disease.diagnosis_date) : startDate, // 优先使用原始诊断日期
    activeStage: activeStage, // 添加活跃阶段信息，方便在时间线中使用
    records: records, // 保存原始记录数据
    patientId: disease.patientId || disease.patient_id || disease.patientID || disease.patient_ID,
    isPrivate: disease.is_private || disease.isPrivate
  };
  
  // 确认记录数据是否正确设置
  if (!result.records || result.records.length === 0) {
    console.warn(`警告: 返回的病理[${disease.name}]结果中records为空`);
  } else {
    console.log(`确认: 返回的病理[${disease.name}]结果中包含${result.records.length}条记录`);
  }
  
  console.log(`适配后病理[${disease.name}]结果:`, result);
  
  // 确保有患者ID
  if (!result.patientId) {
    console.warn(`警告: 病理[${disease.name}] (ID: ${disease.id}) 没有患者ID信息`);
  } else {
    console.log(`病理[${disease.name}]的患者ID: ${result.patientId}`);
  }
  
  return result;
}; 