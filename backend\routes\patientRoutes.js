const express = require('express');
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');
const knex = require('knex')(require('../knexfile').development);
const router = express.Router();
const { Model } = require('objection');
const { auth } = require('../src/middleware/auth');

// JWT密钥，应从环境变量或配置文件获取
const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret';

// 设置 Objection.js
Model.knex(knex);

// 中间件：验证用户认证
const authenticateUser = async (req, res, next) => {
  const authHeader = req.headers.authorization;
  if (!authHeader) return res.status(401).json({ error: '未授权' });
  
  const token = authHeader.split(' ')[1];
  try {
    const { id } = jwt.verify(token, JWT_SECRET);
    req.userId = id; // 将用户ID附加到请求对象上
    
    // 检查用户是否存在
    const user = await knex('users').where({ id }).first();
    if (!user) {
      return res.status(404).json({ error: '用户不存在' });
    }
    
    req.user = user; // 将用户信息附加到请求对象上
    next();
  } catch (error) {
    console.error('认证错误:', error);
    res.status(401).json({ error: 'token无效或已过期' });
  }
};

// 添加日志记录函数
const addPatientLog = async (patient_id, user_id, action, details) => {
  try {
    await knex('patient_logs').insert({
      id: uuidv4(),
      patient_id,
      user_id,
      action,
      details: JSON.stringify(details),
      created_at: new Date().toISOString()
    });
  } catch (error) {
    console.error('添加患者日志错误:', error);
  }
};

// 获取所有患者
router.get('/', auth, async (req, res) => {
  try {
    // 获取请求参数
    const { userId } = req;
    const userRole = req.user.role;
    const { service_context } = req.query; // 服务上下文参数
    
    console.log(`[DEBUG] 获取患者列表请求: 用户ID=${userId}, 用户角色=${userRole}, 服务上下文=${service_context || 'false'}`);
    
    // 判断操作上下文 - 普通用户上下文还是服务上下文
    const isServiceContext = service_context === 'true';
    
    let patientsData = [];
    
    // 在普通用户上下文中，任何用户都只能看到自己的患者
    if (!isServiceContext) {
      console.log(`[DEBUG] 普通用户上下文: 用户只能查看自己的患者`);
      
      // 获取用户自己的患者
      patientsData = await knex('patients')
        .where('user_id', userId)
        .orderBy('created_at', 'desc');
    } 
    // 在服务上下文中，服务用户和管理员可以看到被授权的患者
    else if (userRole === 'SERVICE' || userRole === 'ADMIN') {
      console.log(`[DEBUG] 服务上下文: 查看自己的和被授权的患者`);
      
      // 1. 获取用户自己的患者
      const ownPatients = await knex('patients')
      .where('user_id', userId)
      .orderBy('created_at', 'desc');
      
      let accessiblePatientIds = ownPatients.map(p => p.id);
      patientsData = [...ownPatients];
      
      // 2. 获取通过授权访问的患者
      // 2.1 获取活跃的授权关系
      const authorizations = await knex('user_authorizations')
        .where('authorized_id', userId)
        .where('status', 'ACTIVE');
      
      console.log(`[DEBUG] 用户${userId}有${authorizations.length}个授权关系`);
      
      if (authorizations.length > 0) {
        // 获取有具体患者ID的授权
        const patientAuthorizations = authorizations.filter(auth => auth.patient_id);
        const patientIds = patientAuthorizations.map(auth => auth.patient_id);
        
        console.log(`[DEBUG] 有患者ID的授权数量: ${patientAuthorizations.length}`);
        
        if (patientIds.length > 0) {
          // 查询这些患者的信息
          const authorizedPatients = await knex('patients')
            .whereIn('id', patientIds)
            .orderBy('created_at', 'desc');
            
          console.log(`[DEBUG] 授权访问的特定患者数量: ${authorizedPatients.length}`);
          
          // 添加到结果集，并去重
          const existingIds = new Set(patientsData.map(p => p.id));
          for (const patient of authorizedPatients) {
            if (!existingIds.has(patient.id)) {
              patientsData.push(patient);
              existingIds.add(patient.id);
            }
          }
        }
        
        // 获取全局授权（没有指定患者ID）
        const globalAuthorizations = authorizations.filter(auth => !auth.patient_id);
        const authorizerIds = globalAuthorizations.map(auth => auth.authorizer_id);
        
        console.log(`[DEBUG] 全局授权数量: ${globalAuthorizations.length}`);
        
        if (authorizerIds.length > 0) {
          // 查询这些授权人的所有患者
          const globallyAuthorizedPatients = await knex('patients')
            .whereIn('user_id', authorizerIds)
            .orderBy('created_at', 'desc');
            
          console.log(`[DEBUG] 全局授权患者数量: ${globallyAuthorizedPatients.length}`);
          
          // 添加到结果集，并去重
          const existingIds = new Set(patientsData.map(p => p.id));
          for (const patient of globallyAuthorizedPatients) {
            if (!existingIds.has(patient.id)) {
              patientsData.push(patient);
              existingIds.add(patient.id);
            }
          }
        }
      }
    } 
    // 如果是在服务上下文，但用户既不是服务用户也不是管理员
    else {
      console.log(`[DEBUG] 用户角色 ${userRole} 无权访问服务上下文`);
      return res.status(403).json({ error: '无权访问服务管理功能' });
    }
    
    console.log(`[DEBUG] 最终返回患者数量: ${patientsData.length}`);
    
    // 转换为驼峰命名格式
    const patients = patientsData.map(patient => ({
      id: patient.id,
      name: patient.name,
      gender: patient.gender,
      phoneNumber: patient.phone_number,
      email: patient.email,
      birthDate: patient.birth_date,
      idCard: patient.id_card,
      medicareCard: patient.medicare_card,
      medicareLocation: patient.medicare_location,
      address: patient.address,
      emergencyContactName: patient.emergency_contact_name,
      emergencyContactPhone: patient.emergency_contact_phone,
      emergencyContactRelationship: patient.emergency_contact_relationship,
      pastMedicalHistory: patient.past_medical_history,
      familyMedicalHistory: patient.family_medical_history,
      allergyHistory: patient.allergy_history,
      bloodType: patient.blood_type,
      lastVisitDate: patient.last_visit_date,
      height: patient.height,
      weight: patient.weight,
      bmi: patient.bmi,
      userId: patient.user_id,
      isPrimary: patient.is_primary,
      createdAt: patient.created_at,
      updatedAt: patient.updated_at,
      // 添加标记，表示该患者是否是通过授权访问的
      isAuthorized: patient.user_id !== userId
    }));
    
    res.json(patients);
  } catch (error) {
    console.error('获取患者列表失败:', error);
    res.status(500).json({ error: '获取患者列表失败' });
  }
});

// 获取单个患者详情
router.get('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.userId;
    const userRole = req.user.role;
    const { service_context } = req.query; // 服务上下文参数
    
    console.log(`[DEBUG] 获取患者详情请求: 患者ID=${id}, 用户ID=${userId}, 用户角色=${userRole}, 服务上下文=${service_context || 'false'}`);
    
    // 判断操作上下文 - 普通用户上下文还是服务上下文
    const isServiceContext = service_context === 'true';

    // 获取患者信息
    const patient = await knex('patients').where({ id }).first();
    
    if (!patient) {
      return res.status(404).json({ error: '患者不存在' });
    }
    
    // 权限检查
    const isOwner = patient.user_id === userId;
    const isAdmin = userRole === 'ADMIN';
    
    // 在普通用户上下文中，任何用户都只能看到自己的患者
    if (!isServiceContext) {
      if (!isOwner && !isAdmin) {
        // --- 新增授权检查逻辑 START ---
        let hasAccessViaAuthorization = false;
        if (userRole === 'SERVICE') { // 只为SERVICE用户在普通上下文检查授权
            console.log(`[DEBUG] 普通用户上下文 (SERVICE用户): 检查授权关系 for patient ${id} (owner: ${patient.user_id}) by user ${userId}`);
            const authorization = await knex('user_authorizations')
              .where({
                'authorized_id': userId, // 当前SERVICE用户
                'status': 'ACTIVE',
                // 授权人应该是患者的创建者/管理者
                'authorizer_id': patient.user_id 
              })
              .where(function() { // 检查针对特定患者的授权，或者没有指定患者ID的"全局"授权
                this.where('patient_id', id).orWhereNull('patient_id');
              })
              .first();

            if (authorization) {
                // 检查隐私级别，例如 'STANDARD' 或 'FULL' 才允许访问详情
                if (['STANDARD', 'FULL'].includes(authorization.privacy_level)) {
                    hasAccessViaAuthorization = true;
                    console.log(`[DEBUG] 普通用户上下文: SERVICE用户通过授权 ${authorization.id} (级别 ${authorization.privacy_level}) 访问患者 ${id}`);
                } else {
                    console.log(`[DEBUG] 普通用户上下文: SERVICE用户授权 ${authorization.id} (级别 ${authorization.privacy_level}) 不足以访问患者 ${id} 详情`);
                }
            } else {
              console.log(`[DEBUG] 普通用户上下文 (SERVICE用户): 未找到对患者 ${id} (owner: ${patient.user_id}) 的有效授权 for user ${userId}`);
            }
        }
        
        if (!hasAccessViaAuthorization) { // 如果不是SERVICE角色，或者SERVICE角色但无有效授权
            console.log(`[DEBUG] 普通用户上下文: 用户 ${userId} (角色 ${userRole}) 无权访问患者 ${id} (非自己且无有效授权)`);
        return res.status(403).json({ error: '无权访问此患者信息' });
        }
        // --- 新增授权检查逻辑 END ---
      }
    } 
    // 在服务上下文中，检查授权关系
    else if (userRole === 'SERVICE' || isAdmin) {
      // 如果不是拥有者或管理员，检查授权关系
      if (!isOwner && !isAdmin) {
        console.log(`[DEBUG] 服务上下文: 检查授权关系`);
        
        // 查询授权关系
        const authorization = await knex('user_authorizations')
          .where({
            'authorized_id': userId,
            'status': 'ACTIVE'
          })
          .where(function() {
            this.where('patient_id', id)
              .orWhere(function() {
                this.where('patient_id', null)
                    .andWhere('authorizer_id', patient.user_id);
              });
          })
          .first();
        
        if (!authorization) {
          console.log(`[DEBUG] 服务上下文: 用户无授权访问此患者`);
          return res.status(403).json({ error: '无权访问此患者信息' });
        }
      }
    }
    // 非服务用户在服务上下文中尝试访问
    else {
      console.log(`[DEBUG] 用户角色 ${userRole} 无权访问服务上下文`);
      return res.status(403).json({ error: '无权访问服务管理功能' });
    }
    
    // 转换为驼峰命名格式
    const patientData = {
      id: patient.id,
      name: patient.name,
      gender: patient.gender,
      phoneNumber: patient.phone_number,
      email: patient.email,
      birthDate: patient.birth_date,
      idCard: patient.id_card,
      medicareCard: patient.medicare_card,
      medicareLocation: patient.medicare_location,
      address: patient.address,
      emergencyContactName: patient.emergency_contact_name,
      emergencyContactPhone: patient.emergency_contact_phone,
      emergencyContactRelationship: patient.emergency_contact_relationship,
      pastMedicalHistory: patient.past_medical_history,
      familyMedicalHistory: patient.family_medical_history,
      allergyHistory: patient.allergy_history,
      bloodType: patient.blood_type,
      lastVisitDate: patient.last_visit_date,
      height: patient.height,
      weight: patient.weight,
      bmi: patient.bmi,
      userId: patient.user_id,
      isPrimary: patient.is_primary,
      createdAt: patient.created_at,
      updatedAt: patient.updated_at
    };
    
    res.json(patientData);
  } catch (error) {
    console.error('获取患者详情失败:', error);
    res.status(500).json({ error: '获取患者详情失败' });
  }
});

// 创建新患者
router.post('/', auth, async (req, res) => {
  try {
    const { userId } = req;
    const {
      name,
      gender,
      birthDate,
      phoneNumber,
      email,
      address,
      idCard,
      medicareCard,
      medicareLocation,
      bloodType,
      emergencyContactName,
      emergencyContactPhone,
      emergencyContactRelationship,
      pastMedicalHistory,
      familyMedicalHistory,
      allergyHistory,
      isPrimary,
      height,
      weight,
      bmi
    } = req.body;
    
    // 转换为下划线命名格式
    const patientData = {
      name,
      gender,
      birth_date: birthDate,
      phone_number: phoneNumber,
      email,
      address,
      id_card: idCard,
      medicare_card: medicareCard,
      medicare_location: medicareLocation,
      blood_type: bloodType,
      emergency_contact_name: emergencyContactName,
      emergency_contact_phone: emergencyContactPhone,
      emergency_contact_relationship: emergencyContactRelationship,
      past_medical_history: pastMedicalHistory,
      family_medical_history: familyMedicalHistory,
      allergy_history: allergyHistory,
      is_primary: isPrimary ? 1 : 0
    };
    
    // 添加身高、体重和BMI字段处理
    if (height !== undefined) patientData.height = Number(height);
    if (weight !== undefined) patientData.weight = Number(weight);
    
    // 计算BMI或使用提供的值
    if (bmi !== undefined) {
      patientData.bmi = Number(bmi);
    } else if (height && weight && Number(height) > 0) {
      // 自动计算BMI
      const heightInMeter = Number(height) / 100;
      const bmiValue = Number(weight) / (heightInMeter * heightInMeter);
      patientData.bmi = Math.round(bmiValue * 10) / 10;
    }
    
    // 检查是否已经达到患者数量限制
    const userRecord = await knex('users').where('id', userId).first();
    if (!userRecord) {
      return res.status(404).json({ error: '用户不存在' });
    }
    
    // 获取用户当前患者数量
    const patientCount = await knex('patients')
      .where('user_id', userId)
      .count('id as count')
      .first();
    
    // 检查是否超过限制
    if (patientCount.count >= userRecord.family_member_limit) {
      return res.status(403).json({ 
        error: `已达到最大患者数量限制 (${userRecord.family_member_limit})。请升级账户或删除不需要的患者记录。` 
      });
    }
    
    // 如果标记为本人档案，需要先将其他本人档案重置
    if (isPrimary) {
      await knex('patients')
        .where('user_id', userId)
        .where('is_primary', 1)
        .update({ is_primary: 0 });
    }
    
    // 创建新患者
    const now = new Date().toISOString();
    const patient_id = uuidv4();
    
    await knex('patients').insert({
      id: patient_id,
      user_id: userId,
      ...patientData,
      created_at: now,
      updated_at: now
    });
    
    const newPatientData = await knex('patients').where('id', patient_id).first();
    
    // 转换为驼峰命名格式
    const newPatient = {
      id: newPatientData.id,
      name: newPatientData.name,
      gender: newPatientData.gender,
      phoneNumber: newPatientData.phone_number,
      email: newPatientData.email,
      birthDate: newPatientData.birth_date,
      idCard: newPatientData.id_card,
      medicareCard: newPatientData.medicare_card,
      medicareLocation: newPatientData.medicare_location,
      address: newPatientData.address,
      emergencyContactName: newPatientData.emergency_contact_name,
      emergencyContactPhone: newPatientData.emergency_contact_phone,
      emergencyContactRelationship: newPatientData.emergency_contact_relationship,
      pastMedicalHistory: newPatientData.past_medical_history,
      familyMedicalHistory: newPatientData.family_medical_history,
      allergyHistory: newPatientData.allergy_history,
      bloodType: newPatientData.blood_type,
      lastVisitDate: newPatientData.last_visit_date,
      height: newPatientData.height,
      weight: newPatientData.weight,
      bmi: newPatientData.bmi,
      userId: newPatientData.user_id,
      isPrimary: newPatientData.is_primary,
      createdAt: newPatientData.created_at,
      updatedAt: newPatientData.updated_at
    };
    
    res.status(201).json({ 
      message: '患者创建成功',
      patient: newPatient
    });
  } catch (error) {
    console.error('创建患者失败:', error);
    
    // 处理特定错误
    if (error.code === 'SQLITE_CONSTRAINT') {
      return res.status(400).json({ error: '患者信息已存在（身份证号或医保卡号重复）' });
    }
    
    res.status(500).json({ error: '创建患者失败' });
  }
});

// 更新患者信息
router.put('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const { userId } = req;
    
    // 处理请求数据，支持驼峰命名格式
    const {
      name,
      gender,
      birthDate,
      phoneNumber,
      email,
      address,
      idCard,
      medicareCard,
      medicareLocation,
      bloodType,
      emergencyContactName,
      emergencyContactPhone,
      emergencyContactRelationship,
      pastMedicalHistory,
      familyMedicalHistory,
      allergyHistory,
      isPrimary,
      height,
      weight,
      bmi
    } = req.body;
    
    console.log('接收到的患者更新数据:', req.body);
    
    // 转换为下划线命名格式
    const patientData = {};
    
    // 只更新提供的字段
    if (name !== undefined) patientData.name = name;
    if (gender !== undefined) patientData.gender = gender;
    if (birthDate !== undefined) patientData.birth_date = birthDate;
    if (phoneNumber !== undefined) patientData.phone_number = phoneNumber;
    if (email !== undefined) patientData.email = email;
    if (address !== undefined) patientData.address = address;
    if (idCard !== undefined) patientData.id_card = idCard;
    if (medicareCard !== undefined) patientData.medicare_card = medicareCard;
    if (medicareLocation !== undefined) patientData.medicare_location = medicareLocation;
    if (bloodType !== undefined) patientData.blood_type = bloodType;
    if (emergencyContactName !== undefined) patientData.emergency_contact_name = emergencyContactName;
    if (emergencyContactPhone !== undefined) patientData.emergency_contact_phone = emergencyContactPhone;
    if (emergencyContactRelationship !== undefined) patientData.emergency_contact_relationship = emergencyContactRelationship;
    if (pastMedicalHistory !== undefined) patientData.past_medical_history = pastMedicalHistory;
    if (familyMedicalHistory !== undefined) patientData.family_medical_history = familyMedicalHistory;
    if (allergyHistory !== undefined) patientData.allergy_history = allergyHistory;
    if (isPrimary !== undefined) patientData.is_primary = isPrimary ? 1 : 0;
    // 添加身高、体重和BMI字段处理
    if (height !== undefined) patientData.height = Number(height);
    if (weight !== undefined) patientData.weight = Number(weight);
    if (bmi !== undefined) patientData.bmi = Number(bmi);
    
    // 如果提供了身高和体重但没有BMI，自动计算BMI
    if ((height !== undefined || weight !== undefined) && bmi === undefined) {
      const currentPatient = await knex('patients').where('id', id).first();
      const heightValue = height !== undefined ? Number(height) : currentPatient.height;
      const weightValue = weight !== undefined ? Number(weight) : currentPatient.weight;
      
      if (heightValue && weightValue && heightValue > 0) {
        // BMI = 体重(kg) / (身高(m) * 身高(m))
        const heightInMeter = heightValue / 100;
        const bmiValue = weightValue / (heightInMeter * heightInMeter);
        // 四舍五入到小数点后一位
        patientData.bmi = Math.round(bmiValue * 10) / 10;
      }
    }
    
    // 检查患者是否存在
    const patient = await knex('patients')
      .where('id', id)
      .andWhere('user_id', userId)
      .first();
    
    if (!patient) {
      return res.status(404).json({ error: '患者不存在' });
    }
    
    // 如果标记为本人档案，需要先将其他本人档案重置
    if (isPrimary) {
      await knex('patients')
        .where('user_id', userId)
        .where('is_primary', 1)
        .whereNot('id', id)
        .update({ is_primary: 0 });
    }
    
    // 更新患者信息
    const now = new Date().toISOString();
    patientData.updated_at = now;
    
    await knex('patients')
      .where('id', id)
      .update(patientData);
    
    const updatedPatientData = await knex('patients').where('id', id).first();
    
    // 转换为驼峰命名格式
    const updatedPatient = {
      id: updatedPatientData.id,
      name: updatedPatientData.name,
      gender: updatedPatientData.gender,
      phoneNumber: updatedPatientData.phone_number,
      email: updatedPatientData.email,
      birthDate: updatedPatientData.birth_date,
      idCard: updatedPatientData.id_card,
      medicareCard: updatedPatientData.medicare_card,
      medicareLocation: updatedPatientData.medicare_location,
      address: updatedPatientData.address,
      emergencyContactName: updatedPatientData.emergency_contact_name,
      emergencyContactPhone: updatedPatientData.emergency_contact_phone,
      emergencyContactRelationship: updatedPatientData.emergency_contact_relationship,
      pastMedicalHistory: updatedPatientData.past_medical_history,
      familyMedicalHistory: updatedPatientData.family_medical_history,
      allergyHistory: updatedPatientData.allergy_history,
      bloodType: updatedPatientData.blood_type,
      lastVisitDate: updatedPatientData.last_visit_date,
      height: updatedPatientData.height,
      weight: updatedPatientData.weight,
      bmi: updatedPatientData.bmi,
      userId: updatedPatientData.user_id,
      isPrimary: updatedPatientData.is_primary,
      createdAt: updatedPatientData.created_at,
      updatedAt: updatedPatientData.updated_at
    };
    
    res.json({ 
      message: '患者信息更新成功',
      patient: updatedPatient
    });
  } catch (error) {
    console.error('更新患者失败:', error);
    
    // 处理特定错误
    if (error.code === 'SQLITE_CONSTRAINT') {
      return res.status(400).json({ error: '患者信息更新失败（身份证号或医保卡号重复）' });
    }
    
    res.status(500).json({ error: '更新患者失败' });
  }
});

// 删除患者
router.delete('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const { userId } = req;
    
    // 检查患者是否存在
    const patient = await knex('patients')
      .where('id', id)
      .andWhere('user_id', userId)
      .first();
    
    if (!patient) {
      return res.status(404).json({ error: '患者不存在' });
    }
    
    // 检查是否有关联的病理记录
    const diseaseCount = await knex('diseases')
      .where('patient_id', id)
      .count('id as count')
      .first();
    
    if (diseaseCount.count > 0) {
      return res.status(409).json({ 
        error: `无法删除该患者，因为存在 ${diseaseCount.count} 条关联的病理记录。请先删除相关病理记录。` 
      });
    }
    
    // 删除患者
    await knex('patients')
      .where('id', id)
      .del();
    
    res.json({ message: '患者删除成功' });
  } catch (error) {
    console.error('删除患者失败:', error);
    res.status(500).json({ error: '删除患者失败' });
  }
});

/**
 * 获取指定患者的所有疾病
 * GET /patients/:id/diseases
 */
router.get('/:id/diseases', auth, async (req, res) => {
  try {
    const patient_id = req.params.id;
    const userId = req.userId || req.user_id || req.user.id;
    const userRole = req.user.role;
    const { service_context } = req.query; // 新增参数，表示是否在服务上下文中
    
    console.log(`[DEBUG] 获取患者疾病列表请求: 患者ID=${patient_id}, 用户ID=${userId}, 用户角色=${userRole}, 服务上下文=${service_context || 'false'}`);
    
    // 判断操作上下文 - 普通用户上下文还是服务上下文
    const isServiceContext = service_context === 'true';
    
    // 验证患者是否存在
    const patient = await knex('patients')
      .where({ id: patient_id })
      .first();
    
    if (!patient) {
      console.log(`[DEBUG] 患者不存在: ID=${patient_id}`);
      return res.status(404).json({ message: '患者不存在' });
    }
    
    // 获取患者所有者ID
    const patientUserId = patient.user_id;
    
    // 在普通用户上下文中，任何用户都只能查看自己的患者的疾病
    if (!isServiceContext) {
      console.log(`[DEBUG] 普通用户上下文: 用户只能查看自己的患者的疾病`);
      
      if (patientUserId !== userId) {
        return res.status(403).json({ message: '无权访问此患者信息' });
      }
    }
    // 在服务上下文中，检查是否有权限访问患者的疾病
    else if (userRole === 'SERVICE' || userRole === 'ADMIN') {
      console.log(`[DEBUG] 服务上下文: 检查是否有权限访问患者的疾病`);
      
      // 如果不是患者直接所有者，检查是否有授权关系
      if (patientUserId !== userId) {
        console.log(`[DEBUG] 患者不属于当前用户，检查授权关系`);
        
        // 检查授权关系
        const authorizationExists = await knex('user_authorizations')
          .where({
            'authorized_id': userId,
            'status': 'ACTIVE'
          })
          .where(function() {
            // 1. 检查是否有对特定患者的授权
            this.where('patient_id', patient_id)
            // 2. 或者检查是否有对该患者所有者的全局授权(patient_id为null)
            .orWhere(function() {
              this.where('patient_id', null)
                  .andWhere('authorizer_id', patientUserId);
            });
          })
          .first();
        
        if (!authorizationExists) {
          console.log(`[DEBUG] 用户 ${userId} 没有访问患者 ${patient_id} 的授权`);
      return res.status(403).json({ message: '无权访问此患者信息' });
        }
        
        console.log(`[DEBUG] 用户 ${userId} 有授权访问患者 ${patient_id} 的疾病列表`);
      } else {
        console.log(`[DEBUG] 患者属于当前用户，有权限访问`);
      }
    }
    // 如果是在服务上下文，但用户既不是服务用户也不是管理员
    else {
      console.log(`[DEBUG] 用户角色 ${userRole} 无权访问服务上下文`);
      return res.status(403).json({ message: '无权访问服务管理功能' });
    }
    
    // 获取患者的所有疾病记录
    const diseaseData = await knex('diseases')
      .where({ 
        patient_id, 
        is_deleted: false 
      })
      .orderBy('diagnosis_date', 'desc');
    
    console.log(`[DEBUG] 获取到患者 ${patient_id} 的 ${diseaseData.length} 条疾病记录`);
    
    // 转换为驼峰命名格式
    const diseases = diseaseData.map(disease => ({
      id: disease.id,
      patientId: disease.patient_id,
      userId: disease.user_id,
      name: disease.name,
      diagnosisDate: disease.diagnosis_date,
      stage: disease.stage,
      description: disease.description,
      treatment: disease.treatment,
      status: disease.status,
      isDeleted: disease.is_deleted,
      isPrivate: disease.is_private,
      isActive: disease.is_active,
      isChronic: disease.is_chronic,
      createdAt: disease.created_at,
      updatedAt: disease.updated_at,
      deletedAt: disease.deleted_at
    }));
    
    res.json(diseases);
  } catch (error) {
    console.error('获取患者疾病列表失败:', error);
    res.status(500).json({ message: '获取患者疾病列表失败', error: error.message });
  }
});

// 获取患者操作历史
router.get('/:id/timeline', authenticateUser, async (req, res) => {
  try {
    const { userId } = req;
    const { id } = req.params;
    const { page = 1, limit = 10, action } = req.query;
    
    // 检查患者是否存在并属于当前用户
    const patient = await knex('patients')
      .where({ id, user_id: userId, deleted_at: null })
      .first();
    
    if (!patient) {
      return res.status(404).json({ error: '患者不存在' });
    }
    
    // 构建查询
    let query = knex('patient_logs').where({ patient_id: id });
    
    // 按操作类型筛选
    if (action && ['CREATE', 'UPDATE', 'DELETE'].includes(action)) {
      query = query.where('action', action);
    }
    
    // 获取总记录数
    const totalQuery = query.clone();
    const { count } = await totalQuery.count('id as count').first();
    
    // 分页查询
    const offset = (page - 1) * limit;
    const logs = await query
      .orderBy('created_at', 'desc')
      .limit(limit)
      .offset(offset);
    
    // 构建响应
    res.json({
      logs,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: parseInt(count),
        totalPages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    console.error('获取患者操作历史错误:', error);
    res.status(500).json({ error: '服务器错误' });
  }
});

module.exports = router; 