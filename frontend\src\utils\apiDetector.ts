import axios from 'axios';
import { API_PATHS, API_BASE_URL, WS_BASE_URL } from '../config/apiPaths';

// 存储API路径探测结果
let apiPathMode: 'prefix' | 'noPrefix' | 'both' | 'unknown' = 'unknown';

/**
 * 应用启动时探测API路径模式
 * 判断服务器是否支持带/api前缀和不带前缀的路径
 */
export async function detectApiPathMode(): Promise<void> {
  try {
    console.log('[apiDetector] 开始探测API路径模式...');

    // 使用相对路径
    const withPrefixUrl = '/api/health';
    const noPrefixUrl = '/health';

    let withPrefixWorks = false;
    let noPrefixWorks = false;

    // 探测带前缀的路径
    try {
      const prefixResponse = await axios.get(withPrefixUrl, { timeout: 3000 });
      withPrefixWorks = prefixResponse.status === 200;
      console.log('[apiDetector] 带/api前缀的路径测试成功');
    } catch (error) {
      console.warn('[apiDetector] 带/api前缀的路径测试失败');
    }

    // 探测不带前缀的路径
    try {
      const noPrefixResponse = await axios.get(noPrefixUrl, { timeout: 3000 });
      noPrefixWorks = noPrefixResponse.status === 200;
      console.log('[apiDetector] 不带前缀的路径测试成功');
    } catch (error) {
      console.warn('[apiDetector] 不带前缀的路径测试失败');
    }

    // 设置API路径模式
    if (withPrefixWorks && noPrefixWorks) {
      apiPathMode = 'both';
    } else if (withPrefixWorks) {
      apiPathMode = 'prefix';
    } else if (noPrefixWorks) {
      apiPathMode = 'noPrefix';
    }

    console.log(`[apiDetector] API路径模式探测结果: ${apiPathMode}`);
    localStorage.setItem('apiPathMode', apiPathMode);
  } catch (error) {
    console.error('[apiDetector] API路径探测失败:', error);
  }
}

/**
 * 获取当前API路径模式
 */
export function getApiPathMode(): 'prefix' | 'noPrefix' | 'both' | 'unknown' {
  if (apiPathMode === 'unknown') {
    const savedMode = localStorage.getItem('apiPathMode');
    if (savedMode && ['prefix', 'noPrefix', 'both'].includes(savedMode)) {
      apiPathMode = savedMode as 'prefix' | 'noPrefix' | 'both';
    }
  }
  return apiPathMode;
}

/**
 * 根据当前API路径模式获取合适的API路径
 */
export function getProperApiPath(path: string): string {
  const mode = getApiPathMode();

  if (mode === 'prefix' || mode === 'both') {
    if (path.startsWith('/api')) {
      return path;
    }
    return `/api${path}`;
  } else if (mode === 'noPrefix') {
    return path.startsWith('/api') ? path.substring(4) : path;
  }

  // 默认添加前缀
  return path.startsWith('/api') ? path : `/api${path}`;
}

export const detectApiBaseUrl = () => {
    const currentHost = window.location.hostname;
    const currentPort = window.location.port;

    // 如果是本地开发环境
    if (currentHost === 'localhost' || currentHost === '127.0.0.1') {
        return 'http://localhost:3001';
    }

    // 如果是生产环境
    return 'https://hkb.life';
};

export const detectWsBaseUrl = () => {
    const currentHost = window.location.hostname;
    const currentPort = window.location.port;

    // 如果是本地开发环境
    if (currentHost === 'localhost' || currentHost === '127.0.0.1') {
        // 统一使用3005端口，与后端配置保持一致
        return 'ws://localhost:3005/ws';
    }

    // 如果是生产环境，使用配置的服务器地址和端口
    return 'wss://hkb.life/ws';
};

export const getApiUrl = (path: string) => {
    const baseUrl = detectApiBaseUrl();
    // 确保路径以/api开头
    const apiPath = path.startsWith('/api') ? path : `/api${path}`;
    return `${baseUrl}${apiPath}`;
};

export const getWsUrl = (path: string) => {
    const baseUrl = detectWsBaseUrl();
    // 确保path不以/ws开头，避免重复
    const cleanPath = path.startsWith('/ws') ? path.substring(3) : path;
    return `${baseUrl}${cleanPath}`;
};