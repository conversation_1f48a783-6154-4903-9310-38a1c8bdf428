const axios = require('axios');

// 获取JWT令牌函数
async function getToken() {
  try {
    // 使用测试账号登录获取令牌
    const response = await axios.post('http://localhost:3000/login', {
      username: 'testadmin',
      password: 'test123'
    });
    
    return response.data.token;
  } catch (error) {
    console.error('登录失败，无法获取令牌:', error.response?.data || error.message);
    return null;
  }
}

// 创建多个测试患者
async function createTestPatients(count, token) {
  const headers = { Authorization: `Bearer ${token}` };
  const createdPatients = [];
  
  console.log(`开始创建 ${count} 个测试患者...`);
  
  for (let i = 0; i < count; i++) {
    try {
      const patientData = {
        name: `性能测试-${i}`,
        gender: i % 2 === 0 ? '男' : '女',
        phoneNumber: `1380013${String(i).padStart(4, '0')}`,
        birthDate: '1990-01-01',
        bloodType: ['A', 'B', 'AB', 'O'][i % 4],
        isPrimary: i === 0 ? 1 : 0, // 只有第一个设为本人
      };
      
      const response = await axios.post('http://localhost:3000/patients', patientData, { headers });
      createdPatients.push(response.data.patient);
      
      if ((i + 1) % 5 === 0 || i === count - 1) {
        console.log(`已创建 ${i + 1}/${count} 个测试患者`);
      }
    } catch (error) {
      console.error(`创建第 ${i+1} 个患者失败:`, error.response?.data || error.message);
      // 如果是超出限制错误，就停止创建
      if (error.response?.data?.error?.includes('达到上限')) {
        console.log('已达到患者数量上限，停止创建');
        break;
      }
    }
  }
  
  return createdPatients;
}

// 测试查询性能
async function testQueryPerformance(token) {
  const headers = { Authorization: `Bearer ${token}` };
  
  console.log('\n===== 开始测试查询性能 =====');
  
  // 测试1: 获取所有患者（基础性能）
  console.log('\n测试1: 获取所有患者');
  const start1 = Date.now();
  const response1 = await axios.get('http://localhost:3000/patients', { headers });
  const time1 = Date.now() - start1;
  console.log(`获取 ${response1.data.length} 个患者耗时: ${time1}ms`);
  
  // 测试2: 使用userId条件查询（有索引）
  console.log('\n测试2: 使用userId条件查询（有索引）');
  // 这个请求默认就是使用userId过滤的
  const start2 = Date.now();
  const response2 = await axios.get('http://localhost:3000/patients', { headers });
  const time2 = Date.now() - start2;
  console.log(`通过userId查询患者耗时: ${time2}ms`);
  
  // 测试3: 获取单个患者详情
  if (response1.data.length > 0) {
    const patientId = response1.data[0].id;
    console.log(`\n测试3: 获取单个患者详情 (ID: ${patientId})`);
    const start3 = Date.now();
    await axios.get(`http://localhost:3000/patients/${patientId}`, { headers });
    const time3 = Date.now() - start3;
    console.log(`获取单个患者详情耗时: ${time3}ms`);
  }
  
  console.log('\n测试结论:');
  if (time1 < 500) {
    console.log('✅ 全部查询性能正常 (<500ms)');
  } else {
    console.log('❌ 全部查询性能不佳 (>500ms)，建议优化');
  }
  
  if (time2 < 200) {
    console.log('✅ 条件查询性能正常 (<200ms)，索引有效');
  } else {
    console.log('❌ 条件查询性能不佳 (>200ms)，索引可能无效');
  }
}

// 清理测试数据
async function cleanupTestPatients(token) {
  const headers = { Authorization: `Bearer ${token}` };
  
  console.log('\n===== 清理测试数据 =====');
  try {
    // 获取所有测试患者
    const response = await axios.get('http://localhost:3000/patients', { headers });
    const testPatients = response.data.filter(p => p.name.startsWith('性能测试-'));
    
    console.log(`找到 ${testPatients.length} 个测试患者需要清理`);
    
    // 删除测试患者
    let deletedCount = 0;
    for (const patient of testPatients) {
      try {
        await axios.delete(`http://localhost:3000/patients/${patient.id}`, { headers });
        deletedCount++;
        
        if (deletedCount % 5 === 0 || deletedCount === testPatients.length) {
          console.log(`已删除 ${deletedCount}/${testPatients.length} 个测试患者`);
        }
      } catch (error) {
        console.error(`删除患者 ${patient.id} 失败:`, error.response?.data || error.message);
      }
    }
    
    console.log(`清理完成，共删除 ${deletedCount} 个测试患者`);
  } catch (error) {
    console.error('清理测试数据失败:', error.response?.data || error.message);
  }
}

// 主测试函数
async function runIndexPerformanceTest() {
  console.log('===== 患者API索引性能测试 =====');
  
  // 获取令牌
  const token = await getToken();
  if (!token) {
    console.error('未能获取有效的JWT令牌，测试终止');
    return;
  }
  
  try {
    // 创建测试数据（最多10个患者）
    const createdPatients = await createTestPatients(10, token);
    
    // 测试查询性能
    await testQueryPerformance(token);
    
    // 清理测试数据
    await cleanupTestPatients(token);
    
  } catch (error) {
    console.error('测试失败:', error.message);
  }
  
  console.log('\n===== 索引性能测试完成 =====');
}

// 执行测试
runIndexPerformanceTest(); 