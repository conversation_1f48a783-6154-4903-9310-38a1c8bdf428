import React from 'react';
import { ThemeProvider as MuiThemeProvider, createTheme, ThemeOptions } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { useThemeStore } from './themeStore';
import { PaletteMode } from '@mui/material';

// 定义亮色主题的定制配色
const lightThemeOptions: ThemeOptions = {
  palette: {
    mode: 'light' as PaletteMode,
    primary: {
      main: '#3498db', // 主色调
      light: '#5dade2',
      dark: '#2980b9',
    },
    secondary: {
      main: '#FF6F61', // 辅助色
      light: '#FF8C82',
      dark: '#E55A4D',
    },
    background: {
      default: '#f5f5f5', // 浅灰色背景
      paper: '#ffffff',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
  },
  shape: {
    borderRadius: 8,
  },
};

// 定义暗色主题的定制配色
const darkThemeOptions: ThemeOptions = {
  palette: {
    mode: 'dark' as PaletteMode,
    primary: {
      main: '#3498db', // 保持主色调一致
      light: '#5dade2',
      dark: '#2980b9',
    },
    secondary: {
      main: '#FF6F61',
      light: '#FF8C82',
      dark: '#E55A4D',
    },
    background: {
      default: '#121212', // 暗色模式的整体背景色
      paper: '#1e1e1e', // 暗色模式的卡片背景色
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
  },
  shape: {
    borderRadius: 8,
  },
  // 组件级别的样式重写，用于暗色模式
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          '& .MuiDrawer-paper': {
            borderRight: '1px solid rgba(255, 255, 255, 0.05)',
          }
        },
      }
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundImage: 'none',
        },
      },
    },
  },
};

interface ThemeProviderProps {
  children: React.ReactNode;
}

const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const { isDarkMode } = useThemeStore();

  // 根据当前主题模式创建主题
  const theme = React.useMemo(
    () => createTheme(isDarkMode ? darkThemeOptions : lightThemeOptions),
    [isDarkMode]
  );

  return (
    <MuiThemeProvider theme={theme}>
      <CssBaseline /> {/* 重置CSS，应用基础样式 */}
      {children}
    </MuiThemeProvider>
  );
};

export default ThemeProvider; 