import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  CircularProgress,
  Alert,
  Tooltip,
  Switch,
  FormControlLabel,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  useTheme,
  useMediaQuery,
  SelectChangeEvent,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Snackbar
} from '@mui/material';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import RefreshIcon from '@mui/icons-material/Refresh';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import { useAuthStore } from '../store/authStore';
import authorizationService, { Authorization, CreateAuthorizationParams } from '../services/authorizationService';
import AuthorizationCard from '../components/authorization/AuthorizationCard';
import AuthorizationFilter, { AuthorizationFilterValues } from '../components/authorization/AuthorizationFilter';

// 授权状态映射
const statusMap: Record<string, { label: string, color: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' }> = {
  PENDING_AUTHORIZER: { label: '待确认', color: 'warning' },
  PENDING_AUTHORIZED: { label: '待确认', color: 'warning' },
  ACTIVE: { label: '已激活', color: 'success' },
  REVOKED: { label: '已撤销', color: 'error' }
};

// 角色映射
const roleMap: Record<string, { label: string, color: 'default' | 'primary' | 'secondary' | 'info' }> = {
  ADMIN: { label: '管理员', color: 'secondary' },
  SERVICE: { label: '服务用户', color: 'primary' },
  USER: { label: '普通用户', color: 'info' }
};

// 权限级别映射
const privacyLevelMap: Record<string, { label: string, description: string }> = {
  BASIC: { label: '基础授权', description: '只能查看个人用户的授权信息' },
  STANDARD: { label: '标准授权', description: '可编辑病理，读写自己创建的记录和报告，只读授权用户的记录和报告' },
  FULL: { label: '完整授权', description: '可编辑病理，读写授权用户和自己创建的记录和报告' }
};

// 添加自定义错误接口
interface ApiError extends Error {
  response?: {
    status: number;
    data: any;
    headers: any;
  };
  isAuthError?: boolean;
  code?: string;
}

// 添加错误处理辅助函数
const handleApiError = (error: unknown) => {
  const apiError = error as ApiError;

  // 网络错误处理
  if (apiError.message === 'Network Error' || apiError.code === 'ERR_NETWORK') {
    return { isNetworkError: true };
  }

  // 认证错误处理
  if (apiError.isAuthError || (apiError.response && apiError.response.status === 401)) {
    return { isAuthError: true };
  }

  // 其他错误
  return { isOtherError: true, message: apiError.message || '未知错误' };
};

// 服务授权管理页面组件
const ServiceAuthorizationManage: React.FC = () => {
  const [authorizedsData, setAuthorizedsData] = useState<Authorization[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [searchError, setSearchError] = useState('');
  const [searchStatus, setSearchStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [authToDelete, setAuthToDelete] = useState<Authorization | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [filters, setFilters] = useState<AuthorizationFilterValues>({
    search: '',
    status: 'ALL',
    role: 'ALL',
    privacyLevel: 'ALL'
  });

  // 添加反向授权相关状态
  const [openInviteDialog, setOpenInviteDialog] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [selectedUser, setSelectedUser] = useState('');
  const [selectedPatient, setSelectedPatient] = useState<string | null>(null);
  const [selectedPrivacyLevel, setSelectedPrivacyLevel] = useState('STANDARD');
  const [inviteLoading, setInviteLoading] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);
  const [patientError, setPatientError] = useState('');
  const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    type: 'success' | 'error' | 'info' | 'warning';
  }>({
    open: false,
    message: '',
    type: 'info'
  });

  // 添加页面加载状态和验证状态
  const [pageLoaded, setPageLoaded] = useState(false);
  const [tokenValid, setTokenValid] = useState(false);

  // 添加引用来跟踪数据加载状态，防止重复加载
  const dataLoadedRef = useRef(false);

  const authStore = useAuthStore();
  const user = authStore.user;
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // 创建一个字体大小减小1号的主题
  const smallerFontTheme = createTheme({
    ...theme,
    typography: {
      ...theme.typography,
      h5: {
        ...theme.typography.h5,
        fontSize: '1.1rem',  // 减小h5字体
      },
      h6: {
        ...theme.typography.h6,
        fontSize: '0.9rem', // 减小h6字体
      },
      body1: {
        ...theme.typography.body1,
        fontSize: '0.8rem', // 减小body1字体
      },
      body2: {
        ...theme.typography.body2,
        fontSize: '0.7rem', // 减小body2字体
      },
      caption: {
        ...theme.typography.caption,
        fontSize: '0.6rem', // 减小caption字体
      },
      button: {
        ...theme.typography.button,
        fontSize: '0.7rem', // 减小按钮字体
      },
    },
    components: {
      ...theme.components,
      MuiChip: {
        styleOverrides: {
          label: {
            fontSize: '0.6rem', // 减小Chip标签字体
          }
        }
      },
      MuiListItemText: {
        styleOverrides: {
          primary: {
            fontSize: '0.8rem', // 减小列表项主要文本字体
          },
          secondary: {
            fontSize: '0.7rem', // 减小列表项次要文本字体
          }
        }
      },
      MuiInputLabel: {
        styleOverrides: {
          root: {
            fontSize: '0.7rem', // 减小输入标签字体
          }
        }
      },
      MuiMenuItem: {
        styleOverrides: {
          root: {
            fontSize: '0.7rem', // 减小菜单项字体
          }
        }
      },
      MuiTableCell: {
        styleOverrides: {
          root: {
            fontSize: '0.7rem', // 减小表格单元格字体
          },
          head: {
            fontSize: '0.75rem', // 减小表头字体
            fontWeight: 'bold',
          }
        }
      },
      MuiDialogTitle: {
        styleOverrides: {
          root: {
            fontSize: '1.0rem', // 减小对话框标题字体
          }
        }
      },
      MuiDialogContentText: {
        styleOverrides: {
          root: {
            fontSize: '0.7rem', // 减小对话框内容文本字体
          }
        }
      },
      MuiFormControlLabel: {
        styleOverrides: {
          label: {
            fontSize: '0.7rem', // 减小表单控件标签字体
          }
        }
      },
      MuiAlert: {
        styleOverrides: {
          message: {
            fontSize: '0.7rem', // 减小警告信息字体
          }
        }
      }
    }
  });

  // 验证token，验证用户权限
  useEffect(() => {
    // 避免重复验证
    if (dataLoadedRef.current) {
      console.log('[服务授权管理] 已经验证过token，跳过重复验证');
      return;
    }

    const validateAuth = async () => {
      try {
        // 减少非关键日志输出
        if (process.env.NODE_ENV === 'development') {
          console.log('[服务授权管理] 开始验证用户权限');
        }

        // 从localStorage获取token
        const token = localStorage.getItem('token');
        if (!token) {
          console.error('[服务授权管理] 未找到token');
          setError('您需要先登录才能访问此页面');
          setTokenValid(false);
          setPageLoaded(true);
          return;
        }

        // 预先验证token格式
        try {
          const tokenParts = token.split('.');
          if (tokenParts.length !== 3) {
            setError('您的登录凭证无效，请重新登录');
            setTokenValid(false);
            setPageLoaded(true);
            return;
          }

          // 解析token内容简化版
          const payload = JSON.parse(atob(tokenParts[1]));
          const currentTime = Math.floor(Date.now() / 1000);

          // 检查过期
          if (payload.exp && payload.exp < currentTime) {
            setError('您的登录凭证已过期，请重新登录');
            setTokenValid(false);
            setPageLoaded(true);
            return;
          }

          // 确保token中有用户ID
          if (!payload.id) {
            setError('登录凭证无效，请重新登录');
            setTokenValid(false);
            setPageLoaded(true);
            return;
          }

          // 仅在开发环境记录通过日志
          if (process.env.NODE_ENV === 'development') {
            console.log('[服务授权管理] 本地token验证通过');
          }

          // 尝试获取用户信息
          try {
            const authService = await import('../services/authService');
            const userProfile = await (authService as any).fetchUserProfile();

            if (userProfile) {
              authStore.setUser(userProfile);
            }

            setTokenValid(true);
            setPageLoaded(true);
          } catch (err) {
            // 使用错误处理辅助函数
            const errorInfo = handleApiError(err);

            if (errorInfo.isNetworkError) {
              const userId = localStorage.getItem('userId');
              if (userId) {
                if (!authStore.user || !authStore.user.id) {
                  // TODO: 此处不应仅用id设置用户。如果用户未完全加载，应触发完整的用户资料获取，
                  // 或者重新评估此处的状态管理逻辑。暂时注释以避免类型错误。
                  // authStore.setUser({ id: userId });
                  console.warn('[ServiceAuthorizationManage] Network error, authStore.user is incomplete, but cannot set partial user with only id.');
                }
                setTokenValid(true);
                setError('网络连接问题，部分功能可能受限');
              } else {
                setError('网络连接失败且无法获取用户信息');
                setTokenValid(false);
              }
            }
            // 认证错误
            else if (errorInfo.isAuthError) {
              setError('您的身份验证已过期，请重新登录');
              setTokenValid(false);
            }
            // 其他错误
            else {
              setError('验证失败: ' + errorInfo.message);
              setTokenValid(false);
            }
          }
        } catch (tokenError) {
          setError('登录凭证解析失败，请重新登录');
          setTokenValid(false);
        }
      } catch (e) {
        setError('验证过程中发生错误，请刷新页面重试');
        setTokenValid(false);
      } finally {
        setPageLoaded(true);
      }
    };

    validateAuth();
    // 只在组件挂载时执行一次，不要包含会在validateAuth内部被修改的状态
  }, [authStore]); // 只依赖authStore，不依赖于会变化的error和tokenValid

  // 添加登录跳转函数 - 避免直接改变URL导致的页面刷新问题
  const handleLogin = () => {
    console.log('[服务授权管理] 执行登录跳转函数');
    // 保存当前页面路径，以便登录后可以返回
    const currentPath = window.location.pathname;
    localStorage.setItem('redirectAfterLogin', currentPath);

    console.log('[服务授权管理] 登录跳转前的状态:', {
      当前路径: currentPath,
      token是否存在: !!localStorage.getItem('token'),
      userId是否存在: !!localStorage.getItem('userId')
    });

    // 清除认证信息
    localStorage.removeItem('token');
    localStorage.removeItem('userId');
    useAuthStore.getState().clearAuth();

    console.log('[服务授权管理] 即将跳转到登录页');
    // 使用navigate而不是直接修改location，避免页面刷新和WebSocket断开问题
    window.location.href = '/login';
  };

  // 使用useCallback包装loadAuthorizationData，同时使用错误处理辅助函数
  const loadAuthorizationData = useCallback(async () => {
    if (!tokenValid) {
      console.warn('尝试加载授权数据，但token无效');
      return;
    }

    setLoading(true);
    try {
      console.log('[服务授权管理] 开始加载服务用户被授权列表');

      // 获取用户作为被授权人的授权列表
      const response = await authorizationService.getAuthorizationsAsAuthorized();
      console.log('[服务授权管理] 获取授权列表响应:', response);

      // 请求成功且有数据
      if (response.success) {
        const sortedData = Array.isArray(response.data)
          ? [...response.data].sort((a, b) =>
              new Date(b.createdAt || b.created_at || 0).getTime() - new Date(a.createdAt || a.created_at || 0).getTime()
            )
          : [];

        setAuthorizedsData(sortedData);
        setError(''); // 清除错误信息
      }
      // 请求成功但没有数据
      else if (response.success === false && response.authError) {
        console.error('[服务授权管理] 获取授权列表认证错误:', response.message);
        setError(response.message || '身份验证失败，请重新登录');
        setTokenValid(false);
      }
      // 其他错误情况
      else {
        console.error('[服务授权管理] 获取授权列表失败:', response.message);
        setError(response.message || '加载授权数据失败');
      }
    } catch (apiErr) {
      // 使用错误处理辅助函数
      const errorInfo = handleApiError(apiErr);

      if (errorInfo.isNetworkError) {
        setError('网络请求失败，请检查网络连接');
        setAuthorizedsData([]);
        return;
      }

      // 认证错误
      if (errorInfo.isAuthError) {
        setError('您的身份验证已过期，请重新登录');
        setAuthorizedsData([]);
        setTokenValid(false);
        return;
      }

      // 其他错误
      setError(`加载授权数据失败: ${errorInfo.message}`);
      setAuthorizedsData([]);
    } finally {
      setLoading(false);
    }
  }, [tokenValid]);

  // 当页面加载完成且token有效时，加载授权数据
  useEffect(() => {
    console.log('[服务授权管理] 检查是否要加载数据:', { pageLoaded, tokenValid, dataLoaded: dataLoadedRef.current });
    if (pageLoaded && tokenValid && !dataLoadedRef.current) {
      console.log('[服务授权管理] 页面已加载且token有效，开始加载授权数据');
      dataLoadedRef.current = true; // 标记数据已经开始加载
      loadAuthorizationData();
    }
  }, [pageLoaded, tokenValid, loadAuthorizationData]);

  // 加载筛选数据
  useEffect(() => {
    try {
      // 当authorizationData变化时更新筛选结果
      if (filters.search || filters.status !== 'ALL') {
        // 应用筛选条件...
      } else {
        // 不需要筛选...
      }
    } catch (err) {
      // 使用通用错误处理
      handleGeneralError(err);
    }
  }, [filters, authorizedsData]);

  // 可以添加一个页面状态提示
  if (!pageLoaded) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
        <Typography variant="body2" sx={{ ml: 2 }}>
          正在验证用户权限...
        </Typography>
      </Box>
    );
  }

  if (!tokenValid) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          身份验证失败，请重新登录后访问此页面。
        </Alert>
        <Button
          variant="contained"
          color="primary"
          sx={{ mt: 2 }}
          onClick={handleLogin}
        >
          返回登录
        </Button>
      </Box>
    );
  }

  // 更新授权状态
  const handleUpdateStatus = async (id: string, switchValue: boolean) => {
    try {
      console.log('服务用户端更新授权状态:', { id, switchValue });
      const response = await authorizationService.updateAuthorizationStatus(id, switchValue);
      console.log('服务用户端更新授权状态响应:', response);

      if (response.success && response.data) {
        // 使用后端返回的完整授权数据更新本地状态
        // 这样可以确保状态与后端一致，包括双方的开关状态和授权状态
        setAuthorizedsData(prev =>
          prev.map(auth =>
            auth.id === id
              ? {
                  ...auth,
                  // 更新授权状态
                  status: response.data.status,
                  // 更新开关状态 - 使用Boolean()函数进行类型转换
                  authorized_switch: Boolean(response.data.authorized_switch),
                  authorizer_switch: Boolean(response.data.authorizer_switch)
                }
              : auth
          )
        );

        // 显示状态变更通知
        let statusMessage = '';
        switch(response.data.status) {
          case 'ACTIVE':
            statusMessage = '授权已激活';
            break;
          case 'REVOKED':
            statusMessage = '授权已撤销';
            break;
          case 'PENDING_AUTHORIZED':
            statusMessage = '等待被授权人确认';
            break;
          case 'PENDING_AUTHORIZER':
            statusMessage = '等待授权人确认';
            break;
          default:
            statusMessage = '授权状态已更新';
        }

        setNotification({
          open: true,
          message: statusMessage,
          type: 'success'
        });
      } else {
        setError(response.message || '更新授权状态失败');
      }
    } catch (err) {
      // 使用错误处理辅助函数
      const errorInfo = handleApiError(err);
      console.error('服务用户端更新授权状态失败:', errorInfo);
      setError(errorInfo.message || '更新授权状态失败');
    }
  };

  // 打开删除授权对话框
  const handleOpenDeleteDialog = (auth: Authorization) => {
    setAuthToDelete(auth);
    setOpenDeleteDialog(true);
  };

  // 关闭删除授权对话框
  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    setAuthToDelete(null);
  };

  // 删除授权
  const handleDeleteAuthorization = async () => {
    if (!authToDelete) return;

    setDeleteLoading(true);
    try {
      console.log('服务用户端删除授权:', { id: authToDelete.id });
      const response = await authorizationService.deleteRevokedAuthorization(authToDelete.id);

      if (response.success) {
        // 删除成功后更新列表，从本地数据中移除
        setAuthorizedsData(prev => prev.filter(auth => auth.id !== authToDelete.id));
        handleCloseDeleteDialog();
      }
    } catch (err) {
      // 使用错误处理辅助函数
      const errorInfo = handleApiError(err);
      console.error('服务用户端删除授权失败:', errorInfo);
      setError(errorInfo.message || '删除授权失败');
    } finally {
      setDeleteLoading(false);
    }
  };

  // 处理特殊情况的错误处理，通常发生在一般性的try/catch之外
  const handleGeneralError = (err: unknown) => {
    // 使用错误处理辅助函数
    const errorInfo = handleApiError(err);

    if (errorInfo.isAuthError) {
      setError('您的登录已过期，请重新登录');
      setTokenValid(false);
    } else {
      setError(errorInfo.message || '操作失败');
    }
    setAuthorizedsData([]);
  };

  // 筛选处理
  const handleFilter = (newFilters: AuthorizationFilterValues) => {
    setFilters(newFilters);
  };

  // 重置筛选
  const handleResetFilter = () => {
    setFilters({
      search: '',
      status: 'ALL',
      role: 'ALL',
      privacyLevel: 'ALL'
    });
  };

  // 筛选数据
  const filteredData = authorizedsData.filter(auth => {
    // 搜索筛选 - 扩展到多个字段
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      const username = auth.authorizer?.username?.toLowerCase() || '';
      const email = auth.authorizer?.email?.toLowerCase() || '';
      const phone = auth.authorizer?.phone_number?.toLowerCase() || '';
      const patientName = auth.patient?.name?.toLowerCase() || '';

      // 如果搜索词不匹配任何一个字段，就过滤掉
      if (!username.includes(searchLower) &&
          !email.includes(searchLower) &&
          !phone.includes(searchLower) &&
          !patientName.includes(searchLower)) {
        return false;
      }
    }

    // 状态筛选
    if (filters.status !== 'ALL') {
      if (filters.status === 'ACTIVE' && auth.status !== 'ACTIVE') {
        return false;
      } else if (filters.status === 'INACTIVE' && auth.status === 'ACTIVE') {
        // 未激活包括所有非ACTIVE状态
        return false;
      }
    }

    return true;
  });

  // 渲染移动端卡片视图
  const renderMobileCards = () => {
    if (!filteredData || filteredData.length === 0) {
      return (
        <Box sx={{ py: 4, textAlign: 'center' }}>
          <Typography color="text.secondary">
            {authorizedsData.length > 0 ? '没有符合筛选条件的授权记录' : '暂无授权记录，当患者授权您访问他们的资料时，将在此处显示。'}
          </Typography>
          {authorizedsData.length > 0 && filteredData.length === 0 && (
            <Button
              variant="text"
              color="primary"
              onClick={handleResetFilter}
              sx={{ mt: 1 }}
            >
              清除筛选条件
            </Button>
          )}
        </Box>
      );
    }

    return (
      <Box sx={{ mt: 2 }}>
        {filteredData.map((auth) => (
          <AuthorizationCard
            key={auth.id}
            auth={auth}
            isAuthorizer={false}
            onSwitchChange={handleUpdateStatus}
            onDelete={handleOpenDeleteDialog}
          />
        ))}
      </Box>
    );
  };

  // 修改错误处理显示部分
  if (!tokenValid && pageLoaded) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error || '身份验证失败，请重新登录后访问此页面。'}
        </Alert>
        <Button
          variant="contained"
          color="primary"
          onClick={handleLogin}
        >
          返回登录
        </Button>
      </Box>
    );
  }

  // 如果有错误且含"登录"字样，添加登录按钮
  const renderErrorWithLoginButton = () => {
    if (error && (error.includes('登录') || error.includes('身份验证') || error.includes('会话') || error.includes('认证') || error.includes('过期'))) {
      return (
        <Box sx={{ mb: 2 }}>
          <Alert severity="error" sx={{ mb: 1 }}>
            {error}
          </Alert>
          <Button
            variant="contained"
            color="primary"
            onClick={handleLogin}
            sx={{ mr: 1 }}
          >
            重新登录
          </Button>
          <Button
            variant="outlined"
            onClick={() => window.location.reload()}
          >
            刷新页面
          </Button>
        </Box>
      );
    } else if (error) {
      return (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      );
    }
    return null;
  };

  // 打开发起授权邀请对话框
  const handleOpenInviteDialog = () => {
    setOpenInviteDialog(true);
    setSearchQuery('');
    setSearchResults([]);
    setSelectedUser('');
    setSelectedPatient(null);
    setSelectedPrivacyLevel('STANDARD');
    setPatientError('');
  };

  // 关闭发起授权邀请对话框
  const handleCloseInviteDialog = () => {
    setOpenInviteDialog(false);
  };

  // 搜索普通用户
  const handleSearchNormalUsers = async () => {
    if (!searchQuery.trim()) {
      setSearchError('请输入搜索关键词');
      return;
    }

    setSearchLoading(true);
    setSearchError('');
    setPatientError('');
    setSearchStatus('loading');

    try {
      const response = await authorizationService.searchNormalUsers(searchQuery);

      if (response.success) {
        setSearchResults(response.data);
        setSearchStatus(response.data.length > 0 ? 'success' : 'error');
        if (response.data.length === 0) {
          setSearchError('未找到匹配的普通用户');
        }
      } else {
        setSearchStatus('error');
        setSearchError(response.message || '搜索失败');
      }
    } catch (error: any) {
      setSearchStatus('error');
      setSearchError(error.message || '搜索普通用户失败');
    } finally {
      setSearchLoading(false);
    }
  };

  // 处理搜索输入变化
  const handleSearchQueryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    if (searchStatus === 'error') {
      setSearchStatus('idle');
      setSearchError('');
    }
  };

  // 处理用户选择变化
  const handleUserChange = (e: SelectChangeEvent<string>) => {
    const userId = e.target.value;
    setSelectedUser(userId);
    setSelectedPatient(null); // 重置患者选择
    setPatientError('');
  };

  // 检查是否存在重复授权
  const checkExistingAuthorization = (userId: string, patientId: string | null): boolean => {
    return authorizedsData.some(auth =>
      auth.authorizerId === userId &&
      (patientId === null || patientId === undefined || auth.patientId === patientId) &&
      auth.status !== 'REVOKED'
    );
  };

  // 检查是否为自授权
  const isSelfAuthorization = (userId: string): boolean => {
    return userId === user?.id;
  };

  // 创建授权邀请
  const handleCreateInvite = async () => {
    if (!selectedUser) {
      setSearchError('请选择一个普通用户');
      return;
    }

    // 检查是否试图与自己创建授权
    if (isSelfAuthorization(selectedUser)) {
      setSearchError('不能向自己发送授权邀请');
      setNotification({
        open: true,
        message: '不能向自己发送授权邀请，请选择其他用户',
        type: 'error'
      });
      return;
    }

    // 检查是否存在重复授权
    if (checkExistingAuthorization(selectedUser, selectedPatient)) {
      setSearchError('已存在针对该用户' + (selectedPatient ? '和患者' : '') + '的授权关系');
      return;
    }

    setInviteLoading(true);
    try {
      // 构建授权请求参数 - 注意这里的authorizer和authorized是反过来的
      const authData: CreateAuthorizationParams = {
        // 使用驼峰命名
        authorizerId: selectedUser, // 普通用户成为授权人
        authorizedId: user?.id || '', // 当前服务用户成为被授权人
        patientId: selectedPatient || undefined, // 添加患者ID
        privacyLevel: selectedPrivacyLevel,
        // 同时提供下划线命名，确保兼容性
        authorizer_id: selectedUser,
        authorized_id: user?.id || '',
        patient_id: selectedPatient || undefined,
        privacy_level: selectedPrivacyLevel
      };

      const response = await authorizationService.createAuthorization(authData);

      if (response.success) {
        // 关闭对话框并显示成功通知
        handleCloseInviteDialog();
        setNotification({
          open: true,
          message: '授权邀请已成功发送',
          type: 'success'
        });

        // 重新加载数据
        loadAuthorizationData();
      } else {
        setSearchError(response.message || '创建授权邀请失败');
      }
    } catch (error: any) {
      setSearchError(error.message || '创建授权邀请失败');
    } finally {
      setInviteLoading(false);
    }
  };

  // 关闭通知
  const handleCloseNotification = () => {
    setNotification({
      ...notification,
      open: false
    });
  };

  return (
    <ThemeProvider theme={smallerFontTheme}>
      <Box sx={{ p: 3 }}>
        <Box sx={{ mb: 3 }}>
          <Typography variant="h5" component="h1" gutterBottom>服务授权管理</Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            在这里管理患者授权给您作为服务用户的各项权限。您可以确认或拒绝授权请求，以及查看已授权的患者资料访问权限。
          </Typography>

          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleOpenInviteDialog}
              size={isMobile ? "small" : "medium"}
              sx={{ mr: 1 }}
            >
              发起授权邀请
            </Button>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={loadAuthorizationData}
              size={isMobile ? "small" : "medium"}
            >
              刷新
            </Button>
          </Box>
        </Box>

        {/* 使用AuthorizationFilter组件 */}
        <AuthorizationFilter
          onFilter={handleFilter}
          onReset={handleResetFilter}
          isAuthorizer={false}
        />

        {renderErrorWithLoginButton()}

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            {isMobile ? (
              // 移动端卡片视图
              renderMobileCards()
            ) : (
              // 桌面端表格视图
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>授权用户</TableCell>
                      <TableCell>角色</TableCell>
                      <TableCell>授权状态</TableCell>
                      <TableCell>权限级别</TableCell>
                      <TableCell>创建时间</TableCell>
                      <TableCell align="center">操作</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {filteredData.length > 0 ? (
                      filteredData.map((auth) => (
                        <TableRow key={auth.id}>
                          <TableCell>
                            {auth.authorizer?.username || '未知用户'}
                          </TableCell>
                          <TableCell>
                            {auth.authorizer?.role && roleMap[auth.authorizer.role] ? (
                              <Chip
                                label={roleMap[auth.authorizer.role].label}
                                size="small"
                                color={roleMap[auth.authorizer.role].color}
                              />
                            ) : (
                              <Chip label="未知角色" size="small" color="default" />
                            )}
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={
                                auth.status === 'ACTIVE' ? statusMap.ACTIVE.label :
                                auth.status === 'REVOKED' ? statusMap.REVOKED.label :
                                '待确认'
                              }
                              color={
                                auth.status === 'ACTIVE' ? statusMap.ACTIVE.color :
                                auth.status === 'REVOKED' ? statusMap.REVOKED.color :
                                statusMap.PENDING_AUTHORIZER.color
                              }
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Chip label={privacyLevelMap[auth.privacy_level || auth.privacyLevel]?.label || auth.privacy_level || auth.privacyLevel || '标准'} size="small" />
                          </TableCell>
                          <TableCell>
                            {auth.created_at || auth.createdAt ?
                              new Date(auth.created_at || auth.createdAt).toString() !== 'Invalid Date' ?
                                new Date(auth.created_at || auth.createdAt).toLocaleString() :
                                '日期格式错误'
                              : '--'}
                          </TableCell>
                          <TableCell align="center">
                            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                              <FormControlLabel
                                control={
                                  <Switch
                                    checked={!!auth.authorized_switch}
                                    onChange={(e) => {
                                      console.log('服务用户端Switch操作:', {
                                        授权ID: auth.id,
                                        授权状态: auth.status,
                                        当前用户: user?.id,
                                        被授权人ID: auth.authorized_id,
                                        开关状态: e.target.checked,
                                        当前开关状态: auth.authorized_switch,
                                        字段名: Object.keys(auth),
                                        授权数据: auth
                                      });
                                      handleUpdateStatus(auth.id, e.target.checked);
                                    }}
                                    disabled={auth.status === 'REVOKED'}
                                    color="success"
                                    sx={{
                                      '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                                        backgroundColor: '#4caf50',
                                      },
                                      '& .MuiSwitch-switchBase.Mui-checked': {
                                        color: '#4caf50',
                                        '&:hover': {
                                          backgroundColor: 'rgba(76, 175, 80, 0.08)',
                                        },
                                      },
                                      '& .MuiSwitch-track': {
                                        backgroundColor: '#f44336',
                                      },
                                    }}
                                  />
                                }
                                label=""
                                labelPlacement="end"
                              />

                              {auth.status === 'REVOKED' && (
                                <Tooltip title="删除已撤销的授权记录">
                                  <IconButton
                                    color="error"
                                    size="small"
                                    onClick={() => handleOpenDeleteDialog(auth)}
                                  >
                                    <DeleteIcon />
                                  </IconButton>
                                </Tooltip>
                              )}
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={6} align="center">
                          {authorizedsData.length > 0 ? '没有符合筛选条件的授权记录' : '暂无授权记录，当患者授权您访问他们的资料时，将在此处显示。'}
                          {authorizedsData.length > 0 && filteredData.length === 0 && (
                            <Box sx={{ mt: 1 }}>
                              <Button
                                variant="text"
                                color="primary"
                                onClick={handleResetFilter}
                              >
                                清除筛选条件
                              </Button>
                            </Box>
                          )}
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </>
        )}

        {/* 删除授权对话框 */}
        <Dialog
          open={openDeleteDialog}
          onClose={handleCloseDeleteDialog}
          aria-labelledby="delete-dialog-title"
          fullScreen={isMobile}
        >
          <DialogTitle id="delete-dialog-title">确认删除授权记录</DialogTitle>
          <DialogContent>
            <DialogContentText>
              您确定要删除来自 <strong>{authToDelete?.authorizer?.username || '未知用户'}</strong> 的已撤销授权记录吗？此操作不可撤销。
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDeleteDialog} disabled={deleteLoading}>
              取消
            </Button>
            <Button
              onClick={handleDeleteAuthorization}
              color="error"
              variant="contained"
              disabled={deleteLoading}
              startIcon={deleteLoading ? <CircularProgress size={20} /> : <DeleteIcon />}
            >
              {deleteLoading ? '删除中...' : '确认删除'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* 反向授权邀请对话框 */}
        <Dialog open={openInviteDialog} onClose={handleCloseInviteDialog} maxWidth="sm" fullWidth>
          <DialogTitle>发起授权邀请</DialogTitle>
          <DialogContent>
            <DialogContentText>
              搜索并选择一个普通用户及其患者，向其发送授权邀请。当用户接受邀请后，您将获得查看其医疗信息的权限。
            </DialogContentText>

            {/* 搜索框 */}
            <Box sx={{ mt: 2, display: 'flex', alignItems: 'center' }}>
              <TextField
                label="输入用户名、手机号或患者姓名搜索"
                value={searchQuery}
                onChange={handleSearchQueryChange}
                fullWidth
                variant="outlined"
                sx={{ mr: 1 }}
                error={searchStatus === 'error'}
                helperText={searchStatus === 'error' ? searchError : ''}
              />
              <Button
                variant="contained"
                onClick={handleSearchNormalUsers}
                disabled={searchLoading}
              >
                {searchLoading ? <CircularProgress size={24} /> : '搜索'}
              </Button>
            </Box>

            {/* 搜索结果 */}
            {searchStatus === 'success' && (
              <FormControl fullWidth sx={{ mt: 2 }}>
                <InputLabel id="select-user-label">选择用户</InputLabel>
                <Select
                  labelId="select-user-label"
                  value={selectedUser}
                  onChange={handleUserChange}
                  label="选择用户"
                  fullWidth
                >
                  {searchResults.map((user) => (
                    <MenuItem key={user.id} value={user.id}>
                      {user.username} ({user.phone_number || user.email || '无联系方式'})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}

            {/* 患者选择 - 只在选择了用户后显示 */}
            {selectedUser && (
              <FormControl fullWidth sx={{ mt: 2 }} error={!!patientError}>
                <InputLabel id="select-patient-label">选择患者</InputLabel>
                <Select
                  labelId="select-patient-label"
                  value={selectedPatient || ''}
                  onChange={(e) => setSelectedPatient(e.target.value || null)}
                  label="选择患者"
                  fullWidth
                >
                  <MenuItem value="">
                    <em>所有患者（通用授权）</em>
                  </MenuItem>
                  {searchResults
                    .find(user => user.id === selectedUser)?.patients
                    .map((patient: any) => (
                      <MenuItem key={patient.id} value={patient.id}>
                        {patient.name} ({patient.gender === 'MALE' ? '男' : patient.gender === 'FEMALE' ? '女' : '其他'})
                      </MenuItem>
                    ))}
                </Select>
                {patientError && (
                  <Typography color="error" variant="caption">
                    {patientError}
                  </Typography>
                )}
              </FormControl>
            )}

            {/* 隐私级别选择 */}
            <FormControl fullWidth sx={{ mt: 2 }}>
              <InputLabel id="privacy-level-label">隐私级别</InputLabel>
              <Select
                labelId="privacy-level-label"
                value={selectedPrivacyLevel}
                onChange={(e) => setSelectedPrivacyLevel(e.target.value)}
                label="隐私级别"
                fullWidth
              >
                <MenuItem value="BASIC">基础权限（只读信息）</MenuItem>
                <MenuItem value="STANDARD">标准权限（读写增加的信息）</MenuItem>
                <MenuItem value="FULL">完整权限（读写所有信息）</MenuItem>
              </Select>
            </FormControl>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseInviteDialog}>取消</Button>
            <Button
              variant="contained"
              onClick={handleCreateInvite}
              disabled={inviteLoading || !selectedUser}
            >
              {inviteLoading ? <CircularProgress size={24} /> : '发送邀请'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* 通知消息 */}
        <Snackbar
          open={notification.open}
          autoHideDuration={5000}
          onClose={handleCloseNotification}
          message={notification.message}
        />
      </Box>
    </ThemeProvider>
  );
};

export default ServiceAuthorizationManage;