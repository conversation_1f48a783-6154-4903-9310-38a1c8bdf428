require('dotenv').config();
const knex = require('knex');
// const path = require('path'); // 不再需要 path 来拼接 SQLite 文件路径

// 获取环境配置
const env = process.env.NODE_ENV || 'development';

// 注意：请确保在您的环境变量中设置了以下 PostgreSQL 连接参数：
// DB_HOST, DB_USER, DB_PASSWORD, DB_NAME, DB_PORT (可选, 默认 5432)

// 数据库连接配置
const dbConfig = {
  client: 'pg',
  connection: {
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    port: parseInt(process.env.DB_PORT, 10),
    // SSL 配置（如果需要）
    ...(process.env.DB_SSL === 'true' && {
      ssl: {
        rejectUnauthorized: process.env.DB_SSL_REJECT_UNAUTHORIZED === 'true'
      }
    })
  },
  // useNullAsDefault: true, // SQLite 特有的配置，PostgreSQL 不需要或有不同处理方式，Knex 会处理
  pool: {
    min: parseInt(process.env.DB_POOL_MIN, 10) || 2,
    max: parseInt(process.env.DB_POOL_MAX, 10) || 10,
    acquireTimeoutMillis: parseInt(process.env.DB_POOL_ACQUIRE_TIMEOUT, 10) || 30000,
    createTimeoutMillis: parseInt(process.env.DB_POOL_CREATE_TIMEOUT, 10) || 30000,
    destroyTimeoutMillis: parseInt(process.env.DB_POOL_DESTROY_TIMEOUT, 10) || 5000,
    idleTimeoutMillis: parseInt(process.env.DB_POOL_IDLE_TIMEOUT, 10) || 30000,
    reapIntervalMillis: parseInt(process.env.DB_POOL_REAP_INTERVAL, 10) || 1000,
    createRetryIntervalMillis: parseInt(process.env.DB_POOL_CREATE_RETRY_INTERVAL, 10) || 200
  }
};

// 创建knex实例
const db = knex(dbConfig);

// 测试数据库连接
db.raw('SELECT 1 AS result')
  .then((result) => {
    if (result.rows && result.rows[0] && result.rows[0].result === 1) {
      console.log(`[${env}] PostgreSQL 数据库连接成功`);
    } else {
      console.error(`[${env}] PostgreSQL 数据库连接测试未返回预期结果:`, result);
    }
  })
  .catch(err => {
    console.error(`[${env}] PostgreSQL 数据库连接失败:`, err);
    console.error('请检查您的环境变量是否正确配置，并且 PostgreSQL 服务正在运行。');
    process.exit(1); // 如果数据库连接失败，终止应用
  });

// PostgreSQL 原生支持 TEXT 类型，不再需要自定义 LONGTEXT 函数
// const { TEXT } = require('sequelize'); // 如果项目其他地方未使用 Sequelize，可以移除
// function LONGTEXT() {
//   return TEXT('long'); // PostgreSQL 中应直接使用 'text'
// }

module.exports = db;