# 命名规范迁移执行计划

按照以下步骤执行命名规范迁移：

## 第一步：准备工作

1. **确保代码已提交到版本控制**
   ```bash
   cd D:\AZ
   git status
   git add .
   git commit -m "备份迁移前代码状态"
   ```

2. **备份数据库**
   ```bash
   cd D:\AZ\backend
   node -e "const fs=require('fs');const path=require('path');const src='./db.sqlite';const dest=`./db_backup_${new Date().toISOString().replace(/:/g,'-')}.sqlite`;fs.copyFileSync(src,dest);console.log(`数据库已备份到 ${dest}`);"
   ```

## 第二步：执行迁移

1. **检查当前代码中的直接knex查询**
   ```bash
   cd D:\AZ\backend
   node findDirectQueries.js
   ```
   记录需要修改的文件列表。

2. **执行数据库迁移**
   ```bash
   cd D:\AZ\backend
   node runMigration.js
   ```
   
3. **验证数据库表结构**
   ```bash
   cd D:\AZ\backend
   node checkAllTables.js
   ```
   确认所有表已转换为下划线命名。

## 第三步：修改代码

1. **根据findDirectQueries.js的结果修改所有涉及的文件**
   - 对每个标识出的文件，修改直接使用knex的查询，将字段名从驼峰命名改为下划线命名
   - recordRoutes.js文件已经更新，可以作为参考

2. **添加字段映射转换**
   - 确保所有API响应在返回给前端前，将下划线命名转换回驼峰命名
   - 参考recordRoutes.js中的转换方式

## 第四步：测试验证

1. **启动后端服务**
   ```bash
   cd D:\AZ\backend
   npm run dev
   ```

2. **启动前端服务**
   ```bash
   cd D:\AZ\frontend
   npm run dev
   ```

3. **执行功能测试**
   - 测试记录的创建、获取、更新和删除
   - 测试患者的创建、获取、更新和删除
   - 测试疾病的创建、获取、更新和删除
   - 测试用户相关功能
   - 确保所有功能正常工作

## 第五步：发布部署

1. **合并代码至主分支**
   ```bash
   cd D:\AZ
   git add .
   git commit -m "统一使用下划线命名规范"
   git push
   ```

2. **部署到生产环境**
   - 在部署前，确保已有数据库备份
   - 按照标准部署流程部署应用

## 回滚计划

如果迁移过程中遇到问题，可以按以下步骤回滚：

1. **回滚数据库迁移**
   ```bash
   cd D:\AZ\backend
   npx knex migrate:down
   ```

2. **如果回滚失败，使用备份恢复数据库**
   ```bash
   cd D:\AZ\backend
   cp [备份文件名] db.sqlite
   ```

3. **恢复代码**
   ```bash
   cd D:\AZ
   git checkout [迁移前的提交]
   ```

## 后续行动

1. **更新文档**
   - 确保NAMING_CONVENTION.md文档分享给所有开发人员
   - 将命名规范添加到开发指南中

2. **建立代码质量检查**
   - 添加ESLint规则，检查直接knex查询是否使用下划线命名
   - 添加pre-commit钩子，确保合规性

3. **定期检查代码库**
   - 使用findDirectQueries.js脚本定期检查代码库，确保新增代码遵循命名规范 