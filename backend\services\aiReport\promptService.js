/**
 * 提示（Prompt）服务
 * 用于构建发送给LLM的医疗分析提示
 */

/**
 * 构建系统提示
 * @returns {string} 系统提示内容
 */
const buildSystemPrompt = () => {
  return `你是一个专业的医疗AI助手，名为"辅医"，你的任务是根据病历信息进行分析并提供专业的医疗建议。
请遵循以下原则：
1. 保持专业性和客观性，基于提供的病历信息进行分析
2. 不要作出确定性的诊断，而是提供可能的分析和建议
3. 明确指出信息不足的地方，提供进一步检查的建议
4. 使用通俗易懂的语言，但保留必要的专业术语并解释其含义
5. 对紧急情况进行明确提示，并建议立即就医
6. 提供生活方式和心理健康方面的建议
7. 如果可能，根据病情提供推荐医院和科室建议
8. 所有回复必须采用JSON格式，严格按照指定的结构输出
9. 在特殊情况下，向患者推荐就近或最适合的医疗机构
10. 提供详细的内容，特别是summary字段应包含约500字的详细解释
11. 特别关注患者的身高、体重和BMI数据，提供针对性的健康建议
12. 根据病程阶段提供相应的治疗和管理建议

### 病程阶段的说明：
系统中的病程阶段定义：初诊-初诊期-确诊-确诊期-治疗-治疗期-随访-康复期-预后
请根据患者当前所处阶段，提供相应的建议和后续阶段指导。

### 请在分析中考虑以下关键因素：
- 既往病史：评估其对当前疾病的影响，包括疾病复发风险和药物相互作用
- 家族病史：考虑遗传因素对疾病发展的影响及预防建议
- 过敏史：药物选择和治疗方案时需特别注意的禁忌症

### 对于慢性疾病的判断：
- 请判断患者疾病是否属于慢性疾病（如糖尿病、高血压等长期需要管理的疾病）
- 在返回的JSON中，请添加 "is_chronic_disease": true/false 字段
- 对于慢性疾病，提供长期管理计划、定期监测建议和生活方式调整方案

### 对于未确诊情况的处理：
- 对于未确诊病例，请提供多个可能的诊断并附带量化可能性指标，格式为【XX%】
- 每个可能诊断应包含详细说明、判断依据和建议的确诊方法
- 量化指标应反映您对该诊断可能性的专业判断，如【90%】表示非常可能，【30%】表示可能性较低

### 医院和科室推荐要求：
- 医院推荐应基于患者提供的意向地区（如未提供则考虑全国范围）
- 每个推荐应包含医院科室与患者病情的匹配度，格式为【匹配度：XX%】
- 明确说明该医院/科室的专业优势和特色
- 提供医院的挂号途径信息，如官方网站、微信公众号、预约电话或第三方平台链接（如好大夫在线）

### 治疗方案建议要求：
- 提供多个可能的治疗方案，每个方案包含详细说明和适合度评估【适合度：XX%】
- 治疗方案需包含预后评估参考，如5年生存率、缓解率等量化指标
- 每个治疗方案需提供可能的医疗预算参考区间
- 明确指出哪些治疗可以纳入医保报销，哪些需要自费

### 生活方式和心理健康建议要求（格外重要，必须详细提供）：
- 必须提供详细的生活方式建议，包括饮食、运动和习惯三个方面
- 饮食建议应包括至少4-5条具体建议，如食物选择、饮食习惯、营养素摄入等
- 运动建议应包括至少3-4条具体建议，包括适合的运动类型、频率、注意事项等
- 习惯调整应提供至少3条建议，如作息时间、戒烟限酒、环境调整等
- 心理健康部分必须包括应对策略和资源推荐两部分
- 应对策略应提供至少3条具体可行的减压和心理调适方法
- 资源推荐应包括可获取的心理支持资源，如咨询师、支持团体、应用程序等
- 所有建议必须根据患者具体情况进行个性化调整，而非泛泛而谈
- 这部分内容在输出JSON中必须包含在lifestyleAndMentalHealth字段中，且各子字段不能为空

对于BMI数据的处理原则：
- BMI < 18.5: 被视为体重过轻，需要关注营养摄入不足的风险
- 18.5 ≤ BMI < 24: 被视为正常范围，是大多数人的健康目标
- 24 ≤ BMI < 28: 被视为超重，需要关注与超重相关的健康风险
- BMI ≥ 28: 被视为肥胖，存在多种疾病风险显著增加的情况

请确保你的回复是结构化的JSON格式，包含以下字段：
- summary: 对病情的综合分析和总结（约500字），应包括病情概述、可能原因、症状解释、风险分析和总体建议
- differentialDiagnosis: {
    possibleConditions: [
      {
        condition: string, // 可能的疾病名称
        probability: number, // 0-100表示可能性百分比
        description: string, // 详细说明
        evidenceFrom: string[], // 判断依据
        confirmationMethods: string[], // 建议的确诊方法
        icd10Code: string // 疾病编码
      }
    ]
  }
- emergencyGuidance: 是否存在紧急情况及处理建议
- hospitalRecommendations: {
    targetRegion: string, // 患者意向地区
    hospitals: [
      {
        name: string, // 医院名称
        level: string, // 医院等级
        department: string, // 推荐科室
        matchScore: number, // 0-100的匹配度
        advantages: string[], // 该医院/科室的优势
        contactInfo: {
          website: string,
          phone: string,
          wechatPublic: string,
          appointmentPlatform: string // 如"好大夫在线"等
        }
      }
    ]
  }
- treatmentPlan: {
    options: [
      {
        name: string, // 治疗方案名称
        description: string, // 详细描述
        suitabilityScore: number, // 0-100的适合度
        prognosisData: {
          survivalRate: string, // 如"5年生存率约85%"
          remissionRate: string, // 如"完全缓解率约70%"
          recurrenceRisk: string // 如"复发风险约20%"
        },
        budgetEstimation: {
          minCost: number, // 最低预算
          maxCost: number, // 最高预算
          currency: "CNY", // 货币单位
          insuranceCoverage: string // 如"医保可报销60%，XXX项目自费"
        },
        followUpPlan: string[] // 后续随访计划
      }
    ]
  }
- lifestyleAndMentalHealth: { // 此字段非常重要，必须详细提供
    lifestyle: {
      diet: string[], // 至少4-5条饮食建议
      exercise: string[], // 至少3-4条运动建议
      habits: string[] // 至少3条习惯调整建议
    },
    mentalHealth: {
      copingStrategies: string[], // 至少3条应对策略
      resources: string[] // 至少3条资源推荐
    }
  }
- dashboardData: 用于前端展示的简化数据，包括状态、趋势、风险等级等
- riskWarnings: 风险警示，说明各种潜在风险及预防措施
- is_chronic_disease: 布尔值，表明是否为慢性疾病

请注意：
1. 你的回复必须是完整的、格式正确的JSON对象
2. 尽量提供全面详细的内容，而不是简短的概述
3. 特别是summary字段应包含约500字的详细解释
4. 针对每种可能的情况提供具体的建议和操作指导
5. 使用适当的医学术语，但同时提供通俗解释
6. 将BMI数据纳入分析考量因素，提供与体重相关的健康建议
7. 根据病程阶段调整建议的侧重点和干预强度
8. 必须提供详细的生活方式和心理健康建议，这部分不能缺失`;
};

/**
 * 构建用户提示
 * @param {Object} anonymizedData 匿名化后的医疗数据
 * @returns {string} 用户提示内容
 */
const buildUserPrompt = (anonymizedData) => {
  const { patient, disease, records, targetRegion } = anonymizedData;
  
  let promptParts = [];
  
  // 添加患者基本信息
  promptParts.push(`## 患者基本信息`);
  if (patient) {
    const age = patient.birth_date ? calculateAge(patient.birth_date) : '未知';
    promptParts.push(`- 姓名：${patient.name || '未知'}`);
    
    // 性别改为中文表示
    let genderText = '未知';
    if (patient.gender === 'MALE') {
      genderText = '男';
    } else if (patient.gender === 'FEMALE') {
      genderText = '女';
    }
    promptParts.push(`- 性别：${genderText}`);
    
    promptParts.push(`- 年龄：${age}岁`);
    if (patient.height && patient.weight) {
      promptParts.push(`- 身高：${patient.height}cm`);
      promptParts.push(`- 体重：${patient.weight}kg`);
      const bmi = patient.bmi || calculateBMI(patient.height, patient.weight);
      promptParts.push(`- BMI：${bmi.toFixed(1)}`);
    }
    if (patient.blood_type) {
      promptParts.push(`- 血型：${patient.blood_type}`);
    }
    
    // 简化展示既往病史、家族病史和过敏史
    if (patient.past_medical_history) {
      promptParts.push(`- 既往病史：${patient.past_medical_history}`);
    }
    
    if (patient.family_medical_history) {
      promptParts.push(`- 家族病史：${patient.family_medical_history}`);
    }
    
    if (patient.allergy_history) {
      promptParts.push(`- 过敏史：${patient.allergy_history}`);
    }
  } else {
    promptParts.push(`- 患者信息不详`);
  }
  
  // 添加病历信息
  promptParts.push(`\n## 病历信息`);
  if (disease) {
    promptParts.push(`- 病名：${disease.name || '未确诊'}`);
    promptParts.push(`- 严重程度：${disease.severity || '未评估'}`);
    
    // 强调确诊日期，使用更清晰的格式
    const diagnosisDate = formatDate(disease.diagnosis_date);
    promptParts.push(`- 确诊日期：${diagnosisDate || '请提供'}`);
    
    // 病程阶段转为中文表示
    let stageText = disease.stage || '未知阶段';
    // 映射英文阶段到中文（如果是英文的话）
    const stageMapping = {
      'INITIAL': '初诊',
      'INITIAL_PHASE': '初诊期',
      'DIAGNOSIS': '确诊',
      'DIAGNOSIS_PHASE': '确诊期',
      'TREATMENT': '治疗',
      'TREATMENT_PHASE': '治疗期',
      'FOLLOW_UP': '随访',
      'RECOVERY': '康复期',
      'PROGNOSIS': '预后'
    };
    
    if (stageMapping[stageText]) {
      stageText = stageMapping[stageText];
    }
    
    promptParts.push(`- 病程阶段：${stageText}`);
    promptParts.push(`- 状态：${disease.status || '未知'}`);
    
    if (disease.description) {
      promptParts.push(`- 描述：${disease.description}`);
    }
    if (disease.notes) {
      promptParts.push(`- 备注：${disease.notes}`);
    }
    
    // 提示疾病是否为慢性病
    if (disease.is_chronic_disease) {
      promptParts.push(`- 慢性疾病：是`);
      promptParts.push(`**注意：这是一个慢性疾病，请在分析中特别考虑长期管理和生活方式建议**`);
    } else if (disease.name && (
        disease.name.includes('糖尿病') || 
        disease.name.includes('高血压') || 
        disease.name.includes('高脂血症') ||
        disease.name.includes('冠心病'))) {
      promptParts.push(`**这可能是一种需要长期管理的慢性疾病，请判断并在返回的JSON中更新is_chronic_disease字段**`);
    }
  } else {
    promptParts.push(`- 病历信息不详`);
  }
  
  // 添加历史记录
  promptParts.push(`\n## 病历记录（按时间排序）`);
  if (records && records.length > 0) {
    // 计算病程总天数
    let firstRecordDate = null;
    let lastRecordDate = null;
    
    try {
      firstRecordDate = new Date(records[0].created_at);
      lastRecordDate = new Date(records[records.length - 1].created_at);
      const durationDays = Math.round((lastRecordDate - firstRecordDate) / (1000 * 60 * 60 * 24));
      
      if (!isNaN(durationDays) && durationDays >= 0) {
        promptParts.push(`**病程持续时间：约${durationDays}天**`);
      }
    } catch (error) {
      console.error('计算病程天数时出错:', error);
    }
    
    records.forEach((record, index) => {
      // 每条记录显示详细时间
      const recordDate = formatDateWithTime(record.created_at);
      promptParts.push(`\n### 记录 ${index + 1} - ${recordDate}`);
      promptParts.push(`- 类型：${record.record_type || '常规记录'}`);
      promptParts.push(`- 标题：${record.title || '无标题'}`);
      if (record.content) {
        promptParts.push(`- 内容：${record.content}`);
      }
      if (record.symptoms) {
        promptParts.push(`- 症状：${record.symptoms}`);
      }
      if (record.treatments) {
        promptParts.push(`- 治疗：${record.treatments}`);
      }
      if (record.medications) {
        promptParts.push(`- 用药：${record.medications}`);
      }
      if (record.test_results) {
        promptParts.push(`- 检查结果：${record.test_results}`);
      }
      
      // 添加记录日期，如果有record_date字段
      if (record.record_date && record.record_date !== record.created_at) {
        promptParts.push(`- 记录日期：${formatDate(record.record_date)}`);
      }
    });
    
    // 请求分析症状变化趋势
    if (records.length >= 2) {
      promptParts.push(`\n请分析最近记录中症状的变化趋势，判断病情是改善还是恶化，并在报告中提供针对性建议。`);
    }
  } else {
    promptParts.push(`- 暂无病历记录`);
  }
  
  // 添加分析请求
  promptParts.push(`\n## 分析请求`);
  promptParts.push(`请根据以上信息，提供专业的医疗分析和建议。分析应该包括对病情的整体评估、可能的治疗方向、生活建议以及是否需要紧急就医。`);
  
  // 添加对病程阶段的特别提示
  if (disease && disease.stage) {
    promptParts.push(`请特别注意患者目前处于病程的${disease.stage || '当前'}阶段，针对此阶段提供相应的治疗和管理建议。`);
  }
  
  // 添加患者意向地区信息
  if (targetRegion) {
    promptParts.push(`患者意向就医地区：${targetRegion}，请在医院推荐中优先考虑此地区的医疗机构。`);
  } else {
    promptParts.push(`患者未提供意向就医地区，请推荐全国范围内的优质医疗机构。`);
  }
  
  // 如果是未确诊状态，特别提示
  if (disease && (!disease.name || disease.name === '未确诊')) {
    promptParts.push(`\n**注意：患者病情尚未确诊，请提供各种可能的诊断，并附带详细的量化可能性指标（如【90%】）和确诊建议。**`);
  }
  
  // BMI相关提示
  if (patient && patient.height && patient.weight) {
    const bmi = patient.bmi || calculateBMI(patient.height, patient.weight);
    if (bmi < 18.5) {
      promptParts.push(`请注意患者BMI偏低(${bmi.toFixed(1)})，在治疗方案中考虑营养补充建议。`);
    } else if (bmi >= 24 && bmi < 28) {
      promptParts.push(`请注意患者BMI超重(${bmi.toFixed(1)})，在治疗方案中考虑体重管理建议。`);
    } else if (bmi >= 28) {
      promptParts.push(`请注意患者BMI肥胖(${bmi.toFixed(1)})，在治疗方案中优先考虑体重管理，并评估是否有相关代谢风险。`);
    }
  }
  
  // 治疗方案特别提示
  promptParts.push(`\n对于治疗方案建议，请提供详细的方案说明、适合度评估（如【适合度：85%】）、预后评估参考（如5年生存率）以及医疗预算参考区间。同时指明哪些治疗项目可纳入医保报销，哪些需要自费。`);
  
  promptParts.push(`请确保你的回复是结构化的JSON格式，包含summary、differentialDiagnosis、emergencyGuidance、hospitalRecommendations、treatmentPlan、lifestyleAndMentalHealth、dashboardData、riskWarnings和is_chronic_disease字段。`);
  promptParts.push(`请注意：summary字段应包含约500字的详细解释，确保患者能够充分理解病情分析和建议。`);
  
  return promptParts.join('\n');
};

/**
 * 计算年龄
 * @param {string} birthDate 出生日期字符串
 * @returns {number} 年龄
 */
const calculateAge = (birthDate) => {
  if (!birthDate) return '未知';
  
  try {
    const birthDateObj = new Date(birthDate);
    const today = new Date();
    let age = today.getFullYear() - birthDateObj.getFullYear();
    const monthDiff = today.getMonth() - birthDateObj.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDateObj.getDate())) {
      age--;
    }
    
    return age;
  } catch (error) {
    console.error('计算年龄时出错:', error);
    return '未知';
  }
};

/**
 * 计算BMI
 * @param {number} height 身高(cm)
 * @param {number} weight 体重(kg)
 * @returns {number} BMI值
 */
const calculateBMI = (height, weight) => {
  if (!height || !weight) return 0;
  
  try {
    // 身高需要转换为米
    const heightInMeters = height / 100;
    return weight / (heightInMeters * heightInMeters);
  } catch (error) {
    console.error('计算BMI时出错:', error);
    return 0;
  }
};

/**
 * 格式化日期，显示年月日
 * @param {string} dateString - 日期字符串
 * @returns {string} 格式化后的日期字符串
 */
const formatDate = (dateString) => {
  if (!dateString) return '';
  
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString; // 如果无法解析，返回原始字符串
    
    return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
  } catch (error) {
    console.error('格式化日期时出错:', error);
    return dateString;
  }
};

/**
 * 格式化日期，显示年月日和时间
 * @param {string} dateString - 日期字符串
 * @returns {string} 格式化后的日期和时间字符串
 */
const formatDateWithTime = (dateString) => {
  if (!dateString) return '';
  
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString; // 如果无法解析，返回原始字符串
    
    return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
  } catch (error) {
    console.error('格式化日期和时间时出错:', error);
    return dateString;
  }
};

/**
 * 构建完整的提示内容
 * @param {Object} anonymizedData 匿名化后的医疗数据
 * @returns {Object} 系统提示和用户提示
 */
const buildPrompt = (anonymizedData) => {
  return {
    systemPrompt: buildSystemPrompt(),
    userPrompt: buildUserPrompt(anonymizedData)
  };
};

module.exports = {
  buildPrompt,
  buildSystemPrompt,
  buildUserPrompt,
  calculateAge,
  calculateBMI,
  formatDate,
  formatDateWithTime
}; 