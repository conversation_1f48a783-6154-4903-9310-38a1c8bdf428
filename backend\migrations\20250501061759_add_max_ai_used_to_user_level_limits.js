/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.table('user_level_limits', table => {
    // 添加max_ai_used字段，类型为整数，不允许为空
    table.integer('max_ai_used').notNullable().defaultTo(5);
  }).then(() => {
    // 设置不同用户级别的初始值
    return knex('user_level_limits').where({ level_type: 'PERSONAL' }).update({ max_ai_used: 5 });
  }).then(() => {
    return knex('user_level_limits').where({ level_type: 'FAMILY' }).update({ max_ai_used: 20 });
  }).then(() => {
    return knex('user_level_limits').where({ level_type: 'PROFESSIONAL' }).update({ max_ai_used: 50 });
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.table('user_level_limits', table => {
    // 回滚时删除max_ai_used字段
    table.dropColumn('max_ai_used');
  });
};
