#!/bin/bash

# 前端部署脚本
# 用于将新构建的前端文件部署到服务器

echo "🚀 开始部署前端..."

# 检查是否在正确的目录
if [ ! -d "frontend" ]; then
    echo "❌ 错误：请在项目根目录运行此脚本"
    exit 1
fi

# 进入前端目录
cd frontend

echo "📦 检查前端依赖..."
if [ ! -d "node_modules" ]; then
    echo "📥 安装前端依赖..."
    npm install
fi

echo "🔨 构建前端项目..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ 前端构建失败"
    exit 1
fi

echo "✅ 前端构建成功"

# 检查构建文件
if [ ! -d "build" ]; then
    echo "❌ 构建目录不存在"
    exit 1
fi

echo "📋 构建文件信息："
ls -la build/static/js/main.*.js | head -1

# 显示部署说明
echo ""
echo "🎉 前端构建完成！"
echo ""
echo "📤 部署到服务器的步骤："
echo "1. 将整个 frontend/build 目录上传到服务器"
echo "2. 替换服务器上的 /home/<USER>/HKB/frontend/build 目录"
echo "3. 重启 Nginx 服务"
echo ""
echo "🔧 服务器操作命令："
echo "   sudo systemctl reload nginx"
echo ""
echo "💡 或者使用 Git 同步："
echo "   cd /home/<USER>/HKB"
echo "   git add ."
echo "   git commit -m 'Fix CORS and HTTPS issues'"
echo "   git push"
echo "   # 然后在服务器上："
echo "   git pull"
echo "   cd frontend"
echo "   npm run build"
echo ""

# 创建部署包
echo "📦 创建部署包..."
cd ..
tar -czf frontend-build-$(date +%Y%m%d-%H%M%S).tar.gz -C frontend build
echo "✅ 部署包已创建：frontend-build-$(date +%Y%m%d-%H%M%S).tar.gz"

echo ""
echo "🎯 下一步："
echo "1. 提交代码到 Git"
echo "2. 在服务器上拉取最新代码"
echo "3. 重新构建前端"
echo "4. 重启 Nginx"
