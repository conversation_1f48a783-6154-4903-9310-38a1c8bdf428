/**
 * 检查服务记录表中的数据，诊断问题所在
 * 用法: node scripts/checkServiceRecords.js
 */

const knex = require('knex');
const { Model } = require('objection');
const path = require('path');
const db = require('../config/database');
const ServiceRecord = require('../models/ServiceRecord');
const Record = require('../models/Record');

// 设置objection连接
Model.knex(db);

/**
 * 检查服务记录表
 */
async function checkServiceRecords() {
  console.log('开始检查服务记录表...');
  
  try {
    // 检查表结构
    console.log('\n检查服务记录表结构...');
    const hasTable = await db.schema.hasTable('service_records');
    
    if (!hasTable) {
      console.log('错误: service_records表不存在！');
      return;
    }
    
    console.log('service_records表存在');
    
    // 检查表字段
    const columns = await db.table('service_records').columnInfo();
    console.log('service_records表字段信息:');
    for (const [key, value] of Object.entries(columns)) {
      console.log(`  ${key}: ${JSON.stringify(value)}`);
    }
    
    // 检查服务记录数量
    const count = await db('service_records').count('* as count').first();
    console.log(`\n服务记录数量: ${count.count}`);
    
    // 获取所有服务记录
    console.log('\n获取服务记录详情...');
    const serviceRecords = await db('service_records').select('*');
    
    if (serviceRecords.length === 0) {
      console.log('未找到任何服务记录');
      return;
    }
    
    // 检查字段数据
    let validRecordIds = 0;
    let nullRecordIds = 0;
    
    for (const record of serviceRecords) {
      if (record.record_id) {
        validRecordIds++;
      } else {
        nullRecordIds++;
        console.log(`发现记录ID为空的服务记录: ${record.id}`);
      }
    }
    
    console.log(`\n服务记录统计信息:`);
    console.log(`- 总记录数: ${serviceRecords.length}`);
    console.log(`- 有效记录ID数: ${validRecordIds}`);
    console.log(`- 空记录ID数: ${nullRecordIds}`);
    
    // 打印前5条服务记录
    console.log('\n前5条服务记录示例:');
    serviceRecords.slice(0, 5).forEach((record, index) => {
      console.log(`\n记录 ${index + 1}:`);
      for (const [key, value] of Object.entries(record)) {
        console.log(`  ${key}: ${value}`);
      }
    });
    
    // 检查记录关联
    const recordIds = serviceRecords
      .filter(sr => sr.record_id)
      .map(sr => sr.record_id);
      
    if (recordIds.length > 0) {
      console.log(`\n找到 ${recordIds.length} 个有效记录ID，准备查询关联记录...`);
      
      const records = await db('records')
        .whereIn('id', recordIds)
        .select('*');
        
      console.log(`\n关联的记录数: ${records.length} (预期: ${recordIds.length})`);
      
      if (records.length > 0) {
        // 计算缺失的记录
        const foundIds = new Set(records.map(r => r.id));
        const missingIds = recordIds.filter(id => !foundIds.has(id));
        
        if (missingIds.length > 0) {
          console.log(`\n发现 ${missingIds.length} 条缺失的关联记录ID:`);
          missingIds.forEach(id => console.log(`  - ${id}`));
        }
        
        // 打印前3条记录示例
        console.log('\n前3条关联记录示例:');
        records.slice(0, 3).forEach((record, index) => {
          console.log(`\n关联记录 ${index + 1} (ID: ${record.id}):`);
          console.log(`  title: ${record.title}`);
          console.log(`  stage_tags: ${record.stage_tags}`);
          console.log(`  custom_tags: ${record.custom_tags}`);
          console.log(`  stage_node: ${record.stage_node}`);
          console.log(`  stage_phase: ${record.stage_phase}`);
          console.log(`  is_important: ${record.is_important}`);
        });
        
        // 检查记录中的关键字段
        console.log('\n检查记录中的标签字段...');
        
        let jsonStageTags = 0;
        let stringStageTags = 0;
        let jsonCustomTags = 0;
        let stringCustomTags = 0;
        let nullStageTags = 0;
        let nullCustomTags = 0;
        
        for (const record of records) {
          try {
            if (record.stage_tags === null || record.stage_tags === undefined) {
              nullStageTags++;
            } else if (record.stage_tags) {
              try {
                JSON.parse(record.stage_tags);
                jsonStageTags++;
              } catch (e) {
                stringStageTags++;
              }
            }
            
            if (record.custom_tags === null || record.custom_tags === undefined) {
              nullCustomTags++;
            } else if (record.custom_tags) {
              try {
                JSON.parse(record.custom_tags);
                jsonCustomTags++;
              } catch (e) {
                stringCustomTags++;
              }
            }
          } catch (err) {
            console.error(`分析记录 ${record.id} 时出错:`, err);
          }
        }
        
        console.log('\n标签字段统计:');
        console.log(`- null/undefined stage_tags: ${nullStageTags}`);
        console.log(`- JSON格式stage_tags: ${jsonStageTags}`);
        console.log(`- 字符串格式stage_tags: ${stringStageTags}`);
        console.log(`- null/undefined custom_tags: ${nullCustomTags}`);
        console.log(`- JSON格式custom_tags: ${jsonCustomTags}`);
        console.log(`- 字符串格式custom_tags: ${stringCustomTags}`);
      } else {
        console.log('\n未找到任何关联记录！');
      }
    } else {
      console.log('\n未找到任何有效的记录ID！');
    }
    
  } catch (error) {
    console.error('检查过程中发生错误:', error);
    console.error(error.stack);
  } finally {
    // 关闭数据库连接
    await db.destroy();
    console.log('\n数据库连接已关闭');
  }
}

// 执行检查
checkServiceRecords()
  .then(() => {
    console.log('检查脚本执行完毕');
    process.exit(0);
  })
  .catch(err => {
    console.error('脚本执行失败:', err);
    console.error(err.stack);
    process.exit(1);
  }); 