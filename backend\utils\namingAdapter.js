/**
 * 命名规则适配器
 * 用于处理驼峰命名和蛇形命名之间的转换
 */

/**
 * 将对象中的键从驼峰命名转换为蛇形命名
 * 例如: firstName -> first_name
 * @param {Object} obj 原始对象
 * @returns {Object} 转换后的对象
 */
function toSnakeCase(obj) {
  if (!obj || typeof obj !== 'object') return obj;
  
  if (Array.isArray(obj)) {
    return obj.map(toSnakeCase);
  }
  
  const result = {};
  
  Object.keys(obj).forEach(key => {
    // 转换键名
    const snakeKey = key.replace(/([A-Z])/g, '_$1').toLowerCase();
    
    // 递归转换值
    if (obj[key] !== null && typeof obj[key] === 'object') {
      result[snakeKey] = toSnakeCase(obj[key]);
    } else {
      result[snakeKey] = obj[key];
    }
  });
  
  return result;
}

/**
 * 将对象中的键从蛇形命名转换为驼峰命名
 * 例如: first_name -> firstName
 * @param {Object} obj 原始对象
 * @returns {Object} 转换后的对象
 */
function toCamelCase(obj) {
  if (!obj || typeof obj !== 'object') return obj;
  
  if (Array.isArray(obj)) {
    return obj.map(toCamelCase);
  }
  
  const result = {};
  
  Object.keys(obj).forEach(key => {
    // 转换键名
    const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
    
    // 递归转换值
    if (obj[key] !== null && typeof obj[key] === 'object') {
      result[camelKey] = toCamelCase(obj[key]);
    } else {
      result[camelKey] = obj[key];
    }
  });
  
  return result;
}

/**
 * 适配AI报告对象，确保前后端兼容
 * @param {Object} report AI报告对象
 * @param {Boolean} toCamel 是否转换为驼峰命名
 * @returns {Object} 适配后的对象
 */
function adaptAIReport(report, toCamel = true) {
  if (!report) return null;
  
  // 首先进行深拷贝
  const reportCopy = JSON.parse(JSON.stringify(report));
  
  // 转换命名规则
  const adaptedReport = toCamel ? toCamelCase(reportCopy) : toSnakeCase(reportCopy);
  
  // 补充可能缺失的字段
  if (toCamel) {
    if (!adaptedReport.llmConfigs) adaptedReport.llmConfigs = {};
    if (!adaptedReport.visibleFields) adaptedReport.visibleFields = [];
    if (!adaptedReport.configId) adaptedReport.configId = null;
  } else {
    if (!adaptedReport.llm_configs) adaptedReport.llm_configs = {};
    if (!adaptedReport.visible_fields) adaptedReport.visible_fields = [];
    if (!adaptedReport.config_id) adaptedReport.config_id = null;
  }
  
  return adaptedReport;
}

module.exports = {
  toSnakeCase,
  toCamelCase,
  adaptAIReport
};