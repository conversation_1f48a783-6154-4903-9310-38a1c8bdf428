/**
 * AI模型验证脚本
 * 用于检查AIReport模型是否正确配置并能正常工作
 */
const { Model } = require('objection');
const Knex = require('knex');
const knexConfig = require('../knexfile');
const { AIReport, AIReportConfig, AIReportQuota } = require('../models/aiReport');

// 初始化数据库连接
const knex = Knex(knexConfig.development || knexConfig);
Model.knex(knex);

const verifyModels = async () => {
  console.log('开始验证AI模型配置...');

  try {
    // 检查AIReport模型
    console.log('检查AIReport模型...');
    const reportTableName = AIReport.tableName;
    console.log(`表名: ${reportTableName}`);
    console.log('Json模式:', JSON.stringify(AIReport.jsonSchema, null, 2));
    
    // 检查AIReportConfig模型
    console.log('\n检查AIReportConfig模型...');
    const configTableName = AIReportConfig.tableName;
    console.log(`表名: ${configTableName}`);
    console.log('Json模式:', JSON.stringify(AIReportConfig.jsonSchema, null, 2));
    
    // 检查AIReportQuota模型
    console.log('\n检查AIReportQuota模型...');
    const quotaTableName = AIReportQuota.tableName;
    console.log(`表名: ${quotaTableName}`);
    console.log('Json模式:', JSON.stringify(AIReportQuota.jsonSchema, null, 2));
    
    // 测试模型能否正确查询数据
    console.log('\n测试模型查询功能...');
    
    // 查询AIReport
    const reportCount = await AIReport.query().resultSize();
    console.log(`AIReport记录数: ${reportCount}`);
    
    // 查询AIReportConfig
    const configCount = await AIReportConfig.query().resultSize();
    console.log(`AIReportConfig记录数: ${configCount}`);
    
    // 查询AIReportQuota
    const quotaCount = await AIReportQuota.query().resultSize();
    console.log(`AIReportQuota记录数: ${quotaCount}`);
    
    // 测试数据库连接和事务
    console.log('\n测试数据库事务...');
    const trx = await AIReport.startTransaction();
    try {
      // 在事务中执行查询
      const reportCountInTrx = await AIReport.query(trx).resultSize();
      console.log(`事务中查询AIReport记录数: ${reportCountInTrx}`);
      
      // 提交事务
      await trx.commit();
      console.log('事务提交成功');
    } catch (trxError) {
      await trx.rollback();
      console.error('事务失败，已回滚:', trxError.message);
      throw trxError;
    }
    
    console.log('\n模型验证成功!');
  } catch (error) {
    console.error('模型验证失败:', error);
    throw error;
  } finally {
    // 关闭数据库连接
    await knex.destroy();
  }
};

verifyModels()
  .then(() => {
    console.log('验证脚本执行完成');
    process.exit(0);
  })
  .catch(err => {
    console.error('验证脚本执行失败:', err);
    process.exit(1);
  }); 