import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';
import reportWebVitals from './reportWebVitals';
import { initConfig } from './config/appConfig';

// 初始化应用配置
console.log('[应用启动] 开始初始化应用...');
initConfig();

// 创建根元素
const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

// 渲染应用
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);

// 性能测量
reportWebVitals();

// 验证配置是否正确加载
console.log('[应用启动] 验证配置:', {
  APP_CONFIG: (window as any).APP_CONFIG,
  WS_URL: (window as any).APP_CONFIG?.API?.WS_URL
});
