const knex = require('knex')(require('../knexfile').development);
const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');

async function createTestUser() {
  try {
    console.log('开始创建测试用户...');

    // 检查用户是否已存在
    const existingUser = await knex('users').where({ username: 'testuser' }).first();
    
    if (existingUser) {
      console.log('测试用户已存在，ID:', existingUser.id);
      return existingUser;
    }
    
    // 创建新用户
    const userId = uuidv4();
    const passwordHash = await bcrypt.hash('testpassword', 10);
    const now = new Date().toISOString();
    
    const user = {
      id: userId,
      username: 'testuser',
      email: '<EMAIL>',
      passwordHash,
      phoneNumber: '13900000000',
      role: 'USER',
      level: 'PERSONAL',
      activeDiseaseLimit: 5,
      aiUsageCount: 0,
      familyMemberLimit: 3,
      isActive: 1,
      lastLoginAt: now,
      updatedAt: now
    };
    
    await knex('users').insert(user);
    console.log('测试用户创建成功，ID:', userId);
    
    // 确保有用户级别限制
    const levelLimit = await knex('user_level_limits').where({ levelType: 'PERSONAL' }).first();
    
    if (!levelLimit) {
      await knex('user_level_limits').insert({
        id: uuidv4(),
        levelType: 'PERSONAL',
        maxPatients: 5,
        maxPathologies: 10,
        maxAttachmentSize: 5120, // 5MB
        maxTotalStorage: 51200 // 50MB
      });
      console.log('创建了默认的PERSONAL用户级别限制');
    }
    
    return user;
  } catch (error) {
    console.error('创建测试用户失败:', error);
    throw error;
  } finally {
    await knex.destroy();
  }
}

createTestUser()
  .then(() => {
    console.log('测试用户创建脚本完成');
    process.exit(0);
  })
  .catch(err => {
    console.error('测试用户创建脚本失败:', err);
    process.exit(1);
  }); 