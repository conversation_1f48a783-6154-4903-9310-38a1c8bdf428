const { Model, snakeCaseMappers } = require('objection');
const { v4: uuidv4 } = require('uuid');

class User extends Model {
  static get tableName() {
    return 'users';
  }

  static get idColumn() {
    return 'id';
  }

  static get columnNameMappers() {
    return snakeCaseMappers();
  }

  $beforeInsert() {
    this.id = this.id || uuidv4();
    this.createdAt = new Date().toISOString();
    this.updatedAt = this.createdAt;
    this.isActive = this.isActive ?? true;
  }

  $beforeUpdate() {
    this.updatedAt = new Date().toISOString();
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['username', 'passwordHash'],
      properties: {
        id: { type: 'string' },
        username: { type: 'string', minLength: 3, maxLength: 50 },
        email: { type: ['string', 'null'] },
        phoneNumber: { type: ['string', 'null'] },
        passwordHash: { type: 'string' },
        avatar: { type: ['string', 'null'] },
        role: { type: 'string', enum: ['USER', 'SERVICE', 'ADMIN'] },
        level: { type: 'string', enum: ['PERSONAL', 'FAMILY', 'PROFESSIONAL'] },
        activeDiseaseLimit: { type: 'integer' },
        aiUsageCount: { type: 'integer' },
        aiUsageResetAt: { type: ['string', 'null'] },
        familyMemberLimit: { type: 'integer' },
        isActive: { type: 'boolean' },
        createdAt: { type: 'string' },
        updatedAt: { type: 'string' },
        lastLoginAt: { type: ['string', 'null'] },
        deletedAt: { type: ['string', 'null'] }
      }
    };
  }

  static get relationMappings() {
    const Patient = require('./Patient');
    const UserAuthorization = require('./UserAuthorization');
    const ServiceRecord = require('./ServiceRecord');
    const ServiceReport = require('./ServiceReport');
    
    return {
      patients: {
        relation: Model.HasManyRelation,
        modelClass: Patient,
        join: {
          from: 'users.id',
          to: 'patients.userId'
        }
      },
      
      // 作为授权人的授权关系
      asAuthorizerAuthorizations: {
        relation: Model.HasManyRelation,
        modelClass: UserAuthorization,
        join: {
          from: 'users.id',
          to: 'user_authorizations.authorizer_id'
        }
      },
      
      // 作为被授权人的授权关系
      asAuthorizedAuthorizations: {
        relation: Model.HasManyRelation,
        modelClass: UserAuthorization,
        join: {
          from: 'users.id',
          to: 'user_authorizations.authorized_id'
        }
      },
      
      // 作为服务用户创建的记录
      serviceRecords: {
        relation: Model.HasManyRelation,
        modelClass: ServiceRecord,
        join: {
          from: 'users.id',
          to: 'service_records.serviceUserId'
        }
      },
      
      // 作为普通用户被创建的记录
      ownedServiceRecords: {
        relation: Model.HasManyRelation,
        modelClass: ServiceRecord,
        join: {
          from: 'users.id',
          to: 'service_records.ownerUserId'
        }
      },
      
      // 作为服务用户创建的报告
      serviceReports: {
        relation: Model.HasManyRelation,
        modelClass: ServiceReport,
        join: {
          from: 'users.id',
          to: 'service_reports.serviceUserId'
        }
      },
      
      // 作为普通用户被创建的报告
      ownedServiceReports: {
        relation: Model.HasManyRelation,
        modelClass: ServiceReport,
        join: {
          from: 'users.id',
          to: 'service_reports.ownerUserId'
        }
      }
    };
  }
}

module.exports = User; 