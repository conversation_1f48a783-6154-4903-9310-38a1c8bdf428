// 隐私级别类型
export type PrivacyLevel = 'BASIC' | 'STANDARD' | 'FULL';

// 服务记录类型
export type RecordType = 'MEDICAL_RECORD' | 'LAB_RESULT' | 'PRESCRIPTION' | 'SURGERY' | 'CHECKUP' | 'OTHER';

// 主要类型
export type PrimaryType = 'INITIAL' | 'FOLLOWUP' | 'EMERGENCY' | 'REGULAR' | 'OTHER';

// 记录状态
export type RecordStatus = 'ACTIVE' | 'INACTIVE' | 'DELETED';

// 服务记录接口
export interface ServiceRecord {
  id: string;
  authorizationId: string;
  serviceUserId: string;
  ownerUserId: string;
  patientId: string;
  diseaseId?: string;
  title: string;
  content: string;
  recordType: RecordType;
  primaryType: PrimaryType;
  recordDate: string;
  status: RecordStatus;
  tags?: string[];
  createdAt: string;
  updatedAt: string;
  serviceUser?: {
    id: string;
    username: string;
  };
  patient?: {
    id: string;
    name: string;
  };
  disease?: {
    id: string;
    name: string;
  };
}

// 报告类型
export type ReportType = 'DIAGNOSIS' | 'TREATMENT' | 'ANALYSIS' | 'SUMMARY';

// 报告状态
export type ReportStatus = 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';

// API响应接口
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

// 扩展的服务记录创建响应接口，用于处理不同的返回结构
export interface ServiceRecordCreateResponse {
  id?: string;
  record?: ServiceRecord;
  serviceRecord?: {
    id: string;
    recordId: string;
    [key: string]: any;
  };
  data?: {
    id?: string;
    record?: ServiceRecord;
    serviceRecord?: {
      id: string;
      recordId: string;
      [key: string]: any;
    };
  };
  message?: string;
  success?: boolean;
}

// AI报告内容
export interface AIReport {
  id: string;
  content: string;
  rawContent?: string;
  pdfPath?: string;
  createdAt: string;
}

// 服务报告接口
export interface ServiceReport {
  id: string;
  authorizationId: string;
  serviceUserId: string;
  ownerUserId: string;
  patientId: string;
  diseaseId: string;
  reportType: ReportType;
  status: ReportStatus;
  promptOverrides?: Record<string, string>;
  aiReport?: AIReport;
  createdAt: string;
  updatedAt: string;
  serviceUser?: {
    id: string;
    username: string;
  };
  patient?: {
    id: string;
    name: string;
  };
  disease?: {
    id: string;
    name: string;
  };
}

// 授权接口
export interface Authorization {
  id: string;
  authorizer_id: string;
  authorized_id: string;
  privacy_level: PrivacyLevel;
  status: 'ACTIVE' | 'INACTIVE' | 'EXPIRED';
  authorized_switch: boolean;
  authorizer_switch: boolean;
  expiration_date?: string;
  created_at?: string;
  authorizerId?: string;
  authorizedId?: string;
  privacyLevel?: string;
  createdAt?: string;
  updatedAt?: string;
  patient?: {
    id: string;
    name: string;
  };
  authorizer?: {
    id: string;
    username: string;
  };
  authorized?: {
    id: string;
    username: string;
  };
}

// 患者接口
export interface Patient {
  id: string;
  userId: string;
  name: string;
  gender: 'MALE' | 'FEMALE' | 'OTHER';
  birthDate?: string;
  phoneNumber?: string;
  address?: string;
  emergencyContact?: string;
  emergencyPhone?: string;
  medicalHistory?: string;
  allergies?: string[];
  createdAt: string;
  updatedAt: string;
}

// 病理接口
export interface Disease {
  id: string;
  patientId: string;
  name: string;
  category?: string;
  diagnosisDate?: string;
  description?: string;
  status: 'ACTIVE' | 'RESOLVED';
  createdAt: string;
  updatedAt: string;
} 