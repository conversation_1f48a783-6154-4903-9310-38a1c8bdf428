import React, { useMemo } from 'react';
import { 
  Box, 
  Typography, 
  LinearProgress, 
  Chip,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon,
  HistoryToggleOff as TimelineIcon,
  CheckCircleOutline as CheckIcon,
  ErrorOutline as WarningIcon
} from '@mui/icons-material';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import BaseCard from './BaseCard';

interface ProgressDevelopmentCardProps {
  disease: any;
  records: any[];
  loading?: boolean;
}

/**
 * 病程发展卡片组件
 * 显示病情进展信息，包括趋势和重要记录
 */
const ProgressDevelopmentCard: React.FC<ProgressDevelopmentCardProps> = ({ 
  disease, 
  records = [],
  loading = false 
}) => {
  
  // 获取疾病阶段名称
  const getStageName = (stage: string): string => {
    const stageMap: Record<string, string> = {
      'INITIAL': '初诊',
      'DIAGNOSIS': '确诊',
      'TREATMENT': '治疗',
      'RECOVERY': '恢复',
      'FOLLOW_UP': '随访',
      'PROGNOSIS': '预后',
      'ARCHIVE': '归档'
    };
    
    return stageMap[stage] || '未知';
  };
  
  // 格式化日期
  const formatDate = (dateStr: string) => {
    if (!dateStr) return '';
    
    try {
      const date = new Date(dateStr);
      return format(date, 'MM/dd', { locale: zhCN });
    } catch (e) {
      return '';
    }
  };
  
  // 计算病情趋势
  const trend = useMemo(() => {
    // 这里应该结合记录内容和病理信息计算实际趋势
    // 当前使用一个简化的算法
    if (disease?.stage === 'RECOVERY' || disease?.stage === 'FOLLOW_UP') {
      return 'improving';
    } else if (disease?.stage === 'TREATMENT') {
      return 'stable';
    } else {
      return 'needs_attention';
    }
    
    // 在实际应用中，可以通过AI分析记录内容或使用更复杂的算法
  }, [disease?.stage]);
  
  // 获取病理阶段进展百分比
  const progressPercentage = useMemo(() => {
    const stageOrder = [
      'INITIAL', 'DIAGNOSIS', 'TREATMENT', 'RECOVERY', 'FOLLOW_UP'
    ];
    
    const currentStage = disease?.stage || 'INITIAL';
    const currentIndex = stageOrder.indexOf(currentStage);
    
    // 如果找不到当前阶段，默认为0%
    if (currentIndex === -1) return 0;
    
    // 计算百分比，最后一个阶段为100%
    return Math.round((currentIndex / (stageOrder.length - 1)) * 100);
  }, [disease?.stage]);
  
  // 过滤重要记录
  const keyRecords = useMemo(() => {
    if (!records.length) return [];
    
    // 按时间倒序排序
    const sortedRecords = [...records].sort((a, b) => 
      new Date(b.recordDate || b.created_at).getTime() - 
      new Date(a.recordDate || a.created_at).getTime()
    );
    
    // 获取最近的3条重要记录
    // 在实际应用中，可以结合严重程度和类型进行更复杂的筛选
    return sortedRecords.slice(0, 3);
  }, [records]);
  
  // 获取记录图标
  const getRecordIcon = (record: any) => {
    // 根据记录类型或严重程度返回不同图标
    const type = record.recordType?.toLowerCase() || '';
    const severity = record.severity?.toLowerCase() || '';
    
    if (severity === 'high' || severity === 'severe') {
      return <WarningIcon color="error" />;
    } else if (type.includes('follow') || type.includes('revisit')) {
      return <CheckIcon color="success" />;
    } else {
      return <TimelineIcon color="primary" />;
    }
  };
  
  // 获取趋势相关UI元素
  const getTrendUI = () => {
    switch (trend) {
      case 'improving':
        return {
          icon: <TrendingUpIcon sx={{ color: 'success.main' }} />,
          color: 'success',
          text: '好转中'
        };
      case 'stable':
        return {
          icon: <TrendingFlatIcon sx={{ color: 'info.main' }} />,
          color: 'info',
          text: '稳定'
        };
      case 'needs_attention':
        return {
          icon: <TrendingDownIcon sx={{ color: 'warning.main' }} />,
          color: 'warning',
          text: '需要警惕'
        };
      default:
        return {
          icon: <TrendingFlatIcon sx={{ color: 'info.main' }} />,
          color: 'info',
          text: '未知'
        };
    }
  };
  
  const trendUI = getTrendUI();
  
  if (loading) {
    return (
      <BaseCard title="病程进展" loading={true}>
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress size={30} />
        </Box>
      </BaseCard>
    );
  }
  
  return (
    <BaseCard title="病程进展">
      {/* 进度条 */}
      <Box sx={{ mb: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Typography variant="body2" color="text.secondary">
            当前阶段: {getStageName(disease?.stage || 'INITIAL')}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {progressPercentage}%
          </Typography>
        </Box>
        <LinearProgress 
          variant="determinate" 
          value={progressPercentage} 
          sx={{ height: 8, borderRadius: 1 }}
        />
      </Box>
      
      {/* 病情趋势 */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Box sx={{ mr: 2 }}>
          {trendUI.icon}
        </Box>
        <Box>
          <Typography variant="body2" color="text.secondary">
            病情趋势
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Chip 
              label={trendUI.text} 
              size="small" 
              color={trendUI.color as any}
              sx={{ fontSize: '0.7rem', height: 20, mr: 1 }} 
            />
            <Typography variant="body2">
              {disease?.stage === 'TREATMENT' ? '治疗中' : getStageName(disease?.stage || 'INITIAL')}
            </Typography>
          </Box>
        </Box>
      </Box>
      
      {/* 重要记录 */}
      {keyRecords.length > 0 && (
        <>
          <Divider sx={{ my: 1.5 }} />
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            近期重要记录
          </Typography>
          <List dense disablePadding>
            {keyRecords.map(record => (
              <ListItem 
                key={record.id}
                component="div"
                disableGutters
                sx={{ px: 0, mb: 0.5 }}
              >
                <ListItemIcon sx={{ minWidth: 36 }}>
                  {getRecordIcon(record)}
                </ListItemIcon>
                <ListItemText 
                  primary={record.title} 
                  secondary={formatDate(record.recordDate || record.created_at)}
                  primaryTypographyProps={{ variant: 'body2', noWrap: true }}
                  secondaryTypographyProps={{ variant: 'caption' }}
                />
              </ListItem>
            ))}
          </List>
        </>
      )}
    </BaseCard>
  );
};

export default ProgressDevelopmentCard; 