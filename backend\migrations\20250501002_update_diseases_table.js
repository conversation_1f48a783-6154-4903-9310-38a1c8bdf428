/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.table('diseases', function (table) {
    // 添加隐私标记字段（是否只对自己可见）
    // PostgreSQL中使用boolean类型而不是integer
    table.boolean('is_private').defaultTo(false).notNullable();
    
    // 关联患者ID字段
    table.uuid('patient_id').nullable().references('id').inTable('patients');
    
    // 添加索引以提高查询性能
    table.index(['patient_id']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.table('diseases', function (table) {
    table.dropIndex(['patient_id']);
    table.dropColumn('patient_id');
    table.dropColumn('is_private');
  });
}; 