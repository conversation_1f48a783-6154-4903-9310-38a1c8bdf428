// 应用配置
export const APP_CONFIG = {
  // 环境配置
  ENV: {
    IS_PRODUCTION: process.env.NODE_ENV === 'production',
    IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
    IS_TEST: process.env.NODE_ENV === 'test',
  },

  // API配置
  API: {
    VERSION: 'v1',
    BASE_PATH: '/v1',
    BASE_URL: process.env.REACT_APP_API_URL || (process.env.NODE_ENV === 'production' ? 'https://hkb.life' : 'http://localhost:3001'),
    // 统一使用3005端口，与后端配置保持一致
    WS_URL: process.env.REACT_APP_WS_URL || (process.env.NODE_ENV === 'production' ? 'wss://hkb.life/ws' : 'ws://localhost:3005/ws'),
  },

  // 应用信息
  APP: {
    NAME: 'HKB',
    VERSION: process.env.REACT_APP_VERSION || '1.0.0',
    BUILD_TIME: process.env.REACT_APP_BUILD_TIME || new Date().toISOString(),
  },

  // 功能开关
  FEATURES: {
    ENABLE_WEBSOCKET: true,
    ENABLE_CACHE: true,
    ENABLE_OFFLINE_MODE: false,
  },

  // 缓存配置
  CACHE: {
    PREFIX: 'hkb_',
    DEFAULT_TTL: 3600, // 1小时
    MAX_ITEMS: 1000,
  },

  // 请求配置
  REQUEST: {
    TIMEOUT: 90000, // 90秒
    RETRY_COUNT: 3,
    RETRY_DELAY: 1000, // 1秒
  },
} as const;

// 导出类型
export type AppConfig = typeof APP_CONFIG;

// 导出环境变量类型
export interface EnvConfig {
  NODE_ENV: 'development' | 'production' | 'test';
  REACT_APP_API_URL?: string;
  REACT_APP_WS_URL?: string;
  REACT_APP_VERSION?: string;
  REACT_APP_BUILD_TIME?: string;
}

// 验证环境变量
export function validateEnvConfig(config: Partial<EnvConfig>): void {
  const requiredVars = ['NODE_ENV'] as const;
  const missingVars = requiredVars.filter(key => !config[key]);

  if (missingVars.length > 0) {
    console.error('[配置验证] 缺少必需的环境变量:', missingVars.join(', '));
  }

  // 验证WebSocket URL配置
  if (!config.REACT_APP_WS_URL && config.NODE_ENV === 'production') {
    console.warn('[配置验证] 生产环境未配置REACT_APP_WS_URL，将使用默认值');
  }
}

// 初始化配置
export function initConfig(): void {
  console.log('[配置初始化] 开始初始化配置...');
  console.log('[配置初始化] 环境变量:', {
    NODE_ENV: process.env.NODE_ENV,
    REACT_APP_API_URL: process.env.REACT_APP_API_URL,
    REACT_APP_WS_URL: process.env.REACT_APP_WS_URL
  });

  // 验证环境变量
  validateEnvConfig(process.env as Partial<EnvConfig>);

  // 将配置挂载到window对象上
  (window as any).APP_CONFIG = APP_CONFIG;
  console.log('[配置初始化] 配置已挂载到window对象');

  // 记录配置信息
  console.log('[配置初始化] 应用配置:', {
    环境: APP_CONFIG.ENV.IS_PRODUCTION ? '生产环境' : '开发环境',
    API地址: APP_CONFIG.API.BASE_URL,
    WebSocket地址: APP_CONFIG.API.WS_URL,
    版本: APP_CONFIG.APP.VERSION,
    构建时间: APP_CONFIG.APP.BUILD_TIME,
  });

  // 验证配置是否正确挂载
  if (!(window as any).APP_CONFIG) {
    console.error('[配置初始化] 配置挂载失败');
  } else {
    console.log('[配置初始化] 配置挂载成功');
  }
}