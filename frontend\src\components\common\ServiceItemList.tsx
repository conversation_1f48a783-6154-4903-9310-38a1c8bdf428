import React from 'react';
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Alert,
  Typography
} from '@mui/material';

interface Column {
  id: string;
  label: string;
  minWidth?: number;
  align?: 'right' | 'left' | 'center';
  format?: (value: any) => React.ReactNode;
}

interface ServiceItemListProps {
  columns: Column[];
  data: any[];
  loading: boolean;
  error?: string;
  emptyMessage?: string;
  contextValid: boolean;
  contextMessage?: string;
  renderRow: (item: any, index: number) => React.ReactNode;
}

/**
 * 通用服务项目列表组件，用于显示记录和报告等数据
 * 支持自定义列配置、加载状态和错误处理
 */
const ServiceItemList: React.FC<ServiceItemListProps> = ({
  columns,
  data,
  loading,
  error,
  emptyMessage = '暂无数据',
  contextValid = true,
  contextMessage = '请先选择授权和患者',
  renderRow
}) => {
  // 加载状态
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
        <CircularProgress />
      </Box>
    );
  }
  
  // 上下文无效状态
  if (!contextValid) {
    return (
      <Alert severity="info">
        {contextMessage}
      </Alert>
    );
  }
  
  // 错误状态
  if (error) {
    return (
      <Alert severity="error">
        {error}
      </Alert>
    );
  }
  
  // 空数据状态
  if (data.length === 0) {
    return (
      <Alert severity="info">
        {emptyMessage}
      </Alert>
    );
  }
  
  // 渲染数据表格
  return (
    <TableContainer component={Paper} variant="outlined">
      <Table>
        <TableHead>
          <TableRow>
            {columns.map((column) => (
              <TableCell
                key={column.id}
                align={column.align || 'left'}
                style={{ minWidth: column.minWidth }}
              >
                <Typography variant="subtitle2" fontWeight="bold">
                  {column.label}
                </Typography>
              </TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {data.map((item, index) => renderRow(item, index))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default ServiceItemList; 