// 辅医模块类型定义

/**
 * AI报告模型
 * 存储分析结果的全部内容和元数据
 */
export interface AIReport {
  id: string;
  diseaseId: string;  // 关联病理
  patientId: string;  // 方便查询
  userId: string;     // 创建用户
  title: string;
  templateType: 'COMPREHENSIVE_ANALYSIS';
  anonymizedInfo: {   // 简化匿名化信息
    originalName: string;
    anonymizedName: string;
  };
  llmRawResponse: string; // 原始响应(非加密)
  content: AIReportContent;
  recordId: string;  // 关联记录
  pdfUrl: string;
  status?: string;   // 报告状态: PROCESSING, COMPLETED, FAILED
  errorMessage?: string; // 错误信息，当status为FAILED时提供
  createdAt: string;
  updatedAt: string;
}

/**
 * AI报告内容结构
 * 包含分析结果的各个部分
 */
export interface AIReportContent {
  summary: string;
  differentialDiagnosis: {
    possibleConditions: {
      condition: string;
      probability: number; // 0-100
      description: string;
      evidenceFrom?: string[]; // 判断依据
      confirmationMethods?: string[]; // 建议的确诊方法
      icd10Code?: string;
    }[];
    followUpQuestions?: string[];
  };
  emergencyGuidance: {
    isEmergency: boolean;
    immediateActions: string[];
    nextSteps: string[];
  };
  hospitalRecommendations: {
    targetRegion?: string; // 患者意向地区
    hospitals: {
      name: string;
      level: string;
      department: string;
      matchScore: number; // 0-100
      advantages?: string[]; // 该医院/科室的优势
      reasons?: string[]; // 旧版API兼容
      contactInfo?: {
        website?: string;
        phone?: string;
        wechatPublic?: string;
        appointmentPlatform?: string; // 如"好大夫在线"等
      };
      contact?: string; // 旧版API兼容
    }[];
  };
  treatmentPlan: {
    options: Array<
      | string // 旧版API兼容
      | {
          name: string; // 治疗方案名称
          description: string; // 详细描述
          suitabilityScore?: number; // 0-100的适合度
          prognosisData?: {
            survivalRate?: string; // 如"5年生存率约85%"
            remissionRate?: string; // 如"完全缓解率约70%"
            recurrenceRisk?: string; // 如"复发风险约20%"
          };
          budgetEstimation?: {
            minCost?: number; // 最低预算
            maxCost?: number; // 最高预算
            currency?: string; // 货币单位
            insuranceCoverage?: string; // 如"医保可报销60%，XXX项目自费"
          };
          followUpPlan?: string[]; // 后续随访计划
        }
    >;
    followUp?: string[];
  };
  budgetEstimation?: {
    low: { description: string; estimatedCost: string; tradeOffs: string[] };
    medium: { description: string; estimatedCost: string; tradeOffs: string[] };
    high: { description: string; estimatedCost: string; tradeOffs: string[] };
  };
  crossRegionGuidance?: {
    insurance: string[];
    recordSharing: string[];
    logistics: {
      transportation: string[];
      accommodation: string[];
    };
  };
  bmiRecommendations?: {
    bmiValue?: number;
    bmiStatus?: {
      status: string;
      color: string;
    };
    recommendations: string[];
  };
  lifestyleAndMentalHealth: {
    lifestyle: {
      diet: string[];
      exercise: string[];
      habits: string[];
    };
    mentalHealth: {
      copingStrategies: string[];
      resources: string[];
    };
  };
  dashboardData: {  // 仪表盘展示数据
    status: string;
    trend: 'improving' | 'stable' | 'worsening';
    riskLevel: 'low' | 'medium' | 'high';
    isEmergency: boolean;
    topHospital: string;
    budgetRange: string;
  };
  riskWarnings: string[];
  disclaimer?: string; // 旧版API兼容
  is_chronic_disease: boolean; // 表明疾病是否为慢性病
}

/**
 * 用量配额模型
 * 记录用户AI分析使用次数
 */
export interface UsageQuota {
  userId: string;
  period: string; // 格式: 'yyyy-MM'
  analysisCount: number;
  maxCount: number; // 基于用户等级
}

/**
 * 报告可见性配置
 * 控制不同用户角色可见的报告内容
 */
export interface ReportVisibilityConfig {
  userVisibleFields: string[];    // 普通用户可见字段
  serviceVisibleFields: string[]; // 服务用户可见字段
}

/**
 * AI报告摘要数据
 * 用于在Dashboard显示
 */
export interface AIReportSummary {
  lastAnalysisDate: string;
  status: string;
  trend: 'improving' | 'stable' | 'worsening';
  riskLevel: 'low' | 'medium' | 'high';
  keyRecommendations: string[];
  reportUrl: string;
  emergencyStatus: boolean;
  topHospital: string;
  budgetRange: string;
  displayFields: string[]; // 可见字段列表
}

/**
 * 用于创建AI报告的参数
 */
// 1. 定义 ReportTemplateType (如果尚不存在或不正确)
// 请根据实际有效的模板类型调整此列表
export type ReportTemplateType =
  | 'COMPREHENSIVE_ANALYSIS' // 综合分析报告
  | 'DIFFERENTIAL_DIAGNOSIS' // 鉴别诊断报告
  | 'TREATMENT_PLAN_OPTIONS' // 治疗方案选项报告
  | 'SECOND_OPINION'         // 第二诊疗意见报告
  | 'PATIENT_SUMMARY'        // 患者摘要报告
  | 'CUSTOM_REPORT';         // 自定义报告

export interface CreateAIReportParams {
  diseaseId: string;
  patientId: string;
  targetRegion?: string; // 患者意向就医地区
  templateType?: ReportTemplateType; // 使用 ReportTemplateType，设为可选
}

/**
 * AI报告创建响应
 */
export interface CreateAIReportResponse {
  aiReport: AIReport;
  recordId: string;
  status?: 'ALREADY_PROCESSING' | 'SUCCESS' | 'FAILED';  // 处理状态，用于特殊情况
  message?: string;  // 状态说明信息
}