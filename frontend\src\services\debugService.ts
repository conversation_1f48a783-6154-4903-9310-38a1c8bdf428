/**
 * API调试服务
 * 包含用于测试API连接、认证状态和路径配置的实用功能
 */

import apiClient from './apiClient';
import { getToken } from '../utils/auth';
import { AxiosError } from 'axios';

/**
 * 检测API状态和认证
 * 尝试多个请求以验证API连接和认证状态
 */
export const checkApiStatus = async (): Promise<{
  healthCheck: boolean;
  authCheck: boolean;
  aiReportsCheck: boolean;
  error?: unknown;
}> => {
  try {
    const result = {
      healthCheck: false,
      authCheck: false,
      aiReportsCheck: false,
      error: null as unknown
    };

    // 1. 健康检查 - 不需要认证
    try {
      const healthResponse = await apiClient.get('/health');
      result.healthCheck = healthResponse.status === 200;
      console.log('健康检查结果:', healthResponse.data);
    } catch (healthError) {
      console.error('健康检查失败:', healthError);
    }

    // 2. 认证检查 - 检查当前令牌是否有效
    const token = getToken();
    if (token) {
      try {
        // 查询当前用户信息以验证令牌
        const authResponse = await apiClient.get('/users/current');
        result.authCheck = authResponse.status === 200;
        console.log('认证检查结果:', authResponse.data);
      } catch (authError) {
        console.error('认证检查失败:', authError);
      }
    } else {
      console.warn('无法进行认证检查: 未找到令牌');
    }

    // 3. AI报告接口检查
    try {
      // 尝试获取配额信息，这是一个轻量级请求
      const aiResponse = await apiClient.get('/api/ai-reports/quota');
      result.aiReportsCheck = aiResponse.status === 200;
      console.log('AI报告接口检查结果:', aiResponse.data);
    } catch (error) {
      console.error('AI报告接口检查失败:', error);
      
      // 记录详细错误信息
      const aiError = error as AxiosError;
      if (aiError.response) {
        console.error('状态码:', aiError.response.status);
        console.error('响应数据:', aiError.response.data);
        console.error('响应头:', aiError.response.headers);
      } else if (aiError.request) {
        console.error('请求未收到响应:', aiError.request);
      } else {
        console.error('请求配置错误:', aiError.message);
      }
      
      result.error = error;
    }

    return result;
  } catch (error) {
    console.error('API状态检查失败:', error);
    return {
      healthCheck: false,
      authCheck: false,
      aiReportsCheck: false,
      error
    };
  }
};

export default {
  checkApiStatus
}; 