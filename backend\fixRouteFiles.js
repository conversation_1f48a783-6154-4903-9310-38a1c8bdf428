/**
 * 修复routes和src目录中文件的驼峰命名查询
 */
const fs = require('fs');
const path = require('path');

// 驼峰命名到下划线命名的映射
const camelToSnakeMap = {
  // users表
  'passwordHash': 'password_hash',
  'phoneNumber': 'phone_number',
  'isActive': 'is_active',
  'lastLoginAt': 'last_login_at',
  'activeDiseaseLimit': 'active_disease_limit', 
  'aiUsageCount': 'ai_usage_count',
  'aiUsageResetAt': 'ai_usage_reset_at',
  'familyMemberLimit': 'family_member_limit',
  'updatedAt': 'updated_at',
  
  // user_level_limits表
  'levelType': 'level_type',
  'maxPatients': 'max_patients',
  'maxPathologies': 'max_pathologies',
  'maxAttachmentSize': 'max_attachment_size',
  'maxTotalStorage': 'max_total_storage',
  
  // subscriptions表
  'userId': 'user_id',
  'startDate': 'start_date',
  'endDate': 'end_date',
  'createdAt': 'created_at',
  
  // patients表
  'birthDate': 'birth_date',
  'idCard': 'id_card',
  'medicareCard': 'medicare_card',
  'medicareLocation': 'medicare_location',
  'emergencyContactName': 'emergency_contact_name',
  'emergencyContactPhone': 'emergency_contact_phone',
  'emergencyContactRelationship': 'emergency_contact_relationship',
  'pastMedicalHistory': 'past_medical_history',
  'familyMedicalHistory': 'family_medical_history',
  'allergyHistory': 'allergy_history',
  'bloodType': 'blood_type',
  'lastVisitDate': 'last_visit_date',
  'deletedAt': 'deleted_at',
  'isPrimary': 'is_primary',
  
  // diseases表
  'patientId': 'patient_id',
  'diagnosisDate': 'diagnosis_date',
  'isDeleted': 'is_deleted',
  'isPrivate': 'is_private',
  'isActive': 'is_active',
  'isChronic': 'is_chronic',
  
  // tag_categories表
  'isSystem': 'is_system',
  'createdBy': 'created_by',
  
  // tags表
  'categoryId': 'category_id',
  
  // record_tags表
  'recordId': 'record_id',
  'tagId': 'tag_id',
  
  // records表
  'recordDate': 'record_date',
  'diseaseId': 'disease_id',
  'recordType': 'record_type',
  'primaryType': 'primary_type',
  'typeTagsJson': 'type_tags_json',
  'stageTags': 'stage_tags',
  'stageNode': 'stage_node',
  'stagePhase': 'stage_phase',
  'isImportant': 'is_important',
  'customTags': 'custom_tags',
  
  // attachments表
  'fileName': 'file_name',
  'filePath': 'file_path',
  'fileType': 'file_type',
  'fileSize': 'file_size',
  'uploadedBy': 'uploaded_by',
  'uploadedAt': 'uploaded_at'
};

/**
 * 递归获取目录下的所有JS文件
 */
function getJsFiles(dir) {
  const result = [];
  
  const files = fs.readdirSync(dir);
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      result.push(...getJsFiles(filePath));
    } else if (file.endsWith('.js')) {
      result.push(filePath);
    }
  }
  
  return result;
}

/**
 * 修复指定目录下的JS文件
 */
function fixDirectoryFiles(dirName) {
  const dir = path.join(__dirname, dirName);
  
  // 确保目录存在
  if (!fs.existsSync(dir)) {
    console.log(`${dirName}目录不存在`);
    return;
  }
  
  // 获取所有JS文件
  const jsFiles = getJsFiles(dir);
  
  for (const filePath of jsFiles) {
    try {
      // 读取文件内容
      const content = fs.readFileSync(filePath, 'utf8');
      let modifiedContent = content;
      let replacementCount = 0;
      
      // 修复各种查询模式中的驼峰命名
      for (const [camelCase, snakeCase] of Object.entries(camelToSnakeMap)) {
        // 普通对象属性: { camelCase: value }
        const objPropertyPattern = new RegExp(`({[^}]*?)([\\s,])${camelCase}(:\\s*[^,}]*)`, 'g');
        modifiedContent = modifiedContent.replace(objPropertyPattern, (match, before, spacing, after) => {
          replacementCount++;
          return `${before}${spacing}${snakeCase}${after}`;
        });
        
        // where('camelCase', value)
        const wherePattern = new RegExp(`(where\\(\\s*["'])${camelCase}(["']\\s*,[^)]*)`, 'g');
        modifiedContent = modifiedContent.replace(wherePattern, (match, before, after) => {
          replacementCount++;
          return `${before}${snakeCase}${after}`;
        });
        
        // andWhere('camelCase', value)
        const andWherePattern = new RegExp(`(andWhere\\(\\s*["'])${camelCase}(["']\\s*,[^)]*)`, 'g');
        modifiedContent = modifiedContent.replace(andWherePattern, (match, before, after) => {
          replacementCount++;
          return `${before}${snakeCase}${after}`;
        });
        
        // orWhere('camelCase', value)
        const orWherePattern = new RegExp(`(orWhere\\(\\s*["'])${camelCase}(["']\\s*,[^)]*)`, 'g');
        modifiedContent = modifiedContent.replace(orWherePattern, (match, before, after) => {
          replacementCount++;
          return `${before}${snakeCase}${after}`;
        });
        
        // select('camelCase')
        const selectPattern = new RegExp(`(select\\([^)]*["'])${camelCase}(["'][^)]*)`, 'g');
        modifiedContent = modifiedContent.replace(selectPattern, (match, before, after) => {
          replacementCount++;
          return `${before}${snakeCase}${after}`;
        });
        
        // orderBy('camelCase', direction)
        const orderByPattern = new RegExp(`(orderBy\\(\\s*["'])${camelCase}(["'][^)]*)`, 'g');
        modifiedContent = modifiedContent.replace(orderByPattern, (match, before, after) => {
          replacementCount++;
          return `${before}${snakeCase}${after}`;
        });
        
        // update({ camelCase: value }) 或 insert({ camelCase: value })
        const updateInsertPattern = new RegExp(`((update|insert)\\([^{]*{[^}]*?)${camelCase}(:\\s*[^,}]*)`, 'g');
        modifiedContent = modifiedContent.replace(updateInsertPattern, (match, before, op, after) => {
          replacementCount++;
          return `${before}${snakeCase}${after}`;
        });
      }
      
      // 只有在有修改时才写入文件
      if (replacementCount > 0) {
        // 创建备份
        fs.writeFileSync(`${filePath}.bak`, content);
        
        // 写入修改后的内容
        fs.writeFileSync(filePath, modifiedContent);
        
        // 获取相对路径，便于显示
        const relativePath = path.relative(__dirname, filePath);
        console.log(`✅ 修复文件 ${relativePath}: ${replacementCount} 处驼峰命名已替换`);
      }
    } catch (error) {
      const relativePath = path.relative(__dirname, filePath);
      console.error(`修复文件 ${relativePath} 时出错:`, error);
    }
  }
}

/**
 * 主函数：修复routes和src目录中的文件
 */
function fixRouteAndSrcFiles() {
  console.log('开始修复routes和src目录中的驼峰命名查询...\n');
  
  // 修复routes目录
  console.log('修复routes目录...');
  fixDirectoryFiles('routes');
  
  // 修复src目录
  console.log('\n修复src目录...');
  fixDirectoryFiles('src');
  
  console.log('\n修复完成！');
}

// 执行修复
fixRouteAndSrcFiles();