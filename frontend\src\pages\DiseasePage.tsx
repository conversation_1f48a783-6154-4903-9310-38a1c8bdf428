import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useNavigate, Link, useLocation } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Select,
  MenuItem,
  InputLabel,
  FormControl,
  Snackbar,
  Alert,
  AlertColor,
  CircularProgress,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Card,
  CardContent,
  Chip,
  Divider,
  useTheme,
  useMediaQuery,
  Switch
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import SearchIcon from '@mui/icons-material/Search';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { zhCN } from 'date-fns/locale';
import { format } from 'date-fns';
import LockIcon from '@mui/icons-material/Lock';
import LockOpenIcon from '@mui/icons-material/LockOpen';
import InfoOutlined from '@mui/icons-material/InfoOutlined';
import EditOutlined from '@mui/icons-material/EditOutlined';
import DeleteOutlined from '@mui/icons-material/DeleteOutlined';

import { Disease, DiseaseStages, DiseaseStageLabels } from '../types/disease';
import * as diseaseService from '../services/diseaseService';
import * as patientService from '../services/patientService';
import { Patient } from '../types/patient';
import { useAuthStore } from '../store/authStore';

// 格式化日期
const formatDate = (dateString: string | undefined): string => {
  if (!dateString) return '-';
  try {
    const date = new Date(dateString);
    return format(date, 'yyyy-MM-dd');
  } catch (e) {
    return '-';
  }
};

// 通知类型
interface Notification {
  open: boolean;
  message: string;
  type: AlertColor;
}

// 设计规范：病理阶段主色调
const stageMainColors: Record<string, string> = {
  [DiseaseStages.INITIAL]: '#E53935',    // 初诊期
  [DiseaseStages.DIAGNOSIS]: '#8E24AA', // 确诊期
  [DiseaseStages.TREATMENT]: '#FB8C00', // 治疗期
  [DiseaseStages.RECOVERY]: '#FDD835',  // 康复期
  [DiseaseStages.PROGNOSIS]: '#43A047', // 预后期
};

// 设计规范：病理阶段主色调、标签背景色、深色文字
const stageChipStyles: Record<string, {bg: string, color: string, border: string}> = {
  [DiseaseStages.INITIAL]:   { bg: '#FFEBEE', color: '#C62828', border: '#E53935' }, // 初诊
  [DiseaseStages.DIAGNOSIS]: { bg: '#F3E5F5', color: '#6A1B9A', border: '#8E24AA' }, // 确诊
  [DiseaseStages.TREATMENT]: { bg: '#FFF3E0', color: '#EF6C00', border: '#FB8C00' }, // 治疗
  [DiseaseStages.RECOVERY]:  { bg: '#FFFDE7', color: '#F9A825', border: '#FDD835' }, // 康复
  [DiseaseStages.PROGNOSIS]: { bg: '#E8F5E9', color: '#2E7D32', border: '#43A047' }, // 预后
};

// 疾病表单数据
interface DiseaseFormData {
  patientId: string;
  name: string;
  diagnosisDate: string;
  stage: string;
  description: string;
  treatment: string;
}

// 创建一个截断文本并添加省略号的辅助函数
const truncateText = (text: string, maxLength: number) => {
  if (!text) return '无描述';
  return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
};

// 病理管理页面组件
const DiseasePage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation(); // 添加location钩子
  const token = useAuthStore((state) => state.token);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // 判断当前是否在普通用户上下文
  const isUserContext = useMemo(() => {
    // 检查路径是否以/admin开头
    return !location.pathname.startsWith('/admin');
  }, [location.pathname]);

  // 状态定义
  const [diseases, setDiseases] = useState<Disease[]>([]);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [notification, setNotification] = useState<Notification>({
    open: false,
    message: '',
    type: 'success'
  });

  // 筛选和搜索状态
  const [filters, setFilters] = useState({
    patientId: '',
    stage: '',
    name: ''
  });

  // 对话框状态
  const [dialogOpen, setDialogOpen] = useState<boolean>(false);
  const [dialogMode, setDialogMode] = useState<'create' | 'edit'>('create');
  const [currentDisease, setCurrentDisease] = useState<Disease | null>(null);
  const [formData, setFormData] = useState<DiseaseFormData>({
    patientId: '',
    name: '',
    diagnosisDate: '',
    stage: DiseaseStages.INITIAL,
    description: '',
    treatment: ''
  });

  // 确认删除对话框状态
  const [deleteDialogOpen, setDeleteDialogOpen] = useState<boolean>(false);
  const [diseaseToDelete, setDiseaseToDelete] = useState<Disease | null>(null);

  // 获取疾病列表 - 使用useCallback
  const fetchDiseases = useCallback(async () => {
    try {
      setLoading(true);
      // 传入上下文参数
      const data = await diseaseService.getDiseases(filters, isUserContext);
      setDiseases(data);
      setLoading(false);
    } catch (err: any) {
      console.error('获取疾病列表失败', err);
      setError('获取疾病列表失败');
      setLoading(false);
    }
  }, [filters, isUserContext]); // 依赖增加isUserContext

  // 获取患者列表（用于下拉选择）- 使用useCallback
  const fetchPatients = useCallback(async () => {
    try {
      const data = await patientService.getPatients();
      setPatients(data);
    } catch (err: any) {
      console.error('获取患者列表失败', err);
    }
  }, []); // 无依赖项

  // 页面加载时获取数据
  useEffect(() => {
    if (!token) {
      navigate('/login');
      return;
    }

    fetchPatients();
    fetchDiseases();
  }, [token, navigate, fetchPatients, fetchDiseases]);

  // 筛选条件改变时重新获取数据 - 不再需要这个useEffect
  // 因为fetchDiseases已经依赖filters，当filters变化时fetchDiseases会重新创建
  // 然后上面的useEffect会重新执行

  // 处理筛选变化
  const handleFilterChange = (field: keyof typeof filters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 重置筛选条件
  // 暂未使用，添加 eslint 注释防止警告
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const resetFilters = () => {
    setFilters({
      patientId: '',
      stage: '',
      name: ''
    });
  };

  // 打开创建对话框
  const handleOpenCreateDialog = () => {
    setDialogMode('create');

    // 查找默认患者（本人）
    const primaryPatient = patients.find(p => p.isPrimary === 1);

    setFormData({
      patientId: primaryPatient ? primaryPatient.id : '',
      name: '',
      diagnosisDate: '',
      stage: DiseaseStages.INITIAL, // 默认初诊期
      description: '',
      treatment: ''
    });
    setDialogOpen(true);
  };

  // 打开编辑对话框
  const handleOpenEditDialog = (disease: Disease) => {
    setDialogMode('edit');
    setCurrentDisease(disease);
    setFormData({
      patientId: disease.patientId,
      name: disease.name,
      diagnosisDate: formatDate(disease.diagnosisDate),
      stage: disease.stage,
      description: disease.description || '',
      treatment: disease.treatment || ''
    });
    setDialogOpen(true);
  };

  // 关闭对话框
  const handleCloseDialog = () => {
    setDialogOpen(false);
    setCurrentDisease(null);
  };

  // 处理表单输入变化
  const handleInputChange = (field: keyof DiseaseFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 处理日期变化
  const handleDateChange = (date: Date | null) => {
    if (date) {
      setFormData(prev => ({
        ...prev,
        diagnosisDate: format(date, 'yyyy-MM-dd')
      }));
    }
  };

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      if (dialogMode === 'create') {
        await diseaseService.createDisease({
          ...formData,
          isDeleted: false,
          status: 'ACTIVE'
        });
        setNotification({
          open: true,
          message: '疾病记录创建成功',
          type: 'success'
        });
      } else {
        if (currentDisease) {
          await diseaseService.updateDisease(currentDisease.id, {
            ...formData,
            isDeleted: false
          });
          setNotification({
            open: true,
            message: '疾病记录更新成功',
            type: 'success'
          });
        }
      }

      handleCloseDialog();
      fetchDiseases(); // 刷新列表
    } catch (err: any) {
      console.error('保存疾病记录失败', err);
      setNotification({
        open: true,
        message: '保存疾病记录失败',
        type: 'error'
      });
    }
  };

  // 打开删除确认对话框
  const handleOpenDeleteDialog = (disease: Disease) => {
    setDiseaseToDelete(disease);
    setDeleteDialogOpen(true);
  };

  // 关闭删除确认对话框
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setDiseaseToDelete(null);
  };

  // 确认删除
  const handleConfirmDelete = async () => {
    if (!diseaseToDelete) return;

    try {
      await diseaseService.deleteDisease(diseaseToDelete.id);
      setNotification({
        open: true,
        message: '疾病记录已删除',
        type: 'success'
      });
      handleCloseDeleteDialog();
      fetchDiseases(); // 刷新列表
    } catch (err: any) {
      console.error('删除疾病记录失败', err);
      setNotification({
        open: true,
        message: '删除疾病记录失败',
        type: 'error'
      });
    }
  };

  // 关闭通知
  const handleCloseNotification = () => {
    setNotification({
      ...notification,
      open: false
    });
  };

  // 查看疾病详情
  // 暂未使用，添加 eslint 注释防止警告
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleViewDetails = (disease: Disease) => {
    // 可以导航到详情页，或显示详情对话框
    // navigate(`/diseases/${disease.id}`);

    // 这里简单实现为打开编辑对话框但不允许编辑
    handleOpenEditDialog(disease);
  };

  // 获取患者姓名
  const getPatientName = (patientId: string) => {
    const patient = patients.find(p => p.id === patientId);
    return patient ? patient.name : '未知患者';
  };

  // 在getPatientName函数后增加判断隐私状态的辅助函数
  const getPrivacyStatus = (disease: Disease) => {
    // 检查isPrivate属性是否为 true (布尔值) 或 1 (数字)
    return Boolean(disease.isPrivate);
  };

  // 处理隐私状态切换的函数
  const handlePrivacyToggle = async (disease: Disease) => {
    try {
      // 将当前状态取反
      const newPrivacyStatus = disease.isPrivate === 1 ? 0 : 1;

      // 更新数据库 - 只发送必要的isPrivate字段，不发送整个对象
      await diseaseService.updateDisease(disease.id, {
        isPrivate: newPrivacyStatus // 只发送这一个字段
      });

      // 更新本地状态
      setDiseases(diseases.map(d =>
        d.id === disease.id ? { ...d, isPrivate: newPrivacyStatus } : d
      ));

      // 显示成功消息
      setNotification({
        open: true,
        message: newPrivacyStatus === 1
          ? `疾病记录已设为隐私状态`
          : `疾病记录已解除隐私状态`,
        type: 'success'
      });
    } catch (err: any) {
      console.error('更新隐私状态失败', err);
      setNotification({
        open: true,
        message: '更新隐私状态失败',
        type: 'error'
      });
    }
  };

  return (
    <Box sx={{
      m: 0,
      width: '100%',
      paddingLeft: '10px',
      paddingRight: '10px',
      fontSize: '0.875rem', // 添加全局字体大小缩小，基础字体
    }}>
      {/* 顶部标题与添加按钮同行 */}
      <Box sx={{ display: 'flex', alignItems: 'flex-end', justifyContent: 'space-between', mb: 0 }}>
        <Typography
          variant="h5"
          component="h1"
          sx={{
            fontWeight: 500,
            fontSize: { xs: '1.1rem', sm: '1.3rem' },
            mb: 0
          }}
        >
          疾病管理
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleOpenCreateDialog}
          sx={{ minWidth: 100, ml: 2, fontSize: '0.75rem', mb: '3px' }}
        >
          添加疾病
        </Button>
      </Box>
      <Divider sx={{ mb: { xs: 2, md: 3 } }} />
      {/* 错误提示 */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}
      {/* 筛选和搜索区域 */}
      <Box
        sx={{
          p: 0,
          mb: { xs: 2, md: 3 },
          width: '100%',
          '& .MuiInputBase-root': { fontSize: '0.75rem' },
          '& .MuiInputLabel-root': { fontSize: '0.75rem' },
          '& .MuiMenuItem-root': { fontSize: '0.75rem' }
        }}
      >
        {/* 搜索框单独一行 */}
        <Box sx={{ mb: 2, width: '100%' }}>
          <TextField
            fullWidth
            size="small"
            label="搜索疾病名称"
            variant="outlined"
            value={filters.name}
            onChange={(e) => handleFilterChange('name', e.target.value)}
            InputProps={{ startAdornment: <SearchIcon color="action" sx={{ mr: 1 }} /> }}
          />
        </Box>
        {/* 只保留阶段和患者筛选，无重置按钮 */}
        <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' }, gap: 2, width: '100%', mb: 2 }}>
          <FormControl fullWidth size="small">
            <InputLabel id="stage-filter-label">按阶段筛选</InputLabel>
            <Select
              labelId="stage-filter-label"
              value={filters.stage}
              onChange={(e) => handleFilterChange('stage', e.target.value)}
              label="按阶段筛选"
            >
              <MenuItem value="">全部阶段</MenuItem>
              {Object.values(DiseaseStages).map((stage) => (
                <MenuItem key={stage} value={stage}>
                  {DiseaseStageLabels[stage as DiseaseStages]}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <FormControl fullWidth size="small">
            <InputLabel id="patient-filter-label">按患者筛选</InputLabel>
            <Select
              labelId="patient-filter-label"
              value={filters.patientId}
              onChange={(e) => handleFilterChange('patientId', e.target.value)}
              label="按患者筛选"
            >
              <MenuItem value="">全部患者</MenuItem>
              {patients.map((patient) => (
                <MenuItem key={patient.id} value={patient.id}>
                  {patient.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
        {/* 统计信息展示 */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1, color: 'text.secondary', width: '100%' }}>
          <Typography variant="body2" sx={{ fontSize: '0.7rem' }}>
            {filters.stage ? `${DiseaseStageLabels[filters.stage as DiseaseStages]}阶段疾病` : '所有阶段疾病'}
            {filters.patientId ? ` - ${getPatientName(filters.patientId)}` : ''}
          </Typography>
          <Typography variant="body2" sx={{ fontSize: '0.7rem' }}>
            共 {Array.isArray(diseases) ? diseases.length : 0} 条记录
          </Typography>
        </Box>
      </Box>
      {/* 加载中 */}
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
          <CircularProgress />
        </Box>
      )}
      {/* 疾病列表 - 移动端卡片式布局 */}
      {!loading && !error && (
        Array.isArray(diseases) && diseases.length > 0 ? (
          isMobile ? (
            // 移动端卡片式布局
            <>
              {diseases.map((disease) => (
                <Card
                  key={disease.id}
                  elevation={0}
                  sx={{
                    mb: 2,
                    border: '1px solid #e0e0e0',
                    pl: 0,
                    position: 'relative',
                    display: 'flex',
                    flexDirection: 'column',
                    fontSize: '0.75rem' // 全局减小卡片字体
                  }}
                >
                  {/* 左侧色条，使用主色调 */}
                  <Box sx={{
                    position: 'absolute',
                    left: 0,
                    top: 0,
                    bottom: 0,
                    width: '4px',
                    borderRadius: '4px 0 0 4px',
                    background: stageMainColors[disease.stage] || '#2196f3',
                  }} />
                  <CardContent sx={{ pb: '8px !important', pl: 1.5 }}>
                    <Box>
                      {/* 顶部：病理名减小字号+阶段标签+操作按钮 */}
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Typography variant="subtitle1" component="div" sx={{ fontWeight: 600, fontSize: '0.9rem' }}>
                            {disease.name}
                          </Typography>
                          <Chip
                            label={DiseaseStageLabels[disease.stage as DiseaseStages] || disease.stage}
                            size="small"
                            sx={{
                              ml: 1,
                              px: 0.5,
                              height: 18,
                              fontSize: '0.65rem',
                              fontWeight: 500,
                              background: stageChipStyles[disease.stage]?.bg,
                              color: stageChipStyles[disease.stage]?.color,
                              border: `1px solid ${stageChipStyles[disease.stage]?.border}`,
                              borderRadius: 1
                            }}
                          />
                        </Box>
                        <Box>
                          <Tooltip title="查看详情">
                            <IconButton size="small" color="primary" component={Link} to={`/diseases/${disease.id}`}>
                              <InfoOutlined fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="编辑">
                            <IconButton size="small" color="primary" component={Link} to={`/diseases/${disease.id}?edit=true`}>
                              <EditOutlined fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="删除">
                            <IconButton size="small" color="error" onClick={() => handleOpenDeleteDialog(disease)}>
                              <DeleteOutlined fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </Box>
                      {/* 患者姓名、诊断日期 */}
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                        <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.7rem' }}>
                          患者：{getPatientName(disease.patientId)}
                        </Typography>
                        <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem' }}>
                          诊断日期：{formatDate(disease.diagnosisDate)}
                        </Typography>
                      </Box>
                      {/* 疾病描述与隐私状态控件同一行 */}
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 0.5, mb: 0.5 }}>
                        {/* 疾病描述，字号减小，超长文本缩略显示 */}
                        <Tooltip title={disease.description || '无描述'} arrow placement="top">
                          <Typography
                            variant="caption"
                            color="text.secondary"
                            sx={{
                              fontSize: '0.7rem',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                              maxWidth: '60%' // 限制最大宽度，避免挤压右侧控件
                            }}
                          >
                            {truncateText(disease.description || '', 15)}
                          </Typography>
                        </Tooltip>

                        {/* 隐私状态切换开关 */}
                        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 0.5, alignItems: 'center' }}>
                          {/* 始终显示锁图标和隐私文字，根据状态改变颜色 */}
                          {getPrivacyStatus(disease) ? (
                            <LockIcon fontSize="small" color="error" sx={{ mr: 0.5, fontSize: '0.9rem' }} />
                          ) : (
                            <LockOpenIcon fontSize="small" color="disabled" sx={{ mr: 0.5, fontSize: '0.9rem' }} />
                          )}
                          <Typography
                            variant="caption"
                            sx={{
                              fontSize: '0.7rem',
                              color: getPrivacyStatus(disease) ? 'error.main' : 'text.disabled',
                              mr: 1
                            }}
                          >
                            隐私
                          </Typography>
                          <Switch
                            size="small"
                            checked={getPrivacyStatus(disease)}
                            onChange={() => handlePrivacyToggle(disease)}
                            sx={{
                              ml: 0.5,
                              transform: 'scale(0.7)',
                              '& .MuiSwitch-thumb': {
                                backgroundColor: getPrivacyStatus(disease) ? 'error.main' : undefined
                              },
                              '& .MuiSwitch-track': {
                                backgroundColor: getPrivacyStatus(disease) ? 'error.light' : undefined
                              }
                            }}
                          />
                        </Box>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              ))}
            </>
          ) : (
            // 桌面端表格布局
            <TableContainer
              component={Paper}
              elevation={0}
              sx={{
                border: '1px solid #e0e0e0',
                borderRadius: 2,
                overflow: 'hidden', // 避免表格溢出圆角
                '& .MuiTableCell-root': { fontSize: '0.75rem' } // 减小所有表格单元格字体
              }}
            >
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ fontWeight: 'bold', fontSize: '0.75rem' }}>患者姓名</TableCell>
                    <TableCell sx={{ fontWeight: 'bold', fontSize: '0.75rem' }}>疾病名称</TableCell>
                    <TableCell sx={{ fontWeight: 'bold', fontSize: '0.75rem' }}>诊断日期</TableCell>
                    <TableCell sx={{ fontWeight: 'bold', fontSize: '0.75rem' }}>阶段</TableCell>
                    <TableCell sx={{ fontWeight: 'bold', fontSize: '0.75rem' }}>隐私状态</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold', fontSize: '0.75rem' }}>操作</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {Array.isArray(diseases) && diseases.map((disease) => (
                    <TableRow key={disease.id} hover sx={{ '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' } }}>
                      <TableCell sx={{ fontSize: '0.75rem' }}>{getPatientName(disease.patientId)}</TableCell>
                      <TableCell sx={{ fontSize: '0.75rem' }}>{disease.name}</TableCell>
                      <TableCell sx={{ fontSize: '0.75rem' }}>{formatDate(disease.diagnosisDate)}</TableCell>
                      <TableCell sx={{ fontSize: '0.75rem' }}>
                        <Chip
                          label={DiseaseStageLabels[disease.stage as DiseaseStages] || disease.stage}
                          size="small"
                          sx={{
                            px: 0.5,
                            height: 20,
                            fontSize: '0.7rem',
                            fontWeight: 500,
                            background: stageChipStyles[disease.stage]?.bg,
                            color: stageChipStyles[disease.stage]?.color,
                            border: `1px solid ${stageChipStyles[disease.stage]?.border}`,
                            borderRadius: 1.5
                          }}
                        />
                      </TableCell>
                      <TableCell sx={{ fontSize: '0.75rem' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {getPrivacyStatus(disease) ? (
                            <LockIcon fontSize="small" color="error" sx={{ mr: 0.5, fontSize: '0.9rem' }} />
                          ) : (
                            <LockOpenIcon fontSize="small" color="disabled" sx={{ mr: 0.5, fontSize: '0.9rem' }} />
                          )}
                          <Typography
                            variant="caption"
                            sx={{
                              fontSize: '0.7rem',
                              color: getPrivacyStatus(disease) ? 'error.main' : 'text.disabled',
                              mr: 1
                            }}
                          >
                            隐私
                          </Typography>
                          <Switch
                            size="small"
                            checked={getPrivacyStatus(disease)}
                            onChange={() => handlePrivacyToggle(disease)}
                            sx={{
                              transform: 'scale(0.7)',
                              '& .MuiSwitch-thumb': {
                                backgroundColor: getPrivacyStatus(disease) ? 'error.main' : undefined
                              },
                              '& .MuiSwitch-track': {
                                backgroundColor: getPrivacyStatus(disease) ? 'error.light' : undefined
                              }
                            }}
                          />
                        </Box>
                      </TableCell>
                      <TableCell align="right" sx={{ fontSize: '0.75rem' }}>
                        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                          <Tooltip title="查看详情">
                            <IconButton size="small" color="primary" component={Link} to={`/diseases/${disease.id}`}>
                              <InfoOutlined fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="编辑">
                            <IconButton size="small" color="primary" component={Link} to={`/diseases/${disease.id}?edit=true`}>
                              <EditOutlined fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="删除">
                            <IconButton size="small" color="error" onClick={() => handleOpenDeleteDialog(disease)}>
                              <DeleteOutlined fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )
        ) : (
          <Box sx={{
            p: 3,
            textAlign: 'center',
            border: '1px solid #e0e0e0',
            borderRadius: 2,
            mb: 3
          }}>
            <Typography color="text.secondary" gutterBottom sx={{ fontSize: '0.75rem' }}>
              暂无疾病记录
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleOpenCreateDialog}
              sx={{ mt: 1, fontSize: '0.75rem' }}
            >
              添加第一条疾病记录
            </Button>
          </Box>
        )
      )}

      {/* 创建/编辑对话框 */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle sx={{ fontSize: '1rem' }}>
          {dialogMode === 'create' ? '添加疾病记录' : '编辑疾病记录'}
        </DialogTitle>
        <DialogContent dividers>
          <Box sx={{
            display: 'grid',
            gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)' },
            gap: 2,
            mt: 2,
            '& .MuiInputBase-root': { fontSize: '0.75rem' },
            '& .MuiInputLabel-root': { fontSize: '0.75rem' },
            '& .MuiFormControlLabel-label': { fontSize: '0.75rem' }
          }}>
            <FormControl fullWidth required>
              <InputLabel id="patient-select-label">患者</InputLabel>
              <Select
                labelId="patient-select-label"
                value={formData.patientId}
                onChange={(e) => handleInputChange('patientId', e.target.value)}
                label="患者"
                required
                disabled={dialogMode === 'edit'} // 编辑模式下不允许更改患者
              >
                <MenuItem value="">请选择患者</MenuItem>
                {Array.isArray(patients) && patients.map((patient) => (
                  <MenuItem key={patient.id} value={patient.id}>
                    {patient.name}{patient.isPrimary ? ' (本人)' : ''}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <TextField
              fullWidth
              label="疾病名称"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              required
            />

            <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={zhCN}>
              <DatePicker
                label="诊断日期"
                value={formData.diagnosisDate ? new Date(formData.diagnosisDate) : null}
                onChange={handleDateChange}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    required: false
                  }
                }}
              />
            </LocalizationProvider>

            <FormControl fullWidth required>
              <InputLabel id="stage-select-label">疾病阶段</InputLabel>
              <Select
                labelId="stage-select-label"
                value={formData.stage}
                onChange={(e) => handleInputChange('stage', e.target.value)}
                label="疾病阶段"
                required
              >
                {Object.values(DiseaseStages).map((stage) => (
                  <MenuItem key={stage} value={stage}>
                    {DiseaseStageLabels[stage as DiseaseStages]}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <TextField
              fullWidth
              label="疾病描述"
              multiline
              rows={3}
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              sx={{ gridColumn: '1 / -1' }}
              required
            />

            <TextField
              fullWidth
              label="遗嘱及用药或备注"
              multiline
              rows={3}
              value={formData.treatment}
              onChange={(e) => handleInputChange('treatment', e.target.value)}
              sx={{ gridColumn: '1 / -1' }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} sx={{ fontSize: '0.75rem' }}>取消</Button>
          <Button
            onClick={handleSubmit}
            color="primary"
            variant="contained"
            disabled={!formData.patientId || !formData.name || !formData.stage || !formData.description}
            sx={{ fontSize: '0.75rem' }}
          >
            保存
          </Button>
        </DialogActions>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <DialogTitle id="delete-dialog-title" sx={{ fontSize: '1rem' }}>
          确认删除
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" id="delete-dialog-description" sx={{ fontSize: '0.85rem' }}>
            您确定要删除疾病记录"{diseaseToDelete?.name}"吗？此操作不可恢复。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog} color="primary" sx={{ fontSize: '0.75rem' }}>
            取消
          </Button>
          <Button onClick={handleConfirmDelete} color="error" variant="contained" autoFocus sx={{ fontSize: '0.75rem' }}>
            删除
          </Button>
        </DialogActions>
      </Dialog>

      {/* 通知提示 */}
      <Snackbar
        open={notification.open}
        autoHideDuration={5000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseNotification} severity={notification.type} sx={{ width: '100%', '& .MuiAlert-message': { fontSize: '0.75rem' } }}>
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default DiseasePage;