/**
 * 修复用户配额脚本
 * 用于管理员手动纠正用户配额数据
 */
const path = require('path');
const { Model } = require('objection');
const Knex = require('knex');

// 初始化数据库连接
const knex = Knex({
  client: 'sqlite3',
  connection: {
    filename: path.join(__dirname, '../db.sqlite')
  },
  useNullAsDefault: true
});

// 设置Objection.js使用这个knex实例
Model.knex(knex);

// 导入模型
const { AIReportQuota } = require('../models/aiReport');

// 要修复的用户ID
const USER_ID = 'b11a5bb0-bba3-4cc3-9e0a-8248ba681453';

// 正确的配额值
const CORRECT_VALUES = {
  used_this_month: 10,  // 当前月使用次数
  monthly_quota: 20,    // 月度配额
  additional_quota: 0   // 额外配额
};

async function fixUserQuota() {
  try {
    console.log(`开始修复用户(${USER_ID})的配额数据...`);
    
    // 查询当前配额数据
    const quota = await AIReportQuota.query().where({ user_id: USER_ID }).first();
    
    if (!quota) {
      console.log('未找到该用户的配额记录，将创建新记录');
      
      // 创建新的配额记录
      const newQuota = await AIReportQuota.query().insert({
        user_id: USER_ID,
        monthly_quota: CORRECT_VALUES.monthly_quota,
        used_this_month: CORRECT_VALUES.used_this_month,
        total_used: CORRECT_VALUES.used_this_month, // 设置为与当月使用量相同
        additional_quota: CORRECT_VALUES.additional_quota,
        last_reset_date: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });
      
      console.log('成功创建配额记录:', newQuota);
    } else {
      console.log('找到现有配额记录:', quota);
      console.log('准备更新为正确值');
      
      // 更新为正确的配额值
      const updatedQuota = await AIReportQuota.query()
        .findById(quota.id)
        .patch({
          monthly_quota: CORRECT_VALUES.monthly_quota,
          used_this_month: CORRECT_VALUES.used_this_month,
          additional_quota: CORRECT_VALUES.additional_quota,
          updated_at: new Date().toISOString()
        });
      
      console.log('配额更新成功，影响的行数:', updatedQuota);
      
      // 再次查询验证
      const verifiedQuota = await AIReportQuota.query().findById(quota.id);
      console.log('验证更新后的配额:', verifiedQuota);
    }
    
    console.log('配额修复完成!');
  } catch (error) {
    console.error('修复配额时出错:', error);
  } finally {
    // 关闭数据库连接
    await knex.destroy();
  }
}

// 执行修复
fixUserQuota(); 