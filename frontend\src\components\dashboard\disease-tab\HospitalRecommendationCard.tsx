import React, { useState, useEffect, useMemo } from 'react';
import { 
  Box, 
  Typography, 
  CircularProgress,
  Divider,
  List,
  ListItem,
  ListItemAvatar,
  Avatar,
  Chip,
  Link,
  Button
} from '@mui/material';
import {
  LocalHospital as HospitalIcon,
  LocationOn as LocationIcon,
  Phone as PhoneIcon,
  PublicOutlined as WebsiteIcon,
  StarRate as StarIcon,
  MedicalServices as MedicalIcon,
  CheckCircle as AdvantageIcon,
  ChatOutlined as WechatIcon
} from '@mui/icons-material';
import BaseCard from './BaseCard';

interface HospitalRecommendationCardProps {
  aiReport: any;
  loading?: boolean;
  hospitalRecommendations?: any[];
  isDemo?: boolean;
}

interface Hospital {
  id?: string | number;
  name: string;
  address?: string;
  distance?: string;
  rating?: number;
  departments?: string[];
  tags?: string[];
  isRecommended?: boolean;
  // 添加原始JSON数据中的字段
  level?: string;
  department?: string;
  matchScore?: number;
  advantages?: string[];
  contactInfo?: {
    website?: string;
    phone?: string;
    wechatPublic?: string;
    appointmentPlatform?: string;
  };
}

/**
 * 医院推荐卡片组件
 * 显示适合该病理的医院推荐
 */
const HospitalRecommendationCard: React.FC<HospitalRecommendationCardProps> = ({ 
  aiReport, 
  loading = false,
  hospitalRecommendations = [],
  isDemo = false
}) => {
  // 添加演示数据的状态 - 默认根据isDemo决定是否启用演示数据
  const [useDemo, setUseDemo] = useState(isDemo);
  // 添加展开详情的状态
  const [expandedHospitals, setExpandedHospitals] = useState<{[key: string]: boolean}>({});
  
  // 当isDemo属性变化时更新useDemo状态
  useEffect(() => {
    setUseDemo(isDemo);
    console.log('HospitalRecommendationCard - isDemo变化:', isDemo);
  }, [isDemo]);
  
  // 切换医院详情展开状态
  const toggleHospitalExpand = (hospitalId: string | number) => {
    setExpandedHospitals(prev => ({
      ...prev,
      [hospitalId.toString()]: !prev[hospitalId.toString()]
    }));
  };
  
  // 添加日志输出，便于调试
  console.log('HospitalRecommendationCard接收到的数据详情:', {
    hospitalRecommendations: Array.isArray(hospitalRecommendations) ? `${hospitalRecommendations.length}条医院推荐` : '无数据',
    isDemo: isDemo
  });
  
  // 获取默认推荐医院
  const getDefaultHospitals = (): Hospital[] => [
    {
      id: 1,
      name: '北京协和医院',
      level: '三级甲等',
      department: '综合诊疗科',
      matchScore: 95,
      advantages: [
        '国家顶级综合医院',
        '拥有先进医疗设备和技术',
        '多学科诊疗团队协作'
      ],
      contactInfo: {
        website: 'www.pumch.cn',
        phone: '010-69151188',
        wechatPublic: '北京协和医院官方服务号',
        appointmentPlatform: '北京协和医院App预约挂号'
      },
      isRecommended: true
    },
    {
      id: 2,
      name: '中国医学科学院肿瘤医院',
      level: '三级甲等',
      department: '专科医疗中心',
      matchScore: 90,
      advantages: [
        '国家级肿瘤专科研究中心',
        '先进的靶向治疗和免疫治疗',
        '个体化精准医疗方案'
      ],
      contactInfo: {
        website: 'www.cicams.ac.cn',
        phone: '010-87788899',
        wechatPublic: '中国医学科学院肿瘤医院',
        appointmentPlatform: '医院官网或健康160平台'
      },
      isRecommended: true
    }
  ];
  
  // 从AI报告中提取医院推荐
  const hospitals = useMemo((): Hospital[] => {
    // 优先使用传入的推荐医院数据
    if (hospitalRecommendations && hospitalRecommendations.length > 0) {
      console.log('使用传入的医院推荐数据:', hospitalRecommendations.length);
      return hospitalRecommendations.map((hospital: any, index: number) => ({
        id: hospital.id || `hospital-${index}`,
        name: hospital.name || hospital.hospitalName || '未知医院',
        address: hospital.address || hospital.location || hospital.details,
        rating: hospital.rating || hospital.score,
        tags: hospital.tags || hospital.specialties || [],
        departments: hospital.departments || [],
        isRecommended: true,
        // 保留原始数据字段
        level: hospital.level,
        department: hospital.department,
        matchScore: hospital.matchScore,
        advantages: hospital.advantages || [],
        contactInfo: hospital.contactInfo || {}
      }));
    }
    
    if (!aiReport) return [];
    
    // 尝试从AI报告获取已结构化的医院信息
    if (aiReport.content && typeof aiReport.content === 'object') {
      // 直接从内容对象中提取医院信息
      const { 
        hospitalRecommendations, 
        recommendedHospitals, 
        hospitals,
        medicalFacilities,
        recommendedMedicalFacilities
      } = aiReport.content;
      
      // 处理hospitals字段 - 优先匹配
      if (hospitals && Array.isArray(hospitals)) {
        return hospitals.map((hospital: any, index: number) => ({
          id: hospital.id || `hospital-${index}`,
          name: hospital.name || hospital.hospitalName || '未知医院',
          address: hospital.address || hospital.location,
          rating: hospital.rating || hospital.score,
          tags: hospital.tags || hospital.specialties || [],
          departments: hospital.departments || [],
          isRecommended: hospital.recommended !== false,
          // 保留原始数据字段
          level: hospital.level,
          department: hospital.department,
          matchScore: hospital.matchScore,
          advantages: hospital.advantages || [],
          contactInfo: hospital.contactInfo || {}
        }));
      }
      
      // 检查多个可能的字段名称
      if (hospitalRecommendations && Array.isArray(hospitalRecommendations)) {
        return hospitalRecommendations.map((hospital: any, index: number) => ({
          id: hospital.id || `hospital-${index}`,
          name: hospital.name || hospital.hospitalName || '未知医院',
          address: hospital.address || hospital.location,
          rating: hospital.rating || hospital.score,
          tags: hospital.tags || hospital.specialties || [],
          departments: hospital.departments || [],
          isRecommended: true,
          // 保留原始数据字段
          level: hospital.level,
          department: hospital.department,
          matchScore: hospital.matchScore,
          advantages: hospital.advantages || [],
          contactInfo: hospital.contactInfo || {}
        }));
      }
      
      if (recommendedHospitals && Array.isArray(recommendedHospitals)) {
        return recommendedHospitals.map((hospital: any, index: number) => ({
          id: hospital.id || `hospital-${index}`,
          name: hospital.name || hospital.hospitalName || '未知医院',
          address: hospital.address || hospital.location,
          rating: hospital.rating || hospital.score,
          tags: hospital.tags || hospital.specialties || [],
          departments: hospital.departments || [],
          isRecommended: true,
          // 保留原始数据字段
          level: hospital.level,
          department: hospital.department,
          matchScore: hospital.matchScore,
          advantages: hospital.advantages || [],
          contactInfo: hospital.contactInfo || {}
        }));
      }
      
      if (medicalFacilities && Array.isArray(medicalFacilities)) {
        return medicalFacilities.map((hospital: any, index: number) => ({
          id: hospital.id || `hospital-${index}`,
          name: hospital.name || hospital.facilityName || '未知医院',
          address: hospital.address || hospital.location,
          rating: hospital.rating || hospital.score,
          tags: hospital.tags || hospital.specialties || [],
          departments: hospital.departments || [],
          isRecommended: hospital.recommended !== false,
          // 保留原始数据字段
          level: hospital.level,
          department: hospital.department,
          matchScore: hospital.matchScore,
          advantages: hospital.advantages || [],
          contactInfo: hospital.contactInfo || {}
        }));
      }
      
      if (recommendedMedicalFacilities && Array.isArray(recommendedMedicalFacilities)) {
        return recommendedMedicalFacilities.map((hospital: any, index: number) => ({
          id: hospital.id || `hospital-${index}`,
          name: hospital.name || hospital.facilityName || '未知医院',
          address: hospital.address || hospital.location,
          rating: hospital.rating || hospital.score,
          tags: hospital.tags || hospital.specialties || [],
          departments: hospital.departments || [],
          isRecommended: true,
          // 保留原始数据字段
          level: hospital.level,
          department: hospital.department,
          matchScore: hospital.matchScore,
          advantages: hospital.advantages || [],
          contactInfo: hospital.contactInfo || {}
        }));
      }
    }
    
    // 尝试从AI报告中提取文本形式的医院信息
    if (aiReport.content && typeof aiReport.content === 'string') {
      const contentStr = aiReport.content;
      const hospitalMatch = contentStr.match(/推荐医院[:：]([\s\S]+?)(?=\n\n|$)/i) || 
                           contentStr.match(/医院推荐[:：]([\s\S]+?)(?=\n\n|$)/i);
      
      if (hospitalMatch && hospitalMatch[1]) {
        // 简单解析文本中的医院信息
        const hospitalText = hospitalMatch[1].trim();
        const hospitals = hospitalText.split(/\n\d+[.、]|\n[-*•]/).filter(Boolean);
        
        return hospitals.map((text: string, index: number) => {
          // 尝试从文本中提取医院名称和地址
          const hospitalInfo = text.trim();
          const nameMatch = hospitalInfo.match(/^(.+?)(?:[（(,，]|地址|位于|位于)/);
          const addressMatch = hospitalInfo.match(/地址[:：](.+?)(?:\n|$)|位于(.+?)(?:\n|$)/i);
          
          return {
            id: `text-hospital-${index}`,
            name: nameMatch ? nameMatch[1].trim() : hospitalInfo.substring(0, 20),
            address: addressMatch ? (addressMatch[1] || addressMatch[2]).trim() : '',
            isRecommended: true
          };
        });
      }
    }
    
    // 尝试从hospitals字段直接获取医院信息
    if (aiReport.hospitals && Array.isArray(aiReport.hospitals)) {
      return aiReport.hospitals.map((hospital: any, index: number) => ({
        id: hospital.id || `hospital-${index}`,
        name: hospital.name || '未知医院',
        level: hospital.level,
        department: hospital.department,
        matchScore: hospital.matchScore,
        advantages: hospital.advantages || [],
        contactInfo: hospital.contactInfo || {},
        isRecommended: true
      }));
    }
    
    // 尝试从dashboardData获取医院信息
    if (aiReport.dashboardData?.hospitals) {
      return aiReport.dashboardData.hospitals;
    }
    
    // 检查recommendedHospitals独立字段
    if (aiReport.recommendedHospitals && Array.isArray(aiReport.recommendedHospitals)) {
      return aiReport.recommendedHospitals;
    }
    
    // 如果没有从AI报告中提取到数据，且启用了演示模式，则返回默认医院列表
    return useDemo ? getDefaultHospitals() : [];
  }, [aiReport, hospitalRecommendations, useDemo]);
  
  // 判断是否使用演示数据
  // const isDemoData = ...; // Attempt to remove if found
  
  if (loading) {
    return (
      <BaseCard title="医院推荐" loading={true}>
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress size={30} />
        </Box>
      </BaseCard>
    );
  }
  
  // 确定显示内容
  // 使用真实数据 或 启用了演示模式时使用演示数据
  const displayHospitals = hospitals.length > 0 ? hospitals : (useDemo ? getDefaultHospitals() : []);
  const isDefault = hospitals.length === 0;
  // const hasRealHospitals = ...; // Attempt to remove if found
  
  if (displayHospitals.length === 0) {
    return (
      <BaseCard title="医院推荐">
        <Box sx={{ textAlign: 'center', py: 3 }}>
          <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
            正在加载医院推荐信息...
          </Typography>
        </Box>
      </BaseCard>
    );
  }
  
  return (
    <BaseCard 
      title={`医院推荐${isDefault ? " (演示数据)" : ""}`}
      subTitle="本信息系智能推荐，仅供参考，谨慎核实！"
      loading={loading}
    >
      <List disablePadding>
        {displayHospitals.map((hospital, index) => {
          const isExpanded = expandedHospitals[hospital.id?.toString() || index.toString()] || false;
          
          return (
            <React.Fragment key={hospital.id || index}>
              {index > 0 && <Divider sx={{ my: 1.5 }} />}
              <ListItem 
                disableGutters 
                alignItems="flex-start"
                sx={{ px: 0, flexDirection: 'column' }}
              >
                <Box sx={{ display: 'flex', width: '100%' }}>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: 'primary.main' }}>
                      <HospitalIcon />
                    </Avatar>
                  </ListItemAvatar>
                  <Box sx={{ flex: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                      <Typography variant="subtitle2" sx={{ mr: 1, fontSize: '0.875rem' }}>
                        {hospital.name}
                      </Typography>
                      {hospital.isRecommended && (
                        <Chip 
                          label="推荐" 
                          size="small" 
                          color="primary" 
                          sx={{ height: 20, fontSize: '0.65rem' }} 
                        />
                      )}
                    </Box>
                    
                    {/* 医院等级和科室 */}
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5, flexWrap: 'wrap', gap: 1 }}>
                      {hospital.level && (
                        <Chip 
                          label={hospital.level} 
                          size="small" 
                          variant="outlined"
                          color="success"
                          sx={{ height: 20, fontSize: '0.65rem' }} 
                        />
                      )}
                      {hospital.department && (
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <MedicalIcon fontSize="small" sx={{ mr: 0.5, fontSize: '0.8rem', color: 'text.secondary' }} />
                          <Typography variant="caption" color="text.secondary">
                            {hospital.department}
                          </Typography>
                        </Box>
                      )}
                    </Box>
                    
                    {/* 匹配度 */}
                    {hospital.matchScore && (
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                        <StarIcon fontSize="small" sx={{ mr: 0.5, fontSize: '0.8rem', color: 'warning.main' }} />
                        <Typography variant="caption" sx={{ color: 'warning.main', fontWeight: 'bold' }}>
                          匹配度: {hospital.matchScore}%
                        </Typography>
                      </Box>
                    )}
                    
                    {/* 地址显示 */}
                    {hospital.address && (
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                        <LocationIcon fontSize="small" sx={{ mr: 0.5, fontSize: '0.9rem', color: 'text.secondary' }} />
                        <Typography component="span" variant="body2" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                          {hospital.address}
                          {hospital.distance && ` · ${hospital.distance}`}
                        </Typography>
                      </Box>
                    )}
                    
                    {/* 标签显示 */}
                    {hospital.tags && hospital.tags.length > 0 && (
                      <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap', mt: 0.5 }}>
                        {hospital.tags.map(tag => (
                          <Chip 
                            key={tag} 
                            label={tag} 
                            size="small" 
                            variant="outlined"
                            sx={{ height: 20, fontSize: '0.65rem' }} 
                          />
                        ))}
                      </Box>
                    )}
                    
                    {/* 详情按钮 */}
                    <Box sx={{ mt: 1 }}>
                      <Button 
                        size="small" 
                        variant="text" 
                        color="primary"
                        onClick={() => toggleHospitalExpand(hospital.id || index)}
                        sx={{ textTransform: 'none', p: 0 }}
                      >
                        {isExpanded ? '收起详情' : '查看详情'}
                      </Button>
                    </Box>
                  </Box>
                </Box>
                
                {/* 展开的详情区域 */}
                {isExpanded && (
                  <Box sx={{ pl: 7, mt: 1, width: '100%' }}>
                    {/* 优势列表 */}
                    {hospital.advantages && hospital.advantages.length > 0 && (
                      <Box sx={{ mb: 1.5 }}>
                        <Typography variant="subtitle2" sx={{ fontSize: '0.75rem', mb: 0.5 }}>
                          医院优势:
                        </Typography>
                        <List dense disablePadding>
                          {hospital.advantages.map((advantage, idx) => (
                            <ListItem key={idx} sx={{ py: 0.3, px: 0 }}>
                              <AdvantageIcon 
                                color="success" 
                                fontSize="small" 
                                sx={{ mr: 1, fontSize: '0.8rem' }} 
                              />
                              <Typography variant="caption">
                                {advantage}
                              </Typography>
                            </ListItem>
                          ))}
                        </List>
                      </Box>
                    )}
                    
                    {/* 联系信息 */}
                    {hospital.contactInfo && (
                      <Box sx={{ mb: 1 }}>
                        <Typography variant="subtitle2" sx={{ fontSize: '0.75rem', mb: 0.5 }}>
                          联系方式:
                        </Typography>
                        <List dense disablePadding>
                          {hospital.contactInfo.phone && (
                            <ListItem sx={{ py: 0.3, px: 0 }}>
                              <PhoneIcon fontSize="small" sx={{ mr: 1, fontSize: '0.8rem', color: 'text.secondary' }} />
                              <Typography variant="caption">
                                {hospital.contactInfo.phone}
                              </Typography>
                            </ListItem>
                          )}
                          {hospital.contactInfo.website && (
                            <ListItem sx={{ py: 0.3, px: 0 }}>
                              <WebsiteIcon fontSize="small" sx={{ mr: 1, fontSize: '0.8rem', color: 'text.secondary' }} />
                              <Link 
                                href={hospital.contactInfo.website.startsWith('http') ? 
                                      hospital.contactInfo.website : 
                                      `http://${hospital.contactInfo.website}`} 
                                target="_blank"
                                variant="caption"
                                sx={{ wordBreak: 'break-all' }}
                              >
                                {hospital.contactInfo.website}
                              </Link>
                            </ListItem>
                          )}
                          {hospital.contactInfo.wechatPublic && (
                            <ListItem sx={{ py: 0.3, px: 0 }}>
                              <WechatIcon fontSize="small" sx={{ mr: 1, fontSize: '0.8rem', color: 'text.secondary' }} />
                              <Typography variant="caption">
                                {hospital.contactInfo.wechatPublic}
                              </Typography>
                            </ListItem>
                          )}
                        </List>
                      </Box>
                    )}
                  </Box>
                )}
              </ListItem>
            </React.Fragment>
          );
        })}
      </List>
    </BaseCard>
  );
};

export default HospitalRecommendationCard; 