/**
 * 创建AI报告配置表
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.createTable('ai_report_configs', table => {
    table.increments('id').primary();
    table.json('user_visible_fields').notNullable().defaultTo(JSON.stringify([
      'summary', 
      'emergencyGuidance', 
      'hospitalRecommendations', 
      'lifestyleAndMentalHealth'
    ])).comment('普通用户可见的报告字段');
    table.json('service_visible_fields').notNullable().defaultTo(JSON.stringify([
      'summary', 
      'differentialDiagnosis', 
      'emergencyGuidance', 
      'hospitalRecommendations', 
      'treatmentPlan', 
      'budgetEstimation', 
      'crossRegionGuidance', 
      'lifestyleAndMentalHealth', 
      'riskWarnings'
    ])).comment('服务用户可见的报告字段');
    table.json('anonymization_rules').nullable().comment('匿名化规则配置');
    table.text('llm_prompt').nullable().comment('发送给LLM的提示模板');
    table.text('llm_response').nullable().comment('LLM响应结构模板');
    table.json('quota_config').notNullable().defaultTo(JSON.stringify({
      'PERSONAL': 3,  // 个人计划每月3次
      'FAMILY': 10,   // 家庭计划每月10次
      'PROFESSIONAL': 30  // 专业计划每月30次
    })).comment('不同套餐的AI分析次数配额');
    table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());
    table.timestamp('updated_at', { useTz: true }).defaultTo(knex.fn.now());
  });
};

/**
 * 删除AI报告配置表
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.dropTableIfExists('ai_report_configs');
}; 