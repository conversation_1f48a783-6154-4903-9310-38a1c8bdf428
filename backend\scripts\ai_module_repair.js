/**
 * AI模块修复恢复执行脚本
 * 按顺序执行所有修复步骤
 */
const { execSync } = require('child_process');
const path = require('path');

const runScript = (scriptPath) => {
  console.log(`\n===== 执行脚本：${path.basename(scriptPath)} =====`);
  try {
    execSync(`node ${scriptPath}`, { stdio: 'inherit' });
    console.log(`✅ 脚本 ${path.basename(scriptPath)} 执行成功`);
    return true;
  } catch (error) {
    console.error(`❌ 脚本 ${path.basename(scriptPath)} 执行失败`);
    return false;
  }
};

const main = async () => {
  console.log('开始AI模块修复流程...');
  console.log('当前时间:', new Date().toLocaleString());
  
  try {
    // 步骤1：验证模型定义
    if (!runScript(path.join(__dirname, 'verify_ai_models.js'))) {
      console.error('模型验证失败，修复中止');
      return;
    }
    
    // 步骤2：清理无效数据
    if (!runScript(path.join(__dirname, 'clean_invalid_reports.js'))) {
      console.warn('⚠️ 清理无效数据失败，但继续下一步');
    }
    
    // 步骤3：验证并修复报告数据
    if (!runScript(path.join(__dirname, 'validate_ai_reports.js'))) {
      console.error('报告数据验证和修复失败，修复中止');
      return;
    }
    
    // 步骤4：测试LLM连接
    if (!runScript(path.join(__dirname, 'test_llm_connection.js'))) {
      console.warn('⚠️ LLM连接测试失败，请检查API配置');
    }
    
    console.log('\n===== AI模块修复过程完成 =====');
    console.log('请检查上述执行结果，确认修复状态');
    console.log('修复完成时间:', new Date().toLocaleString());
    console.log('\n建议接下来手动测试以下功能:');
    console.log('1. 前端访问AI助手页面');
    console.log('2. 创建新AI报告');
    console.log('3. 查看现有报告详情');
    console.log('4. 下载报告PDF');
    
  } catch (error) {
    console.error('修复过程出错:', error);
  }
};

main()
  .then(() => {
    process.exit(0);
  })
  .catch(err => {
    console.error('修复主流程执行失败:', err);
    process.exit(1);
  }); 