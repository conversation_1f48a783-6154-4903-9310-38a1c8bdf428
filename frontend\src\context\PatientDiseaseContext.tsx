import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAuthStore } from '../store/authStore';
import dataCache from '../utils/dataCache';

// 上下文类型定义
interface PatientDiseaseContextType {
  selectedPatientId: string | null;
  selectedDiseaseId: string | null;
  setSelectedPatient: (id: string | null) => void;
  setSelectedDisease: (id: string | null) => void;
  clearSelection: () => void;
  forceRefresh: () => void; // 强制刷新方法
  syncState: () => void; // 强制同步状态方法
  isInitialized: boolean; // 初始化标志
}

// 创建上下文
const PatientDiseaseContext = createContext<PatientDiseaseContextType | undefined>(undefined);

// 上下文提供者属性
interface PatientDiseaseProviderProps {
  children: React.ReactNode;
}

/**
 * 患者-病理关联器上下文提供者组件
 * 用于全局管理当前选中的患者和病理
 */
export const PatientDiseaseProvider: React.FC<PatientDiseaseProviderProps> = ({ children }) => {
  const [selectedPatientId, setSelectedPatientId] = useState<string | null>(null);
  const [selectedDiseaseId, setSelectedDiseaseId] = useState<string | null>(null);
  const [initialized, setInitialized] = useState(false);
  const [refreshCounter, setRefreshCounter] = useState(0);
  const { user } = useAuthStore();

  // 清除所有选择 (移到 useEffect 之前)
  const clearSelection = useCallback(() => {
    console.log('[PatientDiseaseContext] 清除所有选择');
    setSelectedPatientId(null);
    setSelectedDiseaseId(null);
    localStorage.removeItem('selectedPatientId');
    localStorage.removeItem('selectedDiseaseId');
    dataCache.clearDiseaseRelatedCache();
    setRefreshCounter(prev => prev + 1);
    console.log('[PatientDiseaseContext] 清除所有选择完成');
  }, []); // 保持原有依赖

  useEffect(() => {
    const savedPatient = localStorage.getItem('selectedPatientId');
    localStorage.removeItem('selectedDiseaseId');
    console.log('[PatientDiseaseContext] 初始化状态，强制清除病理选择:', { savedPatient });
    if (savedPatient) {
      setSelectedPatientId(savedPatient);
      setSelectedDiseaseId(null);
    }
    setInitialized(true);
  }, []);

  useEffect(() => {
    if (!user) {
      clearSelection();
    }
  }, [user, clearSelection]);

  const syncState = useCallback(() => {
    console.log('[PatientDiseaseContext] 开始同步状态');
    const savedPatient = localStorage.getItem('selectedPatientId');
    const savedDisease = localStorage.getItem('selectedDiseaseId');
    if (savedPatient !== selectedPatientId) {
      if (savedPatient) {
        console.log(`[PatientDiseaseContext] 同步患者ID: localStorage(${savedPatient}) -> 内存状态`);
        setSelectedPatientId(savedPatient);
        localStorage.removeItem('selectedDiseaseId');
        setSelectedDiseaseId(null);
      } else if (selectedPatientId) {
        console.log(`[PatientDiseaseContext] 同步患者ID: 内存状态(${selectedPatientId}) -> localStorage`);
        localStorage.setItem('selectedPatientId', selectedPatientId);
      }
    }
    if (savedPatient === selectedPatientId && selectedPatientId) {
      if (savedDisease !== selectedDiseaseId) {
        if (savedDisease && selectedDiseaseId === null) {
          console.log(`[PatientDiseaseContext] 检测到内存中病理选择被清除，同步到localStorage`);
          localStorage.removeItem('selectedDiseaseId');
        } else if (selectedDiseaseId && (!savedDisease || savedDisease !== selectedDiseaseId)) {
          console.log(`[PatientDiseaseContext] 同步病理ID: 内存状态(${selectedDiseaseId}) -> localStorage`);
          localStorage.setItem('selectedDiseaseId', selectedDiseaseId);
        }
      }
    } else {
      if (savedDisease) {
        console.log(`[PatientDiseaseContext] 检测到无效状态：localStorage中有病理ID但患者ID不匹配，清除病理ID`);
        localStorage.removeItem('selectedDiseaseId');
        if (selectedDiseaseId) {
          setSelectedDiseaseId(null);
        }
      }
    }
  }, [selectedPatientId, selectedDiseaseId]);

  const setSelectedPatient = useCallback((id: string | null) => {
    console.log('[PatientDiseaseContext] 设置选中患者:', id, '当前选中:', selectedPatientId);
    if (id === selectedPatientId) {
      console.log('[PatientDiseaseContext] 患者ID未变化，跳过处理');
      return;
    }
    localStorage.removeItem('selectedDiseaseId');
    setSelectedDiseaseId(null);
    dataCache.clearDiseaseRelatedCache();
    if (id) {
      localStorage.setItem('selectedPatientId', id);
      setSelectedPatientId(id);
      console.log(`[PatientDiseaseContext] 已设置患者ID: ${id}, 并清除病理选择`);
    } else {
      localStorage.removeItem('selectedPatientId');
      setSelectedPatientId(null);
      console.log('[PatientDiseaseContext] 已清除患者ID和病理选择');
    }
    setRefreshCounter(prev => prev + 1);
  }, [selectedPatientId]);

  const setSelectedDisease = useCallback((id: string | null) => {
    console.log('[PatientDiseaseContext] 设置选中病理:', id, '当前选中:', selectedDiseaseId);
    if (id === selectedDiseaseId) {
      console.log('[PatientDiseaseContext] 病理ID未变化，跳过处理');
      return;
    }
    if (!selectedPatientId && id) {
      console.error('[PatientDiseaseContext] 错误：尝试在没有选中患者的情况下设置病理ID，操作被取消');
      return;
    }
    setSelectedDiseaseId(id);
    if (id) {
      localStorage.setItem('selectedDiseaseId', id);
      console.log(`[PatientDiseaseContext] 已将病理ID ${id} 保存到localStorage`);
    } else {
      localStorage.removeItem('selectedDiseaseId');
      console.log('[PatientDiseaseContext] 已从localStorage移除病理ID');
    }
    if (id) {
      dataCache.clearDiseaseRelatedCache(id);
    }
    setRefreshCounter(prev => prev + 1);
  }, [selectedPatientId, selectedDiseaseId]);

  // 强制刷新选择和缓存
  const forceRefresh = useCallback(() => {
    console.log('[PatientDiseaseContext] 强制刷新选择和缓存');
    
    // 保存当前选择
    const currentPatientId = selectedPatientId;
    const currentDiseaseId = selectedDiseaseId; // 保存当前病理ID
    
    // 先彻底清除所有选择和缓存
    localStorage.removeItem('selectedPatientId');
    localStorage.removeItem('selectedDiseaseId');
    setSelectedPatientId(null);
    setSelectedDiseaseId(null);
    dataCache.clear();
    
    // 短暂延时后重设患者ID
    setTimeout(() => {
      if (currentPatientId) {
        console.log('[PatientDiseaseContext] 重新设置患者ID:', currentPatientId);
        localStorage.setItem('selectedPatientId', currentPatientId);
        setSelectedPatientId(currentPatientId);
        
        // 如果之前有选择的病理ID，尝试恢复
        if (currentDiseaseId) {
          console.log('[PatientDiseaseContext] 尝试恢复病理ID:', currentDiseaseId);
          // 延迟稍长一点时间再设置病理ID，确保患者ID已经被正确设置
          setTimeout(() => {
            localStorage.setItem('selectedDiseaseId', currentDiseaseId);
            setSelectedDiseaseId(currentDiseaseId);
            setRefreshCounter(prev => prev + 1); // 触发最终刷新
          }, 50);
        } else {
          setRefreshCounter(prev => prev + 1); // 触发刷新
        }
      } else {
        setRefreshCounter(prev => prev + 1); // 触发刷新
      }
      
      console.log('[PatientDiseaseContext] 强制刷新完成');
    }, 100);
  }, [selectedPatientId, selectedDiseaseId]);
  
  // 监听refreshCounter变更，同步状态
  useEffect(() => {
    if (initialized && refreshCounter > 0) {
      syncState();
    }
  }, [refreshCounter, initialized, syncState]);
  
  // 上下文值
  const contextValue: PatientDiseaseContextType = {
    selectedPatientId,
    selectedDiseaseId,
    setSelectedPatient,
    setSelectedDisease,
    clearSelection,
    forceRefresh,
    syncState,
    isInitialized: initialized
  };
  
  return (
    <PatientDiseaseContext.Provider value={contextValue}>
      {children}
    </PatientDiseaseContext.Provider>
  );
};

/**
 * 使用患者-病理关联器上下文的钩子
 */
export const usePatientDiseaseContext = (): PatientDiseaseContextType => {
  const context = useContext(PatientDiseaseContext);
  if (context === undefined) {
    throw new Error('usePatientDiseaseContext必须在PatientDiseaseProvider内部使用');
  }
  return context;
}; 