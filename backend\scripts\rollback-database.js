/**
 * 数据库回滚脚本
 * 用于按正确顺序回滚所有迁移
 */
const knex = require('knex');
const knexfile = require('../knexfile');
const { Model } = require('objection');

// 获取环境配置
const env = process.env.NODE_ENV || 'development';
const config = knexfile[env];

// 创建数据库连接
const db = knex(config);

// 设置 Objection.js
Model.knex(db);

// 定义回滚顺序 - 注意这是与迁移顺序相反
const ROLLBACK_ORDER = [
  // 初始化数据
  '2025042701_init_database.js',
  
  // 患者记录删除
  '2025042706_drop_patient_records.js',
  
  // 列名规范化
  '20240720_normalize_column_names.js',
  
  // 服务相关
  '20250601003_create_service_reports.js',
  '20250601002_create_service_records.js',
  
  // 授权相关 - 确保回滚顺序正确
  '20250601004_fix_authorizations_table.js',
  '20250601001_create_user_authorizations.js',
  '20250507062409_remove_paused_state.js',
  '20250507045000_update_pending_to_paused.js',
  '20250507044637_add_paused_state_to_check.js',
  '20250507044236_update_status_check_constraint.js',
  '20250507043021_add_switch_states_to_authorizations.js',
  'create_authorizations_table.js',
  
  // 通知相关
  '20250511_create_notifications.js',
  
  // AI报告相关 - 先回滚依赖表
  '20250514141027_add_is_deleted_to_ai_reports.js',
  '20250512092101_add_created_by_updated_by_to_diseases_and_ai_reports.js',
  '20250510004_update_ai_reports.js',
  '20250510003_create_ai_report_quotas.js',
  '20250510002_create_ai_report_configs.js',
  '20250510001_create_ai_reports.js',
  
  // 管理员审计日志相关
  'create_admin_audit_logs_table.js',
  
  // 患者日志相关
  '20250501003_drop_patient_logs.js',
  '20250426161035_create_patient_logs.js',
  
  // 标签相关
  '20240601_create_tags.js',
  
  // 记录相关 - 先回滚依赖records的表
  '20250514141857_add_reference_id_to_records.js',
  '20250514140611_add_is_deleted_to_records.js',
  '20250430002_create_attachments_table.js',
  '20250430001_add_records_table.js',
  
  // 疾病相关
  '20250501002_update_diseases_table.js',
  '20250501001_create_diseases.js',
  
  // 患者相关
  '20250510004_add_height_weight_bmi_to_patients.js',
  '20250426151848_add_email_address_to_patients.js',
  '2025042707_add_patients_index.js',
  '20250426104116_add_patients_index.js',
  '20250426120714_add_is_primary_to_patients.js',
  '2025042604_create_patients.js',
  
  // 订阅相关
  '2025042603_create_subscriptions.js',
  
  // 用户和权限相关
  '20250501061759_add_max_ai_used_to_user_level_limits.js',
  'update_existing_users_created_at.js',
  '20250504041757_add_created_at_to_users.js',
  '2025042602_create_user_level_limits.js',
  '2025042601_create_users.js'
];

async function rollbackDatabase() {
  try {
    console.log('开始回滚数据库...');
    
    // 检查迁移表是否存在
    const hasMigrationTable = await db.schema.hasTable('knex_migrations');
    if (!hasMigrationTable) {
      console.log('迁移表不存在，没有需要回滚的迁移。');
      return;
    }

    // 获取已执行的迁移
    const executedMigrations = await db('knex_migrations').select('name');
    const executedNames = executedMigrations.map(m => m.name);

    // 按顺序回滚迁移
    console.log('执行数据库回滚...');
    for (const migrationName of ROLLBACK_ORDER) {
      if (executedNames.includes(migrationName)) {
        console.log(`回滚迁移: ${migrationName}`);
        try {
          await db.migrate.down({
            name: migrationName,
            directory: config.migrations.directory
          });
          console.log(`迁移 ${migrationName} 回滚成功`);
        } catch (error) {
          console.error(`迁移 ${migrationName} 回滚失败:`, error);
          console.log('尝试继续回滚其他迁移...');
          // 不抛出错误，尝试继续回滚其他迁移
        }
      } else {
        console.log(`迁移 ${migrationName} 未执行，跳过回滚`);
      }
    }
    
    console.log('数据库回滚完成！');
    
    // 检查是否还有未回滚的迁移
    const remainingMigrations = await db('knex_migrations').select('name');
    if (remainingMigrations.length > 0) {
      console.log('\n以下迁移未被回滚:');
      remainingMigrations.forEach(migration => {
        console.log(`- ${migration.name}`);
      });
    } else {
      console.log('\n所有迁移已成功回滚。');
    }

  } catch (error) {
    console.error('数据库回滚失败:', error);
    process.exit(1);
  } finally {
    // 关闭数据库连接
    await db.destroy();
  }
}

// 执行回滚
rollbackDatabase(); 