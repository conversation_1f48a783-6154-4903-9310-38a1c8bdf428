const { Model, snakeCaseMappers } = require('objection');
const { v4: uuidv4 } = require('uuid');

class ServiceReport extends Model {
  static get tableName() {
    return 'service_reports';
  }

  static get idColumn() {
    return 'id';
  }

  static get columnNameMappers() {
    return snakeCaseMappers();
  }

  $beforeInsert() {
    this.id = this.id || uuidv4();
    this.createdAt = new Date().toISOString();
    this.updatedAt = this.createdAt;
  }

  $beforeUpdate() {
    this.updatedAt = new Date().toISOString();
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['aiReportId', 'authorizationId', 'serviceUserId', 'ownerUserId'],
      properties: {
        id: { type: 'string' },
        aiReportId: { type: 'string' },
        authorizationId: { type: 'string' },
        serviceUserId: { type: 'string' },
        ownerUserId: { type: 'string' },
        pdfPath: { type: ['string', 'null'] },
        createdAt: { type: 'string' },
        updatedAt: { type: 'string' }
      }
    };
  }

  static get relationMappings() {
    const User = require('./User');
    const UserAuthorization = require('./UserAuthorization');
    
    // 修改AIReport模型的引入路径
    const { AIReport } = require('./aiReport'); // 假设 aiReport/index.js 正确导出了 AIReport
    
    return {
      // 关联AI报告
      aiReport: {
        relation: Model.BelongsToOneRelation,
        modelClass: AIReport,
        join: {
          from: 'service_reports.aiReportId',
          to: 'ai_reports.id'
        }
      },
      
      // 关联授权
      authorization: {
        relation: Model.BelongsToOneRelation,
        modelClass: UserAuthorization,
        join: {
          from: 'service_reports.authorizationId',
          to: 'user_authorizations.id'
        }
      },
      
      // 服务用户
      serviceUser: {
        relation: Model.BelongsToOneRelation,
        modelClass: User,
        join: {
          from: 'service_reports.serviceUserId',
          to: 'users.id'
        }
      },
      
      // 报告所有者
      ownerUser: {
        relation: Model.BelongsToOneRelation,
        modelClass: User,
        join: {
          from: 'service_reports.ownerUserId',
          to: 'users.id'
        }
      }
    };
  }
}

module.exports = ServiceReport; 