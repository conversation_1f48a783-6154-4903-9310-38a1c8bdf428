import React, { useState, useEffect } from 'react';
import { Box, Typography, CircularProgress } from '@mui/material';
import DiseaseProgressCard, { StageType } from './DiseaseProgressCard';
import { usePatientDiseaseContext } from '../../../context/PatientDiseaseContext';

// 疾病进度类型
interface DiseaseProgress {
  id: string;
  name: string;
  days: number;
  stages: {
    type: StageType;
    date: string;
    completed: boolean;
  }[];
  currentStage: number;
  isActive: boolean;
}

/**
 * 疾病进展卡片列表组件
 * 显示当前患者的所有疾病进展卡片
 */
const DiseaseProgressCards: React.FC = () => {
  const { selectedPatientId } = usePatientDiseaseContext();
  const [loading, setLoading] = useState(false);
  const [diseases, setDiseases] = useState<DiseaseProgress[]>([]);

  // 从后端获取疾病进展数据
  useEffect(() => {
    if (!selectedPatientId) {
      setDiseases([]);
      return;
    }

    setLoading(true);

    // 实际项目中应该调用API获取数据
    // 这里使用模拟数据进行演示
    const mockData: DiseaseProgress[] = [
      {
        id: '1',
        name: '高血压',
        days: 194,
        stages: [
          { type: '初诊', date: '2024-01-21', completed: true },
          { type: '确诊', date: '2024-01-25', completed: true },
          { type: '治疗', date: '2024-01-30', completed: true },
          { type: '随访', date: '待定', completed: false },
          { type: '预后', date: '待定', completed: false },
          { type: '封档', date: '待定', completed: false },
        ],
        currentStage: 2,
        isActive: true
      },
      {
        id: '2',
        name: '糖尿病',
        days: 78,
        stages: [
          { type: '初诊', date: '2024-01-21', completed: true },
          { type: '确诊', date: '2024-01-25', completed: true },
          { type: '治疗', date: '2024-01-30', completed: true },
          { type: '随访', date: '待定', completed: false },
          { type: '预后', date: '待定', completed: false },
          { type: '封档', date: '待定', completed: false },
        ],
        currentStage: 2,
        isActive: true
      }
    ];

    // 模拟异步加载
    setTimeout(() => {
      setDiseases(mockData);
      setLoading(false);
    }, 400);
  }, [selectedPatientId]);

  if (!selectedPatientId) {
    return null;
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress size={40} />
      </Box>
    );
  }

  if (diseases.length === 0) {
    return (
      <Box sx={{ p: 4, textAlign: 'center' }}>
        <Typography color="text.secondary">没有找到病理记录</Typography>
      </Box>
    );
  }

  return (
    <Box>
      <Typography 
        variant="h6" 
        sx={{ 
          mb: 2, 
          fontWeight: 'bold',
          fontSize: { xs: '1rem', sm: '1.1rem', md: '1.2rem' },
          color: 'primary.main'
        }}
      >
        病理进度
      </Typography>

      {diseases.map(disease => (
        <DiseaseProgressCard
          key={disease.id}
          {...disease}
        />
      ))}
    </Box>
  );
};

export default DiseaseProgressCards; 