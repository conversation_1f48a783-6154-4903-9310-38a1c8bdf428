import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Tabs, 
  Tab, 
  Divider, 
  CircularProgress,
  useTheme
} from '@mui/material';
import { 
  Event as EventIcon, 
  Medication as MedicationIcon, 
  Analytics as AnalyticsIcon,
  Science as ScienceIcon,
  HealthAndSafety as HealthIcon
} from '@mui/icons-material';
import { usePatientDiseaseContext } from '../../context/PatientDiseaseContext';

// 病理信息接口
interface DiseaseInfo {
  id: string;
  name: string;
  startDate: string;
  duration: number; // 天数
  description: string;
  medications: {
    name: string;
    dosage: string;
    frequency: string;
    notes?: string;
  }[];
  aiAnalysis: {
    causes: string[];
    lifeAdvice: string[];
    examinations: string[];
    treatments: string[];
    prognosis: {
      score: number;
      description: string;
    };
  };
}

/**
 * 病理分析组件
 * Dashboard的第二个标签页，显示当前选中病理的详细分析
 */
const DiseaseAnalysis: React.FC = () => {
  const theme = useTheme();
  const { selectedPatientId, selectedDiseaseId } = usePatientDiseaseContext();
  const [loading, setLoading] = useState(false);
  const [diseaseInfo, setDiseaseInfo] = useState<DiseaseInfo | null>(null);
  const [activeTab, setActiveTab] = useState(0);

  // 从后端获取病理详情
  useEffect(() => {
    if (!selectedPatientId || !selectedDiseaseId) {
      setDiseaseInfo(null);
      return;
    }

    setLoading(true);

    // 实际项目中应该调用API获取数据
    // 这里使用模拟数据进行演示
    const mockData: DiseaseInfo = {
      id: '1',
      name: '高血压',
      startDate: '2024-01-21',
      duration: 194,
      description: '原发性高血压(伴例)，持续性血压升高。',
      medications: [
        {
          name: '氨氯地平',
          dosage: '5mg',
          frequency: '每日1次',
          notes: '早餐后服用'
        },
        {
          name: '厄贝沙坦',
          dosage: '150mg',
          frequency: '每日1次',
          notes: '晚餐后服用'
        }
      ],
      aiAnalysis: {
        causes: [
          '遗传因素：家族史阳性',
          '生活方式：高盐饮食、缺乏运动',
          '环境因素：长期工作压力大'
        ],
        lifeAdvice: [
          '低盐饮食，每日摄入量不超过5g',
          '规律作息，保证充足睡眠',
          '适量运动，每周至少150分钟有氧运动',
          '减轻压力，学习放松技巧'
        ],
        examinations: [
          '定期血压监测，每周至少3次',
          '每3个月一次肾功能检查',
          '每年一次心电图检查',
          '每年一次眼底检查'
        ],
        treatments: [
          '药物治疗：钙通道阻滞剂和血管紧张素II受体拮抗剂联合使用',
          '生活方式干预：饮食控制、规律运动',
          '定期随访：调整用药方案'
        ],
        prognosis: {
          score: 85,
          description: '通过规范治疗和良好的自我管理，预后良好，可有效控制血压水平，降低并发症风险。'
        }
      }
    };

    // 模拟异步加载
    setTimeout(() => {
      setDiseaseInfo(mockData);
      setLoading(false);
    }, 500);
  }, [selectedPatientId, selectedDiseaseId]);

  // 处理标签切换
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // 提示选择病理
  if (!selectedPatientId || !selectedDiseaseId) {
    return (
      <Box sx={{ p: 4, textAlign: 'center' }}>
        <Typography color="text.secondary">
          请先在概览页面选择患者和病理
        </Typography>
      </Box>
    );
  }

  // 加载中
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress />
      </Box>
    );
  }

  // 数据为空
  if (!diseaseInfo) {
    return (
      <Box sx={{ p: 4, textAlign: 'center' }}>
        <Typography color="text.secondary">
          未找到病理信息
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* 病理标题和基本信息 */}
      <Paper 
        elevation={0}
        sx={{ 
          p: 3, 
          mb: 2, 
          border: '1px solid', 
          borderColor: 'divider',
          borderRadius: 2
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Typography 
            variant="h5" 
            sx={{ 
              fontWeight: 'bold',
              fontSize: { xs: '1.25rem', sm: '1.5rem' }
            }}
          >
            {diseaseInfo.name}
          </Typography>
          <Box 
            sx={{ 
              ml: 2, 
              px: 1.5, 
              py: 0.5, 
              bgcolor: 'primary.main', 
              borderRadius: 1,
              display: 'flex',
              alignItems: 'center'
            }}
          >
            <EventIcon sx={{ color: 'white', fontSize: '0.9rem', mr: 0.5 }} />
            <Typography 
              sx={{ 
                color: 'white', 
                fontSize: '0.8rem',
                fontWeight: 'bold'
              }}
            >
              {diseaseInfo.duration}天
            </Typography>
          </Box>
        </Box>

        <Typography 
          variant="body1" 
          sx={{ 
            color: 'text.secondary',
            mb: 1
          }}
        >
          开始日期: {diseaseInfo.startDate}
        </Typography>

        <Typography 
          variant="body1"
          sx={{ 
            mt: 2
          }}
        >
          {diseaseInfo.description}
        </Typography>
      </Paper>

      {/* 内容标签页 */}
      <Paper 
        elevation={0}
        sx={{ 
          border: '1px solid', 
          borderColor: 'divider',
          borderRadius: 2,
          overflow: 'hidden'
        }}
      >
        <Tabs 
          value={activeTab} 
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
          sx={{ 
            borderBottom: 1, 
            borderColor: 'divider',
            bgcolor: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)'
          }}
        >
          <Tab 
            icon={<MedicationIcon fontSize="small" />} 
            iconPosition="start"
            label="用药信息" 
          />
          <Tab 
            icon={<AnalyticsIcon fontSize="small" />} 
            iconPosition="start"
            label="成因分析" 
          />
          <Tab 
            icon={<HealthIcon fontSize="small" />} 
            iconPosition="start"
            label="生活建议" 
          />
          <Tab 
            icon={<ScienceIcon fontSize="small" />} 
            iconPosition="start"
            label="检查治疗" 
          />
        </Tabs>

        {/* 用药信息 */}
        <Box 
          role="tabpanel"
          hidden={activeTab !== 0}
          sx={{ p: 3 }}
        >
          {activeTab === 0 && (
            <>
              <Typography 
                variant="h6" 
                sx={{ mb: 2, fontWeight: 'bold' }}
              >
                当前用药方案
              </Typography>
              
              {diseaseInfo.medications.map((med, index) => (
                <Paper
                  key={index}
                  variant="outlined"
                  sx={{ 
                    p: 2,
                    mb: 2,
                    borderRadius: 1
                  }}
                >
                  <Typography 
                    variant="subtitle1" 
                    sx={{ 
                      fontWeight: 'bold',
                      color: 'primary.main',
                      display: 'flex',
                      alignItems: 'center',
                      mb: 1
                    }}
                  >
                    <MedicationIcon sx={{ mr: 1, fontSize: '1.2rem' }} />
                    {med.name}
                  </Typography>
                  
                  <Box sx={{ ml: 3 }}>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                      <strong>剂量:</strong> {med.dosage}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                      <strong>频率:</strong> {med.frequency}
                    </Typography>
                    {med.notes && (
                      <Typography variant="body2" color="text.secondary">
                        <strong>备注:</strong> {med.notes}
                      </Typography>
                    )}
                  </Box>
                </Paper>
              ))}
            </>
          )}
        </Box>

        {/* 成因分析 */}
        <Box 
          role="tabpanel"
          hidden={activeTab !== 1}
          sx={{ p: 3 }}
        >
          {activeTab === 1 && (
            <>
              <Typography 
                variant="h6" 
                sx={{ mb: 2, fontWeight: 'bold' }}
              >
                病理成因分析
              </Typography>
              
              <Box sx={{ mb: 3 }}>
                <Typography 
                  variant="subtitle1" 
                  sx={{ 
                    fontWeight: 'bold', 
                    mb: 1.5,
                    color: 'primary.main'
                  }}
                >
                  可能原因
                </Typography>
                
                {diseaseInfo.aiAnalysis.causes.map((cause, index) => (
                  <Box 
                    key={index} 
                    sx={{ 
                      display: 'flex', 
                      mb: 1,
                      alignItems: 'flex-start'
                    }}
                  >
                    <Box 
                      sx={{ 
                        width: 8, 
                        height: 8, 
                        borderRadius: '50%', 
                        bgcolor: 'primary.main',
                        mt: 1,
                        mr: 1.5
                      }} 
                    />
                    <Typography variant="body1">
                      {cause}
                    </Typography>
                  </Box>
                ))}
              </Box>
              
              <Box sx={{ mb: 3 }}>
                <Typography 
                  variant="subtitle1" 
                  sx={{ 
                    fontWeight: 'bold', 
                    mb: 1.5,
                    color: 'primary.main'
                  }}
                >
                  预后评估
                </Typography>
                
                <Box 
                  sx={{ 
                    p: 2, 
                    bgcolor: theme.palette.mode === 'dark' ? 'rgba(0,128,0,0.1)' : 'rgba(0,200,0,0.05)', 
                    borderRadius: 2,
                    border: '1px solid',
                    borderColor: theme.palette.mode === 'dark' ? 'rgba(0,128,0,0.3)' : 'rgba(0,200,0,0.2)',
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mr: 1 }}>
                      预后评分:
                    </Typography>
                    <Typography 
                      variant="subtitle1" 
                      sx={{ 
                        color: diseaseInfo.aiAnalysis.prognosis.score > 70 ? 'success.main' : 'warning.main',
                        fontWeight: 'bold'
                      }}
                    >
                      {diseaseInfo.aiAnalysis.prognosis.score}/100
                    </Typography>
                  </Box>
                  <Typography variant="body2">
                    {diseaseInfo.aiAnalysis.prognosis.description}
                  </Typography>
                </Box>
              </Box>
            </>
          )}
        </Box>

        {/* 生活建议 */}
        <Box 
          role="tabpanel"
          hidden={activeTab !== 2}
          sx={{ p: 3 }}
        >
          {activeTab === 2 && (
            <>
              <Typography 
                variant="h6" 
                sx={{ mb: 2, fontWeight: 'bold' }}
              >
                生活方式建议
              </Typography>
              
              {diseaseInfo.aiAnalysis.lifeAdvice.map((advice, index) => (
                <Box 
                  key={index} 
                  sx={{ 
                    display: 'flex', 
                    mb: 2,
                    p: 2,
                    borderRadius: 1,
                    bgcolor: theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.2)' : 'rgba(0,0,0,0.03)',
                    border: '1px solid',
                    borderColor: 'divider'
                  }}
                >
                  <Typography 
                    variant="body1"
                    sx={{
                      display: 'flex',
                      alignItems: 'flex-start'
                    }}
                  >
                    <Box 
                      sx={{ 
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        width: 24, 
                        height: 24, 
                        borderRadius: '50%', 
                        bgcolor: theme.palette.primary.main,
                        color: 'white',
                        fontWeight: 'bold',
                        fontSize: '0.8rem',
                        mr: 2,
                        flexShrink: 0,
                        mt: 0.2
                      }} 
                    >
                      {index + 1}
                    </Box>
                    {advice}
                  </Typography>
                </Box>
              ))}
            </>
          )}
        </Box>

        {/* 检查治疗 */}
        <Box 
          role="tabpanel"
          hidden={activeTab !== 3}
          sx={{ p: 3 }}
        >
          {activeTab === 3 && (
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
              <Box>
                <Typography 
                  variant="h6" 
                  sx={{ mb: 2, fontWeight: 'bold' }}
                >
                  推荐检查
                </Typography>
                
                <Box 
                  sx={{ 
                    display: 'grid',
                    gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)' },
                    gap: 2
                  }}
                >
                  {diseaseInfo.aiAnalysis.examinations.map((exam, index) => (
                    <Paper
                      key={index}
                      variant="outlined"
                      sx={{ 
                        p: 2,
                        borderRadius: 1
                      }}
                    >
                      <Typography variant="body1">
                        {exam}
                      </Typography>
                    </Paper>
                  ))}
                </Box>
              </Box>
              
              <Divider />
              
              <Box>
                <Typography 
                  variant="h6" 
                  sx={{ mb: 2, fontWeight: 'bold' }}
                >
                  治疗方法
                </Typography>
                
                {diseaseInfo.aiAnalysis.treatments.map((treatment, index) => (
                  <Box 
                    key={index} 
                    sx={{ 
                      display: 'flex', 
                      mb: 2
                    }}
                  >
                    <Box 
                      sx={{ 
                        width: 24, 
                        height: 24, 
                        borderRadius: '50%', 
                        bgcolor: 'primary.main',
                        color: 'white',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        fontWeight: 'bold',
                        mr: 2,
                        flexShrink: 0,
                        mt: 0.2
                      }} 
                    >
                      {String.fromCharCode(65 + index)}
                    </Box>
                    <Typography variant="body1">
                      {treatment}
                    </Typography>
                  </Box>
                ))}
              </Box>
            </Box>
          )}
        </Box>
      </Paper>
    </Box>
  );
};

export default DiseaseAnalysis; 