import React from 'react';
import { Box, Typography, Paper } from '@mui/material';
import { usePatientDiseaseContext } from '../../context/PatientDiseaseContext';
import DiseaseSelector from './DiseaseSelector';
import DiseaseRecordsContainer from './DiseaseRecordsContainer';

/**
 * Dashboard概览页面组件
 * 显示病理选择器和记录列表
 */
const DashboardOverview: React.FC = () => {
  const { selectedPatientId, selectedDiseaseId } = usePatientDiseaseContext();

  // 为调试目的记录当前选择的患者和病理
  console.log('DashboardOverview渲染 - 当前Context状态:', {
    selectedPatientId,
    selectedDiseaseId
  });

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      {/* 病理选择器组件 */}
      <Paper
        elevation={0}
        sx={{
          p: { xs: 2, sm: 3 },
          borderRadius: 2,
          border: '1px solid',
          borderColor: 'divider'
        }}
      >
        <Typography
          variant="h6"
          sx={{
            fontSize: { xs: '1rem', sm: '1.1rem', md: '1.2rem' },
            fontWeight: 600,
            mb: 2,
            color: 'primary.main'
          }}
        >
          病理进度
        </Typography>
        
        {selectedPatientId ? (
          <DiseaseSelector />
        ) : (
          <Box sx={{ 
            display: 'flex', 
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: 100,
            p: 2
          }}>
            <Typography color="text.secondary" variant="body1" align="center">
              请从上方选择一个患者
            </Typography>
          </Box>
        )}
      </Paper>
      
      {/* 调试信息 */}
      {process.env.NODE_ENV === 'development' && (
        <Box sx={{ 
          p: 2, 
          bgcolor: '#f5f5f5', 
          borderRadius: 1, 
          fontSize: '0.8rem',
          color: '#666'
        }}>
          <Typography variant="caption">
            调试信息: 患者ID: {selectedPatientId || '未选择'}, 
            病理ID: {selectedDiseaseId || '未选择'}
          </Typography>
        </Box>
      )}
      
      {/* 病理记录列表组件 - 会根据Context状态自动显示或隐藏 */}
      <DiseaseRecordsContainer />
    </Box>
  );
};

export default DashboardOverview; 