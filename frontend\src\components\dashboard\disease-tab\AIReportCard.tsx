import React, { useState, useEffect, useCallback } from 'react';
import { 
  <PERSON>, 
  Typography, 
  Button, 
  Chip, 
  CircularProgress,
} from '@mui/material';
import {
  Add as AddIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import BaseCard from './BaseCard';

interface AIReportCardProps {
  aiReport: any;
  allReports: any[];
  diseaseId: string;
  loading?: boolean;
  patient?: any;
  extractedSummary?: string;
}

/**
 * 病理概要卡片组件
 * 显示基于AI报告的病理概述内容
 */
const AIReportCard: React.FC<AIReportCardProps> = ({ 
  aiReport, 
  allReports = [],
  diseaseId,
  loading = false,
  patient,
  extractedSummary
}) => {
  const navigate = useNavigate();
  const [summaryContent, setSummaryContent] = useState<string>('');
  const [reportData, setReportData] = useState<{
    summary: string;
    recommendations: string[];
    hospitals: any[];
    emergencyInfo: any;
    riskLevel: string;
  } | null>(null);
  
  // 验证aiReport是否有效
  const isValidReport = aiReport && 
    (aiReport.id || (aiReport.recordType === 'AI_ANALYSIS' || 
    (aiReport.title && aiReport.title.includes('辅医智能分析报告'))));
  
  // 处理创建新报告
  const handleCreateReport = () => {
    navigate(`/ai-assistant?diseaseId=${diseaseId}`);
  };
  
  // 提取完整的摘要内容 - 不再直接设置state
  const extractFullSummary = useCallback(() => {
    let content = '暂无内容';
    let contentSource = '无来源';
    
    // 优先使用提取的结构化数据中的摘要
    if (extractedSummary) {
      content = extractedSummary;
      contentSource = 'extractedSummary';
    } else if (!aiReport) {
      content = '暂无内容';
      contentSource = '无aiReport';
    } else if (aiReport.summary) {
      content = aiReport.summary;
      contentSource = 'aiReport.summary';
    }
    // 没有单独的摘要字段，尝试从内容中提取
    else if (aiReport.content) {
      // 首先尝试解析JSON字符串格式的内容
      if (typeof aiReport.content === 'string') {
        try {
          // 尝试解析为JSON
          const jsonContent = JSON.parse(aiReport.content);
          if (jsonContent && jsonContent.summary) {
            content = jsonContent.summary;
            contentSource = 'aiReport.content (JSON解析).summary';
            console.log('成功从JSON格式的content中提取摘要，长度:', content.length);
          } else {
            // JSON中没有summary字段，使用整个字符串
            content = aiReport.content;
            contentSource = 'aiReport.content (字符串)';
            console.log('从aiReport.content字符串中提取内容，长度:', content.length);
          }
        } catch (e) {
          // 解析失败，说明不是JSON格式，直接使用字符串
          content = aiReport.content;
          contentSource = 'aiReport.content (非JSON字符串)';
          console.log('content不是JSON格式，直接使用字符串，长度:', content.length);
        }
      }
      // 如果content是对象，尝试提取摘要部分
      else if (typeof aiReport.content === 'object') {
        console.log('aiReport.content是对象，包含字段:', Object.keys(aiReport.content));
        const { summary, analysisResult, conclusion } = aiReport.content;
        if (summary) {
          content = summary;
          contentSource = 'aiReport.content.summary';
        } else if (conclusion) {
          content = conclusion;
          contentSource = 'aiReport.content.conclusion';
        } else if (analysisResult) {
          content = analysisResult;
          contentSource = 'aiReport.content.analysisResult';
        } else {
          // 如果都没有找到有效字段，尝试转换整个对象为字符串
          try {
            content = JSON.stringify(aiReport.content, null, 2);
            contentSource = 'aiReport.content (JSON字符串化)';
          } catch(e) {
            console.error('转换content对象为字符串失败:', e);
          }
        }
      }
    }
    // 最后尝试从report字段获取
    else if (aiReport.report && typeof aiReport.report === 'string') {
      content = aiReport.report;
      contentSource = 'aiReport.report';
    } else {
      content = '暂无摘要内容';
      contentSource = '未找到内容来源';
      
      // 尝试分析aiReport的所有字段
      if (aiReport) {
        console.log('aiReport包含的所有字段:', Object.keys(aiReport));
        // 查找可能包含文本内容的字段
        for (const key of Object.keys(aiReport)) {
          if (typeof aiReport[key] === 'string' && aiReport[key].length > 100) {
            console.log(`发现可能的内容字段 ${key}, 长度:`, aiReport[key].length);
            if (content === '暂无摘要内容') {
              content = aiReport[key];
              contentSource = `aiReport.${key} (backup)`;
            }
          }
        }
      }
    }
    
    // 确保内容不被截断，移除可能导致截断的省略号
    if (content) {
      content = content.replace(/\.{3,}$/, '');
    }
    
    console.log('摘要内容来源:', contentSource);
    console.log('摘要内容长度:', content.length);
    if (content.length > 0) {
      console.log('摘要内容前100字符:', content.substring(0, 100));
      if (content.length > 100) {
        console.log('摘要内容后100字符:', content.substring(content.length - 100));
      }
    }
    
    // 直接设置内容，不再设置调试信息
    setSummaryContent(content);
  }, [aiReport, extractedSummary, setSummaryContent]);
  
  // 在组件挂载或aiReport/extractedSummary变化时提取内容
  useEffect(() => {
    extractFullSummary();
  }, [extractFullSummary]);
  
  // 在组件挂载或aiReport变化时解析数据
  useEffect(() => {
    if (aiReport && aiReport.content) {
      try {
        if (typeof aiReport.content === 'string') {
          try {
            const parsed = JSON.parse(aiReport.content);
            if (parsed && parsed.summary) {
              setReportData(parsed);
            }
          } catch (e) {
            // 不是JSON格式，保持reportData为null
            setReportData(null);
          }
        }
      } catch (e) {
        console.error('解析报告数据失败:', e);
        setReportData(null);
      }
    }
  }, [aiReport]);
  
  // 渲染内容部分
  const renderContent = () => {
    if (!aiReport) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress size={30} />
        </Box>
      );
    }
    
    // 添加调试信息输出
    console.log('AIReportCard - 内容长度:', summaryContent.length);
    console.log('AIReportCard - 内容前200字符:', summaryContent.substring(0, 200));
    console.log('AIReportCard - 内容最后100字符:', summaryContent.substring(summaryContent.length - 100));
    
    // 解决内容可能不完整的问题
    let displayContent = summaryContent;
    
    // 如果原始内容非常长，确保它显示完整
    if (displayContent.length > 1000) {
      console.log('AIReportCard - 注意: 内容长度超过1000字符，确保完整显示');
    }
    
    // 如果有解析后的结构化数据，使用结构化显示
    if (reportData) {
      return (
        <Box sx={{ width: '100%' }}>
          {/* 摘要部分 */}
          <Typography 
            variant="body2" 
            component="div" 
            sx={{ 
              lineHeight: 1.8, 
              fontSize: '0.85rem',
              whiteSpace: 'pre-wrap', 
              wordBreak: 'break-word',
              mb: 3
            }}
            dangerouslySetInnerHTML={{ __html: reportData.summary.replace(/\n/g, '<br />') }}
          />
          
          {/* 风险等级指示 */}
          {reportData.riskLevel && (
            <Box sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
              <Typography variant="subtitle2" sx={{ mr: 1 }}>风险等级:</Typography>
              <Chip 
                label={reportData.riskLevel.toUpperCase()} 
                size="small"
                color={
                  reportData.riskLevel === 'high' ? 'error' : 
                  reportData.riskLevel === 'medium' ? 'warning' : 'success'
                }
              />
            </Box>
          )}
          
          {/* 紧急指导信息 */}
          {reportData.emergencyInfo && reportData.emergencyInfo.isEmergency && (
            <Box 
              sx={{ 
                mb: 2, 
                p: 2, 
                bgcolor: 'error.light', 
                borderRadius: 1,
                color: 'error.contrastText'
              }}
            >
              <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                紧急提示
              </Typography>
              {reportData.emergencyInfo.immediateActions && reportData.emergencyInfo.immediateActions.length > 0 && (
                <Box component="ul" sx={{ mt: 1, pl: 2 }}>
                  {reportData.emergencyInfo.immediateActions.map((action: string, index: number) => (
                    <Typography component="li" key={index} variant="body2">
                      {action}
                    </Typography>
                  ))}
                </Box>
              )}
            </Box>
          )}
          
          {/* 医疗建议 */}
          {reportData.recommendations && reportData.recommendations.length > 0 && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold' }}>
                医疗建议
              </Typography>
              <Box component="ul" sx={{ pl: 2 }}>
                {reportData.recommendations.slice(0, 3).map((rec: string, index: number) => (
                  <Typography component="li" key={index} variant="body2" sx={{ mb: 0.5 }}>
                    {rec}
                  </Typography>
                ))}
              </Box>
            </Box>
          )}
          
          {/* 医院推荐 */}
          {reportData.hospitals && reportData.hospitals.length > 0 && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold' }}>
                医院推荐
              </Typography>
              <Box component="ul" sx={{ pl: 2 }}>
                {reportData.hospitals.slice(0, 2).map((hospital: any, index: number) => (
                  <Typography component="li" key={index} variant="body2" sx={{ mb: 0.5 }}>
                    {hospital.name}
                    {hospital.department && ` - ${hospital.department}`}
                  </Typography>
                ))}
              </Box>
            </Box>
          )}
        </Box>
      );
    }
    
    // 如果没有结构化数据，使用原来的纯文本显示方式
    return (
      <Box sx={{ width: '100%' }}>
        <Typography 
          variant="body2" 
          component="div" 
          sx={{ 
            lineHeight: 1.8, 
            fontSize: '0.85rem',
            whiteSpace: 'pre-wrap', 
            wordBreak: 'break-word', 
            '& p': { 
              marginBottom: '12px' 
            },
            maxHeight: 'none', // 确保没有高度限制
            overflow: 'visible', // 确保内容不会被截断
            padding: '0 0 24px 0' // 增加底部内边距确保文本完整显示
          }}
          dangerouslySetInnerHTML={{ __html: displayContent.replace(/\n/g, '<br />') }}
        />
      </Box>
    );
  };
  
  // 无报告时的内容
  if (!isValidReport && !loading) {
    return (
      <BaseCard 
        title="病理概要" 
        loading={loading}
        headerAction={
          <Button
            variant="outlined"
            size="small"
            startIcon={<AddIcon />}
            onClick={handleCreateReport}
            sx={{ textTransform: 'none' }}
          >
            生成报告
          </Button>
        }
      >
        <Box sx={{ 
          display: 'flex', 
          flexDirection: 'column', 
          alignItems: 'center', 
          justifyContent: 'center',
          p: 3
        }}>
          <Typography variant="body1" color="text.secondary" align="center" sx={{ mb: 1 }}>
            暂无病理概要
          </Typography>
          <Typography variant="body2" color="text.secondary" align="center" sx={{ mb: 2 }}>
            请先添加足够的医疗记录，然后点击"生成报告"按钮创建病理概要
          </Typography>
        </Box>
      </BaseCard>
    );
  }
  
  return (
    <BaseCard 
      title="病理概要" 
      loading={loading}
      allowFullscreen={false} // 禁用全屏模式
      sx={{ overflow: 'visible', maxHeight: 'none' }}
    >
      {renderContent()}
      <Typography 
        variant="caption" 
        color="text.secondary" 
        align="center" 
        sx={{ 
          display: 'block', 
          mt: 2, 
          mx: 'auto', 
          fontSize: '0.6rem'
        }}
      >
        本内容属智能分析，仅供参考，请以医疗机构信息为准。
      </Typography>
    </BaseCard>
  );
};

export default AIReportCard; 