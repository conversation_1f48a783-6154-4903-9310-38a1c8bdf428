import React, { useState, useEffect } from 'react';
import { Box, Typography, LinearProgress, useTheme, Tooltip } from '@mui/material';
import { getUserStatistics, UserStatistics } from '../../services/statisticsService';
import { useAuthStore } from '../../store/authStore';
import { useAiUsage } from '../../context/AiUsageContext';

/**
 * 统计数据面板组件
 * 展示病历数、病理数和记录数三项统计数据及AI分析进度
 * 布局方式：上方三个统计卡片始终一排，下方一个AI分析进度条
 */
const StatisticsPanel: React.FC = () => {
  const theme = useTheme();
  const [stats, setStats] = useState<UserStatistics | null>(null);
  const userLevel = useAuthStore((state) => state.user?.level);
  const { aiUsageCount, maxAiUsage, getUsagePercentage, refreshAiUsage } = useAiUsage();
  
  // 加载用户统计数据
  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('开始获取统计数据...');
        
        // 获取统计数据
        let data = await getUserStatistics();
        console.log('获取到的统计数据:', data);
        
        // 检查用户限制数据
        if (data.userLimits) {
          console.log('用户限制数据:', data.userLimits);
        } else {
          console.warn('未找到用户限制数据');
        }
        
        setStats(data);
        
        // 刷新AI使用数据以保持两者的同步
        refreshAiUsage();
      } catch (error) {
        console.error('加载统计数据失败:', error);
        // 出错时返回零值，不再使用硬编码值
        setStats({
          patientHistory: { count: 0, total: 0 },
          diseaseCount: { count: 0 },
          recordCount: { count: 0 },
          aiAnalysis: { current: 0, total: 30 }
        });
      }
    };
    
    fetchData();
  }, [userLevel, refreshAiUsage]); // 添加refreshAiUsage作为依赖
  
  // 使用上下文提供的AI使用百分比
  const aiProgress = getUsagePercentage();
    
  let aiProgressColor: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success' = 'primary';
  if (aiProgress > 80) {
    aiProgressColor = 'warning';
  }
  if (aiProgress >= 100) {
    aiProgressColor = 'error';
  }
  
  // 用户限制信息
  const limits = stats?.userLimits;

  return (
    <Box
      sx={{
        width: 'calc(100% + 20px)',
        mb: 0,
        mt: -2.5,
        ml: '-10px', // 向左偏移10px，保持居中
        borderRadius: 2,
        overflow: 'hidden',
        p: 1.5
      }}
    >
      {/* 上方：三个统计卡片并排 */}
      <Box 
        sx={{ 
          display: 'flex',
          flexDirection: 'row',
          gap: 1.5,
          mb: 1.5,
          flexWrap: { xs: 'nowrap', sm: 'nowrap' }
        }}
      >
        {/* 患者数卡片 */}
        <Tooltip title="当前登记的患者数 / 允许登记的最大患者数量" arrow>
          <Box 
            sx={{ 
              flex: 1,
              p: { xs: 1, sm: 1.2 },
              textAlign: 'center',
              bgcolor: 'rgba(232, 245, 253, 0.6)',
              borderRadius: 1,
              cursor: 'help'
            }}
          >
            <Typography
              variant="body2"
              sx={{
                color: 'text.secondary',
                mb: 0.2,
                fontSize: '0.7rem'
              }}
            >
              患者数
            </Typography>
            <Typography
              variant="h5"
              sx={{
                color: theme.palette.info.main,
                fontWeight: 'bold',
                fontSize: { xs: '1.1rem', sm: '1.4rem' },
                lineHeight: 1.2
              }}
            >
              {/* 优先使用userLimits数据，如果不存在则使用传统字段 */}
              {stats?.patientHistory.count}/
              {limits?.maxPatients !== undefined ? 
                limits.maxPatients : 
                stats?.patientHistory.total}
            </Typography>
          </Box>
        </Tooltip>

        {/* 病理数卡片 */}
        <Tooltip title="已登记的病理数量 / 每位患者最大病理数量" arrow>
          <Box 
            sx={{ 
              flex: 1,
              p: { xs: 1, sm: 1.2 },
              textAlign: 'center',
              bgcolor: 'rgba(232, 245, 253, 0.6)',
              borderRadius: 1,
              cursor: 'help'
            }}
          >
            <Typography
              variant="body2"
              sx={{
                color: 'text.secondary',
                mb: 0.2,
                fontSize: '0.7rem'
              }}
            >
              病理数
            </Typography>
            <Typography
              variant="h5"
              sx={{
                color: theme.palette.primary.main,
                fontWeight: 'bold',
                fontSize: { xs: '1.1rem', sm: '1.4rem' },
                lineHeight: 1.2
              }}
            >
              {/* 显示病理数和每患者最大病理数限制 */}
              {stats?.diseaseCount.count}
            </Typography>
          </Box>
        </Tooltip>

        {/* 记录数卡片 */}
        <Tooltip title="用户所有患者的所有病理下的记录数量总和" arrow>
          <Box 
            sx={{ 
              flex: 1,
              p: { xs: 1, sm: 1.2 },
              textAlign: 'center',
              bgcolor: 'rgba(232, 245, 253, 0.6)',
              borderRadius: 1,
              cursor: 'help'
            }}
          >
            <Typography
              variant="body2"
              sx={{
                color: 'text.secondary',
                mb: 0.2,
                fontSize: '0.7rem'
              }}
            >
              记录数
            </Typography>
            <Typography
              variant="h5"
              sx={{
                color: theme.palette.secondary.main,
                fontWeight: 'bold',
                fontSize: { xs: '1.1rem', sm: '1.4rem' },
                lineHeight: 1.2
              }}
            >
              {/* 显示记录总数 */}
              {stats?.recordCount.count}
            </Typography>
          </Box>
        </Tooltip>
      </Box>

      {/* 下方：AI分析进度条 */}
      <Box sx={{ px: 0.5 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.3 }}>
          <Typography variant="caption" color="text.secondary" fontSize="0.65rem">
            AI分析次数
          </Typography>
          <Typography variant="caption" color="text.secondary" fontSize="0.65rem">
            {aiUsageCount}/{maxAiUsage}
          </Typography>
        </Box>
        <LinearProgress 
          variant="determinate" 
          value={aiProgress}
          sx={{ 
            height: 5, 
            borderRadius: 2.5,
            backgroundColor: 'rgba(243, 229, 229, 0.6)',
            '& .MuiLinearProgress-bar': {
              borderRadius: 2.5,
              bgcolor: aiProgressColor,
            }
          }}
        />
        
        {/* 添加提示文字 */}
        <Typography 
          variant="caption" 
          color="text.secondary" 
          sx={{ 
            display: 'block', 
            textAlign: 'center', 
            mt: 0.5, 
            fontSize: '0.55rem',
            fontStyle: 'italic'
          }}
        >
          本月剩余 {Math.max(0, maxAiUsage - aiUsageCount)} 次AI分析额度
        </Typography>
      </Box>
    </Box>
  );
};

export default StatisticsPanel; 