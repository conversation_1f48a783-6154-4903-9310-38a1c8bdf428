import React, { useState } from 'react';
import { 
  Box, TextField, Button, Select, MenuItem, FormControl, 
  InputLabel, Typography, Table, TableBody, TableCell, 
  TableContainer, TableHead, TableRow, IconButton, Snackbar, Alert,
  Dialog, DialogTitle, DialogContent, DialogActions,
  FormHelperText, CircularProgress, Divider, FormControlLabel, Checkbox,
  Tooltip, Card, CardContent, useMediaQuery, useTheme
} from '@mui/material';
// 图标组件
import PersonAddOutlined from '@mui/icons-material/PersonAddOutlined';
import DeleteOutlined from '@mui/icons-material/DeleteOutlined';
import InfoOutlined from '@mui/icons-material/InfoOutlined';
import EditOutlined from '@mui/icons-material/EditOutlined';
import SearchOutlined from '@mui/icons-material/SearchOutlined';
import SortIcon from '@mui/icons-material/Sort';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getPatients, createPatient, deletePatient, updatePatient } from '../services/patientService';
import { Link } from 'react-router-dom';
import { Patient } from '../types/patient';
import { useAuthStore } from '../store/authStore';
import { UserProfile } from '../types/user';
import { useSnackbar } from 'notistack';

// 性别枚举显示转换函数
const getGenderDisplay = (genderValue: string): string => {
  switch (genderValue) {
    case 'MALE': return '男';
    case 'FEMALE': return '女';
    default: return genderValue;
  }
};

// 关系枚举显示转换函数
const getRelationshipDisplay = (relationshipValue: string): string => {
  switch (relationshipValue) {
    case 'SELF': return '本人';
    case 'FAMILY': return '家属';
    case 'RELATIVE': return '亲戚';
    case 'FRIEND': return '朋友';
    default: return '';
  }
};

// 关系枚举颜色转换函数
const getRelationshipColor = (relationshipValue: string): string => {
  switch (relationshipValue) {
    case 'SELF': return '#2196f3'; // 蓝色
    case 'FAMILY': return '#4caf50'; // 绿色
    case 'RELATIVE': return '#ff9800'; // 橙色
    case 'FRIEND': return '#9c27b0'; // 紫色
    default: return '#e0e0e0'; // 默认灰色
  }
};

// 计算BMI函数
const calculateBMI = (heightCm: number, weightKg: number): number => {
  if (!heightCm || !weightKg || heightCm <= 0) return 0;
  // BMI = 体重(kg) / (身高(m) * 身高(m))
  const heightM = heightCm / 100;
  const bmi = weightKg / (heightM * heightM);
  // 四舍五入到小数点后一位
  return Math.round(bmi * 10) / 10;
};

/*
// 获取BMI状态描述 (未使用)
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const getBMIStatus = (bmi: number): string => {
  if (bmi <= 0) return '';
  if (bmi < 18.5) return '偏瘦';
  if (bmi < 24) return '正常';
  if (bmi < 28) return '超重';
  if (bmi < 30) return '轻度肥胖';
  if (bmi < 40) return '中度肥胖';
  return '重度肥胖';
};
*/

const PatientsPage: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const queryClient = useQueryClient();
  const { enqueueSnackbar } = useSnackbar();
  const [name, setName] = useState('');
  const [gender, setGender] = useState('MALE');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [email, setEmail] = useState('');
  const [address, setAddress] = useState('');
  const [birthDate, setBirthDate] = useState(new Date().toISOString().split('T')[0]);
  const [bloodType, setBloodType] = useState('A');
  const [height, setHeight] = useState<string>('');
  const [weight, setWeight] = useState<string>('');
  const [bmi, setBmi] = useState<number>(0);
  const [emergencyContactName, setEmergencyContactName] = useState('');
  const [emergencyContactPhone, setEmergencyContactPhone] = useState('');
  const [isPrimary, setIsPrimary] = useState<number | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [formErrors, setFormErrors] = useState({ 
    name: '', 
    birthDate: '',
    phoneNumber: '',
    emergencyContactPhone: '',
    height: '',
    weight: ''
  });
  const [notification, setNotification] = useState({ open: false, message: '', type: 'success' as 'success' | 'error' });
  const [editingPatient, setEditingPatient] = useState<Patient | null>(null);
  const [filter, setFilter] = useState({ gender: '', bloodType: '' });
  const [error, setError] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [patientToDelete, setPatientToDelete] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'all' | 'personal' | 'service'>('all');

  const userProfile = useAuthStore((state) => state.user) as UserProfile | null;

  React.useEffect(() => {
    const heightValue = parseFloat(height);
    const weightValue = parseFloat(weight);
    if (heightValue && weightValue && heightValue > 0) {
      setBmi(calculateBMI(heightValue, weightValue));
    } else {
      setBmi(0);
    }
  }, [height, weight]);

  const { 
    data: patients, 
    isLoading, 
    error: queryError 
  } = useQuery({
    queryKey: ['patients'],
    queryFn: getPatients
  });

  const filteredPatients = React.useMemo(() => {
    if (!patients) return [];
    if (!searchTerm.trim() && !filter.gender && !filter.bloodType) return patients;
    
    const searchTermLower = searchTerm.toLowerCase();
    
    return patients.filter((patient: Patient) => {
      const basicMatch = !searchTerm.trim() || 
        patient.name?.toLowerCase().includes(searchTermLower) || 
        patient.idCard?.toLowerCase().includes(searchTermLower) ||
        patient.phoneNumber?.toLowerCase().includes(searchTermLower) ||
        patient.email?.toLowerCase().includes(searchTermLower);
      
      const dateMatch = !searchTerm.trim() || (patient.birthDate && (
        patient.birthDate.includes(searchTerm) || 
        new Date(patient.birthDate).toLocaleDateString().includes(searchTerm)
      ));
      
      const genderMatch = !filter.gender || patient.gender === filter.gender;
      const bloodTypeMatch = !filter.bloodType || patient.bloodType === filter.bloodType;
      
      return (basicMatch || dateMatch) && genderMatch && bloodTypeMatch;
    });
  }, [patients, searchTerm, filter.gender, filter.bloodType]);

  const displayPatients = React.useMemo(() => {
    if (viewMode === 'all') return filteredPatients;
    
    return filteredPatients.filter((patient: Patient) => {
      if (viewMode === 'personal') {
        return !patient.isAuthorized;
      } else if (viewMode === 'service') {
        return patient.isAuthorized;
      }
      return true;
    });
  }, [filteredPatients, viewMode]);

  const sortedPatients = React.useMemo(() => {
    if (!displayPatients) return [];
    
    return [...displayPatients].sort((a, b) => {
      let compareResult = 0;
      
      switch(sortBy) {
        case 'name':
          compareResult = (a.name || '').localeCompare(b.name || '');
          break;
        case 'createdAt':
          compareResult = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
          break;
        case 'birthDate':
          compareResult = new Date(a.birthDate || '').getTime() - new Date(b.birthDate || '').getTime();
          break;
        default:
          compareResult = 0;
      }
      
      return sortDirection === 'asc' ? compareResult : -compareResult;
    });
  }, [displayPatients, sortBy, sortDirection]);

  const isFirstPatient = !patients || patients.length === 0;

  React.useEffect(() => {
    if (openDialog && !editingPatient) {
      setIsPrimary(isFirstPatient ? 1 : null);
    }
  }, [openDialog, isFirstPatient, editingPatient]);

  const validateForm = () => {
    const errors = { 
      name: '', 
      birthDate: '',
      phoneNumber: '',
      emergencyContactPhone: '',
      height: '',
      weight: ''
    };
    let isValid = true;

    if (!name.trim()) {
      errors.name = '姓名不能为空';
      isValid = false;
    }

    if (!birthDate) {
      errors.birthDate = '出生日期不能为空';
      isValid = false;
    }

    if (phoneNumber && !/^1\d{10}$/.test(phoneNumber)) {
      errors.phoneNumber = '请输入有效的11位手机号码';
      isValid = false;
    }

    if (emergencyContactPhone && !/^1\d{10}$/.test(emergencyContactPhone)) {
      errors.emergencyContactPhone = '请输入有效的11位手机号码';
      isValid = false;
    }

    if (height && (isNaN(parseFloat(height)) || parseFloat(height) <= 0 || parseFloat(height) > 300)) {
      errors.height = '请输入有效的身高（0-300厘米）';
      isValid = false;
    }

    if (weight && (isNaN(parseFloat(weight)) || parseFloat(weight) <= 0 || parseFloat(weight) > 500)) {
      errors.weight = '请输入有效的体重（0-500千克）';
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  const createMutation = useMutation({
    mutationFn: (newPatient: Omit<Patient, "id" | "createdAt" | "updatedAt">) => createPatient(newPatient),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['patients'] });
      handleCloseDialog();
      
      const successMessage = data.message 
        ? `患者添加成功。${data.message}` 
        : '患者添加成功';
        
      setNotification({ open: true, message: successMessage, type: 'success' });
      setError('');
    },
    onError: (error: any) => {
      const backendErrorMessage = error.response?.data?.error;
      if (error.response?.status === 403 && backendErrorMessage && backendErrorMessage.includes('已达到最大患者数量限制')) {
        enqueueSnackbar(backendErrorMessage, { variant: 'info' });
      } else {
        const genericErrorMessage = backendErrorMessage || '添加患者失败，请检查输入或稍后重试';
        setNotification({ open: true, message: genericErrorMessage, type: 'error' });
        setError(genericErrorMessage);
      }
    }
  });

  const updateMutation = useMutation({
    mutationFn: ({id, data}: {id: string, data: Partial<Patient>}) => updatePatient(id, data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['patients'] });
      handleCloseDialog();
      setNotification({ 
        open: true, 
        message: `患者更新成功${data.isPrimary ? '，已设置为本人档案' : ''}`, 
        type: 'success' 
      });
      setError('');
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.error || '更新患者失败，请检查输入或稍后重试';
      setNotification({ open: true, message: errorMessage, type: 'error' });
      setError(errorMessage);
    }
  });

  const deleteMutation = useMutation({
    mutationFn: (id: string) => deletePatient(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['patients'] });
      setNotification({ open: true, message: '患者删除成功', type: 'success' });
      setError('');
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.error || '删除患者失败，请稍后重试';
      setNotification({ open: true, message: errorMessage, type: 'error' });
      setError(errorMessage);
    }
  });

  const handleCreatePatient = () => {
    if (!validateForm()) return;
    
    const patientData: Partial<Patient> = { 
      name, 
      gender, 
      phoneNumber, 
      email,
      address,
      birthDate, 
      bloodType,
      height: height ? parseFloat(height) : undefined,
      weight: weight ? parseFloat(weight) : undefined,
      bmi,
      emergencyContactName,
      emergencyContactPhone,
      isPrimary: isFirstPatient || isPrimary === 1 ? 1 : 0
    };
    
    createMutation.mutate(patientData as Omit<Patient, "id" | "createdAt" | "updatedAt">);
  };

  /*
  // 编辑患者（暂未使用）
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleEditPatient = (patient: Patient) => {
    setEditingPatient(patient);
    setName(patient.name);
    setGender(patient.gender);
    setPhoneNumber(patient.phoneNumber || '');
    setEmail(patient.email || '');
    setAddress(patient.address || '');
    setBirthDate(patient.birthDate ? patient.birthDate.split('T')[0] : new Date().toISOString().split('T')[0]);
    setBloodType(patient.bloodType || '');
    setHeight(patient.height ? patient.height.toString() : '');
    setWeight(patient.weight ? patient.weight.toString() : '');
    setEmergencyContactName(patient.emergencyContactName || '');
    setEmergencyContactPhone(patient.emergencyContactPhone || '');
    setIsPrimary(patient.isPrimary ? 1 : 0);
    setOpenDialog(true);
  };
  */

  const handleUpdatePatient = () => {
    if (!validateForm() || !editingPatient) return;
    
    console.log('更新前状态:', {
      isPrimary,
      editingPatient,
      checked: isPrimary === 1
    });

    const patientData: Partial<Patient> = { 
      name, 
      gender, 
      phoneNumber,
      email,
      address,
      birthDate, 
      bloodType,
      height: height ? parseFloat(height) : undefined,
      weight: weight ? parseFloat(weight) : undefined,
      bmi,
      emergencyContactName,
      emergencyContactPhone,
      isPrimary: isPrimary === 1 ? 1 : 0
    };
    
    console.log('提交的患者数据:', patientData);
    
    updateMutation.mutate({
      id: editingPatient.id,
      data: patientData
    });
  };

  const handleDeletePatient = (id: string) => {
    setPatientToDelete(id);
    setDeleteDialogOpen(true);
  };
  
  const confirmDelete = () => {
    if (patientToDelete) {
      deleteMutation.mutate(patientToDelete);
      setDeleteDialogOpen(false);
      setPatientToDelete(null);
    }
  };
  
  const cancelDelete = () => {
    setDeleteDialogOpen(false);
    setPatientToDelete(null);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingPatient(null);
    setName('');
    setGender('MALE');
    setPhoneNumber('');
    setEmail('');
    setAddress('');
    setBirthDate(new Date().toISOString().split('T')[0]);
    setBloodType('A');
    setHeight('');
    setWeight('');
    setBmi(0);
    setEmergencyContactName('');
    setEmergencyContactPhone('');
    setIsPrimary(null);
    setFormErrors({ name: '', birthDate: '', phoneNumber: '', emergencyContactPhone: '', height: '', weight: '' });
  };

  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };

  const handleSortChange = (field: string) => {
    if (sortBy === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortDirection('asc');
    }
  };

  /*
  // patientDiseaseQueries 和 isDiseaseLimitReached 相关逻辑已移除或注释，因为它们依赖于未使用的导入和功能
  */

  if (isLoading) return (
    <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
      <CircularProgress />
    </Box>
  );

  if (queryError) return (
    <Alert severity="error" sx={{ mt: 2 }}>
      加载患者数据失败: {(queryError as Error).message}
    </Alert>
  );

  return (
    <Box sx={{ 
      m: 0,
      width: '100%',
      paddingLeft: '10px',
      paddingRight: '10px',
      fontSize: '0.875rem',
    }}>
      <Box sx={{ display: 'flex', alignItems: 'flex-end', justifyContent: 'space-between', mb: 0 }}>
        <Typography 
          variant="h5" 
          component="h1" 
          sx={{ 
            fontWeight: 500,
            fontSize: { xs: '1.1rem', sm: '1.3rem' },
            mb: 0
          }}
        >
          患者管理
        </Typography>
        <Box sx={{ flexShrink: 0, ml: 3 }}>
        <Button
          variant="contained"
          startIcon={<PersonAddOutlined />}
          onClick={() => setOpenDialog(true)}
          sx={{ minWidth: 80, px: 2, fontSize: '0.75rem', mb: '3px' }}
        >
            添加
        </Button>
        </Box>
      </Box>
      <Divider sx={{ mb: { xs: 2, md: 3 } }} />
      
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}
      
      <Box 
        sx={{ 
          p: 0, 
          mb: { xs: 2, md: 3 },
          width: '100%',
          '& .MuiInputBase-root': { fontSize: '0.75rem' },
          '& .MuiInputLabel-root': { fontSize: '0.75rem' },
          '& .MuiMenuItem-root': { fontSize: '0.75rem' }
        }}
      >
        <Box sx={{ 
          display: 'grid',
          gridTemplateColumns: { xs: '1fr', md: '1fr auto' },
          gap: 2,
          mb: 2,
          width: '100%'
        }}>
          <TextField
            fullWidth
            label="搜索患者"
            variant="outlined"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="搜索姓名、手机号、邮箱、出生日期等"
            size="small"
            InputProps={{
              startAdornment: <SearchOutlined sx={{ color: 'action.active', mr: 1 }} />,
            }}
          />
          
          {userProfile?.role === 'SERVICE' || userProfile?.role === 'ADMIN' ? (
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>显示模式</InputLabel>
              <Select
                value={viewMode}
                label="显示模式"
                onChange={(e) => setViewMode(e.target.value as 'all' | 'personal' | 'service')}
              >
                <MenuItem value="all">全部患者</MenuItem>
                <MenuItem value="personal">个人患者</MenuItem>
                <MenuItem value="service">授权患者</MenuItem>
              </Select>
            </FormControl>
          ) : null}
        </Box>
        
        <Box sx={{ 
          display: 'grid',
          gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' },
          gap: 2,
          width: '100%',
          mb: 2
        }}>
          <FormControl size="small" fullWidth>
            <InputLabel>性别筛选</InputLabel>
            <Select
              value={filter.gender}
              label="性别筛选"
              onChange={(e) => setFilter({...filter, gender: e.target.value})}
            >
              <MenuItem value="">所有性别</MenuItem>
              <MenuItem value="MALE">男</MenuItem>
              <MenuItem value="FEMALE">女</MenuItem>
            </Select>
          </FormControl>
          
          <FormControl size="small" fullWidth>
            <InputLabel>血型筛选</InputLabel>
            <Select
              value={filter.bloodType}
              label="血型筛选"
              onChange={(e) => setFilter({...filter, bloodType: e.target.value})}
            >
              <MenuItem value="">所有血型</MenuItem>
              <MenuItem value="A">A型</MenuItem>
              <MenuItem value="B">B型</MenuItem>
              <MenuItem value="AB">AB型</MenuItem>
              <MenuItem value="O">O型</MenuItem>
              <MenuItem value="其他">其他</MenuItem>
            </Select>
          </FormControl>
        </Box>
        
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1, color: 'text.secondary', width: '100%' }}>
          <Typography variant="body2" sx={{ fontSize: '0.7rem' }}>
            {viewMode === 'all' ? '显示全部患者' : viewMode === 'personal' ? '仅显示个人患者' : '仅显示授权患者'}
          </Typography>
          <Typography variant="body2" sx={{ fontSize: '0.7rem' }}>
            共 {sortedPatients.length} 位患者
            {viewMode === 'all' && (
              <>
                （个人：{(Array.isArray(filteredPatients) ? filteredPatients : []).filter(p => !p.isAuthorized).length}，
                授权：{(Array.isArray(filteredPatients) ? filteredPatients : []).filter(p => p.isAuthorized).length}）
              </>
            )}
          </Typography>
        </Box>
      </Box>
      
      {isLoading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : sortedPatients.length === 0 ? (
        <Box sx={{ 
          p: 3, 
          textAlign: 'center',
          border: '1px solid #e0e0e0',
          borderRadius: 2, 
          mb: 3
        }}>
          <Typography color="text.secondary" gutterBottom sx={{ fontSize: '0.75rem' }}>
            {isFirstPatient 
              ? '您还没有添加任何患者记录' 
              : '没有找到符合条件的患者'}
          </Typography>
          {isFirstPatient && (
            <Button
              variant="contained"
              startIcon={<PersonAddOutlined />}
              onClick={() => setOpenDialog(true)}
              sx={{ mt: 1, fontSize: '0.75rem' }}
            >
              添加第一位患者
            </Button>
          )}
        </Box>
      ) : isMobile ? (
        <>
          {sortedPatients.map((patient: Patient) => {
            return (
              <Card 
                key={patient.id} 
                elevation={0}
                sx={{ 
                  mb: 2, 
                  border: '1px solid #e0e0e0',
                  borderLeft: patient.emergencyContactRelationship ? 
                    `4px solid ${getRelationshipColor(patient.emergencyContactRelationship)}` : 
                    (patient.isPrimary ? '4px solid #2196f3' : '1px solid #e0e0e0'),
                  display: 'flex',
                  flexDirection: 'column',
                  fontSize: '0.75rem'
                }}
              >
                <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 }, display: 'flex', flexDirection: 'column' }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="h6" component="div" sx={{ fontSize: '0.9rem' }}>
                      {patient.name}
                      {patient.emergencyContactRelationship ? (
                        <Tooltip title={`${getRelationshipDisplay(patient.emergencyContactRelationship)}档案`}>
                          <Typography 
                            component="span" 
                            sx={{ 
                              ml: 1, 
                              fontSize: '0.65rem',
                              color: getRelationshipColor(patient.emergencyContactRelationship)
                            }}
                          >
                            ({getRelationshipDisplay(patient.emergencyContactRelationship)})
                          </Typography>
                        </Tooltip>
                      ) : patient.isPrimary ? (
                        <Tooltip title="本人档案">
                          <Typography component="span" color="primary" sx={{ ml: 1, fontSize: '0.65rem' }}>
                            (本人)
                          </Typography>
                        </Tooltip>
                      ) : null}
                    </Typography>
                    <Box>
                      <Tooltip title="查看详情">
                        <IconButton 
                          size="small" 
                          component={Link} 
                          to={`/patients/${patient.id}`}
                          color="primary"
                        >
                          <InfoOutlined fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="编辑">
                        <IconButton 
                          size="small"
                          color="primary"
                          component={Link}
                          to={`/patients/${patient.id}?edit=true`}
                        >
                          <EditOutlined fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="删除">
                        <IconButton 
                          size="small"
                          onClick={() => handleDeletePatient(patient.id)}
                          color="error"
                        >
                          <DeleteOutlined fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', minHeight: 14, mb: 0.1 }}>
                    <Box sx={{
                      width: 14,
                      height: 12,
                      borderRadius: 6,
                      background: '#e0e0e0',
                      display: 'flex',
                      alignItems: 'center',
                      px: 0.5,
                      mr: 0.5
                    }}>
                      <Box sx={{
                        width: 8,
                        height: 8,
                        borderRadius: '50%',
                        background: '#bdbdbd',
                        transition: 'all 0.3s',
                      }} />
                    </Box>
                    <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem', whiteSpace: 'nowrap', p: 0, m: 0 }}>
                      未授权
                    </Typography>
                  </Box>
                  <Box sx={{ alignItems: 'center', minHeight: 18, mb: 0.2 }}>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 0.2, fontSize: '0.7rem' }}>
                      性别: {getGenderDisplay(patient.gender)} | 血型: {patient.bloodType || '未知'}
                    </Typography>
                  </Box>
                  {patient.phoneNumber && (
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5, fontSize: '0.7rem' }}>
                      电话: {patient.phoneNumber}
                    </Typography>
                  )}
                  {patient.birthDate && (
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.7rem' }}>
                        出生日期: {new Date(patient.birthDate).toLocaleDateString()}
                      </Typography>
                      {/*
                      <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.6rem', fontWeight: 500 }}>
                        病理数 {activeCount} / {activeDiseaseLimit}
                      </Typography>
                      */}
                    </Box>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </>
      ) : (
        <TableContainer sx={{ 
          border: '1px solid #e0e0e0',
          borderRadius: 2,
          overflow: 'hidden',
          '& .MuiTableCell-root': { fontSize: '0.75rem' }
        }}>
          <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell 
                onClick={() => handleSortChange('name')}
                sx={{ cursor: 'pointer', fontWeight: 'bold', fontSize: '0.75rem' }}
              >
                姓名
                {sortBy === 'name' && (
                  <SortIcon 
                    fontSize="small" 
                    sx={{ 
                  verticalAlign: 'middle',
                  transform: sortDirection === 'desc' ? 'rotate(180deg)' : 'none' 
                    }} 
                  />
                )}
              </TableCell>
              <TableCell sx={{ fontSize: '0.75rem' }}>性别</TableCell>
              <TableCell sx={{ fontSize: '0.75rem' }}>血型</TableCell>
              <TableCell 
                onClick={() => handleSortChange('birthDate')}
                sx={{ cursor: 'pointer', fontWeight: 'bold', fontSize: '0.75rem' }}
              >
                出生日期
                {sortBy === 'birthDate' && (
                  <SortIcon 
                    fontSize="small" 
                    sx={{ 
                  verticalAlign: 'middle',
                  transform: sortDirection === 'desc' ? 'rotate(180deg)' : 'none' 
                    }} 
                  />
                )}
              </TableCell>
              <TableCell sx={{ fontSize: '0.75rem' }}>联系电话</TableCell>
              <TableCell 
                onClick={() => handleSortChange('createdAt')}
                sx={{ cursor: 'pointer', fontWeight: 'bold', fontSize: '0.75rem' }}
              >
                添加时间
                {sortBy === 'createdAt' && (
                  <SortIcon 
                    fontSize="small" 
                    sx={{ 
                      verticalAlign: 'middle', 
                      transform: sortDirection === 'desc' ? 'rotate(180deg)' : 'none'
                    }} 
                  />
                )}
              </TableCell>
              <TableCell align="right" sx={{ fontSize: '0.75rem' }}>操作</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
              {sortedPatients.map((patient: Patient) => {
                return (
                  <TableRow 
                    key={patient.id}
                    sx={{ 
                      '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' },
                      backgroundColor: patient.isPrimary ? 'rgba(33, 150, 243, 0.06)' : 
                        (patient.emergencyContactRelationship ? 
                          `rgba(${getRelationshipColor(patient.emergencyContactRelationship).replace('#', '').match(/.{2}/g)?.map(hex => parseInt(hex, 16)).join(', ')}, 0.06)` : 
                          'inherit')
                    }}
                  >
                    <TableCell component="th" scope="row" sx={{ fontSize: '0.75rem' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {patient.name}
                        {patient.emergencyContactRelationship ? (
                          <Tooltip title={`${getRelationshipDisplay(patient.emergencyContactRelationship)}档案`}>
                            <Typography 
                              component="span" 
                              sx={{ 
                                ml: 1, 
                                fontSize: '0.6rem',
                                color: getRelationshipColor(patient.emergencyContactRelationship)
                              }}
                            >
                              ({getRelationshipDisplay(patient.emergencyContactRelationship)})
                            </Typography>
                          </Tooltip>
                        ) : patient.isPrimary ? (
                          <Tooltip title="本人档案">
                            <Typography component="span" color="primary" sx={{ ml: 1, fontSize: '0.6rem' }}>
                              (本人)
                            </Typography>
                          </Tooltip>
                        ) : null}
                      </Box>
                    </TableCell>
                    <TableCell sx={{ fontSize: '0.75rem' }}>{getGenderDisplay(patient.gender)}</TableCell>
                    <TableCell sx={{ fontSize: '0.75rem' }}>{patient.bloodType || '-'}</TableCell>
                    <TableCell sx={{ fontSize: '0.75rem' }}>
                      {patient.birthDate 
                        ? new Date(patient.birthDate).toLocaleDateString() 
                        : '-'}
                    </TableCell>
                    <TableCell sx={{ fontSize: '0.75rem' }}>{patient.phoneNumber || '-'}</TableCell>
                    <TableCell sx={{ fontSize: '0.75rem' }}>
                      {new Date(patient.createdAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell align="right">
                      <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                        {/*
                        <Tooltip title={`活跃病理: ${activeCount} / ${activeDiseaseLimit}`}>
                          <Chip label={`${activeCount}/${activeDiseaseLimit}`} size="small" sx={{ fontSize: '0.6rem', height: '18px', mr: 1 }} />
                        </Tooltip>
                        */}
                        <Tooltip title="查看详情">
                    <IconButton
                      size="small"
                      component={Link}
                      to={`/patients/${patient.id}`}
                            color="primary"
                          >
                            <InfoOutlined fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="编辑">
                          <IconButton 
                      size="small"
                            color="primary"
                            component={Link}
                            to={`/patients/${patient.id}?edit=true`}
                          >
                            <EditOutlined fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="删除">
                    <IconButton
                      size="small"
                      onClick={() => handleDeletePatient(patient.id)}
                            color="error"
                    >
                            <DeleteOutlined fontSize="small" />
                    </IconButton>
                        </Tooltip>
                      </Box>
                  </TableCell>
                </TableRow>
                );
              })}
          </TableBody>
        </Table>
      </TableContainer>
      )}

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle sx={{ fontSize: '1rem' }}>
          {editingPatient ? '编辑患者信息' : '添加新患者'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ 
            display: 'grid', 
            gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)' },
            gap: 2,
            mt: 2,
            '& .MuiInputBase-root': { fontSize: '0.75rem' },
            '& .MuiInputLabel-root': { fontSize: '0.75rem' },
            '& .MuiFormControlLabel-label': { fontSize: '0.75rem' }
          }}>
            <Box sx={{ gridColumn: '1 / -1' }}>
              <TextField
                label="姓名"
                fullWidth
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
                error={!!formErrors.name}
                helperText={formErrors.name}
              />
            </Box>
            <FormControl fullWidth>
              <InputLabel>性别</InputLabel>
              <Select
                value={gender}
                label="性别"
                onChange={(e) => setGender(e.target.value)}
              >
                <MenuItem value="MALE">男</MenuItem>
                <MenuItem value="FEMALE">女</MenuItem>
              </Select>
            </FormControl>
            <TextField
              label="出生日期"
              type="date"
              fullWidth
              value={birthDate}
              onChange={(e) => setBirthDate(e.target.value)}
              required
              error={!!formErrors.birthDate}
              helperText={formErrors.birthDate}
              InputLabelProps={{ shrink: true }}
            />
            <TextField
              label="联系电话"
              fullWidth
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
              error={!!formErrors.phoneNumber}
              helperText={formErrors.phoneNumber}
            />
            <TextField
              label="电子邮箱"
              fullWidth
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
            <TextField
              label="地址"
              fullWidth
              value={address}
              onChange={(e) => setAddress(e.target.value)}
            />
            <FormControl fullWidth>
              <InputLabel>血型</InputLabel>
              <Select
                value={bloodType}
                label="血型"
                onChange={(e) => setBloodType(e.target.value)}
              >
                <MenuItem value="A">A</MenuItem>
                <MenuItem value="B">B</MenuItem>
                <MenuItem value="AB">AB</MenuItem>
                <MenuItem value="O">O</MenuItem>
                <MenuItem value="未知">未知</MenuItem>
              </Select>
            </FormControl>
            <TextField
              label="身高"
              fullWidth
              value={height}
              onChange={(e) => setHeight(e.target.value)}
              error={!!formErrors.height}
              helperText={formErrors.height}
            />
            <TextField
              label="体重"
              fullWidth
              value={weight}
              onChange={(e) => setWeight(e.target.value)}
              error={!!formErrors.weight}
              helperText={formErrors.weight}
            />
            <TextField
              label="BMI"
              fullWidth
              value={bmi.toString()}
              InputProps={{
                readOnly: true,
              }}
            />
            <TextField
              label="紧急联系人"
              fullWidth
              value={emergencyContactName}
              onChange={(e) => setEmergencyContactName(e.target.value)}
            />
            <TextField
              label="紧急联系电话"
              fullWidth
              value={emergencyContactPhone}
              onChange={(e) => setEmergencyContactPhone(e.target.value)}
              error={!!formErrors.emergencyContactPhone}
              helperText={formErrors.emergencyContactPhone}
            />
            <Box sx={{ gridColumn: '1 / -1' }}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isPrimary === 1}
                    onChange={(e) => {
                      console.log('复选框变更:', e.target.checked);
                      setIsPrimary(e.target.checked ? 1 : 0);
                    }}
                    color="primary"
                    disabled={isFirstPatient}
                  />
                }
                label={
                  <Box>
                    设为本人档案
                    {isFirstPatient && (
                      <FormHelperText>首个患者将自动设为本人档案</FormHelperText>
                    )}
                  </Box>
                }
              />
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} sx={{ fontSize: '0.75rem' }}>取消</Button>
          <Button 
            onClick={editingPatient ? handleUpdatePatient : handleCreatePatient} 
            variant="contained"
            color="primary"
            disabled={createMutation.isPending || updateMutation.isPending}
            sx={{ fontSize: '0.75rem' }}
          >
            {(createMutation.isPending || updateMutation.isPending) ? (
              <CircularProgress size={20} />
            ) : (
              editingPatient ? '保存修改' : '添加患者'
            )}
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={deleteDialogOpen}
        onClose={cancelDelete}
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <DialogTitle id="delete-dialog-title" sx={{ fontSize: '1rem' }}>
          确认删除
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" id="delete-dialog-description" sx={{ fontSize: '0.85rem' }}>
            您确定要删除此患者记录吗？此操作无法撤销。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={cancelDelete} color="primary" sx={{ fontSize: '0.75rem' }}>
            取消
          </Button>
          <Button onClick={confirmDelete} color="error" variant="contained" autoFocus sx={{ fontSize: '0.75rem' }}>
            删除
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar 
        open={notification.open} 
        autoHideDuration={5000} 
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert 
          onClose={handleCloseNotification} 
          severity={notification.type} 
          sx={{ width: '100%', '& .MuiAlert-message': { fontSize: '0.75rem' } }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default PatientsPage; 