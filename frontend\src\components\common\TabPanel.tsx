import React from 'react';
import { Box } from '@mui/material';

// Tab面板属性接口
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
  id?: string;
  padding?: number;
}

/**
 * 通用的Tab面板组件
 * 用于统一管理系统中的标签面板显示逻辑
 */
const TabPanel: React.FC<TabPanelProps> = ({ 
  children, 
  value, 
  index, 
  id = 'tab', 
  padding = 3,
  ...other 
}) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`${id}-tabpanel-${index}`}
      aria-labelledby={`${id}-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: padding }}>
          {children}
        </Box>
      )}
    </div>
  );
};

export default TabPanel; 