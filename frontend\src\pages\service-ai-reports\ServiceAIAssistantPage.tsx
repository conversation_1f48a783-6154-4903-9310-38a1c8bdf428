import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  Paper,
  CircularProgress,
  Alert as MuiAlert, 
  List,
  ListItem,
  ListItemText,
  IconButton,
  Tooltip,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Snackbar,
  useTheme,
} from '@mui/material';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import {
  SmartToy as SmartToyIcon,
  Visibility as VisibilityIcon,
  FileDownload as FileDownloadIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  ArrowForward as ArrowForwardIcon,
  // Settings as SettingsIcon // No longer used
} from '@mui/icons-material';
import { format, parseISO } from 'date-fns';
import { zhCN } from 'date-fns/locale';

import { useAuthStore } from '../../store/authStore';
import { useServiceUserContext } from '../../context/ServiceUserContext';
import ServiceContextBar from '../../components/service/ServiceContextBar';
// import ServiceContextSelector from '../../components/service/ServiceContextSelector'; // No longer used

import { getAIReports, checkUserQuota as fetchUserQuota, createAIReport as createAIReportService, deleteAIReport as deleteAIReportService } from '../../services/ai-assistant/aiReportService';
import { AIReport } from '../../types/ai-assistant';

const ServiceAIAssistantPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const serviceContext = useServiceUserContext();
  const currentTheme = useTheme();

  const [reports, setReports] = useState<AIReport[]>([]);
  const [totalReportsCount, setTotalReportsCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  // const [infoMessage, setInfoMessage] = useState<string | null>(null); // 移除状态
  // const [quota, setQuota] = useState<{ used: number; max: number; canUse: boolean } | null>(null); // Quota state removed
  // const [quotaLoading, setQuotaLoading] = useState(false); // Quota loading state removed
  const [creatingReport, setCreatingReport] = useState(false);
  const [reportToDelete, setReportToDelete] = useState<AIReport | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deletingReportId, setDeletingReportId] = useState<string | null>(null);

  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'info' | 'warning' | 'error'>('success');

  // Create a theme with font size reduced by 2 sizes and outlined components
  const smallerFontTheme = createTheme({
    ...currentTheme,
    typography: {
      ...currentTheme.typography,
      h5: {
        ...currentTheme.typography.h5,
        fontSize: '1.0rem', 
      },
      h6: {
        ...currentTheme.typography.h6,
        fontSize: '0.85rem',
      },
      body1: {
        ...currentTheme.typography.body1,
        fontSize: '0.75rem',
      },
      body2: {
        ...currentTheme.typography.body2,
        fontSize: '0.65rem',
      },
      caption: {
        ...currentTheme.typography.caption,
        fontSize: '0.55rem',
      },
      button: {
        ...currentTheme.typography.button,
        fontSize: '0.65rem',
      },
    },
    components: {
      ...currentTheme.components,
      MuiPaper: {
        defaultProps: {
          variant: 'outlined',
          elevation: 0,
        },
      },
      MuiListItem: {
        styleOverrides: {
          root: {
            border: `1px solid ${currentTheme.palette.divider}`,
            boxShadow: 'none',
          },
        },
      },
      MuiDialog: {
        styleOverrides: {
          paper: {
            variant: 'outlined', // This might not directly apply, Dialog uses Paper internally
            elevation: 0,
            boxShadow: 'none', // Ensure no shadow on dialog paper
          }
        }
      },
      MuiAlert: {
        defaultProps:{
          variant: 'outlined', // Use outlined for alerts
        },
        styleOverrides: {
          message: {
            fontSize: '0.65rem',
          },
        },
      },
      MuiChip: {
        styleOverrides: {
          label: {
            fontSize: '0.55rem', 
          }
        }
      },
      MuiListItemText: {
        styleOverrides: {
          primary: {
            fontSize: '0.75rem', 
          },
          secondary: {
            fontSize: '0.65rem',
          }
        }
      },
      MuiInputLabel: {
        styleOverrides: {
          root: {
            fontSize: '0.65rem',
          }
        }
      },
      MuiMenuItem: {
        styleOverrides: {
          root: {
            fontSize: '0.65rem',
          }
        }
      },
      MuiTableCell: {
        styleOverrides: {
          root: {
            fontSize: '0.65rem',
          },
          head: {
            fontSize: '0.7rem', 
            fontWeight: 'bold',
          }
        }
      },
      MuiDialogTitle: {
        styleOverrides: {
          root: {
            fontSize: '0.9rem', 
          }
        }
      },
      MuiDialogContentText: {
        styleOverrides: {
          root: {
            fontSize: '0.65rem',
          }
        }
      },
      MuiFormControlLabel: {
        styleOverrides: {
          label: {
            fontSize: '0.65rem',
          }
        }
      },
    }
  });

  const hasFullControlPermission = React.useMemo(() => {
    if (user?.role === 'ADMIN') return true;
    return serviceContext.privacyLevel === 'FULL';
  }, [user?.role, serviceContext.privacyLevel]);

  const canCreateOrEditReports = React.useMemo(() => {
    if (user?.role === 'ADMIN') return true;
    return serviceContext.privacyLevel === 'FULL' || serviceContext.privacyLevel === 'STANDARD';
  }, [user?.role, serviceContext.privacyLevel]);

  const handleSnackbarClose = (event?: React.SyntheticEvent | Event, reason?: string) => {
    if (reason === 'clickaway') {
      return;
    }
    setSnackbarOpen(false);
  };

  // const fetchQuota = useCallback(async () => { // Quota fetching removed
  //   setQuotaLoading(true);
  //   try {
  //     const quotaData = await fetchUserQuota(); 
  //     setQuota(quotaData);
  //   } catch (err) {
  //     console.error('获取服务用户AI报告配额失败:', err);
  //   } finally {
  //     setQuotaLoading(false);
  //   }
  // }, []);

  const fetchServiceAIReports = useCallback(async () => {
    console.log('[ServiceAIAssistantPage] fetchServiceAIReports triggered. Context:', {
      patientId: serviceContext.patientId,
      authorizationId: serviceContext.authorizationId,
      diseaseId: serviceContext.diseaseId,
      patientName: serviceContext.patientName,
      diseaseName: serviceContext.diseaseName,
    });

    if (!serviceContext.patientId || !serviceContext.authorizationId) {
      console.log('[ServiceAIAssistantPage] fetchServiceAIReports: patientId or authorizationId missing.');
      setError('服务上下文不完整 (缺少患者ID或授权ID)。请确保已通过 "服务授权 -> 服务病理管理" 的流程进入此页面。');
      setReports([]);
      setTotalReportsCount(0);
      setLoading(false); 
      return;
    }
    setLoading(true);
    setError(null);
    try {
      const params: { contextPatientId: string; contextAuthorizationId: string; diseaseId?: string } = {
        contextPatientId: serviceContext.patientId,
        contextAuthorizationId: serviceContext.authorizationId,
      };
      if (serviceContext.diseaseId) {
        params.diseaseId = serviceContext.diseaseId;
      }
      const data = await getAIReports(params);
      const mappedReports = data.map(report => ({
        ...report,
        patientName: report.patientName || serviceContext.patientName || '未知患者',
        diseaseName: report.diseaseName || serviceContext.diseaseName || '未知病理',
      }));
      setReports(mappedReports);
      setTotalReportsCount(mappedReports.length);
    } catch (err) {
      console.error('获取服务AI报告列表失败:', err);
      const errorMessage = err instanceof Error ? err.message : '获取服务AI报告列表失败';
      setError(errorMessage);
      setReports([]);
      setTotalReportsCount(0);
    } finally {
      setLoading(false);
    }
  }, [serviceContext.patientId, serviceContext.diseaseId, serviceContext.authorizationId, serviceContext.patientName, serviceContext.diseaseName]);

  useEffect(() => {
    // console.log('[ServiceAIAssistantPage] Main useEffect: Calling fetchQuota.'); // Quota fetching removed
    // fetchQuota(); // Quota fetching removed

    if (serviceContext.patientId && serviceContext.authorizationId) {
      console.log('[ServiceAIAssistantPage] Main useEffect: Context valid, calling fetchServiceAIReports.');
      fetchServiceAIReports();
    } else {
      console.log('[ServiceAIAssistantPage] Main useEffect: Context invalid.');
      setReports([]); 
      setTotalReportsCount(0); 
      setError('服务上下文未设置或不完整。请通过 "服务授权 -> 服务病理管理" 页面选择一个病理后再访问本功能。');
    }
  }, [fetchServiceAIReports, serviceContext.patientId, serviceContext.authorizationId, serviceContext.diseaseId]); // Removed fetchQuota from dependencies

  const formatDate = (dateString: string) => {
    try {
      return format(parseISO(dateString), 'yyyy-MM-dd HH:mm', { locale: zhCN });
    } catch (error) {
      return dateString;
    }
  };

  const handleCreateAIReport = async () => {
    if (!canCreateOrEditReports) {
      setError('您没有权限创建AI报告。');
      return;
    }
    if (!serviceContext.patientId || !serviceContext.diseaseId || !serviceContext.authorizationId) {
      setError('无法创建报告：服务上下文不完整（缺少患者、病理或授权信息）。请通过 "服务授权 -> 服务病理管理" 的流程确保上下文正确设置。');
      return;
    }
    // if (!quota?.canUse) { // Quota check removed
    //   setError('无法创建报告：AI报告生成次数已用完。');
    //   return;
    // }

    setCreatingReport(true);
    setError(null);
    // setInfoMessage('AI报告生成中，请稍候...'); // Replaced by Snackbar

    try {
      const params = {
        patientId: serviceContext.patientId,
        diseaseId: serviceContext.diseaseId,
      };
      
      await createAIReportService(params); // Result not used, directly proceed
      
      setSnackbarMessage('AI报告已成功开始生成，请在列表中关注其状态。');
      setSnackbarSeverity('success');
      setSnackbarOpen(true);

      fetchServiceAIReports();
      // fetchQuota(); // Quota fetching removed

    } catch (err) {
      console.error('创建服务AI报告失败:', err);
      const errorMessage = err instanceof Error ? err.message : '创建AI报告失败，请检查网络或联系管理员。';
      setError(errorMessage);
      // setInfoMessage(null); // No longer using infoMessage for this
    } finally {
      setCreatingReport(false);
    }
  };

  const openDeleteDialog = (report: AIReport) => {
    if (!canCreateOrEditReports) {
      setError('您没有权限删除AI报告。');
      return;
    }
    setReportToDelete(report);
    setDeleteDialogOpen(true);
  };

  const closeDeleteDialog = () => {
    setReportToDelete(null);
    setDeleteDialogOpen(false);
  };

  const handleDeleteAIReport = async () => {
    if (!reportToDelete) return;
    if (!canCreateOrEditReports) {
      setError('您没有权限删除AI报告。');
      closeDeleteDialog();
      return;
    }

    setDeletingReportId(reportToDelete.id);
    setError(null);

    try {
      await deleteAIReportService(reportToDelete.id);
      setSnackbarMessage(`报告 "${reportToDelete.title || reportToDelete.id}" 已成功删除。`);
      setSnackbarSeverity('success');
      setSnackbarOpen(true);

      closeDeleteDialog();
      fetchServiceAIReports(); 
    } catch (err) {
      console.error('删除服务AI报告失败:', err);
      const errorMessage = err instanceof Error ? err.message : '删除AI报告失败，请稍后重试。';
      setError(errorMessage);
      closeDeleteDialog(); 
    } finally {
      setDeletingReportId(null);
    }
  };

  const renderReportItem = (report: AIReport) => {
    // 确定当前用户是否有删除此特定报告的权限
    const canDeleteThisReport = (
      user?.id === report.userId || // 假设 report.userId 是创建者ID
      user?.role === 'ADMIN' ||
      serviceContext.privacyLevel === 'FULL'
    );

    return (
      <ListItem
        key={report.id}
        sx={{ 
          mb: 2, 
          bgcolor: 'background.paper', 
          borderRadius: 1,
          // position: 'relative', // Removed as icons are now in flow
          // pb: 5, // Removed as icons are now in flow
        }}
      >
        <ListItemText
          primary={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="h6">{report.title || 'AI分析报告'}</Typography>
              <Chip
                label={report.status === 'COMPLETED' ? '已完成' : 
                       report.status === 'PROCESSING' ? '处理中' : 
                       report.status === 'FAILED' ? '失败' : report.status}
                color={report.status === 'COMPLETED' ? 'success' : 
                       report.status === 'PROCESSING' ? 'warning' : 
                       report.status === 'FAILED' ? 'error' : 'default'}
                size="small"
              />
            </Box>
          }
          secondary={
            <>
              <Typography variant="body2" color="text.secondary">
                患者：{report.patientName || '未知'} ({report.patientId?.substring(0,8)}...)
              </Typography>
              <Typography variant="body2" color="text.secondary">
                病理：{report.diseaseName || '未知'} ({report.diseaseId?.substring(0,8)}...)
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 0.5 }}>
                <Typography variant="body2" color="text.secondary">
                  创建时间：{report.createdAt ? formatDate(report.createdAt) : 'N/A'}
                </Typography>
                <Box 
                  sx={{ 
                    // position: 'absolute', // Removed
                    // bottom: currentTheme.spacing(1), // Removed
                    // right: currentTheme.spacing(1), // Removed
                    display: 'flex', 
                    gap: 0.5 
                  }}
                >
                  {report.status === 'COMPLETED' && (
                    <Tooltip title="查看报告">
                      <IconButton 
                        size="small" 
                        onClick={() => navigate(`/service-ai-reports/reports/${report.id}`)}
                        sx={{ p: 0.5 }}
                      >
                        <VisibilityIcon sx={{ fontSize: '1.1rem', color: 'primary.main' }} />
                      </IconButton>
                    </Tooltip>
                  )}
                  {canDeleteThisReport && (
                     <Tooltip title="删除报告">
                        <span>
                          <IconButton 
                            size="small" 
                            onClick={() => openDeleteDialog(report)} 
                            disabled={deletingReportId === report.id}
                            sx={{ p: 0.5 }}
                          >
                            {deletingReportId === report.id ? (
                              <CircularProgress size={18} sx={{ color: 'secondary.main'}} />
                            ) : (
                              <DeleteIcon sx={{ fontSize: '1.1rem', color: 'secondary.main' }} />
                            )}
                          </IconButton>
                        </span>
                      </Tooltip>
                  )}
                </Box>
              </Box>
              {report.status === 'FAILED' && report.errorMessage && (
                <Typography variant="caption" color="error" sx={{ display: 'block', mt: 0.5 }}>
                  错误: {report.errorMessage}
                </Typography>
              )}
            </>
          }
          secondaryTypographyProps={{ component: 'div' }}
        />
      </ListItem>
    );
  };

  // const handleContextSelectionComplete = () => { // 移除此函数
  //   fetchServiceAIReports();
  //   fetchQuota();
  // };

  return (
    <ThemeProvider theme={smallerFontTheme}>
      <Box sx={{ p: 3 }}>
        <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center', color: 'primary.main' }}>
          <SmartToyIcon sx={{ mr: 1 }} /> 服务用户AI助手与报告管理
        </Typography>

        <ServiceContextBar onSelectContext={() => {}} />

        {!loading && reports.length > 0 && serviceContext.patientId && serviceContext.authorizationId && (
          <Typography variant="body2" sx={{ my: 2, color: 'text.secondary' }}>
            当前患者共 {totalReportsCount} 条AI报告
          </Typography>
        )}
        {!loading && reports.length === 0 && serviceContext.patientId && serviceContext.authorizationId && !error && (
           <Typography variant="body2" sx={{ my: 2, color: 'text.secondary' }}>
            当前患者共 0 条AI报告
          </Typography>
        )}

        <Paper sx={{ p: 2, mt: 2 /* variant and elevation are set by theme */ }}>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h6">AI报告列表</Typography>
            <Box>
              {canCreateOrEditReports && serviceContext.patientId && serviceContext.diseaseId && ( // Quota check removed from condition
                <Button
                  variant="contained"
                  startIcon={<SmartToyIcon />}
                  onClick={handleCreateAIReport}
                  disabled={loading || creatingReport}
                >
                  {creatingReport ? '生成中...' : '生成AI报告'}
                </Button>
              )}
              {/* Tooltip for quota limit removed */}
            </Box>
          </Box>

          {error && <MuiAlert severity="error" sx={{ mb: 2 }}>{error}</MuiAlert>} 
          {/* {infoMessage && <MuiAlert severity="info" sx={{ mb: 2 }}>{infoMessage}</MuiAlert>} // 移除 infoMessage Alert */}
          
          {/* Quota display UI removed */}
          {/* {quota && (
            <Box sx={{ mb: 2, p:1, backgroundColor: 'grey.100', borderRadius: 1 }}>
              <Typography variant="body2" color="text.secondary">
                您的AI分析配额: {quota.used} / {quota.max} 次
                {!quota.canUse && <Typography component="span" variant="body2" color="error" sx={{ ml:1 }}> (次数已用完)</Typography>}
              </Typography>
            </Box>
          )} */}
          
          {loading ? (
            <CircularProgress sx={{ display: 'block', margin: 'auto', mt: 3 }} />
          ) : reports.length > 0 ? (
            <List>{reports.map(renderReportItem)}</List>
          ) : (
            <Box sx={{ textAlign: 'center', p: 3 }}>
              <Typography sx={{ color: 'text.secondary', mb: 2 }}>
                {serviceContext.patientId && serviceContext.authorizationId ? '当前上下文暂无AI分析报告。' : '请先选择服务上下文。'}
              </Typography>
              {serviceContext.patientId && serviceContext.authorizationId && serviceContext.diseaseId && reports.length === 0 && canCreateOrEditReports && ( // Quota check removed
                <Button
                  variant="contained"
                  startIcon={<SmartToyIcon />}
                  onClick={handleCreateAIReport}
                  disabled={creatingReport}
                >
                  {creatingReport ? '生成中...' : '立即创建AI报告'}
                </Button>
              )}
              {/* Tooltip for quota limit removed for the empty list case */}
            </Box>
          )}
        </Paper>

        {reportToDelete && (
          <Dialog open={deleteDialogOpen} onClose={closeDeleteDialog}>
            <DialogTitle>确认删除AI报告</DialogTitle>
            <DialogContent>
              <DialogContentText>
                您确定要删除AI分析报告 "<strong>{reportToDelete.title || reportToDelete.id}</strong>"吗？此操作不可撤销。
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button onClick={closeDeleteDialog}>取消</Button>
              <Button onClick={handleDeleteAIReport} color="error" autoFocus>
                删除
              </Button>
            </DialogActions>
          </Dialog>
        )}

        <Snackbar
          open={snackbarOpen}
          autoHideDuration={6000}
          onClose={handleSnackbarClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        >
          <MuiAlert onClose={handleSnackbarClose} severity={snackbarSeverity} sx={{ width: '100%' }} /* variant is set by theme */>
            {snackbarMessage}
          </MuiAlert>
        </Snackbar>

      </Box>
    </ThemeProvider>
  );
};

export default ServiceAIAssistantPage;