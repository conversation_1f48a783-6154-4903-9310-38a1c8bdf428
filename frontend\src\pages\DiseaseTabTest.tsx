import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Container,
  Paper, 
  Button,
  CircularProgress,
  Alert,
  Divider,
  useTheme
} from '@mui/material';
import { useParams, useNavigate } from 'react-router-dom';
import { getDisease } from '../services/diseaseService';
import { getPatient } from '../services/patientService';
import { getRecords } from '../services/recordService';
import { getAIReports } from '../services/ai-assistant/aiReportService';

// 引入病理Tab组件（先注释掉直到开发完成）
/*
import PatientInfoCard from '../components/dashboard/disease-tab/PatientInfoCard';
import DiseaseInfoCard from '../components/dashboard/disease-tab/DiseaseInfoCard';
import DiseaseTimeline from '../components/dashboard/disease-tab/DiseaseTimeline';
import AIReportCard from '../components/dashboard/disease-tab/AIReportCard';
import ProgressDevelopmentCard from '../components/dashboard/disease-tab/ProgressDevelopmentCard';
import MedicalInfoCard from '../components/dashboard/disease-tab/MedicalInfoCard';
import MedicalAdviceCard from '../components/dashboard/disease-tab/MedicalAdviceCard';
import HospitalRecommendationCard from '../components/dashboard/disease-tab/HospitalRecommendationCard';
import ActionBar from '../components/dashboard/disease-tab/ActionBar';
*/

/**
 * 病理Tab测试页面
 * 用于独立测试病理页面组件
 */
const DiseaseTabTest: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { diseaseId } = useParams<{ diseaseId: string }>();
  
  // 数据状态
  const [patient, setPatient] = useState<any>(null);
  const [disease, setDisease] = useState<any>(null);
  const [records, setRecords] = useState<any[]>([]);
  const [aiReports, setAIReports] = useState<any[]>([]);
  
  // 加载状态
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // 加载数据
  useEffect(() => {
    const fetchData = async () => {
      if (!diseaseId) return;
      
      setLoading(true);
      setError(null);
      
      try {
        // 加载病理信息
        const diseaseData = await getDisease(diseaseId);
        setDisease(diseaseData);
        
        // 加载患者信息
        if (diseaseData?.patientId) {
          const patientData = await getPatient(diseaseData.patientId);
          setPatient(patientData);
        }
        
        // 加载记录
        const recordsData = await getRecords({ diseaseId });
        setRecords(Array.isArray(recordsData) ? recordsData : 
                  (recordsData?.records || []));
        
        // 加载AI报告
        const reportsData = await getAIReports(diseaseId ? { diseaseId } : undefined);
        setAIReports(Array.isArray(reportsData) ? reportsData : []);
        
      } catch (err) {
        console.error('加载数据失败:', err);
        setError('加载病理数据失败，请稍后重试');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [diseaseId]);
  
  // 处理返回按钮点击
  const handleBack = () => {
    navigate('/dashboard');
  };
  
  // 加载中显示loading状态
  if (loading) {
    return (
      <Container maxWidth="md" sx={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center',
        minHeight: '400px'
      }}>
        <CircularProgress size={40} />
      </Container>
    );
  }
  
  // 显示错误
  if (error) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Button 
          variant="outlined" 
          onClick={handleBack}
          sx={{ mt: 2 }}
        >
          返回仪表盘
        </Button>
      </Container>
    );
  }
  
  // 未找到病理时显示提示
  if (!disease) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Paper 
          elevation={0} 
          sx={{ 
            p: 4, 
            textAlign: 'center',
            borderRadius: 2,
            border: `1px solid ${theme.palette.divider}`
          }}
        >
          <Typography variant="h5" color="text.secondary" gutterBottom>
            未找到病理信息
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
            请确认病理ID是否正确
          </Typography>
          <Button 
            variant="contained" 
            onClick={handleBack}
          >
            返回仪表盘
          </Button>
        </Paper>
      </Container>
    );
  }
  
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* 标题与病理信息 */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          病理详情测试页面
        </Typography>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="subtitle1">
            {disease.name} ({patient?.name || '未知患者'})
          </Typography>
          <Button 
            variant="outlined" 
            onClick={handleBack}
          >
            返回仪表盘
          </Button>
        </Box>
        <Divider sx={{ my: 2 }} />
      </Box>
      
      {/* 数据概览 */}
      <Paper sx={{ p: 3, mb: 4, borderRadius: 2 }}>
        <Typography variant="h6" gutterBottom>
          已加载数据
        </Typography>
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2">
            <strong>病理ID:</strong> {disease.id}
          </Typography>
          <Typography variant="body2">
            <strong>患者ID:</strong> {disease.patientId}
          </Typography>
          <Typography variant="body2">
            <strong>记录数量:</strong> {records.length}
          </Typography>
          <Typography variant="body2">
            <strong>AI报告数量:</strong> {aiReports.length}
          </Typography>
        </Box>
      </Paper>
      
      {/* 病理Tab组件测试区 */}
      <Paper sx={{ p: 3, borderRadius: 2 }}>
        <Typography variant="h6" gutterBottom>
          病理Tab组件测试
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          此处将展示病理Tab页面的各组件
        </Typography>
        
        {/* 组件测试区域，随开发进度取消注释 */}
        <Box sx={{ p: 3, bgcolor: 'grey.100', borderRadius: 2, textAlign: 'center' }}>
          <Typography variant="body1">
            组件测试区域，处于开发阶段
          </Typography>
        </Box>
      </Paper>
    </Container>
  );
};

export default DiseaseTabTest; 