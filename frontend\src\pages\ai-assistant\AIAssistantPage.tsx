import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import { 
  Box, 
  Typography, 
  Button, 
  Paper, 
  List, 
  ListItem, 
  ListItemText, 
  ListItemSecondaryAction, 
  IconButton,
  Divider,
  CircularProgress,
  Alert,
  Tooltip,
  Chip,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  InputAdornment,
  Grid,
  useTheme,
  Snackbar,
  Stack
} from '@mui/material';
import {
  SmartToy as SmartToyIcon,
  Visibility as VisibilityIcon,
  FileDownload as FileDownloadIcon,
  ArrowForward as ArrowForwardIcon,
  Info as InfoIcon,
  Check as CheckIcon,
  Error as ErrorIcon,
  LocationOn as LocationIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Search as SearchIcon,
  Description as DescriptionIcon,
  Download as DownloadIcon,
  Add as AddIcon,
  CheckCircle as CheckCircleIcon,
  Pending as PendingIcon
} from '@mui/icons-material';
import { format, parseISO } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { usePatientDiseaseContext } from '../../context/PatientDiseaseContext';
import { getAIReports, createAIReport, downloadAIReportPDF, checkUserQuota, getAIReport, deleteAIReport, regenerateAIReport, pollReportStatus } from '../../services/ai-assistant/aiReportService';
import { AIReport, AIReportStatus, CreateAIReportParams } from '../../types/ai-assistant';
import { useAiUsage } from '../../context/AiUsageContext';
import { incrementAiUsage } from '../../services/aiUsageService';
import { useAuthStore } from '../../store/authStore';
import { getDiseases } from '../../services/disease/diseaseService';
import { getPatients } from '../../services/patient/patientService';
import { Disease } from '../../types/disease';
import { Patient } from '../../types/patient';
import { formatDate } from '../../utils/dateUtils';
import { useSnackbar } from 'notistack';
import { LoadingButton } from '@mui/lab';
import { useReportConfig } from '../../hooks/useReportConfig';
import { ReportVisibilityConfig } from '../../types/ai-assistant';

/**
 * 辅医模块主页面
 * 显示当前病理的AI分析列表，并提供创建新分析的功能
 */
const AIAssistantPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { selectedPatientId, selectedDiseaseId } = usePatientDiseaseContext();
  const [reports, setReports] = useState<AIReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [infoMessage, setInfoMessage] = useState<string | null>(null);
  const [quotaLoading, setQuotaLoading] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [targetRegion, setTargetRegion] = useState<string>('');
  
  // 删除报告的状态
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [reportToDelete, setReportToDelete] = useState<string | null>(null);
  const [deleting, setDeleting] = useState(false);

  // 使用AI使用上下文获取最新的使用数据
  const { aiUsageCount, maxAiUsage, refreshAiUsage } = useAiUsage();
  // 计算是否还可以使用AI功能
  const canUseAi = maxAiUsage > aiUsageCount;

  // 中国主要城市列表
  const majorCities = [
    '北京', '上海', '广州', '深圳', '天津', '重庆', '武汉', '成都',
    '南京', '杭州', '西安', '郑州', '长沙', '济南', '青岛', '大连',
    '沈阳', '哈尔滨', '长春', '福州', '厦门', '合肥', '南昌', '昆明',
    '贵阳', '南宁', '海口', '兰州', '西宁', '银川', '乌鲁木齐', '呼和浩特',
    '石家庄', '太原', '其他'
  ];

  // 添加请求缓存
  const [quotaCache, setQuotaCache] = useState<{data: any, timestamp: number} | null>(null);
  const CACHE_DURATION = 30000; // 30秒缓存

  // 优化初始化加载
  useEffect(() => {
    let mounted = true;
    
    const initialLoad = async () => {
    try {
        console.log('[AIAssistantPage] 开始初始化加载...');
        setLoading(true);
        
        // 并行加载所有必要数据
        const [quotaData] = await Promise.all([
          fetchQuota()
        ]);
        
        if (mounted) {
          console.log('[AIAssistantPage] 设置配额缓存...');
          setQuotaCache({
            data: quotaData,
            timestamp: Date.now()
          });
      
          // 立即加载报告列表
          console.log('[AIAssistantPage] 开始加载报告列表...');
          await fetchReports();
        }
    } catch (error) {
        console.error('[AIAssistantPage] 初始化加载失败:', error);
        setError('页面加载失败，请刷新重试');
      } finally {
        if (mounted) {
          setLoading(false);
        }
    }
  };

    initialLoad();
    
    return () => {
      mounted = false;
    };
  }, []);
  
  // 优化报告列表获取函数
  const fetchReports = async (diseaseIdForFilter?: string) => {
    console.log('[AIAssistantPage] 开始获取报告列表...');
    setLoading(true);
    setError(null);
    
    try {
      const params = diseaseIdForFilter ? { diseaseId: diseaseIdForFilter } : undefined;
      console.log('[AIAssistantPage] 请求参数:', params);
      
      const data = await getAIReports(params); 
      console.log('[AIAssistantPage] 获取到报告数据:', data);
      
      setReports(data.map(report => ({
        ...report,
        patientName: report.patientName || '未知患者',
        diseaseName: report.diseaseName || '未知病理',
      })));
    } catch (err) {
      console.error('[AIAssistantPage] 获取报告列表失败:', err);
      setError(err instanceof Error ? err.message : '获取AI分析报告列表失败，请稍后重试');
      setReports([]);
    } finally {
      setLoading(false);
    }
  };

  // 当选中的病理变化时，加载其AI报告列表
  useEffect(() => {
    if (selectedDiseaseId) {
      console.log('[AIAssistantPage] 病理ID变化，重新加载报告列表...');
      fetchReports(selectedDiseaseId);
    }
  }, [selectedDiseaseId]);

  // 优化自动刷新逻辑
  useEffect(() => {
    let refreshTimeout: NodeJS.Timeout;
    let mounted = true;
    
    const hasProcessingReports = reports.some(report => {
      const status = report.status;
      return status === 'PROCESSING' || status === 'PENDING';
    });
    
    if (hasProcessingReports) {
      console.log('[AIAssistantPage] 检测到处理中的报告，开始自动刷新...');
      
      const refreshReports = async () => {
        if (!mounted) return;
        
        try {
          console.log('[AIAssistantPage] 自动刷新报告列表...');
          if (selectedDiseaseId) {
            await fetchReports(selectedDiseaseId);
          } else {
            await fetchReports();
          }
          
          // 检查是否还有处理中的报告
          const stillProcessing = reports.some(report => {
            const status = report.status;
            return status === 'PROCESSING' || status === 'PENDING';
          });
          
          if (stillProcessing) {
            // 如果还有处理中的报告，继续轮询
            refreshTimeout = setTimeout(refreshReports, 5000); // 每5秒刷新一次
          } else {
            console.log('[AIAssistantPage] 所有报告处理完成，停止自动刷新');
          }
        } catch (error) {
          console.error('[AIAssistantPage] 自动刷新失败:', error);
          // 发生错误时也继续轮询
          refreshTimeout = setTimeout(refreshReports, 5000);
        }
      };
      
      // 开始第一次刷新
      refreshTimeout = setTimeout(refreshReports, 5000);
    }
    
    return () => {
      mounted = false;
      if (refreshTimeout) {
        clearTimeout(refreshTimeout);
      }
    };
  }, [reports, selectedDiseaseId]);

  // 优化配额获取函数
  const fetchQuota = async () => {
    if (quotaLoading) return null;
    
    // 检查缓存是否有效
    if (quotaCache && Date.now() - quotaCache.timestamp < CACHE_DURATION) {
      return quotaCache.data;
    }
    
    setQuotaLoading(true);
    
    try {
      const quotaData = await checkUserQuota();
      
      if (quotaData) {
        setQuotaCache({
          data: quotaData,
          timestamp: Date.now()
        });
      }
      
      return quotaData;
    } catch (error) {
      console.error('获取用户配额失败', error);
      return null;
    } finally {
      setQuotaLoading(false);
    }
  };
  
  // 打开创建报告对话框
  const handleOpenCreateDialog = () => {
    if (!selectedDiseaseId) return;
    setOpenDialog(true);
  };

  // 关闭创建报告对话框
  const handleCloseDialog = () => {
    setOpenDialog(false);
  };
  
  // 创建新的AI报告
  const handleCreateReport = async () => {
    if (!selectedDiseaseId || creating) return;

    // 确保patientId有效
    if (!selectedPatientId) {
      setError('无法创建报告：未选择患者');
      console.error('创建AI报告失败: 未选择患者 (patientId为空)');
      return;
    }
    
    console.log('准备创建AI报告，请求参数检查:');
    console.log('- 病理ID:', selectedDiseaseId);
    console.log('- 患者ID:', selectedPatientId);
    console.log('- 目标区域:', targetRegion || '全国');
    
    setCreating(true);
    handleCloseDialog();
    setError(null);
    setInfoMessage('AI报告生成请求已提交，正在处理中...');
    
    try {
      // 检查是否还有可用配额
      if (!canUseAi) {
        setError('您的AI报告生成次数已用完，请联系客服');
        setInfoMessage(null);
        setCreating(false);
        return;
      }
      
      // 准备请求参数
      const requestParams: CreateAIReportParams = {
        diseaseId: selectedDiseaseId,
        patientId: selectedPatientId,
        targetRegion: targetRegion || '全国'
      };
      
      console.log('发送的请求参数:', JSON.stringify(requestParams, null, 2));
      
      // 发送创建请求
      const response = await createAIReport(requestParams);
      
      // 增加AI使用次数计数
      refreshAiUsage();
      
      // 保存创建的报告ID
      const createdReportId = response.aiReport.id;
      
      if (response.status === 'ALREADY_PROCESSING') {
        setInfoMessage('已有报告正在生成中，请耐心等待...');
        await fetchReports();
        setCreating(false);
        return;
      }
      
      // 设置生成进度信息
      let progressCounter = 1;
      const updateProgressMessage = () => {
        if (progressCounter > 20) progressCounter = 1;
        let dots = '.'.repeat(progressCounter % 4);
        setInfoMessage(`AI报告正在生成中${dots} (预计需要2-3分钟，请耐心等待)`);
        progressCounter++;
      };
      
      const progressInterval = setInterval(updateProgressMessage, 1000);
      updateProgressMessage();
      
      try {
        // 开始轮询检查报告状态
        const completedReport = await pollReportStatus(
          createdReportId,
          30,  // 最多轮询30次
          (status, report, attempts) => {
            if (status === 'PROCESSING') {
              let dots = '.'.repeat(attempts % 4);
              setInfoMessage(`AI报告正在生成中${dots} (已等待${attempts * 6}秒，预计总需要150秒)`);
            } else if (status === 'COMPLETED') {
              clearInterval(progressInterval);
              setInfoMessage('报告生成成功！正在加载...');
              // 立即刷新报告列表
              fetchReports();
            } else if (status === 'FAILED') {
              clearInterval(progressInterval);
              setInfoMessage(null);
              setError(`报告生成失败: ${report.errorMessage || '未知错误'}`);
            }
          }
        );
        
        clearInterval(progressInterval);
        
        // 刷新报告列表
        await fetchReports();
        
        // 跳转到报告详情页
        navigate(`/ai-assistant/reports/${completedReport.id}`);
        
      } catch (pollError) {
        clearInterval(progressInterval);
        console.error('轮询报告状态失败:', pollError);
        
        // 即使轮询失败，也刷新报告列表
        await fetchReports();
        
        if (createdReportId) {
          setInfoMessage('报告可能已生成，请查看列表');
          // 提供直接查看的链接
          setError('轮询超时，但报告可能已生成。请查看报告列表或直接查看报告。');
        } else {
          setError('轮询报告状态失败，请刷新页面查看报告状态');
        }
      }
    } catch (error) {
      console.error('创建报告失败', error);
      setInfoMessage(null);
      setError(error instanceof Error ? error.message : '创建报告失败，请稍后重试');
    } finally {
      setCreating(false);
    }
  };
  
  // 下载PDF报告
  const handleDownloadPDF = async (reportId: string) => {
    try {
      setError(''); // 清除之前的错误信息
      setInfoMessage('正在准备下载报告，请稍候...');
      
      await downloadAIReportPDF(reportId);
      
      // 下载成功提示
      setInfoMessage('报告下载成功');
      
      // 3秒后清除提示信息
      setTimeout(() => {
        if (setInfoMessage) { // 检查组件是否仍然挂载
          setInfoMessage(null);
        }
      }, 3000);
    } catch (error) {
      console.error('下载PDF失败', error);
      
      // 提供更有用的错误消息
      if (error instanceof Error) {
        if (error.message.includes('Network Error') || error.message.includes('net::ERR')) {
          setError('网络错误：PDF下载失败，但文件可能已开始下载。请检查您的下载文件夹。');
        } else {
          setError(`下载PDF失败：${error.message}`);
        }
      } else {
        setError('下载PDF失败，请稍后重试');
      }
    } finally {
      // 延迟清除加载状态，让用户有时间看到消息
      setTimeout(() => {
        if (setInfoMessage) { // 检查组件是否仍然挂载
          if (infoMessage === '正在准备下载报告，请稍候...') {
            setInfoMessage(null);
          }
        }
      }, 1000);
    }
  };
  
  // 查看报告详情
  const handleViewReport = (reportId: string) => {
    navigate(`/ai-assistant/reports/${reportId}`);
  };
  
  // 格式化日期
  const formatDate = (dateString: string) => {
    try {
      return format(parseISO(dateString), 'yyyy-MM-dd HH:mm', { locale: zhCN });
    } catch (error) {
      return dateString;
    }
  };
  
  // 删除AI报告
  const handleDeleteReport = async (reportId: string) => {
    setDeleteDialogOpen(false);
    
    if (!reportId) return;
    
    setDeleting(true);
    setError(null); // 清除之前的错误信息
    setInfoMessage('正在删除报告，请稍候...');
    
    try {
      await deleteAIReport(reportId);
      // 刷新报告列表
      await fetchReports();
      setInfoMessage('报告删除成功');
    } catch (error) {
      console.error('删除报告失败', error);
      setError('删除报告失败，请稍后重试');
    } finally {
      setDeleting(false);
      setReportToDelete(null);
    }
  };
  
  // 打开删除确认对话框
  const openDeleteConfirmDialog = (reportId: string) => {
    setReportToDelete(reportId);
    setDeleteDialogOpen(true);
  };
  
  // 关闭删除确认对话框
  const closeDeleteConfirmDialog = () => {
    setDeleteDialogOpen(false);
    setReportToDelete(null);
  };

  // 重新生成AI报告
  const handleRegenerateReport = async (reportId: string) => {
    try {
      setLoading(true);
      setError(null);
      setInfoMessage('正在准备重新生成报告...');
      
      // 先检查是否还有可用配额
      if (!canUseAi) {
        setError('您的AI报告生成次数已用完，请联系客服');
        setInfoMessage(null);
        setLoading(false);
        return;
      }
      
      // 获取报告详情
      const report = reports.find(r => r.id === reportId);
      if (!report) {
        throw new Error('未找到要重新生成的报告');
      }
      
      const params: CreateAIReportParams = {
        diseaseId: report.diseaseId,
        patientId: report.patientId,
        targetRegion: report.content?.hospitalRecommendations?.targetRegion || '全国'
      };
      
      // 发送重新生成请求
      const response = await regenerateAIReport(params);
      
      // 增加AI使用次数计数
      refreshAiUsage();
      
      // 后端同步检查
      await fetchQuota();
      
      // 刷新报告列表
      await fetchReports();
      
      // 跳转到新报告详情页
      navigate(`/ai-assistant/reports/${response.aiReport.id}`);
      
      setInfoMessage(null);
    } catch (error) {
      console.error('重新生成报告失败', error);
      setInfoMessage(null);
      setError('重新生成报告失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 在渲染列表项时添加重新生成按钮
  const renderReportItem = (report: AIReport) => (
    <ListItem
      key={report.id}
      sx={{
        mb: 2,
        bgcolor: 'background.paper',
        borderRadius: 1,
        boxShadow: 'none',
        border: '1px solid #e0e0e0',
        '&:hover': {
          boxShadow: 1,
        },
        px: '10px',
        position: 'relative',
        pt: 1,
        pb: 1 // 恢复正常内边距
      }}
    >
      <ListItemText
        primary={
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="h6" component="div" sx={{ fontSize: '0.75rem' }}>
                {report.title || 'AI分析报告'}
              </Typography>
              <Chip
                label={report.status === 'COMPLETED' ? '已完成' : 
                       report.status === 'PROCESSING' ? '生成中' : 
                       report.status === 'FAILED' ? '失败' : report.status}
                color={report.status === 'COMPLETED' ? 'success' : 
                       report.status === 'PROCESSING' ? 'warning' : 
                       report.status === 'FAILED' ? 'error' : 'default'}
                size="small"
                sx={{ '& .MuiChip-label': { fontSize: '0.65rem' } }}
              />
            </Box>
          </Box>
        }
        secondary={
          <Box sx={{ mt: 1 }}>
            <Typography component="span" variant="body2" color="text.secondary" sx={{ display: 'block', fontSize: '0.7rem' }}>
              患者：{report.patientName || '未指定'}
            </Typography>
            <Typography component="span" variant="body2" color="text.secondary" sx={{ display: 'block', fontSize: '0.7rem' }}>
              病理：{report.diseaseName || '未指定'}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Typography component="span" variant="body2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center', fontSize: '0.7rem' }}>
                <DescriptionIcon fontSize="inherit" sx={{ mr: 0.5 }} />
                创建时间：{report.createdAt ? formatDate(report.createdAt) : '未知'}
              </Typography>
              <Box sx={{ display: 'flex', gap: 0.5 }}>
                {report.status === 'COMPLETED' && (
                  <>
                    <Tooltip title="查看报告">
                      <IconButton 
                        onClick={() => handleViewReport(report.id)} 
                        size="small"
                        color="primary"
                        sx={{ p: 0.5 }}
                      >
                        <VisibilityIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="下载PDF">
                      <IconButton 
                        onClick={() => handleDownloadPDF(report.id)} 
                        size="small"
                        color="primary"
                        sx={{ p: 0.5 }}
                      >
                        <FileDownloadIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </>
                )}
                {report.status === 'FAILED' && (
                  <Tooltip title="重新生成">
                    <IconButton 
                      onClick={() => handleRegenerateReport(report.id)} 
                      size="small"
                      color="primary"
                      sx={{ p: 0.5 }}
                    >
                      <RefreshIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                )}
                <Tooltip title="删除报告">
                  <IconButton 
                    onClick={() => openDeleteConfirmDialog(report.id)} 
                    size="small"
                    color="secondary"
                    sx={{ p: 0.5 }}
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            </Box>
            {report.status === 'FAILED' && (
              <Typography component="span" variant="body2" color="error" sx={{ mt: 0.5, display: 'block', fontSize: '0.7rem' }}>
                错误：{report.errorMessage || '报告生成失败，详情请查看报告。'}
              </Typography>
            )}
          </Box>
        }
        secondaryTypographyProps={{ component: 'div' }}
      />
    </ListItem>
  );

  return (
    <Box>
      <Paper sx={{ p: 3, mb: 3, boxShadow: 'none', border: 'none', borderRadius: 1, mx: '10px' }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Box display="flex" alignItems="center">
            <SmartToyIcon sx={{ mr: 1 }} />
            <Typography variant="h6" sx={{ fontSize: { xs: '0.95rem', sm: '1.05rem' } }}>辅医分析</Typography>
          </Box>
          
          {selectedDiseaseId && (
            <Button 
              variant="outlined" 
              color="primary" 
              startIcon={<SmartToyIcon />}
              onClick={handleOpenCreateDialog}
              disabled={creating || !canUseAi}
              endIcon={creating && <CircularProgress size={20} color="inherit" />}
              sx={{ fontSize: '0.75rem' }}
            >
              {creating ? '生成报告中 (约需2分钟)' : '生成分析报告'}
            </Button>
          )}
        </Box>
        
        {error && (
          <Alert severity="error" sx={{ mb: 2, '& .MuiAlert-message': { fontSize: '0.75rem' } }}>
            {error}
          </Alert>
        )}
        
        {infoMessage && (
          <Alert severity="info" sx={{ mb: 2, '& .MuiAlert-message': { fontSize: '0.75rem' } }}>
            {infoMessage}
          </Alert>
        )}
        
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.7rem' }}>
            本月已用 {aiUsageCount}/{maxAiUsage} 次分析
          </Typography>
          {!canUseAi && (
            <Typography variant="body2" color="error" sx={{ fontSize: '0.7rem' }}>
              您本月的AI分析次数已用完，请下月再试或升级账户
            </Typography>
          )}
        </Box>
      </Paper>
      
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3, mx: '10px' }}>
          <CircularProgress />
        </Box>
      ) : reports.length > 0 ? (
        <Paper sx={{ boxShadow: 'none', border: 'none', borderRadius: 1, mx: '10px' }}>
          {!selectedDiseaseId && (
            <Alert severity="info" sx={{ m: 2, '& .MuiAlert-message': { fontSize: '0.75rem' } }}>
              当前显示您的所有AI报告。如需针对特定病理生成新报告或筛选列表，请先在看板页面选择患者和病理，然后再返回此页面。
               <Button 
                variant="outlined" 
                size="small"
                onClick={() => navigate('/dashboard')}
                sx={{ ml:1, mt: 0.5, fontSize: '0.7rem' }}
              >
                前往看板选择病理
              </Button>
            </Alert>
          )}
          <List>
            {reports.map((report) => renderReportItem(report))}
          </List>
        </Paper>
      ) : (
        <Paper sx={{ p: 3, textAlign: 'center', boxShadow: 'none', border: 'none', borderRadius: 1, mx: '10px' }}>
          <Typography color="text.secondary" gutterBottom sx={{ fontSize: '0.75rem' }}>
            {selectedDiseaseId ? '当前选定病理暂无分析报告。' : '您还没有任何AI分析报告。'}
          </Typography>
          {!selectedDiseaseId && (
             <Button 
                variant="outlined" 
                onClick={() => navigate('/dashboard')}
                sx={{ mt: 2, fontSize: '0.75rem' }}
              >
                前往看板选择患者和病理以生成新报告
              </Button>
          )}
        </Paper>
      )}

      {/* 创建报告对话框 */}
      <Dialog open={openDialog} onClose={handleCloseDialog} sx={{ '& .MuiDialog-paper': { mx: '10px', width: 'calc(100% - 20px)', maxWidth: 600 } }}>
        <DialogTitle sx={{ fontSize: '1rem' }}>创建AI分析报告</DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ fontSize: '0.75rem' }}>
            请选择患者意向就医地区，这将帮助系统提供更精确的医院和科室推荐。
          </DialogContentText>
          <Alert severity="info" sx={{ mt: 2, mb: 2, '& .MuiAlert-message': { fontSize: '0.75rem' } }}>
            <Typography variant="body2" sx={{ fontSize: '0.7rem' }}>
              注意：生成AI报告需要约2分钟时间，请耐心等待。报告生成完成后将自动跳转。
            </Typography>
          </Alert>
          <Box sx={{ mt: 2 }}>
            <FormControl fullWidth>
              <InputLabel id="region-select-label" sx={{ fontSize: '0.75rem' }}>意向就医地区</InputLabel>
              <Select
                labelId="region-select-label"
                value={targetRegion}
                label="意向就医地区"
                onChange={(e) => setTargetRegion(e.target.value)}
                startAdornment={<LocationIcon sx={{ mr: 1, ml: -0.5 }} />}
                sx={{ '& .MuiSelect-select': { fontSize: '0.75rem' } }}
              >
                <MenuItem value="" sx={{ fontSize: '0.75rem' }}>
                  <em>全国范围</em>
                </MenuItem>
                {majorCities.map((city) => (
                  <MenuItem key={city} value={city} sx={{ fontSize: '0.75rem' }}>
                    {city}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} sx={{ fontSize: '0.75rem' }}>取消</Button>
          <Button 
            onClick={handleCreateReport}
            disabled={creating}
            color="primary"
            variant="contained"
            endIcon={creating && <CircularProgress size={20} color="inherit" />}
            sx={{ fontSize: '0.75rem' }}
          >
            {creating ? '生成报告中 (约需2分钟)' : '生成报告'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog
        open={deleteDialogOpen}
        onClose={closeDeleteConfirmDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        sx={{ '& .MuiDialog-paper': { mx: '10px', width: 'calc(100% - 20px)', maxWidth: 400 } }}
      >
        <DialogTitle id="alert-dialog-title" sx={{ fontSize: '1rem' }}>{"确认删除报告"}</DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description" sx={{ fontSize: '0.75rem' }}>
            您确定要删除该报告吗？删除后将无法恢复。
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeDeleteConfirmDialog} sx={{ fontSize: '0.75rem' }}>取消</Button>
          <Button 
            onClick={() => reportToDelete && handleDeleteReport(reportToDelete)} 
            color="error"
            disabled={deleting}
            startIcon={deleting && <CircularProgress size={20} />}
            sx={{ fontSize: '0.75rem' }}
          >
            {deleting ? '删除中...' : '确认删除'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AIAssistantPage; 