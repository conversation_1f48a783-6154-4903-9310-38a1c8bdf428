const { Model, snakeCaseMappers } = require('objection');

class TagCategory extends Model {
  static get tableName() {
    return 'tag_categories';
  }

  static get idColumn() {
    return 'id';
  }

  // 添加下划线命名映射配置
  static get columnNameMappers() {
    return snakeCaseMappers();
  }

  // 字段验证规则
  static get jsonSchema() {
    return {
      type: 'object',
      required: ['name'],
      properties: {
        id: { type: 'string' },
        name: { type: 'string', minLength: 1, maxLength: 50 },
        description: { type: ['string', 'null'], maxLength: 500 },
        icon: { type: ['string', 'null'], maxLength: 50 },
        color: { type: 'string', maxLength: 20, default: '#4A90E2' },
        order: { type: 'integer', default: 0 },
        isSystem: { type: 'boolean', default: false },
        createdBy: { type: ['string', 'null'] },
        updatedAt: { type: ['string', 'null'] },
        createdAt: { type: ['string', 'null'] }
      }
    };
  }

  // 关系定义
  static get relationMappings() {
    const Tag = require('./Tag');
    const User = require('./User');

    return {
      // 该分类下的标签（一对多）
      tags: {
        relation: Model.HasManyRelation,
        modelClass: Tag,
        join: {
          from: 'tag_categories.id',
          to: 'tags.categoryId'
        }
      },

      // 创建者关系
      creator: {
        relation: Model.BelongsToOneRelation,
        modelClass: User,
        join: {
          from: 'tag_categories.createdBy',
          to: 'users.id'
        }
      }
    };
  }

  // 获取所有分类及其标签
  static async getAllWithTags() {
    return this.query()
      .withGraphFetched('tags')
      .orderBy('order', 'asc');
  }

  // 更新分类时间
  $beforeUpdate() {
    this.updatedAt = new Date().toISOString();
  }

  // 创建分类时设置时间
  $beforeInsert() {
    const now = new Date().toISOString();
    this.createdAt = now;
    this.updatedAt = now;
  }
}

module.exports = TagCategory; 