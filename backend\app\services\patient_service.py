from typing import List, Dict
from sqlalchemy.orm import Session
from app.models.patient import Patient, PatientRecord
from app.core.logger import logger

class PatientService:
    def __init__(self, db: Session):
        """初始化患者服务"""
        self.db = db

    def get_patient(self, patient_id: int) -> Patient:
        """获取患者信息"""
        return self.db.query(Patient).filter(Patient.id == patient_id).first()

    def get_patient_records(self, patient_id: int) -> List[Dict]:
        """获取患者的所有记录"""
        try:
            # 获取患者信息
            patient = self.get_patient(patient_id)
            if not patient:
                return []
            
            # 获取所有记录，排除AI分析记录
            records = self.db.query(PatientRecord).filter(
                PatientRecord.patient_id == patient_id,
                PatientRecord.record_type != "AI_ANALYSIS"  # 排除AI分析记录
            ).order_by(PatientRecord.created_at.desc()).all()
            
            # 转换为字典列表
            result = []
            for record in records:
                record_dict = {
                    "id": record.id,
                    "patient_id": record.patient_id,
                    "record_type": record.record_type,
                    "content": record.content,
                    "created_at": record.created_at.isoformat(),
                    "updated_at": record.updated_at.isoformat() if record.updated_at else None
                }
                result.append(record_dict)
            
            return result
        except Exception as e:
            logger.error(f"获取患者记录失败: {str(e)}")
            raise 