{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.2", "@mui/lab": "^7.0.0-beta.11", "@mui/material": "^7.0.2", "@mui/system": "^7.0.2", "@mui/x-data-grid": "^8.1.0", "@mui/x-date-pickers": "^8.3.1", "@tanstack/react-query": "^5.74.4", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "axios": "^1.9.0", "date-fns": "^4.1.0", "notistack": "^3.0.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.1", "react-router-dom": "^7.5.2", "react-scripts": "5.0.1", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "zustand": "^5.0.3"}, "scripts": {"start": "react-scripts start --host 0.0.0.0", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "overrides": {"nth-check": "^2.1.1", "postcss": "^8.4.38"}, "devDependencies": {"@types/react-virtualized-auto-sizer": "^1.0.4", "@types/react-window": "^1.8.8", "axios-mock-adapter": "^2.1.0"}}