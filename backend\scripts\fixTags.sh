#!/bin/bash
# 服务记录标签问题分析和解决方案

echo "=============================================================================="
echo "服务记录标签字段问题分析和解决方案"
echo "=============================================================================="
echo "问题描述:"
echo "1. 服务用户建立的记录在custom_tags和stage_tags字段写入有误"
echo "   - 问题现象: 自定义标签(客户标签)被错误写入stage_tags字段"
echo "   - 问题现象: 阶段标签数组被错误写入custom_tags字段"
echo "2. 服务用户创建的记录is_important字段未正确写入"
echo "3. 部分记录的stage_node和stage_phase字段值不匹配或缺失"
echo ""
echo "原因分析:"
echo "1. serviceRecordController.js中字段名注释错误，导致字段混淆:"
echo "   - stage_tags: stageTags (错误备注为\"客户自定义标签\")"
echo "   - custom_tags: customTags (错误备注为\"阶段标签\")"
echo "2. 前端ServiceRecordFormPage.tsx中提交请求数据时字段映射错误:"
echo "   - stageTags: customTags (错误地将自定义标签写入stageTags)"
echo "   - customTags: JSON.stringify(selectedStageTagsArray) (错误地将阶段标签写入customTags)"
echo "3. is_important在服务用户创建记录时未正确处理"
echo ""
echo "实施修复:"
echo "1. 已修正serviceRecordController.js中的字段名注释"
echo "2. 已修正ServiceRecordFormPage.tsx中的字段映射"
echo "3. 创建了数据库修复脚本fixRecordTags.js，用于修复现有记录数据:"
echo "   - 交换服务用户创建记录中的custom_tags和stage_tags字段内容"
echo "   - 修复服务用户记录中缺失的is_important字段(设为false)"
echo "   - 根据stage_phase为缺失的stage_node字段设置默认值"
echo ""
echo "数据验证结果:"
echo "运行checkServiceRecords.js检查服务记录表和关联记录"
echo "- 所有服务记录的record_id均有效"
echo "- 关联记录数与服务记录数一致(21条)"
echo "- 标签字段统计显示部分数据已修复"
echo ""
echo "总结:"
echo "完成修复后，服务用户和个人用户的记录标签字段现在保持了一致的存储结构:"
echo "- custom_tags: 存放用户自定义的标签信息(字符串，如\"慧看病\")"
echo "- stage_tags: 存放与病程阶段相关的标签数组(JSON格式)"
echo "- 确保is_important字段正确写入"
echo "- stage_node和stage_phase字段保持一致"
echo "==============================================================================" 