const express = require('express');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');
const knex = require('knex')(require('../knexfile').development);
const patientRoutes = require('../routes/patientRoutes');

const app = express();
app.use(express.json());
const JWT_SECRET = 'your_jwt_secret'; // 生产环境中使用环境变量

// 允许跨域请求
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  if (req.method === 'OPTIONS') {
    return res.sendStatus(200);
  }
  next();
});

// 注册用户
app.post('/register', async (req, res) => {
  const {
    username,
    email,
    password,
    phoneNumber,
    role = 'USER',
    level = 'PERSONAL',
    activeDiseaseLimit = 5,
    aiUsageCount = 0,
    familyMemberLimit = 3,
  } = req.body;
  const passwordHash = await bcrypt.hash(password, 10);
  const updatedAt = new Date().toISOString();
  try {
    await knex('users').insert({
      id: uuidv4(),
      username,
      email,
      passwordHash,
      phoneNumber,
      role,
      level,
      activeDiseaseLimit,
      aiUsageCount,
      familyMemberLimit,
      updatedAt,
    });
    res.status(201).json({ message: '用户注册成功' });
  } catch (error) {
    console.error('注册错误:', error);
    res.status(400).json({ error: '用户名、邮箱或手机号已存在' });
  }
});

// 登录
app.post('/login', async (req, res) => {
  const { username, password } = req.body;
  console.log('登录请求参数:', { username, password: '******' });

  try {
    // 首先尝试通过用户名查找用户
    let user = await knex('users').where({ username }).first();
    console.log('通过用户名查找结果:', user ? '找到用户' : '未找到用户');
    
    // 如果未找到，尝试通过电子邮件查找
    if (!user) {
      console.log('尝试通过邮箱查找...');
      user = await knex('users').where({ email: username }).first();
      console.log('通过邮箱查找结果:', user ? '找到用户' : '未找到用户');
    }
    
    // 如果仍未找到，尝试通过手机号码查找
    if (!user) {
      console.log('尝试通过手机号查找...');
      user = await knex('users').where({ phoneNumber: username }).first();
      console.log('通过手机号查找结果:', user ? '找到用户' : '未找到用户');
    }
    
    // 如果所有尝试都失败，或密码不匹配，返回错误
    if (!user) {
      console.log('未找到匹配的用户');
      return res.status(401).json({ error: '用户名/邮箱/手机号或密码错误' });
    }
    
    const passwordMatch = await bcrypt.compare(password, user.passwordHash);
    console.log('密码匹配结果:', passwordMatch ? '匹配' : '不匹配');
    
    if (!passwordMatch) {
      return res.status(401).json({ error: '用户名/邮箱/手机号或密码错误' });
    }
    
    if (!user.isActive) {
      console.log('用户账号未激活');
      return res.status(403).json({ error: '账号未激活' });
    }
    
    const token = jwt.sign({ id: user.id, username: user.username }, JWT_SECRET, {
      expiresIn: '15m', // 短生命周期
    });
    
    const lastLoginAt = new Date().toISOString();
    await knex('users').where({ id: user.id }).update({ lastLoginAt });
    console.log('登录成功, 用户ID:', user.id);
    res.json({ token });
  } catch (error) {
    console.error('登录错误详情:', error);
    res.status(500).json({ error: '服务器错误' });
  }
});

// 获取用户信息（受保护）
app.get('/user/profile', async (req, res) => {
  const authHeader = req.headers.authorization;
  if (!authHeader) return res.status(401).json({ error: '未授权' });
  const token = authHeader.split(' ')[1];
  try {
    const { id } = jwt.verify(token, JWT_SECRET);
    const user = await knex('users').where({ id }).first();
    if (!user) return res.status(404).json({ error: '用户不存在' });
    const levelLimit = await knex('user_level_limits')
      .where({ levelType: user.level })
      .first();
    res.json({
      username: user.username,
      email: user.email,
      phoneNumber: user.phoneNumber,
      avatar: user.avatar,
      role: user.role,
      level: user.level,
      lastLoginAt: user.lastLoginAt,
      activeDiseaseLimit: user.activeDiseaseLimit,
      aiUsageCount: user.aiUsageCount,
      aiUsageResetAt: user.aiUsageResetAt,
      familyMemberLimit: user.familyMemberLimit,
      maxPatients: levelLimit?.maxPatients,
      maxPathologies: levelLimit?.maxPathologies,
      maxAttachmentSize: levelLimit?.maxAttachmentSize,
      maxTotalStorage: levelLimit?.maxTotalStorage,
    });
  } catch (error) {
    console.error('获取用户信息错误:', error);
    res.status(401).json({ error: 'token 无效或已过期' });
  }
});

// 更新用户信息（受保护）
app.put('/user/profile', async (req, res) => {
  const authHeader = req.headers.authorization;
  if (!authHeader) return res.status(401).json({ error: '未授权' });
  const token = authHeader.split(' ')[1];
  try {
    const { id } = jwt.verify(token, JWT_SECRET);
    const { email, phoneNumber, avatar } = req.body;
    const updatedAt = new Date().toISOString();
    await knex('users')
      .where({ id })
      .update({ email, phoneNumber, avatar, updatedAt });
    res.json({ message: '用户信息更新成功' });
  } catch (error) {
    console.error('更新用户信息错误:', error);
    res.status(401).json({ error: 'token 无效或已过期' });
  }
});

// 使用患者路由
app.use('/patients', patientRoutes);

// 初始化默认用户等级限制
async function setupDefaultLevelLimits() {
  try {
    const existingLevels = await knex('user_level_limits').select('levelType');
    
    // 如果没有记录，则添加默认值
    if (existingLevels.length === 0) {
      const defaultLevels = [
        {
          id: uuidv4(),
          levelType: 'PERSONAL',
          maxPatients: 1,
          maxPathologies: 3,
          maxAttachmentSize: 2048, // 2MB
          maxTotalStorage: 10240, // 10MB
        },
        {
          id: uuidv4(),
          levelType: 'FAMILY',
          maxPatients: 4,
          maxPathologies: 5,
          maxAttachmentSize: 5120, // 5MB
          maxTotalStorage: 25600, // 25MB
        },
        {
          id: uuidv4(),
          levelType: 'PROFESSIONAL',
          maxPatients: 8,
          maxPathologies: 5,
          maxAttachmentSize: 10240, // 10MB
          maxTotalStorage: 51200, // 50MB
        }
      ];
      
      await knex('user_level_limits').insert(defaultLevels);
      console.log('已创建默认用户等级限制');
    }
  } catch (error) {
    console.error('设置默认用户等级限制失败:', error);
  }
}

// 服务器启动
const PORT = process.env.PORT || 3001;
app.listen(PORT, async () => {
  console.log(`后端服务运行在端口 ${PORT}`);
  // 初始化默认用户等级限制
  await setupDefaultLevelLimits();
}); 