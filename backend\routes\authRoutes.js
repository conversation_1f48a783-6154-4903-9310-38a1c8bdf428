const express = require('express');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');
const knex = require('knex')(require('../knexfile')[process.env.NODE_ENV === 'production' ? 'production' : 'development']);
const { auth } = require('../src/middleware/auth');

const router = express.Router();
const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret';

/**
 * 注册用户
 * @route POST /register
 * @desc 注册新用户
 * @access 公开
 */
router.post('/register', async (req, res) => {
  try {
    // 从请求体中提取数据，同时支持驼峰和下划线命名风格
    const {
      username,
      email,
      password,
      // 同时支持驼峰命名和下划线命名
      phone_number,
      phoneNumber,
      role = 'USER',
      level = 'PERSONAL',
      active_disease_limit = 3,
      ai_usage_count = 0,
      family_member_limit = 1,
    } = req.body;
    
    // 使用phone_number字段，如果不存在则使用phoneNumber字段
    const finalPhoneNumber = phone_number || phoneNumber || null;
    
    // 记录注册数据（不含密码）
    console.log('注册请求:', {
      username,
      email,
      hasPassword: !!password,
      phone_number: finalPhoneNumber,
      role
    });
    
    const passwordHash = await bcrypt.hash(password, 10);
    const now = new Date().toISOString();
    const updatedAt = now;
    const createdAt = now;
    
    // 插入用户记录
    await knex('users').insert({
      id: uuidv4(),
      username,
      email,
      password_hash: passwordHash,
      phone_number: finalPhoneNumber,
      role,
      level,
      is_active: true,
      active_disease_limit: active_disease_limit,
      ai_usage_count: ai_usage_count,
      ai_usage_reset_at: null,
      family_member_limit: family_member_limit,
      updated_at: new Date().toISOString(),
      created_at: new Date().toISOString(),
    });
    res.status(201).json({ message: '用户注册成功' });
  } catch (error) {
    console.error('注册错误:', error);
    res.status(400).json({ error: '用户名、邮箱或手机号已存在' });
  }
});

/**
 * 用户登录
 * @route POST /login
 * @desc 验证用户凭据并返回JWT令牌
 * @access 公开
 */
router.post('/login', async (req, res) => {
  const { username, password } = req.body;
  console.log('登录请求参数:', { username, password: '******' });

  try {
    // 查看users表的结构
    const userColumns = await knex('users').columnInfo();
    console.log('用户表字段:', Object.keys(userColumns));
    
    // 首先尝试通过用户名查找用户
    let user = await knex('users').where({ username }).first();
    console.log('通过用户名查找结果:', user ? '找到用户' : '未找到用户');
    if (user) console.log('用户详情:', { id: user.id, username: user.username, role: user.role, level: user.level });
    
    // 如果未找到，尝试通过电子邮件查找
    if (!user) {
      console.log('尝试通过邮箱查找...');
      user = await knex('users').where({ email: username }).first();
      console.log('通过邮箱查找结果:', user ? '找到用户' : '未找到用户');
    }
    
    // 如果仍未找到，尝试通过手机号码查找
    if (!user) {
      console.log('尝试通过手机号查找...');
      user = await knex('users').where({ phoneNumber: username }).first();
      console.log('通过手机号查找结果:', user ? '找到用户' : '未找到用户');
    }
    
    // 如果所有查找方法都失败，返回401未授权状态
    if (!user) {
      console.log('未找到用户，登录失败');
      return res.status(401).json({ error: '用户名或密码错误' });
    }
    
    // 验证密码
    console.log('用户对象（登录前）:', user);
    console.log('用户密码哈希（登录前）:', user ? user.password_hash : '用户对象为空');
    const validPassword = await bcrypt.compare(password, user.password_hash);
    console.log('密码验证结果:', validPassword ? '密码正确' : '密码错误');
    
    if (!validPassword) {
      return res.status(401).json({ error: '用户名或密码错误' });
    }

    // 检查用户是否被禁用
    if (user.isActive === false || user.isActive === 0) { // 明确检查 false 或 0
      console.log(`用户 ${user.username} (ID: ${user.id}) 已被禁用，登录失败。isActive: ${user.isActive}`);
      return res.status(403).json({ error: '用户已被禁用，请联系管理员。' });
    }
    
    // 创建JWT令牌
    const token = jwt.sign(
      { 
        id: user.id, 
        username: user.username,
        role: user.role,
        level: user.level
      }, 
      JWT_SECRET, 
      { expiresIn: '24h' }
    );
    
    // 移除密码哈希后返回用户信息
    const { passwordHash, ...userWithoutPassword } = user;
    console.log('登录成功，用户:', { id: user.id, username: user.username, role: user.role });
    
    res.json({ 
      message: '登录成功', 
      token,
      user: userWithoutPassword 
    });
  } catch (error) {
    console.error('登录错误详情:', error);
    res.status(500).json({ error: '服务器错误' });
  }
});

/**
 * 获取用户信息
 * @route GET /user/profile
 * @desc 获取已认证用户的资料
 * @access 私有（需要认证）
 */
router.get('/user/profile', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    console.log(`获取用户(${userId})资料`);
    
    const user = await knex('users')
      .select(
        'id', 
        'username', 
        'email', 
        'phone_number', 
        'role', 
        'level', 
        'active_disease_limit',
        'family_member_limit',
        'ai_usage_count',
        'avatar',
        'created_at',
        'updated_at'
      )
      .where({ id: userId })
      .first();
    
    if (!user) {
      console.error(`未找到用户(${userId})`);
      return res.status(404).json({ error: '用户不存在' });
    }
    
    // 转换为驼峰命名
    const camelCaseUser = {
      id: user.id,
      username: user.username,
      email: user.email,
      phoneNumber: user.phone_number,
      role: user.role,
      level: user.level,
      activeDiseaseLimit: user.active_disease_limit,
      familyMemberLimit: user.family_member_limit,
      aiUsageCount: user.ai_usage_count,
      avatar: user.avatar,
      createdAt: user.created_at,
      updatedAt: user.updated_at
    };
    
    res.json(camelCaseUser);
  } catch (error) {
    console.error('获取用户资料失败:', error);
    res.status(500).json({ error: '服务器错误' });
  }
});

/**
 * 修改密码
 * @route POST /user/change-password
 * @desc 允许已认证用户修改密码
 * @access 私有（需要认证）
 */
router.post('/user/change-password', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    
    // 打印请求体以便调试
    console.log('修改密码请求体:', JSON.stringify(req.body));
    
    // 使用前端发送的参数名
    const { currentPassword, newPassword } = req.body;
    
    // 验证请求参数
    if (!currentPassword || !newPassword) {
      console.log('修改密码参数缺失:', { currentPassword: !!currentPassword, newPassword: !!newPassword });
      return res.status(400).json({ error: '当前密码和新密码都是必须的' });
    }
    
    // 获取用户的当前密码哈希
    const user = await knex('users')
      .select('password_hash')
      .where({ id: userId })
      .first();
    
    if (!user) {
      console.log('修改密码用户不存在:', userId);
      return res.status(404).json({ error: '用户不存在' });
    }
    
    // 验证当前密码
    const validPassword = await bcrypt.compare(currentPassword, user.password_hash);
    if (!validPassword) {
      console.log('修改密码当前密码不正确');
      return res.status(401).json({ error: '当前密码不正确' });
    }
    
    // 哈希新密码
    const newPasswordHash = await bcrypt.hash(newPassword, 10);
    
    // 更新密码
    await knex('users')
      .where({ id: userId })
      .update({ 
        password_hash: newPasswordHash,
        updated_at: new Date().toISOString()
      });
    
    console.log('密码修改成功:', userId);
    res.json({ message: '密码修改成功' });
  } catch (error) {
    console.error('修改密码失败:', error);
    res.status(500).json({ error: '服务器错误，密码修改失败' });
  }
});

module.exports = router; 