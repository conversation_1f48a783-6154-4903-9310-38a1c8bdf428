// 加载环境变量
require('dotenv').config();

const express = require('express');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');
const knex = require('knex')(require('../knexfile').development);
const cors = require('cors');
const net = require('net');
const { setUpDb } = require('./db');
const { checkPort } = require('./utils/portUtils');
const { auth } = require('./middleware/auth');
const WebSocket = require('ws');
const http = require('http');
const bodyParser = require('body-parser');

// 引入CORS调试工具
const { debugCorsHeaders, addDownloadCorsHeaders } = require('./utils/corsDebugger');

// 引入路由
const patientRoutes = require('../routes/patientRoutes');
const diseaseRoutes = require('../routes/diseaseRoutes');
const userRoutes = require('../routes/userRoutes');
// 引入授权相关路由
const authorizationRoutes = require('../routes/authorization');
const serviceRecordRoutes = require('../routes/serviceRecord');
const serviceReportRoutes = require('../routes/serviceReport');
// 引入认证相关路由
const authRoutes = require('../routes/authRoutes');
// 引入用户限制路由
const userLimitRoutes = require('../routes/userLimitRoutes');
// 引入标签相关路由
const tagRoutes = require('../routes/tagRoutes');
// 引入记录相关路由
const recordRoutes = require('../routes/recordRoutes');
// 引入AI报告路由
const aiReportRoutes = require('../routes/aiReportRoutes');
// 引入通知路由
const notificationRoutes = require('../routes/notificationRoutes');
// 引入测试路由
const testRoutes = require('../routes/testRoutes');
// 引入管理员路由
const adminRoutes = require('../routes/adminRoutes');

// 定义端口，优先使用环境变量中的端口，如果没有则使用3001
const PORT = process.env.PORT || 3001;
// 根据环境设置不同的WebSocket默认端口
// 开发环境使用3005，生产环境使用3005-3008
const WS_PORT = process.env.WS_PORT || 3005;

const app = express();
// 增加请求体大小限制，解决 PayloadTooLargeError 错误
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ limit: '10mb', extended: true }));
const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret'; // 使用环境变量中的JWT密钥

// 允许跨域请求
app.use(cors({
  // 允许更多源地址，包括本地IP和不同端口，以及HTTPS协议
  origin: function(origin, callback) {
    const allowedOrigins = [
      // HTTP 本地开发环境
      'http://localhost:3000',
      'http://localhost:3001',
      'http://localhost:3002',
      'http://localhost:3004',
      'http://localhost:3005',
      'http://localhost:3006',
      'http://localhost:3007',
      'http://localhost:3008',
      'http://127.0.0.1:3000',
      'http://127.0.0.1:3001',
      'http://127.0.0.1:3002',
      'http://127.0.0.1:3004',
      'http://127.0.0.1:3005',
      'http://127.0.0.1:3006',
      'http://127.0.0.1:3007',
      'http://127.0.0.1:3008',
      // HTTP 生产环境
      'http://************',
      'http://************:3000',
      'http://************:3001',
      'http://************:3002',
      'http://************:3004',
      'http://************:3005',
      'http://************:3006',
      'http://************:3007',
      'http://************:3008',
      // HTTPS 生产环境
      'https://hkb.life',
      'https://************',
      'https://************:443'
    ];

    // 允许没有origin的请求（比如本地文件或Postman）
    if (!origin) {
      return callback(null, true);
    }

    // 检查请求源是否在允许列表中（精确匹配）
    if (allowedOrigins.indexOf(origin) !== -1) {
      return callback(null, true);
    }

    // 允许本地IP不同端口的请求 (如果需要的话，注意生产环境安全性)
    if (process.env.NODE_ENV !== 'production') {
      if (origin.match(/^http:\/\/192\.168\.\d+\.\d+:\d+$/) ||
          origin.match(/^http:\/\/127\.0\.0\.1:\d+$/) ||
          origin.match(/^http:\/\/localhost:\d+$/)) {
        return callback(null, true);
      }
    } else {
      // 在生产环境，允许所有来自生产环境IP和域名的请求（HTTP和HTTPS）
      if (origin.match(/^https?:\/\/14\.103\.135\.3(:\d+)?$/) ||
          origin.match(/^https:\/\/hkb\.life$/)) {
        return callback(null, true);
      }
    }

    // 记录被拒绝的源
    console.error(`CORS: 拒绝来自 ${origin} 的请求`);
    callback(new Error('不允许的源'));
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'X-Is-Authorization-Request',
    'cache-control',
    'Cache-Control',
    'Pragma',
    'If-Modified-Since',
    'If-None-Match',
    'X-Request-ID',
    'expires',
    'Expires',
    // 添加更多常用的缓存控制头
    'ETag',
    'Last-Modified'
  ],
  credentials: true,
  exposedHeaders: ['Content-Disposition', 'Content-Type', 'Content-Length', 'Content-Range', 'Content-Description'],
  maxAge: 86400 // 预检请求结果缓存24小时
}));

// 为附件下载API添加专门的CORS处理
app.options('/records/attachments/download/:id', cors());
app.options('/api/records/attachments/download/:id', cors());

// 添加全局日志中间件，记录所有请求
app.use((req, res, next) => {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] 请求: ${req.method} ${req.originalUrl}, IP: ${req.ip}`);

  // 记录请求头中的认证信息（隐藏token内容）
  const auth = req.headers.authorization ? '存在' : '不存在';
  console.log(`[${timestamp}] 认证头: ${auth}`);

  // 捕获响应结束事件
  res.on('finish', () => {
    console.log(`[${timestamp}] 响应: ${req.method} ${req.originalUrl}, 状态码: ${res.statusCode}`);
  });

  next();
});

// 添加CORS调试和增强的下载CORS头中间件
app.use(addDownloadCorsHeaders);

// 为特定路径添加CORS调试
app.use('/records/attachments/download', (req, res, next) => {
  console.log('附件下载路径被访问:', req.method, req.path);
  // 仅在开发环境启用完整CORS调试
  if (process.env.NODE_ENV === 'development') {
    debugCorsHeaders(req, res);
  }
  next();
});

// ====== 新增：统一 API 路由前缀处理 ======
/**
 * 注册一个路由，同时支持带/api前缀和不带前缀的路径
 * @param {string} path - 路由基础路径（不带前缀）
 * @param {object} router - Express路由对象
 */
function registerDualPrefixRoute(path, router) {
  console.log(`注册双路径路由: ${path} 和 /api${path}`);
  // 注册不带前缀的路径
  app.use(path, router);
  // 注册带/api前缀的路径
  app.use(`/api${path}`, router);
}

// 使用统一的路由注册函数注册路由
registerDualPrefixRoute('/patients', patientRoutes);
registerDualPrefixRoute('/diseases', diseaseRoutes);
registerDualPrefixRoute('/users', userRoutes);
registerDualPrefixRoute('/authorizations', authorizationRoutes);
registerDualPrefixRoute('/service-records', serviceRecordRoutes);
registerDualPrefixRoute('/service-reports', serviceReportRoutes);
// 注册认证相关路由
registerDualPrefixRoute('', authRoutes); // 根路径，处理 /login, /register 等
// 注册用户限制相关路由
registerDualPrefixRoute('/user', userLimitRoutes); // 用户相关路径，处理 /user/limits, /user/profile 等
// 直接注册AI使用量路由，确保能匹配到 /api/user/ai-usage/* 路径
app.use('/api/user/ai-usage', userLimitRoutes);
// 注册标签相关路由
registerDualPrefixRoute('/tags', tagRoutes);
// 注册记录相关路由
registerDualPrefixRoute('/records', recordRoutes);
// 注册AI报告路由
registerDualPrefixRoute('/ai-reports', aiReportRoutes);
// 注册通知路由
registerDualPrefixRoute('/notifications', notificationRoutes);
// 注册测试路由
registerDualPrefixRoute('/test', testRoutes);
// 注册管理员路由
registerDualPrefixRoute('/admin', adminRoutes);

// 添加兼容性路由处理，将旧路径重定向到新路径
app.use('/api/service/records', (req, res, next) => {
  console.log('兼容性路由: 将 /api/service/records 重定向到 /api/service-records');
  req.url = req.url.replace('/api/service/records', '/api/service-records');
  app._router.handle(req, res, next);
});

// 添加兼容性路由处理，将旧路径重定向到新路径
app.use('/api/service/reports', (req, res, next) => {
  console.log('兼容性路由: 将 /api/service/reports 重定向到 /api/service-reports');
  req.url = req.url.replace('/api/service/reports', '/api/service-reports');
  app._router.handle(req, res, next);
});

// 健康检查端点 - 不带前缀
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: process.env.APP_VERSION || '1.0.0',
    env: process.env.NODE_ENV || 'development',
    prefix: 'none'
  });
});

// 健康检查端点 - 带前缀
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: process.env.APP_VERSION || '1.0.0',
    env: process.env.NODE_ENV || 'development',
    prefix: 'api'
  });
});

// 默认路由
app.get('/', (req, res) => {
  res.send('欢迎使用患者管理系统API');
});

// 添加全局错误处理中间件
app.use((err, req, res, next) => {
  // 记录错误到控制台
  console.error('全局错误处理器捕获错误:', err);
  console.error('错误堆栈:', err.stack);

  // 记录到日志文件
  const fs = require('fs');
  const path = require('path');
  const logDir = path.join(__dirname, '../logs');

  // 确保日志目录存在
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }

  const logFile = path.join(logDir, 'server-errors.log');
  const timestamp = new Date().toISOString();
  const logEntry = `${timestamp} - 错误: ${err.message}\n路径: ${req.method} ${req.originalUrl}\n请求体: ${JSON.stringify(req.body, null, 2)}\n堆栈: ${err.stack}\n\n`;

  fs.appendFileSync(logFile, logEntry);

  // 响应客户端
  res.status(500).json({
    success: false,
    message: '服务器内部错误',
    error: err.message,
    path: req.originalUrl,
    timestamp
  });
});

// 创建HTTP服务器
const server = http.createServer(app);

// WebSocket服务器相关变量
let wss; // 声明 wss 变量
let heartbeatInterval; // 声明心跳检测变量

// 存储所有活跃的WebSocket连接
const clients = new Map();

// WebSocket连接处理函数
const handleWebSocketConnection = (ws, req) => {
  console.log('新的WebSocket客户端连接');

  // 从URL中获取token
  const url = new URL(req.url, 'http://localhost');
  const token = url.searchParams.get('token');

  if (!token) {
    console.log('WebSocket连接缺少token');
    ws.close(1008, '缺少认证token');
    return;
  }

  try {
    // 验证token
    const decoded = jwt.verify(token, JWT_SECRET);
    console.log('WebSocket客户端认证成功:', decoded.username);

    // 存储客户端信息
    clients.set(ws, {
      userId: decoded.id,
      username: decoded.username,
      lastPing: Date.now()
    });

    // 发送欢迎消息
    ws.send(JSON.stringify({
      type: 'connection',
      message: '已连接到后端WebSocket服务器',
      timestamp: new Date().toISOString()
    }));

    // 处理消息
    ws.on('message', (message) => {
      console.log('收到WebSocket消息:', message.toString());
      try {
        const data = JSON.parse(message);
        // 处理不同类型的消息
        if (data.type === 'ping') {
          // 更新最后ping时间
          const client = clients.get(ws);
          if (client) {
            client.lastPing = Date.now();
          }
          ws.send(JSON.stringify({
            type: 'pong',
            timestamp: new Date().toISOString()
          }));
        }
      } catch (err) {
        console.error('解析WebSocket消息失败:', err);
      }
    });

    // 处理ping
    ws.on('pong', () => {
      const client = clients.get(ws);
      if (client) {
        client.lastPing = Date.now();
      }
    });

    // 处理关闭
    ws.on('close', () => {
      console.log('WebSocket客户端断开连接');
      clients.delete(ws);
    });

    // 处理错误
    ws.on('error', (error) => {
      console.error('WebSocket错误:', error);
      clients.delete(ws);
    });

  } catch (err) {
    console.error('WebSocket token验证失败:', err);
    ws.close(1008, '无效的token');
  }
};

// 心跳检测间隔（毫秒）
const HEARTBEAT_INTERVAL = 30000;
// 客户端超时时间（毫秒）
const CLIENT_TIMEOUT = 35000;

// 定期发送心跳检测函数
const startHeartbeat = () => {
  heartbeatInterval = setInterval(() => {
    const now = Date.now();
    clients.forEach((client, ws) => {
      if (now - client.lastPing > CLIENT_TIMEOUT) {
        console.log('客户端超时，关闭连接');
        ws.terminate();
        clients.delete(ws);
      } else {
        ws.ping();
      }
    });
  }, HEARTBEAT_INTERVAL);
};

// 清理心跳检测函数
const stopHeartbeat = () => {
    if (heartbeatInterval) {
        clearInterval(heartbeatInterval);
    }
};

// 修改startServer函数
async function startServer() {
  try {
    await setUpDb();

    // 检查API端口是否被占用
    const isApiPortAvailable = await checkPort(PORT);
    if (!isApiPortAvailable) {
      console.error(`API端口 ${PORT} 已经被占用，请尝试其他端口。`);
      // 记录到错误日志文件
      logErrorToFile(`API端口 ${PORT} 已经被占用。`);
      process.exit(1);
    }

    // API 服务器监听
    server.listen(PORT, '0.0.0.0', async () => { // 使用 async 回调
      console.log(`API服务器运行在 http://0.0.0.0:${PORT}`);
      console.log(`本地访问: http://localhost:${PORT}`);

      // 在API服务器成功监听后尝试启动WebSocket服务器
      try {
        // 检查WebSocket端口是否被占用（可选，但在生产环境建议保留）
        // 仅在非 production_debug 模式下进行严格检查
        if (process.env.NODE_ENV !== 'production_debug') {
             const isWsPortAvailable = await checkPort(WS_PORT);
             if (!isWsPortAvailable) {
                 console.error(`WebSocket端口 ${WS_PORT} 已经被占用，请尝试其他端口。`);
                 // 记录到错误日志文件
                 logErrorToFile(`WebSocket端口 ${WS_PORT} 已经被占用。`);
                 // 不退出主进程，只记录错误并禁用 WebSocket 服务
                 wss = null; // 设置 wss 为 null 表示 WebSocket 服务未启动
                 console.warn('WebSocket 服务因端口问题未启动。');
                 return; // 停止 WebSocket 启动流程
             }
        } else {
            console.warn(`[WARN] 在 production_debug 模式下跳过 WebSocket 端口 ${WS_PORT} 的可用性严格检查。`);
        }

        wss = new WebSocket.Server({
          port: WS_PORT,
          path: '/ws',
          // CORS verifyClient 逻辑保持不变，确保已包含所有需要的源
          verifyClient: (info, callback) => {
            const origin = info.origin || info.req.headers.origin;
            // 允许来自所有端口的本地连接和来自生产环境IP（带可选端口）的连接，包括HTTPS
            const allowedOrigins = [
                // HTTP 本地开发环境
                'http://localhost:3000',
                'http://localhost:3001',
                'http://localhost:3002',
                'http://localhost:3003',
                'http://localhost:3004',
                'http://localhost:3005',
                'http://localhost:3006',
                'http://localhost:3007',
                'http://localhost:3008',
                'http://127.0.0.1:3000',
                'http://127.0.0.1:3001',
                'http://127.0.0.1:3002',
                'http://127.0.0.1:3003',
                'http://127.0.0.1:3004',
                'http://127.0.0.1:3005',
                'http://127.0.0.1:3006',
                'http://127.0.0.1:3007',
                'http://127.0.0.1:3008',
                // HTTP 生产环境
                'http://************',
                'http://************:3000',
                'http://************:3001',
                'http://************:3002',
                'http://************:3003',
                'http://************:3004',
                'http://************:3005',
                'http://************:3006',
                'http://************:3007',
                'http://************:3008',
                // HTTPS 生产环境
                'https://hkb.life',
                'https://************',
                'https://************:443'
            ];

            if (!origin) {
              // 允许没有origin的请求（例如同源的服务器端请求或某些工具）
              return callback(null, true);
            }

            // 检查请求源是否在允许列表中（精确匹配）
            if (allowedOrigins.includes(origin)) {
              return callback(true);
            }

            // 允许本地IP不同端口的请求 (在非生产环境更宽松)
            if (process.env.NODE_ENV !== 'production') {
              if (origin.match(/^http:\/\/192\.168\.\d+\.\d+:\d+$/) ||
                  origin.match(/^http:\/\/127\.0\.0\.1:\d+$/) ||
                  origin.match(/^http:\/\/localhost:\d+$/)) {
                return callback(true);
              }
            }

            // 在生产环境，更严格检查来源
            if (process.env.NODE_ENV === 'production') {
              // 允许来自生产环境IP和域名的请求（HTTP和HTTPS）
              if (origin.match(/^https?:\/\/14\.103\.135\.3(:\d+)?$/) ||
                  origin.match(/^https:\/\/hkb\.life$/)) {
                   return callback(true);
              }
            }

            // 记录被拒绝的源
            console.error(`WebSocket CORS: 拒绝来自 ${origin} 的连接`);
            callback(false, 403, '不允许的源');
          },
          // 添加心跳检测配置
          clientTracking: true,
          perMessageDeflate: {
            zlibDeflateOptions: {
              chunkSize: 1024,
              memLevel: 7,
              level: 3
            },
            zlibInflateOptions: {
              chunkSize: 10 * 1024
            },
            clientNoContextTakeover: true,
            serverNoContextTakeover: true,
            serverMaxWindowBits: 10,
            concurrencyLimit: 10,
            threshold: 1024
          }
        });

        console.log(`WebSocket服务运行在 ws://localhost:${WS_PORT}/ws`);
        console.log(`请确保防火墙已开放 ${PORT} 和 ${WS_PORT} 端口`);

        // 注册WebSocket事件监听
        wss.on('connection', handleWebSocketConnection);

        // 启动心跳检测
        startHeartbeat();

      } catch (wsError) {
        console.error(`WebSocket服务器启动失败，端口 ${WS_PORT} 可能不可用或其他错误:`, wsError);
        // 记录到错误日志文件
        logErrorToFile(`WebSocket服务器启动失败，端口 ${WS_PORT} 错误: ${wsError.message}`);
        wss = null; // 设置 wss 为 null 表示 WebSocket 服务未启动
        console.warn('WebSocket 服务因启动错误未启动。');
      }
    });
  } catch (error) {
    console.error('启动服务器时出错:', error);
    // 记录到错误日志文件
    logErrorToFile(`启动服务器时出错: ${error.message}`);
    process.exit(1); // API 服务启动失败则退出主进程
  }
}

// 添加一个辅助函数来记录错误到文件
function logErrorToFile(message) {
  const fs = require('fs');
  const path = require('path');
  const logDir = path.join(__dirname, '../logs');

  // 确保日志目录存在
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }

  const logFile = path.join(logDir, 'server-startup-errors.log');
  const timestamp = new Date().toISOString();
  const logEntry = `${timestamp} - ${message}\n`;

  fs.appendFileSync(logFile, logEntry);
}

// 处理进程退出，清理资源
process.on('SIGINT', async () => {
  console.log('正在优雅关闭服务...');
  // 停止心跳检测
  stopHeartbeat();
  // 关闭WebSocket服务器（如果已启动）
  if (wss) {
    try {
      wss.close(() => {
        console.log('WebSocket服务器已关闭');
        // 关闭HTTP服务器
        server.close(() => {
          console.log('HTTP服务器已关闭');
          process.exit(0);
        });
      });
    } catch (error) {
       console.error('关闭WebSocket服务器时出错:', error);
       // 记录到错误日志文件
       logErrorToFile(`关闭WebSocket服务器时出错: ${error.message}`);
       // 如果关闭WS出错，仍然尝试关闭HTTP服务器
       server.close(() => {
         console.log('HTTP服务器已关闭');
         process.exit(0);
       });
    }
  } else {
      // 如果WebSocket服务未启动，直接关闭HTTP服务器
       server.close(() => {
         console.log('HTTP服务器已关闭');
         process.exit(0);
       });
  }
});

// 启动服务器
startServer();
