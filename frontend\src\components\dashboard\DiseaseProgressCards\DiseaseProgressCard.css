/* 时间轴容器 */
.timeline-container {
  position: relative;
  width: 100%;
  height: 60px;
  margin-bottom: 5px;
}

/* 时间轴节点内容容器 */
.timeline-content {
  position: relative;
  display: flex;
  justify-content: space-between;
  width: 100%;
  z-index: 2;
}

/* 时间轴连接线容器 */
.timeline-connectors {
  position: absolute;
  top: 9px; /* 调整使连接线对齐节点中心 */
  left: 10px;
  right: 10px;
  display: flex;
  z-index: 1;
}

/* 节点容器 */
.stage-node-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 5px;
  position: relative;
}

/* 节点样式 */
.stage-node {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  border: 2px solid;
  position: relative;
  transition: all 0.3s ease;
}

/* 活跃节点样式 */
.stage-node.active {
  width: 22px;
  height: 22px;
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.2);
}

/* 节点中心点 */
.node-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: white;
}

/* 连接线样式 */
.stage-connector {
  flex: 1;
  height: 3px;
  margin: 0 2px;
  transition: background-color 0.3s ease;
}

/* 阶段标签 */
.stage-label {
  margin-top: 5px;
  font-size: 0.7rem;
  text-align: center;
  white-space: nowrap;
  max-width: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 响应式调整 */
@media (max-width: 500px) {
  .stage-node {
    width: 16px;
    height: 16px;
  }
  
  .stage-node.active {
    width: 20px;
    height: 20px;
  }
  
  .stage-label {
    font-size: 0.65rem;
    max-width: 35px;
  }
  
  .timeline-connectors {
    top: 8px;
  }
}

/* 动画效果 */
.stage-node.active::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(237,137,54,0.2) 0%, rgba(237,137,54,0) 70%);
  animation: pulse 2s infinite;
  z-index: -1;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.3;
  }
  100% {
    transform: scale(0.95);
    opacity: 0.7;
  }
} 