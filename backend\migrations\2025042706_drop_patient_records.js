exports.up = function (knex) {
  return knex.schema.dropTableIfExists('patient_records');
};

exports.down = function (knex) {
  return knex.schema.createTable('patient_records', (table) => {
    table.string('id').primary();
    table.string('patient_id').notNullable().index();
    table.string('user_id').notNullable().index();
    table.string('record_type').notNullable();
    table.text('content').notNullable();
    table.date('record_date').notNullable();
    table.string('remarks');
    table.timestamp('deleted_at', { useTz: true }).index();
    table.timestamp('created_at', { useTz: true }).notNullable();
    table.timestamp('updated_at', { useTz: true }).notNullable();
  });
}; 