const { v4: uuidv4 } = require('uuid');
const UserAuthorization = require('../models/UserAuthorization');
const User = require('../models/User');
const { transaction } = require('objection');
const knex = require('../src/db').knex;
const Patient = require('../models/Patient');

/**
 * 获取用户的授权列表（作为授权人）
 */
const getAuthorizationsAsAuthorizer = async (req, res) => {
  try {
    console.log('开始获取授权列表（作为授权人）');
    
    if (!req.user || !req.user.id) {
      console.error('用户未认证或ID未找到', req.user);
      return res.status(401).json({
        success: false,
        message: '用户未认证或ID未找到'
      });
    }
    
    const userId = req.user.id;
    console.log('用户ID:', userId);
    
    // 使用原生SQL查询以确保获取所有必要的关联用户数据
    const authorizations = await knex.raw(`
      SELECT 
        ua.*,
        u.id as authorized_user_id,
        u.username as authorized_username,
        u.email as authorized_email,
        u.role as authorized_role,
        u.avatar as authorized_avatar,
        p.id as patient_id,
        p.name as patient_name,
        p.gender as patient_gender,
        p.birth_date as patient_birth_date
      FROM user_authorizations ua
      LEFT JOIN users u ON ua.authorized_id = u.id
      LEFT JOIN patients p ON ua.patient_id = p.id
      WHERE ua.authorizer_id = ?
    `, [userId]);
    
    // 处理查询结果
    const rows = authorizations.rows || authorizations;
    console.log('查询结果:', rows.length ? `找到${rows.length}条记录` : '未找到记录');
    
    // 在这里详细打印第一条记录的所有字段，帮助调试
    if (rows.length > 0) {
      console.log('第一条记录的所有字段:', {
        原始字段: Object.keys(rows[0]),
        字段内容: {
          id: rows[0].id,
          authorizer_id: rows[0].authorizer_id,
          authorized_id: rows[0].authorized_id,
          status: rows[0].status,
          privacy_level: rows[0].privacy_level,
          created_at: rows[0].created_at,
          createdAt: rows[0].createdAt,
          created_by: rows[0].created_by,
          patient_id: rows[0].patient_id,
          patient_name: rows[0].patient_name
        }
      });
    }
    
    // 格式化数据 - getAuthorizationsAsAuthorizer函数
    const formattedData = rows.map(auth => ({
      ...auth,
      // 确保字段名称一致性
      id: auth.id,
      authorizer_id: auth.authorizer_id,
      authorized_id: auth.authorized_id,
      status: auth.status,
      privacy_level: auth.privacy_level || auth.privacyLevel,
      privacyLevel: auth.privacy_level || auth.privacyLevel,
      authorizer_switch: !!auth.authorizer_switch, // 确保是布尔值
      authorized_switch: !!auth.authorized_switch, // 确保是布尔值
      created_at: auth.created_at,
      createdAt: auth.created_at,
      
      // 格式化被授权人信息 (对authorizer来说，看到的是authorized用户)
      authorized: {
        id: auth.authorized_user_id || auth.authorized_id,
        username: auth.authorized_username || '未知用户',
        email: auth.authorized_email,
        role: auth.authorized_role || '未知角色',
        avatar: auth.authorized_avatar
      },
      
      // 格式化患者信息 (如果有)
      patient: auth.patient_id ? {
        id: auth.patient_id,
        name: auth.patient_name || '未知患者',
        gender: auth.patient_gender,
        birthDate: auth.patient_birth_date
      } : null
    }));
    
    console.log('格式化后的数据:', JSON.stringify(formattedData.map(d => ({
      id: d.id,
      status: d.status,
      authorized: {
        username: d.authorized.username,
        role: d.authorized.role
      },
      patient: d.patient ? { name: d.patient.name } : null
    }))));
    
    res.json({
      success: true,
      data: formattedData
    });
  } catch (error) {
    console.error('获取授权列表失败详情:');
    console.error('错误消息:', error.message);
    console.error('错误名称:', error.name);
    console.error('错误堆栈:', error.stack);
    
    if (error.code) {
      console.error('错误代码:', error.code);
    }
    
    if (error.errno) {
      console.error('系统错误号:', error.errno);
    }
    
    if (error.sql) {
      console.error('SQL语句:', error.sql);
    }
    
    res.status(500).json({
      success: false,
      message: '获取授权列表失败',
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};

/**
 * 获取用户的授权列表（作为被授权人）
 */
const getAuthorizationsAsAuthorized = async (req, res) => {
  try {
    console.log('开始获取授权列表（作为被授权人）');
    
    if (!req.user || !req.user.id) {
      console.error('用户未认证或ID未找到', req.user);
      return res.status(401).json({
        success: false,
        message: '用户未认证或ID未找到'
      });
    }
    
    const userId = req.user.id;
    console.log('用户ID:', userId);
    
    // 使用原生SQL查询以确保获取所有必要的关联用户数据
    const authorizations = await knex.raw(`
      SELECT 
        ua.*,
        u.id as authorizer_user_id,
        u.username as authorizer_username,
        u.email as authorizer_email,
        u.role as authorizer_role,
        u.avatar as authorizer_avatar,
        p.id as patient_id,
        p.name as patient_name,
        p.gender as patient_gender,
        p.birth_date as patient_birth_date
      FROM user_authorizations ua
      LEFT JOIN users u ON ua.authorizer_id = u.id
      LEFT JOIN patients p ON ua.patient_id = p.id
      WHERE ua.authorized_id = ?
    `, [userId]);
    
    // 处理查询结果
    const rows = authorizations.rows || authorizations;
    console.log('查询结果:', rows.length ? `找到${rows.length}条记录` : '未找到记录');
    
    // 在这里详细打印第一条记录的所有字段，帮助调试
    if (rows.length > 0) {
      console.log('第一条记录的所有字段:', {
        原始字段: Object.keys(rows[0]),
        字段内容: {
          id: rows[0].id,
          authorizer_id: rows[0].authorizer_id,
          authorized_id: rows[0].authorized_id,
          status: rows[0].status,
          privacy_level: rows[0].privacy_level,
          created_at: rows[0].created_at,
          createdAt: rows[0].createdAt,
          created_by: rows[0].created_by,
          patient_id: rows[0].patient_id,
          patient_name: rows[0].patient_name
        }
      });
    }
    
    // 格式化数据 - getAuthorizationsAsAuthorized函数
    const formattedData = rows.map(auth => ({
      ...auth,
      // 确保字段名称一致性
      id: auth.id,
      authorizer_id: auth.authorizer_id,
      authorized_id: auth.authorized_id,
      status: auth.status,
      privacy_level: auth.privacy_level || auth.privacyLevel,
      privacyLevel: auth.privacy_level || auth.privacyLevel,
      authorizer_switch: !!auth.authorizer_switch, // 确保是布尔值
      authorized_switch: !!auth.authorized_switch, // 确保是布尔值
      created_at: auth.created_at,
      createdAt: auth.created_at,
      
      // 格式化授权人信息 (对authorized来说，看到的是authorizer用户)
      authorizer: {
        id: auth.authorizer_user_id || auth.authorizer_id,
        username: auth.authorizer_username || '未知用户',
        email: auth.authorizer_email,
        role: auth.authorizer_role || '未知角色',
        avatar: auth.authorizer_avatar
      },
      
      // 格式化患者信息 (如果有)
      patient: auth.patient_id ? {
        id: auth.patient_id,
        name: auth.patient_name || '未知患者',
        gender: auth.patient_gender,
        birthDate: auth.patient_birth_date
      } : null
    }));
    
    console.log('格式化后的数据:', JSON.stringify(formattedData.map(d => ({
      id: d.id,
      status: d.status,
      authorizer: {
        username: d.authorizer.username,
        role: d.authorizer.role
      },
      patient: d.patient ? { name: d.patient.name } : null
    }))));
    
    res.json({
      success: true,
      data: formattedData
    });
  } catch (error) {
    console.error('获取授权列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取授权列表失败',
      error: error.message
    });
  }
};

/**
 * 创建授权关系
 */
const createAuthorization = async (req, res) => {
  try {
    // 获取参数，同时支持驼峰命名和下划线命名
    const { 
      authorizerId, authorizer_id, 
      authorizedId, authorized_id,
      patientId, patient_id,
      privacyLevel, privacy_level
    } = req.body;
    
    // 获取最终使用的参数值
    const finalAuthorizerId = authorizer_id || authorizerId;
    const finalAuthorizedId = authorized_id || authorizedId;
    const finalPatientId = patient_id || patientId;
    const finalPrivacyLevel = privacy_level || privacyLevel || 'STANDARD';
    
    // 获取当前用户ID - 改进，更详细的处理
    let creator_id;
    if (req.user && req.user.id) {
      creator_id = req.user.id;
      console.log('[DEBUG] 从req.user获取creator_id:', creator_id);
    } else if (req.userId) {
      creator_id = req.userId;
      console.log('[DEBUG] 从req.userId获取creator_id:', creator_id);
    } else if (req.user_id) {
      creator_id = req.user_id;
      console.log('[DEBUG] 从req.user_id获取creator_id:', creator_id);
    } else {
      // 如果都没有，尝试从授权人ID获取
      creator_id = finalAuthorizerId;
      console.log('[DEBUG] 没有找到用户ID，使用finalAuthorizerId作为creator_id:', creator_id);
    }
    
    console.log('[DEBUG] 创建授权请求参数:', {
      finalAuthorizerId,
      finalAuthorizedId,
      finalPatientId,
      finalPrivacyLevel,
      creator_id
    });
    
    console.log('[DEBUG] 请求用户信息详情:', {
      reqUser: req.user ? '存在' : '不存在',
      reqUserId: req.userId || '不存在',
      reqUser_id: req.user_id || '不存在', 
      reqUserObj: req.user ? JSON.stringify(req.user) : '不存在'
    });
    
    // 参数验证
    if (!finalAuthorizerId || !finalAuthorizedId) {
      return res.status(400).json({
        success: false,
        message: '缺少必要的授权人ID或被授权人ID'
      });
    }
    
    // 防止自授权：检查授权人ID和被授权人ID是否相同
    if (finalAuthorizerId === finalAuthorizedId) {
      return res.status(400).json({
        success: false,
        message: '不能向自己申请授权，授权人和被授权人不能是同一人'
      });
    }
    
    // 声明变量在try块外，使其在后续代码可访问
    let authorizerExists, authorizedExists;
    
    // 验证用户存在
    try {
      authorizerExists = await User.query().findById(finalAuthorizerId);
      authorizedExists = await User.query().findById(finalAuthorizedId);
      
      console.log('[DEBUG] 用户验证结果:', {
        authorizerFound: !!authorizerExists, 
        authorizedFound: !!authorizedExists
      });
      
      if (!authorizerExists || !authorizedExists) {
        return res.status(404).json({
          success: false,
          message: !authorizerExists ? '授权人不存在' : '被授权人不存在'
        });
      }
    } catch (error) {
      console.error('[ERROR] 验证用户存在时出错:', error);
      return res.status(500).json({
        success: false,
        message: '验证用户时发生错误: ' + (error.message || '未知错误')
      });
    }
    
    // 验证角色
    // 检查授权人角色。如果操作者(creator_id)与授权人(finalAuthorizerId)是同一人，
    // 并且此人的角色是 ADMIN 或 SERVICE，但在"普通用户"语境下操作，
    // 则允许其作为"普通用户"发起授权。
    let effectiveAuthorizerRole = authorizerExists.role;
    if (creator_id === finalAuthorizerId && (authorizerExists.role === 'ADMIN' || authorizerExists.role === 'SERVICE')) {
      // 在此场景下，将 ADMIN/SERVICE 视为 USER 进行授权人角色检查
      // 这里我们假设前端已经通过菜单限制了这种行为只发生在"普通用户菜单"下
      // 因此，如果一个 ADMIN/SERVICE 用户自己作为 authorizer 发起，我们认为他是在扮演 USER 角色
      console.log(`[DEBUG] 授权人 ${finalAuthorizerId} (角色: ${authorizerExists.role}) 由自己创建，视为 USER 角色进行操作。`);
      effectiveAuthorizerRole = 'USER'; 
    }

    if (effectiveAuthorizerRole !== 'USER') {
      return res.status(400).json({
        success: false,
        message: `授权人角色必须是普通用户 (实际为: ${authorizerExists.role}, 有效为: ${effectiveAuthorizerRole})`
      });
    }
    
    if (authorizedExists.role !== 'SERVICE' && authorizedExists.role !== 'ADMIN') {
      return res.status(400).json({
        success: false,
        message: '被授权人必须是服务用户或管理员'
      });
    }
    
    // 检查是否已存在针对相同患者的授权关系
    const whereClause = {
      authorizer_id: finalAuthorizerId,
      authorized_id: finalAuthorizedId
    };
    
    // 如果指定了患者ID，则也按患者ID进行排重
    if (finalPatientId) {
      whereClause.patient_id = finalPatientId;
    } else {
      // 如果未指定患者ID，则查找无患者ID的授权
      whereClause.patient_id = null;
    }
    
    const existingAuth = await UserAuthorization.query()
      .where(whereClause)
      .whereNot('status', 'REVOKED')
      .first();
      
    if (existingAuth) {
      return res.status(400).json({
        success: false,
        message: finalPatientId 
          ? `已存在针对该患者的授权关系` 
          : `已存在针对所有患者的授权关系`
      });
    }
    
    // 设置开关状态：创建者的开关打开，对方开关关闭
    let authorizer_switch = false;
    let authorized_switch = false;
    
    // 根据创建者判断开关状态
    if (creator_id === finalAuthorizerId) {
      authorizer_switch = true;
      console.log('[DEBUG] 授权人创建，授权人开关打开');
    } else if (creator_id === finalAuthorizedId) {
      authorized_switch = true;
      console.log('[DEBUG] 被授权人创建，被授权人开关打开');
    } else {
      // 如果创建者既不是授权人也不是被授权人（如管理员），默认两个开关都打开
      authorizer_switch = true;
      authorized_switch = true;
      console.log('[DEBUG] 第三方创建，默认两个开关都打开');
    }
    
    // 根据双方开关状态确定授权状态
    let status;
    if (authorizer_switch === true && authorized_switch === true) {
      status = 'ACTIVE'; // 双方都开，激活
      console.log('[DEBUG] 状态设为ACTIVE (双方都开)');
    } else if (authorizer_switch === false && authorized_switch === false) {
      status = 'REVOKED'; // 双方都关，撤销 (不会出现在初始创建)
      console.log('[DEBUG] 状态设为REVOKED (双方都关)');
    } else if (authorizer_switch === true && authorized_switch === false) {
      status = 'PENDING_AUTHORIZED'; // 授权人开，被授权人关，等待被授权人确认
      console.log('[DEBUG] 状态设为PENDING_AUTHORIZED (等待被授权人确认)');
    } else if (authorizer_switch === false && authorized_switch === true) {
      status = 'PENDING_AUTHORIZER'; // 授权人关，被授权人开，等待授权人确认
      console.log('[DEBUG] 状态设为PENDING_AUTHORIZER (等待授权人确认)');
    } else {
      // 确保状态为PENDING_AUTHORIZER或PENDING_AUTHORIZED其中之一
      status = authorizer_switch ? 'PENDING_AUTHORIZED' : 'PENDING_AUTHORIZER';
      console.log('[DEBUG] 状态设为' + status + ' (基于开关状态)');
    }
    
    // 创建授权记录
    const newAuthorization = await UserAuthorization.query().insert({
      id: uuidv4(),
      authorizer_id: finalAuthorizerId,
      authorized_id: finalAuthorizedId,
      patient_id: finalPatientId,
      privacy_level: finalPrivacyLevel,
      status,
      authorizer_switch,
      authorized_switch,
      created_by: creator_id,
      has_new_notification: true,
      status_changed_at: new Date().toISOString()
    });
    
    res.status(201).json({
      success: true,
      data: newAuthorization,
      message: '授权请求已创建'
    });
  } catch (error) {
    console.error('创建授权关系失败:', error);
    res.status(500).json({
      success: false,
      message: '创建授权关系失败',
      error: error.message
    });
  }
};

/**
 * 更新授权状态
 */
const updateAuthorizationStatus = async (req, res) => {
  try {
    const { id } = req.params;
    let { switch_value } = req.body;
    
    // 确保switch_value是布尔值
    if (typeof switch_value === 'string') {
      switch_value = switch_value.toLowerCase() === 'true';
    } else {
      switch_value = switch_value === true;
    }
    
    const user_id = req.user.id;
    
    // 详细日志，排查403问题
    console.log('[授权状态变更] 请求参数:', { 
      id, 
      switch_value,
      switch_value_type: typeof switch_value,
      原始switch_value: req.body.switch_value,
      原始switch_value_type: typeof req.body.switch_value,
      user_id,
      请求体: req.body,
      请求用户: req.user ? {
        id: req.user.id,
        username: req.user.username,
        role: req.user.role
      } : '无用户信息',
      请求方法: req.method,
      请求路径: req.path,
      请求头: req.headers
    });
    
    // 参数验证
    if (switch_value === undefined) {
      console.log('[授权状态变更] 错误: 缺少开关状态参数');
      return res.status(400).json({
        success: false,
        message: '缺少开关状态参数'
      });
    }
    
    // 用knex原生SQL获取授权记录，避免Objection字段映射问题
    const authorization = await knex('user_authorizations').where({ id }).first();
    if (!authorization) {
      console.log('[授权状态变更] 错误: 授权记录不存在', { id });
      return res.status(404).json({
        success: false,
        message: '授权记录不存在'
      });
    }
    
    // 详细日志，授权记录
    console.log('[授权状态变更] 原始授权记录:', {
      id: authorization.id,
      authorizer_id: authorization.authorizer_id,
      authorized_id: authorization.authorized_id,
      status: authorization.status,
      authorizer_switch: authorization.authorizer_switch,
      authorized_switch: authorization.authorized_switch,
      字段名列表: Object.keys(authorization),
      完整记录: authorization,
      当前用户ID: user_id,
      是否授权人: user_id === authorization.authorizer_id,
      是否被授权人: user_id === authorization.authorized_id
    });
    
    // 检查用户权限
    if (user_id !== authorization.authorizer_id && user_id !== authorization.authorized_id) {
      console.log('[授权状态变更] 403: 当前用户无权限操作此授权', {
        当前用户: user_id,
        授权人: authorization.authorizer_id,
        被授权人: authorization.authorized_id
      });
      return res.status(403).json({
        success: false,
        message: '只有授权人或被授权人可以更新授权状态'
      });
    }
    
    // 如果已经撤销，不允许再激活
    if (authorization.status === 'REVOKED') {
      console.log('[授权状态变更] 错误: 授权已撤销，无法再激活');
      return res.status(400).json({
        success: false,
        message: '授权已撤销，无法再激活'
      });
    }
    
    // 更新当前用户的开关状态
    const updateData = {};
    if (user_id === authorization.authorizer_id) {
      updateData.authorizer_switch = switch_value === true;
      console.log('[授权状态变更] 更新授权人开关状态:', switch_value === true);
    } else if (user_id === authorization.authorized_id) {
      updateData.authorized_switch = switch_value === true;
      console.log('[授权状态变更] 更新被授权人开关状态:', switch_value === true);
    }

    // 手动强制转换为布尔值，确保类型一致
    if (updateData.authorizer_switch !== undefined) {
      updateData.authorizer_switch = updateData.authorizer_switch ? 1 : 0;
    }
    if (updateData.authorized_switch !== undefined) {
      updateData.authorized_switch = updateData.authorized_switch ? 1 : 0;
    }
    
    // 获取最新的开关状态（确保转换为布尔值）
    const newAuthorizerSwitch = updateData.authorizer_switch !== undefined 
      ? Boolean(updateData.authorizer_switch) 
      : Boolean(authorization.authorizer_switch === 1 || authorization.authorizer_switch === true);
      
    const newAuthorizedSwitch = updateData.authorized_switch !== undefined 
      ? Boolean(updateData.authorized_switch) 
      : Boolean(authorization.authorized_switch === 1 || authorization.authorized_switch === true);
    
    console.log('[授权状态变更] 开关状态(转换为布尔值):', {
      新授权人开关: newAuthorizerSwitch,
      新被授权人开关: newAuthorizedSwitch,
      当前授权人开关: Boolean(authorization.authorizer_switch === 1 || authorization.authorizer_switch === true),
      当前被授权人开关: Boolean(authorization.authorized_switch === 1 || authorization.authorized_switch === true),
      原始值: {
        当前授权人开关: authorization.authorizer_switch,
        当前被授权人开关: authorization.authorized_switch,
        类型: {
          authorizer_switch: typeof authorization.authorizer_switch,
          authorized_switch: typeof authorization.authorized_switch
        }
      }
    });
    
    // 根据开关状态自动推导授权状态
    console.log('[授权状态变更] 状态判断前：', {
      newAuthorizerSwitch,
      newAuthorizedSwitch,
      newAuthorizerSwitchType: typeof newAuthorizerSwitch,
      newAuthorizedSwitchType: typeof newAuthorizedSwitch,
      当前状态: authorization.status
    });
    
    // 明确转换为布尔值进行比较
    const bothOn = newAuthorizerSwitch === true && newAuthorizedSwitch === true;
    const bothOff = newAuthorizerSwitch === false && newAuthorizedSwitch === false;
    const authorizerOnOnly = newAuthorizerSwitch === true && newAuthorizedSwitch === false;
    const authorizedOnOnly = newAuthorizerSwitch === false && newAuthorizedSwitch === true;
    
    console.log('[授权状态变更] 布尔判断结果：', {
      双方开启: bothOn,
      双方关闭: bothOff,
      仅授权人开启: authorizerOnOnly,
      仅被授权人开启: authorizedOnOnly
    });
    
    if (bothOn) {
      // 双方都开，激活状态
      updateData.status = 'ACTIVE';
      updateData.activated_at = new Date().toISOString();
      console.log('[授权状态变更] 双方都开，状态变为ACTIVE');
    } else if (bothOff) {
      // 双方都关，撤销状态
      updateData.status = 'REVOKED';
      updateData.revoked_at = new Date().toISOString();
      console.log('[授权状态变更] 双方都关，状态变为REVOKED');
    } else if (authorizerOnOnly) {
      // 授权人开，被授权人关，等待被授权人确认
      updateData.status = 'PENDING_AUTHORIZED';
      console.log('[授权状态变更] 授权人开被授权人关，状态变为PENDING_AUTHORIZED (等待被授权人确认)');
    } else if (authorizedOnOnly) {
      // 授权人关，被授权人开，等待授权人确认
      updateData.status = 'PENDING_AUTHORIZER';
      console.log('[授权状态变更] 授权人关被授权人开，状态变为PENDING_AUTHORIZER (等待授权人确认)');
    }
    
    // 状态变更时间
    updateData.status_changed_at = new Date().toISOString();
    updateData.has_new_notification = true;
    
    console.log('[授权状态变更] 即将更新的数据:', updateData);
    
    try {
      // 用knex原生SQL更新授权记录
      await knex('user_authorizations').where({ id }).update(updateData);
      console.log('[授权状态变更] 数据库更新成功');
    } catch (dbError) {
      console.error('[授权状态变更] 数据库更新失败:', dbError);
      return res.status(500).json({
        success: false,
        message: '更新授权状态失败: ' + (dbError.message || '数据库错误')
      });
    }
    
    try {
      // 查询最新记录返回
      const updatedAuth = await knex('user_authorizations').where({ id }).first();
      
      console.log('[授权状态变更] 更新后的记录:', {
        id: updatedAuth.id,
        authorizer_id: updatedAuth.authorizer_id, 
        authorized_id: updatedAuth.authorized_id,
        status: updatedAuth.status,
        authorizer_switch: updatedAuth.authorizer_switch,
        authorized_switch: updatedAuth.authorized_switch
      });

      // 检查更新后的状态是否一致，如果不一致则再次修正
      const updatedAuthorizerSwitch = Boolean(updatedAuth.authorizer_switch === 1 || updatedAuth.authorizer_switch === true);
      const updatedAuthorizedSwitch = Boolean(updatedAuth.authorized_switch === 1 || updatedAuth.authorized_switch === true);
      
      if (updatedAuthorizerSwitch && updatedAuthorizedSwitch && updatedAuth.status !== 'ACTIVE') {
        console.log('[授权状态变更] 检测到状态不一致，修正为ACTIVE');
        await knex('user_authorizations').where({ id }).update({
          status: 'ACTIVE',
          activated_at: new Date().toISOString()
        });
        
        // 重新获取最新记录
        const finalAuth = await knex('user_authorizations').where({ id }).first();
        console.log('[授权状态变更] 状态修正后:', finalAuth);
        
        res.json({
          success: true,
          data: finalAuth,
          message: '授权状态已更新并修正'
        });
      } else {
        res.json({
          success: true,
          data: updatedAuth,
          message: '授权状态已更新'
        });
      }
    } catch (queryError) {
      console.error('[授权状态变更] 查询更新后记录失败:', queryError);
      return res.status(500).json({
        success: false,
        message: '更新成功但获取更新后记录失败'
      });
    }
  } catch (error) {
    console.error('[授权状态变更] 处理异常:', error);
    res.status(500).json({
      success: false,
      message: '更新授权状态失败',
      error: error.message
    });
  }
};

/**
 * 更新授权隐私级别
 */
const updateAuthorizationPrivacyLevel = async (req, res) => {
  const { id } = req.params;
  const { privacyLevel, privacy_level } = req.body;
  const effectivePrivacyLevel = privacyLevel || privacy_level; // 同时支持驼峰和下划线命名
  const userId = req.user.id;
  
  console.log('[DEBUG] 更新隐私级别请求:', {
    授权ID: id,
    请求的隐私级别: effectivePrivacyLevel,
    原始请求体: req.body,
    当前用户ID: userId,
    请求用户: req.user ? {
      id: req.user.id,
      username: req.user.username,
      role: req.user.role
    } : '无用户信息'
  });
  
  // 参数验证
  if (!effectivePrivacyLevel || !['BASIC', 'STANDARD', 'FULL'].includes(effectivePrivacyLevel)) {
    return res.status(400).json({
      success: false,
      message: '无效的隐私级别'
    });
  }
  
  try {
    // 使用原生knex查询获取授权记录，避免Objection.js可能的字段映射问题
    const authorization = await knex('user_authorizations').where({ id }).first();
    
    if (!authorization) {
      return res.status(404).json({
        success: false,
        message: '授权记录不存在'
      });
    }
    
    console.log('[DEBUG] 授权记录详情:', {
      id: authorization.id,
      authorizer_id: authorization.authorizer_id,
      authorized_id: authorization.authorized_id,
      status: authorization.status,
      当前用户ID: userId,
      授权人ID类型: typeof authorization.authorizer_id,
      当前用户ID类型: typeof userId,
      完整记录字段: Object.keys(authorization)
    });
    
    // 修复类型问题：确保ID比较使用相同类型 - 添加字符串转换
    const authorizerId = String(authorization.authorizer_id || '');
    const authorizedId = String(authorization.authorized_id || '');
    const currentUserId = String(userId);
    
    // 添加权限检查，允许授权人或被授权人或管理员可以更新隐私级别
    if (currentUserId !== authorizerId && 
        currentUserId !== authorizedId && 
        req.user.role !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        message: '只有授权人、被授权人或管理员可以更新授权的隐私级别'
      });
    }
    
    // 使用knex直接更新，避免Objection.js的问题
    await knex('user_authorizations')
      .where({ id })
      .update({
        privacy_level: effectivePrivacyLevel,
        has_new_notification: true
      });
    
    // 再次查询获取更新后的记录
    const updatedAuth = await knex('user_authorizations').where({ id }).first();
    
    res.json({
      success: true,
      data: updatedAuth,
      message: '隐私级别已更新'
    });
  } catch (error) {
    console.error('更新隐私级别失败:', error);
    res.status(500).json({
      success: false,
      message: '更新隐私级别失败',
      error: error.message
    });
  }
};

/**
 * 清除新消息标记
 */
const clearNotification = async (req, res) => {
  const { id } = req.params;
  const userId = req.user.id;
  
  try {
    // 获取授权记录
    const authorization = await UserAuthorization.query().findById(id);
    
    if (!authorization) {
      return res.status(404).json({
        success: false,
        message: '授权记录不存在'
      });
    }
    
    // 检查用户权限
    if (userId !== authorization.authorizer_id && userId !== authorization.authorized_id) {
      return res.status(403).json({
        success: false,
        message: '只有授权人或被授权人可以清除通知'
      });
    }
    
    // 更新通知状态
    const updatedAuth = await UserAuthorization.query()
      .patchAndFetchById(id, {
        has_new_notification: false
      });
    
    res.json({
      success: true,
      data: updatedAuth,
      message: '通知已清除'
    });
  } catch (error) {
    console.error('清除通知失败:', error);
    res.status(500).json({
      success: false,
      message: '清除通知失败',
      error: error.message
    });
  }
};

/**
 * 搜索可授权的服务用户
 */
const searchServiceUsers = async (req, res) => {
  const { query } = req.query;
  
  try {
    const serviceUsers = await User.query()
      .where(builder => {
        builder.where('role', 'SERVICE')
          .orWhere('role', 'ADMIN');
      })
      .where(builder => {
        if (query) {
          // 使用LOWER函数进行不区分大小写的精准匹配
          builder.whereRaw("LOWER(username) = ?", [query.toLowerCase()])
            .orWhereRaw("LOWER(email) = ?", [query.toLowerCase()])
            .orWhereRaw("LOWER(phone_number) = ?", [query.toLowerCase()]);
        }
      })
      .select(['id', 'username', 'email', 'phone_number', 'avatar', 'role']);
    
    res.json({
      success: true,
      data: serviceUsers
    });
  } catch (error) {
    console.error('搜索服务用户失败:', error);
    res.status(500).json({
      success: false,
      message: '搜索服务用户失败',
      error: error.message
    });
  }
};

/**
 * 搜索普通用户
 * 允许服务用户或管理员搜索普通用户，用于发起反向授权请求
 */
const searchNormalUsers = async (req, res) => {
  const { query } = req.query;
  const currentUser = req.user;
  
  try {
    // 验证当前用户是服务用户或管理员
    if (currentUser.role !== 'SERVICE' && currentUser.role !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        message: '只有服务用户或管理员可以搜索普通用户'
      });
    }
    
    // 查询普通用户 - 支持按患者名称、用户名、邮箱和手机号码搜索
    const normalUsersQuery = User.query()
      .where('role', 'USER');  // 只搜索普通用户
    
    if (query) {
      // 构建复杂的搜索查询
      normalUsersQuery.where(builder => {
        // 直接匹配用户表字段
        builder.whereRaw("LOWER(username) LIKE ?", [`%${query.toLowerCase()}%`])
          .orWhereRaw("LOWER(email) LIKE ?", [`%${query.toLowerCase()}%`])
          .orWhereRaw("LOWER(phone_number) LIKE ?", [`%${query.toLowerCase()}%`]);
          
        // 使用子查询匹配患者表中的名称，找出这些患者关联的用户ID
        builder.orWhereIn('id', function() {
          this.select('user_id')
            .from('patients')
            .whereRaw("LOWER(name) LIKE ?", [`%${query.toLowerCase()}%`]);
        });
      });
    }
    
    const normalUsers = await normalUsersQuery.select(['id', 'username', 'email', 'phone_number', 'avatar', 'role']);
    
    res.json({
      success: true,
      data: normalUsers
    });
  } catch (error) {
    console.error('搜索普通用户失败:', error);
    res.status(500).json({
      success: false,
      message: '搜索普通用户失败',
      error: error.message
    });
  }
};

/**
 * 删除已撤销的授权记录
 */
const deleteRevokedAuthorization = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    
    console.log('[DEBUG] 删除授权请求:', {
      授权ID: id,
      当前用户ID: userId,
      请求用户: req.user ? {
        id: req.user.id,
        username: req.user.username,
        role: req.user.role
      } : '无用户信息'
    });
    
    // 获取授权记录
    const authorization = await knex('user_authorizations').where({ id }).first();
    
    if (!authorization) {
      console.log('[DEBUG] 授权记录不存在:', { id });
      return res.status(404).json({
        success: false,
        message: '授权记录不存在'
      });
    }
    
    console.log('[DEBUG] 授权记录详情:', {
      id: authorization.id,
      authorizer_id: authorization.authorizer_id,
      authorized_id: authorization.authorized_id,
      status: authorization.status,
      当前用户ID: userId
    });
    
    // 检查用户权限 - 只有授权人或被授权人可以删除
    if (userId !== authorization.authorizer_id && userId !== authorization.authorized_id) {
      console.log('[DEBUG] 权限错误: 当前用户不是授权人也不是被授权人', {
        当前用户ID: userId,
        授权人ID: authorization.authorizer_id,
        被授权人ID: authorization.authorized_id
      });
      
      return res.status(403).json({
        success: false,
        message: '只有授权人或被授权人可以删除授权记录'
      });
    }
    
    // 检查授权状态 - 只能删除已撤销的授权
    if (authorization.status !== 'REVOKED') {
      console.log('[DEBUG] 状态错误: 只能删除已撤销的授权', {
        当前状态: authorization.status
      });
      
      return res.status(400).json({
        success: false,
        message: '只能删除已撤销的授权记录'
      });
    }
    
    // 执行删除操作
    const deleteCount = await knex('user_authorizations').where({ id }).del();
    
    if (deleteCount === 0) {
      console.log('[DEBUG] 删除失败: 未找到记录或记录已被删除');
      return res.status(404).json({
        success: false,
        message: '授权记录不存在或已被删除'
      });
    }
    
    console.log('[DEBUG] 授权记录已删除:', { id, deleteCount });
    
    res.json({
      success: true,
      message: '授权记录已删除'
    });
  } catch (error) {
    console.error('删除授权记录失败:', error);
    res.status(500).json({
      success: false,
      message: '删除授权记录失败',
      error: error.message
    });
  }
};

module.exports = {
  getAuthorizationsAsAuthorizer,
  getAuthorizationsAsAuthorized,
  createAuthorization,
  updateAuthorizationStatus,
  updateAuthorizationPrivacyLevel,
  clearNotification,
  searchServiceUsers,
  searchNormalUsers,
  deleteRevokedAuthorization
};