import React from 'react';
import { 
  Box, 
  Toolbar, 
  Typography, 
  Divider, 
  List, 
  ListItem, 
  ListItemButton, 
  ListItemIcon, 
  ListItemText,
  AppBar,
  Drawer,
  IconButton,
  CssBaseline,
  Paper,
  Breadcrumbs,
  Link
} from '@mui/material';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { 
  Menu as MenuIcon, 
  SupervisorAccount as UserIcon,
  Assessment as ReportIcon,
  Person as PatientIcon,
  MedicalInformation as DiseaseIcon,
  Article as RecordIcon,
  Security as AuthIcon,
  Settings as SettingsIcon,
  VerifiedUser as AuditIcon,
  ArrowBack as BackIcon
} from '@mui/icons-material';
import { useAuthStore } from '../../store/authStore';

// 抽屉宽度
const drawerWidth = 240;

// 管理菜单项
const menuItems = [
  { 
    text: '用户管理', 
    path: '/admin/users',
    icon: <UserIcon />
  },
  { 
    text: 'AI报告管理', 
    path: '/admin/ai-reports',
    icon: <ReportIcon />
  },
  { 
    text: '患者数据管理', 
    path: '/admin/patients',
    icon: <PatientIcon />
  },
  { 
    text: '病理数据管理', 
    path: '/admin/diseases',
    icon: <DiseaseIcon />
  },
  { 
    text: '就诊记录管理', 
    path: '/admin/records',
    icon: <RecordIcon />
  },
  { 
    text: '授权管理', 
    path: '/admin/authorizations',
    icon: <AuthIcon />
  },
  { 
    text: '系统维护', 
    path: '/admin/maintenance',
    icon: <SettingsIcon />
  },
  { 
    text: '审计日志', 
    path: '/admin/audit-logs',
    icon: <AuditIcon />
  }
];

/**
 * 管理布局组件
 * 为管理页面提供侧边导航和顶部导航栏
 */
const AdminLayout: React.FC = () => {
  const [mobileOpen, setMobileOpen] = React.useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuthStore();
  
  // 检查用户是否是管理员
  React.useEffect(() => {
    if (user && user.role !== 'ADMIN') {
      navigate('/dashboard');
    }
  }, [user, navigate]);
  
  // 处理抽屉开关
  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };
  
  // 处理菜单项点击
  const handleMenuItemClick = (path: string) => {
    navigate(path);
    if (mobileOpen) {
      setMobileOpen(false);
    }
  };
  
  // 处理返回主应用
  const handleBackToMain = () => {
    navigate('/dashboard');
  };
  
  // 获取当前页面标题
  const getPageTitle = (): string => {
    const currentPath = location.pathname;
    const currentItem = menuItems.find(item => currentPath === item.path);
    return currentItem ? currentItem.text : '系统管理';
  };
  
  // 抽屉内容
  const drawer = (
    <div>
      <Toolbar>
        <Typography variant="h6" noWrap component="div">
          系统管理
        </Typography>
      </Toolbar>
      <Divider />
      <List>
        {menuItems.map((item) => (
          <ListItem key={item.text} disablePadding>
            <ListItemButton
              selected={location.pathname === item.path}
              onClick={() => handleMenuItemClick(item.path)}
            >
              <ListItemIcon>
                {item.icon}
              </ListItemIcon>
              <ListItemText primary={item.text} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </div>
  );
  
  return (
    <Box sx={{ display: 'flex' }}>
      <CssBaseline />
      
      {/* 顶部导航栏 */}
      <AppBar
        position="fixed"
        sx={{
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          ml: { sm: `${drawerWidth}px` },
          backgroundColor: 'primary.main'
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { sm: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          
          <IconButton
            color="inherit"
            edge="start"
            onClick={handleBackToMain}
            sx={{ mr: 2 }}
          >
            <BackIcon />
          </IconButton>
          
          <Typography variant="h6" noWrap component="div">
            {getPageTitle()}
          </Typography>
        </Toolbar>
      </AppBar>
      
      {/* 响应式抽屉 */}
      <Box
        component="nav"
        sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
      >
        {/* 移动端抽屉 */}
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true, // 为了更好的移动端性能
          }}
          sx={{
            display: { xs: 'block', sm: 'none' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
        >
          {drawer}
        </Drawer>
        
        {/* 桌面端抽屉 */}
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', sm: 'block' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>
      
      {/* 主内容区域 */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          px: { xs: '10px', sm: 3 },
          py: { xs: 2, sm: 3 },
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          minHeight: '100vh',
          backgroundColor: 'background.default'
        }}
      >
        <Toolbar /> {/* 为顶部导航栏腾出空间 */}
        
        {/* 面包屑导航 */}
        <Paper
          elevation={0}
          sx={{
            mb: 2,
            p: 1,
            backgroundColor: 'transparent'
          }}
        >
          <Breadcrumbs 
            aria-label="breadcrumb"
            sx={{ 
              // 一般来说，Breadcrumbs 的字号会继承，但为了确保，可以设置在这里
              // fontSize: '0.8rem', // 或者让内部组件自行控制
            }}
          >
            <Link
              underline="hover"
              color="inherit"
              onClick={() => navigate('/dashboard')} // 假设主页是 /dashboard
              sx={{ cursor: 'pointer', fontWeight: 'bold', fontSize: '0.8rem' }}
            >
              主页
            </Link>
            <Link
              underline="hover"
              color="inherit"
              onClick={() => navigate('/admin')} // 假设系统管理根是 /admin
              sx={{ cursor: 'pointer', fontWeight: 'bold', fontSize: '0.8rem' }}
            >
              系统管理
            </Link>
            <Typography color="text.primary" sx={{ fontWeight: 'bold', fontSize: '0.8rem' }}>
              {getPageTitle()}
            </Typography>
          </Breadcrumbs>
        </Paper>
        
        {/* 子路由内容 */}
        <Outlet />
      </Box>
    </Box>
  );
};

export default AdminLayout; 