const express = require('express');
const router = express.Router();
const { authenticate } = require('../src/middleware/auth');
const authorizationController = require('../controllers/authorizationController');

// 获取授权列表（作为授权人）
router.get('/as-authorizer', authenticate, authorizationController.getAuthorizationsAsAuthorizer);

// 获取授权列表（作为被授权人）
router.get('/as-authorized', authenticate, authorizationController.getAuthorizationsAsAuthorized);

// 创建授权关系
router.post('/', authenticate, authorizationController.createAuthorization);

// 更新授权状态
router.patch('/:id/status', authenticate, authorizationController.updateAuthorizationStatus);

// 更新授权隐私级别 - 同时提供两种路径，兼容前后端
router.patch('/:id/privacy', authenticate, authorizationController.updateAuthorizationPrivacyLevel);
router.patch('/:id/privacy-level', authenticate, authorizationController.updateAuthorizationPrivacyLevel);

// 清除新消息标记
router.patch('/:id/clear-notification', authenticate, authorizationController.clearNotification);

// 搜索可授权的服务用户
router.get('/service-users', authenticate, authorizationController.searchServiceUsers);

// 搜索可授权的普通用户（反向授权）
router.get('/normal-users', authenticate, authorizationController.searchNormalUsers);

// 删除已撤销的授权记录
router.delete('/:id', authenticate, authorizationController.deleteRevokedAuthorization);

module.exports = router; 