/**
 * CORS测试脚本
 * 用于验证服务器CORS配置是否正确
 */
const axios = require('axios');
const { promisify } = require('util');
const sleep = promisify(setTimeout);

// 要测试的服务器URL
const SERVER_URL = process.argv[2] || 'http://localhost:3001';

// 测试的请求头
const TEST_HEADERS = {
  'Origin': 'http://localhost:3002',
  'Cache-Control': 'no-cache, no-store, must-revalidate',
  'Pragma': 'no-cache',
  'Expires': '0',
  'X-Request-ID': '123456'
};

/**
 * 测试CORS预检请求
 */
async function testPreflightRequest() {
  console.log(`\n==== 测试CORS预检请求 ====`);
  console.log(`服务器: ${SERVER_URL}`);
  console.log(`发送Origin: ${TEST_HEADERS.Origin}`);
  
  try {
    // 使用axios模拟预检请求
    const response = await axios({
      method: 'options',
      url: `${SERVER_URL}/api/health`,
      headers: TEST_HEADERS
    });
    
    console.log(`\n响应状态: ${response.status}`);
    console.log(`Access-Control-Allow-Origin: ${response.headers['access-control-allow-origin']}`);
    console.log(`Access-Control-Allow-Headers: ${response.headers['access-control-allow-headers']}`);
    console.log(`Access-Control-Allow-Methods: ${response.headers['access-control-allow-methods']}`);
    
    // 检查允许的请求头是否包含我们需要的
    const allowedHeaders = (response.headers['access-control-allow-headers'] || '').split(',')
      .map(h => h.trim().toLowerCase());

    const requiredHeaders = ['expires', 'pragma', 'cache-control'];
    const missingHeaders = requiredHeaders.filter(h => !allowedHeaders.includes(h));
    
    if (missingHeaders.length === 0) {
      console.log(`\n✅ 所有必需的请求头都被允许`);
    } else {
      console.log(`\n❌ 以下请求头未被允许: ${missingHeaders.join(', ')}`);
    }
    
    return true;
  } catch (error) {
    console.error(`\n❌ 测试失败:`);
    if (error.response) {
      console.log(`状态码: ${error.response.status}`);
      console.log(`响应数据:`, error.response.data);
      console.log(`响应头:`, error.response.headers);
    } else {
      console.error(error.message);
    }
    return false;
  }
}

/**
 * 测试正常CORS请求
 */
async function testCorsRequest() {
  console.log(`\n==== 测试CORS正常请求 ====`);
  console.log(`服务器: ${SERVER_URL}`);
  console.log(`发送Origin: ${TEST_HEADERS.Origin}`);
  
  try {
    const response = await axios({
      method: 'get',
      url: `${SERVER_URL}/api/health`,
      headers: TEST_HEADERS
    });
    
    console.log(`\n响应状态: ${response.status}`);
    console.log(`Access-Control-Allow-Origin: ${response.headers['access-control-allow-origin']}`);
    
    if (response.headers['access-control-allow-origin']) {
      console.log(`\n✅ 正常请求CORS配置正确`);
    } else {
      console.log(`\n❌ 响应缺少CORS头`);
    }
    
    return true;
  } catch (error) {
    console.error(`\n❌ 测试失败:`);
    if (error.response) {
      console.log(`状态码: ${error.response.status}`);
      console.log(`响应数据:`, error.response.data);
      console.log(`响应头:`, error.response.headers);
    } else {
      console.error(error.message);
    }
    return false;
  }
}

/**
 * 主函数
 */
async function main() {
  console.log(`======================================`);
  console.log(`      CORS配置测试工具`);
  console.log(`======================================`);
  
  // 测试预检请求
  const preflightSuccess = await testPreflightRequest();
  
  // 等待1秒
  await sleep(1000);
  
  // 测试正常请求
  const corsSuccess = await testCorsRequest();
  
  console.log(`\n==== 测试结果摘要 ====`);
  console.log(`预检请求: ${preflightSuccess ? '✅ 通过' : '❌ 失败'}`);
  console.log(`正常请求: ${corsSuccess ? '✅ 通过' : '❌ 失败'}`);
  console.log(`总体结果: ${preflightSuccess && corsSuccess ? '✅ CORS配置正确' : '❌ CORS配置存在问题'}`);
}

// 运行测试
main().catch(console.error); 