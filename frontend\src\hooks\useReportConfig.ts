import { useState, useEffect } from 'react';
import { ReportVisibilityConfig } from '../types/ai-assistant';

export const useReportConfig = () => {
  const [config, setConfig] = useState<ReportVisibilityConfig>({
    userVisibleFields: [],
    serviceVisibleFields: []
  });

  useEffect(() => {
    // 这里可以从后端获取配置
    // 暂时使用默认配置
    setConfig({
      userVisibleFields: [
        'summary',
        'differentialDiagnosis',
        'emergencyGuidance',
        'hospitalRecommendations',
        'treatmentPlan',
        'lifestyleAndMentalHealth'
      ],
      serviceVisibleFields: [
        'dashboardData',
        'riskWarnings',
        'bmiRecommendations',
        'budgetEstimation'
      ]
    });
  }, []);

  return config;
}; 