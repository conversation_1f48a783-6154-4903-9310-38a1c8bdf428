import apiClient from './apiClient';
import { Disease } from '../types/disease';
import { logApiError } from '../utils/apiErrorMonitor';
import { useAuthStore } from '../store/authStore'; // 导入auth存储

// 添加获取所有患者的方法
export const getAllPatients = async () => {
  try {
    // 检查当前用户角色
    const authStore = useAuthStore.getState();
    const userRole = authStore.user?.role;
    const isServiceUser = userRole === 'SERVICE' || userRole === 'ADMIN';

    // 检查当前操作模式
    const operationMode = localStorage.getItem('operation_mode') || 'NORMAL';
    const isServiceContext = operationMode === 'SERVICE' || operationMode === 'ADMIN';

    // 构建API路径
    const path = '/patients';
    const params: any = {};

    // 添加服务上下文参数
    if (isServiceUser && isServiceContext) {
      params.service_context = true;
    }

    // 使用统一的API客户端发送请求
    const response = await apiClient.get(path, { params });
    return response.data;
  } catch (error) {
    console.error('[疾病服务] 获取患者列表失败:', error);
    logApiError(error);
    // 捕获错误但返回空数组，避免整个页面崩溃
    return [];
  }
};

// 获取疾病列表，支持筛选和搜索
export const getDiseases = async (params?: { patientId?: string; stage?: string; name?: string }, isUserContext: boolean = true) => {
  try {
    // 使用API配置中心获取路径
    // 如果是用户上下文，使用普通用户接口；否则使用管理接口
    // 注意：apiClient.ts已经配置了基础URL为http://xxx:3001/api，但我们仍然需要添加/api前缀
    const path = isUserContext ? '/api/diseases' : '/api/diseases';

    // 转换参数为后端API需要的格式（蛇形命名法）
    const apiParams: any = {};
    if (params) {
      if (params.patientId) apiParams.patient_id = params.patientId;
      if (params.stage) apiParams.stage = params.stage;
      if (params.name) apiParams.name = params.name;
    }

    // 在用户上下文模式下，添加参数要求只显示当前用户的数据
    // 确保即使管理员在普通用户模式下也只能看到自己的病理
    if (isUserContext) {
      apiParams.only_current_user = true;
    } else {
      // 不在用户上下文模式下，添加服务上下文参数
      apiParams.service_context = true;
      // 添加参数，表示只显示授权数据，不显示当前用户的数据
      apiParams.only_authorized_data = true;

      // 尝试获取当前的授权ID
      try {
        const serviceContextStr = localStorage.getItem('serviceUserContext');
        if (serviceContextStr) {
          const serviceContext = JSON.parse(serviceContextStr);
          if (serviceContext && serviceContext.authorizationId) {
            apiParams.authorization_id = serviceContext.authorizationId;
          }
        }

        // 如果没有从serviceUserContext获取到，尝试从URL获取
        if (!apiParams.authorization_id) {
          const url = new URL(window.location.href);
          const authId = url.searchParams.get('authId');
          if (authId) {
            apiParams.authorization_id = authId;
          }
        }
      } catch (error) {
        console.error('[疾病服务] 获取授权ID失败:', error);
      }
    }

    // 使用统一的API客户端发送请求
    const response = await apiClient.get(path, { params: apiParams });

    // 确保返回的数据是数组
    const responseData = response.data;
    if (!Array.isArray(responseData)) {
      console.warn('[疾病服务] API返回的数据不是数组，返回空数组代替:', responseData);
      return [];
    }

    return responseData;
  } catch (error) {
    console.error('[疾病服务] 获取疾病列表失败:', error);
    logApiError(error);
    throw error;
  }
};

// 获取特定疾病详情
export const getDisease = async (id: string, options?: { includeDeleted?: boolean }) => {
  try {
    // 检查当前用户角色
    const authStore = useAuthStore.getState();
    const userRole = authStore.user?.role;
    const isServiceUser = userRole === 'SERVICE' || userRole === 'ADMIN';

    // 检查当前操作模式
    const operationMode = localStorage.getItem('operation_mode') || 'NORMAL';
    const isServiceContext = operationMode === 'SERVICE' || operationMode === 'ADMIN';

    // 使用API配置中心获取路径
    const path = `/api/diseases/${id}`;

    // 配置请求参数
    const params: any = {};

    // 仅当用户是服务用户且当前在服务上下文模式时，才添加service_context参数
    if (isServiceUser && isServiceContext) {
      params.service_context = true;
    }

    // 是否包含已删除的疾病
    if (options?.includeDeleted) {
      params.include_deleted = true;
    }

    // 使用统一的API客户端发送请求
    const response = await apiClient.get(path, { params });
    return response.data;
  } catch (error) {
    console.error(`[疾病服务] 获取疾病(${id})详情失败:`, error);
    logApiError(error);
    throw error;
  }
};

// 创建新疾病记录
export const createDisease = async (disease: Omit<Disease, 'id' | 'createdAt' | 'updatedAt'>) => {
  try {
    // 检查当前用户角色
    const authStore = useAuthStore.getState();
    const userRole = authStore.user?.role;
    const isServiceUser = userRole === 'SERVICE' || userRole === 'ADMIN';

    // 检查当前操作模式
    const operationMode = localStorage.getItem('operation_mode') || 'NORMAL';
    const isServiceContext = operationMode === 'SERVICE' || operationMode === 'ADMIN';

    // 获取授权级别
    let privacyLevel = null;
    try {
      const serviceContextStr = localStorage.getItem('serviceUserContext');
      if (serviceContextStr) {
        const serviceContext = JSON.parse(serviceContextStr);
        privacyLevel = serviceContext?.privacyLevel;
      }
    } catch (e) {
      console.error('[疾病服务] 获取授权级别失败:', e);
    }

    // 基础授权用户不能创建病理 - 前端安全检查
    if (privacyLevel === 'BASIC') {
      console.error('[疾病服务] 基础授权用户尝试创建病理，操作被拒绝');
      throw new Error('基础授权用户无法创建病理记录');
    }

    // 转换字段名以符合后端API要求
    const requestData: any = {
      name: disease.name,
      stage: disease.stage,
      description: disease.description,
      treatment: disease.treatment,
      status: disease.status
    };

    // 转换特定字段
    if (disease.patientId !== undefined) {
      requestData.patient_id = disease.patientId;
    }

    if (disease.diagnosisDate !== undefined) {
      requestData.diagnosis_date = disease.diagnosisDate;
    }

    if (disease.isPrivate !== undefined) {
      requestData.is_private = disease.isPrivate;
    }

    // 使用API配置中心获取路径
    const path = '/api/diseases';

    // 获取服务上下文参数
    const params: any = {};

    // 仅当用户是服务用户且当前在服务上下文模式时，才添加service_context参数
    if (isServiceUser && isServiceContext) {
      params.service_context = true;

      // 尝试多种方法获取授权ID，增强健壮性
      try {
        // 1. 从localStorage获取
        let authorizationId = null;

        // 尝试从serviceUserContext获取
        const serviceContextStr = localStorage.getItem('serviceUserContext');
        if (serviceContextStr) {
          try {
            const serviceContext = JSON.parse(serviceContextStr);
            if (serviceContext && serviceContext.authorizationId) {
              authorizationId = serviceContext.authorizationId;
            }
          } catch (e) {
            console.error('[疾病服务] 解析serviceUserContext失败:', e);
          }
        }

        // 如果上面的方法没有获取到，尝试从URL参数获取
        if (!authorizationId) {
          const url = new URL(window.location.href);
          authorizationId = url.searchParams.get('authId');
        }

        // 如果存在授权ID，添加到请求体中而不是URL参数
        if (authorizationId) {
          // 将授权ID添加到请求数据（而非URL参数）
          requestData.authorization_id = authorizationId;
          // 确保在请求体中标记这是服务上下文请求
          requestData.service_context = true;
        }
      } catch (error) {
        console.error('[疾病服务] 获取服务上下文信息失败:', error);
      }
    }

    // 增加调试日志，打印完整请求信息
    console.log('[疾病服务] 创建病理请求详情:', {
      path: path,
      requestData: requestData,
      params: params,
      authContext: {
        isServiceUser,
        isServiceContext,
        operationMode,
        userRole
      }
    });

    // 使用统一的API客户端发送请求
    const response = await apiClient.post(path, requestData, { params });
    return response.data;
  } catch (error) {
    console.error('[疾病服务] 创建疾病记录失败:', error);
    logApiError(error);
    throw error;
  }
};

// 更新疾病记录
export const updateDisease = async (id: string, disease: Partial<Disease>) => {
  try {
    // 检查当前用户角色
    const authStore = useAuthStore.getState();
    const userRole = authStore.user?.role;
    const isServiceUser = userRole === 'SERVICE' || userRole === 'ADMIN';

    // 检查当前操作模式
    const operationMode = localStorage.getItem('operation_mode') || 'NORMAL';
    const isServiceContext = operationMode === 'SERVICE' || operationMode === 'ADMIN';

    // 转换字段名以符合后端API要求
    const requestData: any = {};

    // 只复制需要更新的字段，避免发送整个对象
    if (disease.name !== undefined) requestData.name = disease.name;
    if (disease.stage !== undefined) requestData.stage = disease.stage;
    if (disease.description !== undefined) requestData.description = disease.description;
    if (disease.treatment !== undefined) requestData.treatment = disease.treatment;
    if (disease.status !== undefined) requestData.status = disease.status;

    // 特殊字段转换为下划线命名
    if (disease.isPrivate !== undefined) {
      requestData.is_private = disease.isPrivate;
    }

    if (disease.diagnosisDate !== undefined) {
      requestData.diagnosis_date = disease.diagnosisDate;
    }

    if (disease.patientId !== undefined) {
      requestData.patient_id = disease.patientId;
    }

    // 使用API配置中心获取路径
    const path = `/api/diseases/${id}`;

    // 仅当用户是服务用户且当前在服务上下文模式时，才添加service_context参数
    const params: any = {};
    if (isServiceUser && isServiceContext) {
      params.service_context = true;
      // 也添加到请求体中，确保后端可以从请求体读取
      requestData.service_context = true;

      // 尝试多种方法获取授权ID，增强健壮性
      try {
        // 1. 从localStorage获取
        let authorizationId = null;

        // 尝试从serviceUserContext获取
        const serviceContextStr = localStorage.getItem('serviceUserContext');
        if (serviceContextStr) {
          try {
            const serviceContext = JSON.parse(serviceContextStr);
            if (serviceContext && serviceContext.authorizationId) {
              authorizationId = serviceContext.authorizationId;
            }
          } catch (e) {
            console.error('[疾病服务] 解析serviceUserContext失败:', e);
          }
        }

        // 如果上面的方法没有获取到，尝试从URL参数获取
        if (!authorizationId) {
          const url = new URL(window.location.href);
          authorizationId = url.searchParams.get('authId');
        }

        // 如果存在授权ID，添加到请求体中
        if (authorizationId) {
          requestData.authorization_id = authorizationId;
        }
      } catch (error) {
        console.error('[疾病服务] 获取服务上下文信息失败:', error);
      }
    }

    // 增加调试日志
    console.log('[疾病服务] 更新病理请求详情:', {
      id: id,
      path: path,
      requestData: requestData,
      params: params,
      authContext: {
        isServiceUser,
        isServiceContext,
        operationMode,
        userRole
      }
    });

    // 使用统一的API客户端发送请求
    const response = await apiClient.put(path, requestData, { params });
    return response.data;
  } catch (error) {
    console.error('[疾病服务] 疾病记录更新失败:', error);
    logApiError(error);
    throw error;
  }
};

// 删除疾病记录
export const deleteDisease = async (id: string) => {
  try {
    // 检查当前用户角色
    const authStore = useAuthStore.getState();
    const userRole = authStore.user?.role;
    const isServiceUser = userRole === 'SERVICE' || userRole === 'ADMIN';

    // 检查当前操作模式
    const operationMode = localStorage.getItem('operation_mode') || 'NORMAL';
    const isServiceContext = operationMode === 'SERVICE' || operationMode === 'ADMIN';

    // 使用API配置中心获取路径
    const path = `/api/diseases/${id}`;

    // 仅当用户是服务用户且当前在服务上下文模式时，才添加service_context参数
    const params: any = {};
    if (isServiceUser && isServiceContext) {
      params.service_context = true;

      // 尝试多种方法获取授权ID，增强健壮性
      try {
        // 1. 从localStorage获取授权ID
        let authorizationId = null;

        // 尝试从serviceUserContext获取
        const serviceContextStr = localStorage.getItem('serviceUserContext');
        if (serviceContextStr) {
          try {
            const serviceContext = JSON.parse(serviceContextStr);
            if (serviceContext && serviceContext.authorizationId) {
              authorizationId = serviceContext.authorizationId;
            }
          } catch (e) {
            console.error('[疾病服务] 解析serviceUserContext失败:', e);
          }
        }

        // 如果上面的方法没有获取到，尝试从URL参数获取
        if (!authorizationId) {
          const url = new URL(window.location.href);
          authorizationId = url.searchParams.get('authId');
        }

        // 如果存在授权ID，添加到请求参数中
        if (authorizationId) {
          params.authorization_id = authorizationId;
        }
      } catch (error) {
        console.error('[疾病服务] 获取服务上下文信息失败:', error);
      }
    }

    // 增加调试日志
    console.log('[疾病服务] 删除病理请求详情:', {
      id: id,
      path: path,
      params: params,
      authContext: {
        isServiceUser,
        isServiceContext,
        operationMode,
        userRole
      }
    });

    // 使用统一的API客户端发送请求
    const response = await apiClient.delete(path, { params });
    return response.data;
  } catch (error) {
    console.error(`[疾病服务] 删除疾病记录(${id})失败:`, error);
    logApiError(error);
    throw error;
  }
};

// 获取疾病阶段列表
export const getDiseaseStages = async () => {
  try {
    // 检查当前用户角色
    const authStore = useAuthStore.getState();
    const userRole = authStore.user?.role;
    const isServiceUser = userRole === 'SERVICE' || userRole === 'ADMIN';

    // 检查当前操作模式
    const operationMode = localStorage.getItem('operation_mode') || 'NORMAL';
    const isServiceContext = operationMode === 'SERVICE' || operationMode === 'ADMIN';

    // 构建API路径 - 注意这是扩展的路径，API配置中没有直接定义
    const path = `/api/diseases/stages`;

    // 仅当用户是服务用户且当前在服务上下文模式时，才添加service_context参数
    const params: any = {};
    if (isServiceUser && isServiceContext) {
      params.service_context = true;
    }

    // 使用统一的API客户端发送请求
    const response = await apiClient.get(path, { params });
    return response.data;
  } catch (error) {
    console.error('[疾病服务] 获取疾病阶段列表失败:', error);
    logApiError(error);
    throw error;
  }
};

// 保存已知无权访问的患者ID
const noAccessPatientIds = new Set<string>();

// 使用缓存减少重复请求
const diseaseCache: Record<string, {data: any[], timestamp: number}> = {};

// 获取特定患者的所有疾病
export const getPatientDiseases = async (patientId: string, authorizationId?: string) => {
  try {
    // 0. 如果patientId无效，直接返回空数组
    if (!patientId || patientId === 'undefined' || patientId === 'null') {
      console.warn('[疾病服务] 请求了无效的患者ID:', patientId);
      return [];
    }

    // 1. 检查是否为已知无权访问的患者 - 无权访问患者缓存10分钟
    if (noAccessPatientIds.has(patientId)) {
      console.log(`[疾病服务] 跳过无权访问的患者(ID: ${patientId})的疾病请求`);
      return [];
    }

    // 2. 检查缓存，如果缓存存在且未过期（60秒内）则直接返回
    const cacheKey = `diseases_${patientId}_${authorizationId || ''}`;
    const now = Date.now();
    const cachedData = diseaseCache[cacheKey];
    if (cachedData && (now - cachedData.timestamp < 60000)) {
      // 使用缓存数据
      return cachedData.data;
    }

    // 检查当前用户角色
    const authStore = useAuthStore.getState();
    const userRole = authStore.user?.role;
    const isServiceUser = userRole === 'SERVICE' || userRole === 'ADMIN';

    // 检查当前操作模式
    const operationMode = localStorage.getItem('operation_mode') || 'NORMAL';
    const isServiceContext = operationMode === 'SERVICE' || operationMode === 'ADMIN';

    let path;
    let params: any = {};

    // 仅当用户是服务用户且当前在服务上下文模式时，才添加service_context参数
    if (isServiceUser && isServiceContext) {
      params.service_context = true;
    }

    // 根据用户角色、操作模式和授权参数选择不同的API路径
    if (isServiceUser && isServiceContext && authorizationId) {
      // 服务用户在服务上下文中通过授权关系访问患者疾病
      path = `/api/service-records/authorized/${authorizationId}/diseases`;
    } else {
      // 普通用户或在普通上下文中的服务用户直接访问自己的患者疾病
      path = `/api/patients/${patientId}/diseases`;
    }

    // 使用统一的API客户端发送请求，传递参数
    const response = await apiClient.get(path, { params });

    // 成功获取到数据，更新缓存
    const responseData = response.data;
    diseaseCache[cacheKey] = {
      data: responseData,
      timestamp: now
    };

    return responseData;
  } catch (error: any) {
    console.error(`[疾病服务] 获取患者(${patientId})疾病数据失败:`, error);
    logApiError(error);

    // 如果是403错误（权限不足），记录到无权访问列表
    if (error.response?.status === 403) {
      console.log(`[疾病服务] 添加患者(ID: ${patientId})到无权访问列表`);
      noAccessPatientIds.add(patientId);

      // 添加定时清理，10分钟后从无权访问列表移除
      setTimeout(() => {
        noAccessPatientIds.delete(patientId);
        console.log(`[疾病服务] 从无权访问列表中移除患者(ID: ${patientId})`);
      }, 600000); // 10分钟
    }

    // 返回空数组作为默认值，而不是让错误传播
    return [];
  }
};