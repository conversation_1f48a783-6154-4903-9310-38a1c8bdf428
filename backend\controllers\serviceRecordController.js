const { v4: uuidv4 } = require('uuid');
const { transaction } = require('objection');
const Record = require('../models/Record');
const ServiceRecord = require('../models/ServiceRecord');
const UserAuthorization = require('../models/UserAuthorization');
const User = require('../models/User');
const Patient = require('../models/Patient');
const Disease = require('../models/Disease');

/**
 * 获取服务用户创建的服务记录
 */
const getServiceRecords = async (req, res) => {
  const userId = req.user.id;
  const { patientId, diseaseId, authorizationId } = req.query;

  try {
    // 构建基本查询 - 修正字段名称，使用下划线形式
    const query = ServiceRecord.query()
      .where('service_user_id', userId)
      .withGraphFetched('[record.[patient, disease, attachments], authorization.authorizer]');

    // 添加过滤条件
    if (patientId) {
      query.joinRelated('record').where('record.patient_id', patientId);
    }

    if (diseaseId) {
      query.joinRelated('record').where('record.disease_id', diseaseId);
    }

    if (authorizationId) {
      query.where('authorization_id', authorizationId);
    }

    // 查询并按时间倒序排序
    const serviceRecords = await query
      .orderBy('created_at', 'desc');

    res.json({
      success: true,
      data: serviceRecords
    });
  } catch (error) {
    console.error('获取服务记录失败:', error);
    res.status(500).json({
      success: false,
      message: '获取服务记录失败',
      error: error.message
    });
  }
};

/**
 * 获取授权用户的记录
 */
const getAuthorizedUserRecords = async (req, res) => {
  try {
    const userId = req.user.id;
    const { authorizationId } = req.params;

    console.log(`获取授权用户记录 - 用户ID: ${userId}, 授权ID: ${authorizationId}`);

    // 验证授权关系 - 修正字段名称，使用下划线形式
    const authorization = await UserAuthorization.query()
      .findById(authorizationId)
      .where('authorized_id', userId)
      .where('status', 'ACTIVE')
      .first();

    if (!authorization) {
      console.log(`授权关系不存在或不活跃 - 授权ID: ${authorizationId}, 用户ID: ${userId}`);
      return res.status(403).json({
        success: false,
        message: '没有有效的授权关系'
      });
    }

    console.log('找到授权关系:', JSON.stringify(authorization));

    // 检查授权级别
    const privacyLevel = authorization.privacy_level || authorization.privacyLevel;

    // 基础授权只能查看授权信息，不能查看患者列表
    if (privacyLevel === 'BASIC') {
      console.log(`基础授权级别无法访问患者列表 - 授权ID: ${authorizationId}, 级别: ${privacyLevel}`);
      return res.status(403).json({
        success: false,
        message: '基础授权级别不允许访问患者列表'
      });
    }

    // 检查是否有授权人ID
    const authorizerId = authorization.authorizer_id || authorization.authorizerId;

    if (!authorizerId) {
      console.error('授权关系缺少授权人ID - 授权ID:', authorizationId);
      return res.status(500).json({
        success: false,
        message: '授权关系数据不完整，缺少授权人ID'
      });
    }

    // 判断是否有患者限制 - 修正字段名称，使用下划线形式
    let userRecordsQuery = Record.query()
      .skipUndefined() // 跳过undefined的值
      .where('user_id', authorizerId)
      .where('is_deleted', false); // 不显示已删除的记录

    // 如果授权限定了特定患者
    const patientId = authorization.patient_id || authorization.patientId;
    if (patientId) {
      console.log(`授权限定特定患者 - 患者ID: ${patientId}`);
      userRecordsQuery = userRecordsQuery.where('patient_id', patientId);
    }

    // 无论是标准授权还是完整授权，都需要遵从隐私设置
    userRecordsQuery = userRecordsQuery.where('is_private', false);

    // 查询用户记录
    const records = await userRecordsQuery
      .withGraphFetched('[patient, attachments, disease]')
      .orderBy('created_at', 'desc');

    console.log(`找到记录数量: ${records.length}`);

    res.json({
      success: true,
      data: records
    });
  } catch (error) {
    console.error('获取授权用户记录失败:', error);
    res.status(500).json({
      success: false,
      message: '获取授权用户记录失败',
      error: error.message
    });
  }
};

/**
 * 创建服务记录
 */
const createServiceRecord = async (req, res) => {
  // 获取记录字段
  const {
    authorizationId,
    patientId,
    diseaseId,
    title,
    content,
    tags,
    recordType,
    primaryType,
    recordDate,
    stagePhase,
    stageNode,
    stageTags,
    isImportant,
    customTags,
    severity
  } = req.body;

  // 记录完整请求体以便调试
  console.log(`[serviceRecordController] 创建服务记录请求体:`, JSON.stringify(req.body, null, 2));

  const serviceUserId = req.user.id;
  console.log(`[serviceRecordController] 当前用户ID: ${serviceUserId}`);

  try {
    // 验证必要字段
    if (!authorizationId) {
      console.log('[serviceRecordController] 缺少授权ID');
      return res.status(400).json({
        success: false,
        message: '缺少必要参数: authorizationId'
      });
    }

    if (!title) {
      console.log('[serviceRecordController] 缺少标题');
      return res.status(400).json({
        success: false,
        message: '缺少必要参数: title'
      });
    }

    console.log(`[serviceRecordController] 创建服务记录 - 用户ID: ${serviceUserId}, 授权ID: ${authorizationId}, 患者ID: ${patientId}, 疾病ID: ${diseaseId}`);

    // 验证授权关系 - 修正字段名称，使用下划线形式
    console.log(`[serviceRecordController] 查询授权关系 - 授权ID: ${authorizationId}, 用户ID: ${serviceUserId}`);

    // 检查授权ID是否存在
    if (!authorizationId) {
      console.log(`[serviceRecordController] 授权ID不存在`);
      return res.status(400).json({
        success: false,
        message: '无法验证授权关系：授权ID不存在'
      });
    }

    // 使用where查询而不是findById
    const authorization = await UserAuthorization.query()
      .where('id', authorizationId)
      .where('authorized_id', serviceUserId)
      .where('status', 'ACTIVE')
      .first(); // 确保添加first()获取单个结果

    if (!authorization) {
      console.log(`[serviceRecordController] 授权关系不存在或不活跃 - 授权ID: ${authorizationId}, 用户ID: ${serviceUserId}`);
      return res.status(403).json({
        success: false,
        message: '没有有效的授权关系'
      });
    }

    console.log(`[serviceRecordController] 找到授权关系:`, JSON.stringify(authorization, null, 2));

    // 验证授权人ID字段存在
    if (!authorization.authorizer_id && !authorization.authorizerId) {
      console.error(`[serviceRecordController] 授权关系缺少授权人ID字段 - 授权ID: ${authorizationId}`);
      return res.status(500).json({
        success: false,
        message: '授权关系数据不完整，缺少授权人ID'
      });
    }

    // 统一使用authorizer_id字段
    const authorizerId = authorization.authorizer_id || authorization.authorizerId;
    console.log(`[serviceRecordController] 授权人ID: ${authorizerId}`);

    // 验证权限级别 - 修正字段名称，使用下划线形式
    const privacyLevel = authorization.privacy_level || authorization.privacyLevel;
    console.log(`[serviceRecordController] 隐私级别: ${privacyLevel}`);

    if (privacyLevel === 'BASIC') {
      console.log(`[serviceRecordController] 授权级别不足 - 授权ID: ${authorizationId}, 级别: ${privacyLevel}`);
      return res.status(403).json({
        success: false,
        message: '基础授权级别不允许创建记录'
      });
    }

    // 标准授权和完整授权都可以创建记录
    console.log(`[serviceRecordController] 授权级别符合要求 - 级别: ${privacyLevel}`);

    // 验证患者ID
    let patientToUse = patientId;

    // 获取授权中的患者ID (兼容两种字段命名)
    const authPatientId = authorization.patient_id || authorization.patientId;
    console.log(`[serviceRecordController] 授权关系中的患者ID: ${authPatientId}`);

    // 如果授权关系指定了患者，则必须使用该患者
    if (authPatientId && patientId && patientId !== authPatientId) {
      console.log(`[serviceRecordController] 患者不匹配 - 请求患者ID: ${patientId}, 授权患者ID: ${authPatientId}`);
      return res.status(400).json({
        success: false,
        message: '无法为未授权的患者创建记录'
      });
    }

    // 如果未指定患者，则使用授权关系中的患者
    if (!patientToUse && authPatientId) {
      patientToUse = authPatientId;
      console.log(`[serviceRecordController] 使用授权关系中的患者ID: ${patientToUse}`);
    }

    // 如果仍未指定患者，则获取用户的主要患者
    if (!patientToUse) {
      console.log(`[serviceRecordController] 尝试获取主要患者 - 授权人ID: ${authorizerId}`);
      const primaryPatient = await Patient.query()
        .where('user_id', authorizerId)
        .where('is_primary', true)
        .first();

      if (primaryPatient) {
        patientToUse = primaryPatient.id;
        console.log(`[serviceRecordController] 找到主要患者 - 患者ID: ${patientToUse}`);
      } else {
        console.log(`[serviceRecordController] 未找到主要患者 - 授权人ID: ${authorizerId}`);
        return res.status(400).json({
          success: false,
          message: '未指定患者，且用户没有主要患者'
        });
      }
    }

    try {
      // 开始记录事务信息
      console.log(`[serviceRecordController] 开始事务 - 准备创建记录和服务记录`);
      console.log(`[serviceRecordController] 记录参数: title=${title}, patientId=${patientToUse}, diseaseId=${diseaseId}`);

      // 执行事务
      const result = await transaction(Record.knex(), async (trx) => {
        // 创建记录 - 修正字段名称，使用下划线形式
        console.log(`[serviceRecordController] 创建记录 - 用户ID=${authorizerId}, 患者ID=${patientToUse}, 疾病ID=${diseaseId}`);
        console.log(`[serviceRecordController] 病程数据 - 阶段=${stagePhase}, 节点=${stageNode}, 类型:`, {
          阶段类型: typeof stagePhase,
          节点类型: typeof stageNode
        });

        const record = await Record.query(trx).insert({
          id: uuidv4(),
          user_id: authorizerId,
          patient_id: patientToUse,
          disease_id: diseaseId,
          title,
          content,
          tags: tags || [],
          record_type: recordType || 'OTHER',
          primary_type: primaryType || 'OTHER',
          record_date: recordDate || new Date().toISOString(),
          is_private: false, // 服务记录默认不是隐私的
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          created_by: serviceUserId, // 添加创建者ID，使用当前服务用户的ID
          stage_tags: stageTags,     // 阶段标签
          custom_tags: customTags,   // 客户自定义标签
          stage_node: stageNode,     // 阶段节点
          stage_phase: stagePhase,   // 阶段
          is_important: isImportant === true || isImportant === 'true',
          severity: severity
        });

        console.log(`[serviceRecordController] 记录创建成功 - 记录ID: ${record.id}`);

        // 创建服务记录关联 - 修正字段名称，使用下划线形式
        console.log(`[serviceRecordController] 创建服务记录关联 - 记录ID=${record.id}, 授权ID=${authorizationId}`);
        const serviceRecord = await ServiceRecord.query(trx).insert({
          id: uuidv4(),
          recordId: record.id,
          authorizationId: authorizationId,
          serviceUserId: serviceUserId,
          ownerUserId: authorizerId,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        });

        console.log(`[serviceRecordController] 服务记录关联创建成功 - ID: ${serviceRecord.id}`);

        return { record, serviceRecord };
      });

      console.log(`[serviceRecordController] 事务完成 - 成功创建服务记录 - 记录ID: ${result.record.id}`);

      res.status(201).json({
        success: true,
        data: {
          id: result.record.id,
          record: result.record,
          serviceRecord: result.serviceRecord
        },
        message: '服务记录已创建'
      });
    } catch (transactionError) {
      console.error('[serviceRecordController] 执行事务失败:', transactionError);
      console.error('[serviceRecordController] 错误详情:', transactionError.stack);

      // 尝试获取更详细的错误信息
      let errorDetail = transactionError.message;
      if (transactionError.errno) {
        errorDetail += ` (errno: ${transactionError.errno})`;
      }
      if (transactionError.code) {
        errorDetail += ` (code: ${transactionError.code})`;
      }
      if (transactionError.sql) {
        errorDetail += ` (SQL: ${transactionError.sql})`;
      }

      return res.status(500).json({
        success: false,
        message: '创建服务记录事务失败',
        error: errorDetail
      });
    }
  } catch (error) {
    console.error('[serviceRecordController] 创建服务记录失败:', error);
    console.error('[serviceRecordController] 错误堆栈:', error.stack);

    // 尝试获取更详细的错误信息
    let errorDetail = error.message;
    if (error.name) {
      errorDetail = `${error.name}: ${errorDetail}`;
    }

    res.status(500).json({
      success: false,
      message: '创建服务记录失败',
      error: errorDetail,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};

/**
 * 更新服务记录
 */
const updateServiceRecord = async (req, res) => {
  const { id } = req.params; // 使用id参数，与路由保持一致
  const {
    title,
    content,
    tags,
    recordType,
    primaryType,
    recordDate,
    stagePhase,
    stageNode,
    stageTags,
    isImportant,
    customTags,
    severity
  } = req.body;

  const serviceUserId = req.user.id;

  try {
    // 查找对应的服务记录 - 修正字段名称，使用下划线形式
    const serviceRecord = await ServiceRecord.query()
      .where('record_id', id) // 使用id参数，与路由保持一致
      .where('service_user_id', serviceUserId)
      .first();

    if (!serviceRecord) {
      return res.status(404).json({
        success: false,
        message: '服务记录不存在或无权访问'
      });
    }

    // 获取授权ID，支持驼峰命名和下划线命名
    const authorizationId = serviceRecord.authorizationId || serviceRecord.authorization_id;

    console.log(`验证授权关系 - 授权ID: ${authorizationId}, 服务用户ID: ${serviceUserId}`);

    // 检查授权ID是否存在
    if (!authorizationId) {
      console.log(`授权ID不存在 - 服务记录ID: ${serviceRecord.id}`);
      return res.status(403).json({
        success: false,
        message: '无法验证授权关系：授权ID不存在'
      });
    }

    // 验证授权关系 - 使用where查询而不是findById
    const authorization = await UserAuthorization.query()
      .where('id', authorizationId)
      .where('authorized_id', serviceUserId)
      .where('status', 'ACTIVE')
      .first();

    if (!authorization) {
      return res.status(403).json({
        success: false,
        message: '没有有效的授权关系'
      });
    }

    // 验证权限级别 - 只有STANDARD或FULL权限才能修改记录
    const privacyLevel = authorization.privacy_level || authorization.privacyLevel;
    if (privacyLevel === 'BASIC') {
      return res.status(403).json({
        success: false,
        message: '基础授权级别不允许修改记录'
      });
    }

    // 查找原始记录，确认是否是服务用户创建的或者具有完整授权
    const record = await Record.query()
      .findById(id) // 使用id参数，与路由保持一致
      .withGraphFetched('serviceRecords');

    // 检查是否是服务用户创建的记录
    const isCreatedByServiceUser = record.serviceRecords &&
      record.serviceRecords.some(sr => sr.service_user_id === serviceUserId);

    // 标准授权只能修改自己创建的记录
    if (privacyLevel === 'STANDARD' && !isCreatedByServiceUser) {
      return res.status(403).json({
        success: false,
        message: '标准授权级别只能修改自己创建的记录'
      });
    }

    // 执行事务
    const result = await transaction(Record.knex(), async (trx) => {
      // 更新记录
      const updatedRecord = await Record.query(trx)
        .patchAndFetchById(id, { // 使用id参数，与路由保持一致
          title,
          content,
          tags: tags || undefined,
          record_type: recordType || undefined,
          primary_type: primaryType || undefined,
          record_date: recordDate || undefined,
          updated_at: new Date().toISOString(),
          stage_tags: stageTags !== undefined ? stageTags : undefined, // 阶段标签
          stage_node: stageNode !== undefined ? stageNode : undefined,
          stage_phase: stagePhase !== undefined ? stagePhase : undefined,
          is_important: isImportant !== undefined ? isImportant : undefined,
          custom_tags: customTags !== undefined ? customTags : undefined, // 客户自定义标签
          severity: severity !== undefined ? severity : undefined
        });

      // 更新服务记录
      const updatedServiceRecord = await ServiceRecord.query(trx)
        .patchAndFetchById(serviceRecord.id, {
          updated_at: new Date().toISOString()
        });

      return { record: updatedRecord, serviceRecord: updatedServiceRecord };
    });

    res.json({
      success: true,
      data: result,
      message: '服务记录已更新'
    });
  } catch (error) {
    console.error('更新服务记录失败:', error);
    res.status(500).json({
      success: false,
      message: '更新服务记录失败',
      error: error.message
    });
  }
};

/**
 * 删除服务记录
 */
const deleteServiceRecord = async (req, res) => {
  const { id } = req.params; // 使用id参数，与路由保持一致
  const serviceUserId = req.user.id;

  try {
    // 查找对应的服务记录 - 修正字段名称，使用下划线形式
    const serviceRecord = await ServiceRecord.query()
      .where('record_id', id) // 使用id参数，与路由保持一致
      .where('service_user_id', serviceUserId)
      .first();

    if (!serviceRecord) {
      return res.status(404).json({
        success: false,
        message: '服务记录不存在或无权访问'
      });
    }

    // 获取授权ID，支持驼峰命名和下划线命名
    const authorizationId = serviceRecord.authorizationId || serviceRecord.authorization_id;

    console.log(`验证授权关系 - 授权ID: ${authorizationId}, 服务用户ID: ${serviceUserId}`);

    // 检查授权ID是否存在
    if (!authorizationId) {
      console.log(`授权ID不存在 - 服务记录ID: ${serviceRecord.id}`);
      return res.status(403).json({
        success: false,
        message: '无法验证授权关系：授权ID不存在'
      });
    }

    // 验证授权关系 - 修正字段名称，使用下划线形式
    const authorization = await UserAuthorization.query()
      .where('id', authorizationId)
      .where('authorized_id', serviceUserId)
      .where('status', 'ACTIVE')
      .first();

    if (!authorization) {
      console.log(`无有效授权关系 - 授权ID: ${authorizationId}, 服务用户ID: ${serviceUserId}`);
      return res.status(403).json({
        success: false,
        message: '没有有效的授权关系'
      });
    }

    console.log(`找到有效授权关系 - 授权ID: ${authorizationId}, 授权级别: ${authorization.privacy_level || authorization.privacyLevel}`);

    // 验证权限级别 - 只有STANDARD或FULL权限才能删除记录
    if (authorization.privacy_level === 'BASIC') {
      return res.status(403).json({
        success: false,
        message: '基础授权级别不允许删除记录'
      });
    }

    // 查找原始记录，确认是否是服务用户创建的
    const record = await Record.query()
      .findById(id) // 使用id参数，与路由保持一致
      .withGraphFetched('serviceRecords');

    if (!record) {
      console.log(`记录不存在 - 记录ID: ${id}`);
      return res.status(404).json({
        success: false,
        message: '记录不存在'
      });
    }

    console.log(`查找到记录 - 记录ID: ${id}, 创建者ID: ${record.created_by || record.createdBy}`);

    // 检查是否是服务用户创建的记录
    // 首先检查record.serviceRecords是否存在
    let isCreatedByServiceUser = false;

    // 方法1：检查serviceRecords关联
    if (record.serviceRecords && Array.isArray(record.serviceRecords)) {
      isCreatedByServiceUser = record.serviceRecords.some(sr => {
        const srUserId = sr.serviceUserId || sr.service_user_id;
        return srUserId === serviceUserId;
      });
      console.log(`通过serviceRecords关联检查 - 是否由服务用户创建: ${isCreatedByServiceUser}`);
    }

    // 方法2：检查created_by字段
    if (!isCreatedByServiceUser) {
      const recordCreatedBy = record.created_by || record.createdBy;
      isCreatedByServiceUser = recordCreatedBy === serviceUserId;
      console.log(`通过created_by字段检查 - 是否由服务用户创建: ${isCreatedByServiceUser}, 记录创建者: ${recordCreatedBy}, 当前用户: ${serviceUserId}`);
    }

    // 只能删除自己创建的记录，不能删除授权用户的记录
    if (!isCreatedByServiceUser) {
      console.log(`无权删除记录 - 记录ID: ${id}, 不是由当前服务用户创建`);
      return res.status(403).json({
        success: false,
        message: '无法删除授权用户创建的记录，只能删除您自己创建的记录'
      });
    }

    try {
      // 执行事务
      console.log(`开始执行删除事务 - 服务记录ID: ${serviceRecord.id}, 记录ID: ${id}`);
      await transaction(ServiceRecord.knex(), async (trx) => {
        try {
          // 删除服务记录关联
          console.log(`删除服务记录关联 - 服务记录ID: ${serviceRecord.id}`);
          await ServiceRecord.query(trx)
            .delete()
            .where('id', serviceRecord.id);

          console.log(`服务记录关联删除成功`);

          // 删除记录 - 使用软删除而不是硬删除
          console.log(`软删除记录 - 记录ID: ${id}`);
          const now = new Date().toISOString();
          await Record.query(trx)
            .patch({
              is_deleted: true,
              deleted_at: now,
              deleted_by: serviceUserId,
              updated_at: now
            })
            .where('id', id); // 使用id参数，与路由保持一致

          console.log(`记录软删除成功`);
        } catch (error) {
          console.error(`事务内部错误:`, error);
          throw error; // 重新抛出错误，让外部catch捕获
        }
      });

      console.log(`删除事务完成 - 服务记录ID: ${serviceRecord.id}, 记录ID: ${id}`);
      res.json({
        success: true,
        message: '服务记录已删除'
      });
    } catch (error) {
      console.error(`删除事务失败 - 服务记录ID: ${serviceRecord.id}, 记录ID: ${id}`, error);
      res.status(500).json({
        success: false,
        message: '删除服务记录失败',
        error: error.message
      });
    }
  } catch (error) {
    console.error('删除服务记录失败:', error);
    res.status(500).json({
      success: false,
      message: '删除服务记录失败',
      error: error.message
    });
  }
};

/**
 * 获取授权用户的患者列表
 */
const getAuthorizedPatients = async (req, res) => {
  try {
    const userId = req.user.id;
    const { authorizationId } = req.params;

    console.log(`获取授权患者列表 - 用户ID: ${userId}, 授权ID: ${authorizationId}`);

    // 验证授权关系 - 修正字段名称，使用下划线形式
    const authorization = await UserAuthorization.query()
      .findById(authorizationId)
      .where('authorized_id', userId)
      .where('status', 'ACTIVE')
      .first();

    if (!authorization) {
      console.log(`授权关系不存在或不活跃 - 授权ID: ${authorizationId}, 用户ID: ${userId}`);
      return res.status(403).json({
        success: false,
        message: '没有有效的授权关系'
      });
    }

    console.log('找到授权关系:', JSON.stringify(authorization));

    // 检查授权级别
    const privacyLevel = authorization.privacy_level || authorization.privacyLevel;

    // 基础授权只能查看授权信息，不能查看患者列表
    if (privacyLevel === 'BASIC') {
      console.log(`基础授权级别无法访问患者列表 - 授权ID: ${authorizationId}, 级别: ${privacyLevel}`);
      return res.status(403).json({
        success: false,
        message: '基础授权级别不允许访问患者列表'
      });
    }

    // 获取授权人ID
    const authorizerId = authorization.authorizer_id || authorization.authorizerId;

    if (!authorizerId) {
      console.error('授权关系缺少授权人ID - 授权ID:', authorizationId);
      return res.status(500).json({
        success: false,
        message: '授权关系数据不完整，缺少授权人ID'
      });
    }

    // 如果授权限定了特定患者 - 修正字段名称，使用下划线形式
    const patientId = authorization.patient_id || authorization.patientId;

    if (patientId) {
      console.log(`授权限定特定患者 - 患者ID: ${patientId}`);
      const patient = await Patient.query()
        .findById(patientId);

      return res.json({
        success: true,
        data: patient ? [patient] : []
      });
    }

    // 否则获取用户所有患者 - 确保字段不为undefined
    console.log(`获取所有患者 - 授权人ID: ${authorizerId}`);
    const patients = await Patient.query()
      .skipUndefined() // 跳过undefined的值
      .where('user_id', authorizerId);

    console.log(`找到患者数量: ${patients.length}`);

    res.json({
      success: true,
      data: patients
    });
  } catch (error) {
    console.error('获取授权患者列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取授权患者列表失败',
      error: error.message
    });
  }
};

/**
 * 获取授权用户的病理列表
 */
const getAuthorizedDiseases = async (req, res) => {
  try {
    const userId = req.user.id;
    const { authorizationId } = req.params;

    console.log(`获取授权病理列表 - 用户ID: ${userId}, 授权ID: ${authorizationId}`);

    // 验证授权关系 - 修正字段名称，使用下划线形式
    const authorization = await UserAuthorization.query()
      .findById(authorizationId)
      .where('authorized_id', userId)
      .where('status', 'ACTIVE')
      .first();

    if (!authorization) {
      console.log(`授权关系不存在或不活跃 - 授权ID: ${authorizationId}, 用户ID: ${userId}`);
      return res.status(403).json({
        success: false,
        message: '没有有效的授权关系'
      });
    }

    console.log('找到授权关系:', JSON.stringify(authorization));

    // 检查授权级别
    const privacyLevel = authorization.privacy_level || authorization.privacyLevel;

    // 基础授权只能查看授权信息，不能查看病理列表
    if (privacyLevel === 'BASIC') {
      console.log(`基础授权级别无法访问病理列表 - 授权ID: ${authorizationId}, 级别: ${privacyLevel}`);
      return res.status(403).json({
        success: false,
        message: '基础授权级别不允许访问病理列表'
      });
    }

    // 检查是否有授权人ID
    const authorizerId = authorization.authorizer_id || authorization.authorizerId;

    if (!authorizerId) {
      console.error('授权关系缺少授权人ID - 授权ID:', authorizationId);
      return res.status(500).json({
        success: false,
        message: '授权关系数据不完整，缺少授权人ID'
      });
    }

    // 查询条件 - 修正字段名称，使用下划线形式
    const queryBuilder = Disease.query()
      .skipUndefined() // 跳过undefined的值
      .where('user_id', authorizerId)
      .where('is_deleted', false); // 添加过滤条件，排除已删除的病理

    // 如果授权限定了特定患者 - 修正字段名称，使用下划线形式
    const patientId = authorization.patient_id || authorization.patientId;
    if (patientId) {
      console.log(`授权限定特定患者 - 患者ID: ${patientId}`);
      queryBuilder.where('patient_id', patientId);
    }

    // 标准权限只能查看非隐私病理
    if (privacyLevel === 'STANDARD') {
      // 标准权限只能查看非隐私病理
      queryBuilder.where('is_private', false);
    }

    const diseases = await queryBuilder;
    console.log(`找到病理数量: ${diseases.length}`);

    res.json({
      success: true,
      data: diseases
    });
  } catch (error) {
    console.error('获取授权病理列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取授权病理列表失败',
      error: error.message
    });
  }
};

/**
 * 根据上下文(授权ID、患者ID和疾病ID)获取服务记录
 */
const getServiceRecordsByContext = async (req, res) => {
  try {
    const userId = req.user.id;
    const { authorizationId, patientId, diseaseId } = req.query;

    console.log(`获取上下文服务记录 - 用户ID: ${userId}, 授权ID: ${authorizationId}, 患者ID: ${patientId}, 疾病ID: ${diseaseId}`);

    // 验证必要参数
    if (!authorizationId || !patientId || !diseaseId) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数: authorizationId, patientId, diseaseId'
      });
    }

    // 验证授权关系 - 直接使用knex查询而不是ORM
    const knex = Record.knex();
    const auth = await knex('user_authorizations')
      .where('id', authorizationId)
      .where(function() {
        this.where('authorized_id', userId)
            .orWhere('authorizer_id', userId);
      })
      .where('status', 'ACTIVE')
      .first();

    if (!auth) {
      console.log(`授权关系不存在或不活跃 - 授权ID: ${authorizationId}, 用户ID: ${userId}`);
      return res.status(403).json({
        success: false,
        message: '没有有效的授权关系'
      });
    }

    console.log('找到授权关系:', JSON.stringify(auth));

    // 获取有效的用户ID列表，过滤掉undefined值
    const userIds = [auth.authorizer_id, auth.authorized_id].filter(id => id !== undefined && id !== null);

    console.log(`授权关系用户IDs: 授权人=${auth.authorizer_id}, 被授权人=${auth.authorized_id}, 过滤后=${userIds}`);

    if (userIds.length === 0) {
      console.log('警告: 授权关系中没有有效的用户ID');
      return res.status(500).json({
        success: false,
        message: '授权关系数据不完整，缺少有效的用户ID'
      });
    }

    // 使用knex直接查询记录
    const records = await knex('records')
      .where('patient_id', patientId)
      .where('disease_id', diseaseId)
      .whereIn('user_id', userIds)
      .orderBy('created_at', 'desc');

    console.log(`找到 ${records.length} 条匹配记录`);

    // 检查隐私级别，过滤隐私记录
    const privacyLevel = auth.privacy_level;
    let filteredRecords = records;

    // 如果当前用户是被授权者，并且权限不是FULL，则需要过滤私密记录
    if (userId === auth.authorized_id && privacyLevel !== 'FULL') {
      filteredRecords = records.filter(record => !record.is_private);
      console.log(`权限过滤后剩余 ${filteredRecords.length} 条记录`);
    }

    // 补充服务记录信息
    const enhancedRecords = filteredRecords.map(record => {
      return {
        ...record,
        patientId: record.patient_id, // 添加驼峰命名便于前端使用
        diseaseId: record.disease_id,
        userId: record.user_id,
        isCreatedByCurrentUser: record.created_by === userId || record.user_id === userId,
        privacyLevel: privacyLevel
      };
    });

    // 可选：加载患者和疾病信息
    const recordIds = enhancedRecords.map(r => r.id);

    // 如果有记录，加载关联数据
    if (recordIds.length > 0) {
      try {
        // 加载患者数据
        const patients = await knex('patients')
          .whereIn('id', enhancedRecords.map(r => r.patient_id).filter(Boolean));

        const patientMap = patients.reduce((map, patient) => {
          map[patient.id] = patient;
          return map;
        }, {});

        // 加载疾病数据
        const diseases = await knex('diseases')
          .whereIn('id', enhancedRecords.map(r => r.disease_id).filter(Boolean));

        const diseaseMap = diseases.reduce((map, disease) => {
          map[disease.id] = disease;
          return map;
        }, {});

        // 关联数据到记录
        enhancedRecords.forEach(record => {
          if (record.patient_id && patientMap[record.patient_id]) {
            record.patient = patientMap[record.patient_id];
          }

          if (record.disease_id && diseaseMap[record.disease_id]) {
            record.disease = diseaseMap[record.disease_id];
          }
        });
      } catch (err) {
        console.warn('加载关联数据时出错:', err.message);
      }
    }

    res.json({
      success: true,
      data: enhancedRecords
    });
  } catch (error) {
    console.error('获取上下文服务记录失败:', error);
    res.status(500).json({
      success: false,
      message: '获取上下文服务记录失败',
      error: error.message
    });
  }
};

/**
 * 获取单个服务记录详情
 */
const getServiceRecordById = async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;

    console.log(`获取服务记录详情 - 用户ID: ${userId}, 用户角色: ${req.user.role}, 记录ID: ${id}`);

    // 查询服务记录，通过record_id字段查找，而不是id字段
    const serviceRecord = await ServiceRecord.query()
      .where('record_id', id)
      .withGraphFetched('[record.[patient, disease, attachments], authorization.authorizer]')
      .first();

    // 打印查询结果，帮助调试
    console.log(`查询结果 - 记录ID: ${id}, 找到服务记录: ${serviceRecord ? 'Yes' : 'No'}`);
    if (serviceRecord) {
      console.log(`服务记录字段 - ID: ${serviceRecord.id}, 记录ID: ${serviceRecord.recordId || serviceRecord.record_id}`);
      console.log(`服务记录关联 - 服务用户ID: ${serviceRecord.serviceUserId || serviceRecord.service_user_id}, 所有者ID: ${serviceRecord.ownerUserId || serviceRecord.owner_user_id}`);
    }

    if (!serviceRecord) {
      console.log(`服务记录不存在 - 记录ID: ${id}`);
      return res.status(404).json({
        success: false,
        message: '服务记录不存在'
      });
    }

    // 获取服务记录关联的字段，支持驼峰命名和下划线命名
    const serviceUserId = serviceRecord.serviceUserId || serviceRecord.service_user_id;
    const ownerUserId = serviceRecord.ownerUserId || serviceRecord.owner_user_id;
    const authorizationId = serviceRecord.authorizationId || serviceRecord.authorization_id;

    console.log(`服务记录信息 - 记录ID: ${id}, 服务用户ID: ${serviceUserId}, 记录所有者ID: ${ownerUserId}, 授权ID: ${authorizationId}`);

    // 管理员可以访问所有记录
    if (req.user.role === 'ADMIN') {
      console.log(`管理员访问服务记录 - 记录ID: ${id}`);
    }
    // 如果当前用户是服务用户或记录所有者，直接允许访问
    else if (serviceUserId === userId || ownerUserId === userId) {
      console.log(`服务用户或记录所有者访问服务记录 - 记录ID: ${id}`);
    }
    // 否则，需要检查授权关系和授权级别
    else {
      console.log(`第三方用户访问服务记录，检查授权关系 - 记录ID: ${id}, 用户ID: ${userId}`);

      // 检查授权ID是否存在
      if (!authorizationId) {
        console.log(`授权ID不存在 - 记录ID: ${id}`);
        return res.status(403).json({
          success: false,
          message: '无法验证授权关系：授权ID不存在'
        });
      }

      // 查询授权关系 - 使用where查询而不是findById
      const authorization = await UserAuthorization.query()
        .where('id', authorizationId)
        .where(function() {
          this.where('authorized_id', userId)
              .orWhere('authorizer_id', userId);
        })
        .where('status', 'ACTIVE')
        .first();

      if (!authorization) {
        console.log(`无有效授权关系 - 记录ID: ${id}, 用户ID: ${userId}, 授权ID: ${authorizationId}`);
        return res.status(403).json({
          success: false,
          message: '无权访问此服务记录：没有有效的授权关系'
        });
      }

      // 获取授权级别
      const privacyLevel = authorization.privacy_level || authorization.privacyLevel;
      console.log(`授权级别 - 记录ID: ${id}, 用户ID: ${userId}, 授权级别: ${privacyLevel}`);

      // 检查记录是否为隐私内容
      const record = serviceRecord.record;
      const isPrivate = record && (record.isPrivate === true || record.is_private === true);

      if (isPrivate) {
        console.log(`记录为隐私内容 - 记录ID: ${id}, 用户无权访问隐私内容`);
        return res.status(403).json({
          success: false,
          message: '无权访问此服务记录：该记录为隐私内容'
        });
      }

      // 所有授权级别都可以查看非隐私记录
      console.log(`授权用户访问非隐私记录 - 记录ID: ${id}, 用户ID: ${userId}, 授权级别: ${privacyLevel}`);
    }

    console.log(`成功获取服务记录 - 记录ID: ${id}`);

    res.json({
      success: true,
      data: serviceRecord
    });
  } catch (error) {
    console.error('获取服务记录详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取服务记录详情失败',
      error: error.message
    });
  }
};

module.exports = {
  getServiceRecords,
  getAuthorizedUserRecords,
  createServiceRecord,
  updateServiceRecord,
  deleteServiceRecord,
  getAuthorizedPatients,
  getAuthorizedDiseases,
  getServiceRecordsByContext,
  getServiceRecordById
};