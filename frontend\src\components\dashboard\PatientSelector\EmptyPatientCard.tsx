import React from 'react';
import { Box, Typography, Paper } from '@mui/material';
import { AddCircleOutline } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

/**
 * 空患者卡片组件
 * 用于在患者选择器中显示添加新患者的卡片
 */
const EmptyPatientCard: React.FC = () => {
  const navigate = useNavigate();

  // 处理添加患者点击事件
  const handleAddPatient = () => {
    // 跳转到添加患者页面
    navigate('/patients/create');
  };

  return (
    <Paper
      elevation={0}
      sx={{
        borderRadius: 2,
        border: '1px dashed',
        borderColor: 'divider',
        backgroundColor: 'background.paper',
        cursor: 'pointer',
        transition: 'all 0.2s',
        display: 'flex',
        pl: 0,
        overflow: 'hidden',
        height: '100%',
        minHeight: 80,
        '&:hover': {
          borderColor: 'primary.main',
          backgroundColor: 'action.hover',
        },
      }}
      onClick={handleAddPatient}
    >
      {/* 左侧彩条 - 使用灰色 */}
      <Box
        sx={{
          width: 6,
          backgroundColor: '#e0e0e0', // 灰色
        }}
      />

      <Box
        sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          p: 1.5
        }}
      >
        <AddCircleOutline
          fontSize="medium"
          sx={{ 
            color: 'text.disabled',
            mb: 0.5,
            fontSize: '1.8rem',
            transition: 'color 0.2s',
            '.MuiPaper-root:hover &': {
              color: 'primary.main',
            }
          }}
        />
        <Typography 
          variant="subtitle1" 
          color="text.secondary"
          align="center"
          sx={{
            transition: 'color 0.2s',
            fontSize: '0.7rem',
            '.MuiPaper-root:hover &': {
              color: 'primary.main',
            }
          }}
        >
          点击添加患者
        </Typography>
      </Box>
    </Paper>
  );
};

export default EmptyPatientCard; 