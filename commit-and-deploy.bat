@echo off
chcp 65001 >nul
echo 📝 提交修复代码并准备部署...

REM 检查 Git 状态
echo 🔍 检查 Git 状态...
git status

echo.
echo 📋 修复内容摘要：
echo ✅ 修复了前端 Mixed Content 错误
echo ✅ 更新了环境变量配置
echo ✅ 修复了后端 CORS 配置
echo ✅ 添加了 HTTPS 支持
echo.

REM 添加所有修改的文件
echo 📦 添加修改的文件...
git add frontend/.env
git add frontend/.env.production
git add frontend/src/services/
git add frontend/src/utils/
git add frontend/src/hooks/
git add frontend/public/manifest.json
git add backend/src/index.js
git add *.sh
git add *.bat
git add test-login-fix.html

REM 显示将要提交的文件
echo.
echo 📋 将要提交的文件：
git diff --cached --name-only

echo.
set /p "confirm=🤔 是否继续提交？(y/N): "
if /i not "%confirm%"=="y" (
    echo ❌ 取消提交
    pause
    exit /b 1
)

REM 提交代码
echo 💾 提交代码...
git commit -m "修复登录 Mixed Content 和 CORS 问题

- 修复前端环境变量配置，支持 HTTPS
- 更新所有 API 客户端，移除硬编码 URL
- 修复后端 CORS 配置，添加 HTTPS 源支持
- 更新 WebSocket CORS 配置
- 修复 manifest.json 配置
- 添加部署和重启脚本"

if %errorlevel% neq 0 (
    echo ❌ 代码提交失败
    pause
    exit /b 1
)

echo ✅ 代码提交成功

REM 推送到远程仓库
echo.
set /p "push=🚀 是否推送到远程仓库？(y/N): "
if /i "%push%"=="y" (
    echo 📤 推送代码...
    git push
    if %errorlevel% neq 0 (
        echo ❌ 代码推送失败
        pause
        exit /b 1
    )
    echo ✅ 代码推送成功
)

echo.
echo 🎉 代码同步完成！
echo.
echo 🖥️  服务器部署步骤：
echo 1. 登录服务器
echo 2. 执行以下命令：
echo.
echo    cd /home/<USER>/HKB
echo    git pull
echo    cd frontend
echo    npm run build
echo    cd ../backend
echo    pm2 restart all
echo    sudo systemctl reload nginx
echo.
echo 3. 测试登录功能：
echo    https://hkb.life/login
echo.
echo 🔍 验证步骤：
echo 1. 检查浏览器控制台不再有 Mixed Content 错误
echo 2. 登录功能正常工作
echo 3. API 请求使用 HTTPS 协议

pause
