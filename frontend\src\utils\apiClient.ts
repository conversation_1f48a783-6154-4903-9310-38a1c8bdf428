import axios from 'axios';
import https from 'https';

// 创建 axios 实例
const apiClient = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000',
  timeout: 10000,
  // 忽略证书错误
  httpsAgent: new https.Agent({
    rejectUnauthorized: false
  })
});

// 添加请求拦截器
apiClient.interceptors.response.use(
  response => response,
  async error => {
    const config = error.config;
    
    // 如果没有设置重试配置，则设置默认值
    if (!config || !config.retry) {
      config.retry = 3;
      config.retryDelay = 1000;
    }
    
    // 如果还有重试次数
    if (config.retry > 0) {
      config.retry -= 1;
      
      // 延迟重试
      await new Promise(resolve => setTimeout(resolve, config.retryDelay));
      
      // 重试请求
      return apiClient(config);
    }
    
    return Promise.reject(error);
  }
);

export default apiClient; 