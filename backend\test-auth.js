/**
 * 用户身份验证测试脚本
 * 测试身份验证中间件和用户信息传递
 */
const jwt = require('jsonwebtoken');
const knex = require('./src/db').knex;
const User = require('./models/User');
const { Model } = require('objection');

// 设置knex实例
Model.knex(knex);

// JWT密钥，从环境变量或默认值获取
const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret';

// 模拟Express请求和响应对象
const createMockRequest = (headers = {}, body = {}) => ({
  headers,
  body,
  query: {}
});

const createMockResponse = () => {
  const res = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  return res;
};

// 模拟身份验证中间件
const mockAuthMiddleware = async (req, token) => {
  try {
    req.headers = { authorization: `Bearer ${token}` };
    
    // 验证令牌
    const decoded = jwt.verify(token, JWT_SECRET);
    console.log('令牌验证成功，解码结果:', decoded);
    
    // 检查用户是否存在
    const user = await knex('users')
      .where('id', decoded.id)
      .first();
    
    if (!user) {
      console.error('用户不存在');
      return false;
    }
    
    console.log('找到用户:', user);
    
    // 将用户ID添加到请求对象中
    req.userId = decoded.id;
    req.user_id = decoded.id;
    req.user = user;
    
    return true;
  } catch (error) {
    console.error('身份验证失败:', error);
    return false;
  }
};

// 测试创建授权请求
const testCreateAuthorization = async (token) => {
  console.log('\n=== 测试创建授权请求 ===');
  
  const req = createMockRequest();
  const authenticated = await mockAuthMiddleware(req, token);
  
  if (!authenticated) {
    console.error('身份验证失败，无法继续测试');
    return;
  }
  
  // 找一个SERVICE或ADMIN角色的用户
  const serviceUser = await User.query()
    .where('role', 'in', ['SERVICE', 'ADMIN'])
    .first();
  
  if (!serviceUser) {
    console.error('找不到服务用户或管理员用户');
    return;
  }
  
  console.log('找到服务用户:', serviceUser.id);
  
  // 构建创建授权的请求体
  req.body = {
    authorizerId: req.user.id,
    authorizedId: serviceUser.id,
    privacyLevel: 'STANDARD'
  };
  
  console.log('请求体:', req.body);
  console.log('用户信息:', {
    reqUser: req.user ? '存在' : '不存在',
    reqUserId: req.userId || '不存在',
    reqUser_id: req.user_id || '不存在'
  });

  // 如果这是真实场景，这里会调用控制器方法
  console.log('请求体中的授权人ID:', req.body.authorizerId);
  console.log('请求用户的ID:', req.user.id);
  
  if (req.body.authorizerId === req.user.id) {
    console.log('✅ 身份一致：请求体中的授权人ID与当前用户ID一致');
  } else {
    console.log('❌ 身份不一致：请求体中的授权人ID与当前用户ID不一致');
  }
  
  console.log('=== 测试完成 ===');
};

// 测试查询授权列表
const testGetAuthorizations = async (token) => {
  console.log('\n=== 测试查询授权列表 ===');
  
  const req = createMockRequest();
  const authenticated = await mockAuthMiddleware(req, token);
  
  if (!authenticated) {
    console.error('身份验证失败，无法继续测试');
    return;
  }
  
  console.log('用户ID:', req.user.id);
  
  // 检查表是否存在
  const hasTable = await knex.schema.hasTable('user_authorizations');
  console.log('表user_authorizations存在:', hasTable);
  
  if (hasTable) {
    // 查询授权列表
    const authorizations = await knex('user_authorizations')
      .where('authorizer_id', req.user.id)
      .select('*');
    
    console.log(`找到${authorizations.length}条授权记录`);
    
    if (authorizations.length > 0) {
      console.log('第一条记录:', authorizations[0]);
    }
  }
  
  console.log('=== 测试完成 ===');
};

// 运行测试
const runTests = async () => {
  try {
    console.log('===== 开始测试用户身份验证和授权 =====');
    
    // 获取第一个用户的信息
    const user = await User.query().first();
    
    if (!user) {
      console.error('找不到任何用户，无法继续测试');
      return;
    }
    
    console.log('测试用户:', user.username, user.id);
    
    // 创建JWT令牌
    const token = jwt.sign({ id: user.id }, JWT_SECRET, { expiresIn: '1h' });
    console.log('创建的令牌:', token);
    
    // 测试验证令牌
    const decodedToken = jwt.verify(token, JWT_SECRET);
    console.log('验证令牌结果:', decodedToken);
    
    // 测试创建授权
    await testCreateAuthorization(token);
    
    // 测试查询授权列表
    await testGetAuthorizations(token);
    
  } catch (error) {
    console.error('测试过程中出错:', error);
  } finally {
    // 关闭数据库连接
    await knex.destroy();
    console.log('===== 测试完成 =====');
  }
};

// 执行测试
runTests(); 