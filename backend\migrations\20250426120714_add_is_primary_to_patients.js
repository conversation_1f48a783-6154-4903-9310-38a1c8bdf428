/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.table('patients', function(table) {
    // 添加是否为主要患者标记
    // PostgreSQL中使用boolean类型而不是integer
    table.boolean('is_primary').defaultTo(false).notNullable();
    // 添加索引以提高查询性能
    table.index('is_primary');
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.table('patients', function(table) {
    // 回滚时删除字段
    table.dropColumn('is_primary');
  });
};
