/**
 * PostgreSQL连接检查脚本
 * 用于验证PostgreSQL连接配置是否正确
 */
const knex = require('knex');
const knexfile = require('../knexfile');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// 检查环境变量
console.log('检查PostgreSQL连接环境变量...');
const requiredEnvVars = ['DB_HOST', 'DB_PORT', 'DB_USER', 'DB_PASSWORD', 'DB_NAME'];
const missingVars = [];

for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    missingVars.push(envVar);
  } else {
    // 掩盖密码，只显示前两个字符
    const value = envVar === 'DB_PASSWORD' 
      ? `${process.env[envVar].substring(0, 2)}${'*'.repeat(6)}`
      : process.env[envVar];
    console.log(`${envVar}: ${value}`);
  }
}

if (missingVars.length > 0) {
  console.error(`❌ 缺少必要的环境变量: ${missingVars.join(', ')}`);
  console.error('请确保.env文件中包含这些变量，或者已设置为系统环境变量');
  process.exit(1);
}

// 获取环境配置
const env = process.env.NODE_ENV || 'development';
console.log(`当前环境: ${env}`);
const config = knexfile[env];

// 创建测试连接
console.log('\n尝试连接到PostgreSQL数据库...');

// 检查密码格式
if (typeof config.connection.password !== 'string') {
  console.error('❌ 数据库密码不是字符串格式!');
  console.error(`密码类型: ${typeof config.connection.password}`);
  console.error('请确保密码格式正确，避免使用特殊字符或确保正确转义');
  process.exit(1);
}

// 修正连接配置
const fixedConfig = {
  ...config,
  connection: {
    ...config.connection
  },
  pool: { min: 0, max: 1 } // 最小化连接池
};

// 创建数据库连接
const db = knex(fixedConfig);

// 尝试连接
async function testConnection() {
  try {
    console.log('正在连接数据库...');
    const result = await db.raw('SELECT version() as version');
    
    console.log('✅ 数据库连接成功!');
    const version = result.rows ? result.rows[0].version : result[0].version;
    console.log(`PostgreSQL版本: ${version}`);
    
    // 测试表查询
    console.log('\n尝试查询数据库表...');
    const tables = await db.raw(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    `);
    
    const tableList = tables.rows || tables;
    console.log(`发现${tableList.length}个表:`);
    tableList.forEach((table, index) => {
      console.log(`${index + 1}. ${table.table_name || table.TABLE_NAME}`);
    });
    
    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败:');
    console.error(`错误类型: ${error.name}`);
    console.error(`错误消息: ${error.message}`);
    
    if (error.message.includes('password authentication failed')) {
      console.error('\n可能的解决方案:');
      console.error('- 检查DB_PASSWORD环境变量是否正确');
      console.error('- 确保PostgreSQL用户存在且密码正确');
      console.error('- 检查pg_hba.conf文件中的认证方法');
    } else if (error.message.includes('SCRAM-SERVER-FIRST-MESSAGE')) {
      console.error('\n可能的解决方案:');
      console.error('- 确保DB_PASSWORD是有效的字符串格式');
      console.error('- 避免在密码中使用特殊字符或确保正确转义');
      console.error('- 尝试在.env文件中用引号包裹密码');
    } else if (error.message.includes('connect ECONNREFUSED')) {
      console.error('\n可能的解决方案:');
      console.error('- 检查DB_HOST和DB_PORT是否正确');
      console.error('- 确保PostgreSQL服务正在运行');
      console.error('- 检查网络连接和防火墙设置');
    } else if (error.message.includes('database') && error.message.includes('does not exist')) {
      console.error('\n可能的解决方案:');
      console.error('- 确保DB_NAME指定的数据库存在');
      console.error('- 尝试先创建数据库: CREATE DATABASE your_db_name;');
    }
    
    return false;
  } finally {
    await db.destroy();
  }
}

// 执行连接测试
testConnection()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('测试过程中发生错误:', error);
    process.exit(1);
  }); 