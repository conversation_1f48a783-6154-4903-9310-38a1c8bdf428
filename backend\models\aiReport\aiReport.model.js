/**
 * AI报告数据库模型
 * 存储用户的AI分析报告及相关元数据
 */
const { DataTypes } = require('sequelize');
const sequelize = require('../../config/database');
const Disease = require('../disease/disease.model');
const User = require('../user/user.model');
const Patient = require('../patient/patient.model');
const Record = require('../record/record.model');

/**
 * AIReport模型
 * 存储由LLM生成的AI分析报告及相关元数据
 */
const AIReport = sequelize.define('AIReport', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  diseaseId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Disease,
      key: 'id'
    }
  },
  patientId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Patient,
      key: 'id'
    }
  },
  userId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: User,
      key: 'id'
    }
  },
  recordId: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Record,
      key: 'id'
    },
    comment: '关联的记录ID（AI分析可能会生成一条记录）'
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false
  },
  templateType: {
    type: DataTypes.ENUM('COMPREHENSIVE_ANALYSIS'),
    defaultValue: 'COMPREHENSIVE_ANALYSIS',
    allowNull: false
  },
  anonymizedInfo: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '匿名化后的信息，包含原始姓名和匿名化后的姓名'
  },
  llmRawResponse: {
    type: DataTypes.TEXT('long'),
    allowNull: true,
    comment: 'LLM原始响应内容（JSON格式）'
  },
  content: {
    type: DataTypes.JSON,
    allowNull: false,
    comment: '分析报告内容（结构化JSON）'
  },
  pdfUrl: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'PDF报告的存储路径'
  },
  status: {
    type: DataTypes.ENUM('PROCESSING', 'COMPLETED', 'FAILED'),
    defaultValue: 'PROCESSING',
    allowNull: false
  },
  errorMessage: {
    type: DataTypes.TEXT,
    allowNull: true
  }
}, {
  tableName: 'ai_reports',
  timestamps: true,
  indexes: [
    {
      name: 'ai_report_disease_idx',
      fields: ['diseaseId']
    },
    {
      name: 'ai_report_patient_idx',
      fields: ['patientId']
    },
    {
      name: 'ai_report_user_idx',
      fields: ['userId']
    }
  ]
});

// 设置与Disease的关联
AIReport.belongsTo(Disease, { 
  foreignKey: 'diseaseId', 
  as: 'disease'
});

Disease.hasMany(AIReport, { 
  foreignKey: 'diseaseId', 
  as: 'aiReports'
});

// 设置与User的关联
AIReport.belongsTo(User, { 
  foreignKey: 'userId', 
  as: 'user'
});

// 设置与Patient的关联
AIReport.belongsTo(Patient, { 
  foreignKey: 'patientId', 
  as: 'patient'
});

// 设置与Record的关联
AIReport.belongsTo(Record, { 
  foreignKey: 'recordId', 
  as: 'record'
});

module.exports = AIReport; 