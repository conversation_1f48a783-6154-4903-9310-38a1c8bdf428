/**
 * 匿名化服务
 * 用于处理患者敏感信息，确保发送给LLM的数据不包含隐私内容
 */

/**
 * 对身份证号进行脱敏处理
 * @param {string} idCard 身份证号
 * @returns {string} 脱敏后的身份证号
 */
const maskIdCard = (idCard) => {
  if (!idCard || typeof idCard !== 'string') return '';
  
  // 保留前3位和后4位，中间用*代替
  if (idCard.length >= 8) {
    return `${idCard.substring(0, 3)}${'*'.repeat(idCard.length - 7)}${idCard.substring(idCard.length - 4)}`;
  }
  return '*'.repeat(idCard.length);
};

/**
 * 对手机号进行脱敏处理
 * @param {string} phoneNumber 手机号
 * @returns {string} 脱敏后的手机号
 */
const maskPhoneNumber = (phoneNumber) => {
  if (!phoneNumber || typeof phoneNumber !== 'string') return '';
  
  // 保留前3位和后4位，中间用*代替
  if (phoneNumber.length >= 8) {
    return `${phoneNumber.substring(0, 3)}${'*'.repeat(phoneNumber.length - 7)}${phoneNumber.substring(phoneNumber.length - 4)}`;
  }
  return '*'.repeat(phoneNumber.length);
};

/**
 * 对邮箱进行脱敏处理
 * @param {string} email 邮箱
 * @returns {string} 脱敏后的邮箱
 */
const maskEmail = (email) => {
  if (!email || typeof email !== 'string') return '';
  
  const parts = email.split('@');
  if (parts.length !== 2) return '<EMAIL>';
  
  const username = parts[0];
  const domain = parts[1];
  
  // 保留用户名前面2个字符，其余用*代替
  let maskedUsername = username;
  if (username.length > 2) {
    maskedUsername = `${username.substring(0, 2)}${'*'.repeat(username.length - 2)}`;
  }
  
  return `${maskedUsername}@${domain}`;
};

/**
 * 对出生日期进行脱敏处理
 * @param {string} birthDate 出生日期
 * @returns {string} 脱敏后的出生日期
 */
const maskBirthDate = (birthDate) => {
  if (!birthDate || typeof birthDate !== 'string') return '';
  
  // 仅保留年份，月和日用**代替
  const parts = birthDate.split('-');
  if (parts.length === 3) {
    return `${parts[0]}-**-**`;
  }
  return birthDate;
};

/**
 * 对地址进行脱敏处理
 * @param {string} address 地址
 * @returns {string} 脱敏后的地址
 */
const maskAddress = (address) => {
  if (!address || typeof address !== 'string') return '';
  
  // 提取省市区信息，其余用****代替
  const provinceCity = address.match(/(.+?(省|市|自治区|自治州|盟|旗)){1,2}/);
  if (provinceCity) {
    return `${provinceCity[0]}******`;
  }
  
  // 如果无法提取省市区，仅保留少量信息
  if (address.length > 4) {
    return `${address.substring(0, 4)}******`;
  }
  return '******';
};

/**
 * 匿名化患者信息
 * @param {Object} patientData 患者数据
 * @returns {Object} 匿名化后的数据及映射关系
 */
const anonymizePatientData = (patientData) => {
  if (!patientData) {
    return { anonymizedData: {}, mapping: {} };
  }
  
  // 创建深拷贝，避免修改原始对象
  const dataCopy = JSON.parse(JSON.stringify(patientData));
  
  // 存储原始值与匿名化值的映射
  const mapping = {};
  
  // 处理患者姓名 - 更改为*先生/*女士格式
  if (dataCopy.name) {
    // 根据性别确定称谓
    const genderSuffix = dataCopy.gender === 'MALE' ? '先生' : 
                         dataCopy.gender === 'FEMALE' ? '女士' : '患者';
    
    // 使用姓氏的第一个字加称谓，如果没有姓氏则使用*作为占位符
    const lastName = dataCopy.name.charAt(0);
    const anonymizedName = `${lastName !== '' ? lastName : '*'}${genderSuffix}`;
    
    mapping[dataCopy.name] = anonymizedName;
    dataCopy.name = anonymizedName;
  }
  
  // 处理身份证号
  if (dataCopy.idCard) {
    const anonymizedIdCard = maskIdCard(dataCopy.idCard);
    mapping[dataCopy.idCard] = anonymizedIdCard;
    dataCopy.idCard = anonymizedIdCard;
  }
  
  // 处理医保卡号
  if (dataCopy.medicareCard) {
    const anonymizedMedicareCard = maskIdCard(dataCopy.medicareCard);
    mapping[dataCopy.medicareCard] = anonymizedMedicareCard;
    dataCopy.medicareCard = anonymizedMedicareCard;
  }
  
  // 处理手机号
  if (dataCopy.phoneNumber) {
    const anonymizedPhone = maskPhoneNumber(dataCopy.phoneNumber);
    mapping[dataCopy.phoneNumber] = anonymizedPhone;
    dataCopy.phoneNumber = anonymizedPhone;
  }
  
  // 处理电子邮件
  if (dataCopy.email) {
    const anonymizedEmail = maskEmail(dataCopy.email);
    mapping[dataCopy.email] = anonymizedEmail;
    dataCopy.email = anonymizedEmail;
  }
  
  // 处理出生日期
  if (dataCopy.birthDate) {
    const anonymizedBirthDate = maskBirthDate(dataCopy.birthDate);
    mapping[dataCopy.birthDate] = anonymizedBirthDate;
    dataCopy.birthDate = anonymizedBirthDate;
  }
  
  // 处理地址
  if (dataCopy.address) {
    const anonymizedAddress = maskAddress(dataCopy.address);
    mapping[dataCopy.address] = anonymizedAddress;
    dataCopy.address = anonymizedAddress;
  }

  // 处理亲属姓名
  if (dataCopy.emergencyContactName) {
    const anonymizedRelativeName = `联系人${dataCopy.emergencyContactName.charAt(0)}`;
    mapping[dataCopy.emergencyContactName] = anonymizedRelativeName;
    dataCopy.emergencyContactName = anonymizedRelativeName;
  }
  
  // 处理亲属联系方式
  if (dataCopy.emergencyContactPhone) {
    const anonymizedRelativePhone = maskPhoneNumber(dataCopy.emergencyContactPhone);
    mapping[dataCopy.emergencyContactPhone] = anonymizedRelativePhone;
    dataCopy.emergencyContactPhone = anonymizedRelativePhone;
  }
  
  return {
    anonymizedData: dataCopy,
    mapping
  };
};

/**
 * 匿名化病历信息
 * @param {Object} diseaseData 病历数据
 * @param {Object} mapping 现有的匿名化映射
 * @returns {Object} 匿名化后的数据及更新的映射关系
 */
const anonymizeDiseaseData = (diseaseData, mapping = {}) => {
  if (!diseaseData) {
    return { anonymizedData: {}, mapping };
  }
  
  // 创建深拷贝，避免修改原始对象
  const dataCopy = JSON.parse(JSON.stringify(diseaseData));
  const updatedMapping = { ...mapping };
  
  // 处理病历记录中可能包含的患者姓名和其他敏感信息
  if (dataCopy.description && typeof dataCopy.description === 'string') {
    // 遍历现有映射，替换描述中的敏感信息
    Object.keys(updatedMapping).forEach(originalValue => {
      if (originalValue && originalValue.length > 1) { // 避免替换单个字符
        dataCopy.description = dataCopy.description.replace(
          new RegExp(originalValue, 'g'), 
          updatedMapping[originalValue]
        );
      }
    });
    
    // 额外处理可能的身份证格式
    dataCopy.description = dataCopy.description.replace(
      /\d{17}[\dXx]/g, 
      match => maskIdCard(match)
    );
    
    // 额外处理可能的手机号格式
    dataCopy.description = dataCopy.description.replace(
      /1[3-9]\d{9}/g,
      match => maskPhoneNumber(match)
    );
    
    // 额外处理可能的邮箱格式
    dataCopy.description = dataCopy.description.replace(
      /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g,
      match => maskEmail(match)
    );
  }
  
  // 同样处理notes字段
  if (dataCopy.notes && typeof dataCopy.notes === 'string') {
    Object.keys(updatedMapping).forEach(originalValue => {
      if (originalValue && originalValue.length > 1) {
        dataCopy.notes = dataCopy.notes.replace(
          new RegExp(originalValue, 'g'), 
          updatedMapping[originalValue]
        );
      }
    });
    
    // 额外处理可能的身份证格式
    dataCopy.notes = dataCopy.notes.replace(
      /\d{17}[\dXx]/g, 
      match => maskIdCard(match)
    );
    
    // 额外处理可能的手机号格式
    dataCopy.notes = dataCopy.notes.replace(
      /1[3-9]\d{9}/g,
      match => maskPhoneNumber(match)
    );
    
    // 额外处理可能的邮箱格式
    dataCopy.notes = dataCopy.notes.replace(
      /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g,
      match => maskEmail(match)
    );
  }
  
  // 特定医院、医生姓名等也可能需要匿名化
  if (dataCopy.hospital_name) {
    const anonymizedHospital = `某${dataCopy.hospital_name.includes('医院') ? '医院' : '医疗机构'}`;
    updatedMapping[dataCopy.hospital_name] = anonymizedHospital;
    dataCopy.hospital_name = anonymizedHospital;
  }
  
  if (dataCopy.doctor_name) {
    const anonymizedDoctor = `医生${dataCopy.doctor_name.charAt(0)}`;
    updatedMapping[dataCopy.doctor_name] = anonymizedDoctor;
    dataCopy.doctor_name = anonymizedDoctor;
  }
  
  return {
    anonymizedData: dataCopy,
    mapping: updatedMapping
  };
};

/**
 * 匿名化一组病历记录
 * @param {Array} records 病历记录数组
 * @param {Object} mapping 现有的匿名化映射
 * @returns {Object} 匿名化后的数据及更新的映射关系
 */
const anonymizeRecords = (records, mapping = {}) => {
  if (!records || !Array.isArray(records)) {
    return { anonymizedRecords: [], mapping };
  }
  
  const anonymizedRecords = [];
  let updatedMapping = { ...mapping };
  
  for (const record of records) {
    // 创建深拷贝，避免修改原始对象
    const recordCopy = JSON.parse(JSON.stringify(record));
    
    // 处理记录内容中的敏感信息
    if (recordCopy.content && typeof recordCopy.content === 'string') {
      Object.keys(updatedMapping).forEach(originalValue => {
        if (originalValue && originalValue.length > 1) {
          recordCopy.content = recordCopy.content.replace(
            new RegExp(originalValue, 'g'), 
            updatedMapping[originalValue]
          );
        }
      });
      
      // 额外处理可能的身份证格式
      recordCopy.content = recordCopy.content.replace(
        /\d{17}[\dXx]/g, 
        match => maskIdCard(match)
      );
      
      // 额外处理可能的手机号格式
      recordCopy.content = recordCopy.content.replace(
        /1[3-9]\d{9}/g,
        match => maskPhoneNumber(match)
      );
      
      // 额外处理可能的邮箱格式
      recordCopy.content = recordCopy.content.replace(
        /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g,
        match => maskEmail(match)
      );
    }
    
    anonymizedRecords.push(recordCopy);
  }
  
  return {
    anonymizedRecords,
    mapping: updatedMapping
  };
};

/**
 * 完整匿名化医疗数据
 * @param {Object} data 包含患者、病历和记录的完整数据
 * @returns {Object} 完全匿名化后的数据和映射关系
 */
const anonymizeMedicalData = (data) => {
  // 首先匿名化患者数据
  const { anonymizedData: anonymizedPatient, mapping: patientMapping } = 
    anonymizePatientData(data.patient);
  
  // 然后匿名化病历数据，使用已有的映射
  const { anonymizedData: anonymizedDisease, mapping: diseaseMapping } = 
    anonymizeDiseaseData(data.disease, patientMapping);
  
  // 最后匿名化记录数据，使用累积的映射
  const { anonymizedRecords, mapping: finalMapping } = 
    anonymizeRecords(data.records, diseaseMapping);
  
  return {
    anonymizedData: {
      patient: anonymizedPatient,
      disease: anonymizedDisease,
      records: anonymizedRecords
    },
    mapping: finalMapping
  };
};

module.exports = {
  anonymizePatientData,
  anonymizeDiseaseData,
  anonymizeRecords,
  anonymizeMedicalData,
  // 暴露辅助函数，以便其他模块使用
  maskIdCard,
  maskPhoneNumber,
  maskEmail,
  maskBirthDate,
  maskAddress
}; 