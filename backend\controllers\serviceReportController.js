const { v4: uuidv4 } = require('uuid');
const { transaction } = require('objection');
const { AIReport } = require('../models/aiReport');
const ServiceReport = require('../models/ServiceReport');
const UserAuthorization = require('../models/UserAuthorization');
const User = require('../models/User');
const Patient = require('../models/Patient');
const Disease = require('../models/Disease');

/**
 * 获取服务用户创建的AI报告
 */
const getServiceReports = async (req, res) => {
  try {
    const userId = req.user.id;
    
    // 查询服务用户创建的报告
    const serviceReports = await ServiceReport.query()
      .where('service_user_id', userId)
      .withGraphFetched('[aiReport, authorization.[authorizer, patient]]');
      
    res.json({
      success: true,
      data: serviceReports
    });
  } catch (error) {
    console.error('获取服务报告失败:', error);
    res.status(500).json({
      success: false,
      message: '获取服务报告失败',
      error: error.message
    });
  }
};

/**
 * 为授权用户创建AI报告
 */
const createServiceReport = async (req, res) => {
  // 获取报告字段
  const { 
    authorizationId, 
    patientId, 
    diseaseId,
    reportType,
    promptOverrides
  } = req.body;
  
  const serviceUserId = req.user.id;
  
  try {
    // 验证授权关系
    const authorization = await UserAuthorization.query()
      .findById(authorizationId)
      .where('authorizedId', serviceUserId)
      .where('status', 'ACTIVE');
      
    if (!authorization) {
      return res.status(403).json({
        success: false,
        message: '没有有效的授权关系'
      });
    }
    
    // 检查授权级别
    const privacyLevel = authorization.privacyLevel;
    
    // 基础授权不能创建报告
    if (privacyLevel === 'BASIC') {
      return res.status(403).json({
        success: false,
        message: '基础授权级别不允许创建报告'
      });
    }
    
    // 验证患者ID
    let patientToUse = patientId;
    
    // 如果授权关系指定了患者，则必须使用该患者
    if (authorization.patientId && patientId !== authorization.patientId) {
      return res.status(400).json({
        success: false,
        message: '无法为未授权的患者创建报告'
      });
    }
    
    // 如果未指定患者，则使用授权关系中的患者
    if (!patientToUse && authorization.patientId) {
      patientToUse = authorization.patientId;
    }
    
    // 如果仍未指定患者，则获取用户的主要患者
    if (!patientToUse) {
      const primaryPatient = await Patient.query()
        .where('userId', authorization.authorizerId)
        .where('isPrimary', true)
        .first();
        
      if (primaryPatient) {
        patientToUse = primaryPatient.id;
      } else {
        return res.status(400).json({
          success: false,
          message: '未指定患者，且用户没有主要患者'
        });
      }
    }
    
    // 验证疾病ID
    if (!diseaseId) {
      return res.status(400).json({
        success: false,
        message: '疾病ID是必须的'
      });
    }
    
    // 查询疾病
    const disease = await Disease.query()
      .findById(diseaseId)
      .where('userId', authorization.authorizerId);
      
    if (!disease) {
      return res.status(404).json({
        success: false,
        message: '疾病不存在或无权访问'
      });
    }
    
    // 检查隐私级别
    if ((authorization.privacyLevel === 'BASIC' || authorization.privacyLevel === 'STANDARD') && disease.isPrivate) {
      return res.status(403).json({
        success: false,
        message: '当前授权级别无法访问隐私病理'
      });
    }
    
    // 执行事务
    const result = await transaction(AIReport.knex(), async (trx) => {
      // 创建AI报告
      const aiReport = await AIReport.query(trx).insert({
        id: uuidv4(),
        userId: authorization.authorizerId,
        patientId: patientToUse,
        diseaseId,
        reportType: reportType || 'STANDARD',
        status: 'PENDING',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        promptOverrides: promptOverrides || {}
      });
      
      // 创建服务报告关联
      const serviceReport = await ServiceReport.query(trx).insert({
        id: uuidv4(),
        aiReportId: aiReport.id,
        authorizationId,
        serviceUserId,
        ownerUserId: authorization.authorizerId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
      
      return { aiReport, serviceReport };
    });
    
    // 异步生成报告（此处不等待完成）
    generateAIReport(result.aiReport.id).catch(err => {
      console.error('生成AI报告失败:', err);
    });
    
    res.status(201).json({
      success: true,
      data: result,
      message: '服务报告请求已创建，正在生成中'
    });
  } catch (error) {
    console.error('创建服务报告失败:', error);
    res.status(500).json({
      success: false,
      message: '创建服务报告失败',
      error: error.message
    });
  }
};

/**
 * 异步生成AI报告
 */
const generateAIReport = async (reportId) => {
  try {
    // 在这里调用AI报告生成逻辑
    // 这部分代码通常是复用现有的AI报告生成服务
    // 实际实现会根据系统中已有的AI报告生成代码整合
    
    // 示例: 更新报告状态
    await AIReport.query()
      .findById(reportId)
      .patch({
        status: 'PROCESSING'
      });
    
    // TODO: 实际AI生成逻辑
    
    // 生成完成后更新状态
    await AIReport.query()
      .findById(reportId)
      .patch({
        status: 'COMPLETED',
        updatedAt: new Date().toISOString()
      });
  } catch (error) {
    console.error(`生成报告 ${reportId} 失败:`, error);
    
    // 更新报告状态为失败
    await AIReport.query()
      .findById(reportId)
      .patch({
        status: 'FAILED',
        errorMessage: error.message,
        updatedAt: new Date().toISOString()
      });
  }
};

/**
 * 生成并下载扩展版PDF报告
 */
const generateExtendedPdf = async (req, res) => {
  const { serviceReportId } = req.params;
  const serviceUserId = req.user.id;
  
  try {
    // 查找服务报告
    const serviceReport = await ServiceReport.query()
      .findById(serviceReportId)
      .where('serviceUserId', serviceUserId)
      .withGraphFetched('aiReport');
      
    if (!serviceReport) {
      return res.status(404).json({
        success: false,
        message: '服务报告不存在或无权访问'
      });
    }
    
    // 检查AI报告状态
    if (!serviceReport.aiReport || serviceReport.aiReport.status !== 'COMPLETED') {
      return res.status(400).json({
        success: false,
        message: 'AI报告尚未生成完成，无法创建PDF'
      });
    }
    
    // TODO: 生成扩展版PDF的逻辑
    // 这里应该调用实际的PDF生成服务
    const pdfPath = `/reports/extended/${serviceReportId}.pdf`;
    
    // 更新服务报告的PDF路径
    await ServiceReport.query()
      .patchAndFetchById(serviceReportId, {
        pdfPath,
        updatedAt: new Date().toISOString()
      });
    
    res.json({
      success: true,
      data: { pdfPath },
      message: '扩展版PDF报告已生成'
    });
  } catch (error) {
    console.error('生成扩展版PDF失败:', error);
    res.status(500).json({
      success: false,
      message: '生成扩展版PDF失败',
      error: error.message
    });
  }
};

/**
 * 获取授权用户的报告列表
 */
const getAuthorizedUserReports = async (req, res) => {
  try {
    const userId = req.user.id;
    const { authorizationId } = req.params;
    
    // 验证授权关系
    const authorization = await UserAuthorization.query()
      .findById(authorizationId)
      .where('authorizedId', userId)
      .where('status', 'ACTIVE');
      
    if (!authorization) {
      return res.status(403).json({
        success: false,
        message: '没有有效的授权关系'
      });
    }
    
    // 检查授权级别
    const privacyLevel = authorization.privacyLevel;
    
    // 基础授权只能查看授权信息，不能查看报告列表
    if (privacyLevel === 'BASIC') {
      return res.status(403).json({
        success: false,
        message: '基础授权级别不允许访问报告列表'
      });
    }
    
    // 查询条件
    let reportsQuery = AIReport.query()
      .where('userId', authorization.authorizerId)
      .where('isDeleted', false); // 不显示已删除的报告
      
    // 如果授权限定了特定患者
    if (authorization.patientId) {
      reportsQuery = reportsQuery.where('patientId', authorization.patientId);
    }
    
    // 标准授权只能查看非隐私病理的报告
    if (privacyLevel === 'STANDARD') {
      // 获取非隐私病理的ID
      const publicDiseases = await Disease.query()
        .where('userId', authorization.authorizerId)
        .where('isPrivate', false)
        .select('id');
        
      const publicDiseaseIds = publicDiseases.map(d => d.id);
      reportsQuery = reportsQuery.whereIn('diseaseId', publicDiseaseIds);
    }
    
    // 查询用户报告
    const reports = await reportsQuery
      .withGraphFetched('[patient, disease]')
      .orderBy('createdAt', 'desc');
    
    res.json({
      success: true,
      data: reports
    });
  } catch (error) {
    console.error('获取授权用户报告失败:', error);
    res.status(500).json({
      success: false,
      message: '获取授权用户报告失败',
      error: error.message
    });
  }
};

module.exports = {
  getServiceReports,
  createServiceReport,
  generateExtendedPdf,
  getAuthorizedUserReports
}; 