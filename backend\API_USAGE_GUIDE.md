# API使用指南

## 目录
1. [API路径约定](#api路径约定)
2. [认证机制](#认证机制)
3. [请求与响应格式](#请求与响应格式)
4. [API端点列表](#api端点列表)
5. [错误处理](#错误处理)
6. [最佳实践](#最佳实践)
7. [常见问题](#常见问题)
8. [API重构变更](#api重构变更)

## API路径约定

### 基本规则

1. **双路径支持**：所有API端点同时支持两种路径格式
   - 带`/api`前缀：`/api/login`、`/api/users/profile`
   - 不带前缀：`/login`、`/users/profile`

2. **命名约定**
   - 路径使用小写字母和连字符(-)
   - 复数形式表示资源集合：`/patients`、`/diseases`
   - 单数形式表示特定操作：`/user/profile`

3. **路径格式**
   - 资源集合：`/资源名称`
   - 特定资源：`/资源名称/:id`
   - 特定资源的操作：`/资源名称/:id/操作名称`
   - 用户操作：`/user/操作名称`

### 路径前缀选择

- 前端默认使用`/api`前缀的路径
- 如需使用不带前缀的路径，请使用`authApiClient`而非默认的`apiClient`

## 认证机制

### JWT令牌认证

所有需要认证的API调用必须在HTTP请求头中包含JWT令牌：

```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 认证流程

1. 用户登录时，服务器返回JWT令牌
2. 前端存储令牌（localStorage或其他安全存储）
3. 后续请求在Authorization头中包含令牌
4. 令牌过期时（401响应）需重新登录获取新令牌

### 令牌信息

JWT令牌包含以下信息：
- 用户ID
- 用户名
- 角色
- 级别
- 过期时间（默认24小时）

## 请求与响应格式

### 请求格式

大多数API使用JSON格式进行数据交换：

```json
{
  "username": "exampleUser",
  "password": "securePassword123"
}
```

### 响应格式

成功响应例子：

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "username": "exampleUser",
  "email": "<EMAIL>",
  "phoneNumber": "13800138000",
  "role": "USER",
  "level": "PERSONAL"
}
```

错误响应例子：

```json
{
  "error": "用户名或密码错误"
}
```

## API端点列表

### 认证相关

| 路径                      | 方法 | 需要认证 | 描述                  |
|--------------------------|------|---------|---------------------|
| `/login`                 | POST | 否      | 用户登录              |
| `/register`              | POST | 否      | 用户注册              |
| `/user/profile`          | GET  | 是      | 获取当前用户信息        |
| `/user/change-password`  | POST | 是      | 修改密码              |

### 患者相关

| 路径                       | 方法   | 需要认证 | 描述                   |
|---------------------------|-------|---------|----------------------|
| `/patients`               | GET   | 是      | 获取患者列表            |
| `/patients`               | POST  | 是      | 创建新患者              |
| `/patients/:id`           | GET   | 是      | 获取特定患者详情         |
| `/patients/:id`           | PUT   | 是      | 更新患者信息            |
| `/patients/:id`           | DELETE| 是      | 删除患者               |

### 疾病相关

| 路径                       | 方法   | 需要认证 | 描述                   |
|---------------------------|-------|---------|----------------------|
| `/diseases`               | GET   | 是      | 获取疾病列表            |
| `/diseases`               | POST  | 是      | 创建新疾病              |
| `/diseases/:id`           | GET   | 是      | 获取特定疾病详情         |
| `/diseases/:id`           | PUT   | 是      | 更新疾病信息            |
| `/diseases/:id`           | DELETE| 是      | 删除疾病               |

### 授权相关

| 路径                                  | 方法   | 需要认证 | 描述                      |
|--------------------------------------|-------|---------|--------------------------|
| `/authorizations/as-authorizer`      | GET   | 是      | 获取用户作为授权人的授权列表  |
| `/authorizations/as-authorized`      | GET   | 是      | 获取用户作为被授权人的授权列表 |
| `/authorizations`                    | POST  | 是      | 创建新授权                  |
| `/authorizations/:id/status`         | PATCH | 是      | 更新授权状态                |
| `/authorizations/:id/privacy-level`  | PATCH | 是      | 更新授权隐私级别             |
| `/authorizations/:id`                | DELETE| 是      | 删除授权记录                |

## 错误处理

### 常见HTTP状态码

| 状态码 | 描述                 | 处理方法                        |
|-------|---------------------|--------------------------------|
| 200   | 成功                 | 处理返回的数据                   |
| 201   | 创建成功              | 处理返回的新资源数据              |
| 400   | 请求参数错误           | 检查请求参数是否正确              |
| 401   | 认证失败              | 重新登录或更新令牌                |
| 403   | 权限不足              | 提示用户无权访问                  |
| 404   | 资源不存在            | 检查资源ID是否正确                |
| 500   | 服务器错误            | 联系系统管理员或稍后重试            |

### 错误响应格式

所有错误响应都使用以下格式：

```json
{
  "error": "错误描述信息"
}
```

## 最佳实践

### 前端API调用

1. **统一使用API配置**

```typescript
import { API_PATHS } from '../config/apiPaths';
import apiClient from '../services/apiClient';

// 获取用户信息
const getUserProfile = async () => {
  try {
    const response = await apiClient.get(API_PATHS.USER.PROFILE);
    return response.data;
  } catch (error) {
    // 错误处理
    console.error('获取用户信息失败:', error);
    throw error;
  }
};
```

2. **添加API调用超时**

```typescript
const response = await apiClient.get(path, { timeout: 10000 });
```

3. **统一错误处理**

```typescript
try {
  const response = await apiClient.get(path);
  return response.data;
} catch (error) {
  if (error.response) {
    // 服务器返回错误状态码
    if (error.response.status === 401) {
      // 处理认证错误
      redirectToLogin();
    } else {
      // 处理其他错误
      showErrorMessage(error.response.data.error || '操作失败');
    }
  } else if (error.request) {
    // 请求发送但未收到响应
    showErrorMessage('网络连接问题，请检查网络连接');
  } else {
    // 请求设置错误
    showErrorMessage('请求配置错误');
  }
  throw error;
}
```

### 后端API实现

1. **使用统一路由注册**

```javascript
const authRoutes = require('../routes/authRoutes');
const patientRoutes = require('../routes/patientRoutes');

// 注册路由，同时支持带/api前缀和不带前缀
function registerDualPrefixRoute(path, router) {
  app.use(path, router);
  app.use(`/api${path}`, router);
}

registerDualPrefixRoute('', authRoutes); // 处理 /login, /register 等
registerDualPrefixRoute('/patients', patientRoutes);
```

2. **统一错误处理**

```javascript
router.post('/login', async (req, res) => {
  try {
    // 业务逻辑
  } catch (error) {
    console.error('登录错误:', error);
    res.status(500).json({ error: '服务器错误' });
  }
});
```

## 常见问题

### 1. 收到401未授权错误

**原因**: JWT令牌已过期或无效

**解决方案**:
- 重新登录获取新令牌
- 确认令牌已正确添加到请求头中
- 检查token是否被正确存储

### 2. API请求返回404找不到

**原因**: 请求路径可能不正确，或前缀使用不一致

**解决方案**:
- 检查API路径是否正确
- 确认是否使用了正确的前缀（带 `/api` 或不带）
- 使用API配置中心提供的路径常量

### 3. 请求参数格式错误(400)

**原因**: 请求参数不符合后端要求

**解决方案**:
- 检查请求参数的键名和值类型
- 确保日期格式正确（ISO 8601格式）
- 查看API文档中的参数要求 

## API重构变更

### 路由结构优化

我们对API路由进行了以下优化：

1. **路由文件独立化**：
   - 认证相关路由移至 `routes/authRoutes.js`
   - 用户限制相关路由移至 `routes/userLimitRoutes.js`
   - 标签相关路由移至 `routes/tagRoutes.js`
   - 其他资源已有独立路由文件

2. **统一路由注册方式**：
   - 使用 `registerDualPrefixRoute` 函数注册所有路由
   - 确保每个路由同时支持带`/api`前缀和不带前缀的路径

3. **路由命名规范**：
   - 资源集合类型路由统一使用复数形式：`/patients`, `/diseases`
   - 用户操作类型路由统一使用单数形式：`/user/profile`, `/user/limits`
   - 认证类型路由使用无前缀单词：`/login`, `/register`

### 主要路由变更

| 旧路径                    | 新路径                    | 处理文件                |
|--------------------------|--------------------------|------------------------|
| `/login`                 | `/login`, `/api/login`   | `routes/authRoutes.js` |
| `/register`              | `/register`, `/api/register` | `routes/authRoutes.js` |
| `/user/profile`          | `/user/profile`, `/api/user/profile` | `routes/userLimitRoutes.js` |
| `/user/limits`           | `/user/limits`, `/api/user/limits` | `routes/userLimitRoutes.js` |
| `/user/change-password`  | `/user/change-password`, `/api/user/change-password` | `routes/authRoutes.js` |
| `/tags`                  | `/tags`, `/api/tags`     | `routes/tagRoutes.js`  |
| `/records`               | `/records`, `/api/records` | `routes/recordRoutes.js` |
| `/authorizations`        | `/authorizations`, `/api/authorizations` | `routes/authorization.js` |

### 前端适配

前端代码已经适配了这些变更，主要调整：

1. 统一使用 `API_PATHS` 配置中定义的路径
2. 移除了对 `apiPathDetector.getApiPath()` 的直接调用
3. 确保 `apiClient` 使用统一的基础URL（始终带有`/api`前缀）

### 兼容性保证

为确保兼容性，我们采取了以下措施：

1. 保留了双路径支持，所有端点同时支持带前缀和不带前缀的访问
2. 使用了相同的请求体和响应格式
3. 确保API返回的错误格式保持一致 