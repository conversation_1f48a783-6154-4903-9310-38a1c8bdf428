import axios from 'axios';
import { useAuthStore } from '../store/authStore';
import { setupInterceptors } from '../utils/apiInterceptor';
import { API_PATHS } from '../config/apiPaths';

// 确定API的基础URL
let baseURL = '';  // 使用相对路径

// 如果是开发环境，使用本地URL
if (process.env.NODE_ENV === 'development') {
  baseURL = 'http://localhost:3001';
} else if (process.env.NODE_ENV === 'production') {
  // 生产环境默认使用HTTPS
  baseURL = 'https://hkb.life';
}

// 如果配置了环境变量，优先使用环境变量
if (process.env.REACT_APP_API_URL) {
  baseURL = process.env.REACT_APP_API_URL.replace(/\/$/, '');
  baseURL = baseURL.replace(/\/api$/, '');
}

// 创建axios实例
const apiClient = axios.create({
  baseURL: baseURL,
  timeout: 90000,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// 设置请求拦截器
setupInterceptors(apiClient);

// 添加请求重试逻辑
apiClient.interceptors.response.use(undefined, async (error) => {
  const config = error.config;

  // 如果没有设置重试配置，则设置默认值
  if (!config || !config.retry) {
    config.retry = 3;
    config.retryDelay = 1000;
  }

  // 如果还有重试次数
  if (config.retry > 0) {
    // 延迟重试
    await new Promise(resolve => setTimeout(resolve, config.retryDelay));

    // 减少重试次数
    config.retry -= 1;

    // 重试请求
    return apiClient(config);
  }

  return Promise.reject(error);
});

// 添加请求取消功能
const pendingRequests = new Map();

// 生成请求的唯一键
const generateRequestKey = (config: any) => {
  const { url, method, params, data } = config;
  return [url, method, JSON.stringify(params), JSON.stringify(data)].join('&');
};

// 添加请求取消拦截器
apiClient.interceptors.request.use(
  (config) => {
    const requestKey = generateRequestKey(config);

    // 如果存在相同的请求，则取消之前的请求
    if (pendingRequests.has(requestKey)) {
      const controller = pendingRequests.get(requestKey);
      controller.abort();
      pendingRequests.delete(requestKey);
    }

    // 创建新的AbortController
    const controller = new AbortController();
    config.signal = controller.signal;

    // 保存请求
    pendingRequests.set(requestKey, controller);

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 添加响应拦截器，清理已完成的请求
apiClient.interceptors.response.use(
  (response) => {
    const requestKey = generateRequestKey(response.config);
    pendingRequests.delete(requestKey);
    return response;
  },
  (error) => {
    if (error.config) {
      const requestKey = generateRequestKey(error.config);
      pendingRequests.delete(requestKey);
    }
    return Promise.reject(error);
  }
);

// 添加详细的日志
console.log(`========= API 客户端配置 =========`);
console.log(`基础URL: ${baseURL}`);
console.log(`实际请求baseURL: ${apiClient.defaults.baseURL}`);
console.log(`环境: ${process.env.NODE_ENV}`);
console.log(`REACT_APP_API_URL: ${process.env.REACT_APP_API_URL || '未设置'}`);
console.log(`使用withCredentials: ${apiClient.defaults.withCredentials}`);
console.log(`===================================`);

// 添加标签服务专用的apiClient
export const tagsApiClient = axios.create({
  // 直接使用baseURL，不重复添加/api前缀
  baseURL: baseURL,
  timeout: 90000,
  withCredentials: false,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// 为tagsApiClient添加相同的拦截器
tagsApiClient.interceptors.request.use(
  config => {
    const token = useAuthStore.getState().token || localStorage.getItem('token');

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    } else {
      console.warn(`[tagsApiClient] 请求未包含token: ${config.url}`);
    }

    // 修改URL处理，确保路径正确
    const url = config.url || '';

    // 只有当url不是以/tags开头时才添加/tags前缀
    if (!url.startsWith('/tags')) {
      config.url = `/tags${url}`;
    }

    // 在开发环境中添加详细的请求日志
    if (process.env.NODE_ENV === 'development') {
      // 记录实际请求的完整URL和请求头信息
      console.log(`[tagsApiClient] 请求详情:`, {
        完整URL: `${config.baseURL}${config.url}`,
        方法: config.method?.toUpperCase(),
        请求头: {
          Authorization: token ? '存在' : '不存在',
          ContentType: config.headers['Content-Type']
        },
        数据: config.data ? '存在' : '不存在'
      });
    }

    return config;
  },
  error => {
    console.error('[tagsApiClient] 请求错误:', error);
    return Promise.reject(error);
  }
);

tagsApiClient.interceptors.response.use(
  response => {
    console.log(`[tagsApiClient] 响应成功: ${response.config.method?.toUpperCase()} ${response.config.url}`);
    return response;
  },
  error => {
    console.error('[tagsApiClient] 请求失败:', error);
    return Promise.reject(error);
  }
);

// 请求拦截器：在请求头中添加token
apiClient.interceptors.request.use(
  config => {
    // 从authStore获取token
    const token = useAuthStore.getState().token || localStorage.getItem('token');

    if (token) {
      // 设置请求头
      config.headers.Authorization = `Bearer ${token}`;

      // 记录完整的请求URL和参数
      console.log(`[apiClient] 完整请求URL: ${config.baseURL}${config.url}`, {
        方法: config.method?.toUpperCase(),
        参数: config.params,
        数据: config.data ? '存在' : '不存在',
        headers: {
          Authorization: 'Bearer ***',
          ContentType: config.headers['Content-Type']
        }
      });
    } else {
      console.warn(`[apiClient] 请求未包含token: ${config.url}`);
    }

    // 处理PDF下载请求
    if (config.url?.includes('/pdf')) {
      config.responseType = 'blob';
      config.headers['Accept'] = 'application/pdf';
    }

    // 仅在开发环境记录请求详情
    if (process.env.NODE_ENV === 'development') {
      console.log(`[apiClient] 请求: ${config.method?.toUpperCase()} ${config.url}`);
    }

    return config;
  },
  error => {
    console.error('[apiClient] 请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  response => {
    // 添加更详细的成功响应日志
    console.log(`[apiClient] 响应成功: ${response.config.method?.toUpperCase()} ${response.config.url}`, {
      状态码: response.status,
      数据键: response.data ? Object.keys(response.data) : [],
      时间戳: new Date().toISOString()
    });
    return response;
  },
  async error => {
    // 添加详细的错误日志
    if (error.response) {
      console.error(`[apiClient] 请求失败: ${error.config?.method?.toUpperCase()} ${error.config?.url}`, {
        状态码: error.response.status,
        响应数据: error.response.data,
        请求URL: error.config?.url,
        完整URL: error.config?.baseURL + error.config?.url,
        时间戳: new Date().toISOString()
      });
    } else if (error.request) {
      // 请求已发送但未收到响应
      console.error(`[apiClient] 无响应: ${error.config?.method?.toUpperCase()} ${error.config?.url}`, {
        错误消息: error.message,
        请求配置: {
          baseURL: error.config?.baseURL,
          url: error.config?.url,
          完整URL: error.config?.baseURL + error.config?.url,
          方法: error.config?.method?.toUpperCase(),
          超时: error.config?.timeout
        },
        时间戳: new Date().toISOString()
      });
    } else {
      // 请求设置时出错
      console.error(`[apiClient] 请求错误:`, {
        错误消息: error.message,
        堆栈: error.stack,
        时间戳: new Date().toISOString()
      });
    }

    const originalRequest = error.config || {};

    // 记录网络错误（包括CORS错误）
    if (error.message === 'Network Error' || error.code === 'ERR_NETWORK') {
      console.error(`[apiClient] 网络错误: ${originalRequest.url || '未知URL'}`);

      // 检查当前是否在服务页面 - 使用简化的检测方法
      const currentPath = window.location.pathname;
      const isServicePage = currentPath.includes('/service');

      // 给错误添加标记，但不进行重定向
      error.isNetworkError = true;
      error.isServicePage = isServicePage;

      return Promise.reject(error);
    }

    // 处理错误响应
    if (error.response) {
      // 401错误 - 未授权
      if (error.response.status === 401 && !originalRequest._retry) {
        // 标记原始请求已经尝试过重试，防止循环
        originalRequest._retry = true;

        // 检查请求路径和当前页面路径 - 简化判断
        const isAuthorizationPath = originalRequest.url?.includes('authorization');
        const currentPath = window.location.pathname;
        const isServicePage = currentPath.includes('/service');

        // 仅在开发环境记录详细401错误
        if (process.env.NODE_ENV === 'development') {
          console.error(`[apiClient] 401错误: ${originalRequest.url}, 当前页面: ${currentPath}`);
        }

        // 服务相关页面的401错误特殊处理，不进行重定向而是让页面自己处理
        if (isServicePage || isAuthorizationPath) {
          // 为错误对象添加特殊标记，供页面组件判断
          error.isAuthError = true;
          error.isServicePage = isServicePage;
          error.isAuthorizationPath = isAuthorizationPath;
          return Promise.reject(error);
        }

        // 保存当前路径，以便登录后可以返回
        if (currentPath !== '/login') {
          localStorage.setItem('redirectAfterLogin', currentPath);
        }

        // 清除认证信息
        useAuthStore.getState().clearAuth();

        // 重定向到登录页
        if (currentPath !== '/login') {
          window.location.href = '/login';
          return new Promise(() => {}); // 阻止后续错误处理
        }
      }
    }

    return Promise.reject(error);
  }
);

// 创建一个存储全局状态的对象 - 避免直接在非Hook中使用Hook
const globalStateStore = {
  getToken: () => localStorage.getItem('token'),
  getServiceContext: () => {
    // 从localStorage获取当前操作模式，默认为普通模式
    const mode = localStorage.getItem('operation_mode') || 'NORMAL';

    // 只有当前操作模式是SERVICE或ADMIN时才返回true
    return mode === 'SERVICE' || mode === 'ADMIN';
  }
};

// 导出API客户端
export default apiClient;

// 导出API路径
export { API_PATHS };

// 导出请求统计函数
export { getRequestStats, resetRequestStats } from '../utils/apiInterceptor';