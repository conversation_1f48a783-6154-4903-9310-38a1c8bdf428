module.exports = {
    apps: [
        {
            name: 'backend-services',
            script: 'start-servers.js',
            instances: 4,
            exec_mode: 'cluster',
            autorestart: true,
            watch: false,
            max_memory_restart: '1G',
            env: {
                NODE_ENV: 'production',
                DATABASE_HOST: process.env.DATABASE_HOST,
                DATABASE_USER: process.env.DATABASE_USER,
                DATABASE_PASSWORD: process.env.DATABASE_PASSWORD,
                DATABASE_NAME: process.env.DATABASE_NAME,
                JWT_SECRET: process.env.JWT_SECRET,
                LOG_LEVEL: 'info',
            },
            output: './logs/console.log',
            error: './logs/error.log',
            merge_logs: true,
            log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
            env_production: {
                PORT: '3001',
                WS_PORT: '3005',
                NODE_ENV: 'production'
            },
            env_0: {
                PORT: '3001',
                WS_PORT: '3005',
                NODE_ENV: 'production'
            },
            env_1: {
                PORT: '3002',
                WS_PORT: '3006',
                NODE_ENV: 'production'
            },
            env_2: {
                PORT: '3003',
                WS_PORT: '3007',
                NODE_ENV: 'production'
            },
            env_3: {
                PORT: '3004',
                WS_PORT: '3008',
                NODE_ENV: 'production'
            }
        }
    ]
};