/**
 * AI报告数据验证和修复脚本
 */
const { Model } = require('objection');
const Knex = require('knex');
const knexConfig = require('../knexfile');
const { AIReport } = require('../models/aiReport');

// 初始化数据库连接
const knex = Knex(knexConfig.development || knexConfig);
Model.knex(knex);

const validateAIReports = async () => {
  console.log('开始验证AI报告数据...');
  const reports = await AIReport.query();
  console.log(`总共找到${reports.length}条报告`);
  
  let validCount = 0;
  let invalidCount = 0;
  let fixedCount = 0;
  let errorDetails = [];
  
  for (const report of reports) {
    try {
      console.log(`检查报告 ${report.id}, 标题: ${report.title || '无标题'}`);
      
      // 检查content字段
      if (!report.content) {
        console.error(`报告 ${report.id} content为空`);
        errorDetails.push({ id: report.id, error: 'content字段为空' });
        invalidCount++;
        continue;
      }
      
      if (typeof report.content === 'string') {
        try {
          // 尝试解析JSON字符串
          const parsedContent = JSON.parse(report.content);
          
          // 检查关键字段是否存在
          if (!parsedContent.summary) parsedContent.summary = "";
          if (!parsedContent.differentialDiagnosis) {
            parsedContent.differentialDiagnosis = { possibleConditions: [] };
          }
          if (!parsedContent.emergencyGuidance) {
            parsedContent.emergencyGuidance = { 
              isEmergency: false, 
              immediateActions: [], 
              nextSteps: [] 
            };
          }
          if (!parsedContent.hospitalRecommendations) {
            parsedContent.hospitalRecommendations = { hospitals: [] };
          }
          if (!parsedContent.treatmentPlan) {
            parsedContent.treatmentPlan = { options: [] };
          }
          if (!parsedContent.lifestyleAndMentalHealth) {
            parsedContent.lifestyleAndMentalHealth = {
              lifestyle: { diet: [], exercise: [], habits: [] },
              mentalHealth: { copingStrategies: [], resources: [] }
            };
          }
          if (!parsedContent.dashboardData) {
            parsedContent.dashboardData = {
              status: '处理中',
              trend: 'stable',
              riskLevel: 'low',
              isEmergency: false,
              topHospital: '',
              budgetRange: ''
            };
          }
          if (!parsedContent.riskWarnings) {
            parsedContent.riskWarnings = [];
          }
          if (parsedContent.is_chronic_disease === undefined) {
            parsedContent.is_chronic_disease = false;
          }
          
          // 更新数据库
          await AIReport.query().findById(report.id).patch({
            content: parsedContent,
            status: 'COMPLETED'  // 只修复内容正常的记录状态
          });
          
          console.log(`修复报告: ${report.id} - 将字符串转换为JSON对象`);
          fixedCount++;
        } catch (parseErr) {
          console.error(`报告 ${report.id} content解析失败:`, parseErr.message);
          errorDetails.push({ 
            id: report.id, 
            error: `JSON解析错误: ${parseErr.message}`,
            contentSample: report.content.substring(0, 100) + '...'
          });
          invalidCount++;
        }
      } else if (report.content && typeof report.content === 'object') {
        // 检查关键字段是否存在
        const content = { ...report.content };
        let updated = false;
        
        if (!content.summary) {
          content.summary = "";
          updated = true;
        }
        if (!content.differentialDiagnosis) {
          content.differentialDiagnosis = { possibleConditions: [] };
          updated = true;
        }
        if (!content.emergencyGuidance) {
          content.emergencyGuidance = { 
            isEmergency: false, 
            immediateActions: [], 
            nextSteps: [] 
          };
          updated = true;
        }
        if (!content.hospitalRecommendations) {
          content.hospitalRecommendations = { hospitals: [] };
          updated = true;
        }
        if (!content.treatmentPlan) {
          content.treatmentPlan = { options: [] };
          updated = true;
        }
        if (!content.lifestyleAndMentalHealth) {
          content.lifestyleAndMentalHealth = {
            lifestyle: { diet: [], exercise: [], habits: [] },
            mentalHealth: { copingStrategies: [], resources: [] }
          };
          updated = true;
        }
        if (!content.dashboardData) {
          content.dashboardData = {
            status: '处理中',
            trend: 'stable',
            riskLevel: 'low',
            isEmergency: false,
            topHospital: '',
            budgetRange: ''
          };
          updated = true;
        }
        if (!content.riskWarnings) {
          content.riskWarnings = [];
          updated = true;
        }
        if (content.is_chronic_disease === undefined) {
          content.is_chronic_disease = false;
          updated = true;
        }
        
        if (updated) {
          // 更新数据库
          await AIReport.query().findById(report.id).patch({
            content: content
          });
          console.log(`修复报告: ${report.id} - 补充缺失字段`);
          fixedCount++;
        } else {
          console.log(`报告 ${report.id} 数据格式正确`);
          validCount++;
        }
      } else {
        console.error(`报告 ${report.id} content字段无效: ${typeof report.content}`);
        errorDetails.push({ id: report.id, error: `无效的content类型: ${typeof report.content}` });
        invalidCount++;
      }
    } catch (err) {
      console.error(`处理报告失败 ${report.id}:`, err.message);
      errorDetails.push({ id: report.id, error: `处理错误: ${err.message}` });
      invalidCount++;
    }
  }
  
  console.log('验证完成:');
  console.log(`- 有效报告: ${validCount}`);
  console.log(`- 已修复报告: ${fixedCount}`);
  console.log(`- 无效报告: ${invalidCount}`);
  
  if (errorDetails.length > 0) {
    console.log('错误详情:');
    console.log(JSON.stringify(errorDetails, null, 2));
  }
  
  // 关闭数据库连接
  await knex.destroy();
};

validateAIReports()
  .then(() => {
    console.log('验证脚本执行完成');
    process.exit(0);
  })
  .catch(err => {
    console.error('验证脚本执行失败:', err);
    process.exit(1);
  }); 