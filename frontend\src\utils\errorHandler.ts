import { AxiosError } from 'axios';

/**
 * 标准化API错误对象
 */
export interface ApiError {
  message: string;
  code?: string;
  status?: number;
  details?: any;
}

/**
 * API响应数据接口
 */
interface ApiErrorResponse {
  message?: string;
  code?: string;
  details?: any;
  [key: string]: any;
}

/**
 * 从各种错误类型中提取可读的错误信息
 * @param error - 任何类型的错误对象
 * @returns 标准化的错误对象
 */
export const handleApiError = (error: unknown): ApiError => {
  // 处理Axios错误
  if (isAxiosError(error)) {
    const status = error.response?.status;
    const data = error.response?.data as ApiErrorResponse | undefined;
    
    // 如果响应中包含错误信息，优先使用
    if (data && typeof data === 'object') {
      return {
        message: data.message || `请求失败 (${status})`,
        code: data.code || String(status || 'UNKNOWN'),
        status,
        details: data.details || data
      };
    }
    
    // 一般HTTP错误处理
    if (status === 401) {
      return { message: '您的登录已过期，请重新登录', code: 'UNAUTHORIZED', status };
    } else if (status === 403) {
      return { message: '您没有权限执行此操作', code: 'FORBIDDEN', status };
    } else if (status === 404) {
      return { message: '请求的资源不存在', code: 'NOT_FOUND', status };
    } else if (status && status >= 500) {
      return { message: '服务器错误，请稍后再试', code: 'SERVER_ERROR', status };
    }
    
    return {
      message: error.message || '网络请求失败',
      code: String(status || 'NETWORK_ERROR'),
      status
    };
  }
  
  // 处理标准Error对象
  if (error instanceof Error) {
    return {
      message: error.message || '操作失败',
      code: 'ERROR'
    };
  }
  
  // 处理字符串
  if (typeof error === 'string') {
    return {
      message: error,
      code: 'ERROR'
    };
  }
  
  // 处理未知类型
  return {
    message: '未知错误',
    code: 'UNKNOWN_ERROR'
  };
};

/**
 * 类型保护函数，判断错误是否为Axios错误
 */
function isAxiosError(error: any): error is AxiosError {
  return error && typeof error === 'object' && 'isAxiosError' in error;
}

/**
 * 在控制台记录错误，并根据环境进行格式化
 * @param error - 错误对象
 * @param context - 错误上下文说明
 */
export const logError = (error: unknown, context: string): void => {
  const apiError = handleApiError(error);
  
  if (process.env.NODE_ENV === 'development') {
    console.error(`[${context}] ${apiError.message}`, error);
  } else {
    console.error(`[${context}] ${apiError.message}`);
  }
  
  // 这里可以添加错误监控服务的集成，如Sentry等
};

// 修改默认导出为命名对象
const errorHandler = {
  handleApiError,
  logError
};

export default errorHandler; 