/**
 * 创建标签相关表
 */
exports.up = function(knex) {
  return knex.schema
    // 创建标签分类表
    .createTable('tag_categories', (table) => {
      table.uuid('id').primary();
      table.string('name').notNullable();
      table.string('description').nullable();
      table.string('color').nullable();
      table.boolean('is_system').defaultTo(false).comment('是否系统预设分类');
      table.uuid('created_by').nullable().references('id').inTable('users').onDelete('SET NULL');    
      table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());
      table.timestamp('updated_at', { useTz: true }).defaultTo(knex.fn.now());
    })
    // 创建标签表
    .createTable('tags', (table) => {
      table.uuid('id').primary();
      table.string('name').notNullable();
      table.string('description').nullable();
      table.string('color').nullable();
      table.uuid('category_id').notNullable().references('id').inTable('tag_categories').onDelete('CASCADE').comment('所属分类ID');
      table.uuid('created_by').nullable().references('id').inTable('users').onDelete('SET NULL');    
      table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());
      table.timestamp('updated_at', { useTz: true }).defaultTo(knex.fn.now());
      
      // 索引和约束
      table.unique(['name', 'category_id']);
    })
    // 创建记录-标签关联表
    .createTable('record_tags', (table) => {
      table.uuid('id').primary();
      table.uuid('record_id').notNullable().references('id').inTable('records').onDelete('CASCADE').comment('记录ID');
      table.uuid('tag_id').notNullable().references('id').inTable('tags').onDelete('CASCADE').comment('标签ID');
      table.uuid('created_by').nullable().references('id').inTable('users').onDelete('SET NULL');    
      table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());
      table.timestamp('updated_at', { useTz: true }).defaultTo(knex.fn.now());
      
      // 索引和约束
      table.unique(['record_id', 'tag_id']);
    });
};

exports.down = function(knex) {
  return knex.schema
    .dropTableIfExists('record_tags')
    .dropTableIfExists('tags')
    .dropTableIfExists('tag_categories');
}; 