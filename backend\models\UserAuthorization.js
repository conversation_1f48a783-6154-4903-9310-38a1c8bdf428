const { Model } = require('objection');
const { v4: uuidv4 } = require('uuid');
const knexInstance = require('../src/db').knex;

class UserAuthorization extends Model {
  static get tableName() {
    return 'user_authorizations';
  }

  static get idColumn() {
    return 'id';
  }

  // 明确定义数据库列映射
  static get columnNameMappers() {
    return {
      parse(obj) {
        // 从数据库转换为模型
        return obj;
      },
      format(obj) {
        // 从模型转换为数据库
        return obj;
      }
    };
  }

  $beforeInsert() {
    if (!this.id) {
      this.id = uuidv4();
    }
    
    const now = new Date().toISOString();
    this.created_at = now;
    this.updated_at = now;
  }

  $beforeUpdate() {
    this.updated_at = new Date().toISOString();
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['id', 'status'],
      properties: {
        id: { type: 'string' },
        authorizer_id: { type: 'string' },
        authorized_id: { type: 'string' },
        status: { type: 'string' },
        privacy_level: { type: 'string' },
        patient_id: { type: 'string' },
        created_by: { type: 'string' },
        has_new_notification: { type: 'boolean' },
        created_at: { type: 'string' },
        updated_at: { type: 'string' },
        activated_at: { type: 'string' },
        revoked_at: { type: ['string', 'null'] },
        authorizer_switch: { type: 'boolean' },
        authorized_switch: { type: 'boolean' },
        status_changed_at: { type: ['string', 'null'] },
      }
    };
  }

  static get relationMappings() {
    const User = require('./User');
    const Patient = require('./Patient');
    const ServiceRecord = require('./ServiceRecord');
    const ServiceReport = require('./ServiceReport');
    
    return {
      // 授权人
      authorizer: {
        relation: Model.BelongsToOneRelation,
        modelClass: User,
        join: {
          from: 'user_authorizations.authorizer_id',
          to: 'users.id'
        }
      },
      
      // 被授权人
      authorized: {
        relation: Model.BelongsToOneRelation,
        modelClass: User,
        join: {
          from: 'user_authorizations.authorized_id',
          to: 'users.id'
        }
      },
      
      // 授权的患者
      patient: {
        relation: Model.BelongsToOneRelation,
        modelClass: Patient,
        join: {
          from: 'user_authorizations.patient_id',
          to: 'patients.id'
        }
      },
      
      // 创建者
      creator: {
        relation: Model.BelongsToOneRelation,
        modelClass: User,
        join: {
          from: 'user_authorizations.created_by',
          to: 'users.id'
        }
      },
      
      // 相关服务记录
      serviceRecords: {
        relation: Model.HasManyRelation,
        modelClass: ServiceRecord,
        join: {
          from: 'user_authorizations.id',
          to: 'service_records.authorization_id'
        }
      },
      
      // 相关服务报告
      serviceReports: {
        relation: Model.HasManyRelation,
        modelClass: ServiceReport,
        join: {
          from: 'user_authorizations.id',
          to: 'service_reports.authorization_id'
        }
      }
    };
  }
  
  // 方便访问Schema
  static knexSchema() {
    return knexInstance.schema;
  }
}

// 设置模型的knex实例
Model.knex(knexInstance);

module.exports = UserAuthorization; 