/**
 * 创建AI报告配额表
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.createTable('ai_report_quotas', table => {
    table.uuid('id').primary();
    table.uuid('user_id').notNullable().references('id').inTable('users').onDelete('CASCADE');
    table.integer('monthly_quota').notNullable().defaultTo(3).comment('用户每月可用的AI分析次数');
    table.integer('used_this_month').notNullable().defaultTo(0).comment('本月已使用的次数');
    table.integer('total_used').notNullable().defaultTo(0).comment('历史总使用次数');
    table.timestamp('last_reset_date', { useTz: true }).notNullable().defaultTo(knex.fn.now()).comment('上次重置配额的日期');
    table.integer('additional_quota').notNullable().defaultTo(0).comment('额外赠送或购买的次数');
    table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());
    table.timestamp('updated_at', { useTz: true }).defaultTo(knex.fn.now());

    // 添加索引和唯一约束
    table.unique('user_id', 'ai_report_quota_user_idx');
  });
};

/**
 * 删除AI报告配额表
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.dropTableIfExists('ai_report_quotas');
}; 