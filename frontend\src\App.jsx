import React, { useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { useAuthStore } from './store/authStore';
import apiPathDetector from './services/apiPathDetector';
import Login from './pages/Login';
// 添加其他组件导入...

function App() {
  const isAuthenticated = useAuthStore(state => !!state.token);
  const [apiPathReady, setApiPathReady] = useState(false);
  
  // 应用启动时进行API路径探测
  useEffect(() => {
    const detectApiPath = async () => {
      try {
        console.log('开始API路径探测...');
        const result = await apiPathDetector.detectApiPathFormat();
        console.log('API路径探测完成，最佳格式:', result ? '带/api前缀' : '不带/api前缀');
        setApiPathReady(true);
      } catch (error) {
        console.warn('API路径探测失败，将使用默认格式:', error);
        // 即使探测失败也标记为就绪，使用默认值
        setApiPathReady(true);
      }
    };
    
    detectApiPath();
  }, []);
  
  // 其他组件代码...
} 