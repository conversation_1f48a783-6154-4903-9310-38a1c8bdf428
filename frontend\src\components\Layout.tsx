import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  Typography,
  IconButton,
  Divider,
  useMediaQuery,
  useTheme,
  Avatar,
  Menu,
  MenuItem,
  ListItemIcon,
  Tooltip
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import CloseIcon from '@mui/icons-material/Close';
import PersonIcon from '@mui/icons-material/Person';
import ExitToAppIcon from '@mui/icons-material/ExitToApp';
import ZoomInMapIcon from '@mui/icons-material/ZoomInMap';
import ZoomOutMapIcon from '@mui/icons-material/ZoomOutMap';
import Brightness4Icon from '@mui/icons-material/Brightness4';
import Brightness7Icon from '@mui/icons-material/Brightness7';
import LockResetIcon from '@mui/icons-material/LockReset';
import SubscriptionsIcon from '@mui/icons-material/Subscriptions';
import NavMenu from './NavMenu';
import { useAuthStore } from '../store/authStore';
import { useThemeStore } from '../theme/themeStore';

// 抽屉宽度
const drawerWidth = 190;

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [mobileOpen, setMobileOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const { clearAuth, user } = useAuthStore();
  
  // 页面触摸缩放状态 - 从localStorage中获取初始状态，默认为锁定状态
  const [isZoomLocked, setIsZoomLocked] = useState(() => {
    const savedZoomState = localStorage.getItem('app-zoom-locked');
    // 默认是锁定状态 (true)
    return savedZoomState ? savedZoomState === 'locked' : true;
  });
  
  // 明暗模式状态 - 从主题存储获取
  const { isDarkMode, toggleTheme } = useThemeStore();

  // 应用触摸缩放锁定/解锁
  useEffect(() => {
    // 获取meta viewport标签
    const viewportMeta = document.querySelector('meta[name="viewport"]');
    if (viewportMeta) {
      if (isZoomLocked) {
        // 锁定缩放: 设置用户不能缩放
        viewportMeta.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
      } else {
        // 解锁缩放: 允许用户使用触摸手势缩放
        viewportMeta.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes');
      }
    }
  }, [isZoomLocked]);

  // 处理抽屉开关
  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  // 处理用户菜单打开
  const handleUserMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  // 处理用户菜单关闭
  const handleUserMenuClose = () => {
    setAnchorEl(null);
  };

  // 处理个人资料
  const handleProfile = () => {
    navigate('/profile');
    handleUserMenuClose();
  };

  // 处理密码修改
  const handleChangePassword = () => {
    navigate('/profile/change-password');
    handleUserMenuClose();
  };

  // 处理订阅信息
  const handleSubscription = () => {
    navigate('/profile/subscription');
    handleUserMenuClose();
  };

  // 处理注销
  const handleLogout = () => {
    clearAuth();
    navigate('/login');
    handleUserMenuClose();
  };

  // 处理页面缩放锁定切换
  const handleZoomToggle = () => {
    const newZoomLockState = !isZoomLocked;
    setIsZoomLocked(newZoomLockState);
    
    // 保存缩放状态到localStorage
    localStorage.setItem('app-zoom-locked', newZoomLockState ? 'locked' : 'unlocked');
  };

  // 处理明暗模式切换
  const handleDarkModeToggle = () => {
    toggleTheme(); // 使用主题存储的切换方法
  };

  // 获取用户角色显示文本
  const getRoleText = (role: string) => {
    switch(role) {
      case 'ADMIN': return '管理员';
      case 'SERVICE': return '服务';
      case 'USER': return '用户';
      default: return '用户';
    }
  };

  // 抽屉内容
  const drawerContent = (
    <>
      {isMobile && (
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', p: 1, py: 0.75 }}>
          <Typography variant="h6" noWrap component="div" sx={{ fontSize: '1.1rem' }}>
            慧看病
          </Typography>
          <IconButton onClick={handleDrawerToggle} size="small" sx={{ p: 0.5 }}>
            <CloseIcon />
          </IconButton>
        </Box>
      )}
      <Divider sx={{ mt: 0, mb: 0.5 }} />
      <Box sx={{ mt: 2.5 }}></Box> {/* 添加20px的上边距 */}
      <NavMenu onNavigate={isMobile ? handleDrawerToggle : undefined} />
    </>
  );

  return (
    <Box sx={{ display: 'flex' }}>
      {/* 顶部应用栏 */}
      <AppBar
        position="fixed"
        sx={{
          width: { md: `calc(100% - ${drawerWidth}px)` },
          ml: { md: `${drawerWidth}px` },
          zIndex: (theme) => theme.zIndex.drawer + 1
        }}
      >
        <Toolbar sx={{ minHeight: { xs: 56, md: 64 } }}> {/* 移动端更小的工具栏 */}
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ 
              mr: 2, 
              display: { md: 'none' },
              color: 'secondary.main', // 使用辅色
              '&:hover': {
                bgcolor: 'primary.main', // 使用主色系背景
              }
            }}
          >
            <MenuIcon />
          </IconButton>
          <Typography 
            variant="h6" 
            noWrap 
            component="div" 
            sx={{ 
              flexGrow: 1, 
              color: '#ffffff', // 改为纯白色以增加反差
              fontWeight: 700, // 增加字重
              textShadow: '0 1px 2px rgba(0,0,0,0.2)', // 添加文字阴影增加反差
              fontSize: { xs: '1.1rem', md: '1.25rem' } // 移动端更小的字体
            }}
          >
            慧看病
          </Typography>
          
          {/* 页面缩放锁定按钮 */}
          <Tooltip title={isZoomLocked ? "允许触摸缩放 (适合老年用户)" : "锁定触摸缩放 (保持最佳视觉效果)"}>
            <IconButton 
              color="inherit" 
              onClick={handleZoomToggle} 
              sx={{ 
                mr: 1,
                '&:hover': {
                  bgcolor: 'rgba(255, 111, 97, 0.15)', // 辅色半透明背景用于悬停效果
                }
              }}
            >
              {isZoomLocked ? <ZoomOutMapIcon /> : <ZoomInMapIcon />}
            </IconButton>
          </Tooltip>
          
          {/* 明暗模式切换按钮 */}
          <Tooltip title={isDarkMode ? "切换为亮色模式" : "切换为暗色模式"}>
            <IconButton 
              color="inherit" 
              onClick={handleDarkModeToggle} 
              sx={{ 
                mr: 2,
                '&:hover': {
                  bgcolor: 'rgba(255, 111, 97, 0.15)', // 辅色半透明背景用于悬停效果
                }
              }}
            >
              {isDarkMode ? <Brightness7Icon /> : <Brightness4Icon />}
            </IconButton>
          </Tooltip>
          
          {/* 用户信息区域 */}
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {user && (
              <Box sx={{ flexDirection: 'column', alignItems: 'flex-end', mr: 1, display: { xs: 'none', sm: 'flex' } }}>
                <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                  {user.username || '用户'}
                </Typography>
                <Typography variant="caption" sx={{ opacity: 0.8 }}>
                  {user.role ? getRoleText(user.role) : '用户'}
                </Typography>
              </Box>
            )}
            <Tooltip title="账户设置">
              <IconButton
                onClick={handleUserMenuOpen}
                color="inherit"
                size="small"
                sx={{ 
                  ml: { xs: 0, sm: 1 },
                  width: { xs: 40, md: 'auto' }, // 移动端更大的点击区域
                  height: { xs: 40, md: 'auto' }
                }}
              >
                <Avatar sx={{ 
                  width: { xs: 32, md: 36 }, // 移动端稍小的头像
                  height: { xs: 32, md: 36 },
                  bgcolor: 'secondary.main' 
                }}>
                  {user?.username ? user.username.charAt(0).toUpperCase() : 'U'}
                </Avatar>
              </IconButton>
            </Tooltip>
            <Menu
              anchorEl={anchorEl}
              id="account-menu"
              open={Boolean(anchorEl)}
              onClose={handleUserMenuClose}
              transformOrigin={{ horizontal: 'right', vertical: 'top' }}
              anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
              PaperProps={{
                elevation: 0,
                sx: {
                  overflow: 'visible',
                  filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
                  mt: 1.5,
                  '& .MuiAvatar-root': {
                    width: 28,
                    height: 28,
                    ml: -0.5,
                    mr: 1,
                  },
                  '&:before': {
                    content: '""',
                    display: 'block',
                    position: 'absolute',
                    top: 0,
                    right: 14,
                    width: 10,
                    height: 10,
                    bgcolor: 'background.paper',
                    transform: 'translateY(-50%) rotate(45deg)',
                    zIndex: 0,
                  },
                  // 移动端菜单选项更紧凑
                  '& .MuiMenuItem-root': {
                    py: { xs: 1, md: 0.75 },
                    minHeight: { xs: 38, md: 36 }
                  },
                  '& .MuiDivider-root': {
                    my: 0.5
                  }
                },
              }}
            >
              <MenuItem onClick={handleProfile}>
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <PersonIcon fontSize="small" />
                </ListItemIcon>
                个人资料
              </MenuItem>
              <MenuItem onClick={handleChangePassword}>
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <LockResetIcon fontSize="small" />
                </ListItemIcon>
                更改密码
              </MenuItem>
              <MenuItem onClick={handleSubscription}>
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <SubscriptionsIcon fontSize="small" />
                </ListItemIcon>
                订阅信息
              </MenuItem>
              <Divider />
              <MenuItem onClick={handleLogout}>
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <ExitToAppIcon fontSize="small" />
                </ListItemIcon>
                退出登录
              </MenuItem>
            </Menu>
          </Box>
        </Toolbar>
      </AppBar>

      {/* 侧边栏抽屉 */}
      <Drawer
        variant={isMobile ? 'temporary' : 'permanent'}
        open={isMobile ? mobileOpen : true}
        onClose={handleDrawerToggle}
        ModalProps={{ keepMounted: true }}
        sx={{
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: isMobile ? '200px' : drawerWidth, // 减小移动端菜单宽度
            // 添加阴影增强视觉分离
            boxShadow: isMobile ? '0px 4px 20px rgba(0,0,0,0.15)' : 'none'
          },
          width: drawerWidth,
          flexShrink: 0
        }}
      >
        {drawerContent}
      </Drawer>

      {/* 主要内容区域 */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: { xs: 1, md: 2 },
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          ml: { md: `${drawerWidth}px` },
          minHeight: '100vh',
          overflow: 'hidden', // 添加溢出隐藏属性
          bgcolor: 'background.default', // 使用主题默认背景色
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        <Toolbar />
        <Box sx={{
          bgcolor: 'background.paper', // 确保内容区有白色背景
          borderRadius: 2,
          overflow: 'hidden',
          width: '100%',
          flex: 1,
          boxShadow: 'none'
        }}>
          {children}
        </Box>
      </Box>
    </Box>
  );
};

export default Layout; 