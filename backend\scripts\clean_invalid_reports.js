/**
 * 清理无效AI报告数据脚本
 */
const { Model } = require('objection');
const Knex = require('knex');
const knexConfig = require('../knexfile');
const { AIReport } = require('../models/aiReport');

// 初始化数据库连接
const knex = Knex(knexConfig.development || knexConfig);
Model.knex(knex);

const cleanInvalidReports = async () => {
  console.log('开始清理无效AI报告数据...');
  
  try {
    // 标记明显无效的记录
    const invalidReports = await AIReport.query()
      .where(builder => {
        builder.whereNull('content')
          .orWhere('content', '{}')
          .orWhere('content', '""')
          .orWhere('content', '[]')
          .orWhere('content', 'null')
          .orWhere('content', '');
      });
    
    console.log(`找到 ${invalidReports.length} 条无效记录`);
    
    // 更新这些记录的状态
    if (invalidReports.length > 0) {
      const updatedCount = await AIReport.query()
        .where(builder => {
          builder.whereNull('content')
            .orWhere('content', '{}')
            .orWhere('content', '""')
            .orWhere('content', '[]')
            .orWhere('content', 'null')
            .orWhere('content', '');
        })
        .patch({
          status: 'FAILED',
          error_message: '数据格式无效'
        });
      
      console.log(`已更新 ${updatedCount} 条无效记录的状态`);
    }
    
    // 检查处理中但超过2小时的报告
    const twoHoursAgo = new Date();
    twoHoursAgo.setHours(twoHoursAgo.getHours() - 2);
    
    const stuckReports = await AIReport.query()
      .where('status', 'PROCESSING')
      .where('created_at', '<', twoHoursAgo.toISOString());
    
    console.log(`找到 ${stuckReports.length} 条卡住的处理中报告`);
    
    // 更新这些记录的状态
    if (stuckReports.length > 0) {
      const updatedStuckCount = await AIReport.query()
        .where('status', 'PROCESSING')
        .where('created_at', '<', twoHoursAgo.toISOString())
        .patch({
          status: 'FAILED',
          error_message: '处理超时'
        });
      
      console.log(`已更新 ${updatedStuckCount} 条卡住的报告状态`);
    }
    
    console.log('清理无效报告完成');
  } catch (error) {
    console.error('清理无效报告失败:', error);
    throw error;
  } finally {
    // 关闭数据库连接
    await knex.destroy();
  }
};

cleanInvalidReports()
  .then(() => {
    console.log('清理脚本执行完成');
    process.exit(0);
  })
  .catch(err => {
    console.error('清理脚本执行失败:', err);
    process.exit(1);
  }); 