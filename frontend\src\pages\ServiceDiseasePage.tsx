import React, { useState, useEffect, useCallback, useMemo, Suspense, lazy, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Select,
  MenuItem,
  InputLabel,
  FormControl,
  Snackbar,
  Alert,
  AlertColor,
  CircularProgress,
  Tooltip,
  Card,
  CardContent,
  Chip,
  useTheme,
  useMediaQuery,
  InputAdornment,
  Pagination,
  Stack,
  ThemeProvider,
  createTheme
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { zhCN } from 'date-fns/locale';
import { format } from 'date-fns';
import { Link } from 'react-router-dom';
import Skeleton from '@mui/material/Skeleton';
import { useQueryClient } from '@tanstack/react-query';

// 图标
import SearchIcon from '@mui/icons-material/Search';
import InfoOutlined from '@mui/icons-material/InfoOutlined';
import EditOutlined from '@mui/icons-material/EditOutlined';
import DeleteOutlined from '@mui/icons-material/DeleteOutlined';
import RefreshIcon from '@mui/icons-material/Refresh';
import VisibilityIcon from '@mui/icons-material/Visibility';
import AddCircleIcon from '@mui/icons-material/AddCircle';

// 服务与上下文
import { Disease, DiseaseStages, DiseaseStageLabels } from '../types/disease';
import * as diseaseService from '../services/diseaseService';
import * as authorizationService from '../services/authorizationService';
import serviceRecordService from '../services/serviceRecordService';
import { Patient } from '../types/patient';
import { useAuthStore } from '../store/authStore';
import { useServiceUserContext } from '../context/ServiceUserContext';
import ServiceContextBar from '../components/service/ServiceContextBar';

// 从DiseasePage复用函数
const formatDate = (dateString: string | undefined): string => {
  if (!dateString) return '-';
  try {
    const date = new Date(dateString);
    return format(date, 'yyyy-MM-dd');
  } catch (e) {
    return '-';
  }
};

// 通知类型
interface Notification {
  open: boolean;
  message: string;
  type: AlertColor;
}

// 设计规范：病理阶段主色调
const stageMainColors: Record<string, string> = {
  [DiseaseStages.INITIAL]: '#E53935',    // 初诊期
  [DiseaseStages.DIAGNOSIS]: '#8E24AA', // 确诊期
  [DiseaseStages.TREATMENT]: '#FB8C00', // 治疗期
  [DiseaseStages.RECOVERY]: '#FDD835',  // 康复期
  [DiseaseStages.PROGNOSIS]: '#43A047', // 预后期
};

// 设计规范：病理阶段主色调、标签背景色、深色文字
const stageChipStyles: Record<string, {bg: string, color: string, border: string}> = {
  [DiseaseStages.INITIAL]:   { bg: '#FFEBEE', color: '#C62828', border: '#E53935' }, // 初诊
  [DiseaseStages.DIAGNOSIS]: { bg: '#F3E5F5', color: '#6A1B9A', border: '#8E24AA' }, // 确诊
  [DiseaseStages.TREATMENT]: { bg: '#FFF3E0', color: '#EF6C00', border: '#FB8C00' }, // 治疗
  [DiseaseStages.RECOVERY]:  { bg: '#FFFDE7', color: '#F9A825', border: '#FDD835' }, // 康复
  [DiseaseStages.PROGNOSIS]: { bg: '#E8F5E9', color: '#2E7D32', border: '#43A047' }, // 预后
};

// 添加权限级别颜色配置
const privacyLevelStyles: Record<string, {bg: string, color: string, border: string, label: string}> = {
  'BASIC': { 
    bg: '#ECEFF1', 
    color: '#546E7A', 
    border: '#78909C',
    label: '基础授权' 
  },
  'STANDARD': { 
    bg: '#E3F2FD', 
    color: '#1565C0', 
    border: '#1E88E5',
    label: '标准授权' 
  },
  'FULL': { 
    bg: '#E8F5E9', 
    color: '#2E7D32', 
    border: '#43A047',
    label: '完整授权' 
  }
};

// 疾病表单数据
interface DiseaseFormData {
  patientId: string;
  name: string;
  diagnosisDate: string;
  stage: string;
  description: string;
  treatment: string;
}

// 创建一个截断文本并添加省略号的辅助函数
const truncateText = (text: string, maxLength: number) => {
  if (!text) return '无描述';
  return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
};

// 扩展疾病类型以包含额外属性
interface ExtendedDisease extends Disease {
  authorizationId?: string;
  privacyLevel?: string;
  authorizerName?: string;
  patientName?: string;
  dataSource?: {
    isCreatedByCurrentUser: boolean;
    isOwnPatient: boolean;
    isExternalPatient: boolean;
  };
}

// 创建一个扩展的患者接口，允许isDeleted等属性
interface ExtendedPatient extends Patient {
  isDeleted?: boolean;
}

// 创建一个正确的懒加载组件
const LazyDiseaseCard = lazy(() => 
  Promise.resolve().then(() => ({ 
    default: React.memo(function DiseaseCard({ disease, onSelect, onEdit, onDelete, onCreateNew, permissions, privacyLevel, isCreatedByCurrentUser }: any) {
      const theme = useTheme();
      const levelStyle = privacyLevelStyles[privacyLevel];
      const disabledColor = theme.palette.action.disabled;
      const mainColor = theme.palette.mode === 'dark' ? theme.palette.primary.light : theme.palette.primary.main;
      const showCreatedByMe = disease.dataSource?.isCreatedByCurrentUser || isCreatedByCurrentUser;
      
      return (
        <Card 
          sx={{ 
            borderLeft: `4px solid ${stageMainColors[disease.stage] || '#757575'}`,
            cursor: 'pointer',
            transition: 'transform 0.2s, box-shadow 0.2s',
            '&:hover': {
              transform: 'translateY(-4px)',
              boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
            }
          }}
          onClick={() => onSelect(disease)}
        >
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
              <Typography variant="h6" component="div">
                {disease.name}
                {showCreatedByMe && (
                  <Chip 
                    label="我创建的" 
                    size="small" 
                    color="success" 
                    sx={{ ml: 1, height: 20, fontSize: '0.7rem' }} 
                  />
                )}
              </Typography>
              <Chip 
                label={DiseaseStageLabels[disease.stage as DiseaseStages] || disease.stage} 
                size="small" 
                sx={{ 
                  backgroundColor: stageChipStyles[disease.stage as DiseaseStages]?.bg || '#f5f5f5',
                  color: stageChipStyles[disease.stage as DiseaseStages]?.color || '#424242',
                  borderColor: stageChipStyles[disease.stage as DiseaseStages]?.border || '#e0e0e0',
                  fontWeight: 500
                }}
              />
            </Box>
            
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }} component="div">
              授权用户: {disease.authorizerName || '未知'}
              {disease.privacyLevel && 
                <Chip 
                  label={privacyLevelStyles[disease.privacyLevel]?.label || disease.privacyLevel} 
                  size="small" 
                  sx={{ 
                    ml: 1, 
                    fontSize: '0.7rem',
                    backgroundColor: privacyLevelStyles[disease.privacyLevel]?.bg || '#f5f5f5',
                    color: privacyLevelStyles[disease.privacyLevel]?.color || '#424242',
                    borderColor: privacyLevelStyles[disease.privacyLevel]?.border || '#e0e0e0'
                  }} 
                />
              }
            </Typography>
            
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              患者: {disease.patientName || '未知患者'}
            </Typography>
            
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              诊断日期: {formatDate(disease.diagnosisDate)}
            </Typography>
            
            <Typography variant="body2" color="text.secondary">
              {truncateText(disease.description || '无描述', 80)}
            </Typography>
          </CardContent>
          
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'flex-end', 
            p: 1,
            borderTop: '1px solid #f0f0f0',
            mt: 1,
            backgroundColor: levelStyle?.bg || '#f5f5f5',
            borderBottomLeftRadius: 'inherit',
            borderBottomRightRadius: 'inherit'
          }}>
            <Tooltip title="查看详情">
              <IconButton 
                size="small"
                color="primary"
                onClick={(e) => {
                  e.stopPropagation();
                  onSelect(disease);
                }}
              >
                <VisibilityIcon fontSize="small" sx={{ color: mainColor }} />
              </IconButton>
            </Tooltip>
            
            <Tooltip title={permissions.canEdit ? "编辑病理" : "无编辑权限"}>
              <span>
                <IconButton 
                  size="small"
                  disabled={!permissions.canEdit}
                  onClick={(e) => {
                    e.stopPropagation();
                    onEdit(disease);
                  }}
                >
                  <EditOutlined 
                    fontSize="small" 
                    sx={{ color: permissions.canEdit ? mainColor : disabledColor }} 
                  />
                </IconButton>
              </span>
            </Tooltip>
          </Box>
        </Card>
      );
    })
  }))
);

const DiseaseCardSkeleton = () => (
  <Card sx={{ height: '100%' }}>
    <CardContent>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <Skeleton variant="text" width="60%" height={32} />
        <Skeleton variant="rectangular" width={70} height={24} sx={{ borderRadius: 4 }} />
      </Box>
      <Skeleton variant="text" width="90%" />
      <Skeleton variant="text" width="40%" />
      <Skeleton variant="text" width="30%" />
      <Skeleton variant="text" width="100%" />
      <Skeleton variant="text" width="100%" />
    </CardContent>
    <Box sx={{ p: 1, borderTop: '1px solid #f0f0f0', display: 'flex', justifyContent: 'flex-end' }}>
      <Skeleton variant="circular" width={28} height={28} sx={{ mx: 0.5 }} />
      <Skeleton variant="circular" width={28} height={28} sx={{ mx: 0.5 }} />
      <Skeleton variant="circular" width={28} height={28} sx={{ mx: 0.5 }} />
      <Skeleton variant="circular" width={28} height={28} sx={{ mx: 0.5 }} />
    </Box>
  </Card>
);

const ServiceDiseasePage: React.FC = () => {
  const theme = useTheme(); 
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const navigate = useNavigate();
  const location = useLocation();
  const queryClient = useQueryClient();
  const { user } = useAuthStore();
  const serviceContext = useServiceUserContext();
  
  const reducedFontSizeTheme = createTheme(theme, { 
    typography: {
      ...theme.typography, 
      h1: { ...theme.typography.h1, fontSize: theme.typography.h1.fontSize ? `calc(${theme.typography.h1.fontSize} - 0.4rem)`:'5.6rem' },
      h2: { ...theme.typography.h2, fontSize: theme.typography.h2.fontSize ? `calc(${theme.typography.h2.fontSize} - 0.3rem)`:'3.45rem' },
      h3: { ...theme.typography.h3, fontSize: theme.typography.h3.fontSize ? `calc(${theme.typography.h3.fontSize} - 0.25rem)`:'2.75rem' },
      h4: { ...theme.typography.h4, fontSize: theme.typography.h4.fontSize ? `calc(${theme.typography.h4.fontSize} - 0.2rem)`:'1.925rem' },
      h5: { ...theme.typography.h5, fontSize: '1.0rem' },
      h6: { ...theme.typography.h6, fontSize: '0.85rem' },
      subtitle1: { ...theme.typography.subtitle1, fontSize: '0.75rem' },
      subtitle2: { ...theme.typography.subtitle2, fontSize: '0.65rem' },
      body1: { ...theme.typography.body1, fontSize: '0.75rem' },
      body2: { ...theme.typography.body2, fontSize: '0.65rem' },
      button: { ...theme.typography.button, fontSize: '0.65rem' },
      caption: { ...theme.typography.caption, fontSize: '0.55rem' },
      overline: { ...theme.typography.overline, fontSize: '0.55rem' },
    },
    components: {
      ...theme.components, 
      MuiInputLabel: { 
        ...theme.components?.MuiInputLabel,
        styleOverrides: { 
          ...theme.components?.MuiInputLabel?.styleOverrides,
          root: { 
            ...(theme.components?.MuiInputLabel?.styleOverrides as any)?.root,
            fontSize: '0.75rem' 
          } 
        } 
      },
      MuiMenuItem: { 
        ...theme.components?.MuiMenuItem,
        styleOverrides: { 
          ...theme.components?.MuiMenuItem?.styleOverrides,
          root: { 
            ...(theme.components?.MuiMenuItem?.styleOverrides as any)?.root,
            fontSize: '0.75rem' 
          } 
        } 
      },
      MuiTableCell: { 
        ...theme.components?.MuiTableCell,
        styleOverrides: { 
          ...theme.components?.MuiTableCell?.styleOverrides,
          root: { 
            ...(theme.components?.MuiTableCell?.styleOverrides as any)?.root,
            fontSize: '0.65rem' 
          }, 
          head: { 
            ...(theme.components?.MuiTableCell?.styleOverrides as any)?.head,
            fontSize: '0.7rem', 
            fontWeight: 'bold' 
          } 
        } 
      },
      MuiChip: {
        ...theme.components?.MuiChip,
        styleOverrides: {
          ...theme.components?.MuiChip?.styleOverrides,
          label: { 
            ...(theme.components?.MuiChip?.styleOverrides as any)?.label,
            fontSize: '0.55rem' 
          }, 
          labelSmall: { 
            ...(theme.components?.MuiChip?.styleOverrides as any)?.labelSmall,
            fontSize: '0.5rem' 
          }
        }
      },
      MuiButton: {
        ...theme.components?.MuiButton,
        styleOverrides: {
          ...theme.components?.MuiButton?.styleOverrides,
          sizeSmall: { 
            ...(theme.components?.MuiButton?.styleOverrides as any)?.sizeSmall,
            fontSize: '0.6rem' 
          },
          sizeMedium: { 
            ...(theme.components?.MuiButton?.styleOverrides as any)?.sizeMedium,
            fontSize: '0.65rem' 
          },
          sizeLarge: { 
            ...(theme.components?.MuiButton?.styleOverrides as any)?.sizeLarge,
            fontSize: '0.75rem' 
          }
        }
      },
      MuiDialogTitle: { 
        ...theme.components?.MuiDialogTitle,
        styleOverrides: { 
          ...theme.components?.MuiDialogTitle?.styleOverrides,
          root: { 
            ...(theme.components?.MuiDialogTitle?.styleOverrides as any)?.root,
            fontSize: '0.85rem' 
          } 
        } 
      },
      MuiDialogContentText: { 
        ...theme.components?.MuiDialogContentText,
        styleOverrides: { 
          ...theme.components?.MuiDialogContentText?.styleOverrides,
          root: { 
            ...(theme.components?.MuiDialogContentText?.styleOverrides as any)?.root,
            fontSize: '0.75rem' 
          } 
        } 
      },
      MuiFormControlLabel: { 
        ...theme.components?.MuiFormControlLabel,
        styleOverrides: { 
          ...theme.components?.MuiFormControlLabel?.styleOverrides,
          label: { 
            ...(theme.components?.MuiFormControlLabel?.styleOverrides as any)?.label,
            fontSize: '0.75rem' 
          } 
        } 
      },
      MuiAlert: { 
        ...theme.components?.MuiAlert,
        styleOverrides: { 
          ...theme.components?.MuiAlert?.styleOverrides,
          message: { 
            ...(theme.components?.MuiAlert?.styleOverrides as any)?.message,
            fontSize: '0.65rem' 
          },
        } 
      },
      MuiPaginationItem: {
        ...theme.components?.MuiPaginationItem,
        styleOverrides: { 
          ...theme.components?.MuiPaginationItem?.styleOverrides,
          root: {
            ...(theme.components?.MuiPaginationItem?.styleOverrides as any)?.root,
            fontSize: '0.7rem', 
          },
          ellipsis: {
             ...(theme.components?.MuiPaginationItem?.styleOverrides as any)?.ellipsis,
            fontSize: '0.7rem',
          }
        }
      }
    }
  });
  
  const [diseases, setDiseases] = useState<ExtendedDisease[]>([]);
  const [authorizations, setAuthorizations] = useState<any[]>([]);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [notification, setNotification] = useState<Notification>({
    open: false,
    message: '',
    type: 'success'
  });
  
  const [searchTerm, setSearchTerm] = useState('');
  
  const [filters, setFilters] = useState({
    patientId: '',
    diseaseId: '',
    searchText: '',
    stage: '',
    showMine: false
  });
  
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 9,
    total: 0
  });
  
  const [dialogOpen, setDialogOpen] = useState<boolean>(false);
  const [dialogMode, setDialogMode] = useState<'create' | 'edit'>('create');
  const [currentDisease, setCurrentDisease] = useState<ExtendedDisease | null>(null);
  const [formData, setFormData] = useState<DiseaseFormData>({
    patientId: '',
    name: '',
    diagnosisDate: '',
    stage: DiseaseStages.INITIAL,
    description: '',
    treatment: ''
  });
  
  const [deleteDialogOpen, setDeleteDialogOpen] = useState<boolean>(false);
  const [diseaseToDelete, setDiseaseToDelete] = useState<ExtendedDisease | null>(null);
  
  const lastRequestTime = useRef<number>(0);

  const fetchData = useCallback(async (forceRefresh = false) => {
    try {
      const now = Date.now();
      if (!forceRefresh && now - lastRequestTime.current < 1000) {
        console.log('[服务病理管理] 请求过于频繁，跳过本次请求');
        return;
      }
      lastRequestTime.current = now;
      
      setLoading(true);
      
      if (serviceContext.privacyLevel === 'BASIC') {
        console.log('[服务病理管理] 基础授权级别无法访问病理列表，跳过获取');
        setDiseases([]);
        setLoading(false);
        setNotification({
          open: true,
          message: '基础授权级别无法访问病理列表，请联系授权用户提升您的授权级别',
          type: 'warning'
        });
        return;
      }

      let authsData: any[] = [];
      try {
        const authsResponse = await authorizationService.getAuthorizationsAsAuthorized();
        authsData = authsResponse.data || [];
        setAuthorizations(authsData);
      } catch (authError) {
        console.error('[服务病理管理] 获取授权列表失败:', authError);
      }
      
      let patientsData: Patient[] = [];
      try {
        const patientsResponse = await diseaseService.getAllPatients();
        patientsData = Array.isArray(patientsResponse) ? patientsResponse : [];
        patientsData = patientsData.filter((p: ExtendedPatient) => !p.isDeleted);
        setPatients(patientsData);
      } catch (patientError) {
        console.error('[服务病理管理] 获取患者列表失败:', patientError);
      }
      
      if (authsData.length === 0 && patientsData.length === 0) {
        setDiseases([]);
        setLoading(false);
        setNotification({
          open: true,
          message: '没有可用的授权关系或患者数据',
          type: 'warning'
        });
        return;
      }
      
      const patientIdsFromAuths = new Set<string>();
      const authMap = new Map<string, any>();
      
      authsData.forEach((auth: any) => {
        if (auth && (auth.patient_id || auth.patientId)) {
          const patientId = auth.patient_id || auth.patientId;
          patientIdsFromAuths.add(patientId);
          
          if (!authMap.has(patientId) || 
             (auth.privacy_level === 'FULL' || auth.privacyLevel === 'FULL')) {
            authMap.set(patientId, auth);
          }
        }
      });
      
      let diseasesData = [];
      try {
        const diseaseResponse = await diseaseService.getDiseases({}, false);
        diseasesData = Array.isArray(diseaseResponse) ? diseaseResponse : [];
      } catch (diseaseError) {
        console.error('[服务病理管理] 获取疾病列表失败:', diseaseError);
        setNotification({
          open: true,
          message: `获取病理列表失败: ${(diseaseError as Error).message || '未知错误'}`,
          type: 'error'
        });
      }
      
      if (diseasesData.length === 0) {
        setDiseases([]);
        setLoading(false);
        return;
      }
      
      const enrichedDiseases = diseasesData.map((disease: any) => {
        try {
          const enrichedDisease: ExtendedDisease = {
            ...disease,
            patientName: '未知患者',
            authorizerName: '未知授权人',
            privacyLevel: null,
            authorizationId: null
          };
          
          const patient = patientsData.find(
            (p: any) => p.id === disease.patientId || p.id === disease.patient_id
          );
          
          if (patient) {
            enrichedDisease.patientName = patient.name || `患者#${patient.id}`;
            
            const authForPatient = authMap.get(patient.id);
            if (authForPatient) {
              enrichedDisease.authorizationId = authForPatient.id;
              enrichedDisease.privacyLevel = authForPatient.privacy_level || authForPatient.privacyLevel;
              
              if (authForPatient.authorizer_name || authForPatient.authorizerName) {
                enrichedDisease.authorizerName = authForPatient.authorizer_name || authForPatient.authorizerName;
              } else if (authForPatient.authorizer && 
                      (authForPatient.authorizer.name || authForPatient.authorizer.username)) {
                enrichedDisease.authorizerName = authForPatient.authorizer.name || authForPatient.authorizer.username;
              }
            }
          }
          
          return enrichedDisease;
        } catch (e) {
          console.error('[服务病理管理] 处理疾病数据时出错:', e);
          return disease;
        }
      });
      
      setDiseases(enrichedDiseases);
    } catch (error) {
      console.error('[服务病理管理] 获取疾病列表失败:', error);
      setNotification({
        open: true,
        message: `加载病理列表失败: ${(error as Error).message || '未知错误'}`,
        type: 'error'
      });
      setDiseases([]);
    } finally {
      setLoading(false);
      sessionStorage.setItem('last_disease_fetch_time', Date.now().toString());
    }
  }, [serviceContext.privacyLevel, setLoading, setDiseases, setNotification, setAuthorizations, setPatients]);
  
  useEffect(() => {
    if (serviceContext.privacyLevel && serviceContext.privacyLevel !== 'BASIC') { 
      fetchData();
    }
  }, [fetchData, serviceContext.privacyLevel]); 
  
  const getPermissionsForDisease = useCallback((disease?: ExtendedDisease) => {
    const permissions = {
      canView: true,
      canCreate: false,
      canEdit: false,
      canDelete: false
    };
    
    const globalPrivacyLevel = serviceContext.privacyLevel;
    const currentUserId = useAuthStore.getState().user?.id;
    
    switch(globalPrivacyLevel) {
      case 'FULL':
        permissions.canCreate = true;
        break;
      case 'STANDARD':
        permissions.canCreate = true;
        break;
      case 'BASIC':
      default:
        permissions.canCreate = false;
        break;
    }
    
    if (!disease) {
      return permissions;
    }
    
    const diseasePrivacyLevel = disease.privacyLevel || globalPrivacyLevel;
    const isDiseaseCreator = disease.userId === currentUserId;
    
    switch(diseasePrivacyLevel) {
      case 'FULL':
        permissions.canEdit = true;
        permissions.canDelete = true;
        break;
      case 'STANDARD':
        permissions.canEdit = true;
        permissions.canDelete = isDiseaseCreator;
        break;
      case 'BASIC':
      default:
        permissions.canEdit = false;
        permissions.canDelete = false;
        break;
    }
    
    return permissions;
  }, [serviceContext.privacyLevel]);
  
  const fetchAuthorizations = useCallback(async () => {
    setLoading(true);
    try {
      const response = await authorizationService.getAuthorizationsAsAuthorized();
      if (response.success) {
        const activeAuthorizations = response.data.filter((auth: any) => 
          auth.status === 'ACTIVE' && auth.authorized_switch && auth.authorizer_switch
        );
        setAuthorizations(activeAuthorizations);
        
        if (activeAuthorizations.length > 0 && !serviceContext.authorizationId) {
          console.log('[服务病理管理] 加载了授权列表，准备获取所有病理数据');
        }
      }
    } catch (err: any) {
      console.error('[服务病理管理] 加载授权列表失败:', err);
      setError('加载授权关系失败，请刷新页面重试');
      } finally {
      setLoading(false);
    }
  }, [serviceContext.authorizationId, setLoading, setAuthorizations, setError]);
  
  const fetchPatients = useCallback(async () => {
    try {
      if (authorizations.length === 0) {
        return;
      }
      
      if (serviceContext.privacyLevel === 'BASIC') {
        console.log('[服务病理管理] 基础授权级别无法访问患者列表，跳过获取');
        return;
      }
      
      const allPatients: Patient[] = [];
      const processedPatientIds = new Set<string>();
      
      await Promise.all(authorizations.map(async (auth) => {
    try {
          const response = await serviceRecordService.getAuthorizedPatients(auth.id);
          if (response && response.data && Array.isArray(response.data)) {
            response.data.forEach((patientData: any) => {
              if (!processedPatientIds.has(patientData.id)) {
                processedPatientIds.add(patientData.id);
                const patient: ExtendedPatient = {
                  id: patientData.id,
                  name: patientData.name,
                  gender: patientData.gender || '',
                  birthDate: patientData.birthDate || '',
                  phoneNumber: patientData.phone || patientData.phoneNumber || '',
                  email: patientData.email || '',
                  address: patientData.address || '',
                  idCard: patientData.idCard || '',
                  medicareCard: patientData.medicareCard || '',
                  medicareLocation: patientData.medicareLocation || '',
                  emergencyContactName: patientData.emergencyContactName || '',
                  emergencyContactPhone: patientData.emergencyContactPhone || '',
                  emergencyContactRelationship: patientData.emergencyContactRelationship || '',
                  pastMedicalHistory: patientData.pastMedicalHistory || '',
                  familyMedicalHistory: patientData.familyMedicalHistory || '',
                  allergyHistory: patientData.allergyHistory || '',
                  bloodType: patientData.bloodType || '',
                  lastVisitDate: patientData.lastVisitDate || '',
                  userId: patientData.userId || '',
                  isPrimary: patientData.isPrimary || 0,
                  isDeleted: patientData.isDeleted || false,
                  createdAt: patientData.createdAt || '',
                  updatedAt: patientData.updatedAt || '',
                  isAuthorized: patientData.isAuthorized || false
                };
                allPatients.push(patient);
              }
            });
          }
        } catch (err) {
          console.error(`获取授权 ${auth.id} 的患者失败:`, err);
        }
      }));
      
      setPatients(allPatients);
    } catch (err: any) {
      console.error('获取患者列表失败:', err);
    }
  }, [authorizations, serviceContext.privacyLevel, setPatients]);
  
  useEffect(() => {
    const hasDataToLoad = authorizations.length > 0 && !loading;
    const lastFetchTime = parseInt(sessionStorage.getItem('last_disease_fetch_time') || '0');
    const now = Date.now();
    const timeSinceLastFetch = now - lastFetchTime;
    
    if (timeSinceLastFetch < 2000) {
      return;
    }
    
    const loadingKey = `disease_loading_${authorizations.length}`;
    const alreadyLoaded = sessionStorage.getItem(loadingKey);
    
    if (hasDataToLoad && !alreadyLoaded) {
      sessionStorage.setItem(loadingKey, 'true');
      console.log('[服务病理管理] 授权数据已加载，加载病理数据');
      fetchData(); 
    }
    
    return () => {
      if (hasDataToLoad) {
        sessionStorage.removeItem(loadingKey);
      }
    };
  }, [authorizations.length, loading, fetchData]); 
  
  useEffect(() => {
    if (authorizations.length > 0 && filters.patientId && filters.patientId.trim() !== '') {
      const filterKey = `patient_filter_${filters.patientId}`;
      const alreadyFiltered = sessionStorage.getItem(filterKey);
      const lastFetchTime = parseInt(sessionStorage.getItem('last_disease_fetch_time') || '0');
      const now = Date.now();
      const timeSinceLastFetch = now - lastFetchTime;
      
      if (timeSinceLastFetch < 2000) {
        return;
      }
      
      if (!alreadyFiltered) {
        sessionStorage.setItem(filterKey, 'true');
        console.log(`[服务病理管理] 患者筛选变化 ${filters.patientId}，重新加载数据`);
        fetchData(); 
      }
      
      return () => {
        sessionStorage.removeItem(filterKey);
      };
    }
  }, [filters.patientId, authorizations.length, fetchData]); 
  
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const authId = searchParams.get('authId');
    const patientId = searchParams.get('patientId');
    
    console.log('[服务病理管理] URL参数:', { authId, patientId });
      
    const contextChanged = 
      (authId && authId !== serviceContext.authorizationId) || 
      (patientId && patientId !== serviceContext.patientId);
    
    const contextKey = `context_change_${authId || ''}_${patientId || ''}`;
    const alreadyProcessed = sessionStorage.getItem(contextKey);
    
    if (!alreadyProcessed && contextChanged) {
      sessionStorage.setItem(contextKey, 'true');
      
      if (authId && (!serviceContext.authorizationId || serviceContext.authorizationId !== authId)) {
        console.log(`[服务病理管理] 从URL设置授权ID: ${authId}`);
        serviceContext.setAuthorization(authId, '', null, null); 
      }
      
      if (patientId && (!serviceContext.patientId || serviceContext.patientId !== patientId)) {
        console.log(`[服务病理管理] 从URL设置患者ID: ${patientId}`);
        serviceContext.setPatient(patientId, ''); 
      }
      
      if (contextChanged) {
        console.log('[服务病理管理] 上下文参数变化，重新加载数据');
        setTimeout(() => {
          fetchData(true); 
        }, 300);
      }
    }
    
    return () => {
      if (contextChanged) {
        sessionStorage.removeItem(contextKey);
      }
    };
  }, [location.search, serviceContext, fetchData]); 
  
  useEffect(() => {
    if (!user) {
      navigate('/login');
      return;
    }
    
    const loadData = async () => {
      try {
        if (serviceContext.privacyLevel === 'BASIC') {
          console.log("[服务病理管理] 基础授权级别用户，显示相应提示");
          setLoading(false);
          setNotification({
            open: true,
            message: '基础授权级别只能查看授权信息，无法访问病理列表',
            type: 'info'
          });
          return;
        }
        
        await fetchAuthorizations();
      } catch (error) {
        console.error("[服务病理管理] 加载初始数据失败:", error);
        setError("加载数据失败，请刷新页面重试");
      }
    };
    
    console.log("[服务病理管理] 页面初始化");
    loadData();
    
    return () => {
      console.log("[服务病理管理] 页面卸载，清理缓存");
      const keysToRemove = [];
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key && (
            key.startsWith('disease_loading_') || 
            key.startsWith('patient_filter_') || 
            key.startsWith('context_change_') ||
            key === 'last_disease_fetch_time'
          )) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach(key => sessionStorage.removeItem(key));
    };
  }, [user, navigate, fetchAuthorizations, serviceContext.privacyLevel]);
  
  useEffect(() => {
    if (authorizations.length > 0) {
      fetchPatients();
    }
  }, [authorizations, fetchPatients]);
  
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setSearchTerm(value);
    setFilters(prev => ({ ...prev, searchText: value }));
  };
  
  const handlePatientFilterChange = (patientId: string) => {
    setFilters(prev => ({ ...prev, patientId }));
  };
  
  const handleStageFilterChange = (stage: string) => {
    setFilters(prev => ({ ...prev, stage }));
  };
  
  const handleOpenCreateDialog = () => {
    const permissions = getPermissionsForDisease();
    if (!permissions.canCreate) {
      setNotification({
        open: true,
        message: '您没有创建病理的权限',
        type: 'error'
      });
      return;
    }
    
    if (serviceContext.privacyLevel === 'BASIC') {
      setNotification({
        open: true,
        message: '基础授权用户无法创建病理记录',
        type: 'error'
      });
      return;
    }
    
    setDialogMode('create');
    setFormData({
      patientId: filters.patientId || '',
      name: '',
      diagnosisDate: '',
      stage: DiseaseStages.INITIAL,
      description: '',
      treatment: ''
    });
    setDialogOpen(true);
  };
  
  const handleOpenEditDialog = (disease: ExtendedDisease) => {
    const permissions = getPermissionsForDisease(disease);
    if (!permissions.canEdit) {
      setNotification({
        open: true,
        message: '您没有编辑病理的权限',
        type: 'error'
      });
      return;
    }
    
    setDialogMode('edit');
    setCurrentDisease(disease);
    setFormData({
      patientId: disease.patientId || '',
      name: disease.name,
      diagnosisDate: formatDate(disease.diagnosisDate),
      stage: disease.stage,
      description: disease.description || '',
      treatment: disease.treatment || ''
    });
    setDialogOpen(true);
  };
  
  const handleCloseDialog = () => {
    setDialogOpen(false);
  };
  
  const handleInputChange = (field: keyof DiseaseFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };
  
  const handleDateChange = (date: Date | null) => {
    if (date) {
      const dateString = format(date, 'yyyy-MM-dd');
      handleInputChange('diagnosisDate', dateString);
    }
  };
  
  const handleSubmit = async () => {
    try {
      if (dialogMode === 'create') {
        if (!formData.patientId) {
          console.error('[病理创建] 未指定患者ID', formData);
          setNotification({
            open: true,
            message: '创建病理需要选择患者',
            type: 'error'
          });
          return;
        }
        
        const requestData = {
          ...formData,
          patient_id: formData.patientId,
          isDeleted: false
        };
        
        console.log('[病理创建] 表单数据:', formData);
        console.log('[病理创建] 请求数据:', requestData);
        
        if (!serviceContext.authorizationId && authorizations.length > 0) {
          const firstAuth = authorizations[0];
          const authorizerId = firstAuth.authorizer_id || 
                             (firstAuth.authorizer ? firstAuth.authorizer.id : null) || 
                             firstAuth.authorizerId;
          const privacyLevel = firstAuth.privacy_level || firstAuth.privacyLevel;
          const status = firstAuth.status || 'ACTIVE';
          
          console.log('[病理创建] 设置临时授权上下文:', {
            authId: firstAuth.id,
            authorizerId,
            privacyLevel,
            status
          });
          
          serviceContext.setAuthorization(
            firstAuth.id, 
            authorizerId,
            privacyLevel,
            status
          );
          
          if (formData.patientId) {
            const patient = patients.find(p => p.id === formData.patientId);
            if (patient) {
              console.log('[病理创建] 设置患者上下文:', {
                patientId: patient.id,
                patientName: patient.name
              });
              serviceContext.setPatient(patient.id, patient.name);
            } else {
              console.error('[病理创建] 未找到患者:', formData.patientId);
            }
          }
        }
        
        if (!serviceContext.authorizationId) {
          setNotification({
            open: true,
            message: '创建病理前需要选择有效的授权关系',
            type: 'error'
          });
          return;
        }
        
        console.log('[病理创建] 创建病理请求:', {
          formData,
          requestData,
          serviceContext: {
            authorizationId: serviceContext.authorizationId,
            patientId: serviceContext.patientId,
            ownerUserId: serviceContext.ownerUserId,
            privacyLevel: serviceContext.privacyLevel
          }
        });
        
        await diseaseService.createDisease(requestData);
        
        setNotification({
          open: true,
          message: '病理创建成功',
          type: 'success'
        });
      } else {
        if (currentDisease) {
          await diseaseService.updateDisease(
            currentDisease.id, 
            formData
          );
          setNotification({
            open: true,
            message: '病理更新成功',
            type: 'success'
          });
        }
      }
      setDialogOpen(false);
      fetchData(true); 
    } catch (error: any) {
      console.error('提交病理表单失败:', error);
      setNotification({
        open: true,
        message: `操作失败: ${error.message || '未知错误'}`,
        type: 'error'
      });
    }
  };
  
  const handleOpenDeleteDialog = (disease: ExtendedDisease) => {
    const permissions = getPermissionsForDisease(disease);
    if (!permissions.canDelete) {
      setNotification({
        open: true,
        message: '您没有删除病理的权限',
        type: 'error'
      });
      return;
    }
    
    setDiseaseToDelete(disease);
    setDeleteDialogOpen(true);
  };
  
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setDiseaseToDelete(null);
  };
  
  const handleConfirmDelete = async () => {
    if (!diseaseToDelete) return;
    
    try {
      await diseaseService.deleteDisease(diseaseToDelete.id);
      
      if (diseaseToDelete.id === serviceContext.diseaseId) {
        serviceContext.setDisease('', '');
      }
      
      setNotification({
        open: true,
        message: '病理删除成功',
        type: 'success'
      });
      setDeleteDialogOpen(false);
      setDiseaseToDelete(null);
      
      fetchData(true); 
    } catch (error: any) {
      console.error('删除病理失败:', error);
      setNotification({
        open: true,
        message: `删除失败: ${error.message || '未知错误'}`,
        type: 'error'
      });
    }
  };
  
  const handleCloseNotification = () => {
    setNotification({
      ...notification,
      open: false
    });
  };
  
  const handleDiseaseSelect = (disease: ExtendedDisease) => {
    if (disease.authorizationId) {
      const auth = authorizations.find(a => a.id === disease.authorizationId);
      if (auth) {
        serviceContext.setAuthorization(
          disease.authorizationId,
          auth.authorizer_id || auth.authorizer?.id,
          auth.privacy_level || auth.privacyLevel,
          auth.status
        );
      }
    }
    serviceContext.setPatient(disease.patientId, disease.patientName || '');
    serviceContext.setDisease(disease.id || '', disease.name || '');
    setNotification({
      open: true,
      message: `已选择病理: ${disease.name}`,
      type: 'success'
    });
  };
  
  const handleRefresh = () => {
    setLoading(true);
    sessionStorage.removeItem(`diseases_loaded_${serviceContext.authorizationId}_${serviceContext.patientId}`);
    sessionStorage.removeItem(`diseases_filtered_${serviceContext.authorizationId}_${serviceContext.patientId}`);
    
    queryClient.invalidateQueries();

    if (serviceContext.authorizationId || serviceContext.patientId) {
        fetchAuthorizations().then(() => fetchData(true));
    } else if (user?.id) {
        fetchAuthorizations().then(() => fetchData(true));
    } else {
        setLoading(false);
    }
    setNotification({ open: true, message: '数据已刷新', type: 'success' });
  };
  
  const filteredDiseases = useMemo(() => {
    if (!Array.isArray(diseases)) {
      console.error('[服务病理管理] diseases不是数组类型:', diseases);
      return [];
    }
    
    return diseases.filter(disease => {
      if (filters.stage && disease.stage !== filters.stage) {
        return false;
      }
      if (filters.searchText) {
        const searchLower = filters.searchText.toLowerCase();
        const nameMatch = disease.name?.toLowerCase().includes(searchLower);
        const descMatch = disease.description?.toLowerCase().includes(searchLower);
        const treatmentMatch = disease.treatment?.toLowerCase().includes(searchLower);
        const patientMatch = disease.patientName?.toLowerCase().includes(searchLower) || 
                             patients.find(p => p.id === disease.patientId)?.name?.toLowerCase().includes(searchLower);
        const userMatch = disease.authorizerName?.toLowerCase().includes(searchLower);
        
        if (!nameMatch && !descMatch && !treatmentMatch && !patientMatch && !userMatch) {
          return false;
        }
      }
      return true;
    });
  }, [diseases, filters.stage, filters.searchText, patients]);
  
  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPagination(prev => ({
      ...prev,
      page: value
    }));
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  const paginatedDiseases = useMemo(() => {
    const startIndex = (pagination.page - 1) * pagination.pageSize;
    return filteredDiseases.slice(startIndex, startIndex + pagination.pageSize);
  }, [filteredDiseases, pagination.page, pagination.pageSize]);
  
  const renderPrivacyLevelAlert = () => {
    const privacyLevel = serviceContext.privacyLevel;
    if (privacyLevel === null) return null;
    
    const levelStyle = privacyLevelStyles[privacyLevel as keyof typeof privacyLevelStyles]; 
    if (!levelStyle) return null;

    let alertText = '';
    let allowedActions: string[] = [];
    
    switch(privacyLevel) {
      case 'BASIC':
        alertText = '基础授权级别：只读权限，您只能查看授权信息，无法访问病理列表或进行任何操作。请联系授权用户提升您的授权级别。';
        allowedActions = ['查看授权信息'];
        break;
      case 'STANDARD':
        alertText = '标准授权级别：可以查看和编辑授权用户的病理信息，但只能删除自己创建的记录。';
        allowedActions = ['查看', '创建', '编辑', '删除(自己创建的)'];
        break;
      case 'FULL':
        alertText = '完全授权级别：完全读写权限，可以查看、创建、编辑和删除所有病理信息。';
        allowedActions = ['查看', '创建', '编辑', '删除'];
        break;
      default:
        return null; 
    }
    
      return (
      <Alert 
        severity="info"
        sx={{ 
          mb: 2,
          backgroundColor: levelStyle.bg,
          color: levelStyle.color,
          border: `1px solid ${levelStyle.border}`,
          '& .MuiAlert-icon': {
            color: levelStyle.color
          }
        }}
      >
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Chip 
              label={levelStyle.label} 
              size="small" 
              sx={{ 
                backgroundColor: 'rgba(255, 255, 255, 0.5)',
                color: levelStyle.color,
                fontWeight: 500
              }}
            />
            <Typography variant="body2">{alertText}</Typography>
          </Box>
          
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
            {allowedActions.map((action, index) => {
              let actionIcon;
              if (action.includes('查看')) {
                actionIcon = <VisibilityIcon fontSize="small" />;
              } else if (action.includes('创建')) {
                actionIcon = <AddCircleIcon fontSize="small" />;
              } else if (action.includes('编辑')) {
                actionIcon = <EditOutlined fontSize="small" />;
              } else if (action.includes('删除')) {
                actionIcon = <DeleteOutlined fontSize="small" />;
              } else {
                actionIcon = <InfoOutlined fontSize="small" />;
              }
              
              return (
                <Chip 
                  key={index}
                  label={action} 
                  size="small" 
                  sx={{ 
                    backgroundColor: 'rgba(255, 255, 255, 0.7)',
                    color: levelStyle.color,
                    fontSize: '0.7rem'
                  }}
                  icon={actionIcon}
                />
              );
            })}
          </Box>
        </Box>
        </Alert>
      );
  };
    
    return (
    <ThemeProvider theme={reducedFontSizeTheme}>
      <Box sx={{ px: '10px' }}>
        {serviceContext.authorizationId && (
          <ServiceContextBar />
        )}
        
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center', 
          mb: 3,
          flexWrap: 'wrap', 
          gap: 1
        }}>
          <Typography 
            variant="h5" 
            component="h1" 
            gutterBottom
            sx={{ fontWeight: 500 }}
          >
            疾病管理
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={handleRefresh}
            >
              刷新
            </Button>
          </Box>
        </Box>
        
        {renderPrivacyLevelAlert()}
        
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        {serviceContext.privacyLevel === 'BASIC' ? (
          <Paper 
            sx={{ 
              p: 4, 
              textAlign: 'center',
              bgcolor: privacyLevelStyles['BASIC'].bg,
              border: `1px solid ${privacyLevelStyles['BASIC'].border}`,
              borderRadius: 2,
              mb: 2
            }}
          >
            <Typography variant="h6" color={privacyLevelStyles['BASIC'].color} gutterBottom>
              您当前的授权级别仅支持查看授权信息
            </Typography>
            <Typography color="text.secondary" paragraph>
              基础授权级别无法访问病理列表和患者数据。请联系授权用户提升您的授权级别，以获得更多功能。
            </Typography>
            <Button 
              variant="contained" 
              component={Link} 
              to="/service-authorizations"
              sx={{ 
                mt: 2, 
                bgcolor: privacyLevelStyles['BASIC'].color,
                '&:hover': {
                  bgcolor: privacyLevelStyles['BASIC'].color,
                  opacity: 0.9
                }
              }}
            >
              查看授权关系
            </Button>
          </Paper>
        ) : (
          <>
            <Paper sx={{ p: 2, mb: 3 }}>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <TextField
                  fullWidth
                  placeholder="搜索疾病名称、授权用户、患者名、描述..."
                  variant="outlined"
                  value={searchTerm}
                  onChange={handleSearchChange}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                />
                  
                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                  <FormControl sx={{ minWidth: 200, flex: 1 }} variant="outlined">
                    <InputLabel id="patient-filter-label" shrink={true}>按患者筛选</InputLabel>
                    <Select
                      labelId="patient-filter-label"
                      id="patient-filter"
                      value={filters.patientId}
                      label="按患者筛选"
                      onChange={(e) => handlePatientFilterChange(e.target.value)}
                      displayEmpty
                      notched={true}
                      sx={{ '& .MuiSelect-select': { display: 'flex', alignItems: 'center' } }}
                      renderValue={(selected) => {
                        if (!selected) return "所有患者";
                        const selectedPatient = patients.find(p => p.id === selected);
                        return selectedPatient ? selectedPatient.name : "所有患者";
                      }}
                    >
                      <MenuItem value="">所有患者</MenuItem>
                      {patients.map((patient) => (
                        <MenuItem key={patient.id} value={patient.id}>
                          {patient.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                  
                  <FormControl sx={{ minWidth: 200, flex: 1 }} variant="outlined">
                    <InputLabel id="stage-filter-label" shrink={true}>按阶段筛选</InputLabel>
                    <Select
                      labelId="stage-filter-label"
                      id="stage-filter"
                      value={filters.stage}
                      label="按阶段筛选"
                      onChange={(e) => handleStageFilterChange(e.target.value)}
                      displayEmpty
                      notched={true}
                      sx={{ '& .MuiSelect-select': { display: 'flex', alignItems: 'center' } }}
                      renderValue={(selected) => {
                        if (!selected) return "所有阶段";
                        return DiseaseStageLabels[selected as DiseaseStages] || selected;
                      }}
                    >
                      <MenuItem value="">所有阶段</MenuItem>
                      {Object.entries(DiseaseStageLabels).map(([value, label]) => (
                        <MenuItem key={value} value={value}>
                          {label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Box>
              </Box>
            </Paper>
            
            {loading && (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            )}
            
            {!loading && diseases.length === 0 && (
              <Paper sx={{ p: 3, textAlign: 'center' }}>
                <Typography color="text.secondary" sx={{ mb: 2 }}>
                  {authorizations.length > 0 
                    ? (searchTerm 
                      ? '没有找到匹配的疾病' 
                      : (filters.patientId 
                        ? '该患者暂无疾病记录' 
                        : '暂无病理记录'))
                    : '暂无授权关系'
                  }
                </Typography>
              </Paper>
            )}
            
            {!loading && diseases.length > 0 && (
              <>
                <Box sx={{ 
                  display: 'grid', 
                  gridTemplateColumns: { 
                    xs: '1fr', 
                    sm: 'repeat(2, 1fr)', 
                    md: 'repeat(3, 1fr)' 
                  },
                  gap: 2,
                  mb: 2
                }}>
                  {paginatedDiseases.map(disease => {
                    const permissions = getPermissionsForDisease(disease);
                    const privacyLevel = disease.privacyLevel || serviceContext.privacyLevel || 'BASIC';
                    const currentUserIdForCard = useAuthStore.getState().user?.id;
                    const isCreatedByCurrentUser = disease.userId === currentUserIdForCard;
                    
                    return (
                      <React.Fragment key={disease.id}>
                        <Suspense fallback={<DiseaseCardSkeleton />}>
                          <LazyDiseaseCard
                            disease={disease}
                            permissions={permissions}
                            privacyLevel={privacyLevel}
                            isCreatedByCurrentUser={isCreatedByCurrentUser}
                            onSelect={handleDiseaseSelect}
                            onEdit={handleOpenEditDialog}
                            onDelete={handleOpenDeleteDialog}
                            onCreateNew={handleOpenCreateDialog}
                          />
                        </Suspense>
                      </React.Fragment>
                    );
                  })}
                </Box>
                      
                {filteredDiseases.length > pagination.pageSize && (
                  <Stack spacing={2} sx={{ mt: 3, mb: 2 }}>
                    <Pagination 
                      count={Math.ceil(filteredDiseases.length / pagination.pageSize)} 
                      page={pagination.page}
                      onChange={handlePageChange}
                      color="primary"
                      showFirstButton
                      showLastButton
                      siblingCount={1}
                      boundaryCount={1}
                      size={isMobile ? "small" : "medium"}
                      sx={{ 
                        display: 'flex',
                        justifyContent: 'center',
                        '& .MuiPaginationItem-root': {
                          fontWeight: 500
                        }
                      }}
                    />
                    <Typography 
                      variant="body2" 
                      color="text.secondary" 
                      align="center"
                    >
                      显示 {filteredDiseases.length ? (pagination.page - 1) * pagination.pageSize + 1 : 0} - {Math.min(pagination.page * pagination.pageSize, filteredDiseases.length)} 条，共 {filteredDiseases.length} 条
                    </Typography>
                  </Stack>
                )}
              </>
            )}
          </>
        )}
        
        <Dialog 
          open={dialogOpen} 
          onClose={handleCloseDialog} 
          maxWidth="md" 
          fullWidth
        >
          <DialogTitle>
            {dialogMode === 'create' ? '添加病理' : '编辑病理'}
          </DialogTitle>
          <DialogContent>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
              <FormControl fullWidth required>
                <InputLabel>选择患者</InputLabel>
                <Select
                  value={formData.patientId}
                  label="选择患者"
                  onChange={(e) => handleInputChange('patientId', e.target.value)}
                >
                  {patients.map((patient) => (
                    <MenuItem key={patient.id} value={patient.id}>
                      {patient.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              
              <TextField
                label="病理名称"
                fullWidth
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                required
              />
              
              <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={zhCN}>
                <DatePicker
                  label="诊断日期"
                  value={formData.diagnosisDate ? new Date(formData.diagnosisDate) : null}
                  onChange={handleDateChange}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      required: true
            }
          }}
        />
              </LocalizationProvider>
              
              <FormControl fullWidth>
                <InputLabel>病理阶段</InputLabel>
                <Select
                  value={formData.stage}
                  label="病理阶段"
                  onChange={(e) => handleInputChange('stage', e.target.value)}
                >
                  {Object.entries(DiseaseStageLabels).map(([value, label]) => (
                    <MenuItem key={value} value={value}>{label}</MenuItem>
                  ))}
                </Select>
              </FormControl>
              
            <TextField
                label="病理描述"
              fullWidth
                multiline
                rows={4}
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
            />
              
            <TextField
                label="治疗方案"
              fullWidth
                multiline
                rows={4}
                value={formData.treatment}
                onChange={(e) => handleInputChange('treatment', e.target.value)}
              />
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>取消</Button>
            <Button 
              onClick={handleSubmit} 
              variant="contained"
              disabled={!formData.name || !formData.diagnosisDate}
            >
              保存
            </Button>
          </DialogActions>
        </Dialog>
        
        <Dialog open={deleteDialogOpen} onClose={handleCloseDeleteDialog}>
          <DialogTitle>确认删除</DialogTitle>
          <DialogContent>
            <Typography>
              您确定要删除病理 "{diseaseToDelete?.name}" 吗？此操作不可撤销。
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDeleteDialog}>取消</Button>
            <Button onClick={handleConfirmDelete} color="error">删除</Button>
          </DialogActions>
        </Dialog>
        
        <Snackbar
          open={notification.open}
          autoHideDuration={6000}
          onClose={handleCloseNotification}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        >
          <Alert 
            onClose={handleCloseNotification} 
            severity={notification.type}
            variant="filled"
          >
            {notification.message}
          </Alert>
        </Snackbar>
      </Box>
    </ThemeProvider>
  );
};

export default ServiceDiseasePage; 