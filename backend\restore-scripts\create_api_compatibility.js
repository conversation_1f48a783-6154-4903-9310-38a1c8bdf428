/**
 * API兼容层脚本
 * 创建兼容新版本API路径的路由适配器
 */
const fs = require('fs');
const path = require('path');

console.log('===== 创建API路径兼容层 =====');

// 创建主路由适配器
function createMainRouteAdapter() {
  console.log('正在创建主路由适配器...');
  
  const adapterContent = `
/**
 * AI报告路由兼容适配器
 * 将新版API路径映射到旧版路由处理器
 */
const express = require('express');
const router = express.Router();
const aiReportRouter = require('./aiReport');
const { authenticate } = require('../middleware/auth');

// 确保所有请求都经过身份验证
router.use(authenticate);

// 简单映射 - 将根路径请求转发到旧版路径处理
router.post('/', (req, res) => {
  req.url = '/ai-reports';
  aiReportRouter(req, res);
});

// 获取AI报告的兼容路径
router.get('/:id', (req, res) => {
  req.url = \`/ai-reports/\${req.params.id}\`;
  aiReportRouter(req, res);
});

// 重新生成报告的兼容路径
router.post('/regenerate', (req, res) => {
  req.url = '/ai-reports/regenerate';
  aiReportRouter(req, res);
});

// 配额检查的兼容路径
router.get('/quota', (req, res) => {
  req.url = '/ai-reports/quota';
  aiReportRouter(req, res);
});

// 其他潜在需要的兼容路径映射
router.get('/', (req, res) => {
  if (req.query.diseaseId) {
    req.url = '/ai-reports?diseaseId=' + req.query.diseaseId;
  } else {
    req.url = '/ai-reports';
  }
  aiReportRouter(req, res);
});

// 报告PDF下载的兼容路径
router.get('/:id/pdf', (req, res) => {
  req.url = \`/ai-reports/\${req.params.id}/pdf\`;
  aiReportRouter(req, res);
});

// 适配任何其他潜在的路由
router.use('/*', (req, res, next) => {
  console.log('[AI报告路由适配器] 转发请求到旧版路由:', req.url);
  aiReportRouter(req, res, next);
});

module.exports = router;
`;

  const filePath = path.join(__dirname, '..', 'routes', 'aiReportRoutes.js');
  fs.writeFileSync(filePath, adapterContent, 'utf8');
  console.log(`已创建主路由适配器: ${filePath}`);
}

// 更新应用入口，连接新旧路由
function updateAppRoutes() {
  console.log('正在更新应用路由连接...');
  
  const appFile = path.join(__dirname, '..', 'app.js');
  
  if (!fs.existsSync(appFile)) {
    console.warn('警告: 未找到app.js，请手动更新应用路由');
    return;
  }
  
  let appContent = fs.readFileSync(appFile, 'utf8');
  
  // 检查是否已有相关路由定义
  if (appContent.includes('const aiReportRoutes = require(\'./routes/aiReportRoutes\');')) {
    // 已有新版本路由定义，无需修改
    console.log('应用已包含AI报告路由引用，无需更新');
    return;
  }
  
  if (appContent.includes('const aiReportRouter = require(\'./routes/aiReport\');')) {
    // 已有旧版本路由定义，检查是否需要添加适配器路由
    if (!appContent.includes('app.use(\'/api/ai-reports\'')) {
      // 没有挂载路径，需要添加
      const routeMountPattern = /app\.use\(['"]\/api\//;
      const lastRouteMountIndex = appContent.lastIndexOf('app.use(\'/api/');
      
      if (lastRouteMountIndex !== -1) {
        // 找到最后一个路由挂载点，在它之后插入AI报告路由
        const insertPoint = appContent.indexOf('\n', lastRouteMountIndex) + 1;
        appContent = appContent.slice(0, insertPoint) +
          'app.use(\'/api/ai-reports\', aiReportRouter);\n' +
          appContent.slice(insertPoint);
          
        fs.writeFileSync(appFile, appContent, 'utf8');
        console.log('已添加旧版本路由挂载点');
      }
    }
    return;
  }
  
  // 需要添加路由定义和挂载点
  const importPattern = /const\s+(\w+Routes|\w+Router)\s+=\s+require\(['"]\.\/routes\//;
  const lastImportIndex = appContent.lastIndexOf('const ');
  const importEndIndex = appContent.indexOf('\n', lastImportIndex) + 1;
  
  // 添加路由导入
  let updatedContent = appContent.slice(0, importEndIndex) +
    'const aiReportRouter = require(\'./routes/aiReport\');\n' +
    'const aiReportRoutes = require(\'./routes/aiReportRoutes\');\n' +
    appContent.slice(importEndIndex);
  
  // 添加路由挂载点
  const routeMountPattern = /app\.use\(['"]\/api\//;
  const lastRouteMountIndex = updatedContent.lastIndexOf('app.use(\'/api/');
  
  if (lastRouteMountIndex !== -1) {
    const insertPoint = updatedContent.indexOf('\n', lastRouteMountIndex) + 1;
    updatedContent = updatedContent.slice(0, insertPoint) +
      'app.use(\'/api/ai-reports\', aiReportRouter);\n' +
      'app.use(\'/api/ai-report\', aiReportRoutes); // 兼容新版API路径\n' +
      updatedContent.slice(insertPoint);
  }
  
  fs.writeFileSync(appFile, updatedContent, 'utf8');
  console.log('已添加新旧版本路由引用和挂载点');
}

// 执行主要功能
(async function main() {
  try {
    createMainRouteAdapter();
    updateAppRoutes();
    console.log('API路径兼容层创建完成！');
  } catch (error) {
    console.error('创建API兼容层失败:', error);
    process.exit(1);
  }
})(); 