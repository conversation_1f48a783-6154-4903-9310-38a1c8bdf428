const express = require('express');
const router = express.Router();
const { v4: uuidv4 } = require('uuid');
const knex = require('knex')(require('../knexfile').development);
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { auth } = require('../src/middleware/auth');

// JWT密钥，应从环境变量获取
const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret';

// 获取用户个人资料
router.get('/profile', auth, async (req, res) => {
  try {
    const { userId } = req;
    
    const user = await knex('users')
      .where('id', userId)
      .select(
        'id',
        'username',
        'email',
        'phoneNumber',
        'role',
        'level',
        'activeDiseaseLimit',
        'aiUsageCount',
        'familyMemberLimit',
        'createdAt',
        'lastLoginAt'
      )
      .first();
    
    if (!user) {
      return res.status(404).json({ error: '用户不存在' });
    }
    
    res.json(user);
  } catch (error) {
    console.error('获取用户资料失败:', error);
    res.status(500).json({ error: '获取用户资料失败' });
  }
});

// 更新用户资料
router.put('/profile', auth, async (req, res) => {
  try {
    const { userId } = req;
    const { email, phoneNumber, avatar } = req.body;
    
    const user = await knex('users')
      .where('id', userId)
      .first();
    
    if (!user) {
      return res.status(404).json({ error: '用户不存在' });
    }
    
    const now = new Date().toISOString();
    await knex('users')
      .where('id', userId)
      .update({
        email,
        phoneNumber,
        avatar,
        updatedAt: now
      });
    
    res.json({ message: '用户资料更新成功' });
  } catch (error) {
    console.error('更新用户资料失败:', error);
    res.status(500).json({ error: '更新用户资料失败' });
  }
});

// 更改密码
router.put('/change-password', auth, async (req, res) => {
  try {
    const { userId } = req;
    const { currentPassword, newPassword } = req.body;
    
    if (!currentPassword || !newPassword) {
      return res.status(400).json({ error: '当前密码和新密码都是必填项' });
    }
    
    const user = await knex('users')
      .where('id', userId)
      .first();
    
    if (!user) {
      return res.status(404).json({ error: '用户不存在' });
    }
    
    // 验证当前密码
    const passwordMatch = await bcrypt.compare(currentPassword, user.passwordHash);
    if (!passwordMatch) {
      return res.status(401).json({ error: '当前密码不正确' });
    }
    
    // 生成新密码的哈希值
    const newPasswordHash = await bcrypt.hash(newPassword, 10);
    
    // 更新密码
    const now = new Date().toISOString();
    await knex('users')
      .where('id', userId)
      .update({
        passwordHash: newPasswordHash,
        updatedAt: now
      });
    
    res.json({ message: '密码修改成功' });
  } catch (error) {
    console.error('修改密码失败:', error);
    res.status(500).json({ error: '修改密码失败' });
  }
});

// 获取当前用户的权限和限制
router.get('/limits', auth, async (req, res) => {
  try {
    const { userId } = req;
    
    const user = await knex('users')
      .where('id', userId)
      .select(
        'level',
        'activeDiseaseLimit',
        'aiUsageCount',
        'familyMemberLimit'
      )
      .first();
    
    if (!user) {
      return res.status(404).json({ error: '用户不存在' });
    }
    
    // 获取该等级的用户限制
    const levelLimits = await knex('user_level_limits')
      .where('levelType', user.level)
      .first();
    
    // 获取用户当前的患者数量
    const patientCount = await knex('patients')
      .where('userId', userId)
      .count('id as count')
      .first();
    
    // 获取用户当前的疾病记录数量
    const diseaseCount = await knex('diseases')
      .where('userId', userId)
      .count('id as count')
      .first();
    
    res.json({
      level: user.level,
      activeDiseaseLimit: user.activeDiseaseLimit,
      aiUsageCount: user.aiUsageCount,
      familyMemberLimit: user.familyMemberLimit,
      currentPatientCount: patientCount.count,
      currentDiseaseCount: diseaseCount.count,
      levelLimits: levelLimits || {}
    });
  } catch (error) {
    console.error('获取用户限制失败:', error);
    res.status(500).json({ error: '获取用户限制失败' });
  }
});

module.exports = router; 