/**
 * 中文文件名测试脚本
 * 用于测试文件上传和下载时中文文件名的处理
 */

const fs = require('fs');
const path = require('path');
const http = require('http');
const https = require('https');
const { v4: uuidv4 } = require('uuid');
const knex = require('knex')(require('../knexfile').development);

// 测试文件名列表 - 包含各种中文和特殊字符
const testFilenames = [
  '测试文档.txt',
  '医疗报告-2025年.pdf',
  '检查结果（复查）.doc',
  '智能辅助医疗指导报告.pdf',
  '患者资料_完整版.xlsx',
  '中文 英文 123.txt',
  '特殊字符+测试.docx',
  '长文件名测试文档名称可能会非常长测试编码问题.txt'
];

// 测试目录
const testDir = path.join(__dirname, '../test-files');

// 创建测试目录
if (!fs.existsSync(testDir)) {
  fs.mkdirSync(testDir, { recursive: true });
}

// 创建测试文件
function createTestFiles() {
  console.log('正在创建测试文件...');
  
  const testFiles = [];
  
  testFilenames.forEach(filename => {
    const filePath = path.join(testDir, filename);
    fs.writeFileSync(filePath, `这是测试文件内容: ${filename}`, 'utf8');
    testFiles.push({ name: filename, path: filePath });
    console.log(`- 已创建: ${filename}`);
  });
  
  return testFiles;
}

// 测试文件名编码
function testFilenameEncoding() {
  console.log('\n--- 测试文件名编码 ---');
  
  testFilenames.forEach(filename => {
    console.log(`\n原始文件名: ${filename}`);
    
    // 使用Buffer测试不同编码
    const bufUtf8 = Buffer.from(filename, 'utf8');
    const bufBinary = Buffer.from(filename, 'binary');
    
    console.log(`UTF8 Buffer: ${bufUtf8.toString('hex')}`);
    console.log(`Binary Buffer: ${bufBinary.toString('hex')}`);
    
    // 测试encodeURIComponent
    const encoded = encodeURIComponent(filename);
    console.log(`URL编码: ${encoded}`);
    
    // 测试解码
    const decoded = decodeURIComponent(encoded);
    console.log(`URL解码: ${decoded}`);
    
    // 测试Content-Disposition头格式
    console.log(`Content-Disposition: attachment; filename="${encoded}"; filename*=UTF-8''${encoded}`);
  });
}

// 测试数据库中文名称存储
async function testDatabaseStorage() {
  console.log('\n--- 测试数据库中文存储 ---');
  
  // 创建临时表
  const testTableName = `filename_test_${Date.now()}`;
  
  try {
    // 创建测试表
    await knex.schema.createTable(testTableName, table => {
      table.increments('id');
      table.string('file_name').notNullable();
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    
    console.log(`创建测试表: ${testTableName}`);
    
    // 插入测试数据
    for (const filename of testFilenames) {
      await knex(testTableName).insert({
        file_name: filename
      });
      console.log(`- 插入: ${filename}`);
    }
    
    // 读取测试数据
    const results = await knex(testTableName).select('*');
    console.log('\n从数据库读取:');
    results.forEach(row => {
      console.log(`[${row.id}] ${row.file_name}`);
    });
    
    // 清理测试表
    await knex.schema.dropTable(testTableName);
    console.log(`\n已删除测试表: ${testTableName}`);
    
  } catch (err) {
    console.error('数据库测试失败:', err);
  }
}

// 主函数
async function runTests() {
  console.log('======= 中文文件名测试脚本 =======');
  
  const testFiles = createTestFiles();
  testFilenameEncoding();
  await testDatabaseStorage();
  
  console.log('\n所有测试完成!');
}

// 运行测试
runTests().catch(err => {
  console.error('测试失败:', err);
  process.exit(1);
}); 