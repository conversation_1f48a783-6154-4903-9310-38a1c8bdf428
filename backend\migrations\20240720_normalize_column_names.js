/**
 * 列名规范化迁移 - 将所有驼峰命名列改为下划线命名
 */
exports.up = async function(knex) {
  try {
  // users表
  await renameColumnsToSnakeCase(knex, 'users', [
    { from: 'passwordHash', to: 'password_hash' },
    { from: 'phoneNumber', to: 'phone_number' },
    { from: 'isActive', to: 'is_active' },
    { from: 'lastLoginAt', to: 'last_login_at' },
    { from: 'activeDiseaseLimit', to: 'active_disease_limit' },
    { from: 'aiUsageCount', to: 'ai_usage_count' },
    { from: 'aiUsageResetAt', to: 'ai_usage_reset_at' },
    { from: 'familyMemberLimit', to: 'family_member_limit' },
    { from: 'updatedAt', to: 'updated_at' }
  ]);

  // user_level_limits表
  await renameColumnsToSnakeCase(knex, 'user_level_limits', [
    { from: 'levelType', to: 'level_type' },
    { from: 'maxPatients', to: 'max_patients' },
    { from: 'maxPathologies', to: 'max_pathologies' },
    { from: 'maxAttachmentSize', to: 'max_attachment_size' },
    { from: 'maxTotalStorage', to: 'max_total_storage' }
  ]);

  // subscriptions表
  await renameColumnsToSnakeCase(knex, 'subscriptions', [
    { from: 'userId', to: 'user_id' },
    { from: 'startDate', to: 'start_date' },
    { from: 'endDate', to: 'end_date' },
    { from: 'createdAt', to: 'created_at' }
  ]);

  // patients表
  await renameColumnsToSnakeCase(knex, 'patients', [
    { from: 'birthDate', to: 'birth_date' },
    { from: 'phoneNumber', to: 'phone_number' },
    { from: 'idCard', to: 'id_card' },
    { from: 'medicareCard', to: 'medicare_card' },
    { from: 'medicareLocation', to: 'medicare_location' },
    { from: 'emergencyContactName', to: 'emergency_contact_name' },
    { from: 'emergencyContactPhone', to: 'emergency_contact_phone' },
    { from: 'emergencyContactRelationship', to: 'emergency_contact_relationship' },
    { from: 'pastMedicalHistory', to: 'past_medical_history' },
    { from: 'familyMedicalHistory', to: 'family_medical_history' },
    { from: 'allergyHistory', to: 'allergy_history' },
    { from: 'bloodType', to: 'blood_type' },
    { from: 'lastVisitDate', to: 'last_visit_date' },
    { from: 'deletedAt', to: 'deleted_at' },
    { from: 'userId', to: 'user_id' },
    { from: 'createdAt', to: 'created_at' },
    { from: 'updatedAt', to: 'updated_at' },
    { from: 'isPrimary', to: 'is_primary' }
  ]);

  // diseases表
  await renameColumnsToSnakeCase(knex, 'diseases', [
    { from: 'patientId', to: 'patient_id' },
    { from: 'diagnosisDate', to: 'diagnosis_date' },
    { from: 'isDeleted', to: 'is_deleted' },
    { from: 'createdAt', to: 'created_at' },
    { from: 'updatedAt', to: 'updated_at' },
    { from: 'isPrivate', to: 'is_private' },
    { from: 'isActive', to: 'is_active' },
    { from: 'isChronic', to: 'is_chronic' },
    { from: 'userId', to: 'user_id' }
  ]);

  // tag_categories表
  await renameColumnsToSnakeCase(knex, 'tag_categories', [
    { from: 'isSystem', to: 'is_system' },
    { from: 'createdBy', to: 'created_by' }
  ]);

  // tags表
  await renameColumnsToSnakeCase(knex, 'tags', [
    { from: 'categoryId', to: 'category_id' },
    { from: 'createdBy', to: 'created_by' }
  ]);

  // record_tags表
  await renameColumnsToSnakeCase(knex, 'record_tags', [
    { from: 'recordId', to: 'record_id' },
    { from: 'tagId', to: 'tag_id' },
    { from: 'createdBy', to: 'created_by' }
  ]);

  // records表
  await renameColumnsToSnakeCase(knex, 'records', [
    { from: 'recordDate', to: 'record_date' },
    { from: 'patientId', to: 'patient_id' },
    { from: 'diseaseId', to: 'disease_id' },
    { from: 'userId', to: 'user_id' },
    { from: 'createdBy', to: 'created_by' },
    { from: 'recordType', to: 'record_type' },
    { from: 'primaryType', to: 'primary_type' },
    { from: 'typeTagsJson', to: 'type_tags_json' },
    { from: 'stageTags', to: 'stage_tags' },
    { from: 'stageNode', to: 'stage_node' },
    { from: 'stagePhase', to: 'stage_phase' },
    { from: 'isPrivate', to: 'is_private' },
    { from: 'isImportant', to: 'is_important' },
    { from: 'customTags', to: 'custom_tags' },
    { from: 'deletedAt', to: 'deleted_at' }
  ]);

  // attachments表
  await renameColumnsToSnakeCase(knex, 'attachments', [
    { from: 'recordId', to: 'record_id' }
  ]);
    
    return Promise.resolve();
  } catch (error) {
    console.error('列名规范化迁移失败:', error);
    return Promise.reject(error);
  }
};

exports.down = async function(knex) {
  try {
  // users表
  await renameColumnsToSnakeCase(knex, 'users', [
    { from: 'password_hash', to: 'passwordHash' },
    { from: 'phone_number', to: 'phoneNumber' },
    { from: 'is_active', to: 'isActive' },
    { from: 'last_login_at', to: 'lastLoginAt' },
    { from: 'active_disease_limit', to: 'activeDiseaseLimit' },
    { from: 'ai_usage_count', to: 'aiUsageCount' },
    { from: 'ai_usage_reset_at', to: 'aiUsageResetAt' },
    { from: 'family_member_limit', to: 'familyMemberLimit' },
    { from: 'updated_at', to: 'updatedAt' }
  ]);

  // user_level_limits表
  await renameColumnsToSnakeCase(knex, 'user_level_limits', [
    { from: 'level_type', to: 'levelType' },
    { from: 'max_patients', to: 'maxPatients' },
    { from: 'max_pathologies', to: 'maxPathologies' },
    { from: 'max_attachment_size', to: 'maxAttachmentSize' },
    { from: 'max_total_storage', to: 'maxTotalStorage' }
  ]);

  // subscriptions表
  await renameColumnsToSnakeCase(knex, 'subscriptions', [
    { from: 'user_id', to: 'userId' },
    { from: 'start_date', to: 'startDate' },
    { from: 'end_date', to: 'endDate' },
    { from: 'created_at', to: 'createdAt' }
  ]);

  // patients表
  await renameColumnsToSnakeCase(knex, 'patients', [
    { from: 'birth_date', to: 'birthDate' },
    { from: 'phone_number', to: 'phoneNumber' },
    { from: 'id_card', to: 'idCard' },
    { from: 'medicare_card', to: 'medicareCard' },
    { from: 'medicare_location', to: 'medicareLocation' },
    { from: 'emergency_contact_name', to: 'emergencyContactName' },
    { from: 'emergency_contact_phone', to: 'emergencyContactPhone' },
    { from: 'emergency_contact_relationship', to: 'emergencyContactRelationship' },
    { from: 'past_medical_history', to: 'pastMedicalHistory' },
    { from: 'family_medical_history', to: 'familyMedicalHistory' },
    { from: 'allergy_history', to: 'allergyHistory' },
    { from: 'blood_type', to: 'bloodType' },
    { from: 'last_visit_date', to: 'lastVisitDate' },
    { from: 'deleted_at', to: 'deletedAt' },
    { from: 'user_id', to: 'userId' },
    { from: 'created_at', to: 'createdAt' },
    { from: 'updated_at', to: 'updatedAt' },
    { from: 'is_primary', to: 'isPrimary' }
  ]);

  // diseases表
  await renameColumnsToSnakeCase(knex, 'diseases', [
    { from: 'patient_id', to: 'patientId' },
    { from: 'diagnosis_date', to: 'diagnosisDate' },
    { from: 'is_deleted', to: 'isDeleted' },
    { from: 'created_at', to: 'createdAt' },
    { from: 'updated_at', to: 'updatedAt' },
    { from: 'is_private', to: 'isPrivate' },
    { from: 'is_active', to: 'isActive' },
    { from: 'is_chronic', to: 'isChronic' },
    { from: 'user_id', to: 'userId' }
  ]);

  // tag_categories表
  await renameColumnsToSnakeCase(knex, 'tag_categories', [
    { from: 'is_system', to: 'isSystem' },
    { from: 'created_by', to: 'createdBy' },
    { from: 'created_at', to: 'createdAt' },
    { from: 'updated_at', to: 'updatedAt' }
  ]);

  // tags表
  await renameColumnsToSnakeCase(knex, 'tags', [
    { from: 'category_id', to: 'categoryId' },
    { from: 'created_by', to: 'createdBy' },
    { from: 'created_at', to: 'createdAt' },
    { from: 'updated_at', to: 'updatedAt' }
  ]);

  // record_tags表
  await renameColumnsToSnakeCase(knex, 'record_tags', [
    { from: 'record_id', to: 'recordId' },
    { from: 'tag_id', to: 'tagId' },
    { from: 'created_by', to: 'createdBy' },
    { from: 'created_at', to: 'createdAt' },
    { from: 'updated_at', to: 'updatedAt' }
  ]);

  // records表
  await renameColumnsToSnakeCase(knex, 'records', [
    { from: 'record_date', to: 'recordDate' },
    { from: 'patient_id', to: 'patientId' },
    { from: 'disease_id', to: 'diseaseId' },
    { from: 'user_id', to: 'userId' },
    { from: 'created_by', to: 'createdBy' },
    { from: 'record_type', to: 'recordType' },
    { from: 'primary_type', to: 'primaryType' },
    { from: 'type_tags_json', to: 'typeTagsJson' },
    { from: 'stage_tags', to: 'stageTags' },
    { from: 'stage_node', to: 'stageNode' },
    { from: 'stage_phase', to: 'stagePhase' },
    { from: 'is_private', to: 'isPrivate' },
    { from: 'is_important', to: 'isImportant' },
    { from: 'custom_tags', to: 'customTags' },
    { from: 'deleted_at', to: 'deletedAt' }
  ]);

  // attachments表
  await renameColumnsToSnakeCase(knex, 'attachments', [
    { from: 'record_id', to: 'recordId' },
    { from: 'file_name', to: 'fileName' },
    { from: 'file_path', to: 'filePath' },
    { from: 'file_type', to: 'fileType' },
    { from: 'file_size', to: 'fileSize' },
    { from: 'uploaded_by', to: 'uploadedBy' },
    { from: 'uploaded_at', to: 'uploadedAt' },
    { from: 'created_at', to: 'createdAt' },
    { from: 'updated_at', to: 'updatedAt' }
  ]);
    
    return Promise.resolve();
  } catch (error) {
    console.error('列名规范化回滚失败:', error);
    return Promise.reject(error);
  }
};

/**
 * 辅助函数：重命名表的列名
 */
async function renameColumnsToSnakeCase(knex, tableName, columns) {
  // 查询表结构以检查列是否存在
  // PostgreSQL兼容查询：从 information_schema.columns 获取列信息
      const tableInfo = await knex.raw(`SELECT column_name AS name FROM information_schema.columns WHERE table_schema = 'public' AND table_name = '${tableName}'`);
  
  // 处理PostgreSQL结果集结构 - 确保能正确获取列名
  const rows = tableInfo.rows || tableInfo;
  const existingColumns = Array.isArray(rows) ? rows.map(col => col.name) : [];
  
  // 对每个列名执行重命名操作
  for (const {from, to} of columns) {
    // 检查源列名是否存在
    if (existingColumns.includes(from)) {
      console.log(`重命名 ${tableName}.${from} → ${to}`);
      await knex.schema.table(tableName, table => {
        table.renameColumn(from, to);
      });
    } else if (!existingColumns.includes(to)) {
      console.log(`列 ${tableName}.${from} 不存在，且 ${to} 也不存在，跳过`);
    } else {
      console.log(`列 ${tableName}.${from} 不存在，但 ${to} 已存在，跳过`);
    }
  }
  
  return Promise.resolve();
}