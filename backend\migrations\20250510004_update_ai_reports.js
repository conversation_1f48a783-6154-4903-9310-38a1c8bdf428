/**
 * 更新AI报告表结构
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.alterTable('ai_reports', table => {
    // 修改llm_raw_response字段为JSON类型
    table.json('llm_raw_response').nullable().comment('LLM原始响应内容（JSON格式）').alter();
    
    // 添加llm_configs字段
    table.json('llm_configs').nullable().comment('LLM调用配置（JSON格式）');
    
    // 添加config_id字段，关联到ai_report_configs表
    table.integer('config_id').nullable().references('id').inTable('ai_report_configs');
    
    // 添加visible_fields字段，用于控制报告内容的可见性
    table.json('visible_fields').nullable().comment('当前报告可见的字段列表');
  });
};

/**
 * 回滚AI报告表结构
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.alterTable('ai_reports', table => {
    // 恢复llm_raw_response字段为text类型
    table.text('llm_raw_response').nullable().comment('LLM原始响应内容（JSON格式）').alter(); // 移除 'longtext' 参数以兼容 PostgreSQL
    
    // 删除新增的字段
    table.dropColumn('llm_configs');
    table.dropColumn('config_id');
    table.dropColumn('visible_fields');
  });
};