/**
 * 创建AI报告表
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.createTable('ai_reports', table => {
    table.uuid('id').primary();
    table.uuid('disease_id').notNullable().references('id').inTable('diseases').onDelete('CASCADE');
    table.uuid('patient_id').notNullable().references('id').inTable('patients').onDelete('CASCADE');
    table.uuid('user_id').notNullable().references('id').inTable('users').onDelete('CASCADE');
    table.uuid('record_id').nullable().references('id').inTable('records').onDelete('SET NULL');
    table.string('title', 255).notNullable();
    table.enum('template_type', ['COMPREHENSIVE_ANALYSIS']).defaultTo('COMPREHENSIVE_ANALYSIS').notNullable();
    table.json('anonymized_info').nullable().comment('匿名化后的信息，包含原始姓名和匿名化后的姓名');
    table.text('llm_raw_response').nullable().comment('LLM原始响应内容（JSON格式）'); // PostgreSQL 中 TEXT 类型足以存储长文本，移除了 'longtext' 参数
    table.json('content').notNullable().comment('分析报告内容（结构化JSON）');
    table.string('pdf_url', 255).nullable().comment('PDF报告的存储路径');
    table.enum('status', ['PROCESSING', 'COMPLETED', 'FAILED']).defaultTo('PROCESSING').notNullable();
    table.text('error_message').nullable();
    table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());
    table.timestamp('updated_at', { useTz: true }).defaultTo(knex.fn.now());

    // 添加索引
    table.index('disease_id', 'ai_report_disease_idx');
    table.index('patient_id', 'ai_report_patient_idx');
    table.index('user_id', 'ai_report_user_idx');
    table.index('record_id', 'ai_report_record_idx');
    table.index('status', 'ai_report_status_idx');
  });
};

/**
 * 删除AI报告表
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.dropTableIfExists('ai_reports');
};