import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// 主题存储接口
interface ThemeState {
  isDarkMode: boolean;  // 是否为深色模式
  toggleTheme: () => void;  // 切换主题的方法
}

// 创建主题存储，并持久化到localStorage
export const useThemeStore = create<ThemeState>()(
  persist(
    (set) => ({
      isDarkMode: false,  // 默认为浅色模式
      toggleTheme: () => set((state) => ({ isDarkMode: !state.isDarkMode })),
    }),
    {
      name: 'theme-storage',  // localStorage的键名
    }
  )
); 