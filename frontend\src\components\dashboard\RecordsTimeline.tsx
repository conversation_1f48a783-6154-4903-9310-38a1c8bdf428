import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Chip, 
  TextField,
  InputAdornment,
  Stack,
  CircularProgress,
  Divider,
  Button,
  Tooltip,
  IconButton,
  useTheme,
  Alert
} from '@mui/material';
import { 
  Search as SearchIcon,
  FilterList as FilterIcon,
  Event as EventIcon,
  MedicalServices as MedicalIcon,
  Science as ScienceIcon,
  ReceiptLong as RecordIcon,
  HealthAndSafety as HealthIcon,
  LocalHospital as HospitalIcon,
  Visibility as ViewIcon,
  ChevronRight as ChevronRightIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { usePatientDiseaseContext } from '../../context/PatientDiseaseContext';
import { getRecords } from '../../services/recordService';

// 记录类型定义
type RecordType = '治疗' | '检查' | '确诊' | '随访' | '用药';

// 记录项接口
interface RecordItem {
  id: string;
  title: string;
  type: RecordType;
  date: string;
  content: string;
}

// 将后端返回的记录数据映射为前端需要的格式
const mapRecordData = (record: any): RecordItem => {
  // 记录类型映射
  let recordType: RecordType = '随访';
  
  // 处理primaryType
  if (record.primaryType || record.primary_type) {
    // 使用驼峰或下划线命名的字段
    const primaryType = (record.primaryType || record.primary_type || '').toUpperCase();
    
    // 根据primaryType设置类型
    switch(primaryType) {
      case 'TREATMENT': recordType = '治疗'; break;
      case 'EXAM': 
      case 'EXAMINATION': recordType = '检查'; break;
      case 'DIAGNOSIS': 
      case 'DIAGNOSIS_CONFIRMATION': recordType = '确诊'; break;
      case 'FOLLOWUP': 
      case 'FOLLOW_UP': recordType = '随访'; break;
      case 'MEDICATION': recordType = '用药'; break;
      default: recordType = '随访';
    }
  } 
  // 尝试解析recordType字段
  else if (record.recordType || record.record_type) {
    let types: string[] = [];
    try {
      const typeData = record.recordType || record.record_type;
      
      // 根据类型处理
      if (typeof typeData === 'string') {
        // 处理可能的双重JSON字符串
        const cleanString = typeData.replace(/\\\"/g, '"');
        types = JSON.parse(cleanString);
      } else if (Array.isArray(typeData)) {
        types = typeData;
      }
    } catch (e) {
      console.warn('解析记录类型失败:', e);
      types = [];
    }
    
    // 使用第一个记录类型
    if (types.length > 0) {
      const firstType = types[0].toUpperCase();
      if (firstType === 'TREATMENT') recordType = '治疗';
      else if (firstType === 'EXAM' || firstType === 'EXAMINATION') recordType = '检查';
      else if (firstType === 'DIAGNOSIS' || firstType === 'DIAGNOSIS_CONFIRMATION') recordType = '确诊';
      else if (firstType === 'FOLLOWUP' || firstType === 'FOLLOW_UP') recordType = '随访';
      else if (firstType === 'MEDICATION') recordType = '用药';
    }
  }

  // 处理日期字段
  let recordDate = '';
  if (record.recordDate || record.record_date) {
    recordDate = record.recordDate || record.record_date;
    // 如果日期包含T，只取日期部分
    if (recordDate && recordDate.includes('T')) {
      recordDate = recordDate.split('T')[0];
    }
  } else if (record.created_at || record.createdAt) {
    // 使用创建日期作为备选
    recordDate = record.created_at || record.createdAt;
    // 如果是日期时间字符串，只保留日期部分
    if (recordDate && recordDate.includes(' ')) {
      recordDate = recordDate.split(' ')[0];
    }
  } else {
    // 默认使用当前日期
    recordDate = new Date().toISOString().split('T')[0];
  }

  return {
    id: record.id,
    title: record.title || '无标题记录',
    type: recordType,
    date: recordDate,
    content: record.content || record.description || ''
  };
};

/**
 * 记录时间轴组件
 * 按照时间顺序显示医疗记录
 */
const RecordsTimeline: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { selectedPatientId, selectedDiseaseId } = usePatientDiseaseContext();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [records, setRecords] = useState<RecordItem[]>([]);
  const [searchText, setSearchText] = useState('');
  const [activeFilters, setActiveFilters] = useState<RecordType[]>([]);

  // 获取记录图标
  const getRecordIcon = (type: RecordType) => {
    switch (type) {
      case '治疗':
        return <MedicalIcon sx={{ color: theme.palette.primary.main }} />;
      case '检查':
        return <ScienceIcon sx={{ color: theme.palette.secondary.main }} />;
      case '确诊':
        return <HealthIcon sx={{ color: theme.palette.info.main }} />;
      case '用药':
        return <HospitalIcon sx={{ color: theme.palette.warning.main }} />;
      case '随访':
        return <RecordIcon sx={{ color: theme.palette.success.main }} />;
      default:
        return <MedicalIcon />;
    }
  };

  // 获取记录数据
  useEffect(() => {
    if (selectedPatientId || selectedDiseaseId) {
      setLoading(true);
      setError(null);
      
      // 构建过滤条件
      const filters: any = {};
      if (selectedPatientId) {
        filters.patientId = selectedPatientId;
      }
      if (selectedDiseaseId) {
        filters.diseaseId = selectedDiseaseId;
      }
      
      // 调用API获取记录
      getRecords(filters)
        .then((data: any) => {
          console.log('获取记录API响应:', data);  // 临时日志，调试用
          
          // 处理不同的响应格式
          let recordsData = data;
          
          // 检查是否是包含records属性的对象格式
          if (data && typeof data === 'object' && !Array.isArray(data) && data.records) {
            recordsData = data.records;
          }
          
          if (Array.isArray(recordsData)) {
            // 排除已删除的记录
            const activeRecords = recordsData.filter(record => !record.deleted_at);
            // 映射并设置记录
            const mappedRecords = activeRecords.map(mapRecordData);
            setRecords(mappedRecords);
            
            if (activeRecords.length === 0) {
              console.log('没有找到有效记录');
            }
          } else {
            console.error('获取记录响应格式不正确:', data);
            setError('获取记录数据格式不正确');
            setRecords([]);
          }
        })
        .catch((err: Error) => {
          console.error('获取记录失败:', err);
          setError('获取记录失败，请稍后再试');
          setRecords([]);
        })
        .finally(() => {
          setLoading(false);
        });
    } else {
      setRecords([]);
    }
  }, [selectedPatientId, selectedDiseaseId]);

  // 处理筛选切换
  const handleFilterToggle = (type: RecordType) => {
    setActiveFilters(prev => {
      if (prev.includes(type)) {
        return prev.filter(t => t !== type);
      } else {
        return [...prev, type];
      }
    });
  };

  // 筛选和搜索记录
  const filteredRecords = records.filter(record => {
    // 筛选类型
    if (activeFilters.length > 0 && !activeFilters.includes(record.type)) {
      return false;
    }
    
    // 搜索文本
    if (searchText) {
      const searchLower = searchText.toLowerCase();
      return (
        record.title.toLowerCase().includes(searchLower) ||
        record.content.toLowerCase().includes(searchLower)
      );
    }
    
    return true;
  });

  // 处理查看记录详情
  const handleViewRecord = (recordId: string) => {
    navigate(`/records/${recordId}`);
  };

  // 处理创建新记录
  const handleCreateRecord = () => {
    navigate('/records');
  };

  // 按日期分组记录
  const groupedRecords = records.reduce((groups: Record<string, RecordItem[]>, record) => {
    const date = record.date;
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(record);
    return groups;
  }, {});

  // 日期数组（排序）
  const dates = Object.keys(groupedRecords).sort((a, b) => 
    new Date(b).getTime() - new Date(a).getTime()
  );

  // 格式化日期显示
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('zh-CN', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      weekday: 'long'
    });
  };

  // 加载中或错误状态渲染
  if (loading) {
    return (
      <Paper 
        elevation={0}
        sx={{ 
          p: { xs: 2, sm: 3 },
          borderRadius: 2,
          border: '1px solid',
          borderColor: 'divider',
          display: 'flex',
          justifyContent: 'center'
        }}
      >
        <CircularProgress />
      </Paper>
    );
  }

  if (error) {
    return (
      <Paper 
        elevation={0}
        sx={{ 
          p: { xs: 2, sm: 3 },
          borderRadius: 2,
          border: '1px solid',
          borderColor: 'divider'
        }}
      >
        <Alert severity="error">
          {error}
        </Alert>
      </Paper>
    );
  }

  if (!selectedPatientId && !selectedDiseaseId) {
    return (
      <Paper 
        elevation={0}
        sx={{ 
          p: { xs: 2, sm: 3 },
          borderRadius: 2,
          border: '1px solid',
          borderColor: 'divider'
        }}
      >
        <Alert severity="info">
          请先选择患者和病理查看相关记录
        </Alert>
      </Paper>
    );
  }

  if (records.length === 0) {
    return (
      <Paper 
        elevation={0}
        sx={{ 
          p: { xs: 2, sm: 3 },
          borderRadius: 2,
          border: '1px solid',
          borderColor: 'divider'
        }}
      >
        <Alert severity="info">
          暂无相关记录
        </Alert>
      </Paper>
    );
  }

  return (
    <Paper 
      elevation={0}
      sx={{ 
        p: { xs: 2, sm: 3 },
        borderRadius: 2,
        border: '1px solid',
        borderColor: 'divider'
      }}
    >
      <Typography 
        variant="h6" 
        sx={{ 
          mb: 3, 
          fontWeight: 'bold',
          fontSize: { xs: '1.1rem', sm: '1.2rem', md: '1.3rem' }
        }}
      >
        记录时间轴
      </Typography>

      {dates.map((date, index) => (
        <Box key={date} sx={{ mb: 4 }}>
          <Typography 
            variant="subtitle1" 
            sx={{ 
              fontWeight: 'bold',
              color: 'primary.main',
              mb: 2
            }}
          >
            {formatDate(date)}
          </Typography>
          
          {groupedRecords[date].map((record: RecordItem) => (
            <Paper
              key={record.id}
              elevation={1}
              sx={{
                p: 2,
                mb: 2,
                borderLeft: `4px solid ${
                  record.type === '治疗' ? theme.palette.primary.main :
                  record.type === '检查' ? theme.palette.secondary.main :
                  record.type === '确诊' ? theme.palette.info.main :
                  record.type === '用药' ? theme.palette.warning.main :
                  theme.palette.success.main
                }`
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Chip 
                    icon={getRecordIcon(record.type)} 
                    label={record.type} 
                    size="small"
                    sx={{ mr: 2 }}
                  />
                  <Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>
                    {record.title}
                  </Typography>
                </Box>
                <Tooltip title="查看详情">
                  <IconButton 
                    size="small" 
                    onClick={() => handleViewRecord(record.id)}
                  >
                    <ViewIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
              <Typography variant="body2" color="text.secondary">
                {record.content}
              </Typography>
            </Paper>
          ))}
        </Box>
      ))}
    </Paper>
  );
};

export default RecordsTimeline; 