import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  SelectChangeEvent,
  CircularProgress,
  Chip,
  Tooltip,
  Tabs,
  Tab,
  Breadcrumbs,
  TableRow,
  TableCell,
  Paper,
  Alert
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import VisibilityIcon from '@mui/icons-material/Visibility';
import RefreshIcon from '@mui/icons-material/Refresh';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import { Link } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import serviceReportService from '../services/serviceReportService';
import ServiceContextBar from '../components/service/ServiceContextBar';
import ServiceItemList from '../components/common/ServiceItemList';
import TabPanel from '../components/common/TabPanel';
import { useServiceContext } from '../hooks/useServiceContext';
import { ServiceReport, ReportType, ReportStatus } from '../types/serviceTypes';
import { logError } from '../utils/errorHandler';

// 服务报告管理页面组件
const ServiceReportManage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  
  // 使用自定义Hook管理服务上下文
  const { 
    serviceContext, 
    setShowContextSelector, 
    error, 
    setError, 
    isValidContext,
    isCompleteContext,
    validateContext 
  } = useServiceContext();

  // 组件状态
  const [tabValue, setTabValue] = useState(0);
  const [reports, setReports] = useState<ServiceReport[]>([]);
  const [loading, setLoading] = useState(false);
  const [openCreateDialog, setOpenCreateDialog] = useState(false);
  const [openViewDialog, setOpenViewDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [selectedReport, setSelectedReport] = useState<ServiceReport | null>(null);
  const [aiLoading, setAiLoading] = useState(false);
  
  // 表单数据默认值
  const defaultFormData = {
    reportType: 'DIAGNOSIS' as ReportType,
    promptOverrides: {} as Record<string, string>
  };
  
  const [formData, setFormData] = useState(defaultFormData);

  // 加载服务报告数据，使用useCallback包装
  const loadReportsData = useCallback(async () => {
    if (!serviceContext.authorizationId) return;
    
    setLoading(true);
    setError('');
    try {
      const response = await serviceReportService.getServiceReportsByAuth(serviceContext.authorizationId);
      if (response.success) {
        // 如果有选择患者，则过滤出与该患者相关的报告
        const filteredReports = serviceContext.patientId 
          ? response.data.filter((report: ServiceReport) => report.patientId === serviceContext.patientId) 
          : response.data;
        
        setReports(filteredReports);
      }
    } catch (err) {
      logError(err, '加载服务报告');
      setError('加载服务报告失败');
    } finally {
      setLoading(false);
    }
  }, [serviceContext.authorizationId, serviceContext.patientId, setError]);

  // 首次加载
  useEffect(() => {
    if (serviceContext.authorizationId && serviceContext.patientId) {
      loadReportsData();
    }
  }, [serviceContext.authorizationId, serviceContext.patientId, loadReportsData]);

  // 切换标签
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // 打开创建报告对话框
  const openCreateReportDialog = () => {
    validateContext(() => {
      // 如果没有选择病理，则无法创建AI报告
      if (!serviceContext.diseaseId) {
        setError('请先选择病理');
        navigate('/service-diseases');
        return;
      }
      
      setFormData(defaultFormData);
      setOpenCreateDialog(true);
    }, true); // 需要病理
  };

  // 关闭创建报告对话框
  const handleCloseCreateDialog = () => {
    setOpenCreateDialog(false);
  };

  // 打开查看报告对话框
  const handleOpenViewDialog = (report: ServiceReport) => {
    setSelectedReport(report);
    setOpenViewDialog(true);
  };

  // 关闭查看报告对话框
  const handleCloseViewDialog = () => {
    setOpenViewDialog(false);
    setSelectedReport(null);
  };

  // 打开删除报告对话框
  const handleOpenDeleteDialog = (report: ServiceReport) => {
    setSelectedReport(report);
    setOpenDeleteDialog(true);
  };

  // 关闭删除报告对话框
  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    setSelectedReport(null);
  };

  // 处理选择框变化
  const handleSelectChange = (e: SelectChangeEvent<string>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // 创建报告
  const handleCreateReport = async () => {
    // 验证服务上下文信息
    if (!serviceContext.authorizationId) {
      setError('请先选择授权关系');
      setShowContextSelector(true);
      return;
    }

    if (!serviceContext.diseaseId) {
      setError('请选择病理');
      return;
    }

    // 添加上下文信息到报告数据
    const reportData = {
      ...formData,
      authorizationId: serviceContext.authorizationId,
      patientId: serviceContext.patientId || undefined,
      diseaseId: serviceContext.diseaseId
    };

    try {
      setAiLoading(true);
      const response = await serviceReportService.createServiceReport(reportData);
      if (response.success) {
        setOpenCreateDialog(false);
        loadReportsData();
      }
    } catch (err) {
      logError(err, '创建服务报告');
      setError('创建报告失败');
    } finally {
      setAiLoading(false);
    }
  };

  // 生成AI报告
  const handleGenerateAIReport = async (reportId: string) => {
    try {
      setAiLoading(true);
      const response = await serviceReportService.generateAIReport(reportId);
      if (response.success) {
        loadReportsData();
      }
    } catch (err) {
      logError(err, '生成AI报告');
      setError('生成AI报告失败');
    } finally {
      setAiLoading(false);
    }
  };

  // 删除报告
  const handleDeleteReport = async () => {
    if (!selectedReport) return;

    try {
      const response = await serviceReportService.deleteServiceReport(selectedReport.id);
      if (response.success) {
        setOpenDeleteDialog(false);
        loadReportsData();
      }
    } catch (err) {
      logError(err, '删除服务报告');
      setError('删除报告失败');
    }
  };

  // 渲染报告状态
  const renderReportStatus = (status: ReportStatus) => {
    switch (status) {
      case 'PENDING':
        return <Chip label="待处理" color="warning" size="small" />;
      case 'PROCESSING':
        return <Chip label="处理中" color="info" size="small" />;
      case 'COMPLETED':
        return <Chip label="已完成" color="success" size="small" />;
      case 'FAILED':
        return <Chip label="失败" color="error" size="small" />;
      default:
        return <Chip label="未知状态" color="default" size="small" />;
    }
  };

  // 渲染报告类型
  const renderReportType = (type: ReportType) => {
    switch (type) {
      case 'DIAGNOSIS':
        return <Chip label="诊断报告" color="primary" size="small" variant="outlined" />;
      case 'TREATMENT':
        return <Chip label="治疗方案" color="secondary" size="small" variant="outlined" />;
      case 'ANALYSIS':
        return <Chip label="病情分析" color="info" size="small" variant="outlined" />;
      case 'SUMMARY':
        return <Chip label="病情总结" color="success" size="small" variant="outlined" />;
      default:
        return <Chip label="其他" color="default" size="small" variant="outlined" />;
    }
  };

  // 检查用户是否有权限管理（编辑/删除）指定的报告
  const canManageReport = (report: ServiceReport): boolean => {
    // 只能删除自己创建的报告
    const isCreator = report.serviceUserId === user?.id;
    
    // 检查服务上下文中的权限级别
    const hasPermission = isCreator;
    
    return hasPermission;
  };
  
  // 表格列定义
  const columns = [
    { id: 'type', label: '类型', minWidth: 120 },
    { id: 'status', label: '状态', minWidth: 100 },
    { id: 'date', label: '创建日期', minWidth: 150 },
    { id: 'disease', label: '病理', minWidth: 150 },
    { id: 'creator', label: '创建者', minWidth: 120 },
    { id: 'actions', label: '操作', minWidth: 150, align: 'center' as const }
  ];
  
  // 渲染表格行
  const renderRow = (report: ServiceReport, index: number) => (
    <TableRow key={report.id} hover>
      <TableCell>{renderReportType(report.reportType)}</TableCell>
      <TableCell>{renderReportStatus(report.status)}</TableCell>
      <TableCell>
        {report.createdAt ? new Date(report.createdAt).toLocaleDateString() : '未知'}
      </TableCell>
      <TableCell>
        {report.disease?.name || serviceContext.diseaseName || '未知病理'}
      </TableCell>
      <TableCell>
        {report.serviceUser?.username || '未知'}
      </TableCell>
      <TableCell align="center">
        <Box sx={{ display: 'flex', justifyContent: 'center' }}>
          <Tooltip title="查看">
            <IconButton 
              size="small" 
              color="info"
              onClick={() => handleOpenViewDialog(report)}
            >
              <VisibilityIcon />
            </IconButton>
          </Tooltip>
          
          {report.status === 'PENDING' && (
            <Tooltip title="生成">
              <IconButton 
                size="small" 
                color="primary"
                onClick={() => handleGenerateAIReport(report.id)}
                disabled={aiLoading}
              >
                <SmartToyIcon />
              </IconButton>
            </Tooltip>
          )}
          
          {canManageReport(report) && (
            <Tooltip title="删除">
              <IconButton 
                size="small" 
                color="error"
                onClick={() => handleOpenDeleteDialog(report)}
              >
                <DeleteIcon />
              </IconButton>
            </Tooltip>
          )}
        </Box>
      </TableCell>
    </TableRow>
  );

  // 渲染权限级别提示
  const renderPrivacyLevelAlert = () => {
    if (!serviceContext.privacyLevel) return null;
    
    let alertType = 'info';
    let message = '';
    
    switch (serviceContext.privacyLevel) {
      case 'BASIC':
        alertType = 'warning';
        message = '您当前拥有基础授权级别，只能查看报告，无法创建新报告。';
        break;
      case 'STANDARD':
        alertType = 'info';
        message = '您当前拥有标准授权级别，可以创建报告。';
        break;
      case 'FULL':
        alertType = 'success';
        message = '您当前拥有完整授权级别，可以进行所有操作。';
        break;
    }
    
    return (
      <Alert severity={alertType as any} sx={{ mb: 2 }}>
        {message}
      </Alert>
    );
  };

  return (
    <Box sx={{ px: 2 }}>
      {/* ServiceContextBar */}
      <ServiceContextBar />
      
      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 2 }}>
        <Link to="/service-dashboard" style={{ textDecoration: 'none', color: 'inherit' }}>
          服务首页
        </Link>
        <Typography color="text.primary">服务报告</Typography>
      </Breadcrumbs>
      
      {/* 页面标题和操作按钮 */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h5" component="h1">
          智能服务报告
        </Typography>
        <Box>
          <Button 
            variant="contained" 
            startIcon={<RefreshIcon />} 
            size="small"
            onClick={loadReportsData}
            sx={{ mr: 1 }}
          >
            刷新
          </Button>
          <Button 
            variant="contained" 
            color="primary" 
            startIcon={<AddIcon />}
            size="small"
            onClick={openCreateReportDialog}
            disabled={!isCompleteContext}
          >
            创建AI报告
          </Button>
        </Box>
      </Box>
      
      {/* 标签导航 */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
        <Tabs value={tabValue} onChange={handleTabChange}>
          <Tab label="所有报告" />
          <Tab label="我创建的" />
        </Tabs>
      </Box>
      
      {/* 报告列表 */}
      <TabPanel value={tabValue} index={0} id="service-report">
        <ServiceItemList
          columns={columns}
          data={reports}
          loading={loading}
          error={error}
          emptyMessage="暂无报告数据"
          contextValid={Boolean(isValidContext)}
          renderRow={renderRow}
        />
      </TabPanel>
      
      <TabPanel value={tabValue} index={1} id="my-service-report">
        <ServiceItemList
          columns={columns}
          data={reports.filter(report => report.serviceUserId === user?.id)}
          loading={loading}
          error={error}
          emptyMessage="暂无您创建的报告"
          contextValid={Boolean(isValidContext)}
          renderRow={renderRow}
        />
      </TabPanel>
      
      {/* 创建报告对话框 */}
      <Dialog open={openCreateDialog} onClose={handleCloseCreateDialog} maxWidth="sm" fullWidth>
        <DialogTitle>创建AI报告</DialogTitle>
        <DialogContent>
          <FormControl fullWidth margin="normal">
            <InputLabel id="reportType-label">报告类型</InputLabel>
            <Select
              labelId="reportType-label"
              name="reportType"
              value={formData.reportType}
              label="报告类型"
              onChange={handleSelectChange}
            >
              <MenuItem value="DIAGNOSIS">疾病诊断</MenuItem>
              <MenuItem value="TREATMENT">治疗方案</MenuItem>
              <MenuItem value="ANALYSIS">病情分析</MenuItem>
              <MenuItem value="SUMMARY">病情总结</MenuItem>
            </Select>
          </FormControl>
          
          <Box sx={{ mt: 2, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
            <Typography variant="body2" color="info.contrastText">
              将基于患者 {serviceContext.patientName} 的 {serviceContext.diseaseName} 病理信息生成AI报告。
              所有与该病理相关的记录都将被AI分析。
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseCreateDialog}>取消</Button>
          <Button 
            onClick={handleCreateReport} 
            variant="contained" 
            color="primary"
            disabled={aiLoading}
          >
            {aiLoading ? <CircularProgress size={24} /> : '创建'}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* 查看报告对话框 */}
      <Dialog open={openViewDialog} onClose={handleCloseViewDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedReport?.reportType === 'DIAGNOSIS' ? '疾病诊断' :
           selectedReport?.reportType === 'TREATMENT' ? '治疗方案' :
           selectedReport?.reportType === 'ANALYSIS' ? '病情分析' :
           selectedReport?.reportType === 'SUMMARY' ? '病情总结' : 'AI报告'}
        </DialogTitle>
        <DialogContent>
          {selectedReport?.status === 'COMPLETED' ? (
            <Paper variant="outlined" sx={{ p: 2, mt: 2 }}>
              <Typography
                component="div"
                dangerouslySetInnerHTML={{ __html: selectedReport?.aiReport?.content || '报告内容为空' }}
              />
            </Paper>
          ) : (
            <Box sx={{ mt: 2, p: 2, bgcolor: selectedReport?.status === 'FAILED' ? 'error.light' : 'info.light', borderRadius: 1 }}>
              <Typography variant="body1" color={selectedReport?.status === 'FAILED' ? 'error.contrastText' : 'info.contrastText'}>
                {selectedReport?.status === 'PENDING' && '报告等待生成中...'}
                {selectedReport?.status === 'PROCESSING' && '报告正在生成中...'}
                {selectedReport?.status === 'FAILED' && '报告生成失败，请重试'}
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseViewDialog}>关闭</Button>
          {selectedReport?.status === 'PENDING' && (
            <Button 
              onClick={() => handleGenerateAIReport(selectedReport.id)}
              variant="contained" 
              color="primary"
              startIcon={<SmartToyIcon />}
              disabled={aiLoading}
            >
              {aiLoading ? <CircularProgress size={24} /> : '立即生成'}
            </Button>
          )}
          {selectedReport?.status === 'FAILED' && (
            <Button 
              onClick={() => handleGenerateAIReport(selectedReport.id)}
              variant="contained" 
              color="primary"
              startIcon={<RefreshIcon />}
              disabled={aiLoading}
            >
              {aiLoading ? <CircularProgress size={24} /> : '重新生成'}
            </Button>
          )}
        </DialogActions>
      </Dialog>
      
      {/* 删除报告确认对话框 */}
      <Dialog open={openDeleteDialog} onClose={handleCloseDeleteDialog}>
        <DialogTitle>确认删除</DialogTitle>
        <DialogContent>
          <Typography>
            您确定要删除这份AI报告吗？此操作不可逆。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog}>取消</Button>
          <Button 
            onClick={handleDeleteReport} 
            variant="contained" 
            color="error"
          >
            删除
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* 错误提示 */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}
      
      {/* 权限级别提示 */}
      {renderPrivacyLevelAlert()}
    </Box>
  );
};

export default ServiceReportManage; 