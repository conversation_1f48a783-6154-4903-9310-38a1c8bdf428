/**
 * AI报告内容过滤服务
 * 根据用户角色和相应的可见性配置过滤报告内容
 */

/**
 * 根据用户角色过滤报告内容
 * @param {Object} reportContent - 完整的报告内容
 * @param {string} userRole - 用户角色 ('USER' 或 'SERVICE_USER')
 * @param {Object} config - 报告可见性配置
 * @returns {Object} 过滤后的报告内容
 */
const filterReportContent = (reportContent, userRole, config) => {
  if (!reportContent) {
    throw new Error('报告内容为空');
  }

  // 如果没有配置，使用默认配置
  if (!config) {
    config = {
      userVisibleFields: ['summary', 'emergencyGuidance', 'hospitalRecommendations', 'lifestyleAndMentalHealth'],
      serviceVisibleFields: ['summary', 'differentialDiagnosis', 'emergencyGuidance', 'hospitalRecommendations', 'treatmentPlan', 'budgetEstimation', 'crossRegionGuidance', 'lifestyleAndMentalHealth', 'riskWarnings']
    };
  }

  // 复制一份原始内容，避免修改原对象
  const filteredContent = { ...reportContent };
  
  // 确定当前用户可见的字段
  const visibleFields = userRole === 'SERVICE_USER' ? 
    config.serviceVisibleFields : 
    config.userVisibleFields;
  
  // 收集当前报告中所有可过滤的字段
  const allFields = Object.keys(reportContent).filter(field => 
    field !== 'dashboardData' && // dashboardData 始终可见，不参与过滤
    field !== 'disclaimer' &&    // 免责声明始终可见
    typeof reportContent[field] === 'object'
  );
  
  // 过滤不可见字段
  allFields.forEach(field => {
    if (!visibleFields.includes(field)) {
      delete filteredContent[field];
    }
  });
  
  return filteredContent;
};

/**
 * 确定用户角色
 * @param {Object} user - 用户对象
 * @returns {string} 用户角色 ('USER' 或 'SERVICE_USER')
 */
const determineUserRole = (user) => {
  if (!user) return 'USER';
  
  // 根据用户对象的属性确定用户角色
  // SERVICE_USER角色包括医生、导医、客服等系统服务人员
  if (user.role === 'DOCTOR' || 
      user.role === 'GUIDE' || 
      user.role === 'CUSTOMER_SERVICE' || 
      user.role === 'ADMIN' ||
      user.role === 'SERVICE' ||
      user.level === 'PROFESSIONAL') {
    return 'SERVICE_USER';
  }
  
  return 'USER';
};

module.exports = {
  filterReportContent,
  determineUserRole
}; 