import React, { useState, useEffect, useRef, useMemo } from 'react';
import { 
  Box, 
  Tabs, 
  Tab, 
  useMediaQuery,
  useTheme,
  CircularProgress,
  Typography,
  Button
} from '@mui/material';
import { 
  AddCircleOutline as AddIcon
} from '@mui/icons-material';
import { usePatientDiseaseContext } from '../context/PatientDiseaseContext';
import UserInfoCalendar from '../components/dashboard/UserInfoCalendar';
import StatisticsPanel from '../components/dashboard/StatisticsPanel';
import PatientCard from '../components/dashboard/PatientSelector/PatientCard';
import DiseaseCard from '../components/dashboard/DiseaseCard';
import RecordCard from '../components/dashboard/RecordCard';
import { getPatients } from '../services/patientService';
import { getPatientDiseases } from '../services/diseaseService';
import { getRecords } from '../services/recordService';
import { Patient } from '../types/patient';
import { Disease } from '../types/disease';
import { useNavigate, useLocation } from 'react-router-dom';
// import { } from '../components/records/EnhancedMedicalRecordTimelineExport'; // REMOVE THIS EMPTY IMPORT STATEMENT
import EnhancedMedicalRecordTimeline from '../components/records/EnhancedMedicalRecordTimeline';
// 引入病理Tab页面组件
import DiseaseTab from '../components/dashboard/disease-tab/DiseaseTab';
import dataCache from '../utils/dataCache';

// 标签页接口
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

// 标签页内容组件
const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`dashboard-tabpanel-${index}`}
      aria-labelledby={`dashboard-tab-${index}`}
      style={{ width: '100%' }}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 0 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

// 获取标签属性
const a11yProps = (index: number) => {
  return {
    id: `dashboard-tab-${index}`,
    'aria-controls': `dashboard-tabpanel-${index}`,
  };
};

/**
 * Dashboard主页面组件
 * 包含概览、病理和记录三个标签页
 */
const Dashboard: React.FC = () => {
  // 当前选中的标签页
  const [tabValue, setTabValue] = useState(0);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const navigate = useNavigate();
  const location = useLocation(); // 使用useLocation钩子获取当前URL信息
  const { selectedPatientId, selectedDiseaseId, setSelectedPatient, setSelectedDisease, syncState } = usePatientDiseaseContext();
  
  // 患者数据状态
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // 病理数据状态
  const [diseases, setDiseases] = useState<Disease[]>([]);
  const [loadingDiseases, setLoadingDiseases] = useState(false);
  
  // 记录数据状态
  const [records, setRecords] = useState<any[]>([]);
  const [filteredRecords, setFilteredRecords] = useState<any[]>([]);
  const [loadingRecords, setLoadingRecords] = useState(false);
  
  // 为记录标签页添加单独的状态
  const [timelineRecords, setTimelineRecords] = useState<any[]>([]);
  const [loadingTimelineRecords, setLoadingTimelineRecords] = useState(false);
  
  // 将useRef移到组件顶层，修复Hook使用错误
  const prevDiseaseIdsRef = useRef('');
  
  // 使用useMemo缓存疾病ID字符串，避免不必要的重新计算
  const diseaseIdsString = useMemo(() => {
    return diseases.map(d => d.id).sort().join(',');
  }, [diseases]);

  // 处理URL参数
  useEffect(() => {
    // 解析URL参数
    const searchParams = new URLSearchParams(location.search);
    const tabParam = searchParams.get('tab');
    const diseaseIdParam = searchParams.get('diseaseId');
    const patientIdParam = searchParams.get('patientId');
    
    console.log('[Dashboard] 解析URL参数:', {
      tab: tabParam,
      diseaseId: diseaseIdParam,
      patientId: patientIdParam
    });
    
    // 参数解析顺序：1. 患者ID 2. 病理ID 3. Tab值
    // 这样可以确保正确的依赖关系
    
    // 1. 首先设置患者ID（如果存在）
    if (patientIdParam && patientIdParam !== selectedPatientId) {
      setSelectedPatient(patientIdParam);
    }
    
    // 2. 然后设置病理ID（在下一个useEffect中处理，因为依赖于selectedPatientId）
    
    // 3. 最后设置tab值
    if (tabParam) {
      if (tabParam === 'records') {
        setTabValue(2); // 记录标签页
      } else if (tabParam === 'disease') {
        setTabValue(1); // 病理标签页
      } else {
        const tabIndex = parseInt(tabParam, 10);
        if (!isNaN(tabIndex) && tabIndex >= 0 && tabIndex <= 2) {
          setTabValue(tabIndex);
        }
      }
    }
  }, [location.search, selectedPatientId, setSelectedPatient]);
  
  // 处理病理ID参数（依赖于患者ID）
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const diseaseIdParam = searchParams.get('diseaseId');
    
    // 如果没有当前选中的患者ID，清除病理ID参数
    if (!selectedPatientId) {
      if (diseaseIdParam) {
        // 移除URL中的diseaseId参数
        searchParams.delete('diseaseId');
        const newUrl = `${window.location.pathname}?${searchParams.toString()}`;
        window.history.pushState({}, '', newUrl);
      }
      return;
    }
    
    // 只有在有患者ID和URL中有病理ID参数的情况下才尝试设置病理ID
    if (selectedPatientId && diseaseIdParam && diseaseIdParam !== selectedDiseaseId) {
      console.log(`[Dashboard] URL参数中有病理ID: ${diseaseIdParam}，尝试验证并设置`);
      
      // 如果还没有加载完病理列表，不进行设置，等待病理列表加载完成
      if (loadingDiseases) {
        console.log(`[Dashboard] 病理列表正在加载中，暂不设置病理ID`);
        return;
      }
      
      // 查找该病理是否属于当前患者
      const isBelongsToPatient = diseases.some(disease => disease.id === diseaseIdParam);
      
      if (isBelongsToPatient) {
        console.log(`[Dashboard] 设置病理ID: ${diseaseIdParam} (属于当前患者)`);
        setSelectedDisease(diseaseIdParam);
      } else {
        // 如果病理不属于当前患者，移除URL中的病理ID参数
        console.log(`[Dashboard] 病理ID ${diseaseIdParam} 不属于当前患者，移除URL参数`);
        searchParams.delete('diseaseId');
        const newUrl = `${window.location.pathname}?${searchParams.toString()}`;
        window.history.pushState({}, '', newUrl);
        
        // 确保内存中的选择状态也被清除
        if (selectedDiseaseId) {
          setSelectedDisease(null);
        }
      }
    }
  }, [selectedPatientId, selectedDiseaseId, diseases, setSelectedDisease, location.search, loadingDiseases]);
  
  // 从后端获取患者数据
  useEffect(() => {
    const fetchPatients = async () => {
      try {
        setLoading(true);
        const patientsData = await getPatients();
        setPatients(patientsData);
        setError(null);
      } catch (err) {
        console.error('获取患者列表失败:', err);
        setError('获取患者数据失败，请稍后再试');
      } finally {
        setLoading(false);
      }
    };
    
    fetchPatients();
  }, []);
  
  // 从后端获取选中患者的病理数据
  useEffect(() => {
    const fetchPatientDiseases = async () => {
      if (!selectedPatientId) {
        setDiseases([]);
        return;
      }
      
      try {
        setLoadingDiseases(true);
        const diseasesData = await getPatientDiseases(selectedPatientId);
        
        // 直接设置新数据，不再比较新旧数据
        setDiseases(diseasesData);
        console.log('病理数据已更新');
      } catch (err) {
        console.error('获取患者病理列表失败:', err);
      } finally {
        setLoadingDiseases(false);
      }
    };
    
    fetchPatientDiseases();
    
    // 设置定期刷新，确保显示最新的病理数据（包括隐私状态）
    const refreshTimer = setInterval(() => {
      if (selectedPatientId) {
        // 使用静默刷新，不显示加载状态
        const silentFetch = async () => {
          try {
            const diseasesData = await getPatientDiseases(selectedPatientId);
            setDiseases(diseasesData);
            console.log('静默刷新：病理数据已更新');
          } catch (err) {
            console.error('静默刷新病理数据失败:', err);
          }
        };
        
        silentFetch();
      }
    }, 30000); // 将刷新间隔改为30秒
    
    return () => clearInterval(refreshTimer); // 清理定时器
    
  }, [selectedPatientId]); // 移除 diseases 依赖，只依赖于 selectedPatientId
  
  // 获取选中病理的记录数据 (用于概览标签页)
  useEffect(() => {
    const fetchDiseaseRecords = async () => {
      if (!selectedDiseaseId) {
        setRecords([]);
        setFilteredRecords([]); // 清空记录
        return;
      }
      
      try {
        setLoadingRecords(true);
        console.log(`[Dashboard] 开始获取病理 ${selectedDiseaseId} 的记录数据...`);
        
        // 获取病理的所有记录，不使用SQL筛选
        const response = await getRecords({ 
          diseaseId: selectedDiseaseId,
          limit: 300 // 增加limit确保获取所有记录
          // 移除where_clause条件，在前端进行筛选
        });
        
        // 处理不同的响应格式
        let recordsData: any[] = [];
        
        if (Array.isArray(response)) {
          // 直接是数组的情况
          recordsData = response;
        } else if (response && typeof response === 'object') {
          // 对象格式，检查是否有records字段
          if (response.records && Array.isArray(response.records)) {
            // 标准分页响应格式 {records: Array, meta: {...}}
            recordsData = response.records;
          } else {
            // 其他对象格式，但没有records数组字段
            console.error('获取到的记录数据格式不正确:', response);
            recordsData = []; // 设置为空数组
          }
        } else {
          // 其他情况
          console.error('获取到的记录数据格式不支持:', response);
          recordsData = []; // 设置为空数组
        }
        
        console.log(`[Dashboard] 获取到 ${recordsData.length} 条原始记录`);
        
        // 在前端进行筛选时，使用更宽松的条件，允许更多字段配对，以展示所有有效的病程节点
        const filteredData = recordsData.filter(record => {
          // 检查是否有病程节点 - 考虑多种可能的字段名
          const hasStageNode = Boolean(
            record.stage_node || 
            record.stageNode || 
            record.stage_node_id || 
            record.stageNodeId ||
            record.stage || // 新增匹配字段 - stage
            record.node || // 新增匹配字段 - node
            record.stage_phase || // 新增匹配字段 - stage_phase
            record.stagePhase || // 新增匹配字段 - stagePhase
            (record.recordType === 'STAGE_CHANGE' || record.record_type === 'STAGE_CHANGE') // 根据记录类型判断
          );
          
          // 检查记录类型 - 排除AI分析类型
          let isNotAIAnalysis = true;
          const recordType = record.record_type || record.recordType;
          if (recordType) {
            isNotAIAnalysis = String(recordType).toUpperCase() !== 'AI_ANALYSIS';
          }
          
          // 检查是否被删除 - deleted_at为空表示未删除
          // 使用更宽松的删除检查，null、undefined或空字符串都视为未删除
          const isNotDeleted = (
            record.deleted_at === null || 
            record.deleted_at === undefined || 
            record.deleted_at === '' ||
            record.deletedAt === null ||
            record.deletedAt === undefined ||
            record.deletedAt === ''
          );
          
          // 输出更详细的筛选过程日志
          if (hasStageNode) {
            console.log(`[Dashboard] 病程节点记录: ID=${record.id}, 标题=${record.title || record.name || '无标题'}, 删除状态=${isNotDeleted}, 记录类型=${recordType}, 保留=${hasStageNode && isNotDeleted && isNotAIAnalysis}`);
          }
          
          return hasStageNode && isNotDeleted && isNotAIAnalysis;
        });
        
        // 设置记录状态
        setRecords(recordsData);  // 保存原始记录
        setFilteredRecords(filteredData); // 使用过滤后的数据
        
        // 记录筛选结果
        console.log(`[Dashboard] 从后端获取到 ${recordsData.length} 条记录，筛选后剩余 ${filteredData.length} 条关键病程节点记录`);
        console.log('[Dashboard] 筛选后的记录:', filteredData.map(r => ({
          id: r.id,
          title: r.title || r.name,
          stageNode: r.stage_node || r.stageNode || r.stage || r.node || r.stage_phase || r.stagePhase || 'undefined',
          recordType: r.recordType || r.record_type
        })));
      } catch (err) {
        console.error('获取病理记录列表失败:', err);
        setRecords([]);
        setFilteredRecords([]);
      } finally {
        setLoadingRecords(false);
      }
    };
    
    fetchDiseaseRecords();
  }, [selectedDiseaseId]);

  // 获取选中病理的所有记录数据 (用于记录标签页时间轴)
  useEffect(() => {
    const fetchTimelineRecords = async () => {
      if (!selectedDiseaseId) {
        setTimelineRecords([]); // 清空记录
        return;
      }
      
      try {
        setLoadingTimelineRecords(true);
        // 获取病理的所有记录，设置大的limit值来确保获取全部记录
        const response = await getRecords({ 
          diseaseId: selectedDiseaseId,
          limit: 1000, // 显式设置一个大的limit值，确保获取所有记录
          // 不设置where_clause，获取所有记录而不仅仅是有病程节点的记录
        });
        
        // 处理不同的响应格式
        let recordsData: any[] = [];
        
        if (Array.isArray(response)) {
          // 直接是数组的情况
          recordsData = response;
        } else if (response && typeof response === 'object') {
          // 对象格式，检查是否有records字段
          if (response.records && Array.isArray(response.records)) {
            // 标准分页响应格式 {records: Array, meta: {...}}
            recordsData = response.records;
          } else {
            // 其他对象格式，但没有records数组字段
            console.error('获取到的记录数据格式不正确:', response);
            recordsData = []; // 设置为空数组
          }
        } else {
          // 其他情况
          console.error('获取到的记录数据格式不支持:', response);
          recordsData = []; // 设置为空数组
        }
        
        // 更新时间轴记录状态
        setTimelineRecords(recordsData);
        
        // 记录获取结果
        console.log(`从后端获取到用于时间轴的病理记录 ${recordsData.length} 条`);
      } catch (err) {
        console.error('获取时间轴记录列表失败:', err);
        setTimelineRecords([]);
      } finally {
        setLoadingTimelineRecords(false);
      }
    };
    
    fetchTimelineRecords();
  }, [selectedDiseaseId]);

  // 加载所有病理的病程记录 - 优化此函数避免无限渲染
  useEffect(() => {
    // 防止不必要的加载
    if (!diseases || diseases.length === 0) {
      return;
    }
    
    // 只有当疾病ID字符串变化时才执行
    if (diseaseIdsString !== prevDiseaseIdsRef.current) {
      prevDiseaseIdsRef.current = diseaseIdsString;
      
      const fetchAllDiseaseRecords = async () => {
        try {
          console.log(`[Dashboard] 开始预加载所有病理(${diseases.length}个)的记录...`);
          
          // 创建一个存储所有记录的数组
          let allRecords: any[] = [];
          
          // 按序加载每个病理的记录
          for (const disease of diseases) {
            console.log(`[Dashboard] 预加载病理 ${disease.name} (${disease.id}) 的记录`);
            
            const response = await getRecords({ 
              diseaseId: disease.id,
              limit: 300 // 增加限制，确保获取足够的记录
              // 移除SQL筛选条件，在前端进行筛选
            });
            
            let diseaseRecords: any[] = [];
            
            if (Array.isArray(response)) {
              diseaseRecords = response;
            } else if (response && typeof response === 'object' && response.records) {
              diseaseRecords = response.records;
            }
            
            console.log(`[Dashboard] 病理 ${disease.name} 获取到 ${diseaseRecords.length} 条原始记录`);
            
            // 在前端进行筛选，使用与fetchDiseaseRecords相同的宽松条件
            const filteredRecords = diseaseRecords.filter(record => {
              // 检查是否有病程节点 - 考虑多种可能的字段名
              const hasStageNode = Boolean(
                record.stage_node || 
                record.stageNode || 
                record.stage_node_id || 
                record.stageNodeId ||
                record.stage || 
                record.node || 
                record.stage_phase || 
                record.stagePhase || 
                (record.recordType === 'STAGE_CHANGE' || record.record_type === 'STAGE_CHANGE')
              );
              
              // 检查是否被删除 - 使用宽松的条件
              const isNotDeleted = (
                record.deleted_at === null || 
                record.deleted_at === undefined || 
                record.deleted_at === '' ||
                record.deletedAt === null ||
                record.deletedAt === undefined ||
                record.deletedAt === ''
              );
              
              // 检查记录类型 - 排除AI分析类型
              let isNotAIAnalysis = true;
              const recordType = record.record_type || record.recordType;
              if (recordType) {
                isNotAIAnalysis = String(recordType).toUpperCase() !== 'AI_ANALYSIS';
              }
              
              return hasStageNode && isNotDeleted && isNotAIAnalysis;
            });
            
            console.log(`[Dashboard] 病理 ${disease.name} 筛选后保留 ${filteredRecords.length} 条记录`);
            
            // 将筛选后的记录添加到总记录列表
            allRecords = [...allRecords, ...filteredRecords];
          }
          
          console.log(`[Dashboard] 预加载并筛选了 ${allRecords.length} 条关键病程节点记录`);
          
          // 如果当前没有选中病理，则使用所有记录作为筛选记录
          if (!selectedDiseaseId) {
            setRecords(allRecords);
            setFilteredRecords(allRecords);
          }
        } catch (error) {
          console.error('[Dashboard] 预加载病程记录失败:', error);
        }
      };
      
      fetchAllDiseaseRecords();
    }
  }, [diseaseIdsString, selectedDiseaseId, diseases]);

  // 组件挂载时同步一次患者和病理状态，确保界面展示正确
  useEffect(() => {
    // 同步Context状态与localStorage
    syncState();
    
    console.log('[Dashboard] 初始化时同步状态完成:', {
      selectedPatientId,
      selectedDiseaseId
    });
  }, [syncState, selectedPatientId, selectedDiseaseId]);

  // 处理标签页切换
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    
    // 构建新的URL参数
    const searchParams = new URLSearchParams(window.location.search);
    
    // 更新tab参数
    if (newValue === 0) {
      searchParams.delete('tab'); // 概览标签页不需要在URL中显示
    } else if (newValue === 1) {
      searchParams.set('tab', 'disease');
    } else if (newValue === 2) {
      searchParams.set('tab', 'records');
    }
    
    // 保留其他参数
    if (selectedDiseaseId) {
      searchParams.set('diseaseId', selectedDiseaseId);
    } else {
      searchParams.delete('diseaseId');
    }
    
    // 更新URL，不刷新页面
    const newUrl = `${window.location.pathname}?${searchParams.toString()}`;
    window.history.pushState({}, '', newUrl);
  };

  // 根据出生日期计算年龄
  const calculateAge = (birthDate: string): number => {
    if (!birthDate) return 0;
    
    try {
      const today = new Date();
      const birthDateObj = new Date(birthDate);
      let age = today.getFullYear() - birthDateObj.getFullYear();
      const monthDiff = today.getMonth() - birthDateObj.getMonth();
      
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDateObj.getDate())) {
        age--;
      }
      
      return age;
    } catch (e) {
      console.error('计算年龄出错:', e);
      return 0;
    }
  };

  // 性别适配器函数
  const getGenderAdapter = (gender: string): 'male' | 'female' => {
    if (gender === '男' || gender.toUpperCase() === 'MALE') {
      return 'male';
    }
    return 'female';
  };

  // 关系适配器函数
  const getRelationshipAdapter = (patient: Patient): string => {
    // 如果是本人档案
    if (patient.isPrimary === 1) {
      return 'SELF';
    }
    
    // 根据紧急联系人关系判断
    if (patient.emergencyContactRelationship) {
      // 确保关系值大写，匹配常量定义
      return patient.emergencyContactRelationship.toUpperCase();
    }
    
    // 默认作为家属
    return 'FAMILY';
  };
  
  const handlePatientSelect = (patientId: string) => {
    console.log('[Dashboard] 选择患者:', patientId);
    
    // 当前患者ID，用于检查是否是一次真正的患者切换
    const isPatientChanged = patientId !== selectedPatientId;
    
    if (isPatientChanged) {
      // 如果是切换患者，强制执行以下步骤
      
      // 1. 首先清除localStorage中的disease ID
      localStorage.removeItem('selectedDiseaseId');
      
      // 2. 然后清理所有状态
      setSelectedDisease(null); // 先显式清除病理选择
      dataCache.clearDiseaseRelatedCache(); // 清除所有病理相关缓存
      setRecords([]);
      setFilteredRecords([]);
      setTimelineRecords([]);
      
      // 3. 设置新的患者ID
      setSelectedPatient(patientId);
      
      // 4. 确保URL参数也被更新
      const searchParams = new URLSearchParams(window.location.search);
      searchParams.delete('diseaseId'); // 删除病理ID参数
      searchParams.delete('tab'); // 返回概览标签
      const newUrl = `${window.location.pathname}?${searchParams.toString()}`;
      window.history.pushState({}, '', newUrl);
      
      // 5. 切换到概览标签页
      setTabValue(0);
      
      console.log('[Dashboard] 已切换患者并清除病理选择:', patientId);
    } else {
      console.log('[Dashboard] 选择的患者与当前相同，跳过处理:', patientId);
    }
  };
  
  // 处理病理选择
  const handleDiseaseSelect = (diseaseId: string) => {
    console.log('[Dashboard] 处理病理选择:', diseaseId, '当前选中:', selectedDiseaseId);
    
    // 如果选择的病理已经是当前选中的，不做任何操作
    if (diseaseId === selectedDiseaseId) {
      console.log('[Dashboard] 病理已选中，跳过处理');
      return;
    }
    
    // 清除相关的缓存
    dataCache.clearDiseaseRelatedCache(diseaseId);
    
    // 清空当前记录，避免显示过时数据
    setRecords([]);
    setFilteredRecords([]);
    
    // 设置新的病理ID
    setSelectedDisease(diseaseId);
    
    // 更新URL参数，不刷新页面
    const searchParams = new URLSearchParams(window.location.search);
    searchParams.set('diseaseId', diseaseId);
    const newUrl = `${window.location.pathname}?${searchParams.toString()}`;
    window.history.pushState({}, '', newUrl);
    
    console.log(`[Dashboard] 已设置选中的病理: ${diseaseId}`);
  };
  
  // 处理添加新病理
  const handleAddDisease = () => {
    if (selectedPatientId) {
      // 修改导航目标到 /diseases，并可选地传递patientId
      // 如果 /diseases 页面能自动使用 context 中的 selectedPatientId，则 patientId 参数可以省略
      navigate(`/diseases?patientId=${selectedPatientId}`); 
      // 或者，如果 /diseases 页面总是显示当前选定患者的病理（通过上下文）:
      // navigate('/diseases'); 
    } else {
      // 如果没有选中的患者，则直接跳转到通用的病理列表页面
      navigate('/diseases');
    }
  };
  
  // 处理添加新记录
  const handleAddRecord = () => {
    if (selectedPatientId && selectedDiseaseId) {
      navigate(`/records/new?patientId=${selectedPatientId}&diseaseId=${selectedDiseaseId}`);
    }
  };
  
  // 处理查看记录详情
  const handleViewRecord = (recordId: string) => {
    console.log('记录查看请求已传递到EnhancedMedicalRecordTimeline组件内部处理');
  };

  // 渲染患者卡片部分
  const renderPatientCards = () => {
    if (loading) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
          <CircularProgress size={30} />
        </Box>
      );
    }

    if (error) {
      return (
        <Box sx={{ p: 1, textAlign: 'center', color: 'error.main' }}>
          <Typography variant="body2">{error}</Typography>
        </Box>
      );
    }

    // 确保 patients 是数组
    const patientsArray = Array.isArray(patients) ? patients : [];
    return (
      <>
        <Box 
          sx={{ 
            display: 'grid',
            gridTemplateColumns: 'repeat(4, 1fr)', // 始终保持4个排一排
            gap: { xs: 1, sm: 1.5 },
            '& > *': {
              minHeight: '100%'
            }
          }}
        >
          {patientsArray.slice().reverse().map((patient) => (
            <Box 
              key={patient.id}
            >
              <PatientCard 
                id={patient.id}
                name={patient.name}
                age={calculateAge(patient.birthDate)}
                gender={getGenderAdapter(patient.gender)}
                relationship={getRelationshipAdapter(patient)}
                maxDiseases={5}
                birthDate={patient.birthDate}
                bloodType={patient.bloodType}
                phoneNumber={patient.phoneNumber}
                isSelected={patient.id === selectedPatientId}
                onClick={() => handlePatientSelect(patient.id)}
              />
            </Box>
          ))}
        </Box>
      </>
    );
  };
  
  // 渲染病理卡片部分
  const renderDiseaseCards = () => {
    if (!selectedPatientId) {
      return (
        <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>
          <Typography variant="body2">请先选择一位患者</Typography>
        </Box>
      );
    }
    
    if (loadingDiseases) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
          <CircularProgress size={30} />
        </Box>
      );
    }
    
    if (diseases.length === 0) {
      return (
        <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>
          <Typography variant="body2">该患者暂无病理记录</Typography>
          <Button 
            startIcon={<AddIcon />}
            size="small"
            sx={{ mt: 1 }}
            onClick={handleAddDisease}
          >
            添加病理
          </Button>
        </Box>
      );
    }
    
    // 确保所有记录按病理ID分组，并且只显示当前患者的病理相关记录
    const validRecords = (selectedPatientId && Array.isArray(filteredRecords)) 
      ? filteredRecords.filter(record => {
          // 检查记录的病理ID - 考虑多种可能的字段名
          const recordDiseaseId = record.disease_id || record.diseaseId;
          
          // 检查是否有病程节点 - 考虑多种可能的字段名
          const hasStageNode = Boolean(
            record.stage_node || 
            record.stageNode || 
            record.stage_node_id || 
            record.stageNodeId
          );
          
          // 检查是否被删除 - deleted_at为空表示未删除
          const isNotDeleted = record.deleted_at === null || record.deleted_at === undefined;
          
          // 检查记录类型 - 排除AI分析类型
          let isNotAIAnalysis = true;
          const recordType = record.record_type || record.recordType;
          if (recordType) {
            isNotAIAnalysis = String(recordType).toUpperCase() !== 'AI_ANALYSIS';
          }
          
          // 检查该记录是否属于当前患者的任何一个病理
          const belongsToCurrentPatient = diseases.some(disease => disease.id === recordDiseaseId);
          
          return belongsToCurrentPatient && hasStageNode && isNotDeleted && isNotAIAnalysis;
        })
      : [];
    
    console.log(`[Dashboard] 渲染病理卡片: 找到 ${validRecords.length} 条有效记录用于展示病程节点`);
    
    return (
      <>
        <Box 
          sx={{ 
            display: 'grid',
            gridTemplateColumns: {
              xs: 'repeat(1, 1fr)',
              sm: 'repeat(auto-fill, minmax(210px, 1fr))'
            },
            gap: 0
          }}
        >
          {diseases.map((disease) => {
            // 为每个病理卡片找到对应的记录
            const diseaseRecords = validRecords.filter(record => 
              (record.disease_id === disease.id || record.diseaseId === disease.id)
            );
            
            return (
              <DiseaseCard
                key={disease.id}
                id={disease.id}
                name={disease.name}
                diagnosisDate={disease.diagnosisDate}
                createdAt={disease.createdAt}
                isPrivate={disease.isPrivate === 1}
                stages={diseaseRecords}
                isSelected={disease.id === selectedDiseaseId}
                onClick={() => handleDiseaseSelect(disease.id)}
              />
            );
          })}
        </Box>
      </>
    );
  };
  
  // 渲染记录卡片部分
  const renderRecordCards = () => {
    if (!selectedDiseaseId) {
      return (
        <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>
          <Typography variant="body2">请先选择一个病理</Typography>
        </Box>
      );
    }
    
    if (loadingRecords) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
          <CircularProgress size={30} />
        </Box>
      );
    }
    
    // 确保记录是数组
    const recordsToShow = Array.isArray(filteredRecords) ? filteredRecords : [];
    const totalRecords = Array.isArray(records) ? records.length : 0;
    
    // 筛选逻辑应该已经在fetchDiseaseRecords中完成，这里不再做重复筛选
    // 仅作为安全检查，确保所有记录都满足我们的条件
    const keyNodeRecords = recordsToShow.filter(record => {
      // 检查是否有病程节点 - 考虑多种可能的字段名
      const hasStageNode = Boolean(
        record.stage_node || 
        record.stageNode || 
        record.stage_node_id || 
        record.stageNodeId
      );
      
      // 检查是否被删除 - deleted_at为空表示未删除
      const isNotDeleted = record.deleted_at === null || record.deleted_at === undefined;
      
      // 检查记录类型 - 排除AI分析类型
      let isNotAIAnalysis = true;
      const recordType = record.record_type || record.recordType;
      if (recordType) {
        isNotAIAnalysis = String(recordType).toUpperCase() !== 'AI_ANALYSIS';
      }
      
      return hasStageNode && isNotDeleted && isNotAIAnalysis;
    });
    
    console.log(`[Dashboard] 渲染记录卡片: 从${recordsToShow.length}条记录中筛选出${keyNodeRecords.length}条病程节点记录`);
    
    // 按时间从新到旧排序（使用recordDate或created_at字段）
    const sortedRecords = [...keyNodeRecords].sort((a, b) => {
      // 尝试各种可能的日期字段
      const getDate = (record: any) => {
        const dateStr = record.recordDate || record.record_date || record.created_at || record.createdAt;
        if (!dateStr) return 0;
        try {
          return new Date(dateStr).getTime();
        } catch (e) {
          return 0;
        }
      };
      
      const dateA = getDate(a);
      const dateB = getDate(b);
      
      return dateB - dateA; // 降序排列，最新的在前面
    });
    
    if (sortedRecords.length === 0) {
      return (
        <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>
          <Typography variant="body2">
            {totalRecords > 0 
              ? '该病理没有包含病程节点的记录'
              : '该病理暂无记录'}
          </Typography>
          <Button 
            startIcon={<AddIcon />}
            size="small"
            sx={{ mt: 1 }}
            onClick={handleAddRecord}
          >
            添加记录
          </Button>
        </Box>
      );
    }
    
    return (
      <>
        <Box
          sx={{
            display: 'grid',
            gridTemplateColumns: {
              xs: 'repeat(1, 1fr)',
              sm: 'repeat(auto-fill, minmax(250px, 1fr))'
            },
            gap: { xs: 1.5, sm: 2 }
          }}
        >
          {sortedRecords.map((record) => (
            <RecordCard
              key={record.id}
              id={record.id}
              title={record.title || record.name || `记录 #${record.id}`}
              content={record.content || record.description || ''}
              recordDate={record.recordDate || record.record_date || record.created_at || record.createdAt}
              stageNode={record.stageNode || record.stage_node || record.stage_node_id || record.stageNodeId}
              stagePhase={record.stagePhase || record.stage_phase}
              typeTagsJson={record.typeTagsJson || record.type_tags_json}
              recordType={record.recordType || record.record_type}
              severity={record.severity}
              onClick={() => handleViewRecord(record.id)}
            />
          ))}
          <Box 
            sx={{ 
              display: 'flex', 
              justifyContent: 'center', 
              alignItems: 'center',
              border: '1px dashed',
              borderColor: 'divider',
              borderRadius: 2,
              p: 2,
              cursor: 'pointer',
              '&:hover': {
                borderColor: 'primary.main',
                bgcolor: 'background.paper'
              }
            }}
            onClick={handleAddRecord}
          >
            <AddIcon color="primary" sx={{ mr: 1 }} />
            <Typography variant="body2" color="primary">添加新记录</Typography>
          </Box>
        </Box>
      </>
    );
  };

  // 获取疾病阶段名称
  const getDiseaseStageName = (diseaseId: string): string => {
    // 从diseases数组中找到当前疾病
    const disease = diseases.find(d => d.id === diseaseId);
    if (!disease) return '随访';
    
    // 根据disease的stage属性或者根据最新记录判断阶段
    const stageMap: Record<string, string> = {
      'INITIAL': '初诊',
      'DIAGNOSIS': '确诊',
      'TREATMENT': '治疗',
      'RECOVERY': '恢复',
      'FOLLOW_UP': '随访',
      'PROGNOSIS': '预后',
      'ARCHIVE': '归档'
    };
    
    const stage = disease.stage || 'FOLLOW_UP';
    return stageMap[stage] || '随访';
  };

  return (
    <Box sx={{ 
      m: 0, 
      width: '100%',
      overflow: 'hidden',
      backgroundColor: 'transparent' // 确保背景透明
    }}>
      {/* Tab导航栏 */}
      <Box 
        sx={{ 
          width: '100%', 
          mb: 2,
          overflow: 'hidden'
        }}
      >
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          aria-label="dashboard tabs"
          variant={isMobile ? "fullWidth" : "standard"}
          centered={!isMobile}
          sx={{
            bgcolor: 'transparent',
            '& .MuiTab-root': {
              fontSize: { xs: '0.85rem', sm: '0.9rem', md: '1rem' },
              py: { xs: 1.5, sm: 2 },
              textTransform: 'none',
              fontWeight: 600 // 所有标签都使用粗体
            }
          }}
        >
          <Tab 
            label="概览"
            {...a11yProps(0)} 
          />
          <Tab 
            label="病理"
            {...a11yProps(1)} 
          />
          <Tab 
            label="记录"
            {...a11yProps(2)} 
          />
        </Tabs>
      </Box>

      {/* 只在概览Tab中显示这些组件 */}
      {tabValue === 0 && (
        <Box sx={{ px: 1.5 }}> {/* 减小内边距 */}
          {/* 用户信息和日历组件 */}
          <UserInfoCalendar />

          {/* 统计面板组件 */}
          <StatisticsPanel />
          
          {/* 患者选择区 */}
          <Box 
            sx={{ 
              mb: 1, 
              mt: 0, // 减小上边距
              width: '100%',
              px: 0
            }}
          >
            {renderPatientCards()}
          </Box>
          
          {/* 病理选择和记录区域 */}
          <Box sx={{ mt: 0.5, mb: 2, overflow: 'hidden' }}> {/* 将mt从1.5减少到0.5，整体向上移动10px */}
            <Box sx={{ mb: 3, overflow: 'hidden' }}> {/* 增加下边距 */}
              {renderDiseaseCards()}
            </Box>
            
            <Box sx={{ overflow: 'hidden', mt: 2.5 }}>
              {renderRecordCards()}
            </Box>
          </Box>
        </Box>
      )}

      {/* 概览标签页内容 */}
      <TabPanel value={tabValue} index={0}>
        <Box sx={{ mb: 0, pb: 0 }}>
          {/* 内容已在上方渲染 */}
        </Box>
      </TabPanel>

      {/* 病理标签页内容 */}
      <TabPanel value={tabValue} index={1}>
        <DiseaseTab />
      </TabPanel>

      {/* 记录标签页内容 */}
      <TabPanel value={tabValue} index={2}>
        <Box sx={{ p: 1 }}>
          {selectedDiseaseId ? (
            <React.Fragment>
              {loadingTimelineRecords ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress size={30} />
                </Box>
              ) : (
                <EnhancedMedicalRecordTimeline
                  records={timelineRecords}
                  onViewRecord={handleViewRecord}
                  headerTitle={`目前处于：${getDiseaseStageName(selectedDiseaseId)}期`}
                  isLoading={loadingTimelineRecords}
                  hasMore={false}
                  onAddRecord={handleAddRecord}
                />
              )}
            </React.Fragment>
          ) : (
            <Box sx={{ p: 3, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '200px' }}>
              <Typography variant="body1" color="text.secondary">
                请先选择一个病理查看相关记录
              </Typography>
            </Box>
          )}
        </Box>
      </TabPanel>
    </Box>
  );
};

export default Dashboard; 