# AI报告自动修复任务计划设置脚本
# 需要以管理员权限运行此脚本

# 获取当前路径
$currentPath = Get-Location
$scriptPath = Join-Path -Path $currentPath -ChildPath "scripts\fix_stuck_ai_reports.js"
$nodePath = "node.exe" # 如果node不在环境变量中，需要指定完整路径

# 创建动作
$action = New-ScheduledTaskAction -Execute $nodePath -Argument $scriptPath -WorkingDirectory $currentPath

# 创建触发器，每小时运行一次
$trigger = New-ScheduledTaskTrigger -Once -At (Get-Date) -RepetitionInterval (New-TimeSpan -Minutes 60)

# 任务名称
$taskName = "AZ_AI报告自动修复"
$description = "每小时运行一次，自动修复卡在处理中状态的AI报告"

# 创建或更新任务
$taskExists = Get-ScheduledTask -TaskName $taskName -ErrorAction SilentlyContinue

if ($taskExists) {
    # 任务已存在，更新它
    Write-Host "更新计划任务: $taskName"
    Set-ScheduledTask -TaskName $taskName -Action $action -Trigger $trigger -Description $description
}
else {
    # 创建新任务
    Write-Host "创建计划任务: $taskName"
    Register-ScheduledTask -TaskName $taskName -Action $action -Trigger $trigger -Description $description -RunLevel Highest
}

Write-Host "计划任务设置完成！每小时将自动运行一次AI报告修复脚本。"
Write-Host "脚本路径: $scriptPath"
Write-Host ""
Write-Host "你可以在Windows任务计划程序中查看和修改此任务。" 