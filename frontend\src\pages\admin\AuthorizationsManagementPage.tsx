import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Card,
  CardContent,
  Tooltip,
  CircularProgress,
  Autocomplete,
  Menu,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterListIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Add as AddIcon,
  Refresh as RefreshIcon,
  SecurityUpdateGood as SecurityIcon,
  Share as ShareIcon,
  Cancel as CancelIcon,
  MoreVert as MoreVertIcon,
  Key as KeyIcon,
  PersonOutline as GrantorIcon,
  HowToReg as GranteeIcon,
  Category as ScopeIcon,
  Timer as ExpiresIcon,
  Event as CreatedIcon
} from '@mui/icons-material';
import axios from 'axios';
import { API_BASE_URL } from '../../config/api';
import { API_PATHS } from '../../config/apiPaths';
import { useSnackbar } from 'notistack';
import { format } from 'date-fns';
import { useTheme, ThemeProvider, createTheme as createMuiTheme } from '@mui/material/styles';
import { useMediaQuery } from '@mui/material';

// 授权关系接口
interface Authorization {
  id: string;
  grantorId: string;
  grantorName: string;
  granteeId: string;
  granteeName: string;
  accessType: string;
  scope: string;
  targetId?: string;
  targetName?: string;
  status: string;
  createdAt: string;
  expiresAt?: string;
}

// 用户接口
interface User {
  id: string;
  username: string;
  email: string;
}

/**
 * 授权管理页面
 * 管理员可以查看和管理用户之间的授权关系
 */
const AuthorizationsManagementPage: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { enqueueSnackbar } = useSnackbar();

  const reducedFontSizeTheme = createMuiTheme(theme, {
    typography: {
      h1: { ...theme.typography.h1, fontSize: theme.typography.h1?.fontSize ? `calc(${theme.typography.h1.fontSize} - 0.4rem)`:'5.6rem' },
      h2: { ...theme.typography.h2, fontSize: theme.typography.h2?.fontSize ? `calc(${theme.typography.h2.fontSize} - 0.3rem)`:'3.45rem' },
      h3: { ...theme.typography.h3, fontSize: theme.typography.h3?.fontSize ? `calc(${theme.typography.h3.fontSize} - 0.25rem)`:'2.75rem' },
      h4: { ...theme.typography.h4, fontSize: theme.typography.h4?.fontSize ? `calc(${theme.typography.h4.fontSize} - 0.2rem)`:'1.925rem' },
      h5: { fontSize: '1.0rem' },
      h6: { fontSize: '0.85rem' },
      subtitle1: { fontSize: '0.75rem' },
      subtitle2: { fontSize: '0.65rem' },
      body1: { fontSize: '0.75rem' },
      body2: { fontSize: '0.65rem' },
      button: { fontSize: '0.65rem' },
      caption: { fontSize: '0.55rem' },
      overline: { fontSize: '0.55rem' },
    },
    components: {
      MuiInputLabel: { styleOverrides: { root: { fontSize: '0.75rem' } } },
      MuiMenuItem: { styleOverrides: { root: { fontSize: '0.75rem' } } },
      MuiAutocomplete: { styleOverrides: { inputRoot: { fontSize: '0.75rem' }, option: { fontSize: '0.75rem' } } },
      MuiTableCell: { styleOverrides: { root: { fontSize: '0.65rem' }, head: { fontSize: '0.7rem', fontWeight: 'bold' } } },
      MuiChip: { styleOverrides: { label: { fontSize: '0.55rem' }, labelSmall: { fontSize: '0.5rem' } } },
      MuiButton: { styleOverrides: { sizeSmall: { fontSize: '0.6rem' }, sizeMedium: { fontSize: '0.65rem' }, sizeLarge: { fontSize: '0.75rem' } } },
      MuiDialogTitle: { styleOverrides: { root: { fontSize: '0.85rem' } } },
      MuiDialogContentText: { styleOverrides: { root: { fontSize: '0.75rem' } } },
      MuiFormControlLabel: { styleOverrides: { label: { fontSize: '0.75rem' } } },
      MuiAlert: { styleOverrides: { message: { fontSize: '0.65rem' } } },
      MuiTablePagination: {
        styleOverrides: {
          caption: { fontSize: '0.65rem' },
          selectLabel: { fontSize: '0.65rem' },
          displayedRows: { fontSize: '0.65rem' }
        }
      }
    }
  });

  // 授权列表状态
  const [authorizations, setAuthorizations] = useState<Authorization[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 搜索和筛选状态
  const [searchKeyword, setSearchKeyword] = useState('');
  const [filterOpen, setFilterOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [accessTypeFilter, setAccessTypeFilter] = useState<string>('');
  const [userIdFilter, setUserIdFilter] = useState<string>('');

  // 分页状态
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // 删除确认对话框状态
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [authToDelete, setAuthToDelete] = useState<Authorization | null>(null);

  // 添加/编辑授权对话框状态
  const [authDialogOpen, setAuthDialogOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [selectedAuth, setSelectedAuth] = useState<Authorization | null>(null);

  // 添加/编辑表单状态
  const [formData, setFormData] = useState({
    grantorId: '',
    granteeId: '',
    accessType: 'BASIC',
    scope: 'PATIENT',
    targetId: '',
    status: 'ACTIVE',
  });

  // 用户列表（用于选择授权者和被授权者）
  const [users, setUsers] = useState<User[]>([]);
  const [loadingUsers, setLoadingUsers] = useState(false);

  // 移动端 "更多操作" 菜单状态
  const [mobileMenuAnchorEl, setMobileMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedAuthForMenu, setSelectedAuthForMenu] = useState<Authorization | null>(null);

  // 初始加载授权数据
  useEffect(() => {
    fetchAuthorizations();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 获取授权列表
  const fetchAuthorizations = useCallback(async (filterParams = {}) => {
    setLoading(true);
    setError(null);

    try {
      // 构建查询参数
      const params: any = {
        page,
        pageSize: rowsPerPage,
        ...filterParams
      };

      if (searchKeyword.trim()) {
        params.search = searchKeyword.trim();
      }

      if (statusFilter) {
        params.status = statusFilter;
      }

      if (accessTypeFilter) {
        params.accessType = accessTypeFilter;
      }

      if (userIdFilter) {
        params.userId = userIdFilter;
      }

      // 使用API_PATHS常量确保路径一致性
      const response = await axios.get(`${API_BASE_URL}/api/admin/authorizations`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        },
        params
      });

      if (response.data) {
        setAuthorizations(response.data.authorizations || []);
        setTotalCount(response.data.total || 0);
      } else {
        setAuthorizations([]);
        setTotalCount(0);
      }
    } catch (err: any) {
      console.error('获取授权列表失败:', err);
      setError(err.response?.data?.message || '获取授权列表失败');
      enqueueSnackbar('获取授权列表失败', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  }, [page, rowsPerPage, searchKeyword, statusFilter, accessTypeFilter, userIdFilter, enqueueSnackbar]);

  // 获取用户列表（用于选择授权者和被授权者）
  const fetchUsers = async () => {
    setLoadingUsers(true);

    try {
      // 使用API_PATHS常量确保路径一致性
      const response = await axios.get(`${API_BASE_URL}${API_PATHS.ADMIN.USERS}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        },
        params: {
          pageSize: 100 // 获取足够的用户数据用于选择
        }
      });

      if (response.data) {
        setUsers(response.data.map((user: any) => ({
          id: user.id,
          username: user.username,
          email: user.email
        })));
      }
    } catch (err: any) {
      console.error('获取用户列表失败:', err);
      enqueueSnackbar('获取用户列表失败', { variant: 'error' });
    } finally {
      setLoadingUsers(false);
    }
  };

  // 应用筛选条件
  const applyFilters = () => {
    setPage(0); // 重置到第一页
    fetchAuthorizations({
      search: searchKeyword,
      status: statusFilter,
      accessType: accessTypeFilter,
      userId: userIdFilter
    });
  };

  // 重置筛选条件
  const resetFilters = () => {
    setSearchKeyword('');
    setStatusFilter('');
    setAccessTypeFilter('');
    setUserIdFilter('');
    setPage(0);
    fetchAuthorizations({});
  };

  // 处理搜索输入变化
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchKeyword(event.target.value);
  };

  // 处理回车键搜索
  const handleSearchKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      applyFilters();
    }
  };

  // 处理分页变化
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  // 处理每页行数变化
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // 打开删除确认对话框
  const openDeleteDialog = (auth: Authorization) => {
    setAuthToDelete(auth);
    setDeleteDialogOpen(true);
  };

  // 关闭删除确认对话框
  const closeDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setAuthToDelete(null);
  };

  // 删除授权
  const deleteAuthorization = async () => {
    if (!authToDelete) return;

    try {
      await axios.delete(`${API_BASE_URL}/api/admin/authorizations/${authToDelete.id}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      enqueueSnackbar('授权关系已成功删除', { variant: 'success' });

      // 关闭对话框并刷新列表
      closeDeleteDialog();
      fetchAuthorizations();
    } catch (err: any) {
      console.error('删除授权关系失败:', err);
      enqueueSnackbar('删除授权关系失败: ' + (err.response?.data?.message || err.message), { variant: 'error' });
    }
  };

  // 打开添加/编辑授权对话框
  const openAuthDialog = (auth?: Authorization) => {
    // 如果提供了授权对象，则为编辑模式
    if (auth) {
      setIsEditing(true);
      setSelectedAuth(auth);
      setFormData({
        grantorId: auth.grantorId,
        granteeId: auth.granteeId,
        accessType: auth.accessType,
        scope: auth.scope,
        targetId: auth.targetId || '',
        status: auth.status,
      });
    } else {
      // 添加模式
      setIsEditing(false);
      setSelectedAuth(null);
      setFormData({
        grantorId: '',
        granteeId: '',
        accessType: 'BASIC',
        scope: 'PATIENT',
        targetId: '',
        status: 'ACTIVE',
      });
    }

    // 获取用户列表
    fetchUsers();

    // 打开对话框
    setAuthDialogOpen(true);
  };

  // 关闭添加/编辑授权对话框
  const closeAuthDialog = () => {
    setAuthDialogOpen(false);
    setSelectedAuth(null);
  };

  // 处理表单字段变化
  const handleFormChange = (field: string, value: any) => {
    setFormData({
      ...formData,
      [field]: value
    });
  };

  // 提交授权表单
  const submitAuthForm = async () => {
    try {
      if (isEditing && selectedAuth) {
        // 更新授权
        await axios.put(`${API_BASE_URL}/api/admin/authorizations/${selectedAuth.id}`, formData, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        });

        enqueueSnackbar('授权关系已成功更新', { variant: 'success' });
      } else {
        // 添加授权
        await axios.post(`${API_BASE_URL}/api/admin/authorizations`, formData, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        });

        enqueueSnackbar('授权关系已成功创建', { variant: 'success' });
      }

      // 关闭对话框并刷新列表
      closeAuthDialog();
      fetchAuthorizations();
    } catch (err: any) {
      console.error('保存授权关系失败:', err);
      enqueueSnackbar('保存授权关系失败: ' + (err.response?.data?.message || err.message), { variant: 'error' });
    }
  };

  // 格式化时间
  const formatDateTime = (dateString?: string) => {
    if (!dateString) return '未知';
    try {
      const date = new Date(dateString);
      return format(date, 'yyyy-MM-dd');
    } catch (error) {
      return '无效日期';
    }
  };

  // 获取访问类型标签
  const getAccessTypeChip = (type: string) => {
    switch (type) {
      case 'BASIC':
        return <Chip label="基础授权" size="small" color="info" variant="outlined" />;
      case 'STANDARD':
        return <Chip label="标准授权" size="small" color="warning" variant="outlined" />;
      case 'FULL':
        return <Chip label="完整授权" size="small" color="error" variant="outlined" />;
      default:
        return <Chip label={type} size="small" color="default" variant="outlined" />;
    }
  };

  // 获取授权状态标签
  const getStatusChip = (status: string) => {
    let color: "success" | "error" | "warning" | "default" | "info" | "primary" | "secondary" = "default";
    let label = status;

    switch (status) {
      case 'ACTIVE':
        color = "success";
        label = "有效";
        break;
      case 'PENDING':
        color = "warning";
        label = "待确认";
        break;
      case 'EXPIRED':
        color = "default";
        label = "已过期";
        break;
      case 'REVOKED':
        color = "error";
        label = "已撤销";
        break;
      default:
        color = "default";
        label = status;
    }

    return <Chip label={label} size="small" color={color} />;
  };

  // 获取授权范围文本
  const getScopeText = (scope: string, targetName?: string) => {
    switch (scope) {
      case 'USER': return '用户级别';
      case 'PATIENT': return `患者: ${targetName || '未指定'}`;
      case 'AI_REPORT': return `AI报告: ${targetName || '未指定'}`;
      case 'DISEASE': return `病理: ${targetName || '未指定'}`;
      case 'RECORD': return `就诊记录: ${targetName || '未指定'}`;
      default: return scope;
    }
  };

  // 移动端菜单处理
  const handleMobileMenuOpen = (event: React.MouseEvent<HTMLElement>, auth: Authorization) => {
    setMobileMenuAnchorEl(event.currentTarget);
    setSelectedAuthForMenu(auth);
  };

  const handleMobileMenuClose = () => {
    setMobileMenuAnchorEl(null);
    setSelectedAuthForMenu(null);
  };

  const handleEditFromMenu = () => {
    if (selectedAuthForMenu) {
      openAuthDialog(selectedAuthForMenu);
    }
    handleMobileMenuClose();
  };

  const handleDeleteFromMenu = () => {
    if (selectedAuthForMenu) {
      openDeleteDialog(selectedAuthForMenu);
    }
    handleMobileMenuClose();
  };

  return (
    <ThemeProvider theme={reducedFontSizeTheme}>
      <Box sx={{ py: { xs: 1, sm: 2 } }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: { xs: 1.5, sm: 2 } }}>
          <Typography variant="h6" component="h1">
            授权管理
          </Typography>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={() => openAuthDialog()}
            size="small"
          >
            添加授权
          </Button>
        </Box>

        {/* 搜索和筛选区域 */}
        <Paper elevation={1} sx={{ p: { xs: '10px', sm: 2 }, mb: { xs: 1.5, sm: 3 } }}>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
            <Box sx={{ flex: '1 1 300px', minWidth: 0 }}>
              <TextField
                placeholder="搜索授权关系..."
                variant="outlined"
                size="small"
                fullWidth
                value={searchKeyword}
                onChange={handleSearchChange}
                onKeyPress={handleSearchKeyPress}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Box>

            <Box sx={{ flex: '1 1 300px', display: 'flex', gap: 1, justifyContent: 'flex-end', alignItems: 'center' }}>
              <Button
                variant="outlined"
                color="primary"
                startIcon={<FilterListIcon />}
                onClick={() => setFilterOpen(!filterOpen)}
              >
                高级筛选
              </Button>

              <Button
                variant="outlined"
                color="primary"
                startIcon={<RefreshIcon />}
                onClick={() => fetchAuthorizations()}
                disabled={loading}
              >
                刷新
              </Button>
            </Box>

            {/* 高级筛选面板 */}
            {filterOpen && (
              <Box sx={{ width: '100%' }}>
                <Card variant="outlined">
                  <CardContent>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                      <Box sx={{ flex: '1 1 200px', minWidth: 0 }}>
                        <FormControl fullWidth size="small">
                          <InputLabel id="status-filter-label">状态</InputLabel>
                          <Select
                            labelId="status-filter-label"
                            value={statusFilter}
                            label="状态"
                            onChange={(e) => setStatusFilter(e.target.value)}
                          >
                            <MenuItem value="">全部</MenuItem>
                            <MenuItem value="ACTIVE">有效</MenuItem>
                            <MenuItem value="PENDING">待确认</MenuItem>
                            <MenuItem value="EXPIRED">已过期</MenuItem>
                            <MenuItem value="REVOKED">已撤销</MenuItem>
                          </Select>
                        </FormControl>
                      </Box>

                      <Box sx={{ flex: '1 1 200px', minWidth: 0 }}>
                        <FormControl fullWidth size="small">
                          <InputLabel id="access-type-filter-label">访问类型</InputLabel>
                          <Select
                            labelId="access-type-filter-label"
                            value={accessTypeFilter}
                            label="访问类型"
                            onChange={(e) => setAccessTypeFilter(e.target.value)}
                          >
                            <MenuItem value="">全部</MenuItem>
                            <MenuItem value="BASIC">基础授权 (BASIC)</MenuItem>
                            <MenuItem value="STANDARD">标准授权 (STANDARD)</MenuItem>
                            <MenuItem value="FULL">完整授权 (FULL)</MenuItem>
                          </Select>
                        </FormControl>
                      </Box>

                      <Box sx={{ flex: '1 1 200px', minWidth: 0 }}>
                        <TextField
                          label="用户ID"
                          size="small"
                          fullWidth
                          value={userIdFilter}
                          onChange={(e) => setUserIdFilter(e.target.value)}
                          placeholder="授权者或被授权者ID"
                        />
                      </Box>

                      <Box sx={{ width: '100%', display: 'flex', justifyContent: 'flex-end', mt: 1 }}>
                        <Button
                          variant="outlined"
                          color="secondary"
                          onClick={resetFilters}
                          sx={{ mr: 1 }}
                        >
                          重置
                        </Button>
                        <Button
                          variant="contained"
                          color="primary"
                          onClick={applyFilters}
                        >
                          应用筛选
                        </Button>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Box>
            )}
          </Box>
        </Paper>

        {/* 移动端卡片视图 */}
        {isMobile && (
          <Box>
            {loading && <CircularProgress sx={{ display: 'block', margin: 'auto', my: 4 }} />}
            {error && <Typography color="error" sx={{ textAlign: 'center', my: 4 }}>{error}</Typography>}
            {!loading && !error && authorizations.length === 0 && (
              <Typography sx={{ textAlign: 'center', my: 4 }}>未找到符合条件的授权记录。</Typography>
            )}
            {!loading && !error && authorizations.map((auth) => (
              <Card key={auth.id} sx={{ mb: 2, boxShadow: 3 }}>
                <CardContent sx={{ px: '10px', '&:last-child': { pb: '10px' } }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                    <Box sx={{ flexGrow: 1, mr: 1 }}>
                       <Typography variant="subtitle1" component="div" sx={{ fontWeight: 'bold', wordBreak: 'break-word' }}>
                         <GrantorIcon fontSize="inherit" sx={{ verticalAlign: 'middle', mr: 0.5 }} /> {auth.grantorName}
                         <Typography component="span" variant="body2" color="textSecondary" sx={{ mx: 0.5 }}>授权给</Typography>
                         <GranteeIcon fontSize="inherit" sx={{ verticalAlign: 'middle', mr: 0.5 }} /> {auth.granteeName}
                       </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: -0.5 }}>
                      {getStatusChip(auth.status)}
                      <IconButton size="small" onClick={(event) => handleMobileMenuOpen(event, auth)} sx={{ p: 0.5, ml: 0.5 }}>
                        <MoreVertIcon />
                      </IconButton>
                    </Box>
                  </Box>

                  {/* 授权类型和范围 */}
                  <Typography variant="body2" color="text.secondary" gutterBottom component="div">
                    <KeyIcon fontSize="inherit" sx={{ mr: 0.5, verticalAlign: 'middle' }} />
                    {getAccessTypeChip(auth.accessType)}
                    <Typography component="span" sx={{ mx: 0.5 }}>•</Typography>
                    <ScopeIcon fontSize="inherit" sx={{ mr: 0.5, verticalAlign: 'middle' }} />
                    {getScopeText(auth.scope, auth.targetName || auth.targetId)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    <ExpiresIcon fontSize="inherit" sx={{ verticalAlign: 'bottom', mr: 0.5 }} />
                    有效期至: {auth.expiresAt ? formatDateTime(auth.expiresAt) : '永久'}
                  </Typography>
                  <Typography variant="caption" display="block" sx={{ mt: 1, color: 'text.disabled' }}>
                    <CreatedIcon fontSize="inherit" sx={{ verticalAlign: 'bottom', mr: 0.5 }} />
                    创建于: {formatDateTime(auth.createdAt)}
                  </Typography>
                </CardContent>
              </Card>
            ))}
             <Menu
              anchorEl={mobileMenuAnchorEl}
              open={Boolean(mobileMenuAnchorEl)}
              onClose={handleMobileMenuClose}
            >
              <MenuItem onClick={handleEditFromMenu}>
                <ListItemIcon>
                  <EditIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText>编辑</ListItemText>
              </MenuItem>
              <MenuItem onClick={handleDeleteFromMenu} sx={{ color: 'error.main' }}>
                <ListItemIcon>
                  <DeleteIcon fontSize="small" color="error" />
                </ListItemIcon>
                <ListItemText>撤销授权</ListItemText>
              </MenuItem>
            </Menu>
            {totalCount > 0 && (
              <TablePagination
                component="div"
                count={totalCount}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                rowsPerPageOptions={[5, 10, 25]}
                labelRowsPerPage="每页:"
                sx={{ mt: 2 }}
              />
            )}
          </Box>
        )}

        {/* 桌面端表格视图 */}
        {!isMobile && (
          <Paper sx={{ width: '100%', overflow: 'hidden' }}>
            <TableContainer sx={{ maxHeight: 'calc(100vh - 360px)' }}>
              <Table stickyHeader size="medium">
                <TableHead>
                  <TableRow>
                    <TableCell>授权者</TableCell>
                    <TableCell>被授权者</TableCell>
                    <TableCell>权限类型</TableCell>
                    <TableCell>范围</TableCell>
                    <TableCell>状态</TableCell>
                    <TableCell>有效期</TableCell>
                    <TableCell align="center">操作</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={7} align="center">
                        <CircularProgress size={24} sx={{ my: 2 }}/>
                        <Typography variant="body2" color="textSecondary" sx={{ml: 1, display: 'inline'}}>加载中...</Typography>
                      </TableCell>
                    </TableRow>
                  ) : error ? (
                    <TableRow>
                      <TableCell colSpan={7} align="center">
                        <Typography color="error">{error}</Typography>
                      </TableCell>
                    </TableRow>
                  ) : authorizations.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} align="center">
                        <Typography>没有符合条件的授权记录</Typography>
                      </TableCell>
                    </TableRow>
                  ) : (
                    authorizations.map((auth) => (
                      <TableRow key={auth.id} hover>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <GrantorIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                            <Tooltip title={`ID: ${auth.grantorId}`}>
                              <Typography variant="body2">{auth.grantorName}</Typography>
                            </Tooltip>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <GranteeIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                            <Tooltip title={`ID: ${auth.granteeId}`}>
                              <Typography variant="body2">{auth.granteeName}</Typography>
                            </Tooltip>
                          </Box>
                        </TableCell>
                        <TableCell>{getAccessTypeChip(auth.accessType)}</TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <ScopeIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                            <Tooltip title={auth.targetId ? `Target ID: ${auth.targetId}`: ''}>
                               <Typography variant="body2">{getScopeText(auth.scope, auth.targetName || auth.targetId)}</Typography>
                            </Tooltip>
                          </Box>
                        </TableCell>
                        <TableCell>{getStatusChip(auth.status)}</TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <ExpiresIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                            <Typography variant="body2">{auth.expiresAt ? formatDateTime(auth.expiresAt) : '永久'}</Typography>
                          </Box>
                        </TableCell>
                        <TableCell align="center">
                          <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                            <Tooltip title="编辑授权">
                              <IconButton size="small" onClick={() => openAuthDialog(auth)} sx={{ mr: 0.5 }}>
                                <EditIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="撤销授权">
                              <IconButton size="small" color="error" onClick={() => openDeleteDialog(auth)}>
                                <DeleteIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>

            {/* 分页控件 */}
            <TablePagination
              rowsPerPageOptions={[5, 10, 25, 50]}
              component="div"
              count={totalCount}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
              labelRowsPerPage="每页行数:"
              labelDisplayedRows={({ from, to, count }) => `${from}-${to} / 共${count}条`}
            />
          </Paper>
        )}

        {/* 添加/编辑授权对话框 */}
        <Dialog
          open={authDialogOpen}
          onClose={closeAuthDialog}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <SecurityIcon sx={{ mr: 1 }} />
              {isEditing ? '编辑授权' : '添加新授权'}
            </Box>
          </DialogTitle>
          <DialogContent dividers>
            <Box component="form" noValidate autoComplete="off" sx={{ mt: 1 }}>
              <Autocomplete
                options={users}
                getOptionLabel={(option) => `${option.username} (ID: ${option.id})`}
                value={users.find(u => u.id === formData.grantorId) || null}
                onChange={(event, newValue) => {
                  handleFormChange('grantorId', newValue ? newValue.id : '');
                }}
                onInputChange={(event, newInputValue) => {
                  if(newInputValue.length > 1 && !loadingUsers) fetchUsers();
                }}
                loading={loadingUsers}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="授权者 (Grantor)"
                    margin="dense"
                    fullWidth
                    InputProps={{
                      ...params.InputProps,
                      endAdornment: (
                        <React.Fragment>
                          {loadingUsers ? <CircularProgress color="inherit" size={20} /> : null}
                          {params.InputProps.endAdornment}
                        </React.Fragment>
                      ),
                    }}
                  />
                )}
                disabled={isEditing}
              />

              <Autocomplete
                options={users}
                getOptionLabel={(option) => `${option.username} (ID: ${option.id})`}
                value={users.find(u => u.id === formData.granteeId) || null}
                onChange={(event, newValue) => {
                  handleFormChange('granteeId', newValue ? newValue.id : '');
                }}
                onInputChange={(event, newInputValue) => {
                  if(newInputValue.length > 1 && !loadingUsers) fetchUsers();
                }}
                loading={loadingUsers}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="被授权者 (Grantee)"
                    margin="dense"
                    fullWidth
                    InputProps={{
                      ...params.InputProps,
                      endAdornment: (
                        <React.Fragment>
                          {loadingUsers ? <CircularProgress color="inherit" size={20} /> : null}
                          {params.InputProps.endAdornment}
                        </React.Fragment>
                      ),
                    }}
                  />
                )}
                disabled={isEditing}
              />

              <FormControl fullWidth margin="dense" size="small">
                <InputLabel id="access-type-label">权限类型</InputLabel>
                <Select
                  labelId="access-type-label"
                  value={formData.accessType}
                  label="权限类型"
                  onChange={(e) => handleFormChange('accessType', e.target.value)}
                >
                  <MenuItem value="BASIC">基础授权 (BASIC)</MenuItem>
                  <MenuItem value="STANDARD">标准授权 (STANDARD)</MenuItem>
                  <MenuItem value="FULL">完整授权 (FULL)</MenuItem>
                </Select>
              </FormControl>

              {/* <FormControl fullWidth margin="dense" size="small">
                <InputLabel id="scope-label">授权范围</InputLabel>
                <Select
                  labelId="scope-label"
                  value={formData.scope}
                  label="授权范围"
                  onChange={(e) => handleFormChange('scope', e.target.value)}
                >
                  <MenuItem value="USER">用户级别</MenuItem>
                  <MenuItem value="PATIENT">患者特定数据</MenuItem>
                  <MenuItem value="AI_REPORT">AI报告特定数据</MenuItem>
                  <MenuItem value="DISEASE">病理特定数据</MenuItem>
                  <MenuItem value="RECORD">就诊记录特定数据</MenuItem>
                </Select>
              </FormControl> */}

              {formData.scope !== 'ALL' && (
                <Box sx={{ width: '100%', p: 1 }}>
                  <TextField
                    label={
                      formData.scope === 'PATIENT' ? '患者ID' :
                      formData.scope === 'DISEASE' ? '病理ID' :
                      formData.scope === 'RECORD' ? '记录ID' : '目标ID'
                    }
                    fullWidth
                    value={formData.targetId}
                    onChange={(e) => handleFormChange('targetId', e.target.value)}
                    helperText={
                      formData.scope === 'PATIENT' ? '指定要授权访问的患者ID' :
                      formData.scope === 'DISEASE' ? '指定要授权访问的病理ID' :
                      formData.scope === 'RECORD' ? '指定要授权访问的记录ID' : '指定授权目标的ID'
                    }
                  />
                </Box>
              )}

              <FormControl fullWidth margin="dense" size="small">
                <InputLabel id="status-label">状态</InputLabel>
                <Select
                  labelId="status-label"
                  value={formData.status}
                  label="状态"
                  onChange={(e) => handleFormChange('status', e.target.value)}
                >
                  <MenuItem value="ACTIVE">有效</MenuItem>
                  <MenuItem value="PENDING">待确认</MenuItem>
                  <MenuItem value="REVOKED">已撤销</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={closeAuthDialog} startIcon={<CancelIcon />}>
              取消
            </Button>
            <Button
              onClick={submitAuthForm}
              color="primary"
              variant="contained"
              disabled={loadingUsers || !formData.grantorId || !formData.granteeId}
              startIcon={<ShareIcon />}
            >
              {isEditing ? '更新授权' : '创建授权'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* 删除确认对话框 */}
        <Dialog
          open={deleteDialogOpen}
          onClose={closeDeleteDialog}
        >
          <DialogTitle>
            确认删除授权关系
          </DialogTitle>
          <DialogContent>
            <DialogContentText component="div">
              确定要删除此授权关系吗？删除后相关用户将无法再访问授权的数据。
              {authToDelete && (
                <Box component="div" sx={{ mt: 1 }}>
                  <Typography variant="subtitle2">授权信息：</Typography>
                  <Typography variant="body2">授权者: {authToDelete.grantorName}</Typography>
                  <Typography variant="body2">被授权者: {authToDelete.granteeName}</Typography>
                  <Typography variant="body2">访问类型: {
                    authToDelete.accessType === 'BASIC' ? '基础授权' :
                    authToDelete.accessType === 'STANDARD' ? '标准授权' :
                    authToDelete.accessType === 'FULL' ? '完整授权' : authToDelete.accessType
                  }</Typography>
                  <Typography variant="body2">授权范围: {getScopeText(authToDelete.scope, authToDelete.targetName)}</Typography>
                </Box>
              )}
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={closeDeleteDialog}>
              取消
            </Button>
            <Button
              onClick={deleteAuthorization}
              color="error"
              variant="contained"
              startIcon={<DeleteIcon />}
            >
              确认删除
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </ThemeProvider>
  );
};

export default AuthorizationsManagementPage;