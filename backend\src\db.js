const knex = require('knex')(require('../knexfile').development);
const { Model } = require('objection');

// 设置 Objection.js
Model.knex(knex);

/**
 * 设置数据库表结构和初始数据
 */
async function setUpDb() {
  console.log('正在初始化数据库...');
  
  try {
    // 检查是否需要创建users表
    const hasUsersTable = await knex.schema.hasTable('users');
    if (!hasUsersTable) {
      console.log('创建users表...');
      await knex.schema.createTable('users', table => {
        table.string('id').primary();
        table.string('username').unique().notNullable();
        table.string('email').unique().nullable();
        table.string('phone_number').unique().nullable();
        table.string('password_hash').notNullable();
        table.string('avatar').nullable();
        table.string('role').defaultTo('USER');
        table.string('level').defaultTo('PERSONAL');
        table.integer('active_disease_limit').defaultTo(5);
        table.integer('ai_usage_count').defaultTo(0);
        table.dateTime('ai_usage_reset_at').nullable();
        table.integer('family_member_limit').defaultTo(3);
        table.boolean('is_active').defaultTo(true);
        table.dateTime('created_at').notNullable();
        table.dateTime('updated_at').notNullable();
        table.dateTime('last_login_at').nullable();
        table.dateTime('deleted_at').nullable();
      });
    }
    
    // 检查是否需要创建患者表
    const hasPatientsTable = await knex.schema.hasTable('patients');
    if (!hasPatientsTable) {
      console.log('创建patients表...');
      await knex.schema.createTable('patients', table => {
        table.string('id').primary();
        table.string('user_id').notNullable().references('id').inTable('users');
        table.string('name').notNullable();
        table.string('gender').notNullable();
        table.string('birth_date').nullable();
        table.string('phone_number').nullable();
        table.string('email').nullable();
        table.string('address').nullable();
        table.string('id_card').nullable().unique();
        table.string('medicare_card').nullable().unique();
        table.string('medicare_location').nullable();
        table.string('blood_type').nullable();
        table.string('emergency_contact_name').nullable();
        table.string('emergency_contact_phone').nullable();
        table.string('emergency_contact_relationship').nullable();
        table.text('past_medical_history').nullable();
        table.text('family_medical_history').nullable();
        table.text('allergy_history').nullable();
        table.string('last_visit_date').nullable();
        table.integer('is_primary').defaultTo(0); // 0: 不是本人, 1: 是本人
        table.dateTime('created_at').notNullable();
        table.dateTime('updated_at').notNullable();
        table.dateTime('deleted_at').nullable();
        
        // 添加索引
        table.index('user_id');
        table.index('name');
      });
    }
    
    // 检查是否需要创建疾病表
    const hasDiseasesTable = await knex.schema.hasTable('diseases');
    if (!hasDiseasesTable) {
      console.log('创建diseases表...');
      await knex.schema.createTable('diseases', table => {
        table.string('id').primary();
        table.string('user_id').notNullable().references('id').inTable('users');
        table.string('patient_id').notNullable().references('id').inTable('patients');
        table.string('name').notNullable();
        table.string('diagnosis_date').nullable();
        table.string('stage').nullable();
        table.text('description').nullable();
        table.text('treatment').nullable();
        table.string('status').defaultTo('ACTIVE');
        table.dateTime('created_at').notNullable();
        table.dateTime('updated_at').notNullable();
        table.dateTime('deleted_at').nullable();
        
        // 添加索引
        table.index('user_id');
        table.index('patient_id');
        table.index(['user_id', 'patient_id']);
      });
    }
    
    // 检查是否需要创建用户等级限制表
    const hasUserLevelLimitsTable = await knex.schema.hasTable('user_level_limits');
    if (!hasUserLevelLimitsTable) {
      console.log('创建user_level_limits表...');
      await knex.schema.createTable('user_level_limits', table => {
        table.increments('id').primary();
        table.string('level_type').notNullable().unique();
        table.string('level_name').notNullable();
        table.text('description').nullable();
        table.integer('max_patients').notNullable();
        table.integer('max_pathologies').notNullable();
        table.integer('max_attachment_size').notNullable(); // 单位: MB
        table.integer('max_total_storage').notNullable(); // 单位: MB
        table.integer('ai_usage_limit').notNullable(); // AI使用次数每月限制
        table.dateTime('created_at').notNullable();
        table.dateTime('updated_at').notNullable();
      });
      
      // 插入默认用户等级限制
      await knex('user_level_limits').insert([
        {
          level_type: 'FREE',
          level_name: '免费版',
          description: '基础功能，有限制',
          max_patients: 5,
          max_pathologies: 10,
          max_attachment_size: 5,
          max_total_storage: 50,
          ai_usage_limit: 10,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          level_type: 'BASIC',
          level_name: '基础版',
          description: '更多功能，较少限制',
          max_patients: 15,
          max_pathologies: 30,
          max_attachment_size: 10,
          max_total_storage: 200,
          ai_usage_limit: 30,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          level_type: 'PREMIUM',
          level_name: '高级版',
          description: '全部功能，几乎无限制',
          max_patients: 50,
          max_pathologies: 100,
          max_attachment_size: 50,
          max_total_storage: 1000,
          ai_usage_limit: 100,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          level_type: 'UNLIMITED',
          level_name: '无限版',
          description: '无任何限制',
          max_patients: -1, // -1表示无限制
          max_pathologies: -1,
          max_attachment_size: -1,
          max_total_storage: -1,
          ai_usage_limit: -1,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ]);
    }
    
    console.log('数据库初始化完成');
  } catch (error) {
    console.error('数据库初始化失败:', error);
    throw error;
  }
}

module.exports = {
  knex,
  setUpDb
}; 