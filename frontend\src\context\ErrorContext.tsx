import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import <PERSON>rrorHandler from '../components/ErrorHandler';

interface ErrorContextType {
  handleError: (error: any) => void;
  clearError: () => void;
}

const ErrorContext = createContext<ErrorContextType | undefined>(undefined);

interface ErrorProviderProps {
  children: ReactNode;
}

/**
 * 错误处理上下文提供者组件
 */
export const ErrorProvider: React.FC<ErrorProviderProps> = ({ children }) => {
  const [error, setError] = useState<any>(null);
  const [open, setOpen] = useState(false);

  // 处理错误的方法，将在整个应用中使用
  const handleError = (error: any) => {
    console.error('全局错误捕获:', error);
    setError(error);
    setOpen(true);
  };

  // 清除错误
  const clearError = () => {
    setError(null);
    setOpen(false);
  };

  // 关闭错误对话框
  const handleClose = () => {
    setOpen(false);
  };

  // 在window对象上注册全局错误处理函数
  // 这允许在非Hook上下文中也能使用错误处理
  useEffect(() => {
    // 将错误处理函数添加到window对象
    (window as any).__handleGlobalError = handleError;

    // 清理函数
    return () => {
      // 移除全局错误处理函数
      delete (window as any).__handleGlobalError;
    };
  }, []);

  return (
    <ErrorContext.Provider value={{ handleError, clearError }}>
      {children}
      {error && <ErrorHandler error={error} open={open} onClose={handleClose} />}
    </ErrorContext.Provider>
  );
};

/**
 * 使用错误上下文的自定义钩子
 */
export const useErrorContext = (): ErrorContextType => {
  const context = useContext(ErrorContext);
  if (context === undefined) {
    throw new Error('useErrorContext必须在ErrorProvider内部使用');
  }
  return context;
}; 