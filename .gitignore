# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Diagnostic reports (https://nodejs.org/api/report.html)
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# Snowpack dependency directory (https://snowpack.dev/)
web_modules/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
# Comment in the public line in if your project uses Gatsby and not Next.js
# https://nextjs.org/blog/next-9-1#public-directory-support
# public

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*
# ... (这里是Gitee Node.js模板已有的内容，不用动) ...

# Project Specific Ignores
# -----------------------------------------------------------------------------
*.zip
# Frontend build artifacts
# (如果你的React前端项目构建后输出到 frontend/build/ 目录)
frontend/build/
# (如果你的React前端项目构建后输出到 frontend/dist/ 目录)
# frontend/dist/

# Backend build artifacts
# (如果你的Node.js后端使用TypeScript等需要编译，假设编译后输出到 backend/dist/)
# backend/dist/

# Environment variable files (VERY IMPORTANT!)
# Root level .env files
.env
.env.local
.env.development
.env.production
.env.test
.env.*.local # 涵盖所有本地环境文件，例如 .env.development.local

# Frontend specific .env files
frontend/.env
frontend/.env.local
frontend/.env.development
frontend/.env.production
frontend/.env.test
frontend/.env.*.local

# Backend specific .env files
backend/.env
backend/.env.local
backend/.env.development
backend/.env.production
backend/.env.test
backend/.env.*.local

# IDE specific settings (VSCode example, Node template might have some)
.vscode/

# Other log files or temporary files specific to your project
# my_app_specific.log
# 忽略上传文件目录
backend/uploads/*
# 但保留目录本身和.gitkeep文件
!backend/uploads/.gitkeep

# Cursor IDE
.cursor/
.cursor-cache/
.cursor-settings/
.cursor-workspace/

# Root directory text and markdown files
/*.txt
/*.md
!README.md
