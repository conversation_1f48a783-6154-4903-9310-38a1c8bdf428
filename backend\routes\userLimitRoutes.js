const express = require('express');
const knex = require('knex')(require('../knexfile').development);
const { auth } = require('../src/middleware/auth');
const { AIReportQuota, AIReport } = require('../models/aiReport');
const { errorHandler } = require('../utils/errorHandler');

const router = express.Router();

/**
 * 获取用户限制
 * @route GET /user/limits
 * @desc 获取当前用户的各项使用限制
 * @access 私有（需要认证）
 */
router.get('/limits', auth, async (req, res) => {
  try {
    // 使用auth中间件设置的userId
    const userId = req.user.id;

    console.log('获取用户限制, 用户ID:', userId);

    // 获取用户数据
    const user = await knex('users')
      .where('id', userId)
      .select('family_member_limit', 'active_disease_limit', 'ai_usage_count', 'level')
      .first();

    if (!user) {
      return res.status(404).json({ error: '用户不存在' });
    }

    // 获取该等级的用户限制
    const levelLimits = await knex('user_level_limits')
      .where('level_type', user.level)
      .first();

    // 简单直接查询：当前用户的患者数量
    const patientQuery = await knex('patients')
      .where('user_id', userId)
      .count('id as count')
      .first();

    console.log('患者数量查询结果:', patientQuery);
    const patientCount = patientQuery ? parseInt(patientQuery.count) : 0;

    // 简单直接查询：当前用户的活跃病理数量
    const diseaseQuery = await knex('diseases')
      .where('user_id', userId)
      .where('is_active', 1)
      .where('is_deleted', 0)
      .count('id as count')
      .first();

    console.log('病理数量查询结果:', diseaseQuery);
    const diseaseCount = diseaseQuery ? parseInt(diseaseQuery.count) : 0;

    // 修正后的记录数量查询逻辑
    // 1. 获取用户所有有效的（未删除的）病理ID
    const validDiseaseIds = await knex('diseases')
      .where('user_id', userId)
      .where('is_deleted', 0) // 确保病理是有效的
      .pluck('id'); // 只获取ID列表

    let recordCount = 0;
    if (validDiseaseIds.length > 0) {
      // 2. 基于这些有效病理ID，统计相关的未删除记录
      const recordQuery = await knex('records')
        .whereIn('disease_id', validDiseaseIds) // 只统计属于有效病理的记录
        .whereNull('deleted_at') // 确保记录本身未被删除
      .count('id as count')
      .first();
      recordCount = recordQuery ? parseInt(recordQuery.count) : 0;
    }

    console.log('修正后的记录数量查询结果 (基于有效病理): ', recordCount);

    // 获取用户配额信息
    const quota = await AIReportQuota.query()
      .where('user_id', userId)
      .first();

    // 返回简单明确的结果
    res.json({
      familyMemberLimit: user.family_member_limit,
      activeDiseaseLimit: user.active_disease_limit,
      aiUsageCount: user.ai_usage_count,
      aiMonthlyQuota: quota ? quota.monthly_quota : 3,
      currentPatientCount: patientCount,
      currentDiseaseCount: diseaseCount,
      recordCount: recordCount,
      levelLimits: levelLimits ? {
        levelType: levelLimits.level_type,
        maxPatients: levelLimits.max_patients,
        maxPathologies: levelLimits.max_pathologies,
        maxAttachmentSize: levelLimits.max_attachment_size,
        maxTotalStorage: levelLimits.max_total_storage,
        maxAiUsed: levelLimits.max_ai_used
      } : {},
      quota: {
        usedThisMonth: quota ? quota.used_this_month : 0,
        monthlyQuota: quota ? quota.monthly_quota : 3
      }
    });
  } catch (error) {
    console.error('获取用户限制失败:', error);
    res.status(500).json({ error: '获取用户限制失败' });
  }
});

/**
 * 更新用户信息
 * @route PUT /user/profile
 * @desc 更新当前用户的个人信息
 * @access 私有（需要认证）
 */
router.put('/profile', auth, async (req, res) => {
  try {
    // 使用auth中间件设置的userId
    const userId = req.user.id;

    const { email, phone_number, avatar } = req.body;
    const updated_at = new Date().toISOString();

    await knex('users')
      .where({ id: userId })
      .update({ email, phone_number, avatar, updated_at });

    res.json({ message: '用户信息更新成功' });
  } catch (error) {
    console.error('更新用户信息错误:', error);
    res.status(500).json({ error: '用户信息更新失败' });
  }
});

// 增加AI使用次数 - 同时支持两种路径格式
router.post(['/user/ai-usage/increment', '/ai-usage/increment'], auth, async (req, res) => {
  console.log('收到增加AI使用次数请求，路径:', req.path);
  console.log('请求体:', req.body);
  console.log('用户ID:', req.user.id);

  try {
    const userId = req.user.id;
    // 不再从请求体中获取reportId，避免参数缺失导致错误
    // const { reportId } = req.body; // 可选：关联的报告ID
    const reportId = req.body && req.body.reportId ? req.body.reportId : null;

    // 获取用户信息
    const user = await knex('users')
      .where('id', userId)
      .select('level')
      .first();

    if (!user) {
      return res.status(404).json({ error: '用户不存在' });
    }

    // 获取或创建用户配额记录
    let quota = await AIReportQuota.query()
      .where('user_id', userId)
      .first();

    if (!quota) {
      // 根据用户等级设置默认配额
      const defaultQuota = {
        PERSONAL: 3,
        FAMILY: 10,
        PROFESSIONAL: 30
      };

      const monthlyQuota = defaultQuota[user.level] || 3;

      quota = await AIReportQuota.query().insert({
        user_id: userId,
        monthly_quota: monthlyQuota,
        used_this_month: 0,
        total_used: 0,
        last_reset_date: new Date().toISOString(),
        additional_quota: 0
      });
    }

    // 检查是否已经核销过
    if (reportId) {
      const existingReport = await AIReport.query()
        .where('id', reportId)
        .where('user_id', userId)
        .first();

      if (existingReport && existingReport.quota_used) {
        return res.json({
          message: '该报告已核销过配额',
          quota: {
            usedThisMonth: quota.used_this_month,
            monthlyQuota: quota.monthly_quota
          }
        });
      }
    }

    // 增加使用次数
    await AIReportQuota.query()
      .where('user_id', userId)
      .patch({
        used_this_month: quota.used_this_month + 1,
        total_used: quota.total_used + 1
      });

    // 如果有关联的报告，标记为已核销
    if (reportId) {
      await AIReport.query()
        .where('id', reportId)
        .patch({
          quota_used: true
        });
    }

    // 获取更新后的配额信息
    const updatedQuota = await AIReportQuota.query()
      .where('user_id', userId)
      .first();

    res.json({
      message: 'AI使用次数已增加',
      quota: {
        usedThisMonth: updatedQuota.used_this_month,
        monthlyQuota: updatedQuota.monthly_quota
      }
    });
  } catch (error) {
    errorHandler(error, res, '增加AI使用次数失败');
  }
});

// 重置AI使用次数 - 同时支持两种路径格式
router.post(['/user/ai-usage/reset', '/ai-usage/reset'], auth, async (req, res) => {
  console.log('收到重置AI使用次数请求，路径:', req.path);
  try {
    const userId = req.user.id;

    // 获取用户配额记录
    const quota = await AIReportQuota.query()
      .where('user_id', userId)
      .first();

    if (!quota) {
      return res.status(404).json({ error: '未找到用户配额记录' });
    }

    // 重置使用次数
    await AIReportQuota.query()
      .where('user_id', userId)
      .patch({
        used_this_month: 0,
        last_reset_date: new Date()
      });

    res.json({ message: 'AI使用次数已重置' });
  } catch (error) {
    errorHandler(error, res, '重置AI使用次数失败');
  }
});

module.exports = router;