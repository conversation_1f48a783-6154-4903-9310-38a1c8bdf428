# AI辅助分析模块 - LLM配置指南

本文档介绍如何配置AI辅助分析模块的LLM服务。

## 环境变量配置

请在项目根目录创建或编辑`.env`文件，添加以下配置：

```
# LLM API配置
# 选择模型提供商: deepseek_official 或 volcengine
LLM_PROVIDER=deepseek_official

# DeepSeek API密钥
DEEPSEEK_API_KEY=your_deepseek_api_key

# 火山引擎API密钥
VOLCENGINE_API_KEY=your_volcengine_api_key
# 火山引擎模型选择: deepseek-r1, deepseek-v3, doubao-llama3
VOLCENGINE_MODEL=deepseek-r1

# LLM参数
LLM_MAX_TOKENS=4096
LLM_TEMPERATURE=0.3
```

## 支持的模型

系统目前支持以下LLM模型：

1. **DeepSeek官方API**
   - DeepSeek R1

2. **火山引擎API**
   - DeepSeek R1
   - DeepSeek V3
   - <PERSON><PERSON><PERSON> (LLaMA 3)

## API密钥获取

### DeepSeek官方API
1. 访问DeepSeek官网: https://www.deepseek.com/
2. 注册并登录账号
3. 导航至API设置页面
4. 创建新的API密钥
5. 复制API密钥并保存到`.env`文件

### 火山引擎API
1. 访问火山引擎官网: https://www.volcengine.com/
2. 注册并登录账号
3. 导航至API密钥管理页面
4. 创建新的API密钥
5. 复制API密钥并保存到`.env`文件

## 参数说明

- **LLM_PROVIDER**: 指定使用哪个LLM提供商
- **DEEPSEEK_API_KEY**: DeepSeek官方API密钥
- **VOLCENGINE_API_KEY**: 火山引擎API密钥
- **VOLCENGINE_MODEL**: 使用火山引擎时指定的模型
- **LLM_MAX_TOKENS**: 生成响应的最大token数
- **LLM_TEMPERATURE**: 生成的随机性/创造性（0-1之间，值越低越确定性）

## 模型选择建议

- **DeepSeek R1**：综合性能最佳，适用于大多数医疗分析场景，支持生成详细的分析报告
- **DeepSeek V3**：第三代模型，在某些专业领域表现更好，专业术语理解更准确
- **Doubao (LLaMA 3)**：在中文处理方面有特殊优势，尤其适合需要详细解释的场景

## 参数建议

为了获得高质量的医疗分析报告，我们建议以下参数设置：

- **LLM_MAX_TOKENS**: 设置为8192或更高，确保模型有足够空间生成详细分析
- **LLM_TEMPERATURE**: 0.2-0.3之间，保持回答的一致性和可靠性
- 建议在提示中明确指出需要详细解释，特别是在概述部分（800-1200字为佳）

## 隐私与安全

在使用LLM进行医疗分析时，请注意：

1. 所有患者信息必须事先进行匿名化处理
2. 系统已集成自动匿名化服务，但仍需人工确认没有隐私泄露
3. 不要向LLM提供实际患者姓名、身份证号等敏感信息
4. 对生成的内容进行审核，确保无医疗误导

## 故障排除

如果遇到API调用问题：

1. 检查API密钥是否正确
2. 验证网络连接是否正常
3. 查看API调用额度是否已用完
4. 检查请求格式是否符合要求
5. 查看服务器日志获取更多错误信息

## 联系支持

如有问题，请联系系统管理员或技术支持团队。 