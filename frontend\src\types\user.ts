// 用户角色类型
export type UserRole = 'USER' | 'SERVICE' | 'ADMIN';

// 用户等级类型
export type UserLevel = 'PERSONAL' | 'FAMILY' | 'PROFESSIONAL' | 'ENTERPRISE';

// 用户基本信息接口
export interface UserBasicInfo {
  username: string;
  email: string;
  phoneNumber?: string;
  avatar?: string;
}

// 用户详细信息接口，继承用户基本信息
export interface UserProfile extends UserBasicInfo {
  id?: string;
  role: UserRole;
  level: UserLevel;
  lastLoginAt?: string;
  createdAt?: string;
  updatedAt?: string;
  activeDiseaseLimit: number;
  aiUsageCount: number;
  aiUsageResetAt?: string;
  familyMemberLimit: number;
  maxPatients?: number;
  maxPathologies?: number;
  maxAttachmentSize?: number;
  maxTotalStorage?: number;
  maxAiUsed?: number;
}

// 注册请求数据接口
export interface RegisterRequest extends UserBasicInfo {
  password: string;
  role?: UserRole;
}

// 登录请求数据接口
export interface LoginRequest {
  username: string;
  password: string;
}

// 登录响应数据接口
export interface LoginResponse {
  token: string;
}

// 更新用户资料请求数据接口
export interface UpdateProfileRequest {
  email?: string;
  phoneNumber?: string;
  avatar?: string;
} 