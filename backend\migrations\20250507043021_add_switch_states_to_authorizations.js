/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.hasTable('user_authorizations').then(exists => {
    if (exists) {
  return knex.schema.table('user_authorizations', table => {
    // 授权人开关状态(普通用户是否打开授权)
    table.boolean('authorizer_switch').notNullable().defaultTo(true);
    
    // 被授权人开关状态(服务用户是否打开授权)
    table.boolean('authorized_switch').notNullable().defaultTo(false);
    
    // 最后一次状态变更时间
    table.timestamp('status_changed_at', { useTz: true }).nullable();
      });
    }
    return Promise.resolve();
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.hasTable('user_authorizations').then(exists => {
    if (exists) {
  return knex.schema.table('user_authorizations', table => {
    table.dropColumn('authorizer_switch');
    table.dropColumn('authorized_switch');
    table.dropColumn('status_changed_at');
      });
    }
    return Promise.resolve();
  });
};
