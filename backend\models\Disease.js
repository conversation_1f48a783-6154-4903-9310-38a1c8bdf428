const { Model, snakeCaseMappers } = require('objection');
const { v4: uuidv4 } = require('uuid');

class Disease extends Model {
  static get tableName() {
    return 'diseases';
  }

  static get idColumn() {
    return 'id';
  }

  static get columnNameMappers() {
    return snakeCaseMappers();
  }

  $beforeInsert() {
    this.id = this.id || uuidv4();
    this.createdAt = new Date().toISOString();
    this.updatedAt = this.createdAt;
    this.isDeleted = false; // 确保新创建的记录默认未删除
    this.isPrivate = this.isPrivate || 0; // 默认不是私密疾病
    this.isActive = this.isActive !== undefined ? this.isActive : true; // 默认激活
    this.isChronic = this.isChronic || false; // 默认不是慢性病
    this.status = this.status || 'ACTIVE'; // 默认状态为活跃
  }

  $beforeUpdate() {
    this.updatedAt = new Date().toISOString();
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['patient_id', 'name', 'diagnosis_date'],
      properties: {
        id: { type: 'string' },
        userId: { type: 'string' },
        patientId: { type: 'string' },
        name: { type: 'string', minLength: 1, maxLength: 100 },
        diagnosisDate: { type: ['string', 'null'] },
        stage: { 
          type: ['string', 'null'],
          enum: ['INITIAL', 'DIAGNOSIS', 'TREATMENT', 'RECOVERY', 'PROGNOSIS'] 
        },
        description: { type: ['string', 'null'] },
        treatment: { type: ['string', 'null'] },
        status: { 
          type: 'string', 
          enum: ['ACTIVE', 'INACTIVE', 'CURED'] 
        },
        isPrivate: { type: 'integer', minimum: 0, maximum: 1 },
        isActive: { type: 'boolean' },
        isChronic: { type: 'boolean' },
        isDeleted: { type: 'boolean' },
        createdAt: { type: 'string' },
        updatedAt: { type: 'string' },
        deletedAt: { type: ['string', 'null'] }
      }
    };
  }

  static get relationMappings() {
    const Patient = require('./Patient');
    const User = require('./User');
    
    return {
      patient: {
        relation: Model.BelongsToOneRelation,
        modelClass: Patient,
        join: {
          from: 'diseases.patient_id',
          to: 'patients.id'
        }
      },
      user: {
        relation: Model.BelongsToOneRelation,
        modelClass: User,
        join: {
          from: 'diseases.user_id',
          to: 'users.id'
        }
      }
    };
  }
}

module.exports = Disease; 