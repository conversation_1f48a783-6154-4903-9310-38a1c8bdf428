import React, { useState, useEffect, useCallback } from 'react';
import { 
  Box, 
  Paper, 
  Typography, 
  Button,
  TextField,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  CircularProgress,
  Tooltip,
  Stack
} from '@mui/material';
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { zhCN } from 'date-fns/locale';
import { 
  Search as SearchIcon,
  FilterList as FilterListIcon,
  Refresh as RefreshIcon,
  Visibility as VisibilityIcon,
  Info as InfoIcon,
  AccessTime as AccessTimeIcon
} from '@mui/icons-material';
import { useTheme, ThemeProvider, createTheme as createMuiTheme } from '@mui/material/styles';
import { useMediaQuery } from '@mui/material';
import axios from 'axios';
import { API_BASE_URL } from '../../config/api';
import { useSnackbar } from 'notistack';
import { format } from 'date-fns';

// 审计日志接口定义
interface AuditLog {
  id: string;
  adminId: string;
  adminUsername: string;
  actionType: string;
  targetId: string;
  actionDetails: {
    before?: any;
    after?: any;
    [key: string]: any;
  };
  createdAt: string;
}

/**
 * 审计日志管理页面
 * 用于查看管理员操作记录，实现系统审计功能
 */
const AuditLogsPage: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { enqueueSnackbar } = useSnackbar();
  
  const reducedFontSizeTheme = createMuiTheme(theme, {
    typography: {
      h1: { ...theme.typography.h1, fontSize: theme.typography.h1?.fontSize ? `calc(${theme.typography.h1.fontSize} - 0.4rem)`:'5.6rem' },
      h2: { ...theme.typography.h2, fontSize: theme.typography.h2?.fontSize ? `calc(${theme.typography.h2.fontSize} - 0.3rem)`:'3.45rem' },
      h3: { ...theme.typography.h3, fontSize: theme.typography.h3?.fontSize ? `calc(${theme.typography.h3.fontSize} - 0.25rem)`:'2.75rem' },
      h4: { ...theme.typography.h4, fontSize: theme.typography.h4?.fontSize ? `calc(${theme.typography.h4.fontSize} - 0.2rem)`:'1.925rem' },
      h5: { fontSize: '1.0rem' },
      h6: { fontSize: '0.85rem' },
      subtitle1: { fontSize: '0.75rem' }, 
      subtitle2: { fontSize: '0.65rem' }, 
      body1: { fontSize: '0.75rem' }, 
      body2: { fontSize: '0.65rem' }, 
      button: { fontSize: '0.65rem' },
      caption: { fontSize: '0.55rem' },
      overline: { fontSize: '0.55rem' },
    },
    components: {
      MuiInputLabel: { styleOverrides: { root: { fontSize: '0.75rem' } } },
      MuiMenuItem: { styleOverrides: { root: { fontSize: '0.75rem' } } },
      MuiAccordionSummary: { styleOverrides: { content: { '& .MuiTypography-root': { fontSize: '0.8rem' } } } },
      MuiListItemText: { styleOverrides: { primary: { fontSize: '0.75rem' }, secondary: { fontSize: '0.65rem' } } },
      MuiTableCell: { styleOverrides: { root: { fontSize: '0.65rem' }, head: { fontSize: '0.7rem', fontWeight: 'bold' } } },
      MuiChip: { styleOverrides: { label: { fontSize: '0.55rem' }, labelSmall: { fontSize: '0.5rem' } } },
      MuiButton: { styleOverrides: { sizeSmall: { fontSize: '0.6rem' }, sizeMedium: { fontSize: '0.65rem' }, sizeLarge: { fontSize: '0.75rem' } } },
      MuiDialogTitle: { styleOverrides: { root: { fontSize: '0.85rem' } } },
      MuiDialogContentText: { styleOverrides: { root: { fontSize: '0.75rem' } } },
      MuiFormControlLabel: { styleOverrides: { label: { fontSize: '0.75rem' } } },
      MuiAlert: { styleOverrides: { message: { fontSize: '0.65rem' } } },
      MuiTablePagination: { 
        styleOverrides: { 
          caption: { fontSize: '0.65rem' }, 
          selectLabel: { fontSize: '0.65rem' }, 
          displayedRows: { fontSize: '0.65rem' } 
        } 
      },
      MuiDatePicker: { styleOverrides: { root: { '& .MuiInputBase-input': { fontSize: '0.75rem' } } } }
    }
  });
  
  // 日志列表状态
  const [logs, setLogs] = useState<AuditLog[]>([]);
  const [filteredLogs, setFilteredLogs] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // 搜索和分页状态
  const [searchKeyword, setSearchKeyword] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  
  // 筛选条件状态
  const [filterOpen, setFilterOpen] = useState(false);
  const [actionTypeFilter, setActionTypeFilter] = useState<string>('');
  const [adminFilter, setAdminFilter] = useState<string>('');
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  
  // 日志详情状态
  const [detailDialogOpen, setDetailDialogOpen] = useState(false);
  const [selectedLog, setSelectedLog] = useState<AuditLog | null>(null);
  
  // 处理搜索输入变化
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchKeyword(event.target.value);
  };
  
  // 重置所有筛选条件
  const handleResetFilters = () => {
    setSearchKeyword('');
    setActionTypeFilter('');
    setAdminFilter('');
    setStartDate(null);
    setEndDate(null);
    setFilteredLogs(logs);
  };
  
  // 处理分页变化
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };
  
  // 处理每页行数变化
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };
  
  // 处理查看日志详情
  const handleViewLogDetail = (log: AuditLog) => {
    setSelectedLog(log);
    setDetailDialogOpen(true);
  };
  
  // 关闭日志详情对话框
  const handleCloseDetailDialog = () => {
    setDetailDialogOpen(false);
    setSelectedLog(null);
  };
  
  // 获取操作类型显示文本
  const getActionTypeLabel = (actionType: string) => {
    switch (actionType) {
      case 'USER_CREATE': return '创建用户';
      case 'USER_UPDATE': return '更新用户';
      case 'USER_DELETE': return '删除用户';
      case 'USER_STATUS_CHANGE': return '更改用户状态';
      case 'PATIENT_DELETE': return '删除患者';
      case 'DISEASE_DELETE': return '删除病理';
      case 'RECORD_DELETE': return '删除记录';
      case 'REPORT_DELETE': return '删除报告';
      case 'SYSTEM_SETTING_CHANGE': return '更改系统设置';
      case 'LOGIN_ATTEMPT': return '登录尝试';
      case 'PASSWORD_RESET': return '重置密码';
      default: return actionType.replace(/_/g, ' ');
    }
  };
  
  // 获取操作类型对应的颜色
  const getActionTypeColor = (actionType: string) => {
    if (actionType.includes('CREATE')) return 'success';
    if (actionType.includes('UPDATE')) return 'info';
    if (actionType.includes('DELETE')) return 'error';
    if (actionType.includes('STATUS')) return 'warning';
    return 'default';
  };
  
  // 格式化时间戳
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return isNaN(date.getTime()) 
      ? '无效日期' 
      : format(date, 'yyyy-MM-dd HH:mm:ss');
  };
  
  // 格式化对象为可读字符串，用于显示详情
  const formatObjectForDisplay = (obj: any): string => {
    if (!obj) return '无数据';
    if (typeof obj !== 'object') return String(obj);
    
    return JSON.stringify(obj, null, 2);
  };
  
  // 获取唯一的操作类型列表，用于筛选
  const getUniqueActionTypes = (): string[] => {
    const types = new Set<string>();
    logs.forEach(log => {
      if (log.actionType) types.add(log.actionType);
    });
    return Array.from(types).sort();
  };
  
  // 获取唯一的管理员列表，用于筛选
  const getUniqueAdmins = (): { id: string, name: string }[] => {
    const admins = new Map<string, string>();
    logs.forEach(log => {
      if (log.adminId && log.adminUsername) {
        admins.set(log.adminId, log.adminUsername);
      }
    });
    
    return Array.from(admins.entries()).map(([id, name]) => ({ id, name }));
  };
  
  // 获取所有审计日志
  const fetchLogs = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      // 构建查询参数
      const params: any = {};
      
      if (actionTypeFilter) {
        params.action_type = actionTypeFilter;
      }
      
      if (adminFilter) {
        params.admin_id = adminFilter;
      }
      
      if (startDate) {
        params.start_date = format(startDate, 'yyyy-MM-dd');
      }
      
      if (endDate) {
        params.end_date = format(endDate, 'yyyy-MM-dd');
      }
      
      const response = await axios.get(`${API_BASE_URL}/api/admin/audit-logs`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        },
        params
      });
      
      const logsData: AuditLog[] = Array.isArray(response.data) ? response.data : [];
      
      setLogs(logsData);
      setFilteredLogs(logsData);
    } catch (err: any) {
      console.error('获取审计日志失败:', err);
      setError(err.response?.data?.message || '获取审计日志失败');
      enqueueSnackbar('获取审计日志失败', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  }, [actionTypeFilter, adminFilter, startDate, endDate, enqueueSnackbar]);
  
  // 根据关键字过滤日志
  const filterLogs = useCallback(() => {
    if (!logs.length) return;
    
    // 应用所有筛选条件
    let filtered = [...logs];
    
    // 关键字筛选（作用于用户名、操作类型和目标ID）
    if (searchKeyword.trim()) {
      const keyword = searchKeyword.toLowerCase().trim();
      filtered = filtered.filter(log => 
        log.adminUsername?.toLowerCase().includes(keyword) ||
        log.actionType.toLowerCase().includes(keyword) ||
        log.targetId?.toLowerCase().includes(keyword)
      );
    }
    
    // 按操作类型筛选
    if (actionTypeFilter) {
      filtered = filtered.filter(log => log.actionType === actionTypeFilter);
    }
    
    // 按管理员筛选
    if (adminFilter) {
      filtered = filtered.filter(log => log.adminId === adminFilter);
    }
    
    // 按日期范围筛选
    if (startDate) {
      const startTimestamp = startDate.getTime();
      filtered = filtered.filter(log => new Date(log.createdAt).getTime() >= startTimestamp);
    }
    
    if (endDate) {
      const endTimestamp = endDate.getTime() + 86400000; // 加一天，包含结束当天
      filtered = filtered.filter(log => new Date(log.createdAt).getTime() <= endTimestamp);
    }
    
    setFilteredLogs(filtered);
    setPage(0); // 重置页码
  }, [logs, searchKeyword, actionTypeFilter, adminFilter, startDate, endDate]);
  
  // 初始加载日志数据
  useEffect(() => {
    fetchLogs();
  }, [fetchLogs]);
  
  // 当搜索关键字变化时，过滤日志列表
  useEffect(() => {
    filterLogs();
  }, [searchKeyword, actionTypeFilter, adminFilter, startDate, endDate, logs, filterLogs]);
  
  return (
    <ThemeProvider theme={reducedFontSizeTheme}>
      <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={zhCN}>
        <Box sx={{ py: { xs: 1, sm: 2 } }}>
          <Typography variant="h6" component="h1" gutterBottom sx={{ mb: { xs: 1.5, sm: 2 } }}>
            审计日志
          </Typography>
          
          {/* 搜索和筛选区域 */}
          <Paper 
            elevation={1} 
            sx={{
              p: { xs: 1, sm: 2 }, 
              mb: { xs: 2, sm: 3 }, 
              maxWidth: '100%',
              boxSizing: 'border-box'
            }}
          >
            {isMobile ? (
              // MOBILE: Simplified Filters (Vertical Stack)
              <Stack spacing={1.5}>
                <TextField
                  placeholder="搜索用户名、操作类型或目标ID..."
                  variant="outlined"
                  size="small"
                  fullWidth
                  value={searchKeyword}
                  onChange={handleSearchChange}
                  InputProps={{
                    startAdornment: (<InputAdornment position="start"><SearchIcon /></InputAdornment>),
                  }}
                />
                <FormControl fullWidth size="small">
                  <InputLabel id="mobile-action-type-filter-label" sx={{ 
                    backgroundColor: 'background.paper', 
                    px: 0.5, 
                    position: 'absolute', 
                    top: -6 
                  }}>操作类型</InputLabel>
                  <Select
                    labelId="mobile-action-type-filter-label"
                    value={actionTypeFilter}
                    onChange={(e) => setActionTypeFilter(e.target.value)}
                    displayEmpty
                  >
                    <MenuItem value=""><em>全部类型</em></MenuItem>
                    {getUniqueActionTypes().map(type => (
                      <MenuItem key={`mobile-type-${type}`} value={type}>
                        {getActionTypeLabel(type)}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
                
                <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={zhCN}>
                  <Stack spacing={1.5}>
                    <DatePicker
                      label="开始日期"
                      value={startDate}
                      onChange={setStartDate}
                      slotProps={{ textField: { size: 'small', fullWidth: true } }}
                    />
                    <DatePicker
                      label="结束日期"
                      value={endDate}
                      onChange={setEndDate}
                      slotProps={{ textField: { size: 'small', fullWidth: true } }}
                    />
                  </Stack>
                </LocalizationProvider>

                <Stack direction="row" spacing={1} justifyContent="space-between" sx={{pt: 1}}>
                  <Button variant="outlined" color="secondary" onClick={handleResetFilters} size="small">
                    重置
                  </Button>
                  <Button variant="outlined" color="primary" startIcon={<RefreshIcon />} onClick={fetchLogs} disabled={loading} size="small">
                    刷新
                  </Button>
                  <Button variant="contained" color="primary" onClick={filterLogs} size="small">
                    应用筛选
                  </Button>
                </Stack>
              </Stack>
            ) : (
              // DESKTOP: Original Layout with Advanced Filters button
              <React.Fragment>
                <Stack direction="row" spacing={2} alignItems="center">
                  <Box sx={{ flex: '1 1 300px', minWidth: 0 }}>
                    <TextField
                      placeholder="搜索用户名、操作类型或目标ID..."
                      variant="outlined"
                      size="small"
                      fullWidth
                      value={searchKeyword}
                      onChange={handleSearchChange}
                      InputProps={{
                        startAdornment: (<InputAdornment position="start"><SearchIcon /></InputAdornment>),
                      }}
                    />
                  </Box>
                  <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end', alignItems: 'center' }}>
                    <Button variant="outlined" color="primary" startIcon={<FilterListIcon />} onClick={() => setFilterOpen(!filterOpen)}>
                      高级筛选
                    </Button>
                    <Button variant="outlined" color="primary" startIcon={<RefreshIcon />} onClick={fetchLogs} disabled={loading}>
                      刷新
                    </Button>
                  </Box>
                </Stack>
                {/* DESKTOP: Advanced filter panel (conditionally rendered) */}
                {filterOpen && (
                  <Paper elevation={0} variant="outlined" sx={{ p: { xs: 0.5, sm: 2 }, mt: 2 } /* Adjusted mt from 1 to 2 for desktop */}>
                    <Typography variant="subtitle1" gutterBottom sx={{ mb: 1 }}>
                      高级筛选
                    </Typography>
                    <Stack direction="row" spacing={2} alignItems="center" sx={{ mt: 1 }}>
                      <FormControl fullWidth size="small">
                        <InputLabel id="action-type-filter-label">操作类型</InputLabel>
                        <Select
                          labelId="action-type-filter-label"
                          value={actionTypeFilter}
                          label="操作类型"
                          onChange={(e) => setActionTypeFilter(e.target.value)}
                        >
                          <MenuItem value="">全部</MenuItem>
                          {getUniqueActionTypes().map(type => (
                            <MenuItem key={`desktop-type-${type}`} value={type}>{getActionTypeLabel(type)}</MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                      <FormControl fullWidth size="small">
                        <InputLabel id="admin-filter-label">管理员</InputLabel>
                        <Select
                          labelId="admin-filter-label"
                          value={adminFilter}
                          label="管理员"
                          onChange={(e) => setAdminFilter(e.target.value)}
                        >
                          <MenuItem value="">全部</MenuItem>
                          {getUniqueAdmins().map(admin => (
                            <MenuItem key={`desktop-admin-${admin.id}`} value={admin.id}>{admin.name}</MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                      <Box sx={{ flex: '1 1 200px', minWidth: 0 }}>
                        <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={zhCN}>
                          <DatePicker
                            label="开始日期"
                            value={startDate}
                            onChange={setStartDate}
                            slotProps={{ textField: { size: 'small', fullWidth: true } }}
                          />
                        </LocalizationProvider>
                      </Box>
                      <Box sx={{ flex: '1 1 200px', minWidth: 0 }}>
                        <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={zhCN}>
                          <DatePicker
                            label="结束日期"
                            value={endDate}
                            onChange={setEndDate}
                            slotProps={{ textField: { size: 'small', fullWidth: true } }}
                          />
                        </LocalizationProvider>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
                        <Button variant="outlined" color="secondary" onClick={handleResetFilters} sx={{ mr: 1 }} size="small">
                          重置
                        </Button>
                        <Button variant="contained" color="primary" onClick={filterLogs} size="small">
                          应用筛选
                        </Button>
                      </Box>
                    </Stack>
                  </Paper>
                )}
              </React.Fragment>
            )}
          </Paper>
          
          {/* 日志列表 */}
          <Paper elevation={2} sx={{ width: '100%', overflow: 'hidden', mt: 2 }}>
            <TableContainer sx={{ maxHeight: 'calc(100vh - 300px)', ...(isMobile && { overflowX: 'auto' }) }}>
              <Table stickyHeader size={isMobile ? "small" : "medium"}>
                <TableHead>
                  <TableRow>
                    <TableCell>操作时间</TableCell>
                    <TableCell>管理员</TableCell>
                    <TableCell>操作类型</TableCell>
                    {!isMobile && <TableCell>目标ID</TableCell>}
                    <TableCell align="center">操作</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={isMobile ? 4 : 5} align="center">
                        <CircularProgress size={24} sx={{ my: 2 }} />
                        <Typography variant="body2" color="textSecondary" sx={{ ml: 2 }}>
                          加载中...
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ) : error ? (
                    <TableRow>
                      <TableCell colSpan={isMobile ? 4 : 5} align="center">
                        <Typography variant="body2" color="error" sx={{ my: 2 }}>
                          {error}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ) : filteredLogs.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={isMobile ? 4 : 5} align="center">
                        <Typography variant="body2" color="textSecondary" sx={{ my: 2 }}>
                          {searchKeyword || actionTypeFilter || adminFilter || startDate || endDate ? 
                            '没有匹配的审计日志' : '尚无审计日志数据'}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ) : (
                    // 日志列表，使用分页
                    filteredLogs
                      .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                      .map((log) => (
                        <TableRow 
                          key={log.id}
                          hover
                          sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                        >
                          <TableCell>
                            <Box sx={{ 
                              display: 'flex', 
                              alignItems: 'center',
                              fontSize: '0.6rem', // 减小操作时间的字体大小
                              whiteSpace: 'nowrap' 
                            }}>
                              <AccessTimeIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                              {formatDateTime(log.createdAt)}
                            </Box>
                          </TableCell>
                          
                          <TableCell>
                            {/* 移除管理员图标 */}
                            {log.adminUsername || '未知用户'}
                          </TableCell>
                          
                          <TableCell>
                            <Chip
                              size="small"
                              label={getActionTypeLabel(log.actionType)}
                              color={getActionTypeColor(log.actionType) as any}
                              variant="outlined"
                            />
                          </TableCell>
                          
                          {!isMobile && (
                            <TableCell>
                              <Tooltip title="目标对象ID">
                                <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                                  {log.targetId ? log.targetId.slice(0, 8) + '...' : 'N/A'}
                                </Typography>
                              </Tooltip>
                            </TableCell>
                          )}
                          
                          <TableCell align="center">
                            <Tooltip title="查看详情">
                              <IconButton
                                size="small"
                                onClick={() => handleViewLogDetail(log)}
                              >
                                <VisibilityIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </TableCell>
                        </TableRow>
                      ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>
            
            {/* 分页控件 */}
            <TablePagination
              rowsPerPageOptions={[5, 10, 25, 50]}
              component="div"
              count={filteredLogs.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
              labelRowsPerPage="每页行数:"
              labelDisplayedRows={({ from, to, count }) => `${from}-${to} / 共${count}条`}
            />
          </Paper>
          
          {/* 日志详情对话框 */}
          <Dialog
            open={detailDialogOpen}
            onClose={handleCloseDetailDialog}
            maxWidth="md"
            fullWidth
          >
            <DialogTitle>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <InfoIcon sx={{ mr: 1 }} />
                审计日志详情
              </Box>
            </DialogTitle>
            <DialogContent dividers>
              {selectedLog && (
                <Box>
                  <Typography variant="subtitle1" gutterBottom>基本信息</Typography>
                  <Paper variant="outlined" sx={{ p: { xs: 1, sm: 1.5 }, mb: 2 }}>
                    <Typography variant="body2"><strong>操作者:</strong> {selectedLog.adminUsername} (ID: {selectedLog.adminId})</Typography>
                    <Typography variant="body2"><strong>操作类型:</strong> {getActionTypeLabel(selectedLog.actionType)}</Typography>
                    <Typography variant="body2"><strong>目标ID:</strong> {selectedLog.targetId || 'N/A'}</Typography>
                    <Typography variant="body2"><strong>时间:</strong> {formatDateTime(selectedLog.createdAt)}</Typography>
                  </Paper>

                  <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>操作详情</Typography>
                  <Paper variant="outlined" sx={{ p: { xs: 1, sm: 1.5 } }}>
                  {Object.keys(selectedLog.actionDetails).length > 0 ? (
                    Object.entries(selectedLog.actionDetails).map(([key, value]) => (
                      <Box key={key} sx={{ mb: 1.5 }}>
                        <Typography variant="body2" sx={{ fontWeight: 'bold', textTransform: 'capitalize' }}>{key.replace(/([A-Z])/g, ' $1')}:</Typography>
                        {typeof value === 'object' && value !== null ? (
                          <Box component="pre" sx={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all', backgroundColor: 'background.default', p: 1, borderRadius: 1, mt: 0.5, fontSize: '0.6rem' }}>
                            {formatObjectForDisplay(value)}
                          </Box>
                        ) : (
                          <Typography variant="body2" sx={{ ml: 1 }}>{String(value)}</Typography>
                        )}
                      </Box>
                    ))
                  ) : (
                    <Typography variant="body2" color="textSecondary">无详细操作信息。</Typography>
                  )}
                  </Paper>
                </Box>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseDetailDialog}>
                关闭
              </Button>
            </DialogActions>
          </Dialog>
        </Box>
      </LocalizationProvider>
    </ThemeProvider>
  );
};

export default AuditLogsPage; 