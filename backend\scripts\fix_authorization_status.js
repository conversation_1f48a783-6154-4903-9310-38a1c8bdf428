/**
 * 授权状态修复脚本
 * 检查数据库中授权状态与开关不一致的记录，并进行修复
 */

const knex = require('../src/db').knex;

async function fixAuthorizationStatus() {
  console.log('开始检查授权状态...');
  
  try {
    // 获取所有授权记录
    const authorizations = await knex('user_authorizations').select('*');
    console.log(`共检索到 ${authorizations.length} 条授权记录`);
    
    let fixedCount = 0;
    let alreadyCorrectCount = 0;
    
    // 遍历检查每条记录
    for (const auth of authorizations) {
      // 转换为布尔值
      const authorizerSwitch = Boolean(auth.authorizer_switch === 1 || auth.authorizer_switch === true);
      const authorizedSwitch = Boolean(auth.authorized_switch === 1 || auth.authorized_switch === true);
      
      // 确定正确的状态
      let correctStatus;
      const bothOn = authorizerSwitch && authorizedSwitch;
      const bothOff = !authorizerSwitch && !authorizedSwitch;
      const authorizerOnOnly = authorizerSwitch && !authorizedSwitch;
      const authorizedOnOnly = !authorizerSwitch && authorizedSwitch;
      
      if (bothOn) {
        // 双方都开，激活状态
        correctStatus = 'ACTIVE';
      } else if (bothOff) {
        // 双方都关，撤销状态
        correctStatus = 'REVOKED';
      } else if (authorizerOnOnly) {
        // 授权人开，被授权人关，等待被授权人确认
        correctStatus = 'PENDING_AUTHORIZED';
      } else if (authorizedOnOnly) {
        // 授权人关，被授权人开，等待授权人确认
        correctStatus = 'PENDING_AUTHORIZER';
      }
      
      // 检查是否需要修复
      if (auth.status !== correctStatus) {
        console.log(`[修复] ID: ${auth.id}, 当前状态: ${auth.status}, 正确状态: ${correctStatus}`);
        console.log(`  授权人开关: ${authorizerSwitch}, 被授权人开关: ${authorizedSwitch}`);
        
        // 更新数据库
        const updateData = { status: correctStatus };
        
        // 如果状态变为激活，设置激活时间
        if (correctStatus === 'ACTIVE') {
          updateData.activated_at = new Date().toISOString();
        }
        
        // 如果状态变为撤销，设置撤销时间
        if (correctStatus === 'REVOKED') {
          updateData.revoked_at = new Date().toISOString();
        }
        
        // 更新状态变更时间
        updateData.status_changed_at = new Date().toISOString();
        
        await knex('user_authorizations').where({ id: auth.id }).update(updateData);
        fixedCount++;
      } else {
        alreadyCorrectCount++;
      }
    }
    
    console.log('\n修复完成!');
    console.log(`总记录数: ${authorizations.length}`);
    console.log(`已修复: ${fixedCount}`);
    console.log(`状态正确: ${alreadyCorrectCount}`);
    
  } catch (error) {
    console.error('修复过程中出错:', error);
  } finally {
    // 关闭数据库连接
    await knex.destroy();
  }
}

// 执行修复函数
fixAuthorizationStatus().catch(error => {
  console.error('脚本执行失败:', error);
  process.exit(1);
}); 