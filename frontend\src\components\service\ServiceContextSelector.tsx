import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  CircularProgress,
  Alert,
  Divider,
  SelectChangeEvent
} from '@mui/material';
import { useServiceUserContext } from '../../context/ServiceUserContext';
import serviceRecordService from '../../services/serviceRecordService';

interface ServiceContextSelectorProps {
  // 完成选择回调
  onComplete: () => void;
}

/**
 * 服务上下文选择器组件
 * 用于选择服务授权、患者和病理上下文
 */
const ServiceContextSelector: React.FC<ServiceContextSelectorProps> = ({
  onComplete
}) => {
  // 获取服务上下文
  const serviceContext = useServiceUserContext();
  
  // 状态
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [authorizations, setAuthorizations] = useState<any[]>([]);
  const [patients, setPatients] = useState<any[]>([]);
  const [diseases, setDiseases] = useState<any[]>([]);
  
  // 选中项
  const [selectedAuthId, setSelectedAuthId] = useState<string>(serviceContext.authorizationId || '');
  const [selectedPatientId, setSelectedPatientId] = useState<string>(serviceContext.patientId || '');
  const [selectedDiseaseId, setSelectedDiseaseId] = useState<string>(serviceContext.diseaseId || '');
  
  // 加载授权列表
  useEffect(() => {
    const loadAuthorizations = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const response = await serviceRecordService.getServiceAuthorizations();
        if (response.success && response.data) {
          const authList = response.data;
          setAuthorizations(authList);
          
          // 检查当前选择的授权ID是否在列表中
          if (selectedAuthId) {
            const authExists = authList.some(auth => auth.id === selectedAuthId);
            if (!authExists) {
              console.log('[DEBUG] 当前选择的授权ID不在列表中，重置选择', selectedAuthId);
              setSelectedAuthId('');
              // 清除上下文
              serviceContext.setAuthorization('');
            }
          }
          
          // 如果当前没有选择，默认选第一个
          if ((!selectedAuthId || !authList.some(auth => auth.id === selectedAuthId)) && authList.length > 0) {
            const firstAuthId = authList[0].id;
            setSelectedAuthId(firstAuthId);
            
            // 找到选中的授权
            const auth = authList[0];
            // 更新上下文中的授权信息
            serviceContext.setAuthorization(
              firstAuthId, 
              auth.authorizer_id || auth.authorizer?.id || auth.ownerUserId, 
              auth.privacy_level || auth.privacyLevel,
              auth.status
            );
          }
        } else {
          setError('无法加载授权列表');
        }
      } catch (err: any) {
        console.error('加载授权列表失败:', err);
        setError(err.message || '无法加载授权列表');
      } finally {
        setLoading(false);
      }
    };
    
    loadAuthorizations();
  }, [serviceContext, selectedAuthId]);
  
  // 当授权变化时加载患者列表
  useEffect(() => {
    if (!selectedAuthId) {
      setPatients([]);
      setSelectedPatientId('');
      return;
    }
    
    const loadPatients = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const response = await serviceRecordService.getAuthorizedPatients(selectedAuthId);
        if (response.success && response.data) {
          setPatients(response.data);
          
          // 如果当前没有选择，默认选第一个
          if (!selectedPatientId && response.data.length > 0) {
            setSelectedPatientId(response.data[0].id);
          }
        } else {
          setError('无法加载患者列表');
        }
      } catch (err: any) {
        console.error('加载患者列表失败:', err);
        setError(err.message || '无法加载患者列表');
      } finally {
        setLoading(false);
      }
    };
    
    loadPatients();
  }, [selectedAuthId, selectedPatientId, serviceContext]);
  
  // 当患者变化时加载病理列表
  useEffect(() => {
    if (!selectedAuthId || !selectedPatientId) {
      setDiseases([]);
      setSelectedDiseaseId('');
      return;
    }
    
    const loadDiseases = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const response = await serviceRecordService.getAuthorizedDiseases(selectedAuthId, selectedPatientId);
        if (response.success && response.data) {
          setDiseases(response.data);
          
          // 如果当前没有选择，默认选第一个
          if (!selectedDiseaseId && response.data.length > 0) {
            setSelectedDiseaseId(response.data[0].id);
          }
        } else {
          setError('无法加载病理列表');
        }
      } catch (err: any) {
        console.error('加载病理列表失败:', err);
        setError(err.message || '无法加载病理列表');
      } finally {
        setLoading(false);
      }
    };
    
    loadDiseases();
  }, [selectedAuthId, selectedPatientId, selectedDiseaseId]);
  
  // 处理授权选择变化
  const handleAuthChange = (event: SelectChangeEvent<string>) => {
    const authId = event.target.value;
    setSelectedAuthId(authId);
    
    // 找到选中的授权
    const auth = authorizations.find(a => a.id === authId);
    if (auth) {
      // 更新上下文中的授权信息
      serviceContext.setAuthorization(
        authId, 
        auth.authorizer_id || auth.authorizer?.id || auth.ownerUserId, 
        auth.privacy_level || auth.privacyLevel,
        auth.status
      );
    }
    
    // 重置患者和病理选择
    setSelectedPatientId('');
    setSelectedDiseaseId('');
    serviceContext.setPatient(null);
    serviceContext.setDisease(null);
  };
  
  // 处理患者选择变化
  const handlePatientChange = (event: SelectChangeEvent<string>) => {
    const patientId = event.target.value;
    setSelectedPatientId(patientId);
    
    // 找到选中的患者
    const patient = patients.find(p => p.id === patientId);
    if (patient) {
      // 更新上下文中的患者信息
      serviceContext.setPatient(patientId, patient.name);
    }
    
    // 重置病理选择
    setSelectedDiseaseId('');
    serviceContext.setDisease(null);
  };
  
  // 处理病理选择变化
  const handleDiseaseChange = (event: SelectChangeEvent<string>) => {
    const diseaseId = event.target.value;
    setSelectedDiseaseId(diseaseId);
    
    // 找到选中的病理
    const disease = diseases.find(d => d.id === diseaseId);
    if (disease) {
      // 更新上下文中的病理信息
      serviceContext.setDisease(diseaseId, disease.name);
    }
  };
  
  // 处理完成按钮点击
  const handleComplete = () => {
    onComplete();
  };
  
  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        选择服务上下文
      </Typography>
      
      <Divider sx={{ my: 2 }} />
      
      {/* 错误提示 */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}
      
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        {/* 授权选择 */}
        <FormControl fullWidth disabled={loading}>
          <InputLabel id="authorization-label">选择授权关系</InputLabel>
          <Select
            labelId="authorization-label"
            id="authorization-select"
            value={authorizations.length > 0 ? (authorizations.some(auth => auth.id === selectedAuthId) ? selectedAuthId : '') : ''}
            label="选择授权关系"
            onChange={handleAuthChange}
            displayEmpty
          >
            <MenuItem value="">
              <em>请选择授权关系</em>
            </MenuItem>
            {authorizations.map((auth) => (
              <MenuItem key={auth.id} value={auth.id}>
                {/* 兼容新旧字段命名，尝试多种可能的字段名称 */}
                {auth.authorizer?.username || auth.authorizer_username || auth.ownerUserName || 
                 (auth.authorizer ? `用户: ${auth.authorizer.username}` : 
                  `用户ID: ${auth.authorizer_id || auth.authorizer?.id || auth.ownerUserId || 'undefined'}`)}
                {(auth.privacy_level || auth.privacyLevel) && 
                 ` (${auth.privacy_level || auth.privacyLevel}权限)`}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        
        {/* 患者选择 */}
        <FormControl fullWidth disabled={!selectedAuthId || loading}>
          <InputLabel id="patient-label">选择患者</InputLabel>
          <Select
            labelId="patient-label"
            id="patient-select"
            name="patientId"
            value={patients.length > 0 ? (patients.some(p => p.id === selectedPatientId) ? selectedPatientId : '') : ''}
            label="选择患者"
            onChange={handlePatientChange}
            displayEmpty
          >
            <MenuItem value="">
              <em>请选择患者</em>
            </MenuItem>
            {patients.map((patient) => (
              <MenuItem key={patient.id} value={patient.id}>
                {patient.name || `患者ID: ${patient.id}`}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        
        {/* 病理选择 */}
        <FormControl fullWidth disabled={!selectedPatientId || loading}>
          <InputLabel id="disease-label">选择病理</InputLabel>
          <Select
            labelId="disease-label"
            id="disease-select"
            value={diseases.length > 0 ? (diseases.some(d => d.id === selectedDiseaseId) ? selectedDiseaseId : '') : ''}
            label="选择病理"
            onChange={handleDiseaseChange}
            displayEmpty
          >
            <MenuItem value="">
              <em>请选择病理</em>
            </MenuItem>
            {diseases.map((disease) => (
              <MenuItem key={disease.id} value={disease.id}>
                {disease.name || `病理ID: ${disease.id}`}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        
        {/* 操作区域 */}
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
          {loading && <CircularProgress size={24} sx={{ mr: 2 }} />}
          
          <Button 
            variant="contained" 
            color="primary"
            onClick={handleComplete}
            disabled={loading || !selectedAuthId || !selectedPatientId}
          >
            确认选择
          </Button>
        </Box>
      </Box>
    </Paper>
  );
};

export default ServiceContextSelector; 