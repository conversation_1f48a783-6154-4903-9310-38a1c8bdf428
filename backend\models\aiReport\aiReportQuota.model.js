/**
 * AI报告配额数据库模型
 * 记录用户的AI分析使用次数
 */
const { DataTypes } = require('sequelize');
const sequelize = require('../../config/database');
const User = require('../user/user.model');

/**
 * AIReportQuota模型
 * 记录用户的AI分析使用情况和配额
 */
const AIReportQuota = sequelize.define('AIReportQuota', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  userId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: User,
      key: 'id'
    },
    unique: true
  },
  monthlyQuota: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 3,
    comment: '用户每月可用的AI分析次数'
  },
  usedThisMonth: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '本月已使用的次数'
  },
  totalUsed: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '历史总使用次数'
  },
  lastResetDate: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: '上次重置配额的日期'
  },
  additionalQuota: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '额外赠送或购买的次数'
  }
}, {
  tableName: 'ai_report_quotas',
  timestamps: true,
  underscored: true,
  indexes: [
    {
      name: 'ai_report_quota_user_idx',
      fields: ['userId'],
      unique: true
    }
  ]
});

// 设置与User的关联
AIReportQuota.belongsTo(User, { 
  foreignKey: 'userId', 
  as: 'user'
});

User.hasOne(AIReportQuota, { 
  foreignKey: 'userId', 
  as: 'aiReportQuota'
});

module.exports = AIReportQuota; 