module.exports = {
    apps: [
        {
            name: 'frontend',
            script: 'serve',
            args: '-s build --listen=3002',
            instances: 'max',
            exec_mode: 'cluster',
            autorestart: true,
            watch: false,
            max_memory_restart: '1G',
            env: {
                NODE_ENV: 'production',
            },
            output: './logs/frontend-console.log',
            error: './logs/frontend-error.log',
            merge_logs: true,
            log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
        }
    ]
};