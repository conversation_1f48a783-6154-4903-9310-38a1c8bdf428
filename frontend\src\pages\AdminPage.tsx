import React, { useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Card, 
  CardContent, 
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import TuneIcon from '@mui/icons-material/Tune';
import SupervisorAccountIcon from '@mui/icons-material/SupervisorAccount';
import AssessmentIcon from '@mui/icons-material/Assessment';
import PeopleIcon from '@mui/icons-material/People';
import HealthAndSafetyIcon from '@mui/icons-material/HealthAndSafety';
import ArticleIcon from '@mui/icons-material/Article';
import SecurityIcon from '@mui/icons-material/Security';
import SettingsSystemDaydreamIcon from '@mui/icons-material/SettingsSystemDaydream';
import VerifiedUserIcon from '@mui/icons-material/VerifiedUser';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';

/**
 * 系统管理页面
 * 提供系统管理模块入口
 */
const AdminPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  
  // 检查用户是否有权限访问此页面
  useEffect(() => {
    if (user && user.role !== 'ADMIN') {
      navigate('/dashboard');
    }
  }, [user, navigate]);

  // 管理选项列表
  const adminOptions = [
    {
      title: '用户管理',
      description: '管理系统用户、设置用户权限和配额',
      icon: <SupervisorAccountIcon />,
      path: '/admin/users',
      completed: true
    },
    {
      title: 'AI报告管理',
      description: '查看和管理系统中的AI生成报告',
      icon: <AssessmentIcon />,
      path: '/admin/ai-reports',
      completed: true
    },
    {
      title: '患者数据管理',
      description: '管理系统患者信息和健康数据',
      icon: <PeopleIcon />,
      path: '/admin/patients',
      completed: true
    },
    {
      title: '病理数据管理',
      description: '管理系统中的病理记录和数据',
      icon: <HealthAndSafetyIcon />,
      path: '/admin/diseases',
      completed: true
    },
    {
      title: '就诊记录管理',
      description: '查看和管理所有用户的就诊记录',
      icon: <ArticleIcon />,
      path: '/admin/records',
      completed: true
    },
    {
      title: '授权管理',
      description: '管理用户授权和权限分配',
      icon: <SecurityIcon />,
      path: '/admin/authorizations',
      completed: true
    },
    {
      title: '系统维护',
      description: '查看系统状态、管理会话和配置',
      icon: <SettingsSystemDaydreamIcon />,
      path: '/admin/maintenance',
      completed: true
    },
    {
      title: '审计日志',
      description: '查看系统操作日志和安全记录',
      icon: <VerifiedUserIcon />,
      path: '/admin/audit-logs',
      completed: true
    },
    {
      title: '辅医配置',
      description: '配置AI辅助医疗模块的参数和权限',
      icon: <TuneIcon />,
      path: '/ai-assistant/config',
      completed: true
    }
  ];

  // 处理选项点击
  const handleOptionClick = (path: string) => {
    navigate(path);
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        系统管理
      </Typography>
      
      <Box my={3}>
        <Typography variant="body1" color="text.secondary">
          管理系统配置和用户权限。只有管理员可以访问此页面。
        </Typography>
      </Box>
      
      <Box sx={{ 
        display: 'flex', 
        flexWrap: 'wrap',
        gap: 3
      }}>
        {adminOptions.map((option) => (
          <Box 
            key={option.title}
            sx={{ 
              width: { xs: '100%', sm: 'calc(50% - 16px)', md: 'calc(33.33% - 16px)' },
              mb: 2
            }}
          >
            <Card sx={{ 
              height: '100%', 
              display: 'flex', 
              flexDirection: 'column',
              transition: 'transform 0.2s, box-shadow 0.2s',
              opacity: option.completed ? 1 : 0.7,
              '&:hover': {
                transform: option.completed ? 'translateY(-5px)' : 'none',
                boxShadow: option.completed ? 3 : 1
              }
            }}>
              <CardContent sx={{ flexGrow: 1, p: 0 }}>
                <List>
                  <ListItem disablePadding>
                    <ListItemButton 
                      onClick={() => handleOptionClick(option.path)}
                      disabled={!option.completed}
                    >
                      <ListItemIcon sx={{ color: option.completed ? 'primary.main' : 'text.disabled' }}>
                        {option.icon}
                      </ListItemIcon>
                      <ListItemText 
                        primary={
                          <Typography variant="h6" component="div" color={option.completed ? 'text.primary' : 'text.disabled'}>
                            {option.title}
                            {!option.completed && 
                              <Typography variant="caption" component="span" color="text.secondary" sx={{ ml: 1 }}>
                                (开发中)
                              </Typography>
                            }
                          </Typography>
                        } 
                        secondary={
                          <Typography variant="body2" color={option.completed ? 'text.secondary' : 'text.disabled'}>
                            {option.description}
                          </Typography>
                        }
                      />
                    </ListItemButton>
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Box>
        ))}
      </Box>
    </Container>
  );
};

export default AdminPage; 