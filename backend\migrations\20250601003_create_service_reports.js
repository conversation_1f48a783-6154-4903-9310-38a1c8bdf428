/**
 * 创建服务报告表，用于服务用户为授权用户创建的AI报告
 */
exports.up = function(knex) {
  return knex.schema.createTable('service_reports', table => {
    // 主键
    table.uuid('id').primary();
    
    // 关联到AI报告表
    table.uuid('ai_report_id').notNullable().references('id').inTable('ai_reports').onDelete('CASCADE');
    
    // 关联到用户授权表
    table.uuid('authorization_id').notNullable().references('id').inTable('user_authorizations');
    
    // 创建者ID(服务用户)
    table.uuid('service_user_id').notNullable().references('id').inTable('users');
    
    // 报告所有者(普通用户)
    table.uuid('owner_user_id').notNullable().references('id').inTable('users');
    
    // PDF文件路径(扩展版报告)
    table.string('pdf_path').nullable();
    
    // 时间戳
    table.timestamp('created_at', { useTz: true }).notNullable().defaultTo(knex.fn.now());
    table.timestamp('updated_at', { useTz: true }).notNullable().defaultTo(knex.fn.now());
    
    // 索引
    table.index(['ai_report_id']);
    table.index(['authorization_id']);
    table.index(['service_user_id']);
    table.index(['owner_user_id']);
  });
};

exports.down = function(knex) {
  return knex.schema.dropTableIfExists('service_reports');
}; 