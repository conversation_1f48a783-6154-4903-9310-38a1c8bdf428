/**
 * AI报告配置数据库模型
 * 存储系统级别的AI报告配置，如不同角色可见字段等
 */
const { DataTypes } = require('sequelize');
const sequelize = require('../../config/database');

/**
 * AIReportConfig模型
 * 存储全局AI报告配置，例如不同角色可见的字段
 */
const AIReportConfig = sequelize.define('AIReportConfig', {
  id: {
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true
  },
  userVisibleFields: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: ['summary', 'emergencyGuidance', 'hospitalRecommendations', 'lifestyleAndMentalHealth'],
    comment: '普通用户可见的报告字段'
  },
  serviceVisibleFields: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: ['summary', 'differentialDiagnosis', 'emergencyGuidance', 'hospitalRecommendations', 'treatmentPlan', 'budgetEstimation', 'crossRegionGuidance', 'lifestyleAndMentalHealth', 'riskWarnings'],
    comment: '服务用户可见的报告字段'
  },
  anonymizationRules: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '匿名化规则配置'
  },
  llmPrompt: {
    type: DataTypes.TEXT('long'),
    allowNull: true,
    comment: '发送给LLM的提示模板'
  },
  llmResponse: {
    type: DataTypes.TEXT('long'),
    allowNull: true,
    comment: 'LLM响应结构模板'
  },
  quotaConfig: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: {
      PERSONAL: 3,  // 个人计划每月3次
      FAMILY: 10,   // 家庭计划每月10次
      PROFESSIONAL: 30  // 专业计划每月30次
    },
    comment: '不同套餐的AI分析次数配额'
  }
}, {
  tableName: 'ai_report_configs',
  timestamps: true
});

module.exports = AIReportConfig; 