import React, { useState, useEffect } from 'react';
import { Box, Typography, CircularProgress, Paper } from '@mui/material';
import PatientCard from './PatientCard';
import { usePatientDiseaseContext } from '../../../context/PatientDiseaseContext';
import { getPatients } from '../../../services/patientService';
import { Patient } from '../../../types/patient';

/**
 * 患者选择器组件
 * 显示患者列表和添加患者按钮
 */
const PatientSelector: React.FC = () => {
  const { selectedPatientId } = usePatientDiseaseContext();
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 从后端获取患者数据
  useEffect(() => {
    const fetchPatients = async () => {
      try {
        setLoading(true);
        const patientsData = await getPatients();
        setPatients(patientsData);
        setError(null);
      } catch (err) {
        console.error('获取患者列表失败:', err);
        setError('获取患者数据失败，请稍后再试');
      } finally {
        setLoading(false);
      }
    };
    
    fetchPatients();
  }, []);

  if (loading) {
    return (
      <Paper elevation={0} sx={{ p: 2, borderRadius: 2, border: '1px solid', borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
          <CircularProgress size={30} />
        </Box>
      </Paper>
    );
  }

  if (error) {
    return (
      <Paper elevation={0} sx={{ p: 2, borderRadius: 2, border: '1px solid', borderColor: 'divider' }}>
        <Box sx={{ p: 1, textAlign: 'center', color: 'error.main' }}>
          <Typography variant="body2">{error}</Typography>
        </Box>
      </Paper>
    );
  }

  return (
    <Paper elevation={0} sx={{ p: 2, borderRadius: 2, border: '1px solid', borderColor: 'divider', mt: -2.5 }}>
      <Typography 
        variant="h6" 
        sx={{ 
          mb: 1.5, 
          fontWeight: 'bold',
          fontSize: { xs: '0.95rem', sm: '1rem', md: '1.1rem' }
        }}
      >
        选择患者
      </Typography>
      
      <Box 
        sx={{ 
          display: 'grid',
          gridTemplateColumns: 'repeat(4, 1fr)',
          gap: { xs: 1, sm: 1.5 },
          '& > *': {
            minHeight: '100%'
          }
        }}
      >
        {/* 反转患者数组顺序 */}
        {patients.slice().reverse().map((patient) => (
          <Box 
            key={patient.id}
          >
            <PatientCard 
              id={patient.id}
              name={patient.name}
              age={calculateAge(patient.birthDate)}
              gender={getGenderAdapter(patient.gender)}
              relationship={getRelationshipAdapter(patient)}
              maxDiseases={5}
              birthDate={patient.birthDate}
              bloodType={patient.bloodType}
              phoneNumber={patient.phoneNumber}
              isSelected={patient.id === selectedPatientId}
            />
          </Box>
        ))}
      </Box>
    </Paper>
  );
};

/**
 * 根据出生日期计算年龄
 */
const calculateAge = (birthDate: string): number => {
  if (!birthDate) return 0;
  
  try {
    const today = new Date();
    const birthDateObj = new Date(birthDate);
    let age = today.getFullYear() - birthDateObj.getFullYear();
    const monthDiff = today.getMonth() - birthDateObj.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDateObj.getDate())) {
      age--;
    }
    
    return age;
  } catch (e) {
    console.error('计算年龄出错:', e);
    return 0;
  }
};

/**
 * 性别适配器函数
 */
const getGenderAdapter = (gender: string): 'male' | 'female' => {
  if (gender === '男' || gender.toUpperCase() === 'MALE') {
    return 'male';
  }
  return 'female';
};

/**
 * 关系适配器函数
 */
const getRelationshipAdapter = (patient: Patient): string => {
  // 如果是本人档案
  if (patient.isPrimary === 1) {
    return 'SELF';
  }
  
  // 根据紧急联系人关系判断
  if (patient.emergencyContactRelationship) {
    // 确保关系值大写，匹配常量定义
    return patient.emergencyContactRelationship.toUpperCase();
  }
  
  // 默认作为家属
  return 'FAMILY';
};

export default PatientSelector; 