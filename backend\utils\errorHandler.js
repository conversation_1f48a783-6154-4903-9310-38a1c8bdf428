/**
 * 通用错误处理工具
 */

/**
 * 自定义API错误类
 */
class APIError extends Error {
  constructor(message, statusCode, errors, errorCode) {
    super(message);
    this.name = this.constructor.name;
    this.statusCode = statusCode || 500;
    this.errors = errors;
    this.errorCode = errorCode;
    // Error.captureStackTrace(this, this.constructor); // 可选，用于V8环境
  }
}

// const APIError = require('./APIError'); // 注释掉原来的导入，因为我们在此处定义了它

/**
 * 处理并返回适当的错误响应
 * @param {Error} err - 错误对象
 * @param {Object} res - Express响应对象
 * @param {string} customMessagePrefix - 自定义错误前缀
 */
exports.errorHandler = (err, res, customMessagePrefix = '操作失败') => {
  console.error('\n[errorHandler] Raw Error:', err); 

  let statusCode = err.statusCode || err.status || 500;
  let message = err.message || '发生未知错误';
  let errors = err.errors || undefined;
  let errorCode = err.errorCode || undefined; // 自定义错误代码

  // 检查是否是 Objection.js/Knex 的特定错误类型
  if (err.name === 'ValidationError') { // Objection.js model validation error
    statusCode = 400;
    message = customMessagePrefix + ': 输入数据验证失败';
    errors = err.data || {}; // err.data might contain { field: ['message'] }
    console.error(`[errorHandler] Objection ValidationError:`, JSON.stringify(errors, null, 2));
  } else if (err.name === 'NotFoundError') { // Objection.js Not Found
    statusCode = 404;
    message = customMessagePrefix + ': 未找到相关资源';
    console.error(`[errorHandler] Objection NotFoundError for resource.`);
  } else if (err.nativeError && err.nativeError.code === 'SQLITE_CONSTRAINT') { // Knex/SQLite constraint violation
    statusCode = 409; // Conflict, or 400 Bad Request
    message = customMessagePrefix + ': 操作违反了数据约束（例如，唯一键冲突）。';
    console.error(`[errorHandler] SQLite Constraint Violation:`, err.nativeError.message);
  } else if (err.code === 'SQLITE_ERROR') { // 一般的 SQLite 错误
    statusCode = 500; // 通常是服务器内部问题
    message = customMessagePrefix + ': 数据库操作失败。';
    console.error(`[errorHandler] SQLite Error:`, err.message); // err.message 应该存在
  }

  // 确保 err.message 存在且是字符串才调用 .includes()
  const errorMessageString = (typeof err.message === 'string') ? err.message : '';

  // 旧的逻辑，可以保留或根据新的分类调整
  if (statusCode === 500) { // 进一步细化500错误
    if (errorMessageString.includes('not found') || errorMessageString.includes('未找到')) {
      // statusCode = 404; // 暂时不改变，因为上面已经有更精确的 NotFoundError 处理
      // message = customMessagePrefix + ': 资源未找到';
    } else if (errorMessageString.includes('constraint') || errorMessageString.includes('约束')) {
      // statusCode = 400; // 或 409, 上面已有更精确的 SQLITE_CONSTRAINT 处理
      // message = customMessagePrefix + ': 数据约束冲突';
    }
  }
  
  // 如果是自定义的 APIError 实例
  if (err instanceof APIError) {
    statusCode = err.statusCode;
    message = err.message;
    errors = err.errors;
    errorCode = err.errorCode;
    console.warn(`[errorHandler] APIError handled: ${message} (Status: ${statusCode}, Code: ${errorCode})`);
  }

  // 避免在响应已发送后再次尝试发送
  if (res.headersSent) {
    console.error('[errorHandler] Error: Headers already sent. Cannot send error response to client.');
    console.error('[errorHandler] Original error that could not be sent:', { statusCode, message, errors, errorCode });
    return;
  }

  console.error(`[errorHandler] Responding with Status: ${statusCode}, Message: ${message}, Errors: ${JSON.stringify(errors)}, ErrorCode: ${errorCode}`);
  
  res.status(statusCode).json({
    success: false,
    message: message,
    errors: errors,
    errorCode: errorCode,
    // ...(process.env.NODE_ENV === 'development' && { stack: err.stack }) // Optionally include stack in dev
  });
}; 