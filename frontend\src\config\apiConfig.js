/**
 * API路径配置中心
 * 集中管理所有API路径，确保前端使用统一的路径格式
 */

/**
 * 用户相关API路径
 */
export const USER_API = {
  // 认证相关
  LOGIN: '/login',
  REGISTER: '/register',
  // 用户信息
  PROFILE: '/users/profile',  // 优先使用 /users 前缀
  PROFILE_ALT: '/user/profile', // 兼容旧路径 
  LIMITS: '/user/limits',
  CHANGE_PASSWORD: '/user/change-password'
};

/**
 * 授权相关API路径
 */
export const AUTHORIZATION_API = {
  AS_AUTHORIZER: '/authorizations/as-authorizer',
  AS_AUTHORIZED: '/authorizations/as-authorized',
  SERVICE_USERS: '/authorizations/service-users',
  UPDATE_STATUS: (id) => `/authorizations/${id}/status`,
  UPDATE_PRIVACY: (id) => `/authorizations/${id}/privacy-level`,
  CLEAR_NOTIFICATION: (id) => `/authorizations/${id}/clear-notification`,
  DELETE: (id) => `/authorizations/${id}`
};

/**
 * 患者相关API路径
 */
export const PATIENT_API = {
  LIST: '/patients',
  DETAIL: (id) => `/patients/${id}`,
  CREATE: '/patients',
  UPDATE: (id) => `/patients/${id}`,
  DELETE: (id) => `/patients/${id}`
};

/**
 * 病理相关API路径
 */
export const DISEASE_API = {
  LIST: '/diseases',
  DETAIL: (id) => `/diseases/${id}`,
  CREATE: '/diseases',
  UPDATE: (id) => `/diseases/${id}`,
  DELETE: (id) => `/diseases/${id}`
};

/**
 * 记录相关API路径
 */
export const RECORD_API = {
  LIST: '/records',
  DETAIL: (id) => `/records/${id}`,
  CREATE: '/records',
  UPDATE: (id) => `/records/${id}`,
  DELETE: (id) => `/records/${id}`
};

/**
 * 服务记录相关API路径
 */
export const SERVICE_RECORD_API = {
  LIST: '/service-records',
  DETAIL: (id) => `/service-records/${id}`,
  CREATE: '/service-records',
  UPDATE: (id) => `/service-records/${id}`,
  DELETE: (id) => `/service-records/${id}`
};

/**
 * 服务报告相关API路径
 */
export const SERVICE_REPORT_API = {
  LIST: '/service-reports',
  DETAIL: (id) => `/service-reports/${id}`,
  CREATE: '/service-reports',
  UPDATE: (id) => `/service-reports/${id}`,
  DELETE: (id) => `/service-reports/${id}`
};

/**
 * 健康检查API路径
 */
export const HEALTH_API = {
  CHECK: '/health'
};

/**
 * 工具函数：根据环境自动选择API路径格式
 * @param {string} path - API路径
 * @param {boolean} useApiPrefix - 是否使用/api前缀，默认自动判断
 * @returns {string} 根据当前环境格式化后的API路径
 */
export const formatApiPath = (path, useApiPrefix = null) => {
  // 如果明确指定了是否使用前缀，则按指定的处理
  if (useApiPrefix === true) {
    // 确保路径以/开头但不以/api开头
    const normalizedPath = path.startsWith('/') ? path : `/${path}`;
    return normalizedPath.startsWith('/api/') ? normalizedPath : `/api${normalizedPath}`;
  } else if (useApiPrefix === false) {
    // 确保路径以/开头并去除可能存在的/api前缀
    const normalizedPath = path.startsWith('/') ? path : `/${path}`;
    return normalizedPath.startsWith('/api/') ? normalizedPath.substring(4) : normalizedPath;
  }
  
  // 未明确指定时，根据当前环境判断
  // 这里可以添加更复杂的逻辑，比如根据后端配置或动态探测
  return path;
};

export default {
  USER: USER_API,
  AUTHORIZATION: AUTHORIZATION_API,
  PATIENT: PATIENT_API,
  DISEASE: DISEASE_API,
  RECORD: RECORD_API,
  SERVICE_RECORD: SERVICE_RECORD_API,
  SERVICE_REPORT: SERVICE_REPORT_API,
  HEALTH: HEALTH_API,
  formatApiPath
}; 