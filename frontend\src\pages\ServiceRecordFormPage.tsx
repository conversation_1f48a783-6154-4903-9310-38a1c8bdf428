import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  Chip,
  CircularProgress,
  Alert,
  AlertColor,
  Snackbar,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Stack,
  Switch
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import SaveIcon from '@mui/icons-material/Save';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import InfoIcon from '@mui/icons-material/Info';
import BookmarkIcon from '@mui/icons-material/Bookmark';
import SettingsIcon from '@mui/icons-material/Settings';
import UploadFileIcon from '@mui/icons-material/UploadFile';

// 导入主题相关的 Hook 和函数
import { createTheme, ThemeProvider, useTheme } from '@mui/material/styles';

// 引入服务上下文
import { useServiceUserContext } from '../context/ServiceUserContext';
import * as serviceRecordService from '../services/serviceRecordService';
import apiClient from '../services/apiClient';
import ServiceContextBar from '../components/service/ServiceContextBar';
import { ServiceRecord } from '../types/serviceTypes';

// 引入复用的类型和组件
import { RecordTypeEnum, StagePhaseEnum, StageNodeEnum, SeverityEnum } from '../types/recordEnums';
import TypeTagSelector from '../components/records/TypeTagSelector';
import StageSelector from '../components/records/StageSelector';
import SeveritySlider from '../components/records/SeveritySlider';

// 扩展SeverityEnum添加NORMAL
const ExtendedSeverityValues = {
  MILD: SeverityEnum.MILD,
  MODERATE: SeverityEnum.MODERATE,
  SEVERE: SeverityEnum.SEVERE,
  CRITICAL: SeverityEnum.CRITICAL,
  NORMAL: 'NORMAL'
} as const;

// 记录类型模板定义
const RECORD_TYPE_TEMPLATES: Partial<Record<RecordTypeEnum, { title: string; content: string }>> = {
  [RecordTypeEnum.SELF_DESCRIPTION]: {
    title: '自述',
    content: '主要症状:\n\n发生时间:\n\n严重程度:\n\n持续时间:\n\n诱发因素:\n\n缓解因素:'
  },
  [RecordTypeEnum.SYMPTOM]: {
    title: '症状记录',
    content: '症状描述:\n\n开始时间:\n\n症状变化:\n\n影响日常生活程度:'
  },
  [RecordTypeEnum.EXAMINATION]: {
    title: '检查结果',
    content: '检查项目:\n\n检查日期:\n\n检查结果:\n\n医生意见:'
  },
  [RecordTypeEnum.LAB_TEST]: {
    title: '化验结果',
    content: '化验项目:\n\n采样时间:\n\n报告时间:\n\n结果指标:\n\n参考范围:\n\n结果分析:'
  },
  [RecordTypeEnum.DIAGNOSIS]: {
    title: '诊断结果',
    content: '诊断结论:\n\n诊断依据:\n\n鉴别诊断:\n\n医生建议:'
  },
  [RecordTypeEnum.TREATMENT]: {
    title: '治疗方案',
    content: '治疗类型:\n\n用药名称:\n\n用法用量:\n\n疗程:\n\n注意事项:\n\n预期效果:'
  },
  [RecordTypeEnum.MEDICATION]: {
    title: '用药记录',
    content: '药品名称:\n\n规格:\n\n用法用量:\n\n服用时间:\n\n不良反应:\n\n效果评估:'
  },
  [RecordTypeEnum.FOLLOW_UP]: {
    title: '随访记录',
    content: '随访日期:\n\n病情进展:\n\n治疗效果评估:\n\n需要调整的方案:\n\n下次随访时间:'
  }
};

// 扩展服务记录类型，添加前端需要的额外字段
interface ExtendedServiceRecord extends Partial<ServiceRecord> {
  severity?: string;
  isImportant?: boolean;
  isPrivate?: boolean;
  recordDate?: string;
  recordTypeData?: RecordTypeEnum[] | string;
  primaryTypeData?: RecordTypeEnum | string;
  stagePhase?: StagePhaseEnum | string;
  stageNode?: StageNodeEnum | string;
  stageTags?: string | string[];
  customTags?: string;
}

// 通知类型接口
interface Notification {
  open: boolean;
  message: string;
  type: AlertColor;
}

// 附件类型
interface Attachment {
  file: File;
  preview?: string;
  id?: string;
  fileName?: string;
  fileSize?: number;
  fileType?: string;
  isExisting?: boolean;
}

// 表单数据接口
interface RecordFormData {
  title: string;
  content: string;
  type: string | string[];
  severity: string;
  tags: string[];
  recordDate: string;
  isImportant: boolean;
  isPrivate: boolean;
  recordTypeData: RecordTypeEnum[];
  primaryTypeData: RecordTypeEnum;
  stagePhase: StagePhaseEnum | '';
  stageNode: StageNodeEnum | '';
  customTags: string;
}

// 用户等级限制
interface UserLevelLimits {
  maxAttachmentSize: number;
  maxQuantity: number;
  maxTotalStorage: number;
}

// 服务记录表单页面组件
const ServiceRecordFormPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const serviceContext = useServiceUserContext();
  const currentTheme = useTheme(); // 获取当前主题

  // 为 ServiceContextBar 创建一个字体更小的主题
  const smallerContextTheme = createTheme({
    ...currentTheme,
    typography: {
      ...currentTheme.typography,
      body1: {
        ...currentTheme.typography.body1,
        fontSize: '0.75rem', // 假设比默认 body1 (1rem) 小约2号
      },
      body2: {
        ...currentTheme.typography.body2,
        fontSize: '0.65rem', // 假设比默认 body2 (0.875rem) 小约2号
      },
      // 如果 ServiceContextBar 中的 Chip 也需要调整，可以在这里添加 MuiChip 的 fontSize
    },
    components: {
      ...currentTheme.components,
      MuiChip: { // 确保ServiceContextBar中的Chip字体也相应调整
        styleOverrides: {
          ...currentTheme.components?.MuiChip?.styleOverrides,
          labelSmall: {
            ...(currentTheme.components?.MuiChip?.styleOverrides as any)?.labelSmall,
            fontSize: '0.6rem', // 比 0.7rem 再小一点
          },
          labelMedium: { // ServiceContextBar 默认用 small chip，但以防万一
             ...(currentTheme.components?.MuiChip?.styleOverrides as any)?.labelMedium,
            fontSize: '0.65rem',
          }
        }
      }
    }
  });
  
  // 为标题行创建一个字体更小的主题
  const titleRowTheme = createTheme({
    ...currentTheme,
    typography: {
      ...currentTheme.typography,
      // 将 h5 的字号调整为比当前 h5 小约2号，例如接近 h6 或 subtitle1
      h5: {
        ...currentTheme.typography.h5,
        fontSize: currentTheme.typography.h6.fontSize || '1.0rem', // 使用 h6 的字号或默认1rem
      },
    },
    components: {
      ...currentTheme.components,
      MuiButton: {
        ...currentTheme.components?.MuiButton,
        styleOverrides: {
          ...currentTheme.components?.MuiButton?.styleOverrides,
          // 假设按钮默认尺寸是 medium，减小字体
          root: {
            ...(currentTheme.components?.MuiButton?.styleOverrides as any)?.root,
            fontSize: '0.75rem', // 比默认按钮字体 (0.875rem or 0.8125rem) 小
          },
          // 如果需要，可以更细致地调整 sizeSmall, sizeMedium, sizeLarge
        }
      }
    }
  });
  
  // 为表单其余控件创建一个字体更小的主题
  const formControlsTheme = createTheme({
    ...currentTheme,
    typography: {
      ...currentTheme.typography,
      subtitle1: { ...currentTheme.typography.subtitle1, fontSize: '0.75rem' }, // 原 1rem
      subtitle2: { ...currentTheme.typography.subtitle2, fontSize: '0.65rem' }, // 原 0.875rem
      body1: { ...currentTheme.typography.body1, fontSize: '0.75rem' },     // 原 1rem
      body2: { ...currentTheme.typography.body2, fontSize: '0.65rem' },     // 原 0.875rem
      button: { ...currentTheme.typography.button, fontSize: '0.65rem' },    // 原 0.8125rem or 0.875rem
      caption: { ...currentTheme.typography.caption, fontSize: '0.55rem' },  // 原 0.75rem
      overline: { ...currentTheme.typography.overline, fontSize: '0.55rem' }, // 原 0.75rem
    },
    components: {
      ...currentTheme.components,
      MuiInputLabel: {
        ...currentTheme.components?.MuiInputLabel,
        styleOverrides: {
          ...currentTheme.components?.MuiInputLabel?.styleOverrides,
          root: { 
            ...(currentTheme.components?.MuiInputLabel?.styleOverrides as any)?.root,
            fontSize: '0.75rem' 
          },
        }
      },
      MuiInputBase: {
        ...currentTheme.components?.MuiInputBase,
        styleOverrides: {
          ...currentTheme.components?.MuiInputBase?.styleOverrides,
          input: { 
            ...(currentTheme.components?.MuiInputBase?.styleOverrides as any)?.input,
            fontSize: '0.75rem' 
          },
        }
      },
      MuiFormHelperText: {
        ...currentTheme.components?.MuiFormHelperText,
        styleOverrides: {
          ...currentTheme.components?.MuiFormHelperText?.styleOverrides,
          root: { 
            ...(currentTheme.components?.MuiFormHelperText?.styleOverrides as any)?.root,
            fontSize: '0.6rem' // 比caption略大一点
          },
        }
      },
      MuiButton: { // Will affect bottom buttons and any other unspecified buttons
        ...currentTheme.components?.MuiButton,
        styleOverrides: {
          ...currentTheme.components?.MuiButton?.styleOverrides,
          root: {
            ...(currentTheme.components?.MuiButton?.styleOverrides as any)?.root,
            fontSize: '0.7rem', // General button font size
          },
        }
      },
      MuiChip: {
        ...currentTheme.components?.MuiChip,
        styleOverrides: {
          ...currentTheme.components?.MuiChip?.styleOverrides,
          label: { 
            ...(currentTheme.components?.MuiChip?.styleOverrides as any)?.label,
            fontSize: '0.6rem' 
          },
          labelSmall: {
            ...(currentTheme.components?.MuiChip?.styleOverrides as any)?.labelSmall,
            fontSize: '0.55rem'
          }
        }
      },
      MuiAccordionSummary: {
        ...currentTheme.components?.MuiAccordionSummary,
        styleOverrides: {
          ...currentTheme.components?.MuiAccordionSummary?.styleOverrides,
          content: { // Target the content of accordion summary for typography
            ...(currentTheme.components?.MuiAccordionSummary?.styleOverrides as any)?.content,
            // Typography inside will use theme's typography settings
            // If direct text is used, or specific typography variant is needed, it can be set here
          }
        }
      },
      MuiAlert: { // For Snackbar alert
        ...currentTheme.components?.MuiAlert,
        styleOverrides: {
          ...currentTheme.components?.MuiAlert?.styleOverrides,
          message: { 
            ...(currentTheme.components?.MuiAlert?.styleOverrides as any)?.message,
            fontSize: '0.7rem' 
          },
        }
      },
      // Typography variants h1-h6 are not typically used in this section, so direct overrides for them here are omitted
      // but subtitle1, subtitle2, body1, body2, button, caption, overline in theme.typography will take care of them.
    }
  });
  
  // 判断是否为编辑模式
  const isEditMode = !!id;
  
  // 状态
  const [formData, setFormData] = useState<RecordFormData>({
    title: '',
    content: '',
    type: RecordTypeEnum.SELF_DESCRIPTION,
    severity: ExtendedSeverityValues.NORMAL,
    tags: [],
    recordDate: new Date().toISOString().slice(0, 16),
    isImportant: false,
    isPrivate: false,
    recordTypeData: [RecordTypeEnum.SELF_DESCRIPTION],
    primaryTypeData: RecordTypeEnum.SELF_DESCRIPTION,
    stagePhase: '' as StagePhaseEnum | '',
    stageNode: '' as StageNodeEnum | '',
    customTags: ''
  });
  const [loading, setLoading] = useState(isEditMode);
  const [error, setError] = useState<string | null>(null);
  const [notification, setNotification] = useState<Notification>({
    open: false,
    message: '',
    type: 'success'
  });
  const [formErrors, setFormErrors] = useState<Partial<Record<keyof RecordFormData, string>>>({});
  const [expanded, setExpanded] = useState(false);
  const [attachments, setAttachments] = useState<Attachment[]>([]);
  
  // 添加记录类型、阶段和严重程度的专用状态
  const [recordTypes, setRecordTypes] = useState<RecordTypeEnum[]>([RecordTypeEnum.SELF_DESCRIPTION]);
  const [primaryType, setPrimaryType] = useState<RecordTypeEnum>(RecordTypeEnum.SELF_DESCRIPTION);
  const [selectedPhase, setSelectedPhase] = useState<StagePhaseEnum | null>(null);
  const [selectedNode, setSelectedNode] = useState<StageNodeEnum | null>(null);
  const [selectedStageTagsArray, setSelectedStageTagsArray] = useState<string[]>([]);
  const [severityValue, setSeverityValue] = useState<SeverityEnum>(SeverityEnum.MODERATE);
  const [customTags, setCustomTags] = useState<string>('');
  
  // 用户权限/等级限制
  const [userLevelLimits] = useState<UserLevelLimits>({
    maxAttachmentSize: 5120, // 默认5MB
    maxQuantity: 5, // 默认最多5个附件
    maxTotalStorage: 20480, // 默认20MB总容量
  });
  
  // 计算已用附件总大小（KB）
  const totalAttachmentSize = attachments.reduce((total, attachment) => {
    return total + Math.ceil(attachment.file.size / 1024);
  }, 0);
  
  // 获取记录的附件 - 用 useCallback 包裹
  const fetchRecordAttachments = useCallback(async (recordId: string) => {
    try {
      console.log("正在获取记录附件:", recordId);
      const response = await apiClient.get(`/records/attachments/record/${recordId}`, {
        params: {
          service_context: true,
          include_all_users: true
        }
      });
      
      if (response.data && Array.isArray(response.data)) {
        console.log("获取到附件数据:", response.data);
        
        const attachmentPreviewsOnly: Attachment[] = response.data.map((att: any) => ({
          file: new File([], att.fileName || att.file_name || "未知文件", {
            type: att.fileType || att.file_type || "application/octet-stream",
            lastModified: new Date().getTime()
          }),
          preview: undefined,
          id: att.id,
          fileName: att.fileName || att.file_name,
          fileSize: att.fileSize || att.file_size,
          fileType: att.fileType || att.file_type,
          isExisting: true
        }));
        
        setAttachments(attachmentPreviewsOnly);
      }
    } catch (err) {
      console.error("获取附件失败:", err);
    }
  }, []);
  
  // 获取要编辑的记录数据
  useEffect(() => {
    if (!isEditMode) return;
    
    const fetchRecord = async () => {
      if (!id) return;
      
      setLoading(true);
      try {
        const response = await serviceRecordService.getServiceRecordById(id);
        console.log("获取到记录数据:", response.data);
        
        if (response && response.data) {
          const record = response.data as ExtendedServiceRecord;
          
          // 解析记录类型，可能是JSON字符串
          let recordType = record.recordType || record.recordTypeData;
          let recordTypesArray: RecordTypeEnum[] = [RecordTypeEnum.SELF_DESCRIPTION];
          
          if (typeof recordType === 'string') {
            try {
              const parsed = JSON.parse(recordType);
              if (Array.isArray(parsed)) {
                recordTypesArray = parsed;
              } else if (typeof parsed === 'string') {
                recordTypesArray = [parsed as RecordTypeEnum];
              }
            } catch (e) {
              // 如果解析失败，尝试直接使用
              recordTypesArray = [recordType as RecordTypeEnum];
            }
          } else if (Array.isArray(recordType)) {
            recordTypesArray = recordType as RecordTypeEnum[];
          }
          
          // 设置记录类型
          setRecordTypes(recordTypesArray);
          
          // 设置主要类型
          const primaryTypeValue = record.primaryType || record.primaryTypeData || recordTypesArray[0] || RecordTypeEnum.SELF_DESCRIPTION;
          // 确保primaryTypeValue是有效的RecordTypeEnum值
          let primaryTypeEnum: RecordTypeEnum;
          if (typeof primaryTypeValue === 'string' && Object.values(RecordTypeEnum).includes(primaryTypeValue as any)) {
            primaryTypeEnum = primaryTypeValue as RecordTypeEnum;
          } else {
            // 默认使用自述类型
            primaryTypeEnum = RecordTypeEnum.SELF_DESCRIPTION;
          }
          setPrimaryType(primaryTypeEnum);
          
          // 设置阶段和节点
          let stagePhaseEnum: StagePhaseEnum | '' = '';
          if (record.stagePhase && typeof record.stagePhase === 'string' && 
              Object.values(StagePhaseEnum).includes(record.stagePhase as any)) {
            stagePhaseEnum = record.stagePhase as StagePhaseEnum;
            setSelectedPhase(stagePhaseEnum);
          }
          
          let stageNodeEnum: StageNodeEnum | '' = '';
          if (record.stageNode && typeof record.stageNode === 'string' && 
              Object.values(StageNodeEnum).includes(record.stageNode as any)) {
            stageNodeEnum = record.stageNode as StageNodeEnum;
            setSelectedNode(stageNodeEnum);
          }
          
          // 设置严重程度
          let severityEnum: SeverityEnum = SeverityEnum.MODERATE;
          if (record.severity && typeof record.severity === 'string') {
            // 检查是否是有效的SeverityEnum值
            if (Object.values(SeverityEnum).includes(record.severity as any)) {
              severityEnum = record.severity as SeverityEnum;
              console.log("设置严重程度为:", severityEnum);
            } else if (record.severity === 'NORMAL') {
              // 如果是NORMAL，使用默认的MODERATE（因为NORMAL不是SeverityEnum的一部分）
              severityEnum = SeverityEnum.MODERATE;
            }
          }
          setSeverityValue(severityEnum);
          
          // 设置自定义标签
          if (record.customTags) {
            setCustomTags(record.customTags);
          }
          
          // 设置阶段标签
          if (record.stageTags) {
            try {
              // 尝试解析stageTags
              const parsedStageTags = typeof record.stageTags === 'string' 
                ? JSON.parse(record.stageTags) 
                : record.stageTags;
              
              if (Array.isArray(parsedStageTags)) {
                setSelectedStageTagsArray(parsedStageTags);
              }
            } catch (e) {
              console.error('解析stageTags失败:', e);
            }
          }
          
          // 设置表单数据
          setFormData({
            title: record.title || '',
            content: record.content || '',
            type: recordTypesArray,
            severity: record.severity || ExtendedSeverityValues.NORMAL,
            tags: record.tags || [],
            recordDate: record.recordDate ? new Date(record.recordDate).toISOString().slice(0, 16) : 
                       record.createdAt ? new Date(record.createdAt).toISOString().slice(0, 16) : 
                       new Date().toISOString().slice(0, 16),
            isImportant: record.isImportant || false,
            isPrivate: false, // 服务用户不能设置私密性
            recordTypeData: recordTypesArray,
            primaryTypeData: primaryTypeEnum,
            stagePhase: stagePhaseEnum,
            stageNode: stageNodeEnum,
            customTags: record.customTags || ''
          });
          
          // 获取附件
          fetchRecordAttachments(id);
        } else {
          setError('记录不存在或无法访问');
        }
      } catch (err: any) {
        console.error('获取记录详情失败:', err);
        setError(err.message || '获取记录详情失败');
      } finally {
        setLoading(false);
      }
    };
    
    fetchRecord();
  }, [id, isEditMode, fetchRecordAttachments]);
  
  // 当记录类型变化时，应用相应模板
  useEffect(() => {
    if (typeof formData.type === 'string' && !isEditMode) {
      const recordType = formData.type as RecordTypeEnum;
      // 如果记录类型有对应的模板，使用模板内容
      if (RECORD_TYPE_TEMPLATES[recordType]) {
        setFormData(prev => ({
          ...prev,
          title: RECORD_TYPE_TEMPLATES[recordType]!.title,
          content: RECORD_TYPE_TEMPLATES[recordType]!.content
        }));
      }
    }
  }, [formData.type, isEditMode]);
  
  // 表单验证
  const validateForm = (): boolean => {
    const errors: Partial<Record<keyof RecordFormData, string>> = {};
    
    if (!formData.title.trim()) {
      errors.title = '标题不能为空';
    }
    
    if (!formData.content.trim()) {
      errors.content = '内容不能为空';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };
  
  // 处理表单字段变化
  const handleInputChange = (field: keyof RecordFormData, value: any) => {
    setFormData({
      ...formData,
      [field]: value
    });
    
    // 清除相应的错误
    if (formErrors[field]) {
      setFormErrors({
        ...formErrors,
        [field]: undefined
      });
    }
  };
  
  // 处理附件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!event.target.files || event.target.files.length === 0) return;
    
    const newFiles = Array.from(event.target.files);
    
    // 检查附件数量限制
    if (attachments.length + newFiles.length > userLevelLimits.maxQuantity) {
      setNotification({
        open: true,
        message: `附件数量超过限制 (最多${userLevelLimits.maxQuantity}个)`,
        type: 'error'
      });
      return;
    }
    
    // 检查附件大小限制
    const oversizedFiles = newFiles.filter(file => file.size > userLevelLimits.maxAttachmentSize * 1024);
    if (oversizedFiles.length > 0) {
      setNotification({
        open: true,
        message: `文件大小超过限制 (最大${userLevelLimits.maxAttachmentSize}KB): ${oversizedFiles.map(f => f.name).join(', ')}`,
        type: 'error'
      });
      return;
    }
    
    // 检查总容量限制
    const newTotalSize = totalAttachmentSize + newFiles.reduce((size, file) => size + Math.ceil(file.size / 1024), 0);
    if (newTotalSize > userLevelLimits.maxTotalStorage) {
      setNotification({
        open: true,
        message: `附件总容量超过限制 (最大${userLevelLimits.maxTotalStorage}KB)`,
        type: 'error'
      });
      return;
    }
    
    // 添加新文件到附件列表
    const newAttachments = newFiles.map(file => ({
      file,
      preview: URL.createObjectURL(file)
    }));
    
    setAttachments([...attachments, ...newAttachments]);
  };
  
  // 处理删除附件
  const handleDeleteAttachment = (index: number) => {
    setAttachments(prev => {
      const newAttachments = [...prev];
      // 释放URL对象
      if (newAttachments[index].preview) {
        URL.revokeObjectURL(newAttachments[index].preview!);
      }
      newAttachments.splice(index, 1);
      return newAttachments;
    });
  };
  
  // 处理高级选项展开/折叠
  const handleExpandChange = () => {
    setExpanded(!expanded);
  };
  
  // 上传附件的方法
  const uploadAttachments = async (recordId: string) => {
    if (!recordId || attachments.length === 0) return;
    
    console.log(`[记录表单] 开始上传附件，数量: ${attachments.length}`);
    
    // 过滤出新添加的附件（非已存在的）
    const newAttachments = attachments.filter(att => !att.isExisting);
      
    // 上传附件计数
    let successCount = 0;
    let failCount = 0;
    
    for (const attachment of newAttachments) {
      try {
        // 使用服务函数上传附件
        await serviceRecordService.uploadAttachment(recordId, attachment.file);
        console.log(`[记录表单] 上传附件成功: ${attachment.file.name}`);
        successCount++;
      } catch (attachErr: any) {
        console.error(`[记录表单] 上传附件失败: ${attachment.file.name}`, attachErr);
        failCount++;
      }
    }
    
    // 返回上传结果统计
    return { successCount, failCount, totalCount: newAttachments.length };
  };
      
  // 处理表单提交
  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const formId = id;
      const recordData = {
        title: formData.title,
        content: formData.content,
        type: Array.isArray(formData.recordTypeData) ? formData.recordTypeData : [formData.recordTypeData],
        recordTypeData: JSON.stringify(formData.recordTypeData),
        primaryTypeData: formData.primaryTypeData,
        severity: formData.severity,
        stagePhase: formData.stagePhase || undefined,
        stageNode: formData.stageNode || undefined,
        tags: formData.tags.length > 0 ? formData.tags : undefined, // 使用undefined代替null
        recordDate: formData.recordDate,
        isImportant: formData.isImportant, // 使用boolean而不是数字
        isPrivate: formData.isPrivate, // 使用boolean而不是数字
        patientId: serviceContext.patientId || undefined, // 使用undefined代替null
        diseaseId: serviceContext.diseaseId || undefined, // 使用undefined代替null
        authorizationId: serviceContext.authorizationId || '' // 强制转为字符串
      };

      if (!formId) {
        // 创建模式
        console.log('[记录表单] 创建记录:', recordData);
        const createResult = await serviceRecordService.createServiceRecord(recordData);
        
        if (createResult && createResult.data && createResult.data.id) {
          console.log('[记录表单] 创建成功，记录ID:', createResult.data.id);
          
          // 上传附件
          if (attachments.length > 0) {
            await uploadAttachments(createResult.data.id);
        }
        
        setNotification({
          open: true,
          message: '记录创建成功',
          type: 'success'
        });
          
          // 延迟后返回详情页或列表页
          setTimeout(() => {
            navigate(`/service-records/${createResult.data.id}`);
          }, 1000);
        }
      } else {
        // 编辑模式
        console.log('[记录表单] 更新记录:', recordData);
        await serviceRecordService.updateServiceRecord(formId, recordData);
        
        // 上传新附件
        if (attachments.some(a => !a.isExisting)) {
          await uploadAttachments(formId);
        }
        
              setNotification({
                open: true,
          message: '记录更新成功',
                type: 'success'
              });
      
        // 延迟后返回详情页或列表页
      setTimeout(() => {
          navigate(`/service-records/${formId}`);
        }, 1000);
      }
    } catch (err: any) {
      console.error('[记录表单] 提交失败:', err);
      setError(err.message || '提交表单失败，请检查输入并重试');
    } finally {
      setLoading(false);
    }
  };
  
  // 关闭通知
  const handleCloseNotification = () => {
    setNotification({
      ...notification,
      open: false
    });
  };
  
  // 返回上一页
  const handleGoBack = () => {
    navigate(-1);
  };
  
  // 释放附件预览URL
  useEffect(() => {
    return () => {
      // 组件卸载时释放所有预览URL
      attachments.forEach(attachment => {
        if (attachment.preview) {
          URL.revokeObjectURL(attachment.preview);
        }
      });
    };
  }, [attachments]);
  
  // 处理记录类型变更
  const handleRecordTypeChange = (types: RecordTypeEnum[]) => {
    setRecordTypes(types);
    console.log('记录类型变更:', types);
  };
  
  // 处理主要类型变更
  const handlePrimaryTypeChange = (type: RecordTypeEnum) => {
    setPrimaryType(type);
    console.log('主要类型变更:', type);
  };
  
  // 处理阶段节点变更
  const handleNodeChange = (node: StageNodeEnum | null) => {
    setSelectedNode(node);
    console.log('阶段节点变更:', node, '类型:', typeof node, '值(字符串):', node ? node.toString() : '');
  };
  
  // 处理阶段变更
  const handlePhaseChange = (phase: StagePhaseEnum | null) => {
    setSelectedPhase(phase);
    console.log('阶段变更:', phase, '类型:', typeof phase, '值(字符串):', phase ? phase.toString() : '');
  };
  
  // 处理严重程度变更
  const handleSeverityChange = (severity: SeverityEnum, _numValue: number) => {
    setSeverityValue(severity);
    // 同时更新表单数据
    setFormData(prev => ({
      ...prev,
      severity
    }));
    console.log('严重程度变更:', severity);
  };
  
  // 处理自定义标签变更
  const handleCustomTagsChange = (value: string) => {
    setCustomTags(value);
    console.log('自定义标签变更:', value);
  };
  
  // 处理自定义标签回车事件
  const handleCustomTagKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      event.preventDefault(); // 防止表单提交
      if (customTags.trim()) {
        // 将当前输入的标签添加到数组
        const newTags = customTags.split(',').map(tag => tag.trim()).filter(tag => tag);
        setSelectedStageTagsArray(prev => {
          // 过滤掉重复的标签
          const combinedTags = [...prev, ...newTags];
          return Array.from(new Set(combinedTags));
        });
        // 清空输入框
        setCustomTags('');
      }
    }
  };
  
  // 删除自定义标签
  const handleDeleteCustomTag = (tagToDelete: string) => {
    setSelectedStageTagsArray(prev => prev.filter(tag => tag !== tagToDelete));
  };
  
  // 显示加载中
  if (loading && isEditMode) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }
  
  return (
    <Box>
      {/* 面包屑导航 - 已移除 */}
      {/* 
      <Breadcrumbs sx={{ mb: 2 }}>
        <MuiLink 
          component={Link} 
          to="/dashboard" 
          color="inherit" 
          underline="hover"
        >
          看板
        </MuiLink>
        <MuiLink 
          component={Link} 
          to="/service-records" 
          color="inherit" 
          underline="hover"
        >
          服务记录管理
        </MuiLink>
        <Typography color="text.primary">
          {isEditMode ? '编辑记录' : '创建记录'}
        </Typography>
      </Breadcrumbs>
      */}
      
      {/* 服务上下文信息栏 */}
      <Box sx={{ paddingLeft: '10px', paddingRight: '10px', paddingTop: '10px' }}>
        <ThemeProvider theme={smallerContextTheme}>
          <ServiceContextBar />
        </ThemeProvider>
      </Box>
      
      {/* 页面标题和操作按钮 */}
      <ThemeProvider theme={titleRowTheme}>
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 3,
          paddingLeft: '10px',
          paddingRight: '10px'
        }}>
          <Typography variant="h5" component="h1">
            {isEditMode ? '编辑服务记录' : '创建服务记录'}
          </Typography>
          
          <Box>
            <Button 
              startIcon={<ArrowBackIcon />} 
              onClick={handleGoBack}
              sx={{ mr: 1 }}
            >
              返回
            </Button>
            <Button 
              variant="contained" 
              startIcon={<SaveIcon />} 
              onClick={handleSubmit}
              disabled={loading}
            >
              保存
            </Button>
          </Box>
        </Box>
      </ThemeProvider>
      
      {/* 错误提示, 表单内容, Snackbar etc. will be wrapped in the new ThemeProvider */}
      <ThemeProvider theme={formControlsTheme}>
        {/* 错误提示 */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}
        
        {/* 表单内容 */}
        <Paper sx={{ p: 3 }}>
          <Box component="form" noValidate sx={{ mt: 1 }}>
            {/* 阶段选择器 - 最上方 */}
            <Box sx={{ mt: 0, mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 500 }}>病程阶段</Typography>
              <Paper elevation={0} sx={{ p: 1, bgcolor: 'background.paper' }}>
                <StageSelector
                  selectedNode={selectedNode}
                  selectedPhase={selectedPhase}
                  onNodeChange={handleNodeChange}
                  onPhaseChange={handlePhaseChange}
                  disabled={loading}
                />
              </Paper>
            </Box>
            
            {/* 记录类型标签选择器 */}
            <Box sx={{ mt: 3, mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 500 }}>记录类型</Typography>
              <Paper elevation={0} sx={{ p: 1, bgcolor: 'background.paper' }}>
                <TypeTagSelector
                  selectedTypes={recordTypes}
                  onChange={handleRecordTypeChange}
                  primaryType={primaryType}
                  onPrimaryTypeChange={handlePrimaryTypeChange}
                  compact={false}
                />
              </Paper>
            </Box>
            
            {/* 标题 */}
            <Box sx={{ mt: 3, mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 500 }}>记录内容</Typography>
              
              <TextField
                margin="normal"
                required
                fullWidth
                id="title"
                label="标题"
                name="title"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                error={!!formErrors.title}
                helperText={formErrors.title || "简明扼要，字数不超10字"}
                disabled={loading}
                sx={{ mb: 2 }}
              />
              
              {/* 记录内容 */}
              <TextField
                margin="normal"
                required
                fullWidth
                id="content"
                label="内容"
                name="content"
                multiline
                rows={10}
                value={formData.content}
                onChange={(e) => handleInputChange('content', e.target.value)}
                error={!!formErrors.content}
                helperText={formErrors.content || "详细描述记录的内容，可以包括症状、治疗方案、医嘱等信息"}
                disabled={loading}
                placeholder="请输入记录内容..."
              />
            </Box>
            
            {/* 记录日期 - 移到底部 */}
            <Box sx={{ mt: 3, mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 500 }}>记录日期和时间</Typography>
              <TextField
                fullWidth
                id="recordDate"
                type="datetime-local"
                value={formData.recordDate}
                onChange={(e) => handleInputChange('recordDate', e.target.value)}
                InputLabelProps={{
                  shrink: true,
                }}
                helperText="记录的日期和时间"
                disabled={loading}
              />
            </Box>
            
            {/* 高级选项 */}
            <Accordion expanded={expanded} onChange={handleExpandChange} sx={{ mt: 3 }}>
              <AccordionSummary
                expandIcon={<ExpandMoreIcon />}
                aria-controls="panel1a-content"
                id="panel1a-header"
              >
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <SettingsIcon sx={{ mr: 1 }} />
                  <Typography>高级选项</Typography>
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <Stack spacing={3}>
                  {/* 记录属性 */}
                  <Box sx={{ 
                    padding: 2, 
                    border: '1px solid #e0e0e0', 
                    borderRadius: 1,
                    backgroundColor: 'background.paper'
                  }}>
                    <Typography variant="subtitle2" sx={{ mb: 2 }}>记录属性</Typography>
                    {/* 重要性标记 */}
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Switch
                        checked={formData.isImportant}
                        onChange={(e) => handleInputChange('isImportant', e.target.checked)}
                        disabled={loading}
                      />
                      <BookmarkIcon color={formData.isImportant ? "primary" : "disabled"} />
                      <Typography variant="body2">标记为重要记录</Typography>
                    </Box>
                    {/* 注意：移除了私密性设置，因为服务用户不应有此权限 */}
                  </Box>
                  
                  {/* 严重程度滑块 */}
                  <Box sx={{ 
                    padding: 2, 
                    border: '1px solid #e0e0e0', 
                    borderRadius: 1,
                    backgroundColor: 'background.paper'
                  }}>
                    <SeveritySlider
                      value={severityValue}
                      onChange={handleSeverityChange}
                      disabled={loading}
                    />
                  </Box>
                  
                  {/* 自定义标签字段 */}
                  <Box sx={{ 
                    padding: 2, 
                    border: '1px solid #e0e0e0', 
                    borderRadius: 1,
                    backgroundColor: 'background.paper'
                  }}>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>自定义标签</Typography>
                    <TextField
                      fullWidth
                      size="small"
                      placeholder="添加标签..."
                      value={customTags}
                      onChange={(e) => handleCustomTagsChange(e.target.value)}
                      disabled={loading}
                      InputProps={{
                        startAdornment: <span style={{ marginRight: '4px' }}>#</span>,
                      }}
                      sx={{ mb: 1 }}
                      onKeyDown={handleCustomTagKeyDown}
                    />
                    <Typography variant="caption" color="text.secondary">
                      输入标签，按Enter添加，可选择已有标签或创建新标签
                    </Typography>
                    
                    {/* 显示已添加的自定义标签 */}
                    {selectedStageTagsArray.length > 0 && (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 2 }}>
                        {selectedStageTagsArray.map((tag, index) => (
                          <Chip
                            key={index}
                            label={tag}
                            size="small"
                            color="primary"
                            variant="outlined"
                            onDelete={() => handleDeleteCustomTag(tag)}
                            disabled={loading}
                          />
                        ))}
                      </Box>
                    )}
                  </Box>
                  
                  {/* 附件上传 */}
                  <Box sx={{ 
                    padding: 2, 
                    border: '1px solid #e0e0e0', 
                    borderRadius: 1,
                    backgroundColor: 'background.paper'
                  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <AttachFileIcon />
                        <Typography variant="subtitle2">附件上传</Typography>
                      </Box>
                      <Button
                        variant="outlined"
                        size="small"
                        component="label"
                        disabled={loading || attachments.length >= userLevelLimits.maxQuantity}
                        startIcon={<UploadFileIcon />}
                      >
                        选择文件
                        <input
                          type="file"
                          hidden
                          multiple
                          onChange={handleFileUpload}
                          disabled={loading || attachments.length >= userLevelLimits.maxQuantity}
                        />
                      </Button>
                    </Box>
                    
                    {/* 附件列表 */}
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      已上传: {attachments.length}/{userLevelLimits.maxQuantity} 个文件 ({(totalAttachmentSize/1024).toFixed(2)} MB / {userLevelLimits.maxTotalStorage/1024} MB)
                    </Typography>
                    
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                      {attachments.map((attachment, index) => (
                        <Chip
                          key={index}
                          label={`${attachment.fileName || attachment.file.name} (${Math.ceil((attachment.fileSize || attachment.file.size) / 1024)}KB)`}
                          onDelete={attachment.isExisting ? undefined : () => handleDeleteAttachment(index)}
                          disabled={loading}
                          icon={<AttachFileIcon fontSize="small" />}
                          color={attachment.isExisting ? "primary" : "default"}
                          variant={attachment.isExisting ? "outlined" : "filled"}
                          sx={{ 
                            '& .MuiChip-icon': { 
                              color: attachment.isExisting ? 'primary.main' : 'inherit' 
                            }
                          }}
                        />
                      ))}
                    </Box>
                    
                    <Box sx={{ 
                      mt: 1, 
                      p: 1, 
                      borderRadius: 1, 
                      backgroundColor: 'info.light', 
                      color: 'info.contrastText',
                      display: 'flex',
                      alignItems: 'center'
                    }}>
                      <InfoIcon fontSize="small" sx={{ mr: 1 }} />
                      <Typography variant="caption">
                        支持的文件类型: 图片、PDF、文档等 | 单个文件最大: {userLevelLimits.maxAttachmentSize/1024} MB | 总容量上限: {userLevelLimits.maxTotalStorage/1024} MB
                      </Typography>
                    </Box>
                  </Box>
                </Stack>
              </AccordionDetails>
            </Accordion>
            
            {/* 标签输入移除，集成到高级选项中 */}
            
            {/* 表单底部操作区 */}
            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
              <Button 
                variant="outlined" 
                onClick={handleGoBack}
                disabled={loading}
                sx={{ mr: 1 }}
              >
                取消
              </Button>
              <Button 
                variant="contained" 
                color="primary"
                onClick={handleSubmit}
                disabled={loading}
                startIcon={loading ? <CircularProgress size={24} /> : <SaveIcon />}
              >
                {isEditMode ? '更新记录' : '创建记录'}
              </Button>
            </Box>
          </Box>
        </Paper>
        
        {/* 通知消息 */}
        <Snackbar
          open={notification.open}
          autoHideDuration={6000}
          onClose={handleCloseNotification}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        >
          <Alert 
            onClose={handleCloseNotification} 
            severity={notification.type}
            variant="filled"
          >
            {notification.message}
          </Alert>
        </Snackbar>
      </ThemeProvider>
    </Box>
  );
};

export default ServiceRecordFormPage; 