// 疾病阶段枚举（英文，和后端一致）
export enum DiseaseStages {
  INITIAL = 'INITIAL',
  DIAGNOSIS = 'DIAGNOSIS',
  TREATMENT = 'TREATMENT',
  RECOVERY = 'RECOVERY',
  PROGNOSIS = 'PROGNOSIS'
}

// 阶段中文映射
export const DiseaseStageLabels: Record<DiseaseStages, string> = {
  [DiseaseStages.INITIAL]: '初诊期',
  [DiseaseStages.DIAGNOSIS]: '确诊期',
  [DiseaseStages.TREATMENT]: '治疗期',
  [DiseaseStages.RECOVERY]: '恢复期',
  [DiseaseStages.PROGNOSIS]: '预后期'
};

// 疾病状态枚举
export enum DiseaseStatus {
  ACTIVE = '活跃',
  INACTIVE = '不活跃',
  CURED = '已治愈'
}

// 疾病接口
export interface Disease {
  id: string;
  patientId: string;
  userId?: string;
  name: string;
  diagnosisDate?: string;
  stage: string;
  description?: string;
  treatment?: string;
  status?: string;
  isDeleted: boolean;
  isPrivate?: number;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
} 